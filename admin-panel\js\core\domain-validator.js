/**
 * 轻量级域名验证器
 * 优先验证域名，避免无效资源加载
 * 
 * <AUTHOR>
 * @version 2.0.0 - 架构优化版本
 */

class DomainValidator {
    constructor() {
        this.tenantInfo = null;
        this.isValid = false;
        this.startTime = Date.now();
    }

    /**
     * 验证当前域名并获取租户信息
     * @returns {Promise<Object|null>} 租户信息或null
     */
    async validateDomain() {
        try {
            const hostname = window.location.hostname;
            console.log('🌐 域名验证开始:', hostname);
            
            // 显示验证状态
            this._showValidationStatus('正在验证域名...');
            
            const response = await fetch('/api/admin.php', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify({
                    action: 'resolve_domain',
                    domain: hostname,
                    timestamp: Date.now()
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.error_code === 0 && result.data) {
                this.tenantInfo = result.data;
                this.isValid = true;
                
                const validationTime = Date.now() - this.startTime;
                console.log(`✅ 域名验证成功 (${validationTime}ms):`, this.tenantInfo);
                
                this._showValidationStatus('域名验证成功', 'success');
                return this.tenantInfo;
                
            } else {
                throw new Error(result.message || '域名配置错误');
            }
            
        } catch (error) {
            console.error('❌ 域名验证失败:', error);
            this._showDomainError(error.message);
            return null;
        }
    }

    /**
     * 获取租户加载配置
     * @returns {Object} 加载配置信息
     */
    getTenantLoadConfig() {
        if (!this.isValid || !this.tenantInfo) {
            return null;
        }

        const tenantType = this.tenantInfo.tenant_type;
        
        // 根据租户类型定义加载配置
        const loadConfigs = {
            'system_admin': {
                routes: 'system-admin-routes.js',
                ui: 'system-admin-ui.js',
                bundle: 'system-admin-bundle.js',
                routeCount: 14,
                theme: {
                    primaryColor: '#1890ff',
                    brandName: '系统管理后台',
                    testAccount: 'admin / password'
                }
            },
            'platform_admin': {
                routes: 'platform-admin-routes.js',
                ui: 'platform-admin-ui.js', 
                bundle: 'platform-admin-bundle.js',
                routeCount: 11,
                theme: {
                    primaryColor: '#722ed1',
                    brandName: this.tenantInfo.platform_name || '平台管理后台',
                    testAccount: 'platform_admin_001 / password'
                }
            },
            'provider': {
                routes: 'provider-routes.js',
                ui: 'provider-ui.js',
                bundle: 'provider-bundle.js', 
                routeCount: 19,
                theme: {
                    primaryColor: '#52c41a',
                    brandName: this.tenantInfo.brand_name || '码商管理系统',
                    testAccount: 'provider1 / password'
                }
            },
            'merchant': {
                routes: 'merchant-routes.js',
                ui: 'merchant-ui.js',
                bundle: 'merchant-bundle.js',
                routeCount: 16,
                theme: {
                    primaryColor: '#fa8c16', 
                    brandName: this.tenantInfo.brand_name || '商户管理系统',
                    testAccount: 'merchant1 / password'
                }
            }
        };

        const config = loadConfigs[tenantType];
        if (!config) {
            console.error('❌ 未知的租户类型:', tenantType);
            return null;
        }

        // 合并租户信息
        config.tenantInfo = this.tenantInfo;
        config.tenantType = tenantType;
        
        console.log(`📦 生成${tenantType}加载配置:`, config);
        return config;
    }

    /**
     * 显示验证状态
     * @private
     */
    _showValidationStatus(message, type = 'info') {
        // 创建或更新状态显示
        let statusEl = document.getElementById('domainValidationStatus');
        if (!statusEl) {
            statusEl = document.createElement('div');
            statusEl.id = 'domainValidationStatus';
            statusEl.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px 30px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                text-align: center;
                min-width: 250px;
            `;
            document.body.appendChild(statusEl);
        }

        const colors = {
            info: '#1890ff',
            success: '#52c41a', 
            error: '#ff4d4f'
        };

        statusEl.innerHTML = `
            <div style="color: ${colors[type]}; font-size: 16px; font-weight: 500;">
                ${type === 'info' ? '🔍' : type === 'success' ? '✅' : '❌'} ${message}
            </div>
            ${type === 'info' ? '<div style="margin-top: 10px;"><div class="spinner"></div></div>' : ''}
        `;

        // 添加简单的加载动画CSS
        if (type === 'info' && !document.getElementById('spinnerStyle')) {
            const style = document.createElement('style');
            style.id = 'spinnerStyle';
            style.textContent = `
                .spinner {
                    width: 20px;
                    height: 20px;
                    border: 2px solid #f0f0f0;
                    border-top: 2px solid #1890ff;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        // 成功状态2秒后自动隐藏
        if (type === 'success') {
            setTimeout(() => {
                if (statusEl) {
                    statusEl.remove();
                }
            }, 2000);
        }
    }

    /**
     * 显示域名错误并停止加载
     * @private
     */
    _showDomainError(message) {
        // 隐藏验证状态
        const statusEl = document.getElementById('domainValidationStatus');
        if (statusEl) {
            statusEl.remove();
        }

        // 显示错误对话框
        const errorHtml = `
            <div id="domainErrorModal" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.7);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 99999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <div style="
                    background: white;
                    padding: 40px;
                    border-radius: 12px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    text-align: center;
                    max-width: 500px;
                    margin: 20px;
                ">
                    <div style="color: #ff4d4f; font-size: 48px; margin-bottom: 20px;">🚫</div>
                    <h2 style="color: #1f2937; margin-bottom: 15px; font-size: 24px;">域名配置错误</h2>
                    <p style="color: #6b7280; margin-bottom: 20px; line-height: 1.6;">
                        ${message}
                    </p>
                    <div style="
                        background: #f9fafb;
                        padding: 15px;
                        border-radius: 8px;
                        margin-bottom: 25px;
                        text-align: left;
                        font-size: 14px;
                    ">
                        <strong>当前域名：</strong> ${window.location.hostname}<br>
                        <strong>访问地址：</strong> ${window.location.href}
                    </div>
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="location.reload()" style="
                            background: #1890ff;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: 600;
                            cursor: pointer;
                            transition: background-color 0.2s;
                        ">刷新页面</button>
                        <button onclick="window.close()" style="
                            background: #6b7280;
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: 600;
                            cursor: pointer;
                            transition: background-color 0.2s;
                        ">关闭页面</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', errorHtml);
        
        // 停止任何进一步的脚本执行
        throw new Error('域名验证失败，停止应用加载');
    }

    /**
     * 清理验证状态显示
     */
    cleanup() {
        const statusEl = document.getElementById('domainValidationStatus');
        if (statusEl) {
            statusEl.remove();
        }
    }
}

// 导出到全局作用域
window.DomainValidator = DomainValidator;

console.log('✅ DomainValidator 轻量级域名验证器已加载'); 