/**
 * API代码生成器
 * 从ApiModule.js提取的代码生成相关功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class ApiCodeGenerator {
    constructor() {
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.templates = {};
        
        // 设置全局实例供HTML事件调用
        window.apiCodeGenerator = this;
        
        console.log('✅ ApiCodeGenerator initialized');
        this.initializeTemplates();
    }

    /**
     * 统一渲染方法 - 路由系统调用
     */
    async render(container, params = {}) {
        try {
            console.log('🎯 ApiCodeGenerator渲染 - 参数:', params);
            
            // 显示加载状态
            container.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><div class="mt-2">正在加载代码生成器...</div></div>';
            
            // 根据view参数决定显示内容
            if (params.view === 'docs') {
                container.innerHTML = this.generateApiDocsContent();
            } else {
                container.innerHTML = this.generateCodeGeneratorContent();
            }
            
            // 设置全局引用以便HTML中的onclick调用
            window.apiModule = this;
            
            // 初始化代码生成器
            setTimeout(() => {
                this.initializeIfNeeded();
            }, 100);
            
        } catch (error) {
            console.error('❌ ApiCodeGenerator渲染失败:', error);
            container.innerHTML = `<div class="alert alert-danger">代码生成器加载失败: ${error.message}</div>`;
        }
    }

    /**
     * 生成API文档内容
     */
    generateApiDocsContent() {
        return `
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-book me-2"></i>API接口文档
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="bi bi-book" style="font-size: 3rem; color: #ddd;"></i>
                        <h5 class="mt-3 text-muted">API文档</h5>
                        <p class="text-muted">完整的API接口文档正在开发中...</p>
                        <a href="#" class="btn btn-primary" onclick="window.open('https://developer.paypal.com/docs/api/', '_blank')">
                            <i class="bi bi-box-arrow-up-right me-2"></i>查看在线文档
                        </a>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成代码生成器相关的HTML内容
     */
    generateCodeGeneratorContent() {
        return `
            <div class="row">
                <!-- 代码生成工具 -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-code-slash me-2"></i>API代码生成器
                            </h6>
                        </div>
                        <div class="card-body">
                            <form id="codeGeneratorForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">编程语言</label>
                                        <select class="form-select" id="codeLanguage" onchange="window.apiCodeGenerator?.updateCodeTemplate()">
                                            <option value="php">PHP</option>
                                            <option value="javascript">JavaScript</option>
                                            <option value="python">Python</option>
                                            <option value="java">Java</option>
                                            <option value="csharp">C#</option>
                                            <option value="curl">cURL</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">API类型</label>
                                        <select class="form-select" id="apiTypeForCode" onchange="window.apiCodeGenerator?.updateCodeTemplate()">
                                            <option value="payment">支付接口</option>
                                            <option value="refund">退款接口</option>
                                            <option value="query">查询接口</option>
                                            <option value="callback">回调处理</option>
                                            <option value="signature">签名验证</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">API密钥</label>
                                        <input type="text" class="form-control" id="apiKeyForCode" 
                                               placeholder="your-api-key" value="">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">商户ID</label>
                                        <input type="text" class="form-control" id="merchantIdForCode" 
                                               placeholder="your-merchant-id" value="">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">API端点</label>
                                    <input type="text" class="form-control" id="apiEndpoint" 
                                           placeholder="https://api.example.com/v1/payment" value="">
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-primary" onclick="window.apiCodeGenerator?.generateCode()">
                                        <i class="bi bi-gear me-2"></i>生成代码
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="window.apiCodeGenerator?.copyGeneratedCode()">
                                        <i class="bi bi-clipboard me-2"></i>复制代码
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="window.apiCodeGenerator?.downloadCode()">
                                        <i class="bi bi-download me-2"></i>下载文件
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 代码模板选择 -->
                <div class="col-lg-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-file-code me-2"></i>代码模板
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <a href="#" class="list-group-item list-group-item-action" onclick="window.apiCodeGenerator?.loadCodeTemplate('basic_request')">
                                    <i class="bi bi-play-circle me-2"></i>基础请求
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" onclick="window.apiCodeGenerator?.loadCodeTemplate('with_signature')">
                                    <i class="bi bi-shield-check me-2"></i>带签名验证
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" onclick="window.apiCodeGenerator?.loadCodeTemplate('error_handling')">
                                    <i class="bi bi-exclamation-triangle me-2"></i>错误处理
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" onclick="window.apiCodeGenerator?.loadCodeTemplate('async_callback')">
                                    <i class="bi bi-arrow-left-right me-2"></i>异步回调
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" onclick="window.apiCodeGenerator?.loadCodeTemplate('batch_request')">
                                    <i class="bi bi-layers me-2"></i>批量请求
                                </a>
                            </div>
                            
                            <hr class="my-3">
                            
                            <h6 class="text-muted">SDK下载</h6>
                            <div class="d-grid gap-2">
                                <button class="btn btn-sm btn-outline-primary" onclick="window.apiCodeGenerator?.downloadSDK('php')">
                                    <i class="bi bi-download me-2"></i>PHP SDK
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="window.apiCodeGenerator?.downloadSDK('javascript')">
                                    <i class="bi bi-download me-2"></i>JavaScript SDK
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="window.apiCodeGenerator?.downloadSDK('python')">
                                    <i class="bi bi-download me-2"></i>Python SDK
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 生成的代码 -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-file-earmark-code me-2"></i>生成的代码
                    </h6>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary" onclick="window.apiCodeGenerator?.formatCode()">
                            <i class="bi bi-magic me-1"></i>格式化
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="window.apiCodeGenerator?.clearGeneratedCode()">
                            <i class="bi bi-trash me-1"></i>清空
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="generatedCodeContainer">
                        <div class="text-center py-4 text-muted">
                            <i class="bi bi-code-slash" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">等待生成代码</h5>
                            <p>选择语言和API类型，点击"生成代码"按钮</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 初始化代码模板
     */
    initializeTemplates() {
        this.templates = {
            php: {
                payment: `<?php
// PayPal API 支付接口 - PHP示例
class PayPalPayment {
    private $apiKey;
    private $merchantId;
    private $apiUrl;
    
    public function __construct($apiKey, $merchantId, $apiUrl = '{{api_endpoint}}') {
        $this->apiKey = $apiKey;
        $this->merchantId = $merchantId;
        $this->apiUrl = $apiUrl;
    }
    
    public function createPayment($amount, $currency = 'USD', $description = '') {
        $data = [
            'merchant_id' => $this->merchantId,
            'amount' => $amount,
            'currency' => $currency,
            'description' => $description,
            'timestamp' => time()
        ];
        
        // 生成签名
        $data['signature'] = $this->generateSignature($data);
        
        // 发送请求
        $response = $this->sendRequest($data);
        
        return $response;
    }
    
    private function generateSignature($data) {
        ksort($data);
        $signString = '';
        foreach ($data as $key => $value) {
            if ($key !== 'signature' && $value !== '') {
                $signString .= $key . '=' . $value . '&';
            }
        }
        $signString = rtrim($signString, '&');
        return hash_hmac('sha256', $signString, $this->apiKey);
    }
    
    private function sendRequest($data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'X-API-Key: ' . $this->apiKey
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('API请求失败: HTTP ' . $httpCode);
        }
        
        return json_decode($response, true);
    }
}

// 使用示例
try {
    $payment = new PayPalPayment('{{api_key}}', '{{merchant_id}}');
    $result = $payment->createPayment(100.00, 'USD', '测试支付');
    
    if ($result['success']) {
        echo '支付创建成功: ' . $result['transaction_id'];
    } else {
        echo '支付创建失败: ' . $result['message'];
    }
} catch (Exception $e) {
    echo '错误: ' . $e->getMessage();
}
?>`,
                refund: `<?php
// PayPal API 退款接口 - PHP示例
class PayPalRefund {
    private $apiKey;
    private $merchantId;
    private $apiUrl;
    
    public function __construct($apiKey, $merchantId, $apiUrl = '{{api_endpoint}}') {
        $this->apiKey = $apiKey;
        $this->merchantId = $merchantId;
        $this->apiUrl = $apiUrl;
    }
    
    public function processRefund($transactionId, $amount, $reason = '') {
        $data = [
            'merchant_id' => $this->merchantId,
            'transaction_id' => $transactionId,
            'amount' => $amount,
            'reason' => $reason,
            'timestamp' => time()
        ];
        
        // 生成签名
        $data['signature'] = $this->generateSignature($data);
        
        // 发送请求
        $response = $this->sendRequest($data);
        
        return $response;
    }
    
    private function generateSignature($data) {
        ksort($data);
        $signString = '';
        foreach ($data as $key => $value) {
            if ($key !== 'signature' && $value !== '') {
                $signString .= $key . '=' . $value . '&';
            }
        }
        $signString = rtrim($signString, '&');
        return hash_hmac('sha256', $signString, $this->apiKey);
    }
    
    private function sendRequest($data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'X-API-Key: ' . $this->apiKey
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('API请求失败: HTTP ' . $httpCode);
        }
        
        return json_decode($response, true);
    }
}

// 使用示例
try {
    $refund = new PayPalRefund('{{api_key}}', '{{merchant_id}}');
    $result = $refund->processRefund('TXN123456', 50.00, '客户要求退款');
    
    if ($result['success']) {
        echo '退款处理成功: ' . $result['refund_id'];
    } else {
        echo '退款处理失败: ' . $result['message'];
    }
} catch (Exception $e) {
    echo '错误: ' . $e->getMessage();
}
?>`
            },
            javascript: {
                payment: `// PayPal API 支付接口 - JavaScript示例
class PayPalPayment {
    constructor(apiKey, merchantId, apiUrl = '{{api_endpoint}}') {
        this.apiKey = apiKey;
        this.merchantId = merchantId;
        this.apiUrl = apiUrl;
    }
    
    async createPayment(amount, currency = 'USD', description = '') {
        const data = {
            merchant_id: this.merchantId,
            amount: amount,
            currency: currency,
            description: description,
            timestamp: Math.floor(Date.now() / 1000)
        };
        
        // 生成签名
        data.signature = await this.generateSignature(data);
        
        // 发送请求
        const response = await this.sendRequest(data);
        
        return response;
    }
    
    async generateSignature(data) {
        const sortedKeys = Object.keys(data).sort();
        let signString = '';
        
        sortedKeys.forEach(key => {
            if (key !== 'signature' && data[key] !== '') {
                signString += key + '=' + data[key] + '&';
            }
        });
        
        signString = signString.slice(0, -1); // 移除最后的&
        
        // 使用crypto-js或其他HMAC库
        const crypto = require('crypto');
        return crypto.createHmac('sha256', this.apiKey).update(signString).digest('hex');
    }
    
    async sendRequest(data) {
        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': this.apiKey
                },
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error('API请求失败: HTTP ' + response.status);
            }
            
            return await response.json();
        } catch (error) {
            throw new Error('请求失败: ' + error.message);
        }
    }
}

// 使用示例
(async () => {
    try {
        const payment = new PayPalPayment('{{api_key}}', '{{merchant_id}}');
        const result = await payment.createPayment(100.00, 'USD', '测试支付');
        
        if (result.code === 200) {
            console.log('支付创建成功:', result.transaction_id);
        } else {
            console.log('支付创建失败:', result.message);
        }
    } catch (error) {
        console.error('错误:', error.message);
    }
})();`,
                refund: `// PayPal API 退款接口 - JavaScript示例
class PayPalRefund {
    constructor(apiKey, merchantId, apiUrl = '{{api_endpoint}}') {
        this.apiKey = apiKey;
        this.merchantId = merchantId;
        this.apiUrl = apiUrl;
    }
    
    async processRefund(transactionId, amount, reason = '') {
        const data = {
            merchant_id: this.merchantId,
            transaction_id: transactionId,
            amount: amount,
            reason: reason,
            timestamp: Math.floor(Date.now() / 1000)
        };
        
        // 生成签名
        data.signature = await this.generateSignature(data);
        
        // 发送请求
        const response = await this.sendRequest(data);
        
        return response;
    }
    
    async generateSignature(data) {
        const sortedKeys = Object.keys(data).sort();
        let signString = '';
        
        sortedKeys.forEach(key => {
            if (key !== 'signature' && data[key] !== '') {
                signString += key + '=' + data[key] + '&';
            }
        });
        
        signString = signString.slice(0, -1); // 移除最后的&
        
        // 使用crypto-js或其他HMAC库
        const crypto = require('crypto');
        return crypto.createHmac('sha256', this.apiKey).update(signString).digest('hex');
    }
    
    async sendRequest(data) {
        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': this.apiKey
                },
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error('API请求失败: HTTP ' + response.status);
            }
            
            return await response.json();
        } catch (error) {
            throw new Error('请求失败: ' + error.message);
        }
    }
}

// 使用示例
(async () => {
    try {
        const refund = new PayPalRefund('{{api_key}}', '{{merchant_id}}');
        const result = await refund.processRefund('TXN123456', 50.00, '客户要求退款');
        
        if (result.code === 200) {
            console.log('退款处理成功:', result.refund_id);
        } else {
            console.log('退款处理失败:', result.message);
        }
    } catch (error) {
        console.error('错误:', error.message);
    }
})();`
            },
            python: {
                payment: `# PayPal API 支付接口 - Python示例
import requests
import hashlib
import hmac
import json
import time

class PayPalPayment:
    def __init__(self, api_key, merchant_id, api_url='{{api_endpoint}}'):
        self.api_key = api_key
        self.merchant_id = merchant_id
        self.api_url = api_url
    
    def create_payment(self, amount, currency='USD', description=''):
        data = {
            'merchant_id': self.merchant_id,
            'amount': amount,
            'currency': currency,
            'description': description,
            'timestamp': int(time.time())
        }
        
        # 生成签名
        data['signature'] = self.generate_signature(data)
        
        # 发送请求
        response = self.send_request(data)
        
        return response
    
    def generate_signature(self, data):
        sorted_keys = sorted(data.keys())
        sign_string = ''
        
        for key in sorted_keys:
            if key != 'signature' and data[key] != '':
                sign_string += f"{key}={data[key]}&"
        
        sign_string = sign_string.rstrip('&')
        
        return hmac.new(
            self.api_key.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def send_request(self, data):
        try:
            headers = {
                'Content-Type': 'application/json',
                'X-API-Key': self.api_key
            }
            
            response = requests.post(
                self.api_url,
                headers=headers,
                data=json.dumps(data),
                timeout=30
            )
            
            if response.status_code != 200:
                raise Exception(f'API请求失败: HTTP {response.status_code}')
            
            return response.json()
            
        except Exception as e:
            raise Exception(f'请求失败: {str(e)}')

# 使用示例
if __name__ == '__main__':
    try:
        payment = PayPalPayment('{{api_key}}', '{{merchant_id}}')
        result = payment.create_payment(100.00, 'USD', '测试支付')
        
        if result.get('success'):
            print(f'支付创建成功: {result.get("transaction_id")}')
        else:
            print(f'支付创建失败: {result.get("message")}')
            
    except Exception as e:
        print(f'错误: {str(e)}')`,
                refund: `# PayPal API 退款接口 - Python示例
import requests
import hashlib
import hmac
import json
import time

class PayPalRefund:
    def __init__(self, api_key, merchant_id, api_url='{{api_endpoint}}'):
        self.api_key = api_key
        self.merchant_id = merchant_id
        self.api_url = api_url
    
    def process_refund(self, transaction_id, amount, reason=''):
        data = {
            'merchant_id': self.merchant_id,
            'transaction_id': transaction_id,
            'amount': amount,
            'reason': reason,
            'timestamp': int(time.time())
        }
        
        # 生成签名
        data['signature'] = self.generate_signature(data)
        
        # 发送请求
        response = self.send_request(data)
        
        return response
    
    def generate_signature(self, data):
        sorted_keys = sorted(data.keys())
        sign_string = ''
        
        for key in sorted_keys:
            if key != 'signature' and data[key] != '':
                sign_string += f"{key}={data[key]}&"
        
        sign_string = sign_string.rstrip('&')
        
        return hmac.new(
            self.api_key.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def send_request(self, data):
        try:
            headers = {
                'Content-Type': 'application/json',
                'X-API-Key': self.api_key
            }
            
            response = requests.post(
                self.api_url,
                headers=headers,
                data=json.dumps(data),
                timeout=30
            )
            
            if response.status_code != 200:
                raise Exception(f'API请求失败: HTTP {response.status_code}')
            
            return response.json()
            
        except Exception as e:
            raise Exception(f'请求失败: {str(e)}')

# 使用示例
if __name__ == '__main__':
    try:
        refund = PayPalRefund('{{api_key}}', '{{merchant_id}}')
        result = refund.process_refund('TXN123456', 50.00, '客户要求退款')
        
        if result.get('success'):
            print(f'退款处理成功: {result.get("refund_id")}')
        else:
            print(f'退款处理失败: {result.get("message")}')
            
    except Exception as e:
        print(f'错误: {str(e)}')`
            },
            curl: {
                payment: `# PayPal API 支付接口 - cURL示例

# 基础支付请求
curl -X POST "{{api_endpoint}}" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: {{api_key}}" \\
  -d '{
    "merchant_id": "{{merchant_id}}",
    "amount": 100.00,
    "currency": "USD",
    "description": "测试支付",
    "timestamp": 1640995200,
    "signature": "generated_signature_here"
  }'

# 带签名的完整请求示例
# 1. 准备数据
MERCHANT_ID="{{merchant_id}}"
AMOUNT="100.00"
CURRENCY="USD"
DESCRIPTION="测试支付"
TIMESTAMP=\$(date +%s)
API_KEY="{{api_key}}"

# 2. 生成签名字符串
SIGN_STRING="amount=\${AMOUNT}&currency=\${CURRENCY}&description=\${DESCRIPTION}&merchant_id=\${MERCHANT_ID}&timestamp=\${TIMESTAMP}"

# 3. 生成HMAC-SHA256签名
SIGNATURE=\$(echo -n "\$SIGN_STRING" | openssl dgst -sha256 -hmac "\$API_KEY" -binary | base64)

# 4. 发送请求
curl -X POST "{{api_endpoint}}" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: \$API_KEY" \\
  -d "{
    \\"merchant_id\\": \\"\$MERCHANT_ID\\",
    \\"amount\\": \$AMOUNT,
    \\"currency\\": \\"\$CURRENCY\\",
    \\"description\\": \\"\$DESCRIPTION\\",
    \\"timestamp\\": \$TIMESTAMP,
    \\"signature\\": \\"\$SIGNATURE\\"
  }"`,
                refund: `# PayPal API 退款接口 - cURL示例

# 基础退款请求
curl -X POST "{{api_endpoint}}" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: {{api_key}}" \\
  -d '{
    "merchant_id": "{{merchant_id}}",
    "transaction_id": "TXN123456",
    "amount": 50.00,
    "reason": "客户要求退款",
    "timestamp": 1640995200,
    "signature": "generated_signature_here"
  }'

# 带签名的完整请求示例
# 1. 准备数据
MERCHANT_ID="{{merchant_id}}"
TRANSACTION_ID="TXN123456"
AMOUNT="50.00"
REASON="客户要求退款"
TIMESTAMP=\$(date +%s)
API_KEY="{{api_key}}"

# 2. 生成签名字符串
SIGN_STRING="amount=\${AMOUNT}&merchant_id=\${MERCHANT_ID}&reason=\${REASON}&timestamp=\${TIMESTAMP}&transaction_id=\${TRANSACTION_ID}"

# 3. 生成HMAC-SHA256签名
SIGNATURE=\$(echo -n "\$SIGN_STRING" | openssl dgst -sha256 -hmac "\$API_KEY" -binary | base64)

# 4. 发送请求
curl -X POST "{{api_endpoint}}" \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: \$API_KEY" \\
  -d "{
    \\"merchant_id\\": \\"\$MERCHANT_ID\\",
    \\"transaction_id\\": \\"\$TRANSACTION_ID\\",
    \\"amount\\": \$AMOUNT,
    \\"reason\\": \\"\$REASON\\",
    \\"timestamp\\": \$TIMESTAMP,
    \\"signature\\": \\"\$SIGNATURE\\"
  }"`
            }
        };
    }

    /**
     * 生成代码
     */
    generateCode() {
        const language = document.getElementById('codeLanguage')?.value || 'php';
        const apiType = document.getElementById('apiTypeForCode')?.value || 'payment';
        const apiKey = document.getElementById('apiKeyForCode')?.value || 'your-api-key';
        const merchantId = document.getElementById('merchantIdForCode')?.value || 'your-merchant-id';
        const endpoint = document.getElementById('apiEndpoint')?.value || 'https://api.example.com/v1/payment';

        try {
            console.log('🔄 生成代码:', { language, apiType, apiKey, merchantId, endpoint });

            // 获取模板
            const template = this.getTemplate(language, apiType);
            if (!template) {
                this.showError(`暂不支持 ${language} 语言的 ${apiType} 模板`);
                return;
            }

            // 替换模板变量
            let generatedCode = template
                .replace(/\{\{api_key\}\}/g, apiKey)
                .replace(/\{\{merchant_id\}\}/g, merchantId)
                .replace(/\{\{api_endpoint\}\}/g, endpoint);

            // 显示生成的代码
            this.displayGeneratedCode(generatedCode, language);
            
            this.showSuccess('代码生成成功');

        } catch (error) {
            console.error('❌ 代码生成失败:', error);
            this.showError('代码生成失败: ' + error.message);
        }
    }

    /**
     * 获取代码模板
     */
    getTemplate(language, apiType) {
        return this.templates[language]?.[apiType] || null;
    }

    /**
     * 显示生成的代码
     */
    displayGeneratedCode(code, language) {
        const container = document.getElementById('generatedCodeContainer');
        if (!container) return;

        const languageClass = this.getLanguageClass(language);
        
        container.innerHTML = `
            <div class="code-header mb-2">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="badge bg-primary">${language.toUpperCase()}</span>
                    <small class="text-muted">${new Date().toLocaleString()}</small>
                </div>
            </div>
            <pre class="bg-dark text-light p-3 rounded"><code class="${languageClass}">${this.escapeHtml(code)}</code></pre>
        `;

        // 如果有语法高亮库，可以在这里调用
        this.highlightCode();
    }

    /**
     * 获取语言的CSS类名（用于语法高亮）
     */
    getLanguageClass(language) {
        const classMap = {
            'php': 'language-php',
            'javascript': 'language-javascript',
            'python': 'language-python',
            'java': 'language-java',
            'csharp': 'language-csharp',
            'curl': 'language-bash'
        };
        return classMap[language] || 'language-text';
    }

    /**
     * 转义HTML字符
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 语法高亮（如果有Prism.js或highlight.js）
     */
    highlightCode() {
        // 如果页面中有Prism.js
        if (window.Prism) {
            window.Prism.highlightAll();
        }
        // 如果页面中有highlight.js
        else if (window.hljs) {
            window.hljs.highlightAll();
        }
    }

    /**
     * 复制生成的代码
     */
    copyGeneratedCode() {
        const codeElement = document.querySelector('#generatedCodeContainer pre code');
        if (codeElement) {
            const text = codeElement.textContent;
            navigator.clipboard.writeText(text).then(() => {
                this.showSuccess('代码已复制到剪贴板');
            }).catch(() => {
                this.showError('复制失败');
            });
        } else {
            this.showError('没有可复制的代码');
        }
    }

    /**
     * 下载代码文件
     */
    downloadCode() {
        const codeElement = document.querySelector('#generatedCodeContainer pre code');
        if (!codeElement) {
            this.showError('没有可下载的代码');
            return;
        }

        const language = document.getElementById('codeLanguage')?.value || 'php';
        const apiType = document.getElementById('apiTypeForCode')?.value || 'payment';
        const code = codeElement.textContent;
        
        const fileExtensions = {
            'php': 'php',
            'javascript': 'js',
            'python': 'py',
            'java': 'java',
            'csharp': 'cs',
            'curl': 'sh'
        };
        
        const extension = fileExtensions[language] || 'txt';
        const filename = `paypal-${apiType}-${language}.${extension}`;
        
        const blob = new Blob([code], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess(`代码文件已下载: ${filename}`);
    }

    /**
     * 格式化代码
     */
    formatCode() {
        // 这里可以添加代码格式化逻辑
        this.showSuccess('代码格式化完成');
    }

    /**
     * 清空生成的代码
     */
    clearGeneratedCode() {
        const container = document.getElementById('generatedCodeContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="bi bi-code-slash" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">等待生成代码</h5>
                    <p>选择语言和API类型，点击"生成代码"按钮</p>
                </div>
            `;
        }
    }

    /**
     * 更新代码模板
     */
    updateCodeTemplate() {
        // 当语言或API类型改变时，可以预览模板
        const language = document.getElementById('codeLanguage')?.value;
        const apiType = document.getElementById('apiTypeForCode')?.value;
        
        if (language && apiType) {
            console.log(`📝 切换到 ${language} - ${apiType} 模板`);
        }
    }

    /**
     * 加载代码模板
     */
    loadCodeTemplate(templateType) {
        // 根据模板类型设置相应的参数
        const templates = {
            'basic_request': { language: 'php', apiType: 'payment' },
            'with_signature': { language: 'php', apiType: 'payment' },
            'error_handling': { language: 'javascript', apiType: 'payment' },
            'async_callback': { language: 'javascript', apiType: 'callback' },
            'batch_request': { language: 'python', apiType: 'payment' }
        };
        
        const template = templates[templateType];
        if (template) {
            document.getElementById('codeLanguage').value = template.language;
            document.getElementById('apiTypeForCode').value = template.apiType;
            this.generateCode();
        }
    }

    /**
     * 下载SDK
     */
    downloadSDK(language) {
        // 这里可以生成完整的SDK包
        const sdkFiles = {
            'php': {
                filename: 'paypal-php-sdk.zip',
                description: 'PayPal PHP SDK'
            },
            'javascript': {
                filename: 'paypal-js-sdk.zip', 
                description: 'PayPal JavaScript SDK'
            },
            'python': {
                filename: 'paypal-python-sdk.zip',
                description: 'PayPal Python SDK'
            }
        };
        
        const sdk = sdkFiles[language];
        if (sdk) {
            this.showSuccess(`正在准备下载 ${sdk.description}...`);
            // 这里可以触发实际的SDK下载
        } else {
            this.showError(`暂不支持 ${language} SDK`);
        }
    }

    /**
     * 初始化代码生成器（如果需要）
     */
    initializeIfNeeded() {
        // 如果是代码生成器视图，清空生成的代码区域
        if (document.getElementById('generatedCodeContainer')) {
            this.clearGeneratedCode();
        }
        console.log('✅ 代码生成器初始化完成');
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        if (window.apiModule && window.apiModule.showSuccess && window.apiModule.showSuccess !== this.showSuccess) {
            window.apiModule.showSuccess(message);
        } else {
            console.log('✅ 成功:', message);
            // 简单的成功提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-check-circle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        if (window.apiModule && window.apiModule.showError && window.apiModule.showError !== this.showError) {
            window.apiModule.showError(message);
        } else {
            console.error('❌ 错误:', message);
            // 简单的错误提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }
    }
}

// 导出类
window.ApiCodeGenerator = ApiCodeGenerator;