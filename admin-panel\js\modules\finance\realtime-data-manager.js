/**
 * 实时数据监控管理器 - 四层架构财务系统核心模块
 * 负责财务数据的实时监控、统计分析和可视化展示
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class RealtimeDataManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        
        this.refreshInterval = 5000; // 5秒刷新一次
        this.refreshTimer = null;
        this.charts = {};
        this.websocket = null;
        
        console.log('📊 RealtimeDataManager initialized for tenant:', this.tenantInfo?.tenant_type);
    }

    /**
     * 初始化实时数据监控器
     */
    async init(container) {
        try {
            this.container = container;
            await this.renderRealtimeDataPage();
            await this.loadRealtimeData();
            this.initializeCharts();
            this.startRealtimeUpdates();
            this.bindEvents();
            
            console.log('✅ 实时数据监控器初始化完成');
        } catch (error) {
            console.error('❌ 实时数据监控器初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    /**
     * 渲染实时数据监控页面
     */
    async renderRealtimeDataPage() {
        this.container.innerHTML = `
            <div class="realtime-data-manager">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="bi bi-speedometer2 me-2"></i>实时数据监控</h2>
                            <p class="text-muted mb-0">实时监控财务数据、交易状态和系统性能</p>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <div class="status-indicator">
                                <span class="badge bg-success" id="connectionStatus">
                                    <i class="bi bi-wifi me-1"></i>已连接
                                </span>
                            </div>
                            <div class="last-update text-muted">
                                最后更新: <span id="lastUpdateTime">--</span>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-outline-primary" onclick="realtimeDataManager.refreshData()" title="手动刷新">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                                <button class="btn btn-outline-info" onclick="realtimeDataManager.exportData()" title="导出数据">
                                    <i class="bi bi-download"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 核心指标概览 -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-icon bg-primary">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="todayRevenue">¥0</div>
                                <div class="metric-label">今日收入</div>
                                <div class="metric-change" id="revenueChange">+0%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-icon bg-success">
                                <i class="bi bi-receipt"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="todayTransactions">0</div>
                                <div class="metric-label">今日交易</div>
                                <div class="metric-change" id="transactionChange">+0%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-icon bg-info">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="activeUsers">0</div>
                                <div class="metric-label">活跃用户</div>
                                <div class="metric-change" id="userChange">+0%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-icon bg-warning">
                                <i class="bi bi-percent"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="successRate">0%</div>
                                <div class="metric-label">成功率</div>
                                <div class="metric-change" id="successRateChange">+0%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-icon bg-danger">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="errorCount">0</div>
                                <div class="metric-label">错误数</div>
                                <div class="metric-change" id="errorChange">+0%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-icon bg-secondary">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value" id="avgResponseTime">0ms</div>
                                <div class="metric-label">响应时间</div>
                                <div class="metric-change" id="responseTimeChange">+0%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时图表区域 -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="bi bi-graph-up me-2"></i>实时交易趋势
                                    </h6>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary active" data-chart-period="1h">1小时</button>
                                        <button class="btn btn-outline-primary" data-chart-period="6h">6小时</button>
                                        <button class="btn btn-outline-primary" data-chart-period="24h">24小时</button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <canvas id="transactionTrendChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-pie-chart me-2"></i>交易类型分布
                                </h6>
                            </div>
                            <div class="card-body">
                                <canvas id="transactionTypeChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细数据表格区域 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-building me-2"></i>码商实时数据
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>码商名称</th>
                                                <th>今日交易</th>
                                                <th>今日收入</th>
                                                <th>成功率</th>
                                                <th>状态</th>
                                            </tr>
                                        </thead>
                                        <tbody id="providerDataTableBody">
                                            <tr>
                                                <td colspan="5" class="text-center py-3">
                                                    <div class="spinner-border spinner-border-sm me-2"></div>
                                                    加载中...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-shop me-2"></i>商户实时数据
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>商户名称</th>
                                                <th>今日交易</th>
                                                <th>今日支付</th>
                                                <th>成功率</th>
                                                <th>状态</th>
                                            </tr>
                                        </thead>
                                        <tbody id="merchantDataTableBody">
                                            <tr>
                                                <td colspan="5" class="text-center py-3">
                                                    <div class="spinner-border spinner-border-sm me-2"></div>
                                                    加载中...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统状态监控 -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-cpu me-2"></i>系统性能
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>CPU使用率</span>
                                        <span id="cpuUsage">0%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" id="cpuProgressBar" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>内存使用率</span>
                                        <span id="memoryUsage">0%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-info" id="memoryProgressBar" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>磁盘使用率</span>
                                        <span id="diskUsage">0%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-warning" id="diskProgressBar" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-database me-2"></i>数据库状态
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="metric">
                                            <div class="metric-value text-primary" id="dbConnections">0</div>
                                            <div class="metric-label">连接数</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="metric">
                                            <div class="metric-value text-success" id="dbQueries">0</div>
                                            <div class="metric-label">查询/秒</div>
                                        </div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="metric">
                                            <div class="metric-value text-info" id="dbSize">0MB</div>
                                            <div class="metric-label">数据库大小</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="metric">
                                            <div class="metric-value text-warning" id="dbLatency">0ms</div>
                                            <div class="metric-label">延迟</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-shield-check me-2"></i>安全监控
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-success alert-sm">
                                    <i class="bi bi-check-circle me-2"></i>
                                    系统安全状态正常
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="metric">
                                            <div class="metric-value text-danger" id="suspiciousActivity">0</div>
                                            <div class="metric-label">可疑活动</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="metric">
                                            <div class="metric-value text-warning" id="blockedIPs">0</div>
                                            <div class="metric-label">封禁IP</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近交易记录 -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="bi bi-clock-history me-2"></i>最近交易记录
                            </h6>
                            <button class="btn btn-outline-primary btn-sm" onclick="realtimeDataManager.viewAllTransactions()">
                                查看全部
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>订单号</th>
                                        <th>商户</th>
                                        <th>码商</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>处理时间</th>
                                    </tr>
                                </thead>
                                <tbody id="recentTransactionsTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="spinner-border spinner-border-sm me-2"></div>
                                            加载中...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 图表时间段切换
        document.querySelectorAll('[data-chart-period]').forEach(button => {
            button.addEventListener('click', (e) => {
                document.querySelectorAll('[data-chart-period]').forEach(btn => btn.classList.remove('active'));
                e.target.classList.add('active');
                this.updateChartPeriod(e.target.dataset.chartPeriod);
            });
        });

        // 页面可见性变化事件
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseRealtimeUpdates();
            } else {
                this.resumeRealtimeUpdates();
            }
        });
    }

    /**
     * 加载实时数据
     */
    async loadRealtimeData() {
        try {
            const response = await this.apiClient.get('/admin/finance/realtime-data');

            if (response.success) {
                const data = response.data;
                this.updateMetrics(data.metrics);
                this.updateProviderData(data.providers);
                this.updateMerchantData(data.merchants);
                this.updateSystemStatus(data.system);
                this.updateRecentTransactions(data.recent_transactions);
                this.updateLastUpdateTime();
            } else {
                throw new Error(response.message || '加载数据失败');
            }

        } catch (error) {
            console.error('加载实时数据失败:', error);
            this.showConnectionError();
        }
    }

    /**
     * 更新核心指标
     */
    updateMetrics(metrics) {
        document.getElementById('todayRevenue').textContent = '¥' + this.formatAmount(metrics.today_revenue);
        document.getElementById('todayTransactions').textContent = this.formatNumber(metrics.today_transactions);
        document.getElementById('activeUsers').textContent = this.formatNumber(metrics.active_users);
        document.getElementById('successRate').textContent = metrics.success_rate + '%';
        document.getElementById('errorCount').textContent = this.formatNumber(metrics.error_count);
        document.getElementById('avgResponseTime').textContent = metrics.avg_response_time + 'ms';

        // 更新变化趋势
        this.updateMetricChange('revenueChange', metrics.revenue_change);
        this.updateMetricChange('transactionChange', metrics.transaction_change);
        this.updateMetricChange('userChange', metrics.user_change);
        this.updateMetricChange('successRateChange', metrics.success_rate_change);
        this.updateMetricChange('errorChange', metrics.error_change);
        this.updateMetricChange('responseTimeChange', metrics.response_time_change);
    }

    /**
     * 更新指标变化
     */
    updateMetricChange(elementId, change) {
        const element = document.getElementById(elementId);
        const value = parseFloat(change) || 0;
        
        element.textContent = (value >= 0 ? '+' : '') + value.toFixed(1) + '%';
        element.className = 'metric-change ' + (value >= 0 ? 'text-success' : 'text-danger');
    }

    /**
     * 更新码商数据
     */
    updateProviderData(providers) {
        const tbody = document.getElementById('providerDataTableBody');
        
        if (!providers || providers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-3 text-muted">暂无数据</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = providers.map(provider => `
            <tr>
                <td>
                    <div class="fw-medium">${provider.name}</div>
                    <small class="text-muted">#${provider.id}</small>
                </td>
                <td>${this.formatNumber(provider.today_transactions)}</td>
                <td class="text-success">¥${this.formatAmount(provider.today_revenue)}</td>
                <td>
                    <span class="badge bg-${provider.success_rate >= 95 ? 'success' : provider.success_rate >= 90 ? 'warning' : 'danger'}">
                        ${provider.success_rate}%
                    </span>
                </td>
                <td>
                    <span class="badge bg-${provider.is_online ? 'success' : 'secondary'}">
                        ${provider.is_online ? '在线' : '离线'}
                    </span>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 更新商户数据
     */
    updateMerchantData(merchants) {
        const tbody = document.getElementById('merchantDataTableBody');
        
        if (!merchants || merchants.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center py-3 text-muted">暂无数据</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = merchants.map(merchant => `
            <tr>
                <td>
                    <div class="fw-medium">${merchant.name}</div>
                    <small class="text-muted">#${merchant.id}</small>
                </td>
                <td>${this.formatNumber(merchant.today_transactions)}</td>
                <td class="text-primary">¥${this.formatAmount(merchant.today_amount)}</td>
                <td>
                    <span class="badge bg-${merchant.success_rate >= 95 ? 'success' : merchant.success_rate >= 90 ? 'warning' : 'danger'}">
                        ${merchant.success_rate}%
                    </span>
                </td>
                <td>
                    <span class="badge bg-${merchant.is_active ? 'success' : 'secondary'}">
                        ${merchant.is_active ? '活跃' : '非活跃'}
                    </span>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 更新系统状态
     */
    updateSystemStatus(system) {
        // 系统性能
        document.getElementById('cpuUsage').textContent = system.cpu_usage + '%';
        document.getElementById('cpuProgressBar').style.width = system.cpu_usage + '%';
        document.getElementById('memoryUsage').textContent = system.memory_usage + '%';
        document.getElementById('memoryProgressBar').style.width = system.memory_usage + '%';
        document.getElementById('diskUsage').textContent = system.disk_usage + '%';
        document.getElementById('diskProgressBar').style.width = system.disk_usage + '%';

        // 数据库状态
        document.getElementById('dbConnections').textContent = system.db_connections;
        document.getElementById('dbQueries').textContent = system.db_queries_per_sec;
        document.getElementById('dbSize').textContent = system.db_size + 'MB';
        document.getElementById('dbLatency').textContent = system.db_latency + 'ms';

        // 安全监控
        document.getElementById('suspiciousActivity').textContent = system.suspicious_activity;
        document.getElementById('blockedIPs').textContent = system.blocked_ips;
    }

    /**
     * 更新最近交易记录
     */
    updateRecentTransactions(transactions) {
        const tbody = document.getElementById('recentTransactionsTableBody');
        
        if (!transactions || transactions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4 text-muted">暂无交易记录</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = transactions.map(transaction => `
            <tr>
                <td>${this.formatTime(transaction.created_at)}</td>
                <td>
                    <div class="fw-medium">${transaction.order_no}</div>
                </td>
                <td>${transaction.merchant_name}</td>
                <td>${transaction.provider_name}</td>
                <td class="text-primary">¥${this.formatAmount(transaction.amount)}</td>
                <td>
                    <span class="badge bg-${this.getTransactionStatusColor(transaction.status)}">
                        ${this.getTransactionStatusText(transaction.status)}
                    </span>
                </td>
                <td>${transaction.process_time}ms</td>
            </tr>
        `).join('');
    }

    /**
     * 初始化图表
     */
    initializeCharts() {
        // 交易趋势图表
        const trendCtx = document.getElementById('transactionTrendChart').getContext('2d');
        this.charts.trendChart = new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '交易金额',
                    data: [],
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.4
                }, {
                    label: '交易笔数',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    yAxisID: 'y1',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

        // 交易类型分布图表
        const typeCtx = document.getElementById('transactionTypeChart').getContext('2d');
        this.charts.typeChart = new Chart(typeCtx, {
            type: 'doughnut',
            data: {
                labels: ['支付宝', '微信', '银行卡', '其他'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        'rgb(54, 162, 235)',
                        'rgb(255, 99, 132)',
                        'rgb(255, 205, 86)',
                        'rgb(75, 192, 192)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    /**
     * 启动实时更新
     */
    startRealtimeUpdates() {
        this.refreshTimer = setInterval(() => {
            this.loadRealtimeData();
        }, this.refreshInterval);

        // 初始化WebSocket连接（如果支持）
        this.initWebSocket();
    }

    /**
     * 暂停实时更新
     */
    pauseRealtimeUpdates() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    /**
     * 恢复实时更新
     */
    resumeRealtimeUpdates() {
        if (!this.refreshTimer) {
            this.startRealtimeUpdates();
        }
    }

    /**
     * 初始化WebSocket连接
     */
    initWebSocket() {
        try {
            const wsUrl = `ws://${window.location.host}/ws/realtime-data`;
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('WebSocket连接已建立');
                this.updateConnectionStatus(true);
            };

            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };

            this.websocket.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.updateConnectionStatus(false);
                // 5秒后尝试重连
                setTimeout(() => this.initWebSocket(), 5000);
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.updateConnectionStatus(false);
            };

        } catch (error) {
            console.error('WebSocket初始化失败:', error);
            this.updateConnectionStatus(false);
        }
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'metrics_update':
                this.updateMetrics(data.data);
                break;
            case 'transaction_update':
                this.updateRecentTransactions([data.data, ...this.getRecentTransactions().slice(0, 9)]);
                break;
            case 'system_alert':
                this.showSystemAlert(data.data);
                break;
        }
    }

    /**
     * 更新连接状态
     */
    updateConnectionStatus(connected) {
        const statusEl = document.getElementById('connectionStatus');
        if (connected) {
            statusEl.innerHTML = '<i class="bi bi-wifi me-1"></i>已连接';
            statusEl.className = 'badge bg-success';
        } else {
            statusEl.innerHTML = '<i class="bi bi-wifi-off me-1"></i>连接中断';
            statusEl.className = 'badge bg-danger';
        }
    }

    /**
     * 更新最后更新时间
     */
    updateLastUpdateTime() {
        document.getElementById('lastUpdateTime').textContent = new Date().toLocaleTimeString('zh-CN');
    }

    /**
     * 手动刷新数据
     */
    async refreshData() {
        await this.loadRealtimeData();
    }

    /**
     * 格式化金额
     */
    formatAmount(amount) {
        return parseFloat(amount || 0).toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    /**
     * 格式化数字
     */
    formatNumber(number) {
        return parseInt(number || 0).toLocaleString('zh-CN');
    }

    /**
     * 格式化时间
     */
    formatTime(datetime) {
        return new Date(datetime).toLocaleTimeString('zh-CN');
    }

    /**
     * 获取交易状态颜色
     */
    getTransactionStatusColor(status) {
        const colorMap = {
            'success': 'success',
            'pending': 'warning',
            'failed': 'danger',
            'processing': 'info'
        };
        return colorMap[status] || 'secondary';
    }

    /**
     * 获取交易状态文本
     */
    getTransactionStatusText(status) {
        const textMap = {
            'success': '成功',
            'pending': '待处理',
            'failed': '失败',
            'processing': '处理中'
        };
        return textMap[status] || status;
    }

    /**
     * 显示连接错误
     */
    showConnectionError() {
        this.updateConnectionStatus(false);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error('❌', message);
    }

    /**
     * 销毁管理器
     */
    destroy() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        if (this.websocket) {
            this.websocket.close();
        }
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
    }
}

// 导出模块
window.RealtimeDataManager = RealtimeDataManager;

// 创建全局实例
if (typeof window.realtimeDataManager === 'undefined') {
    window.realtimeDataManager = new RealtimeDataManager();
}

console.log('📊 RealtimeDataManager 模块加载完成'); 