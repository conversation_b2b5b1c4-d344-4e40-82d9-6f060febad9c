<?php
// 管理后台主路由文件
$action = isset($_GET['action']) ? $_GET['action'] : '';

// 根据action路由到不同的模块
switch ($action) {
    // 认证相关
    case 'login':
        require_once dirname(__FILE__) . '/auth.php';
        break;
        
    // 仪表板
    case 'dashboard':
        require_once dirname(__FILE__) . '/dashboard.php';
        break;
        
    // 用户管理
    case 'users':
        require_once dirname(__FILE__) . '/users.php';
        break;
        
    // 员工管理
    case 'employees':
        require_once dirname(__FILE__) . '/employees.php';
        break;
        
    // 设备管理
    case 'devices':
        require_once dirname(__FILE__) . '/devices.php';
        break;
        
    // 码商管理
    case 'providers':
        require_once dirname(__FILE__) . '/providers.php';
        break;
        
    // 商户管理
    case 'merchants':
        require_once dirname(__FILE__) . '/merchants.php';
        break;
        
    // 产品管理
    case 'products':
        require_once dirname(__FILE__) . '/products.php';
        break;
        
    // 支付请求管理
    case 'payment_requests':
    case 'payment_request_detail':
    case 'update_payment_status':
    case 'retry_payment_request':
        require_once dirname(__FILE__) . '/payment_requests.php';
        break;
        
    // 订单管理
    case 'orders':
        require_once dirname(__FILE__) . '/orders.php';
        break;
        
    // 财务管理
    case 'financial':
        require_once dirname(__FILE__) . '/financial.php';
        break;
        
    // 风控管理
    case 'risk_control':
    case 'blacklist':
        require_once dirname(__FILE__) . '/risk_control.php';
        break;
        
    // 性能监控
    case 'performance_monitor':
        require_once dirname(__FILE__) . '/performance_monitor.php';
        break;
        
    // 安全日志
    case 'security_logs':
        require_once dirname(__FILE__) . '/security_logs.php';
        break;
        
    // 支付宝账户管理
    case 'alipay_accounts':
        require_once dirname(__FILE__) . '/alipay_accounts.php';
        break;
        
    // 交易记录
    case 'transactions':
        require_once dirname(__FILE__) . '/transactions.php';
        break;
        
    // 账户管理
    case 'accounts':
        require_once dirname(__FILE__) . '/accounts.php';
        break;
        
    // 权限管理
    case 'permissions':
    case 'permission_groups':
        require_once dirname(__FILE__) . '/permissions.php';
        break;
        
    // API文档
    case 'api_docs':
    case 'api_test':
        require_once dirname(__FILE__) . '/api_docs.php';
        break;
        
    default:
        http_response_code(404);
        echo json_encode(array(
            'code' => 404, 
            'message' => '接口不存在: ' . $action
        ));
        break;
}
?> 