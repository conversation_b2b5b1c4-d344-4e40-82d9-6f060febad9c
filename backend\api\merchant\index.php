<?php
/**
 * 商户后台API主路由 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

$module = isset($_GET['module']) ? $_GET['module'] : '';

try {
    switch ($module) {
        case 'auth':
            // 认证模块（登录等）
            require_once dirname(__FILE__) . '/auth.php';
            break;
            
        case 'api_docs':
            // API文档模块
            require_once dirname(__FILE__) . '/api_docs.php';
            break;
            
        case 'code_generator':
            // 代码生成器模块
            require_once dirname(__FILE__) . '/code_generator.php';
            break;
            
        case 'signature_tool':
            // 签名测试工具模块
            require_once dirname(__FILE__) . '/signature_tool.php';
            break;
            
        case 'tools':
            // 商户工具箱模块
            require_once dirname(__FILE__) . '/tools.php';
            break;
            
        case 'dashboard':
            // 仪表板（默认模块）
            handleDashboard();
            break;
            
        case 'info':
            // 系统信息
            handleSystemInfo();
            break;
            
        default:
            http_response_code(404);
            echo json_encode(array('code' => 404, 'message' => '模块不存在'));
    }
} catch (Exception $e) {
    error_log("Merchant Index API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(array('code' => 500, 'message' => '服务器错误: ' . $e->getMessage()));
}

// 处理仪表板请求
function handleDashboard() {
    try {
        // 初始化认证系统
        $auth = TenantAuth::init();
        
        // 检查认证
        if (!$auth->checkAuth()) {
            http_response_code(401);
            echo json_encode(array('code' => 401, 'message' => '请先登录'));
            return;
        }
        
        // 重定向到工具模块的仪表板
        $_GET['action'] = 'get_dashboard';
        require_once dirname(__FILE__) . '/tools.php';
        
    } catch (Exception $e) {
        error_log('处理仪表板失败: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(array('code' => 500, 'message' => '处理仪表板失败: ' . $e->getMessage()));
    }
}

// 处理系统信息请求
function handleSystemInfo() {
    try {
        // 初始化认证系统
        $auth = TenantAuth::init();
        
        // 检查认证
        if (!$auth->checkAuth()) {
            http_response_code(401);
            echo json_encode(array('code' => 401, 'message' => '请先登录'));
            return;
        }
        
        $currentUser = $auth->getCurrentUser();
        $db = $auth->getDatabase();
        
        // 获取商户信息
        $merchant = $db->fetch(
            "SELECT * FROM merchants WHERE user_id = ?",
            array($currentUser['id'])
        );
        
        if (!$merchant) {
            http_response_code(404);
            echo json_encode(array('code' => 404, 'message' => '商户信息不存在'));
            return;
        }
        
        // 记录操作日志
        $auth->logUserAction('view_system_info', 'merchant', $merchant['id'], '查看系统信息');
        
        $systemInfo = array(
            'system' => array(
                'name' => 'PayPal支付系统商户后台',
                'version' => '2.0.0',
                'api_version' => '2.0',
                'environment' => 'production',
                'timezone' => date_default_timezone_get(),
                'server_time' => date('Y-m-d H:i:s')
            ),
            'merchant' => array(
                'id' => $merchant['id'],
                'company_name' => $merchant['company_name'],
                'status' => $merchant['status'],
                'created_at' => $merchant['created_at'],
                'last_login' => date('Y-m-d H:i:s')
            ),
            'api_endpoints' => array(
                'base_url' => 'https://qrcode.top670.com',
                'merchant_api' => 'https://qrcode.top670.com/api/merchant/',
                'public_api' => 'https://qrcode.top670.com/api.php',
                'documentation' => 'https://qrcode.top670.com/docs'
            ),
            'features' => array(
                'api_documentation' => true,
                'code_generator' => true,
                'signature_tool' => true,
                'real_time_testing' => true,
                'order_management' => true,
                'statistics_dashboard' => true,
                'webhook_management' => true,
                'ip_whitelist' => true
            ),
            'supported_languages' => array(
                'PHP', 'Java', 'Python', 'Node.js', 'C#', 'Go'
            ),
            'api_limits' => array(
                'rate_limit' => '1000 requests/hour',
                'signature_timeout' => '5 minutes',
                'max_order_amount' => '999999.99',
                'callback_timeout' => '30 seconds'
            )
        );
        
        echo json_encode(array(
            'code' => 200,
            'message' => '系统信息获取成功',
            'data' => $systemInfo
        ));
        
    } catch (Exception $e) {
        error_log('获取系统信息失败: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(array('code' => 500, 'message' => '获取系统信息失败: ' . $e->getMessage()));
    }
}
?> 