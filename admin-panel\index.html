﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal管理系统</title>

    <!-- Bootstrap 5 CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            color: #212529;
        }

        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
        }

        .sidebar {
            width: 250px;
            height: 100vh;
            background: white;
            border-right: 1px solid #e9ecef;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
            z-index: 1000;
        }

        .main-content {
            margin-left: 250px;
            min-height: 100vh;
            background-color: #f8f9fa;
        }

        .d-none { display: none !important; }
        .d-block { display: block !important; }
    </style>
</head>
<body>
    <!-- 登录界面 -->
    <div id="loginContainer" class="login-container d-none">
        <div class="login-card">
            <div class="text-center mb-4">
                <h3><i class="bi-credit-card text-primary"></i> PayPal管理系统</h3>
                <p class="text-muted">请登录您的管理账户</p>
            </div>
            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="password" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">登录</button>
            </form>
            <div id="loginError" class="alert alert-danger mt-3 d-none"></div>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="appContainer" class="d-none">
        <div class="sidebar">
            <div class="p-3">
                <h4><i class="bi-credit-card"></i> PayPal管理</h4>
            </div>
            <div id="sidebarMenu">
                <!-- 菜单项将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="main-content">
            <div class="p-3">
                <h5 id="pageTitle">仪表板</h5>
                <div id="contentArea">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 系统初始化脚本 -->
    <script>
        console.log('🚀 PayPal管理系统启动...');

        document.addEventListener('DOMContentLoaded', async () => {
            try {
                await initializeSystem();
            } catch (error) {
                console.error('❌ 系统初始化失败:', error);
                showError(error.message);
            }
        });

        async function initializeSystem() {
            console.log('📄 开始系统初始化');

            try {
                // 加载域名验证器
                await loadScript('./js/core/domain-validator.js');
                console.log('✅ 域名验证器加载完成');

                // 获取域名信息
                const domainValidator = new DomainValidator();
                const domainInfo = await domainValidator.validateDomain();
                console.log('✅ 域名信息获取完成:', domainInfo);

                // 加载应用初始化器
                await loadScript('./js/core/app-initializer-new.js');
                console.log('✅ 应用初始化器加载完成');

                // 启动应用
                await AppInitializer.initialize(domainInfo);
                console.log('✅ 系统启动完成');

            } catch (error) {
                console.error('❌ 初始化错误:', error);
                throw error;
            }
        }

        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src + '?v=' + Date.now();
                script.onload = resolve;
                script.onerror = () => reject(new Error('Failed to load script: ' + src));
                document.head.appendChild(script);
            });
        }

        function showError(message) {
            document.body.innerHTML = `
                <div class="container mt-5">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="alert alert-danger text-center">
                                <h3>系统初始化失败</h3>
                                <p>${message}</p>
                                <button onclick="location.reload()" class="btn btn-primary">重新加载</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
