<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="PayPal管理系统 - 高性能版本">
    <meta name="keywords" content="PayPal, 管理系统, 支付, 金融">
    <meta name="author" content="PayPal Admin Team">
    
    <!-- 性能优化 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#3b82f6">
    
    <!-- 预加载关键资源 - 本地化 -->
    <link rel="preload" href="./css/bootstrap-local.css?v=20240628" as="style">
    <link rel="preload" href="./css/icons-local.css?v=20240628" as="style">

    <title>PayPal管理系统 - 正在加载...</title>

    <!-- 基础样式 - 本地化CSS避免CDN超时 -->
    <link href="./css/bootstrap-local.css?v=20240628" rel="stylesheet">
    <link href="./css/icons-local.css?v=20240628" rel="stylesheet">
    
    <!-- 优化版本的初始化样式 -->
    <style>
        /* 基础布局 - 移除冲突的背景设置 */
        body {
            margin: 0 !important;
            padding: 0 !important;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 容器基础样式 - 让CSS文件控制背景 */
        #appContainer {
            min-height: 100vh;
            display: none;
        }

        #loginContainer {
            min-height: 100vh;
            display: none;
        }

        /* 初始加载动画 */
        .initial-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
        }

        .loader-logo {
            font-size: 48px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        .loader-text {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 30px;
        }

        .loader-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 隐藏类 */
        .hidden {
            display: none !important;
        }

        /* 应用容器 */
        #appContainer {
            display: none;
            min-height: 100vh;
        }

        #loginContainer {
            display: none;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <!-- 初始加载器 -->
    <div id="initialLoader" class="initial-loader">
        <div class="loader-logo">🚀</div>
        <div class="loader-text">PayPal管理系统</div>
        <div class="loader-spinner"></div>
        <div style="margin-top: 20px; font-size: 14px; opacity: 0.8;">
            正在验证域名...
        </div>
    </div>

    <!-- 登录容器 -->
    <div id="loginContainer"></div>

    <!-- 主应用容器 -->
    <div id="appContainer"></div>

    <!-- 优化版本的启动脚本 -->
    <script>
        /**
         * PayPal系统优化启动器 v2.0
         * 实现域名优先验证的新架构
         */
        
        console.log('🚀 PayPal管理系统优化版本启动...');
        console.time('系统启动时间');

        // 全局配置
        window.PAYPAL_CONFIG = {
            version: '2.0.0',
            startTime: Date.now(),
            debug: true
        };

        /**
         * 主启动函数
         */
        async function initializePayPalSystem() {
            try {
                // 第一步：域名验证（最高优先级）
                console.log('📋 第一步：域名验证');
                await loadDomainValidator();
                
                const validator = new window.DomainValidator();
                const tenantInfo = await validator.validateDomain();
                
                if (!tenantInfo) {
                    console.error('❌ 域名验证失败，停止初始化');
                    return;
                }

                // 第二步：获取加载配置
                console.log('📋 第二步：获取加载配置');
                const tenantConfig = validator.getTenantLoadConfig();
                
                if (!tenantConfig) {
                    throw new Error('无法获取租户加载配置');
                }

                // 第三步：按需加载资源
                console.log('📋 第三步：按需加载资源');
                await loadResourceLoader();
                
                const loader = new window.ResourceLoader();
                await loader.loadTenantResources(tenantConfig);

                // 第四步：启动应用
                console.log('📋 第四步：启动应用');
                await startApplication(tenantConfig);

                // 隐藏初始加载器
                hideInitialLoader();
                
                console.timeEnd('系统启动时间');
                console.log('🎉 PayPal管理系统启动完成');

            } catch (error) {
                console.error('❌ 系统启动失败:', error);
                showStartupError(error);
            }
        }

        /**
         * 加载域名验证器
         */
        async function loadDomainValidator() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'js/core/domain-validator.js?v=' + Date.now();
                script.onload = () => {
                    console.log('✅ 域名验证器加载完成');
                    resolve();
                };
                script.onerror = () => {
                    reject(new Error('域名验证器加载失败'));
                };
                document.head.appendChild(script);
            });
        }

        /**
         * 加载资源加载器
         */
        async function loadResourceLoader() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'js/core/resource-loader.js?v=' + Date.now();
                script.onload = () => {
                    console.log('✅ 资源加载器加载完成');
                    resolve();
                };
                script.onerror = () => {
                    reject(new Error('资源加载器加载失败'));
                };
                document.head.appendChild(script);
            });
        }

        /**
         * 启动应用
         */
        async function startApplication(tenantConfig) {
            // 等待应用初始化器加载完成（精简版）
            if (typeof window.AppInitializer !== 'undefined') {
                const app = new window.AppInitializer(tenantConfig);
                await app.initialize();
            } else {
                console.error('❌ 可用的全局对象:', Object.keys(window).filter(k => k.includes('App')));
                throw new Error('应用初始化器未找到');
            }
        }

        /**
         * 隐藏初始加载器
         */
        function hideInitialLoader() {
            const loader = document.getElementById('initialLoader');
            if (loader) {
                loader.style.opacity = '0';
                loader.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    loader.remove();
                }, 500);
            }
        }

        /**
         * 显示启动错误
         */
        function showStartupError(error) {
            const loader = document.getElementById('initialLoader');
            if (loader) {
                loader.innerHTML = `
                    <div style="text-align: center; max-width: 400px;">
                        <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
                        <h2 style="margin-bottom: 15px;">系统启动失败</h2>
                        <p style="margin-bottom: 20px; opacity: 0.9; line-height: 1.6;">
                            ${error.message || '未知错误'}
                        </p>
                        <button onclick="location.reload()" style="
                            background: white;
                            color: #667eea;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: 600;
                            cursor: pointer;
                        ">重新加载</button>
                    </div>
                `;
            }
        }

        /**
         * 更新加载状态
         */
        function updateLoadingStatus(message) {
            const statusEl = document.querySelector('.initial-loader div:last-child');
            if (statusEl) {
                statusEl.textContent = message;
            }
        }

        // 开始初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 DOM加载完成，开始系统初始化');
            initializePayPalSystem();
        });

        // 错误处理
        window.addEventListener('error', (event) => {
            console.error('🚨 全局错误:', event.error);
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('🚨 未处理的Promise拒绝:', event.reason);
        });

    </script>
</body>
</html> 