<?php
/**
 * 产品管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

try {
    // 检查用户权限：系统管理员、平台管理员、商户可以访问产品管理
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin', 'merchant'])) {
        $auth->sendForbidden('无权限访问产品管理');
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($action) {
        case 'products':
        case 'get_products':
            handleProducts($auth);
            break;
        case 'product_stats':
        case 'stats':
            handleProductStats($auth);
            break;
        case 'product_categories':
        case 'categories':
            handleProductCategories($auth);
            break;
        case 'product_rates':
        case 'rates':
            handleRates($auth);
            break;
        default:
            // 默认产品管理
            handleProducts($auth);
    }
} catch (Exception $e) {
    error_log("Products API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

function handleProducts($auth) {
    global $method;
    
    switch ($method) {
        case 'GET':
            handleGetProducts($auth);
            break;
        case 'POST':
            handleCreateProduct($auth);
            break;
        case 'PUT':
            handleUpdateProduct($auth);
            break;
        case 'DELETE':
            handleDeleteProduct($auth);
            break;
        default:
            $auth->sendError('不支持的请求方法', 405);
    }
}

// 获取产品列表
function handleGetProducts($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('product.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    // 数据隔离：根据用户类型限制访问
    if ($currentUser['user_type'] === 'merchant') {
        // 获取商户ID
        $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", [$currentUser['id']]);
        if (!$merchant) {
            $auth->sendError('商户信息不存在', 403);
            return;
        }
        $whereConditions[] = "p.merchant_id = ?";
        $params[] = $merchant['id'];
    } elseif (in_array($currentUser['user_type'], ['system_admin', 'platform_admin'])) {
        if ($merchantId > 0) {
            $whereConditions[] = "p.merchant_id = ?";
            $params[] = $merchantId;
        }
    } else {
        $auth->sendForbidden('无权限访问产品管理');
        return;
    }
    
    if (!empty($status) && in_array($status, array('active', 'inactive', 'suspended'))) {
        $whereConditions[] = "p.status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    try {
        // 获取总数
        $totalResult = $db->fetch(
            "SELECT COUNT(*) as count FROM products p $whereClause",
            $params
        );
        $total = $totalResult['count'];
        
        // 获取产品列表
        $products = $db->fetchAll(
            "SELECT p.*, 
                    m.merchant_code as merchant_name,
                    u.username as merchant_username
             FROM products p
             LEFT JOIN merchants m ON p.merchant_id = m.id
             LEFT JOIN users u ON m.user_id = u.id
             $whereClause
             ORDER BY p.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, array($limit, $offset))
        );
        
        // 获取统计信息
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active_products,
                COUNT(CASE WHEN p.status = 'inactive' THEN 1 END) as inactive_products,
                COUNT(CASE WHEN p.status = 'suspended' THEN 1 END) as suspended_products
             FROM products p $whereClause",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_products', 'product', 0, '查看产品列表');
        
        $response = array(
            'products' => $products,
            'stats' => $stats,
            'pagination' => array(
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            )
        );
        
        $auth->sendSuccess($response, '产品列表获取成功');
        
    } catch (Exception $e) {
        error_log("获取产品列表失败: " . $e->getMessage());
        $auth->sendError('获取产品列表失败: ' . $e->getMessage(), 500);
    }
}

// 创建产品
function handleCreateProduct($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('product.create')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证必填字段
    $requiredFields = array('name', 'min_amount', 'max_amount', 'fee_rate');
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            $auth->sendError("字段 {$field} 不能为空", 400);
            return;
        }
    }
    
    $name = trim($input['name']);
    $description = isset($input['description']) ? trim($input['description']) : '';
    $minAmount = floatval($input['min_amount']);
    $maxAmount = floatval($input['max_amount']);
    $feeRate = floatval($input['fee_rate']);
    $status = isset($input['status']) ? $input['status'] : 'active';
    
    // 获取商户ID
    $merchantId = 0;
    if ($currentUser['user_type'] === 'merchant') {
        $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", [$currentUser['id']]);
        if (!$merchant) {
            $auth->sendError('商户信息不存在', 403);
            return;
        }
        $merchantId = $merchant['id'];
    } elseif (in_array($currentUser['user_type'], ['system_admin', 'platform_admin']) && isset($input['merchant_id'])) {
        $merchantId = intval($input['merchant_id']);
    }
    
    if ($merchantId <= 0) {
        $auth->sendError('商户ID无效', 400);
        return;
    }
    
    try {
        // 创建产品
        $sql = "INSERT INTO products (name, description, min_amount, max_amount, fee_rate, merchant_id, status, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        $result = $db->execute($sql, array(
            $name, $description, $minAmount, $maxAmount, $feeRate, $merchantId, $status
        ));
        
        if ($result) {
            $productId = $db->lastInsertId();
            
            // 记录操作日志
            $auth->logUserAction('create_product', 'product', $productId, "创建产品: {$name}");
            
            $auth->sendSuccess(array('product_id' => $productId), '产品创建成功');
        } else {
            $auth->sendError('产品创建失败', 500);
        }
        
    } catch (Exception $e) {
        error_log("创建产品失败: " . $e->getMessage());
        $auth->sendError('产品创建失败: ' . $e->getMessage(), 500);
    }
}

// 产品统计
function handleProductStats($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('product.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $whereConditions = array();
    $params = array();
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", [$currentUser['id']]);
        if (!$merchant) {
            $auth->sendError('商户信息不存在', 403);
            return;
        }
        $whereConditions[] = "merchant_id = ?";
        $params[] = $merchant['id'];
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    try {
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_products,
                COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_products,
                COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_products,
                AVG(fee_rate) as avg_fee_rate,
                MIN(min_amount) as min_min_amount,
                MAX(max_amount) as max_max_amount
             FROM products $whereClause",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_product_stats', 'product', 0, '查看产品统计');
        
        $auth->sendSuccess($stats, '产品统计获取成功');
        
    } catch (Exception $e) {
        error_log("获取产品统计失败: " . $e->getMessage());
        $auth->sendError('获取产品统计失败: ' . $e->getMessage(), 500);
    }
}

// 其他处理函数（暂时简化）
function handleUpdateProduct($auth) {
    $auth->sendError('功能开发中', 501);
}

function handleDeleteProduct($auth) {
    $auth->sendError('功能开发中', 501);
}

function handleProductCategories($auth) {
    // 返回默认分类
    $categories = array(
        array('id' => 1, 'name' => '支付宝', 'status' => 'active'),
        array('id' => 2, 'name' => '微信支付', 'status' => 'active'),
        array('id' => 3, 'name' => '银行卡', 'status' => 'active')
    );
    
    $auth->sendSuccess($categories, '产品分类获取成功');
}

function handleRates($auth) {
    // 返回默认费率
    $rates = array(
        array('name' => '标准费率', 'rate' => '0.05', 'description' => '标准手续费率'),
        array('name' => '优惠费率', 'rate' => '0.03', 'description' => '优惠手续费率'),
        array('name' => 'VIP费率', 'rate' => '0.01', 'description' => 'VIP客户专享费率')
    );
    
    $auth->sendSuccess($rates, '费率信息获取成功');
}
?> 