/*!
 * 本地化图标 - 使用Unicode字符
 * 避免Bootstrap Icons CDN连接超时
 */

/* 基础图标类 */
.icon {
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

/* 常用图标 */
.icon-home::before { content: "🏠"; }
.icon-user::before { content: "👤"; }
.icon-users::before { content: "👥"; }
.icon-settings::before { content: "⚙️"; }
.icon-dashboard::before { content: "📊"; }
.icon-chart::before { content: "📈"; }
.icon-money::before { content: "💰"; }
.icon-card::before { content: "💳"; }
.icon-bank::before { content: "🏦"; }
.icon-order::before { content: "📋"; }
.icon-product::before { content: "📦"; }
.icon-merchant::before { content: "🏪"; }
.icon-provider::before { content: "🔧"; }
.icon-admin::before { content: "👑"; }
.icon-system::before { content: "🖥️"; }

/* 操作图标 */
.icon-add::before { content: "➕"; }
.icon-edit::before { content: "✏️"; }
.icon-delete::before { content: "🗑️"; }
.icon-view::before { content: "👁️"; }
.icon-download::before { content: "⬇️"; }
.icon-upload::before { content: "⬆️"; }
.icon-save::before { content: "💾"; }
.icon-print::before { content: "🖨️"; }
.icon-search::before { content: "🔍"; }
.icon-filter::before { content: "🔽"; }

/* 状态图标 */
.icon-success::before { content: "✅"; }
.icon-error::before { content: "❌"; }
.icon-warning::before { content: "⚠️"; }
.icon-info::before { content: "ℹ️"; }
.icon-loading::before { content: "⏳"; }
.icon-check::before { content: "✓"; }
.icon-close::before { content: "✕"; }

/* 导航图标 */
.icon-menu::before { content: "☰"; }
.icon-arrow-left::before { content: "←"; }
.icon-arrow-right::before { content: "→"; }
.icon-arrow-up::before { content: "↑"; }
.icon-arrow-down::before { content: "↓"; }

/* 通信图标 */
.icon-mail::before { content: "📧"; }
.icon-phone::before { content: "📞"; }
.icon-message::before { content: "💬"; }
.icon-notification::before { content: "🔔"; }

/* 安全图标 */
.icon-lock::before { content: "🔒"; }
.icon-unlock::before { content: "🔓"; }
.icon-key::before { content: "🔑"; }
.icon-shield::before { content: "🛡️"; }

/* 文件图标 */
.icon-file::before { content: "📄"; }
.icon-folder::before { content: "📁"; }
.icon-image::before { content: "🖼️"; }
.icon-pdf::before { content: "📕"; }

/* 时间图标 */
.icon-clock::before { content: "🕐"; }
.icon-calendar::before { content: "📅"; }
.icon-date::before { content: "📆"; }

/* 工具图标 */
.icon-tools::before { content: "🔧"; }
.icon-config::before { content: "⚙️"; }
.icon-api::before { content: "🔌"; }
.icon-code::before { content: "💻"; }

/* 数据图标 */
.icon-database::before { content: "🗄️"; }
.icon-server::before { content: "🖥️"; }
.icon-cloud::before { content: "☁️"; }
.icon-backup::before { content: "💾"; }

/* 支付相关图标 */
.icon-payment::before { content: "💳"; }
.icon-wallet::before { content: "👛"; }
.icon-coin::before { content: "🪙"; }
.icon-receipt::before { content: "🧾"; }
.icon-qrcode::before { content: "📱"; }
.icon-alipay::before { content: "💙"; }
.icon-wechat::before { content: "💚"; }

/* 尺寸变体 */
.icon-sm {
    font-size: 0.875rem;
}

.icon-lg {
    font-size: 1.25rem;
}

.icon-xl {
    font-size: 1.5rem;
}

.icon-2x {
    font-size: 2rem;
}

/* 颜色变体 */
.icon-primary { color: #0d6efd; }
.icon-secondary { color: #6c757d; }
.icon-success { color: #198754; }
.icon-danger { color: #dc3545; }
.icon-warning { color: #ffc107; }
.icon-info { color: #0dcaf0; }
.icon-light { color: #f8f9fa; }
.icon-dark { color: #212529; }

/* 动画效果 */
.icon-spin {
    animation: icon-spin 1s linear infinite;
}

@keyframes icon-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.icon-pulse {
    animation: icon-pulse 1s ease-in-out infinite alternate;
}

@keyframes icon-pulse {
    0% { opacity: 1; }
    100% { opacity: 0.5; }
} 