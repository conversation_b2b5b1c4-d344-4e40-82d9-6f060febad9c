<?php
/**
 * PayPal支付系统测试脚本
 * 
 * 测试用户应付金额功能的完整流程
 * 
 * <AUTHOR> System
 * @version 1.0
 */

require_once dirname(__FILE__) . '/config/database.php';
require_once dirname(__FILE__) . '/api/core/PayableAmountManager.php';
require_once dirname(__FILE__) . '/api/core/PaymentInstructionManager.php';

/**
 * 输出测试结果
 */
function testOutput($message, $success = true) {
    $status = $success ? '✅' : '❌';
    echo "[" . date('H:i:s') . "] {$status} {$message}" . PHP_EOL;
}

/**
 * 测试数据库连接
 */
function testDatabaseConnection() {
    testOutput("测试数据库连接...");
    
    try {
        $db = new Database();
        $pdo = $db->connect();
        
        // 测试查询
        $stmt = $pdo->query("SELECT 1");
        $result = $stmt->fetch();
        
        if ($result) {
            testOutput("数据库连接成功");
            return $pdo;
        } else {
            testOutput("数据库查询失败", false);
            return false;
        }
        
    } catch (Exception $e) {
        testOutput("数据库连接失败: " . $e->getMessage(), false);
        return false;
    }
}

/**
 * 测试表结构
 */
function testTableStructure($pdo) {
    testOutput("测试表结构...");
    
    $tables = [
        'payment_requests',
        'payment_instructions', 
        'payment_instruction_logs',
        'alipay_bills',
        'alipay_accounts',
        'merchants'
    ];
    
    $allTablesExist = true;
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
            $result = $stmt->fetch();
            
            if ($result) {
                testOutput("表 {$table} 存在");
            } else {
                testOutput("表 {$table} 不存在", false);
                $allTablesExist = false;
            }
            
        } catch (Exception $e) {
            testOutput("检查表 {$table} 失败: " . $e->getMessage(), false);
            $allTablesExist = false;
        }
    }
    
    return $allTablesExist;
}

/**
 * 测试用户应付金额管理器
 */
function testPayableAmountManager() {
    testOutput("测试用户应付金额管理器...");
    
    try {
        $manager = new PayableAmountManager();
        
        // 测试正常情况（无冲突）
        $result1 = $manager->generatePayableAmount(200.00, 1, 1);
        
        if ($result1['success'] && $result1['payable_amount'] == 200.00 && $result1['amount_diff'] == 0.00) {
            testOutput("无冲突情况测试通过");
        } else {
            testOutput("无冲突情况测试失败", false);
            return false;
        }
        
        // 测试金额验证
        $validation = $manager->validatePayableAmount(199.50, 200.00);
        
        if ($validation['valid'] && $validation['amount_diff'] == 0.50) {
            testOutput("金额验证测试通过");
        } else {
            testOutput("金额验证测试失败", false);
            return false;
        }
        
        testOutput("用户应付金额管理器测试通过");
        return true;
        
    } catch (Exception $e) {
        testOutput("用户应付金额管理器测试失败: " . $e->getMessage(), false);
        return false;
    }
}

/**
 * 测试收款指令管理器
 */
function testPaymentInstructionManager() {
    testOutput("测试收款指令管理器...");
    
    try {
        $manager = new PaymentInstructionManager();
        
        // 创建测试指令
        $result = $manager->createInstruction(
            999999, // 测试支付请求ID
            1,      // 支付宝账户ID
            1,      // 商户ID
            200.00, // 订单金额
            199.50, // 用户应付金额
            1       // 1分钟过期
        );
        
        if ($result['success']) {
            $instructionId = $result['instruction_id'];
            testOutput("指令创建成功: {$instructionId}");
            
            // 测试获取指令详情
            $details = $manager->getInstructionDetails($instructionId);
            
            if ($details['success'] && $details['instruction']['target_amount'] == 200.00) {
                testOutput("指令详情获取成功");
                
                // 测试更新指令状态
                $updateResult = $manager->updateInstructionStatus(
                    $instructionId,
                    PaymentInstructionManager::STATUS_COMPLETED,
                    PaymentInstructionManager::RESULT_FOUND,
                    ['found_amount' => 199.50]
                );
                
                if ($updateResult['success']) {
                    testOutput("指令状态更新成功");
                    testOutput("收款指令管理器测试通过");
                    return true;
                } else {
                    testOutput("指令状态更新失败", false);
                    return false;
                }
                
            } else {
                testOutput("指令详情获取失败", false);
                return false;
            }
            
        } else {
            testOutput("指令创建失败: " . $result['error'], false);
            return false;
        }
        
    } catch (Exception $e) {
        testOutput("收款指令管理器测试失败: " . $e->getMessage(), false);
        return false;
    }
}

/**
 * 测试API文件结构
 */
function testApiFileStructure() {
    testOutput("测试API文件结构...");
    
    $requiredFiles = [
        'api/PayableAmountManager.php',
        'api/PaymentInstructionManager.php',
        'api/public/pay_get_qrcode.php',
        'api/mobile/instruction_get.php',
        'api/device/bill_report.php',
        'config/database.php'
    ];
    
    $allFilesExist = true;
    
    foreach ($requiredFiles as $file) {
        $filePath = dirname(__FILE__) . '/' . $file;
        
        if (file_exists($filePath)) {
            testOutput("文件 {$file} 存在");
        } else {
            testOutput("文件 {$file} 不存在", false);
            $allFilesExist = false;
        }
    }
    
    return $allFilesExist;
}

/**
 * 测试完整的支付流程模拟
 */
function testCompletePaymentFlow($pdo) {
    testOutput("测试完整支付流程模拟...");
    
    try {
        // 1. 模拟创建订单
        testOutput("步骤1: 创建支付订单");
        
        $payableAmountManager = new PayableAmountManager();
        $instructionManager = new PaymentInstructionManager();
        
        // 生成用户应付金额
        $payableResult = $payableAmountManager->generatePayableAmount(200.00, 1, 1);
        
        if (!$payableResult['success']) {
            testOutput("生成用户应付金额失败", false);
            return false;
        }
        
        testOutput("用户应付金额: {$payableResult['payable_amount']}元，差额: {$payableResult['amount_diff']}元");
        
        // 2. 创建收款指令
        testOutput("步骤2: 创建收款指令");
        
        $instructionResult = $instructionManager->createInstruction(
            888888,
            1,
            1,
            200.00,
            $payableResult['payable_amount'],
            3
        );
        
        if (!$instructionResult['success']) {
            testOutput("创建收款指令失败", false);
            return false;
        }
        
        $instructionId = $instructionResult['instruction_id'];
        testOutput("收款指令创建成功: {$instructionId}");
        
        // 3. 模拟手机端获取指令
        testOutput("步骤3: 模拟手机端获取指令");
        
        $pendingResult = $instructionManager->getPendingInstructions(1, 'TEST_DEVICE', 5);
        
        if ($pendingResult['success'] && $pendingResult['count'] > 0) {
            testOutput("手机端成功获取 {$pendingResult['count']} 个待执行指令");
        } else {
            testOutput("手机端获取指令失败", false);
            return false;
        }
        
        // 4. 模拟指令执行完成
        testOutput("步骤4: 模拟指令执行完成");
        
        $completeResult = $instructionManager->updateInstructionStatus(
            $instructionId,
            PaymentInstructionManager::STATUS_COMPLETED,
            PaymentInstructionManager::RESULT_FOUND,
            [
                'found_amount' => $payableResult['payable_amount'],
                'bills_count' => 1,
                'device_response' => [
                    'device_id' => 'TEST_DEVICE',
                    'completed_at' => date('Y-m-d H:i:s')
                ]
            ]
        );
        
        if ($completeResult['success']) {
            testOutput("指令执行完成");
            testOutput("完整支付流程模拟成功");
            return true;
        } else {
            testOutput("指令执行完成失败", false);
            return false;
        }
        
    } catch (Exception $e) {
        testOutput("完整支付流程模拟失败: " . $e->getMessage(), false);
        return false;
    }
}

// 主测试程序
echo "🚀 PayPal支付系统 - 用户应付金额功能测试" . PHP_EOL;
echo "================================================" . PHP_EOL;

$testResults = [];

// 1. 测试数据库连接
$pdo = testDatabaseConnection();
$testResults['database'] = $pdo !== false;

if (!$pdo) {
    echo "❌ 数据库连接失败，无法继续测试" . PHP_EOL;
    exit(1);
}

// 2. 测试表结构
$testResults['tables'] = testTableStructure($pdo);

// 3. 测试API文件结构
$testResults['files'] = testApiFileStructure();

// 4. 测试用户应付金额管理器
$testResults['payable_manager'] = testPayableAmountManager();

// 5. 测试收款指令管理器
$testResults['instruction_manager'] = testPaymentInstructionManager();

// 6. 测试完整支付流程
$testResults['complete_flow'] = testCompletePaymentFlow($pdo);

// 测试结果汇总
echo PHP_EOL . "📊 测试结果汇总:" . PHP_EOL;
echo "================================================" . PHP_EOL;

$totalTests = count($testResults);
$passedTests = array_sum($testResults);

foreach ($testResults as $test => $result) {
    $status = $result ? '✅ 通过' : '❌ 失败';
    echo "- {$test}: {$status}" . PHP_EOL;
}

echo PHP_EOL . "总体结果: {$passedTests}/{$totalTests} 项测试通过" . PHP_EOL;

if ($passedTests === $totalTests) {
    echo "🎉 所有测试通过！用户应付金额功能正常工作！" . PHP_EOL;
    exit(0);
} else {
    echo "⚠️  有测试失败，请检查相关功能" . PHP_EOL;
    exit(1);
}
?> 