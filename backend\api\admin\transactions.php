<?php
require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

class TransactionManager {
    private $db;
    private $auth;
    
    public function __construct($database) {
        $this->db = $database;
        $this->auth = new Auth();
    }
    
    public function handleRequest() {
        // 验证token
        $token = $this->auth->getAuthToken();
        $user = $this->auth->getCurrentUser($token);
        if (!$user) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => '未授权访问']);
            return;
        }
        
        $method = $_SERVER['REQUEST_METHOD'];
        $action = isset($_GET['action']) ? $_GET['action'] : '';
        
        // 映射admin.php的action到内部action
        $mapped_action = $this->mapAction($action);
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($mapped_action, $user);
                    break;
                case 'POST':
                    $this->handlePost($mapped_action, $user);
                    break;
                case 'PUT':
                    $this->handlePut($mapped_action, $user);
                    break;
                case 'DELETE':
                    $this->handleDelete($mapped_action, $user);
                    break;
                default:
                    http_response_code(405);
                    echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }
    
    private function mapAction($action) {
        $action_map = [
            'get_transactions' => 'list',
            'get_transaction_stats' => 'stats',
            'get_transaction_detail' => 'detail',
            'update_transaction_status' => 'update_status',
            'update_transaction_remark' => 'remark',
            'export_transactions' => 'export',
            'transactions' => 'list', // 默认
        ];
        
        return isset($action_map[$action]) ? $action_map[$action] : $action;
    }
    
    private function handleGet($action, $user) {
        switch ($action) {
            case 'list':
                $this->getTransactionList($user);
                break;
            case 'stats':
                $this->getTransactionStats($user);
                break;
            case 'detail':
                $this->getTransactionDetail($user);
                break;
            case 'export':
                $this->exportTransactions($user);
                break;
            default:
                $this->getTransactionList($user);
        }
    }
    
    private function handlePost($action, $user) {
        switch ($action) {
            case 'update_status':
                $this->updateTransactionStatus($user);
                break;
            default:
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => '不支持的操作']);
        }
    }
    
    private function handlePut($action, $user) {
        switch ($action) {
            case 'remark':
                $this->updateTransactionRemark($user);
                break;
            default:
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => '不支持的操作']);
        }
    }
    
    private function handleDelete($action, $user) {
        // 交易记录一般不允许删除，只做状态更新
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '交易记录不支持删除操作']);
    }
    
    private function getTransactionList($user) {
        $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
        $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
        $offset = ($page - 1) * $limit;
        
        // 构建查询条件
        $where_conditions = [];
        $params = [];
        
        // 根据用户角色添加权限过滤
        $this->addUserPermissionFilter($where_conditions, $params, $user);
        
        // 添加搜索条件
        $this->addSearchFilters($where_conditions, $params);
        
        $where_clause = empty($where_conditions) ? '' : 'WHERE ' . implode(' AND ', $where_conditions);
        
        // 查询交易列表
        $sql = "
            SELECT 
                t.*,
                m.company_name as merchant_name,
                pp.company_name as provider_name,
                d.device_name,
                d.device_id as device_string_id,
                aa.account_name as alipay_account_name,
                aa.account_number as alipay_account_number
            FROM transactions t
            LEFT JOIN merchants m ON t.merchant_id = m.id  
            LEFT JOIN payment_providers pp ON t.provider_id = pp.id
            LEFT JOIN devices d ON t.device_id = d.id
            LEFT JOIN alipay_accounts aa ON t.alipay_account_id = aa.id
            {$where_clause}
            ORDER BY t.created_at DESC
            LIMIT {$offset}, {$limit}
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 查询总数
        $count_sql = "
            SELECT COUNT(*) as total
            FROM transactions t
            LEFT JOIN merchants m ON t.merchant_id = m.id  
            LEFT JOIN payment_providers pp ON t.provider_id = pp.id
            LEFT JOIN devices d ON t.device_id = d.id
            LEFT JOIN alipay_accounts aa ON t.alipay_account_id = aa.id
            {$where_clause}
        ";
        
        $count_stmt = $this->db->prepare($count_sql);
        $count_stmt->execute($params);
        $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 格式化交易数据
        $formatted_transactions = array_map([$this, 'formatTransactionData'], $transactions);
        
        echo json_encode([
            'code' => 200,
            'message' => '交易列表获取成功',
            'data' => [
                'transactions' => $formatted_transactions,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $limit,
                    'total' => $total,
                    'total_pages' => ceil($total / $limit)
                ]
            ]
        ]);
    }
    
    private function getTransactionStats($user) {
        $where_conditions = [];
        $params = [];
        
        // 根据用户角色添加权限过滤
        $this->addUserPermissionFilter($where_conditions, $params, $user);
        
        // 添加时间范围过滤
        $date_range = isset($_GET['date_range']) ? $_GET['date_range'] : 'today';
        $this->addDateRangeFilter($where_conditions, $params, $date_range);
        
        $where_clause = empty($where_conditions) ? '' : 'WHERE ' . implode(' AND ', $where_conditions);
        
        // 基础统计
        $stats_sql = "
            SELECT 
                COUNT(*) as total_transactions,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as success_transactions,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_transactions,
                COALESCE(SUM(CASE WHEN status = 'success' THEN amount END), 0) as total_amount,
                COALESCE(SUM(CASE WHEN status = 'success' THEN fee END), 0) as total_fee,
                COALESCE(SUM(CASE WHEN status = 'success' THEN actual_amount END), 0) as total_actual_amount,
                COALESCE(AVG(CASE WHEN status = 'success' THEN amount END), 0) as avg_amount
            FROM transactions t
            {$where_clause}
        ";
        
        $stmt = $this->db->prepare($stats_sql);
        $stmt->execute($params);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 计算成功率
        $stats['success_rate'] = $stats['total_transactions'] > 0 
            ? round(($stats['success_transactions'] / $stats['total_transactions']) * 100, 2)
            : 0;
        
        // 状态分布统计
        $status_stats = [
            'success' => intval($stats['success_transactions']),
            'pending' => intval($stats['pending_transactions']),
            'failed' => intval($stats['failed_transactions']),
            'cancelled' => intval($stats['cancelled_transactions'])
        ];
        
        // 时间趋势统计（最近7天）
        $trend_sql = "
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as count,
                COALESCE(SUM(CASE WHEN status = 'success' THEN amount END), 0) as amount
            FROM transactions t
            {$where_clause} AND created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date DESC
            LIMIT 7
        ";
        
        $trend_stmt = $this->db->prepare($trend_sql);
        $trend_stmt->execute($params);
        $trend_data = $trend_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'code' => 200,
            'message' => '交易统计获取成功',
            'data' => [
                'overview' => [
                    'total_transactions' => intval($stats['total_transactions']),
                    'success_transactions' => intval($stats['success_transactions']),
                    'pending_transactions' => intval($stats['pending_transactions']),
                    'failed_transactions' => intval($stats['failed_transactions']),
                    'cancelled_transactions' => intval($stats['cancelled_transactions']),
                    'success_rate' => floatval($stats['success_rate']),
                    'total_amount' => floatval($stats['total_amount']),
                    'total_fee' => floatval($stats['total_fee']),
                    'total_actual_amount' => floatval($stats['total_actual_amount']),
                    'avg_amount' => floatval($stats['avg_amount'])
                ],
                'status_distribution' => $status_stats,
                'trend_data' => $trend_data
            ]
        ]);
    }
    
    private function getTransactionDetail($user) {
        $transaction_id = isset($_GET['id']) ? $_GET['id'] : null;
        if (!$transaction_id) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '缺少交易ID']);
            return;
        }
        
        $where_conditions = ['t.id = ?'];
        $params = [$transaction_id];
        
        // 根据用户角色添加权限过滤
        $this->addUserPermissionFilter($where_conditions, $params, $user);
        
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        
        $sql = "
            SELECT 
                t.*,
                m.company_name as merchant_name,
                u1.email as merchant_email,
                pp.company_name as provider_name,
                pp.contact_person,
                d.device_name,
                d.device_id as device_string_id,
                d.device_brand as brand,
                aa.account_name as alipay_account_name,
                aa.account_number as alipay_account_number,
                aa.real_name as alipay_real_name
            FROM transactions t
            LEFT JOIN merchants m ON t.merchant_id = m.id  
            LEFT JOIN payment_providers pp ON t.provider_id = pp.id
            LEFT JOIN devices d ON t.device_id = d.id
            LEFT JOIN alipay_accounts aa ON t.alipay_account_id = aa.id
            {$where_clause}
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $transaction = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$transaction) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => '交易记录不存在']);
            return;
        }
        
        echo json_encode([
            'code' => 200,
            'message' => '交易详情获取成功',
            'data' => $this->formatTransactionData($transaction, true)
        ]);
    }
    
    private function updateTransactionStatus($user) {
        // 只有管理员和码商可以更新交易状态
        if (!in_array($user['user_type'], ['admin', 'provider'])) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => '没有权限执行此操作']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $transaction_id = isset($input['transaction_id']) ? $input['transaction_id'] : null;
        $new_status = isset($input['status']) ? $input['status'] : null;
        
        if (!$transaction_id || !$new_status) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '缺少必要参数']);
            return;
        }
        
        $valid_statuses = ['pending', 'success', 'failed', 'cancelled'];
        if (!in_array($new_status, $valid_statuses)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的状态值']);
            return;
        }
        
        // 检查权限
        if ($user['user_type'] === 'provider') {
            $check_sql = "SELECT id FROM transactions WHERE id = ? AND provider_id = ?";
            $check_stmt = $this->db->prepare($check_sql);
            $check_stmt->execute([$transaction_id, $user['profile_id']]);
            if (!$check_stmt->fetch()) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => '没有权限操作此交易']);
                return;
            }
        }
        
        $update_fields = ['status = ?'];
        $params = [$new_status];
        
        // 如果状态变为成功，更新支付时间
        if ($new_status === 'success') {
            $update_fields[] = 'paid_at = NOW()';
        }
        
        $params[] = $transaction_id;
        
        $sql = "UPDATE transactions SET " . implode(', ', $update_fields) . " WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        
        if ($stmt->execute($params)) {
            echo json_encode(['code' => 200, 'message' => '交易状态更新成功']);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => '更新失败']);
        }
    }
    
    private function updateTransactionRemark($user) {
        $input = json_decode(file_get_contents('php://input'), true);
        $transaction_id = isset($input['transaction_id']) ? $input['transaction_id'] : null;
        $remark = isset($input['remark']) ? $input['remark'] : '';
        
        if (!$transaction_id) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '缺少交易ID']);
            return;
        }
        
        $where_conditions = ['id = ?'];
        $params = [$remark, $transaction_id];
        
        // 根据用户角色添加权限过滤
        if ($user['user_type'] === 'provider') {
            $where_conditions[] = 'provider_id = ?';
            $params[] = $user['profile_id'];
        } elseif ($user['user_type'] === 'merchant') {
            $where_conditions[] = 'merchant_id = ?';
            $params[] = $user['profile_id'];
        }
        
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        
        $sql = "UPDATE transactions SET remark = ? {$where_clause}";
        $stmt = $this->db->prepare($sql);
        
        if ($stmt->execute($params)) {
            echo json_encode(['code' => 200, 'message' => '备注更新成功']);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => '更新失败']);
        }
    }
    
    private function addUserPermissionFilter(&$where_conditions, &$params, $user) {
        switch ($user['user_type']) {
            case 'provider':
                $where_conditions[] = 't.provider_id = ?';
                $params[] = $user['profile_id'];
                break;
            case 'merchant':
                $where_conditions[] = 't.merchant_id = ?';
                $params[] = $user['profile_id'];
                break;
            case 'admin':
                // 管理员可以查看所有数据
                break;
        }
    }
    
    private function addSearchFilters(&$where_conditions, &$params) {
        // 订单号搜索
        if (isset($_GET['order_id']) && !empty($_GET['order_id'])) {
            $where_conditions[] = 't.order_id LIKE ?';
            $params[] = '%' . $_GET['order_id'] . '%';
        }
        
        // 状态筛选
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $where_conditions[] = 't.status = ?';
            $params[] = $_GET['status'];
        }
        
        // 金额范围
        if (isset($_GET['min_amount']) && !empty($_GET['min_amount'])) {
            $where_conditions[] = 't.amount >= ?';
            $params[] = floatval($_GET['min_amount']);
        }
        
        if (isset($_GET['max_amount']) && !empty($_GET['max_amount'])) {
            $where_conditions[] = 't.amount <= ?';
            $params[] = floatval($_GET['max_amount']);
        }
        
        // 时间范围
        if (isset($_GET['start_date']) && !empty($_GET['start_date'])) {
            $where_conditions[] = 't.created_at >= ?';
            $params[] = $_GET['start_date'] . ' 00:00:00';
        }
        
        if (isset($_GET['end_date']) && !empty($_GET['end_date'])) {
            $where_conditions[] = 't.created_at <= ?';
            $params[] = $_GET['end_date'] . ' 23:59:59';
        }
        
        // 商户筛选（仅管理员可用）
        if (isset($_GET['merchant_id']) && !empty($_GET['merchant_id'])) {
            $where_conditions[] = 't.merchant_id = ?';
            $params[] = intval($_GET['merchant_id']);
        }
        
        // 码商筛选（仅管理员可用）
        if (isset($_GET['provider_id']) && !empty($_GET['provider_id'])) {
            $where_conditions[] = 't.provider_id = ?';
            $params[] = intval($_GET['provider_id']);
        }
    }
    
    private function addDateRangeFilter(&$where_conditions, &$params, $date_range) {
        switch ($date_range) {
            case 'today':
                $where_conditions[] = 'DATE(t.created_at) = CURDATE()';
                break;
            case 'yesterday':
                $where_conditions[] = 'DATE(t.created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)';
                break;
            case 'week':
                $where_conditions[] = 't.created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)';
                break;
            case 'month':
                $where_conditions[] = 't.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)';
                break;
        }
    }
    
    private function formatTransactionData($transaction, $detailed = false) {
        $formatted = [
            'id' => intval($transaction['id']),
            'order_id' => $transaction['order_id'],
            'merchant_id' => intval($transaction['merchant_id']),
            'merchant_name' => isset($transaction['merchant_name']) ? $transaction['merchant_name'] : '未知商户',
            'provider_id' => intval($transaction['provider_id']),
            'provider_name' => isset($transaction['provider_name']) ? $transaction['provider_name'] : '未知码商',
            'amount' => floatval($transaction['amount']),
            'rate' => floatval($transaction['rate']),
            'fee' => floatval($transaction['fee']),
            'actual_amount' => floatval($transaction['actual_amount']),
            'status' => $transaction['status'],
            'status_text' => $this->getStatusText($transaction['status']),
            'created_at' => $transaction['created_at'],
            'paid_at' => $transaction['paid_at'],
            'remark' => isset($transaction['remark']) ? $transaction['remark'] : ''
        ];
        
        // 设备信息
        if ($transaction['device_id']) {
            $formatted['device'] = [
                'id' => intval($transaction['device_id']),
                'name' => isset($transaction['device_name']) ? $transaction['device_name'] : '',
                'string_id' => isset($transaction['device_string_id']) ? $transaction['device_string_id'] : ''
            ];
        }
        
        // 支付账户信息
        if ($transaction['alipay_account_id']) {
            $formatted['alipay_account'] = [
                'id' => intval($transaction['alipay_account_id']),
                'name' => isset($transaction['alipay_account_name']) ? $transaction['alipay_account_name'] : '',
                'number' => $detailed ? $transaction['alipay_account_number'] : $this->maskAccountNumber($transaction['alipay_account_number'])
            ];
        }
        
        // 详细信息（仅在查看详情时返回）
        if ($detailed) {
            $formatted['notify_url'] = isset($transaction['notify_url']) ? $transaction['notify_url'] : '';
            $formatted['return_url'] = isset($transaction['return_url']) ? $transaction['return_url'] : '';
            
            // 商户详细信息
            if (isset($transaction['merchant_email'])) {
                $formatted['merchant_email'] = $transaction['merchant_email'];
            }
            
            // 码商详细信息
            if (isset($transaction['contact_person'])) {
                $formatted['provider_contact'] = $transaction['contact_person'];
            }
            
            // 支付账户详细信息
            if (isset($transaction['alipay_real_name'])) {
                $formatted['alipay_account']['real_name'] = $transaction['alipay_real_name'];
            }
        }
        
        return $formatted;
    }
    
    private function getStatusText($status) {
        $status_texts = [
            'pending' => '待处理',
            'success' => '成功',
            'failed' => '失败',
            'cancelled' => '已取消'
        ];
        
        return isset($status_texts[$status]) ? $status_texts[$status] : $status;
    }
    
    private function maskAccountNumber($account_number) {
        if (!$account_number || strlen($account_number) < 6) {
            return $account_number;
        }
        
        $length = strlen($account_number);
        $visible_start = 3;
        $visible_end = 3;
        
        return substr($account_number, 0, $visible_start) . 
               str_repeat('*', $length - $visible_start - $visible_end) . 
               substr($account_number, -$visible_end);
    }
}

// 实例化并处理请求
try {
    $database = new Database();
    $db = $database->connect();
    $manager = new TransactionManager($db);
    $manager->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器内部错误: ' . $e->getMessage()]);
} 