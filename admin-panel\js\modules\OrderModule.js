/**
 * 订单模块 - 统一订单管理
 * 整合transaction-manager.js、order相关功能
 * 实现界面共用+数据差异化的设计模式
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-01-20
 */

class OrderModule {
    constructor() {

        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        this.currentPage = 1;
        this.pageSize = 20;
        this.filters = {};
        this.currentRole = null;
        
        console.log('✅ OrderModule initialized');
    }

    /**
     * 统一渲染方法 - 方案5配置驱动的入口
     * @param {HTMLElement} container 容器元素
     * @param {Object} params 参数对象 {role: 'merchant'|'provider'|'platform_admin'|'system_admin'}
     */
    async render(container, params = {}) {
        try {
            // 确定用户角色
            this.currentRole = params.role || this.getCurrentUserRole();
            
            console.log(`🎯 订单模块渲染 - 角色: ${this.currentRole}`);
            
            // 显示加载状态
            container.innerHTML = UIComponents.generateLoadingState('正在加载订单数据...');
            
            // 获取角色配置
            const config = RoleDataAdapter.getOrderConfig(this.currentRole);
            
            // 生成统一界面
            container.innerHTML = this.generateHTML(config);
            
            // 初始化事件
            this.initializeEvents();
            
            // 根据角色加载不同数据
            await this.loadData();
            
        } catch (error) {
            console.error('❌ 订单模块渲染失败:', error);
            container.innerHTML = UIComponents.generateEmptyState({
                icon: 'bi-exclamation-triangle',
                title: '订单模块加载失败',
                description: error.message
            });
        }
    }

    /**
     * 获取当前用户角色
     */
    getCurrentUserRole() {
        if (this.tenantInfo?.tenant_type) {
            return this.tenantInfo.tenant_type;
        }
        
        const user = this.authManager?.getUser();
        return user?.user_type || 'merchant';
    }

    /**
     * 生成统一的HTML界面
     */
    generateHTML(config) {
        // 准备统计数据（模拟）
        const mockStats = this.getMockStatsData(this.currentRole);
        config.stats.forEach((stat, index) => {
            stat.value = mockStats[index] || '0';
        });
        
        // 准备筛选器配置
        const filters = this.getFiltersConfig(this.currentRole);
        
        const roleConfig = this.getRoleConfig(this.currentRole);
        
        return `
            <div class="order-module" data-role="${this.currentRole}">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="${roleConfig.icon} me-2"></i>${roleConfig.title}</h2>
                            <p class="text-muted mb-0">${roleConfig.description}</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="refreshOrdersBtn">
                                <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="exportOrdersBtn">
                                <i class="bi bi-download me-2"></i>导出订单
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片区域 -->
                ${UIComponents.generateStatsCards(config.stats, this.currentRole)}

                <!-- 筛选器区域 -->
                ${UIComponents.generateFilters(filters, this.currentRole)}

                <!-- 订单列表 -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">${roleConfig.listTitle}</h5>
                            <span class="badge bg-primary" id="orderCount">0</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr id="orderTableHeader">
                                        ${this.generateTableHeaders()}
                                    </tr>
                                </thead>
                                <tbody id="orderTableBody">
                                    <tr><td colspan="10" class="text-center py-4">加载中...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div id="orderPagination" class="d-flex justify-content-center">
                            <!-- 分页将动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模态框区域 -->
            <div id="orderModals">
                ${this.generateModals()}
            </div>

            <style>
                ${this.generateStyles()}
            </style>
        `;
    }

    /**
     * 根据角色获取配置
     */
    getRoleConfig(role) {
        const configs = {
            'system_admin': {
                title: '系统订单管理',
                description: '全系统订单数据监控与管理',
                icon: 'bi bi-list-check',
                color: 'primary',
                listTitle: '全平台订单列表'
            },
            'platform_admin': {
                title: '平台订单管理',
                description: '管理本平台的订单数据',
                icon: 'bi bi-receipt',
                color: 'success',
                listTitle: '平台订单列表'
            },
            'provider': {
                title: '设备订单管理',
                description: '设备收款订单管理',
                icon: 'bi bi-phone-vibrate',
                color: 'info',
                listTitle: '设备订单列表'
            },
            'merchant': {
                title: '商户订单管理',
                description: '商户交易订单管理',
                icon: 'bi bi-bag-check',
                color: 'warning',
                listTitle: '我的订单列表'
            }
        };
        
        return configs[role] || configs['merchant'];
    }

    /**
     * 生成统计卡片
     */
    generateStatsCards() {
        const statsConfig = this.getStatsConfig(this.currentRole);
        
        return statsConfig.map(stat => `
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-${stat.color}">
                        <i class="${stat.icon}"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="${stat.id}">-</div>
                        <div class="stat-label">${stat.label}</div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 根据角色获取统计配置
     */
    getStatsConfig(role) {
        const configs = {
            'system_admin': [
                { id: 'totalOrders', label: '总订单数', icon: 'bi bi-list-check', color: 'primary' },
                { id: 'todayOrders', label: '今日订单', icon: 'bi bi-calendar-day', color: 'success' },
                { id: 'successOrders', label: '成功订单', icon: 'bi bi-check-circle', color: 'info' },
                { id: 'pendingOrders', label: '待处理订单', icon: 'bi bi-hourglass-split', color: 'warning' }
            ],
            'platform_admin': [
                { id: 'platformOrders', label: '平台订单', icon: 'bi bi-receipt', color: 'primary' },
                { id: 'todayOrders', label: '今日订单', icon: 'bi bi-calendar-day', color: 'success' },
                { id: 'successRate', label: '成功率', icon: 'bi bi-percent', color: 'info' },
                { id: 'avgAmount', label: '平均金额', icon: 'bi bi-currency-dollar', color: 'warning' }
            ],
            'provider': [
                { id: 'deviceOrders', label: '设备订单', icon: 'bi bi-phone', color: 'primary' },
                { id: 'todayIncome', label: '今日收益', icon: 'bi bi-cash-stack', color: 'success' },
                { id: 'processingOrders', label: '处理中', icon: 'bi bi-arrow-repeat', color: 'info' },
                { id: 'completedOrders', label: '已完成', icon: 'bi bi-check-circle', color: 'warning' }
            ],
            'merchant': [
                { id: 'myOrders', label: '我的订单', icon: 'bi bi-bag-check', color: 'primary' },
                { id: 'todayOrders', label: '今日订单', icon: 'bi bi-calendar-day', color: 'success' },
                { id: 'paidOrders', label: '已支付', icon: 'bi bi-check-circle', color: 'info' },
                { id: 'failedOrders', label: '失败订单', icon: 'bi bi-x-circle', color: 'danger' }
            ]
        };
        
        return configs[role] || configs['merchant'];
    }

    /**
     * 获取筛选器配置
     */
    getFiltersConfig(role) {
        return [
            {
                type: 'daterange',
                id: 'dateRange',
                name: 'dateRange',
                label: '时间范围',
                width: 4
            },
            {
                type: 'select',
                id: 'orderStatus',
                name: 'status',
                label: '订单状态',
                width: 2,
                options: [
                    { value: 'pending', label: '待支付' },
                    { value: 'paid', label: '已支付' },
                    { value: 'completed', label: '已完成' },
                    { value: 'failed', label: '失败' },
                    { value: 'cancelled', label: '已取消' }
                ]
            },
            {
                type: 'input',
                id: 'orderSearch',
                name: 'search',
                label: '订单搜索',
                placeholder: '订单号/商户号',
                width: 3
            }
        ];
    }

    /**
     * 生成筛选器
     */
    generateFilters() {
        const baseFilters = `
            <div class="col-md-3">
                <label class="form-label">时间范围</label>
                <select class="form-select" id="dateRangeFilter">
                    <option value="today">今天</option>
                    <option value="yesterday">昨天</option>
                    <option value="week">本周</option>
                    <option value="month">本月</option>
                    <option value="custom">自定义</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">订单状态</label>
                <select class="form-select" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="pending">待支付</option>
                    <option value="processing">处理中</option>
                    <option value="paid">已支付</option>
                    <option value="failed">失败</option>
                    <option value="cancelled">已取消</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">搜索订单</label>
                <input type="text" class="form-control" id="searchInput" placeholder="订单号/商户名">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button class="btn btn-primary" id="applyFiltersBtn">
                        <i class="bi bi-funnel me-2"></i>应用筛选
                    </button>
                    <button class="btn btn-outline-secondary ms-2" id="resetFiltersBtn">
                        <i class="bi bi-arrow-clockwise me-2"></i>重置
                    </button>
                </div>
            </div>
        `;

        // 根据角色添加额外筛选器
        const roleSpecificFilters = this.getRoleSpecificFilters(this.currentRole);
        
        return baseFilters + roleSpecificFilters;
    }

    /**
     * 根据角色获取特定筛选器
     */
    getRoleSpecificFilters(role) {
        if (role === 'system_admin' || role === 'platform_admin') {
            return `
                <div class="col-md-3">
                    <label class="form-label">商户筛选</label>
                    <select class="form-select" id="merchantFilter">
                        <option value="">全部商户</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">码商筛选</label>
                    <select class="form-select" id="providerFilter">
                        <option value="">全部码商</option>
                    </select>
                </div>
            `;
        }
        
        return '';
    }

    /**
     * 生成表格头部
     */
    generateTableHeaders() {
        const headerConfigs = {
            'system_admin': [
                '订单号', '平台', '商户', '码商', '金额', '状态', '创建时间', '操作'
            ],
            'platform_admin': [
                '订单号', '商户', '码商', '金额', '手续费', '状态', '创建时间', '操作'
            ],
            'provider': [
                '订单号', '商户', '金额', '收益', '状态', '支付时间', '操作'
            ],
            'merchant': [
                '订单号', '金额', '手续费', '实际到账', '状态', '创建时间', '操作'
            ]
        };
        
        const headers = headerConfigs[this.currentRole] || headerConfigs['merchant'];
        
        return headers.map(header => `<th>${header}</th>`).join('');
    }

    /**
     * 生成模态框
     */
    generateModals() {
        return `
            <!-- 订单详情模态框 -->
            <div class="modal fade" id="orderDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">订单详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="orderDetailBody">
                            <!-- 详情内容将动态生成 -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 状态更新模态框 -->
            <div class="modal fade" id="updateStatusModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">更新订单状态</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">新状态</label>
                                <select class="form-select" id="newStatus">
                                    <option value="pending">待支付</option>
                                    <option value="processing">处理中</option>
                                    <option value="paid">已支付</option>
                                    <option value="failed">失败</option>
                                    <option value="cancelled">已取消</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">备注</label>
                                <textarea class="form-control" id="statusRemark" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmUpdateBtn">确认更新</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成样式
     */
    generateStyles() {
        return `
            .order-module {
                padding: 0;
            }
            
            .stat-card {
                background: white;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                display: flex;
                align-items: center;
                gap: 15px;
                transition: transform 0.2s;
            }
            
            .stat-card:hover {
                transform: translateY(-2px);
            }
            
            .stat-icon {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 20px;
            }
            
            .stat-content {
                flex: 1;
            }
            
            .stat-number {
                font-size: 24px;
                font-weight: bold;
                color: #333;
            }
            
            .stat-label {
                color: #666;
                font-size: 14px;
            }
            
            .table th {
                background-color: #f8f9fa;
                border-bottom: 2px solid #dee2e6;
                font-weight: 600;
            }
            
            .status-badge {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
            }
            
            .status-pending { background-color: #fff3cd; color: #856404; }
            .status-processing { background-color: #cce5ff; color: #004085; }
            .status-paid { background-color: #d4edda; color: #155724; }
            .status-failed { background-color: #f8d7da; color: #721c24; }
            .status-cancelled { background-color: #e2e3e5; color: #383d41; }
            
            .order-amount {
                font-weight: 600;
                color: #28a745;
            }
            
            .order-fee {
                font-size: 0.9em;
                color: #ffc107;
            }
        `;
    }

    /**
     * 初始化事件监听
     */
    initializeEvents() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshOrdersBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }

        // 导出按钮
        const exportBtn = document.getElementById('exportOrdersBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportOrders());
        }

        // 应用筛选按钮
        const applyFiltersBtn = document.getElementById('applyFiltersBtn');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => this.applyFilters());
        }

        // 重置筛选按钮
        const resetFiltersBtn = document.getElementById('resetFiltersBtn');
        if (resetFiltersBtn) {
            resetFiltersBtn.addEventListener('click', () => this.resetFilters());
        }

        // 搜索框回车事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyFilters();
                }
            });
        }

        // 时间范围变化
        const dateRangeFilter = document.getElementById('dateRangeFilter');
        if (dateRangeFilter) {
            dateRangeFilter.addEventListener('change', (e) => {
                this.toggleCustomDateRange(e.target.value === 'custom');
            });
        }

        // 确认更新状态按钮
        const confirmUpdateBtn = document.getElementById('confirmUpdateBtn');
        if (confirmUpdateBtn) {
            confirmUpdateBtn.addEventListener('click', () => this.confirmUpdateStatus());
        }
    }

    /**
     * 根据角色加载数据
     */
    async loadData() {
        try {
            console.log(`📊 加载${this.currentRole}的订单数据...`);
            
            // 加载统计数据
            await this.loadStatsData();
            
            // 加载订单列表
            await this.loadOrderList();
            
            // 加载筛选选项
            await this.loadFilterOptions();
            
        } catch (error) {
            console.error('❌ 加载订单数据失败:', error);
        }
    }

    /**
     * 加载统计数据
     */
    async loadStatsData() {
        // 模拟统计数据
        const mockStats = this.getMockStatsData(this.currentRole);
        this.updateStatsCards(mockStats);
    }

    /**
     * 获取模拟统计数据
     */
    getMockStatsData(role) {
        const mockData = {
            'system_admin': {
                'totalOrders': '12,345',
                'todayOrders': '234',
                'successOrders': '11,567',
                'pendingOrders': '123'
            },
            'platform_admin': {
                'platformOrders': '2,345',
                'todayOrders': '45',
                'successRate': '94.5%',
                'avgAmount': '¥156.78'
            },
            'provider': {
                'deviceOrders': '567',
                'todayIncome': '¥1,234.56',
                'processingOrders': '12',
                'completedOrders': '555'
            },
            'merchant': {
                'myOrders': '123',
                'todayOrders': '5',
                'paidOrders': '118',
                'failedOrders': '2'
            }
        };
        
        return mockData[role] || mockData['merchant'];
    }

    /**
     * 更新统计卡片
     */
    updateStatsCards(data) {
        Object.keys(data).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = data[key];
            }
        });
    }

    /**
     * 加载订单列表
     */
    async loadOrderList() {
        const tbody = document.getElementById('orderTableBody');
        tbody.innerHTML = '<tr><td colspan="10" class="text-center py-4">加载中...</td></tr>';
        
        // 模拟订单数据
        const mockOrders = this.getMockOrderData(this.currentRole);
        this.renderOrderList(mockOrders);
    }

    /**
     * 获取模拟订单数据
     */
    getMockOrderData(role) {
        const baseOrders = [
            {
                id: 1,
                order_id: 'ORD202401200001',
                merchant_name: '测试商户1',
                provider_name: '码商A',
                amount: 100.00,
                fee: 2.00,
                actual_amount: 98.00,
                status: 'paid',
                created_at: '2024-01-20 10:30:00',
                paid_at: '2024-01-20 10:31:15'
            },
            {
                id: 2,
                order_id: 'ORD202401200002',
                merchant_name: '测试商户2',
                provider_name: '码商B',
                amount: 256.78,
                fee: 5.14,
                actual_amount: 251.64,
                status: 'processing',
                created_at: '2024-01-20 11:15:00',
                paid_at: null
            },
            {
                id: 3,
                order_id: 'ORD202401200003',
                merchant_name: '测试商户1',
                provider_name: '码商A',
                amount: 89.50,
                fee: 1.79,
                actual_amount: 87.71,
                status: 'pending',
                created_at: '2024-01-20 12:00:00',
                paid_at: null
            }
        ];
        
        return baseOrders;
    }

    /**
     * 渲染订单列表
     */
    renderOrderList(orders) {
        const tbody = document.getElementById('orderTableBody');
        const orderCount = document.getElementById('orderCount');
        
        if (orders.length === 0) {
            tbody.innerHTML = '<tr><td colspan="10" class="text-center py-4 text-muted">暂无订单数据</td></tr>';
            orderCount.textContent = '0';
            return;
        }
        
        tbody.innerHTML = orders.map(order => this.renderOrderRow(order)).join('');
        orderCount.textContent = orders.length.toLocaleString();
    }

    /**
     * 渲染订单行
     */
    renderOrderRow(order) {
        const canManage = ['system_admin', 'platform_admin', 'provider'].includes(this.currentRole);
        
        const baseColumns = `
            <td><code>${order.order_id}</code></td>
        `;
        
        const roleSpecificColumns = this.getRoleSpecificColumns(order, this.currentRole);
        
        const actionColumn = `
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="orderModule.viewOrderDetail(${order.id})">
                    <i class="bi bi-eye"></i>
                </button>
                ${canManage ? `
                    <button class="btn btn-sm btn-outline-warning" onclick="orderModule.updateOrderStatus(${order.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                ` : ''}
            </td>
        `;
        
        return `<tr>${baseColumns}${roleSpecificColumns}${actionColumn}</tr>`;
    }

    /**
     * 根据角色获取特定列
     */
    getRoleSpecificColumns(order, role) {
        const statusBadge = this.getStatusBadge(order.status);
        const formatAmount = (amount) => `¥${parseFloat(amount).toLocaleString()}`;
        
        switch (role) {
            case 'system_admin':
                return `
                    <td>平台A</td>
                    <td>${order.merchant_name}</td>
                    <td>${order.provider_name}</td>
                    <td class="order-amount">${formatAmount(order.amount)}</td>
                    <td>${statusBadge}</td>
                    <td>${order.created_at}</td>
                `;
            case 'platform_admin':
                return `
                    <td>${order.merchant_name}</td>
                    <td>${order.provider_name}</td>
                    <td class="order-amount">${formatAmount(order.amount)}</td>
                    <td class="order-fee">${formatAmount(order.fee)}</td>
                    <td>${statusBadge}</td>
                    <td>${order.created_at}</td>
                `;
            case 'provider':
                return `
                    <td>${order.merchant_name}</td>
                    <td class="order-amount">${formatAmount(order.amount)}</td>
                    <td class="order-fee">${formatAmount(order.fee)}</td>
                    <td>${statusBadge}</td>
                    <td>${order.paid_at || '-'}</td>
                `;
            case 'merchant':
            default:
                return `
                    <td class="order-amount">${formatAmount(order.amount)}</td>
                    <td class="order-fee">${formatAmount(order.fee)}</td>
                    <td class="order-amount">${formatAmount(order.actual_amount)}</td>
                    <td>${statusBadge}</td>
                    <td>${order.created_at}</td>
                `;
        }
    }

    /**
     * 获取状态徽章
     */
    getStatusBadge(status) {
        const statusMap = {
            'pending': { text: '待支付', class: 'status-pending' },
            'processing': { text: '处理中', class: 'status-processing' },
            'paid': { text: '已支付', class: 'status-paid' },
            'failed': { text: '失败', class: 'status-failed' },
            'cancelled': { text: '已取消', class: 'status-cancelled' }
        };
        
        const statusInfo = statusMap[status] || { text: status, class: 'status-pending' };
        return `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
    }

    /**
     * 加载筛选选项
     */
    async loadFilterOptions() {
        // 模拟加载商户和码商选项
        console.log('📋 加载筛选选项...');
    }

    /**
     * 切换自定义日期范围
     */
    toggleCustomDateRange(show) {
        // 实现自定义日期范围显示/隐藏逻辑
        console.log('📅 切换自定义日期范围:', show);
    }

    /**
     * 应用筛选
     */
    applyFilters() {
        // 收集筛选条件
        this.filters = {
            dateRange: document.getElementById('dateRangeFilter')?.value,
            status: document.getElementById('statusFilter')?.value,
            search: document.getElementById('searchInput')?.value,
            merchant: document.getElementById('merchantFilter')?.value,
            provider: document.getElementById('providerFilter')?.value
        };
        
        console.log('🔍 应用筛选条件:', this.filters);
        
        // 重新加载数据
        this.loadOrderList();
    }

    /**
     * 重置筛选
     */
    resetFilters() {
        // 重置所有筛选器
        document.getElementById('dateRangeFilter').value = 'today';
        document.getElementById('statusFilter').value = '';
        document.getElementById('searchInput').value = '';
        
        const merchantFilter = document.getElementById('merchantFilter');
        const providerFilter = document.getElementById('providerFilter');
        if (merchantFilter) merchantFilter.value = '';
        if (providerFilter) providerFilter.value = '';
        
        this.filters = {};
        this.loadOrderList();
        
        console.log('🔄 重置筛选条件');
    }

    /**
     * 刷新数据
     */
    async refreshData() {
        console.log('🔄 刷新订单数据');
        await this.loadData();
    }

    /**
     * 导出订单
     */
    exportOrders() {
        console.log('📤 导出订单数据');
    }

    /**
     * 查看订单详情
     */
    viewOrderDetail(orderId) {
        console.log('👁️ 查看订单详情:', orderId);
        
        // 显示订单详情模态框
        const modal = new bootstrap.Modal(document.getElementById('orderDetailModal'));
        modal.show();
    }

    /**
     * 更新订单状态
     */
    updateOrderStatus(orderId) {
        console.log('✏️ 更新订单状态:', orderId);
        
        // 显示状态更新模态框
        const modal = new bootstrap.Modal(document.getElementById('updateStatusModal'));
        modal.show();
    }

    /**
     * 确认更新状态
     */
    confirmUpdateStatus() {
        const newStatus = document.getElementById('newStatus').value;
        const remark = document.getElementById('statusRemark').value;
        
        console.log('✅ 确认更新状态:', { newStatus, remark });
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('updateStatusModal'));
        modal.hide();
    }

    /**
     * 显示错误信息
     */
    showError(container, message) {
        container.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
}

// 全局注册
window.OrderModule = OrderModule;

// 创建全局实例
window.orderModule = new OrderModule();

console.log('✅ OrderModule 已注册到全局');
