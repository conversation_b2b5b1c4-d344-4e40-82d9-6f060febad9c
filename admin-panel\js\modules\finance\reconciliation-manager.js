/**
 * 对账管理器 - 四层架构财务系统核心模块
 * 负责财务数据的自动对账、差异检测和处理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class ReconciliationManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        
        this.currentPage = 1;
        this.pageSize = 20;
        this.reconciliations = [];
        this.differences = [];
        this.autoReconcileTimer = null;
        
        console.log('🔍 ReconciliationManager initialized for tenant:', this.tenantInfo?.tenant_type);
    }

    /**
     * 初始化对账管理器
     */
    async init(container) {
        try {
            this.container = container;
            await this.renderReconciliationPage();
            await this.loadReconciliations();
            await this.loadReconciliationStats();
            this.bindEvents();
            this.startAutoReconcile();
            
            console.log('✅ 对账管理器初始化完成');
        } catch (error) {
            console.error('❌ 对账管理器初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    /**
     * 渲染对账管理页面
     */
    async renderReconciliationPage() {
        this.container.innerHTML = `
            <div class="reconciliation-manager">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="bi bi-check2-all me-2"></i>财务对账</h2>
                            <p class="text-muted mb-0">自动对账、差异检测和处理管理</p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="reconciliationManager.startManualReconcile()">
                                <i class="bi bi-play me-2"></i>手动对账
                            </button>
                            <button class="btn btn-success" onclick="reconciliationManager.showBatchReconcileModal()">
                                <i class="bi bi-collection me-2"></i>批量对账
                            </button>
                            <button class="btn btn-outline-info" onclick="reconciliationManager.showSettingsModal()">
                                <i class="bi bi-gear me-2"></i>对账设置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 对账概览统计 -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="matchedCount">0</div>
                                <div class="stat-label">已匹配</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="differenceCount">0</div>
                                <div class="stat-label">有差异</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-danger">
                                <i class="bi bi-x-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="unmatchedCount">0</div>
                                <div class="stat-label">未匹配</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-clock-history"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="pendingCount">0</div>
                                <div class="stat-label">待处理</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-percent"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="matchRate">0%</div>
                                <div class="stat-label">匹配率</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-secondary">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="differenceAmount">¥0</div>
                                <div class="stat-label">差异金额</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 自动对账状态 -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-robot me-2"></i>自动对账状态
                                    <span class="badge bg-success ms-2" id="autoReconcileStatus">运行中</span>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-primary" id="lastReconcileTime">--</div>
                                            <div class="metric-label">上次对账</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-success" id="nextReconcileTime">--</div>
                                            <div class="metric-label">下次对账</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-info" id="avgReconcileTime">0s</div>
                                            <div class="metric-label">平均耗时</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-warning" id="todayReconcileCount">0</div>
                                            <div class="metric-label">今日对账</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-activity me-2"></i>对账进度
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>当前进度</span>
                                        <span id="reconcileProgress">0%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" id="reconcileProgressBar" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <button class="btn btn-outline-danger btn-sm" onclick="reconciliationManager.stopAutoReconcile()">
                                        <i class="bi bi-stop me-2"></i>停止自动对账
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 对账结果tabs -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#reconcileResults" role="tab">
                                    <i class="bi bi-list-check me-2"></i>对账结果
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#differences" role="tab">
                                    <i class="bi bi-exclamation-triangle me-2"></i>差异处理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#rules" role="tab">
                                    <i class="bi bi-gear me-2"></i>对账规则
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#history" role="tab">
                                    <i class="bi bi-clock-history me-2"></i>对账历史
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <!-- 对账结果 -->
                            <div class="tab-pane fade show active" id="reconcileResults" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="d-flex gap-3">
                                        <select class="form-select form-select-sm" id="filterStatus">
                                            <option value="">全部状态</option>
                                            <option value="matched">已匹配</option>
                                            <option value="unmatched">未匹配</option>
                                            <option value="difference">有差异</option>
                                        </select>
                                        <select class="form-select form-select-sm" id="filterSource">
                                            <option value="">全部来源</option>
                                            <option value="internal">内部系统</option>
                                            <option value="alipay">支付宝</option>
                                            <option value="wechat">微信</option>
                                            <option value="bank">银行</option>
                                        </select>
                                        <input type="date" class="form-control form-control-sm" id="filterDate">
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="reconciliationManager.applyFilters()">
                                            <i class="bi bi-search"></i> 搜索
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="reconciliationManager.resetFilters()">
                                            <i class="bi bi-arrow-clockwise"></i> 重置
                                        </button>
                                        <button class="btn btn-outline-success" onclick="reconciliationManager.exportResults()">
                                            <i class="bi bi-download"></i> 导出
                                        </button>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <input type="checkbox" id="selectAllResults" onchange="reconciliationManager.toggleSelectAll('results')">
                                                </th>
                                                <th>对账日期</th>
                                                <th>订单号</th>
                                                <th>内部金额</th>
                                                <th>外部金额</th>
                                                <th>差异金额</th>
                                                <th>数据源</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="reconcileResultsTableBody">
                                            <tr>
                                                <td colspan="9" class="text-center py-4">
                                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                    加载中...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分页 -->
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div class="text-muted">
                                        显示 <span id="resultsPageInfo">0-0</span> 条，共 <span id="resultsTotalItems">0</span> 条
                                    </div>
                                    <nav>
                                        <ul class="pagination pagination-sm mb-0" id="resultsPagination">
                                        </ul>
                                    </nav>
                                </div>
                            </div>

                            <!-- 差异处理 -->
                            <div class="tab-pane fade" id="differences" role="tabpanel">
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    以下是系统检测到的对账差异，请及时处理
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>差异ID</th>
                                                <th>订单号</th>
                                                <th>差异类型</th>
                                                <th>差异金额</th>
                                                <th>发现时间</th>
                                                <th>处理状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="differencesTableBody">
                                            <tr>
                                                <td colspan="7" class="text-center py-4">
                                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                    加载中...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 对账规则 -->
                            <div class="tab-pane fade" id="rules" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">自动对账规则</h6>
                                            </div>
                                            <div class="card-body">
                                                <form id="autoReconcileRulesForm">
                                                    <div class="mb-3">
                                                        <label class="form-label">对账周期</label>
                                                        <select class="form-select" id="reconcileCycle">
                                                            <option value="hourly">每小时</option>
                                                            <option value="daily">每日</option>
                                                            <option value="weekly">每周</option>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">对账时间</label>
                                                        <input type="time" class="form-control" id="reconcileTime" value="02:00">
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">差异阈值 (元)</label>
                                                        <input type="number" class="form-control" id="differenceThreshold" value="0.01" step="0.01">
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="autoProcessMinorDifferences" checked>
                                                            <label class="form-check-label" for="autoProcessMinorDifferences">
                                                                自动处理小额差异
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <button type="button" class="btn btn-primary" onclick="reconciliationManager.saveRules()">
                                                        <i class="bi bi-check me-2"></i>保存规则
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">匹配规则</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <label class="form-label">主要匹配字段</label>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="matchByOrderNo" checked>
                                                        <label class="form-check-label" for="matchByOrderNo">订单号</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="matchByAmount" checked>
                                                        <label class="form-check-label" for="matchByAmount">交易金额</label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="matchByTime">
                                                        <label class="form-check-label" for="matchByTime">交易时间</label>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">时间容差 (分钟)</label>
                                                    <input type="number" class="form-control" id="timeTolerance" value="5">
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">金额容差 (元)</label>
                                                    <input type="number" class="form-control" id="amountTolerance" value="0.01" step="0.01">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 对账历史 -->
                            <div class="tab-pane fade" id="history" role="tabpanel">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>对账时间</th>
                                                <th>对账类型</th>
                                                <th>数据范围</th>
                                                <th>总记录数</th>
                                                <th>匹配数</th>
                                                <th>差异数</th>
                                                <th>耗时</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="historyTableBody">
                                            <tr>
                                                <td colspan="9" class="text-center py-4">
                                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                    加载中...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 差异处理模态框 -->
            <div class="modal fade" id="processDifferenceModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>处理对账差异
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="differenceDetails">
                                <!-- 差异详情将动态加载 -->
                            </div>
                            <form id="processDifferenceForm">
                                <input type="hidden" id="differenceId">
                                <div class="mb-3">
                                    <label class="form-label">处理方式</label>
                                    <select class="form-select" id="processAction" required>
                                        <option value="">请选择处理方式</option>
                                        <option value="accept_internal">以内部数据为准</option>
                                        <option value="accept_external">以外部数据为准</option>
                                        <option value="manual_adjust">手动调整</option>
                                        <option value="ignore">忽略此差异</option>
                                    </select>
                                </div>
                                <div class="mb-3" id="adjustAmountGroup" style="display: none;">
                                    <label class="form-label">调整金额</label>
                                    <input type="number" class="form-control" id="adjustAmount" step="0.01">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">处理说明</label>
                                    <textarea class="form-control" id="processNote" rows="3" placeholder="请说明处理原因..."></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="reconciliationManager.processDifference()">
                                <i class="bi bi-check me-2"></i>确认处理
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 处理方式变化事件
        document.getElementById('processAction').addEventListener('change', (e) => {
            const adjustGroup = document.getElementById('adjustAmountGroup');
            adjustGroup.style.display = e.target.value === 'manual_adjust' ? 'block' : 'none';
        });

        // Tab切换事件
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const target = e.target.getAttribute('href').substring(1);
                this.loadTabData(target);
            });
        });
    }

    /**
     * 加载对账记录
     */
    async loadReconciliations() {
        try {
            const params = {
                page: this.currentPage,
                pageSize: this.pageSize,
                status: document.getElementById('filterStatus')?.value || '',
                source: document.getElementById('filterSource')?.value || '',
                date: document.getElementById('filterDate')?.value || ''
            };

            const response = await this.apiClient.get('/admin/finance/reconciliations', { params });

            if (response.success) {
                this.reconciliations = response.data.reconciliations;
                this.renderReconciliationTable();
                this.renderPagination(response.data.pagination);
            } else {
                throw new Error(response.message || '加载对账记录失败');
            }

        } catch (error) {
            console.error('加载对账记录失败:', error);
            this.showError('加载记录失败: ' + error.message);
        }
    }

    /**
     * 加载对账统计
     */
    async loadReconciliationStats() {
        try {
            const response = await this.apiClient.get('/admin/finance/reconciliation-stats');

            if (response.success) {
                const stats = response.data;
                this.updateStatsDisplay(stats);
                this.updateAutoReconcileStatus(stats.auto_reconcile);
            }

        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 渲染对账记录表格
     */
    renderReconciliationTable() {
        const tbody = document.getElementById('reconcileResultsTableBody');
        
        if (this.reconciliations.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="bi bi-inbox text-muted" style="font-size: 2rem;"></i>
                        <div class="mt-2 text-muted">暂无对账记录</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.reconciliations.map(record => `
            <tr>
                <td>
                    <input type="checkbox" class="reconcile-checkbox" value="${record.id}">
                </td>
                <td>${this.formatDate(record.reconcile_date)}</td>
                <td>
                    <div class="fw-medium">${record.order_no}</div>
                    <small class="text-muted">#${record.id}</small>
                </td>
                <td class="text-primary">¥${this.formatAmount(record.internal_amount)}</td>
                <td class="text-info">¥${this.formatAmount(record.external_amount)}</td>
                <td class="${record.difference_amount != 0 ? 'text-danger fw-medium' : 'text-muted'}">
                    ¥${this.formatAmount(record.difference_amount)}
                </td>
                <td>
                    <span class="badge bg-${this.getSourceBadgeColor(record.data_source)}">
                        ${this.getSourceText(record.data_source)}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${this.getStatusBadgeColor(record.status)}">
                        ${this.getStatusText(record.status)}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="reconciliationManager.viewReconcileDetail(${record.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${record.status === 'difference' ? `
                            <button class="btn btn-outline-warning" onclick="reconciliationManager.showProcessDifferenceModal(${record.id})" title="处理差异">
                                <i class="bi bi-tools"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-outline-success" onclick="reconciliationManager.reReconcile(${record.id})" title="重新对账">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 开始手动对账
     */
    async startManualReconcile() {
        try {
            const response = await this.apiClient.post('/admin/finance/reconcile/manual');

            if (response.success) {
                this.showSuccess('手动对账已启动');
                this.updateReconcileProgress(0);
                this.monitorReconcileProgress(response.data.task_id);
            } else {
                throw new Error(response.message || '启动对账失败');
            }

        } catch (error) {
            console.error('启动手动对账失败:', error);
            this.showError('启动对账失败: ' + error.message);
        }
    }

    /**
     * 监控对账进度
     */
    async monitorReconcileProgress(taskId) {
        const checkProgress = async () => {
            try {
                const response = await this.apiClient.get(`/admin/finance/reconcile/progress/${taskId}`);
                
                if (response.success) {
                    const progress = response.data;
                    this.updateReconcileProgress(progress.percentage);
                    
                    if (progress.status === 'completed') {
                        this.showSuccess('对账完成');
                        await this.loadReconciliations();
                        await this.loadReconciliationStats();
                    } else if (progress.status === 'failed') {
                        this.showError('对账失败: ' + progress.error);
                    } else {
                        // 继续监控
                        setTimeout(checkProgress, 2000);
                    }
                }
            } catch (error) {
                console.error('监控对账进度失败:', error);
            }
        };

        checkProgress();
    }

    /**
     * 更新对账进度
     */
    updateReconcileProgress(percentage) {
        document.getElementById('reconcileProgress').textContent = percentage + '%';
        document.getElementById('reconcileProgressBar').style.width = percentage + '%';
    }

    /**
     * 显示处理差异模态框
     */
    async showProcessDifferenceModal(recordId) {
        try {
            const response = await this.apiClient.get(`/admin/finance/reconciliations/${recordId}`);
            
            if (response.success) {
                const record = response.data;
                this.renderDifferenceDetails(record);
                document.getElementById('differenceId').value = recordId;
                
                const modal = new bootstrap.Modal(document.getElementById('processDifferenceModal'));
                modal.show();
            }
        } catch (error) {
            console.error('加载差异详情失败:', error);
            this.showError('加载详情失败: ' + error.message);
        }
    }

    /**
     * 渲染差异详情
     */
    renderDifferenceDetails(record) {
        document.getElementById('differenceDetails').innerHTML = `
            <div class="alert alert-warning">
                <h6>差异详情</h6>
                <div class="row">
                    <div class="col-md-6">
                        <strong>订单号:</strong> ${record.order_no}<br>
                        <strong>内部金额:</strong> ¥${this.formatAmount(record.internal_amount)}<br>
                        <strong>外部金额:</strong> ¥${this.formatAmount(record.external_amount)}
                    </div>
                    <div class="col-md-6">
                        <strong>差异金额:</strong> <span class="text-danger">¥${this.formatAmount(record.difference_amount)}</span><br>
                        <strong>数据源:</strong> ${this.getSourceText(record.data_source)}<br>
                        <strong>发现时间:</strong> ${this.formatDateTime(record.created_at)}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 处理差异
     */
    async processDifference() {
        try {
            const form = document.getElementById('processDifferenceForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const data = {
                difference_id: document.getElementById('differenceId').value,
                action: document.getElementById('processAction').value,
                adjust_amount: document.getElementById('adjustAmount').value || null,
                note: document.getElementById('processNote').value
            };

            const response = await this.apiClient.post('/admin/finance/reconcile/process-difference', data);

            if (response.success) {
                this.showSuccess('差异处理成功');
                bootstrap.Modal.getInstance(document.getElementById('processDifferenceModal')).hide();
                await this.loadReconciliations();
                await this.loadReconciliationStats();
            } else {
                throw new Error(response.message || '处理失败');
            }

        } catch (error) {
            console.error('处理差异失败:', error);
            this.showError('处理失败: ' + error.message);
        }
    }

    /**
     * 启动自动对账
     */
    startAutoReconcile() {
        // 每5分钟检查一次自动对账状态
        this.autoReconcileTimer = setInterval(() => {
            this.loadReconciliationStats();
        }, 300000);
    }

    /**
     * 停止自动对账
     */
    async stopAutoReconcile() {
        try {
            const response = await this.apiClient.post('/admin/finance/reconcile/stop-auto');
            
            if (response.success) {
                this.showSuccess('自动对账已停止');
                document.getElementById('autoReconcileStatus').textContent = '已停止';
                document.getElementById('autoReconcileStatus').className = 'badge bg-danger ms-2';
            }
        } catch (error) {
            console.error('停止自动对账失败:', error);
            this.showError('停止失败: ' + error.message);
        }
    }

    /**
     * 更新统计显示
     */
    updateStatsDisplay(stats) {
        document.getElementById('matchedCount').textContent = stats.matched_count || 0;
        document.getElementById('differenceCount').textContent = stats.difference_count || 0;
        document.getElementById('unmatchedCount').textContent = stats.unmatched_count || 0;
        document.getElementById('pendingCount').textContent = stats.pending_count || 0;
        document.getElementById('matchRate').textContent = (stats.match_rate || 0) + '%';
        document.getElementById('differenceAmount').textContent = '¥' + this.formatAmount(stats.difference_amount || 0);
    }

    /**
     * 更新自动对账状态
     */
    updateAutoReconcileStatus(autoStatus) {
        document.getElementById('lastReconcileTime').textContent = autoStatus.last_reconcile_time || '--';
        document.getElementById('nextReconcileTime').textContent = autoStatus.next_reconcile_time || '--';
        document.getElementById('avgReconcileTime').textContent = (autoStatus.avg_reconcile_time || 0) + 's';
        document.getElementById('todayReconcileCount').textContent = autoStatus.today_reconcile_count || 0;
        
        const statusEl = document.getElementById('autoReconcileStatus');
        if (autoStatus.is_running) {
            statusEl.textContent = '运行中';
            statusEl.className = 'badge bg-success ms-2';
        } else {
            statusEl.textContent = '已停止';
            statusEl.className = 'badge bg-danger ms-2';
        }
    }

    /**
     * 获取数据源文本
     */
    getSourceText(source) {
        const sourceMap = {
            'internal': '内部系统',
            'alipay': '支付宝',
            'wechat': '微信',
            'bank': '银行'
        };
        return sourceMap[source] || source;
    }

    /**
     * 获取数据源徽章颜色
     */
    getSourceBadgeColor(source) {
        const colorMap = {
            'internal': 'primary',
            'alipay': 'info',
            'wechat': 'success',
            'bank': 'warning'
        };
        return colorMap[source] || 'secondary';
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'matched': '已匹配',
            'unmatched': '未匹配',
            'difference': '有差异',
            'processing': '处理中'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取状态徽章颜色
     */
    getStatusBadgeColor(status) {
        const colorMap = {
            'matched': 'success',
            'unmatched': 'danger',
            'difference': 'warning',
            'processing': 'info'
        };
        return colorMap[status] || 'secondary';
    }

    /**
     * 格式化金额
     */
    formatAmount(amount) {
        return parseFloat(amount || 0).toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    /**
     * 格式化日期
     */
    formatDate(date) {
        return new Date(date).toLocaleDateString('zh-CN');
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(datetime) {
        return new Date(datetime).toLocaleString('zh-CN');
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        console.log('✅', message);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error('❌', message);
    }

    /**
     * 销毁管理器
     */
    destroy() {
        if (this.autoReconcileTimer) {
            clearInterval(this.autoReconcileTimer);
        }
    }
}

// 导出模块
window.ReconciliationManager = ReconciliationManager;

// 创建全局实例
if (typeof window.reconciliationManager === 'undefined') {
    window.reconciliationManager = new ReconciliationManager();
}

console.log('🔍 ReconciliationManager 模块加载完成'); 