/**
 * 支付请求管理器
 * 从admin.js第11234-11943行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class PaymentRequestManager {
    constructor() {
        this.auth = new AuthManager();
        this.currentPage = 1;
        this.pageSize = 20;
        this.paymentRequests = [];
        this.filters = {
            merchant_id: '',
            status: '',
            date_range: 'today',
            start_date: '',
            end_date: '',
            search: ''
        };
        this.selectedRequestId = null;
    }
    async initialize() {
        this.bindEvents();
        await this.loadMerchantOptions();
        await this.loadPaymentRequests();
    }
    bindEvents() {
        // 筛选器变化事件
        const merchantFilter = document.getElementById('merchantFilter');
        if (merchantFilter) {
            merchantFilter.addEventListener('change', (e) => {
                this.filters.merchant_id = e.target.value;
                this.applyFilters();
            });
        }
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }
        const dateRangeFilter = document.getElementById('dateRangeFilter');
        if (dateRangeFilter) {
            dateRangeFilter.addEventListener('change', (e) => {
                this.filters.date_range = e.target.value;
                this.toggleCustomDateRange();
                if (e.target.value !== 'custom') {
                    this.applyFilters();
                }
            });
        }
        // 搜索框回车事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchPaymentRequests();
                }
            });
        }
    }
    async loadPaymentRequests(page = 1) {
        try {
            this.currentPage = page;
            this.showLoadingState();
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                merchant_id: this.filters.merchant_id,
                status: this.filters.status,
                date_range: this.filters.date_range,
                start_date: this.filters.start_date,
                end_date: this.filters.end_date,
                search: this.filters.search
            });
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=payment_requests&${
                params
            }`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`,
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.paymentRequests = result.data.payment_requests || [];
                this.updateStatsCards(result.data.stats || {
                });
                this.renderPaymentRequestsList(this.paymentRequests);
                this.renderPagination(result.data.pagination || {
                });
            } else {
                throw new Error(result.message || '加载支付请求数据失败');
            }
        } catch (error) {
            console.error('加载支付请求数据失败:',
            error);
            this.showErrorState('加载支付请求数据失败: ' + error.message);
        }
    }
    updateStatsCards(stats) {
        document.getElementById('totalRequests').textContent = stats.total_requests || 0;
        document.getElementById('successRequests').textContent = stats.success_requests || 0;
        document.getElementById('pendingRequests').textContent = stats.pending_requests || 0;
        document.getElementById('failedRequests').textContent = stats.failed_requests || 0;
    }
    renderPaymentRequestsList(requests) {
        const container = document.getElementById('paymentRequestTableContainer');
        document.getElementById('paymentRequestCount').textContent = requests.length;
        if (requests.length === 0) {
            container.innerHTML = `
            <div class="text-center py-4">
            <div class="no-data-icon">💳</div>
            <h5>暂无支付请求数据</h5>
            <p class="text-muted">还没有符合条件的支付请求，或者当前筛选条件下没有找到匹配的数据。</p>
            </div>
            `;
            return;
        }
        const tableHtml = `
        <div class="table-responsive">
        <table class="table table-striped">
        <thead class="table-dark">
        <tr>
        <th>订单号</th>
        <th>商户信息</th>
        <th>支付金额</th>
        <th>支付方式</th>
        <th>状态</th>
        <th>创建时间</th>
        <th>更新时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        ${
            requests.map(request => this.renderPaymentRequestRow(request)).join('')
        }
        </tbody>
        </table>
        </div>
        `;
        container.innerHTML = tableHtml;
    }
    renderPaymentRequestRow(request) {
        const statusBadges = {
            'pending': 'badge bg-warning',
            'processing': 'badge bg-info',
            'success': 'badge bg-success',
            'failed': 'badge bg-danger',
            'cancelled': 'badge bg-secondary'
        };
        const statusTexts = {
            'pending': '待处理',
            'processing': '处理中',
            'success': '成功',
            'failed': '失败',
            'cancelled': '已取消'
        };
        const paymentMethods = {
            'alipay': '支付宝',
            'wechat': '微信支付',
            'unionpay': '银联支付'
        };
        return `
        <tr>
        <td>
        <div class="fw-bold">${
            request.out_trade_no || request.order_id
        }</div>
        <div class="text-muted small">ID: ${
            request.id
        }</div>
        </td>
        <td>
        <div class="fw-bold">${
            request.merchant_name || '未知商户'
        }</div>
        <div class="text-muted small">ID: ${
            request.merchant_id
        }</div>
        </td>
        <td>
        <div class="fw-bold text-primary">¥${
            parseFloat(request.amount || 0).toFixed(2)
        }</div>
        ${
            request.service_fee ? `<div class="text-muted small">手续费: ¥${
                parseFloat(request.service_fee).toFixed(2)
            }</div>` : ''
        }
        </td>
        <td>
        <span class="badge bg-light text-dark">
        ${
            paymentMethods[request.payment_method] || request.payment_method || '未知'
        }
        </span>
        </td>
        <td>
        <span class="${
            statusBadges[request.status] || 'badge bg-secondary'
        }">
        ${
            statusTexts[request.status] || request.status
        }
        </span>
        </td>
        <td>
        <div class="small">${
            this.formatDateTime(request.created_at)
        }</div>
        </td>
        <td>
        <div class="small">${
            this.formatDateTime(request.updated_at)
        }</div>
        </td>
        <td>
        <div class="btn-group" role="group">
        <button class="btn btn-outline-primary btn-sm" onclick="window.paymentRequestManager.viewPaymentRequestDetail(${
            request.id
        })" title="查看详情">
        <i class="bi bi-eye"></i>
        </button>
        ${
            request.status !== 'success' ? `
            <button class="btn btn-outline-warning btn-sm" onclick="window.paymentRequestManager.showUpdateStatus(${
                request.id
            })" title="更新状态">
            <i class="bi bi-arrow-repeat"></i>
            </button>
            ` : ''
        }
        ${
            request.status === 'failed' ? `
            <button class="btn btn-outline-success btn-sm" onclick="window.paymentRequestManager.retryPaymentRequest(${
                request.id
            })" title="重试">
            <i class="bi bi-arrow-clockwise"></i>
            </button>
            ` : ''
        }
        </div>
        </td>
        </tr>
        `;
    }
    renderPagination(pagination) {
        const container = document.getElementById('paymentRequestPagination');
        if (!container || !pagination.total_pages || pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }
        const currentPage = pagination.current_page || 1;
        const totalPages = pagination.total_pages;
        const maxVisiblePages = 5;
        let startPage = Math.max(1,
        currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages,
        startPage + maxVisiblePages - 1);
        if (endPage - startPage < maxVisiblePages - 1) {
            startPage = Math.max(1,
            endPage - maxVisiblePages + 1);
        }
        let paginationHtml = `
        <nav aria-label="支付请求分页">
        <ul class="pagination pagination-sm">
        `;
        // 上一页
        if (currentPage > 1) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.paymentRequestManager.loadPaymentRequests(${
                currentPage - 1
            });
            return false;
            ">&laquo;
            </a>
            </li>
            `;
        }
        // 页码
        for (let i = startPage;
        i <= endPage;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === currentPage ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.paymentRequestManager.loadPaymentRequests(${
                i
            });
            return false;
            ">${
                i
            }</a>
            </li>
            `;
        }
        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.paymentRequestManager.loadPaymentRequests(${
                currentPage + 1
            });
            return false;
            ">&raquo;
            </a>
            </li>
            `;
        }
        paginationHtml += `
        </ul>
        </nav>
        `;
        container.innerHTML = paginationHtml;
    }
    async loadMerchantOptions() {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=merchants&simple=1`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                const merchantFilter = document.getElementById('merchantFilter');
                if (merchantFilter && result.data.merchants) {
                    let optionsHtml = '<option value="">所有商户</option>';
                    result.data.merchants.forEach(merchant => {
                        optionsHtml += `<option value="${
                            merchant.id
                        }">${
                            merchant.name || merchant.username
                        }</option>`;
                    });
                    merchantFilter.innerHTML = optionsHtml;
                }
            }
        } catch (error) {
            console.error('加载商户选项失败:',
            error);
        }
    }
    toggleCustomDateRange() {
        const dateRangeFilter = document.getElementById('dateRangeFilter');
        const customDateRange = document.getElementById('customDateRange');
        if (dateRangeFilter && customDateRange) {
            if (dateRangeFilter.value === 'custom') {
                customDateRange.style.display = 'block';
            } else {
                customDateRange.style.display = 'none';
            }
        }
    }
    applyFilters() {
        if (this.filters.date_range === 'custom') {
            const startDate = document.getElementById('startDate');
            const endDate = document.getElementById('endDate');
            if (startDate && endDate) {
                this.filters.start_date = startDate.value;
                this.filters.end_date = endDate.value;
            }
        }
        this.loadPaymentRequests(1);
    }
    searchPaymentRequests() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            this.filters.search = searchInput.value.trim();
            this.loadPaymentRequests(1);
        }
    }
    refreshPaymentRequests() {
        this.loadPaymentRequests(this.currentPage);
    }
    async viewPaymentRequestDetail(requestId) {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=payment_request_detail&id=${
                requestId
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showPaymentRequestDetailModal(result.data);
            } else {
                throw new Error(result.message || '获取支付请求详情失败');
            }
        } catch (error) {
            console.error('获取支付请求详情失败:',
            error);
            this.showError('获取支付请求详情失败: ' + error.message);
        }
    }
    showPaymentRequestDetailModal(request) {
        const modalContent = document.getElementById('paymentRequestDetailContent');
        if (!modalContent) return;
        const statusBadges = {
            'pending': 'badge bg-warning',
            'processing': 'badge bg-info',
            'success': 'badge bg-success',
            'failed': 'badge bg-danger',
            'cancelled': 'badge bg-secondary'
        };
        const statusTexts = {
            'pending': '待处理',
            'processing': '处理中',
            'success': '成功',
            'failed': '失败',
            'cancelled': '已取消'
        };
        modalContent.innerHTML = `
        <div class="row">
        <div class="col-md-6">
        <h6>基本信息</h6>
        <table class="table table-sm">
        <tr>
        <td><strong>订单号:</strong></td>
        <td>${
            request.out_trade_no || request.order_id
        }</td>
        </tr>
        <tr>
        <td><strong>商户:</strong></td>
        <td>${
            request.merchant_name || '未知商户'
        }</td>
        </tr>
        <tr>
        <td><strong>支付金额:</strong></td>
        <td class="text-primary fw-bold">¥${
            parseFloat(request.amount || 0).toFixed(2)
        }</td>
        </tr>
        <tr>
        <td><strong>手续费:</strong></td>
        <td>¥${
            parseFloat(request.service_fee || 0).toFixed(2)
        }</td>
        </tr>
        <tr>
        <td><strong>支付方式:</strong></td>
        <td>${
            request.payment_method || '未知'
        }</td>
        </tr>
        <tr>
        <td><strong>状态:</strong></td>
        <td>
        <span class="${
            statusBadges[request.status] || 'badge bg-secondary'
        }">
        ${
            statusTexts[request.status] || request.status
        }
        </span>
        </td>
        </tr>
        </table>
        </div>
        <div class="col-md-6">
        <h6>时间信息</h6>
        <table class="table table-sm">
        <tr>
        <td><strong>创建时间:</strong></td>
        <td>${
            this.formatDateTime(request.created_at)
        }</td>
        </tr>
        <tr>
        <td><strong>更新时间:</strong></td>
        <td>${
            this.formatDateTime(request.updated_at)
        }</td>
        </tr>
        ${
            request.paid_at ? `
            <tr>
            <td><strong>支付时间:</strong></td>
            <td>${
                this.formatDateTime(request.paid_at)
            }</td>
            </tr>
            ` : ''
        }
        </table>
        <h6>技术信息</h6>
        <table class="table table-sm">
        <tr>
        <td><strong>客户端IP:</strong></td>
        <td>${
            request.client_ip || '未知'
        }</td>
        </tr>
        <tr>
        <td><strong>产品ID:</strong></td>
        <td>${
            request.product_id || '未知'
        }</td>
        </tr>
        ${
            request.callback_url ? `
            <tr>
            <td><strong>回调地址:</strong></td>
            <td class="text-break">${
                request.callback_url
            }</td>
            </tr>
            ` : ''
        }
        </table>
        </div>
        </div>
        ${
            request.remark ? `
            <div class="mt-3">
            <h6>备注信息</h6>
            <div class="alert alert-info">
            ${
                request.remark
            }
            </div>
            </div>
            ` : ''
        }
        ${
            request.error_message ? `
            <div class="mt-3">
            <h6>错误信息</h6>
            <div class="alert alert-danger">
            ${
                request.error_message
            }
            </div>
            </div>
            ` : ''
        }
        `;
        this.selectedRequestId = request.id;
        const modal = new bootstrap.Modal(document.getElementById('paymentRequestDetailModal'));
        modal.show();
    }
    showUpdateStatus(requestId) {
        this.selectedRequestId = requestId;
        document.getElementById('updateRequestId').value = requestId;
        document.getElementById('newStatus').value = '';
        document.getElementById('statusRemark').value = '';
        const modal = new bootstrap.Modal(document.getElementById('updateStatusModal'));
        modal.show();
    }
    async confirmUpdateStatus() {
        const requestId = document.getElementById('updateRequestId').value;
        const newStatus = document.getElementById('newStatus').value;
        const remark = document.getElementById('statusRemark').value;
        if (!newStatus) {
            this.showError('请选择新状态');
            return;
        }
        try {
            this.setUpdateLoading(true);
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=update_payment_status`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    request_id: requestId,
                    status: newStatus,
                    remark: remark
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('支付状态更新成功');
                bootstrap.Modal.getInstance(document.getElementById('updateStatusModal')).hide();
                this.refreshPaymentRequests();
            } else {
                throw new Error(result.message || '更新支付状态失败');
            }
        } catch (error) {
            console.error('更新支付状态失败:',
            error);
            this.showError('更新支付状态失败: ' + error.message);
        } finally {
            this.setUpdateLoading(false);
        }
    }
    async retryPaymentRequest(requestId = null) {
        const targetId = requestId || this.selectedRequestId;
        if (!targetId) return;
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=retry_payment_request`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    request_id: targetId
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('支付请求重试成功');
                this.refreshPaymentRequests();
            } else {
                throw new Error(result.message || '重试支付请求失败');
            }
        } catch (error) {
            console.error('重试支付请求失败:',
            error);
            this.showError('重试支付请求失败: ' + error.message);
        }
    }
    batchUpdateStatus() {
        // 批量操作功能，可以后续扩展
        this.showInfo('批量操作功能正在开发中...');
    }
    exportData() {
        const params = new URLSearchParams({
            merchant_id: this.filters.merchant_id,
            status: this.filters.status,
            date_range: this.filters.date_range,
            start_date: this.filters.start_date,
            end_date: this.filters.end_date,
            search: this.filters.search,
            export: 1
        });
        const url = `${
            CONFIG.API_BASE_URL
        }/admin.php?action=payment_requests&${
            params
        }`;
        window.open(url, '_blank');
    }
    setUpdateLoading(loading) {
        const updateBtn = document.querySelector('#updateStatusModal .btn-primary');
        const updateBtnText = updateBtn?.querySelector('.update-btn-text');
        const updateBtnLoading = updateBtn?.querySelector('.update-btn-loading');
        if (updateBtn) {
            updateBtn.disabled = loading;
            if (updateBtnText) updateBtnText.style.display = loading ? 'none' : 'inline';
            if (updateBtnLoading) updateBtnLoading.style.display = loading ? 'inline' : 'none';
        }
    }
    showLoadingState() {
        const container = document.getElementById('paymentRequestTableContainer');
        if (container) {
            container.innerHTML = `
            <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-3">正在加载支付请求数据...</div>
            </div>
            `;
        }
    }
    showErrorState(message) {
        const container = document.getElementById('paymentRequestTableContainer');
        if (container) {
            container.innerHTML = `
            <div class="text-center py-5">
            <div class="error-icon text-danger">
            <i class="bi bi-exclamation-triangle" style="font-size: 3rem;
            "></i>
            </div>
            <h5 class="mt-3">加载失败</h5>
            <p class="text-muted">${
                message
            }</p>
            <button class="btn btn-primary" onclick="window.paymentRequestManager.refreshPaymentRequests()">
            <i class="bi bi-arrow-clockwise me-2"></i>重新加载
            </button>
            </div>
            `;
        }
    }
    formatDateTime(dateString) {
        if (!dateString) return '未知';
        try {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            return dateString;
        }
    }
    showSuccess(message) {
        // 创建成功提示
        const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px;
        right: 20px;
        z-index: 9999;
        ">
        <i class="bi bi-check-circle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        `;
        document.body.insertAdjacentHTML('beforeend',
        alertHtml);
        // 3秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 3000);
    }
    showError(message) {
        // 创建错误提示
        const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show position-fixed" style="top: 20px;
        right: 20px;
        z-index: 9999;
        ">
        <i class="bi bi-exclamation-triangle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        `;
        document.body.insertAdjacentHTML('beforeend',
        alertHtml);
        // 5秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }
    showInfo(message) {
        // 创建信息提示
        const alertHtml = `
        <div class="alert alert-info alert-dismissible fade show position-fixed" style="top: 20px;
        right: 20px;
        z-index: 9999;
        ">
        <i class="bi bi-info-circle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        `;
        document.body.insertAdjacentHTML('beforeend',
        alertHtml);
        // 3秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-info');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 3000);
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = PaymentRequestManager;
} else if (typeof window !== "undefined") {
    window.PaymentRequestManager = PaymentRequestManager;
}

console.log('📦 PaymentRequestManager 模块加载完成');
