/**
 * 性能监控模块
 * 负责系统性能监控、统计和报告
 */
class PerformanceManager {
    constructor() {
        this.auth = new AuthManager();
        this.currentView = 'overview';
        this.refreshInterval = null;
        this.charts = {};
    }

    // 显示性能监控界面
    showPerformanceMonitor() {
        const content = `
            <div class="performance-monitor">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4 class="mb-1">
                            <i class="bi bi-speedometer2 me-2"></i>性能监控
                        </h4>
                        <p class="text-muted mb-0">系统资源实时监控和分析</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="window.performanceManager.exportPerformanceReport()">
                            <i class="bi bi-download me-2"></i>导出报告
                        </button>
                        <button class="btn btn-outline-secondary" onclick="window.performanceManager.refreshData()">
                            <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                        </button>
                    </div>
                </div>

                <!-- 性能概览卡片 -->
                <div class="row mb-4" id="performanceOverviewCards">
                    <!-- 动态加载 -->
                </div>

                <!-- 导航标签 -->
                <ul class="nav nav-tabs mb-4" role="tablist">
                    <li class="nav-item">
                        <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#overview-tab" onclick="window.performanceManager.switchView('overview')">
                            <i class="bi bi-graph-up me-2"></i>性能概览
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#system-tab" onclick="window.performanceManager.switchView('system')">
                            <i class="bi bi-cpu me-2"></i>系统统计
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#database-tab" onclick="window.performanceManager.switchView('database')">
                            <i class="bi bi-database me-2"></i>数据库性能
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#api-tab" onclick="window.performanceManager.switchView('api')">
                            <i class="bi bi-cloud me-2"></i>API性能
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#cache-tab" onclick="window.performanceManager.switchView('cache')">
                            <i class="bi bi-lightning me-2"></i>缓存统计
                        </button>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="overview-tab">
                        <div id="overviewContent">
                            <!-- 动态加载 -->
                        </div>
                    </div>
                    <div class="tab-pane fade" id="system-tab">
                        <div id="systemContent">
                            <!-- 动态加载 -->
                        </div>
                    </div>
                    <div class="tab-pane fade" id="database-tab">
                        <div id="databaseContent">
                            <!-- 动态加载 -->
                        </div>
                    </div>
                    <div class="tab-pane fade" id="api-tab">
                        <div id="apiContent">
                            <!-- 动态加载 -->
                        </div>
                    </div>
                    <div class="tab-pane fade" id="cache-tab">
                        <div id="cacheContent">
                            <!-- 动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('mainContent').innerHTML = content;
        this.loadPerformanceOverview();
        this.startAutoRefresh();
    }

    // 切换视图
    switchView(view) {
        this.currentView = view;
        switch (view) {
            case 'overview':
                this.loadPerformanceOverview();
                break;
            case 'system':
                this.loadSystemStats();
                break;
            case 'database':
                this.loadDatabasePerformance();
                break;
            case 'api':
                this.loadApiPerformance();
                break;
            case 'cache':
                this.loadCacheStats();
                break;
        }
    }

    // 加载性能概览
    async loadPerformanceOverview() {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=performance_monitor`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const result = await response.json();
            
            if (result.code === 200) {
                this.renderOverviewCards(result.data);
                this.renderOverviewCharts(result.data);
            } else {
                throw new Error(result.message || '获取性能数据失败');
            }
        } catch (error) {
            console.error('加载性能概览失败:', error);
            this.showError('加载性能概览失败: ' + error.message);
        }
    }

    // 渲染概览卡片
    renderOverviewCards(data) {
        const cardsContainer = document.getElementById('performanceOverviewCards');
        if (!cardsContainer) return;

        const cards = [
            {
                title: '性能评分',
                value: data.performance_score || 0,
                unit: '分',
                icon: 'speedometer2',
                color: (data.performance_score || 0) >= 80 ? 'success' : (data.performance_score || 0) >= 60 ? 'warning' : 'danger'
            },
            {
                title: '数据库连接',
                value: data.database_info?.connections || 0,
                unit: '/' + (data.database_info?.max_connections || 0),
                icon: 'database',
                color: 'info'
            },
            {
                title: '内存使用',
                value: data.system_info?.current_memory_usage || '0MB',
                unit: '',
                icon: 'memory',
                color: 'primary'
            },
            {
                title: '今日API调用',
                value: data.api_stats?.today_calls || 0,
                unit: '次',
                icon: 'cloud',
                color: 'success'
            },
            {
                title: '缓存命中率',
                value: data.cache_stats?.hit_rate || 0,
                unit: '%',
                icon: 'lightning',
                color: 'warning'
            },
            {
                title: '慢查询数',
                value: data.slow_query_count || 0,
                unit: '个',
                icon: 'exclamation-triangle',
                color: (data.slow_query_count || 0) > 10 ? 'danger' : 'success'
            }
        ];

        const cardsHtml = cards.map(card => `
            <div class="col-md-4 col-lg-2 mb-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="d-flex align-items-center justify-content-center mb-2">
                            <i class="bi bi-${card.icon} text-${card.color}" style="font-size: 2rem;"></i>
                        </div>
                        <h6 class="card-title text-muted mb-1">${card.title}</h6>
                        <h4 class="card-text text-${card.color} mb-0">
                            ${card.value}<small class="text-muted">${card.unit}</small>
                        </h4>
                    </div>
                </div>
            </div>
        `).join('');

        cardsContainer.innerHTML = cardsHtml;
    }

    // 渲染概览图表
    renderOverviewCharts(data) {
        const overviewContent = document.getElementById('overviewContent');
        if (!overviewContent) return;

        const chartsHtml = `
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-info-circle me-2"></i>系统信息
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">PHP版本</small>
                                    <div class="fw-bold">${data.system_info?.php_version || 'N/A'}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">MySQL版本</small>
                                    <div class="fw-bold">${data.database_info?.version || 'N/A'}</div>
                                </div>
                                <div class="col-6 mt-3">
                                    <small class="text-muted">内存限制</small>
                                    <div class="fw-bold">${data.system_info?.memory_limit || 'N/A'}</div>
                                </div>
                                <div class="col-6 mt-3">
                                    <small class="text-muted">峰值内存</small>
                                    <div class="fw-bold">${data.system_info?.peak_memory_usage || 'N/A'}</div>
                                </div>
                                <div class="col-12 mt-3">
                                    <small class="text-muted">服务器时间</small>
                                    <div class="fw-bold">${data.system_info?.server_time || new Date().toLocaleString()}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-activity me-2"></i>实时状态
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <small class="text-muted">CPU使用率</small>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-info" style="width: ${data.system_info?.cpu_usage || 0}%">${data.system_info?.cpu_usage || 0}%</div>
                                    </div>
                                </div>
                                <div class="col-12 mb-3">
                                    <small class="text-muted">内存使用率</small>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-warning" style="width: ${data.system_info?.memory_usage_percent || 0}%">${data.system_info?.memory_usage_percent || 0}%</div>
                                    </div>
                                </div>
                                <div class="col-12 mb-3">
                                    <small class="text-muted">磁盘使用率</small>
                                    <div class="progress mt-1">
                                        <div class="progress-bar bg-danger" style="width: ${data.system_info?.disk_usage_percent || 0}%">${data.system_info?.disk_usage_percent || 0}%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        overviewContent.innerHTML = chartsHtml;
    }

    // 加载系统统计
    async loadSystemStats() {
        const systemContent = document.getElementById('systemContent');
        if (!systemContent) return;

        systemContent.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-3">正在加载系统统计数据...</div>
            </div>
        `;

        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=system_stats`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const result = await response.json();
            
            if (result.code === 200) {
                this.renderSystemStats(result.data);
            } else {
                throw new Error(result.message || '获取系统统计失败');
            }
        } catch (error) {
            console.error('加载系统统计失败:', error);
            systemContent.innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <div class="mt-3">加载系统统计失败</div>
                    <small class="text-muted">${error.message}</small>
                </div>
            `;
        }
    }

    // 渲染系统统计
    renderSystemStats(data) {
        const systemContent = document.getElementById('systemContent');
        if (!systemContent) return;

        const statsHtml = `
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-cpu me-2"></i>处理器信息
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">处理器型号</small>
                                    <div class="fw-bold">${data.cpu_info?.model || 'N/A'}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">核心数</small>
                                    <div class="fw-bold">${data.cpu_info?.cores || 'N/A'}</div>
                                </div>
                                <div class="col-6 mt-3">
                                    <small class="text-muted">当前负载</small>
                                    <div class="fw-bold">${data.cpu_info?.load_average || 'N/A'}</div>
                                </div>
                                <div class="col-6 mt-3">
                                    <small class="text-muted">运行时间</small>
                                    <div class="fw-bold">${data.system_info?.uptime || 'N/A'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-hdd me-2"></i>存储信息
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">总容量</small>
                                    <div class="fw-bold">${data.disk_info?.total_space || 'N/A'}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">可用空间</small>
                                    <div class="fw-bold">${data.disk_info?.free_space || 'N/A'}</div>
                                </div>
                                <div class="col-6 mt-3">
                                    <small class="text-muted">已用空间</small>
                                    <div class="fw-bold">${data.disk_info?.used_space || 'N/A'}</div>
                                </div>
                                <div class="col-6 mt-3">
                                    <small class="text-muted">使用率</small>
                                    <div class="fw-bold">${data.disk_info?.usage_percent || 0}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        systemContent.innerHTML = statsHtml;
    }

    // 加载数据库性能
    async loadDatabasePerformance() {
        const databaseContent = document.getElementById('databaseContent');
        if (!databaseContent) return;

        databaseContent.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-3">正在加载数据库性能数据...</div>
            </div>
        `;

        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=database_performance`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const result = await response.json();
            
            if (result.code === 200) {
                this.renderDatabasePerformance(result.data);
            } else {
                throw new Error(result.message || '获取数据库性能失败');
            }
        } catch (error) {
            console.error('加载数据库性能失败:', error);
            databaseContent.innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <div class="mt-3">加载数据库性能失败</div>
                    <small class="text-muted">${error.message}</small>
                </div>
            `;
        }
    }

    // 渲染数据库性能
    renderDatabasePerformance(data) {
        const databaseContent = document.getElementById('databaseContent');
        if (!databaseContent) return;

        const performanceHtml = `
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-database me-2"></i>数据库连接状态
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <small class="text-muted">当前连接数</small>
                                    <div class="fw-bold text-primary">${data.connections?.current || 0}</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">最大连接数</small>
                                    <div class="fw-bold">${data.connections?.max || 0}</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">活跃连接</small>
                                    <div class="fw-bold text-success">${data.connections?.active || 0}</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">空闲连接</small>
                                    <div class="fw-bold text-info">${data.connections?.idle || 0}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-clock me-2"></i>慢查询监控
                            </h6>
                        </div>
                        <div class="card-body">
                            ${data.slow_queries && data.slow_queries.length > 0 ? `
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>查询时间</th>
                                                <th>执行时长</th>
                                                <th>查询语句</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${data.slow_queries.map(query => `
                                                <tr>
                                                    <td><small>${this.formatDateTime(query.query_time)}</small></td>
                                                    <td><span class="badge bg-warning">${query.execution_time}s</span></td>
                                                    <td><code class="small">${this.truncateText(query.sql_text, 100)}</code></td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            ` : `
                                <div class="text-center py-3">
                                    <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                                    <div class="mt-2">暂无慢查询记录</div>
                                </div>
                            `}
                        </div>
                    </div>
                </div>
            </div>
        `;

        databaseContent.innerHTML = performanceHtml;
    }

    // 加载API性能
    async loadApiPerformance() {
        const apiContent = document.getElementById('apiContent');
        if (!apiContent) return;

        apiContent.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-3">正在加载API性能数据...</div>
            </div>
        `;

        // 模拟API性能数据
        setTimeout(() => {
            const mockData = {
                total_requests: 12456,
                success_rate: 99.2,
                avg_response_time: 245,
                error_count: 23,
                endpoints: [
                    { path: '/api/payment/create', requests: 3456, avg_time: 189, success_rate: 99.8 },
                    { path: '/api/payment/query', requests: 2890, avg_time: 156, success_rate: 99.9 },
                    { path: '/api/user/login', requests: 1890, avg_time: 234, success_rate: 98.5 },
                    { path: '/api/transaction/list', requests: 1567, avg_time: 298, success_rate: 99.1 }
                ]
            };
            this.renderApiPerformance(mockData);
        }, 1000);
    }

    // 渲染API性能
    renderApiPerformance(data) {
        const apiContent = document.getElementById('apiContent');
        if (!apiContent) return;

        const performanceHtml = `
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-cloud me-2"></i>API总体性能
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <small class="text-muted">总请求数</small>
                                    <div class="fw-bold text-primary">${data.total_requests?.toLocaleString() || 0}</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">成功率</small>
                                    <div class="fw-bold text-success">${data.success_rate || 0}%</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">平均响应时间</small>
                                    <div class="fw-bold text-info">${data.avg_response_time || 0}ms</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">错误数量</small>
                                    <div class="fw-bold text-danger">${data.error_count || 0}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-list me-2"></i>热门API端点
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>端点路径</th>
                                            <th>请求数</th>
                                            <th>平均响应时间</th>
                                            <th>成功率</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${data.endpoints?.map(endpoint => `
                                            <tr>
                                                <td><code>${endpoint.path}</code></td>
                                                <td>${endpoint.requests?.toLocaleString() || 0}</td>
                                                <td>${endpoint.avg_time || 0}ms</td>
                                                <td>${endpoint.success_rate || 0}%</td>
                                                <td>
                                                    <span class="badge ${endpoint.success_rate >= 99 ? 'bg-success' : endpoint.success_rate >= 95 ? 'bg-warning' : 'bg-danger'}">
                                                        ${endpoint.success_rate >= 99 ? '优秀' : endpoint.success_rate >= 95 ? '良好' : '需优化'}
                                                    </span>
                                                </td>
                                            </tr>
                                        `).join('') || ''}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        apiContent.innerHTML = performanceHtml;
    }

    // 加载缓存统计
    async loadCacheStats() {
        const cacheContent = document.getElementById('cacheContent');
        if (!cacheContent) return;

        cacheContent.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-3">正在加载缓存统计数据...</div>
            </div>
        `;

        // 模拟缓存统计数据
        setTimeout(() => {
            const mockData = {
                hit_rate: 89.5,
                total_keys: 15678,
                memory_usage: '245MB',
                memory_limit: '512MB',
                expired_keys: 234,
                evicted_keys: 45
            };
            this.renderCacheStats(mockData);
        }, 800);
    }

    // 渲染缓存统计
    renderCacheStats(data) {
        const cacheContent = document.getElementById('cacheContent');
        if (!cacheContent) return;

        const statsHtml = `
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-lightning me-2"></i>缓存性能统计
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <small class="text-muted">命中率</small>
                                    <div class="fw-bold text-success">${data.hit_rate || 0}%</div>
                                </div>
                                <div class="col-md-2">
                                    <small class="text-muted">总键数</small>
                                    <div class="fw-bold text-primary">${data.total_keys?.toLocaleString() || 0}</div>
                                </div>
                                <div class="col-md-2">
                                    <small class="text-muted">内存使用</small>
                                    <div class="fw-bold text-info">${data.memory_usage || '0MB'}</div>
                                </div>
                                <div class="col-md-2">
                                    <small class="text-muted">内存限制</small>
                                    <div class="fw-bold">${data.memory_limit || '0MB'}</div>
                                </div>
                                <div class="col-md-2">
                                    <small class="text-muted">过期键数</small>
                                    <div class="fw-bold text-warning">${data.expired_keys || 0}</div>
                                </div>
                                <div class="col-md-2">
                                    <small class="text-muted">淘汰键数</small>
                                    <div class="fw-bold text-danger">${data.evicted_keys || 0}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        cacheContent.innerHTML = statsHtml;
    }

    // 开始自动刷新
    startAutoRefresh() {
        // 清除之前的定时器
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        // 每30秒自动刷新数据
        this.refreshInterval = setInterval(() => {
            this.refreshData();
        }, 30000);
    }

    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // 刷新数据
    refreshData() {
        switch (this.currentView) {
            case 'overview':
                this.loadPerformanceOverview();
                break;
            case 'system':
                this.loadSystemStats();
                break;
            case 'database':
                this.loadDatabasePerformance();
                break;
            case 'api':
                this.loadApiPerformance();
                break;
            case 'cache':
                this.loadCacheStats();
                break;
        }
    }

    // 导出性能报告
    async exportPerformanceReport() {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=export_performance_report`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `performance_report_${new Date().toISOString().split('T')[0]}.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                this.showSuccess('性能报告导出成功');
            } else {
                throw new Error('导出失败');
            }
        } catch (error) {
            console.error('导出性能报告失败:', error);
            this.showError('导出性能报告失败: ' + error.message);
        }
    }

    // 工具方法
    formatDateTime(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    showSuccess(message) {
        // 简单的成功提示，以后可以改为更好的UI组件
        alert('✓ ' + message);
    }

    showError(message) {
        // 简单的错误提示，以后可以改为更好的UI组件
        alert('✗ ' + message);
    }

    // 销毁方法
    destroy() {
        this.stopAutoRefresh();
        this.charts = {};
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceManager;
} else if (typeof window !== 'undefined') {
    window.PerformanceManager = PerformanceManager;
}