<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$result = array();

try {
    require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
    $result['database_require'] = 'success';
    
    $db = Database::getInstance()->getConnection();
    $result['database_connection'] = 'success';
    
    // 测试查询script_versions表
    $stmt = $db->prepare("SELECT COUNT(*) FROM script_versions");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    $result['script_count'] = $count;
    
    // 测试获取脚本列表
    $stmt = $db->prepare("SELECT id, script_name, app_type, app_version, device_brand, environment, description, created_at FROM script_versions LIMIT 5");
    $stmt->execute();
    $scripts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $result['sample_scripts'] = $scripts;
    
    echo json_encode(array(
        'success' => true,
        'message' => '数据库测试成功',
        'data' => $result
    ));
    
} catch (Exception $e) {
    echo json_encode(array(
        'success' => false,
        'message' => '错误: ' . $e->getMessage(),
        'data' => $result
    ));
}
?> 