/**
 * 性能监控系统
 * 监控页面性能、用户体验指标和系统资源使用情况
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            navigation: {},
            resources: [],
            userInteraction: [],
            memory: [],
            errors: []
        };
        
        this.observers = new Map();
        this.startTime = Date.now();
        this.isMonitoring = false;
        
        console.log('📊 PerformanceMonitor initialized');
        this.initialize();
    }

    /**
     * 初始化性能监控
     */
    initialize() {
        if (this.isMonitoring) return;
        
        this.setupNavigationObserver();
        this.setupResourceObserver();
        this.setupUserInteractionObserver();
        this.setupMemoryMonitor();
        this.setupErrorMonitor();
        this.setupLongTaskObserver();
        
        this.isMonitoring = true;
        console.log('📈 性能监控已启动');
    }

    /**
     * 导航性能监控
     */
    setupNavigationObserver() {
        if (!window.performance || !window.performance.getEntriesByType) return;
        
        // 监控页面加载性能
        window.addEventListener('load', () => {
            setTimeout(() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                if (navigation) {
                    this.metrics.navigation = {
                        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                        domReady: navigation.domContentLoadedEventEnd - navigation.fetchStart,
                        pageLoad: navigation.loadEventEnd - navigation.fetchStart,
                        dns: navigation.domainLookupEnd - navigation.domainLookupStart,
                        tcp: navigation.connectEnd - navigation.connectStart,
                        request: navigation.responseStart - navigation.requestStart,
                        response: navigation.responseEnd - navigation.responseStart,
                        timestamp: Date.now()
                    };
                    
                    this.reportMetric('navigation', this.metrics.navigation);
                }
            }, 0);
        });
    }

    /**
     * 资源加载监控
     */
    setupResourceObserver() {
        if (!window.PerformanceObserver) return;
        
        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                
                entries.forEach(entry => {
                    if (entry.entryType === 'resource') {
                        const resource = {
                            name: entry.name,
                            type: this.getResourceType(entry.name),
                            duration: entry.duration,
                            size: entry.transferSize || 0,
                            cached: entry.transferSize === 0 && entry.decodedBodySize > 0,
                            timestamp: Date.now()
                        };
                        
                        this.metrics.resources.push(resource);
                        this.reportMetric('resource', resource);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['resource'] });
            this.observers.set('resource', observer);
        } catch (error) {
            console.warn('资源监控设置失败:', error);
        }
    }

    /**
     * 用户交互监控
     */
    setupUserInteractionObserver() {
        const interactionEvents = ['click', 'scroll', 'keypress', 'touchstart'];
        
        interactionEvents.forEach(eventType => {
            document.addEventListener(eventType, (event) => {
                const interaction = {
                    type: eventType,
                    target: this.getElementSelector(event.target),
                    timestamp: Date.now(),
                    x: event.clientX || 0,
                    y: event.clientY || 0
                };
                
                this.metrics.userInteraction.push(interaction);
                
                // 限制交互记录数量
                if (this.metrics.userInteraction.length > 100) {
                    this.metrics.userInteraction.shift();
                }
                
                this.reportMetric('interaction', interaction);
            }, { passive: true });
        });
    }

    /**
     * 内存使用监控
     */
    setupMemoryMonitor() {
        if (!window.performance || !window.performance.memory) return;
        
        setInterval(() => {
            const memory = {
                used: window.performance.memory.usedJSHeapSize,
                total: window.performance.memory.totalJSHeapSize,
                limit: window.performance.memory.jsHeapSizeLimit,
                timestamp: Date.now()
            };
            
            this.metrics.memory.push(memory);
            
            // 限制内存记录数量
            if (this.metrics.memory.length > 60) { // 保留最近60条记录
                this.metrics.memory.shift();
            }
            
            // 检查内存使用率
            const usagePercent = (memory.used / memory.limit) * 100;
            if (usagePercent > 80) {
                this.reportAlert('high-memory-usage', {
                    usage: usagePercent,
                    used: memory.used,
                    limit: memory.limit
                });
            }
            
            this.reportMetric('memory', memory);
        }, 5000); // 每5秒检查一次
    }

    /**
     * 错误监控
     */
    setupErrorMonitor() {
        // JavaScript错误监控
        window.addEventListener('error', (event) => {
            const error = {
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error ? event.error.stack : null,
                timestamp: Date.now()
            };
            
            this.metrics.errors.push(error);
            this.reportMetric('error', error);
        });
        
        // Promise rejection监控
        window.addEventListener('unhandledrejection', (event) => {
            const error = {
                type: 'promise-rejection',
                message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
                stack: event.reason && event.reason.stack ? event.reason.stack : null,
                timestamp: Date.now()
            };
            
            this.metrics.errors.push(error);
            this.reportMetric('error', error);
        });
    }

    /**
     * 长任务监控
     */
    setupLongTaskObserver() {
        if (!window.PerformanceObserver) return;
        
        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                
                entries.forEach(entry => {
                    if (entry.entryType === 'longtask') {
                        const longTask = {
                            duration: entry.duration,
                            startTime: entry.startTime,
                            timestamp: Date.now()
                        };
                        
                        this.reportAlert('long-task', longTask);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['longtask'] });
            this.observers.set('longtask', observer);
        } catch (error) {
            console.warn('长任务监控设置失败:', error);
        }
    }

    /**
     * 获取资源类型
     */
    getResourceType(url) {
        const extension = url.split('.').pop().toLowerCase();
        
        if (['js', 'mjs'].includes(extension)) return 'script';
        if (['css'].includes(extension)) return 'stylesheet';
        if (['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp'].includes(extension)) return 'image';
        if (['woff', 'woff2', 'ttf', 'otf'].includes(extension)) return 'font';
        if (['json', 'xml'].includes(extension)) return 'xhr';
        
        return 'other';
    }

    /**
     * 获取元素选择器
     */
    getElementSelector(element) {
        if (!element) return 'unknown';
        
        if (element.id) return `#${element.id}`;
        if (element.className) return `.${element.className.split(' ')[0]}`;
        
        return element.tagName.toLowerCase();
    }

    /**
     * 上报性能指标
     */
    reportMetric(type, data) {
        // 触发事件，供其他模块监听
        if (window.eventBus) {
            window.eventBus.emit('performance:metric', { type, data });
        }
        
        // 可以在这里添加上报到服务器的逻辑
        if (this.shouldReport(type)) {
            this.sendToServer('metric', { type, data });
        }
    }

    /**
     * 上报性能警报
     */
    reportAlert(type, data) {
        console.warn(`性能警报: ${type}`, data);
        
        if (window.eventBus) {
            window.eventBus.emit('performance:alert', { type, data });
        }
        
        this.sendToServer('alert', { type, data });
    }

    /**
     * 判断是否需要上报
     */
    shouldReport(type) {
        // 可以根据配置决定哪些指标需要上报
        const reportTypes = ['navigation', 'error'];
        return reportTypes.includes(type);
    }

    /**
     * 发送数据到服务器
     */
    sendToServer(category, data) {
        // 这里实现实际的数据上报逻辑
        // 可以使用 fetch 或其他方式发送到后端
        
        if (window.fetch && window.location.hostname !== 'localhost') {
            fetch('/api/performance/report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    category,
                    data,
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    timestamp: Date.now()
                })
            }).catch(error => {
                console.warn('性能数据上报失败:', error);
            });
        }
    }

    /**
     * 获取性能摘要
     */
    getPerformanceSummary() {
        const now = Date.now();
        const runTime = now - this.startTime;
        
        return {
            runtime: runTime,
            navigation: this.metrics.navigation,
            resourceCount: this.metrics.resources.length,
            interactionCount: this.metrics.userInteraction.length,
            errorCount: this.metrics.errors.length,
            currentMemory: this.getCurrentMemory(),
            averageResourceLoadTime: this.getAverageResourceLoadTime(),
            slowResources: this.getSlowResources(),
            recentErrors: this.metrics.errors.slice(-5)
        };
    }

    /**
     * 获取当前内存使用情况
     */
    getCurrentMemory() {
        if (this.metrics.memory.length === 0) return null;
        
        return this.metrics.memory[this.metrics.memory.length - 1];
    }

    /**
     * 获取平均资源加载时间
     */
    getAverageResourceLoadTime() {
        if (this.metrics.resources.length === 0) return 0;
        
        const totalTime = this.metrics.resources.reduce((sum, resource) => sum + resource.duration, 0);
        return totalTime / this.metrics.resources.length;
    }

    /**
     * 获取加载缓慢的资源
     */
    getSlowResources(threshold = 1000) {
        return this.metrics.resources
            .filter(resource => resource.duration > threshold)
            .sort((a, b) => b.duration - a.duration)
            .slice(0, 10);
    }

    /**
     * 清理旧数据
     */
    cleanup() {
        const maxAge = 30 * 60 * 1000; // 30分钟
        const cutoff = Date.now() - maxAge;
        
        // 清理资源记录
        this.metrics.resources = this.metrics.resources.filter(
            resource => resource.timestamp > cutoff
        );
        
        // 清理交互记录
        this.metrics.userInteraction = this.metrics.userInteraction.filter(
            interaction => interaction.timestamp > cutoff
        );
        
        // 清理错误记录（保留更长时间）
        const errorCutoff = Date.now() - (60 * 60 * 1000); // 1小时
        this.metrics.errors = this.metrics.errors.filter(
            error => error.timestamp > errorCutoff
        );
    }

    /**
     * 停止监控
     */
    stop() {
        // 断开所有观察者
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        this.observers.clear();
        
        this.isMonitoring = false;
        console.log('📊 性能监控已停止');
    }

    /**
     * 获取调试信息
     */
    getDebugInfo() {
        return {
            isMonitoring: this.isMonitoring,
            startTime: this.startTime,
            observerCount: this.observers.size,
            metrics: {
                resources: this.metrics.resources.length,
                interactions: this.metrics.userInteraction.length,
                errors: this.metrics.errors.length,
                memoryRecords: this.metrics.memory.length
            },
            summary: this.getPerformanceSummary()
        };
    }
}

// 创建全局性能监控实例
if (typeof window !== 'undefined') {
    window.PerformanceMonitor = PerformanceMonitor;
    window.performanceMonitor = new PerformanceMonitor();
    
    // 定期清理数据
    setInterval(() => {
        window.performanceMonitor.cleanup();
    }, 10 * 60 * 1000); // 每10分钟清理一次
    
    console.log('🎯 性能监控系统已就绪');
} 