/**
 * 商户UI配置
 * 专门为merchant租户类型定制的界面配置
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-12-28
 */

class MerchantUI {
    constructor() {
        this.config = {
            theme: {
                primaryColor: '#52c41a',
                sidebarStyle: 'light',
                brandName: '商户管理后台'
            },
            layout: {
                sidebarCollapsed: false,
                headerFixed: true,
                footerVisible: true
            },
            features: {
                darkMode: false,
                notifications: true,
                quickActions: true,
                searchGlobal: false,
                paymentTools: true
            }
        };
    }

    init() {
        console.log('🎨 初始化商户UI配置...');
        this.applyTheme();
        this.setupLayout();
        this.initializeComponents();
        console.log('✅ 商户UI初始化完成');
    }

    applyTheme() {
        const theme = this.config.theme;
        document.documentElement.style.setProperty('--primary-color', theme.primaryColor);
        document.documentElement.style.setProperty('--sidebar-style', theme.sidebarStyle);
        if (theme.brandName) {
            document.title = theme.brandName;
        }
    }

    setupLayout() {
        const layout = this.config.layout;
        if (layout.sidebarCollapsed) {
            document.body.classList.add('sidebar-collapsed');
        }
        if (layout.headerFixed) {
            document.body.classList.add('header-fixed');
        }
        if (!layout.footerVisible) {
            document.body.classList.add('footer-hidden');
        }
    }

    initializeComponents() {
        if (this.config.features.notifications) {
            this.initNotifications();
        }
        if (this.config.features.quickActions) {
            this.initQuickActions();
        }
        if (this.config.features.paymentTools) {
            this.initPaymentTools();
        }
    }

    initNotifications() {
        console.log('📢 商户通知已初始化');
    }

    initQuickActions() {
        console.log('⚡ 商户快速操作已初始化');
    }

    initPaymentTools() {
        console.log('💳 支付工具面板已初始化');
    }

    getConfig() {
        return this.config;
    }

    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.applyTheme();
        this.setupLayout();
    }
}

const merchantUI = new MerchantUI();
window.MerchantUI = MerchantUI;
window.merchantUI = merchantUI;

if (typeof module !== 'undefined' && module.exports) {
    module.exports = MerchantUI;
}

console.log('✅ 商户UI配置已加载'); 