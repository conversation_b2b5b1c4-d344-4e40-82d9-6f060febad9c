<?php
require_once dirname(__FILE__) . '/../core/TenantManager.php';

/**
 * 域名管理工具
 * 用于批量创建和管理租户域名
 */
class DomainManager {
    private $tenantManager;
    private $db;
    
    public function __construct() {
        $this->tenantManager = new TenantManager();
        $this->db = Database::getInstance();
    }
    
    /**
     * 为所有码商创建子域名
     */
    public function createProviderSubdomains($baseDomain = 'paypal-admin.com') {
        $providers = $this->db->fetchAll(
            "SELECT pp.id, pp.provider_code, pp.user_id 
             FROM payment_providers pp 
             WHERE pp.status = 'approved'"
        );
        
        foreach ($providers as $provider) {
            $subdomain = 'provider' . $provider['id'] . '.' . $baseDomain;
            
            try {
                // 检查是否已存在
                $existing = $this->db->fetch(
                    "SELECT id FROM tenant_domains WHERE tenant_type = 'provider' AND tenant_id = ?",
                    [$provider['id']]
                );
                
                if (!$existing) {
                    $domainId = $this->tenantManager->createTenantDomain(
                        'provider',
                        $provider['id'],
                        $subdomain,
                        'subdomain',
                        [
                            'primary_color' => '#1890ff',
                            'sidebar_style' => 'light'
                        ]
                    );
                    
                    echo "创建码商域名: {$subdomain} (ID: {$domainId})\n";
                } else {
                    echo "码商域名已存在: {$subdomain}\n";
                }
                
            } catch (Exception $e) {
                echo "创建码商域名失败 {$subdomain}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    /**
     * 为所有商户创建子域名
     */
    public function createMerchantSubdomains($baseDomain = 'paypal-admin.com') {
        $merchants = $this->db->fetchAll(
            "SELECT m.id, m.merchant_code, m.user_id 
             FROM merchants m 
             WHERE m.status = 'approved'"
        );
        
        foreach ($merchants as $merchant) {
            $subdomain = 'merchant' . $merchant['id'] . '.' . $baseDomain;
            
            try {
                // 检查是否已存在
                $existing = $this->db->fetch(
                    "SELECT id FROM tenant_domains WHERE tenant_type = 'merchant' AND tenant_id = ?",
                    [$merchant['id']]
                );
                
                if (!$existing) {
                    $domainId = $this->tenantManager->createTenantDomain(
                        'merchant',
                        $merchant['id'],
                        $subdomain,
                        'subdomain',
                        [
                            'primary_color' => '#722ed1',
                            'sidebar_style' => 'dark'
                        ]
                    );
                    
                    echo "创建商户域名: {$subdomain} (ID: {$domainId})\n";
                } else {
                    echo "商户域名已存在: {$subdomain}\n";
                }
                
            } catch (Exception $e) {
                echo "创建商户域名失败 {$subdomain}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    /**
     * 生成简化的Nginx配置（通配符域名方案）
     */
    public function generateNginxConfig($outputFile = null) {
        $config = "# Multi-tenant Nginx Configuration (Wildcard Domain)\n";
        $config .= "# Generated on " . date('Y-m-d H:i:s') . "\n";
        $config .= "# 通配符域名方案 - 所有子域名和自定义域名统一处理\n\n";
        
        $config .= $this->generateWildcardServerBlock();
        
        if ($outputFile) {
            file_put_contents($outputFile, $config);
            echo "Nginx配置已生成: {$outputFile}\n";
        } else {
            echo $config;
        }
        
        return $config;
    }
    
    /**
     * 生成通配符服务器块配置
     */
    private function generateWildcardServerBlock() {
        $documentRoot = '/var/www/html/tenant-admin';
        $apiRoot = '/var/www/html/backend';
        
        $config = "# 通配符子域名配置\n";
        $config .= "server {\n";
        $config .= "    listen 80;\n";
        $config .= "    listen 443 ssl http2;\n";
        $config .= "    server_name *.paypal-admin.com;\n";
        $config .= "    root {$documentRoot};\n";
        $config .= "    index index.html;\n\n";
        
        $config .= "    # SSL 配置（通配符证书）\n";
        $config .= "    ssl_certificate /etc/ssl/certs/wildcard.paypal-admin.com.crt;\n";
        $config .= "    ssl_certificate_key /etc/ssl/private/wildcard.paypal-admin.com.key;\n";
        $config .= "    ssl_protocols TLSv1.2 TLSv1.3;\n";
        $config .= "    ssl_ciphers HIGH:!aNULL:!MD5;\n\n";
        
        $config .= "    # 前端文件\n";
        $config .= "    location / {\n";
        $config .= "        try_files \$uri \$uri/ /index.html;\n";
        $config .= "        # 租户信息由 PHP 层处理，无需在 Nginx 层添加\n";
        $config .= "    }\n\n";
        
        $config .= "    # API 请求\n";
        $config .= "    location /backend/ {\n";
        $config .= "        alias {$apiRoot}/;\n";
        $config .= "        location ~ \\.php\$ {\n";
        $config .= "            fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;\n";
        $config .= "            fastcgi_index index.php;\n";
        $config .= "            fastcgi_param SCRIPT_FILENAME \$request_filename;\n";
        $config .= "            include fastcgi_params;\n";
        $config .= "        }\n";
        $config .= "    }\n\n";
        
        $config .= "    # 安全头\n";
        $config .= "    add_header X-Frame-Options \"SAMEORIGIN\";\n";
        $config .= "    add_header X-Content-Type-Options \"nosniff\";\n";
        $config .= "    add_header X-XSS-Protection \"1; mode=block\";\n\n";
        
        $config .= "    # 访问日志\n";
        $config .= "    access_log /var/log/nginx/tenant_wildcard_access.log;\n";
        $config .= "    error_log /var/log/nginx/tenant_wildcard_error.log;\n";
        $config .= "}\n\n";
        
        // 自定义域名配置
        $config .= "# 自定义域名配置（捕获所有其他域名）\n";
        $config .= "server {\n";
        $config .= "    listen 80 default_server;\n";
        $config .= "    listen 443 ssl http2 default_server;\n";
        $config .= "    server_name _;\n";
        $config .= "    root {$documentRoot};\n";
        $config .= "    index index.html;\n\n";
        
        $config .= "    # 自签名证书（用于未配置 SSL 的自定义域名）\n";
        $config .= "    ssl_certificate /etc/ssl/certs/default.crt;\n";
        $config .= "    ssl_certificate_key /etc/ssl/private/default.key;\n\n";
        
        $config .= "    # 前端文件\n";
        $config .= "    location / {\n";
        $config .= "        try_files \$uri \$uri/ /index.html;\n";
        $config .= "    }\n\n";
        
        $config .= "    # API 请求\n";
        $config .= "    location /backend/ {\n";
        $config .= "        alias {$apiRoot}/;\n";
        $config .= "        location ~ \\.php\$ {\n";
        $config .= "            fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;\n";
        $config .= "            fastcgi_index index.php;\n";
        $config .= "            fastcgi_param SCRIPT_FILENAME \$request_filename;\n";
        $config .= "            include fastcgi_params;\n";
        $config .= "        }\n";
        $config .= "    }\n\n";
        
        $config .= "    # 访问日志\n";
        $config .= "    access_log /var/log/nginx/tenant_custom_access.log;\n";
        $config .= "    error_log /var/log/nginx/tenant_custom_error.log;\n";
        $config .= "}\n\n";
        
        return $config;
    }
    
    /**
     * 验证域名DNS配置
     */
    public function validateDomainDNS($domain) {
        $results = [];
        
        // A记录检查
        $aRecords = dns_get_record($domain, DNS_A);
        $results['a_records'] = $aRecords;
        
        // CNAME记录检查
        $cnameRecords = dns_get_record($domain, DNS_CNAME);
        $results['cname_records'] = $cnameRecords;
        
        // MX记录检查（可选）
        $mxRecords = dns_get_record($domain, DNS_MX);
        $results['mx_records'] = $mxRecords;
        
        return $results;
    }
    
    /**
     * 批量验证所有域名
     */
    public function validateAllDomains() {
        $domains = $this->db->fetchAll(
            "SELECT domain_name, tenant_type, tenant_id FROM tenant_domains WHERE status = 'active'"
        );
        
        foreach ($domains as $domain) {
            echo "验证域名: {$domain['domain_name']}\n";
            
            try {
                $dnsResults = $this->validateDomainDNS($domain['domain_name']);
                
                if (!empty($dnsResults['a_records']) || !empty($dnsResults['cname_records'])) {
                    echo "  ✓ DNS配置正确\n";
                } else {
                    echo "  ✗ DNS配置缺失\n";
                }
                
            } catch (Exception $e) {
                echo "  ✗ DNS验证失败: " . $e->getMessage() . "\n";
            }
        }
    }
    
    /**
     * 生成域名列表报告
     */
    public function generateDomainReport() {
        $domains = $this->db->fetchAll(
            "SELECT td.*, 
                    CASE 
                        WHEN td.tenant_type = 'provider' THEN CONCAT('码商-', COALESCE(pp.provider_code, pp.id))
                        WHEN td.tenant_type = 'merchant' THEN CONCAT('商户-', COALESCE(m.merchant_code, m.id))
                    END as company_name,
                    CASE 
                        WHEN td.tenant_type = 'provider' THEN pp.provider_code
                        WHEN td.tenant_type = 'merchant' THEN m.merchant_code
                    END as contact_name
             FROM tenant_domains td
             LEFT JOIN payment_providers pp ON td.tenant_type = 'provider' AND td.tenant_id = pp.id
             LEFT JOIN merchants m ON td.tenant_type = 'merchant' AND td.tenant_id = m.id
             ORDER BY td.tenant_type, td.tenant_id, td.created_at"
        );
        
        echo "多租户域名配置报告\n";
        echo "生成时间: " . date('Y-m-d H:i:s') . "\n";
        echo str_repeat('=', 80) . "\n\n";
        
        $stats = [
            'total' => count($domains),
            'active' => 0,
            'inactive' => 0,
            'provider' => 0,
            'merchant' => 0,
            'subdomain' => 0,
            'custom' => 0
        ];
        
        foreach ($domains as $domain) {
            $stats[$domain['status']]++;
            $stats[$domain['tenant_type']]++;
            $stats[$domain['domain_type']]++;
            
            echo "域名: {$domain['domain_name']}\n";
            echo "  租户类型: {$domain['tenant_type']}\n";
            echo "  租户ID: {$domain['tenant_id']}\n";
            echo "  公司名称: {$domain['company_name']}\n";
            echo "  联系人: {$domain['contact_name']}\n";
            echo "  品牌名称: {$domain['brand_name']}\n";
            echo "  域名类型: {$domain['domain_type']}\n";
            echo "  状态: {$domain['status']}\n";
            echo "  SSL启用: " . ($domain['ssl_enabled'] ? '是' : '否') . "\n";
            echo "  创建时间: {$domain['created_at']}\n";
            echo str_repeat('-', 50) . "\n";
        }
        
        echo "\n统计信息:\n";
        echo "  总域名数: {$stats['total']}\n";
        echo "  活跃域名: {$stats['active']}\n";
        echo "  停用域名: {$stats['inactive']}\n";
        echo "  码商域名: {$stats['provider']}\n";
        echo "  商户域名: {$stats['merchant']}\n";
        echo "  子域名: {$stats['subdomain']}\n";
        echo "  自定义域名: {$stats['custom']}\n";
    }
    
    /**
     * 清理测试域名
     */
    public function cleanupTestDomains() {
        $testPatterns = ['test', 'demo', 'example', 'localhost'];
        
        foreach ($testPatterns as $pattern) {
            $domains = $this->db->fetchAll(
                "SELECT id, domain_name FROM tenant_domains WHERE domain_name LIKE ?",
                ["%{$pattern}%"]
            );
            
            foreach ($domains as $domain) {
                echo "删除测试域名: {$domain['domain_name']}\n";
                $this->db->execute(
                    "DELETE FROM tenant_domains WHERE id = ?",
                    [$domain['id']]
                );
            }
        }
    }
}

// 命令行工具
if (php_sapi_name() === 'cli') {
    $manager = new DomainManager();
    
    $command = isset($argv[1]) ? $argv[1] : 'help';
    
    switch ($command) {
        case 'create-providers':
            $baseDomain = isset($argv[2]) ? $argv[2] : 'paypal-admin.com';
            $manager->createProviderSubdomains($baseDomain);
            break;
            
        case 'create-merchants':
            $baseDomain = isset($argv[2]) ? $argv[2] : 'paypal-admin.com';
            $manager->createMerchantSubdomains($baseDomain);
            break;
            
        case 'generate-nginx':
            $outputFile = isset($argv[2]) ? $argv[2] : null;
            $manager->generateNginxConfig($outputFile);
            break;
            
        case 'validate-dns':
            $manager->validateAllDomains();
            break;
            
        case 'report':
            $manager->generateDomainReport();
            break;
            
        case 'cleanup':
            $manager->cleanupTestDomains();
            break;
            
        default:
            echo "多租户域名管理工具\n\n";
            echo "用法: php DomainManager.php <command> [options]\n\n";
            echo "命令:\n";
            echo "  create-providers [domain]  - 为所有码商创建子域名\n";
            echo "  create-merchants [domain]  - 为所有商户创建子域名\n";
            echo "  generate-nginx [file]      - 生成Nginx配置\n";
            echo "  validate-dns               - 验证所有域名DNS配置\n";
            echo "  report                     - 生成域名配置报告\n";
            echo "  cleanup                    - 清理测试域名\n";
            echo "  help                       - 显示此帮助信息\n";
            break;
    }
}
?> 