/**
 * API路由管理模块
 * 用于查看和管理系统的API路由配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

class ApiRouterModule {
    constructor() {
        this.apiClient = null;
        this.utils = null;
        this.authManager = null;
        this.tenantInfo = null;
        
        this.routes = [];
        this.filteredRoutes = [];
        this.currentFilter = 'all';
        this.searchQuery = '';
        
        console.log('🛣️ ApiRouterModule initialized');
    }

    /**
     * 初始化模块
     */
    async init(dependencies) {
        this.apiClient = dependencies.apiClient;
        this.utils = dependencies.utils;
        this.authManager = dependencies.authManager;
        this.tenantInfo = dependencies.tenantInfo;
        
        console.log('✅ API路由管理模块初始化完成');
    }

    /**
     * 渲染API路由管理页面
     */
    async render(container, params = {}) {
        try {
            container.innerHTML = `
                <div class="api-router-module">
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2><i class="bi bi-diagram-3 me-2"></i>API路由管理</h2>
                                <p class="text-muted mb-0">查看和管理系统的API路由配置</p>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-outline-info" onclick="apiRouterModule.refreshRoutes()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>刷新
                                </button>
                                <button class="btn btn-outline-success" onclick="apiRouterModule.exportRoutes()">
                                    <i class="bi bi-download me-2"></i>导出配置
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 路由统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="stat-card">
                                <div class="stat-icon bg-primary">
                                    <i class="bi bi-diagram-3"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="totalRoutesCount">0</div>
                                    <div class="stat-label">总路由数</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="stat-card">
                                <div class="stat-icon bg-success">
                                    <i class="bi bi-shield-check"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="publicRoutesCount">0</div>
                                    <div class="stat-label">公开路由</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="stat-card">
                                <div class="stat-icon bg-warning">
                                    <i class="bi bi-person-check"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="authRoutesCount">0</div>
                                    <div class="stat-label">需认证</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="stat-card">
                                <div class="stat-icon bg-info">
                                    <i class="bi bi-filter"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="tenantFilterCount">0</div>
                                    <div class="stat-label">租户过滤</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="stat-card">
                                <div class="stat-icon bg-danger">
                                    <i class="bi bi-speedometer2"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="rateLimitCount">0</div>
                                    <div class="stat-label">限流路由</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="stat-card">
                                <div class="stat-icon bg-dark">
                                    <i class="bi bi-clock-history"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="cachedRoutesCount">0</div>
                                    <div class="stat-label">缓存路由</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选和搜索 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-funnel me-2"></i>筛选和搜索
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">权限类型</label>
                                    <select class="form-select" id="filterPermission" onchange="apiRouterModule.applyFilter()">
                                        <option value="all">全部权限</option>
                                        <option value="public">公开访问</option>
                                        <option value="authenticated">需认证</option>
                                        <option value="system_admin">系统管理员</option>
                                        <option value="platform_admin">平台管理员</option>
                                        <option value="provider">码商</option>
                                        <option value="merchant">商户</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">HTTP方法</label>
                                    <select class="form-select" id="filterMethod" onchange="apiRouterModule.applyFilter()">
                                        <option value="all">全部方法</option>
                                        <option value="GET">GET</option>
                                        <option value="POST">POST</option>
                                        <option value="PUT">PUT</option>
                                        <option value="DELETE">DELETE</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">特性筛选</label>
                                    <select class="form-select" id="filterFeature" onchange="apiRouterModule.applyFilter()">
                                        <option value="all">全部特性</option>
                                        <option value="tenant_filter">租户过滤</option>
                                        <option value="rate_limit">限流</option>
                                        <option value="cache">缓存</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">搜索路由</label>
                                    <input type="text" class="form-control" id="searchInput" 
                                           placeholder="搜索路由名称或描述..." 
                                           onkeyup="apiRouterModule.searchRoutes(this.value)">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 路由列表 -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="bi bi-list me-2"></i>API路由列表
                                    <span class="badge bg-secondary ms-2" id="routeCountBadge">0</span>
                                </h6>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-secondary" onclick="apiRouterModule.toggleView('table')" 
                                            id="tableViewBtn" title="表格视图">
                                        <i class="bi bi-table"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="apiRouterModule.toggleView('cards')" 
                                            id="cardsViewBtn" title="卡片视图">
                                        <i class="bi bi-grid-3x3-gap"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 表格视图 -->
                            <div id="tableView" class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>路由名称</th>
                                            <th>HTTP方法</th>
                                            <th>路径</th>
                                            <th>权限</th>
                                            <th>特性</th>
                                            <th>描述</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="routesTableBody">
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                <div class="mt-2">正在加载路由配置...</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 卡片视图 -->
                            <div id="cardsView" class="row g-3" style="display: none;">
                                <!-- 卡片内容将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 路由详情模态框 -->
                <div class="modal fade" id="routeDetailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">路由详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body" id="routeDetailContent">
                                <!-- 路由详情内容 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API统计模态框 -->
                <div class="modal fade" id="apiStatsModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">API调用统计</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body" id="apiStatsContent">
                                <!-- API统计内容 -->
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 初始化页面
            await this.initializePage();
            
        } catch (error) {
            console.error('❌ API路由管理模块渲染失败:', error);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h4>加载失败</h4>
                    <p>API路由管理模块加载失败: ${error.message}</p>
                </div>
            `;
        }
    }

    /**
     * 初始化页面
     */
    async initializePage() {
        try {
            // 加载路由配置
            await this.loadRoutes();
            
            // 默认表格视图
            this.toggleView('table');
            
        } catch (error) {
            console.error('页面初始化失败:', error);
        }
    }

    /**
     * 加载路由配置
     */
    async loadRoutes() {
        try {
            // 这里应该调用后端API获取路由配置
            // 暂时使用模拟数据
            const response = await this.apiClient.get('/api/admin.php?action=get_api_routes');
            
            if (response.success) {
                this.routes = response.data.routes || [];
                this.updateStatistics(response.data.stats || {});
            } else {
                // 使用模拟数据
                this.routes = this.getMockRoutes();
                this.updateStatistics(this.calculateStats());
            }

            this.filteredRoutes = [...this.routes];
            this.renderRoutes();
            
        } catch (error) {
            console.error('加载路由配置失败:', error);
            // 使用模拟数据
            this.routes = this.getMockRoutes();
            this.filteredRoutes = [...this.routes];
            this.updateStatistics(this.calculateStats());
            this.renderRoutes();
        }
    }

    /**
     * 获取模拟路由数据
     */
    getMockRoutes() {
        return [
            {
                name: 'auth.login',
                method: 'POST',
                path: '/admin/auth.php',
                permissions: ['public'],
                description: '用户登录',
                rate_limit: 60,
                tenant_filter: false
            },
            {
                name: 'merchant.list',
                method: 'GET',
                path: '/admin/merchants.php',
                permissions: ['platform_admin', 'system_admin'],
                description: '获取商户列表',
                tenant_filter: true
            },
            {
                name: 'provider.list',
                method: 'GET',
                path: '/admin/providers.php',
                permissions: ['platform_admin', 'system_admin'],
                description: '获取码商列表',
                tenant_filter: true
            },
            {
                name: 'alipay_bills.list',
                method: 'GET',
                path: '/alipay_bills.php',
                permissions: ['provider'],
                description: '获取支付宝账单列表',
                tenant_filter: true
            },
            {
                name: 'dashboard.stats',
                method: 'GET',
                path: '/admin/dashboard.php',
                permissions: ['authenticated'],
                description: '获取仪表板统计数据'
            }
        ];
    }

    /**
     * 计算统计数据
     */
    calculateStats() {
        const stats = {
            total: this.routes.length,
            public: 0,
            authenticated: 0,
            tenant_filtered: 0,
            rate_limited: 0,
            cached: 0
        };

        this.routes.forEach(route => {
            if (route.permissions.includes('public')) {
                stats.public++;
            }
            if (route.permissions.includes('authenticated')) {
                stats.authenticated++;
            }
            if (route.tenant_filter) {
                stats.tenant_filtered++;
            }
            if (route.rate_limit) {
                stats.rate_limited++;
            }
            if (route.cache_ttl) {
                stats.cached++;
            }
        });

        return stats;
    }

    /**
     * 更新统计数据
     */
    updateStatistics(stats) {
        document.getElementById('totalRoutesCount').textContent = stats.total || 0;
        document.getElementById('publicRoutesCount').textContent = stats.public || 0;
        document.getElementById('authRoutesCount').textContent = stats.authenticated || 0;
        document.getElementById('tenantFilterCount').textContent = stats.tenant_filtered || 0;
        document.getElementById('rateLimitCount').textContent = stats.rate_limited || 0;
        document.getElementById('cachedRoutesCount').textContent = stats.cached || 0;
    }

    /**
     * 渲染路由列表
     */
    renderRoutes() {
        const tableBody = document.getElementById('routesTableBody');
        const cardsView = document.getElementById('cardsView');
        const routeCountBadge = document.getElementById('routeCountBadge');
        
        routeCountBadge.textContent = this.filteredRoutes.length;

        if (this.filteredRoutes.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="bi bi-inbox text-muted" style="font-size: 2rem;"></i>
                        <div class="mt-2 text-muted">暂无匹配的路由</div>
                    </td>
                </tr>
            `;
            cardsView.innerHTML = `
                <div class="col-12 text-center py-4">
                    <i class="bi bi-inbox text-muted" style="font-size: 2rem;"></i>
                    <div class="mt-2 text-muted">暂无匹配的路由</div>
                </div>
            `;
            return;
        }

        // 渲染表格
        tableBody.innerHTML = this.filteredRoutes.map(route => `
            <tr>
                <td>
                    <div class="fw-medium">${route.name}</div>
                    <small class="text-muted">${route.path}</small>
                </td>
                <td>
                    <span class="badge bg-${this.getMethodColor(route.method)}">${route.method}</span>
                </td>
                <td>
                    <code class="small">${route.path}</code>
                </td>
                <td>
                    ${route.permissions.map(perm => 
                        `<span class="badge bg-${this.getPermissionColor(perm)} me-1">${this.getPermissionText(perm)}</span>`
                    ).join('')}
                </td>
                <td>
                    ${route.tenant_filter ? '<span class="badge bg-info me-1">租户过滤</span>' : ''}
                    ${route.rate_limit ? '<span class="badge bg-warning me-1">限流</span>' : ''}
                    ${route.cache_ttl ? '<span class="badge bg-success me-1">缓存</span>' : ''}
                </td>
                <td>
                    <small class="text-muted">${route.description || '-'}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="apiRouterModule.viewRouteDetail('${route.name}')" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="apiRouterModule.testRoute('${route.name}')" title="测试路由">
                            <i class="bi bi-play-circle"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // 渲染卡片
        cardsView.innerHTML = this.filteredRoutes.map(route => `
            <div class="col-md-6 col-lg-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">${route.name}</h6>
                        <span class="badge bg-${this.getMethodColor(route.method)}">${route.method}</span>
                    </div>
                    <div class="card-body">
                        <p class="card-text small text-muted">${route.description || '无描述'}</p>
                        <div class="mb-2">
                            <strong>路径:</strong>
                            <br><code class="small">${route.path}</code>
                        </div>
                        <div class="mb-2">
                            <strong>权限:</strong><br>
                            ${route.permissions.map(perm => 
                                `<span class="badge bg-${this.getPermissionColor(perm)} me-1">${this.getPermissionText(perm)}</span>`
                            ).join('')}
                        </div>
                        <div class="mb-2">
                            <strong>特性:</strong><br>
                            ${route.tenant_filter ? '<span class="badge bg-info me-1">租户过滤</span>' : ''}
                            ${route.rate_limit ? '<span class="badge bg-warning me-1">限流</span>' : ''}
                            ${route.cache_ttl ? '<span class="badge bg-success me-1">缓存</span>' : ''}
                            ${!route.tenant_filter && !route.rate_limit && !route.cache_ttl ? '<span class="text-muted">无</span>' : ''}
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary btn-sm" onclick="apiRouterModule.viewRouteDetail('${route.name}')">
                                <i class="bi bi-eye me-1"></i>详情
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="apiRouterModule.testRoute('${route.name}')">
                                <i class="bi bi-play-circle me-1"></i>测试
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 切换视图模式
     */
    toggleView(viewType) {
        const tableView = document.getElementById('tableView');
        const cardsView = document.getElementById('cardsView');
        const tableBtn = document.getElementById('tableViewBtn');
        const cardsBtn = document.getElementById('cardsViewBtn');

        if (viewType === 'table') {
            tableView.style.display = 'block';
            cardsView.style.display = 'none';
            tableBtn.classList.add('active');
            cardsBtn.classList.remove('active');
        } else {
            tableView.style.display = 'none';
            cardsView.style.display = 'flex';
            tableBtn.classList.remove('active');
            cardsBtn.classList.add('active');
        }
    }

    /**
     * 应用筛选
     */
    applyFilter() {
        const permissionFilter = document.getElementById('filterPermission').value;
        const methodFilter = document.getElementById('filterMethod').value;
        const featureFilter = document.getElementById('filterFeature').value;

        this.filteredRoutes = this.routes.filter(route => {
            // 权限筛选
            if (permissionFilter !== 'all' && !route.permissions.includes(permissionFilter)) {
                return false;
            }

            // 方法筛选
            if (methodFilter !== 'all' && route.method !== methodFilter) {
                return false;
            }

            // 特性筛选
            if (featureFilter !== 'all') {
                switch (featureFilter) {
                    case 'tenant_filter':
                        if (!route.tenant_filter) return false;
                        break;
                    case 'rate_limit':
                        if (!route.rate_limit) return false;
                        break;
                    case 'cache':
                        if (!route.cache_ttl) return false;
                        break;
                }
            }

            // 搜索筛选
            if (this.searchQuery) {
                const query = this.searchQuery.toLowerCase();
                return route.name.toLowerCase().includes(query) || 
                       (route.description && route.description.toLowerCase().includes(query));
            }

            return true;
        });

        this.renderRoutes();
    }

    /**
     * 搜索路由
     */
    searchRoutes(query) {
        this.searchQuery = query;
        this.applyFilter();
    }

    /**
     * 查看路由详情
     */
    async viewRouteDetail(routeName) {
        const route = this.routes.find(r => r.name === routeName);
        if (!route) return;

        const modalContent = document.getElementById('routeDetailContent');
        
        modalContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>路由名称:</td><td><code>${route.name}</code></td></tr>
                        <tr><td>HTTP方法:</td><td><span class="badge bg-${this.getMethodColor(route.method)}">${route.method}</span></td></tr>
                        <tr><td>路径:</td><td><code>${route.path}</code></td></tr>
                        <tr><td>描述:</td><td>${route.description || '-'}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>权限配置</h6>
                    <table class="table table-sm">
                        <tr><td>权限列表:</td><td>${route.permissions.map(perm => 
                            `<span class="badge bg-${this.getPermissionColor(perm)} me-1">${this.getPermissionText(perm)}</span>`
                        ).join('')}</td></tr>
                        <tr><td>租户过滤:</td><td>${route.tenant_filter ? '✅ 启用' : '❌ 禁用'}</td></tr>
                        <tr><td>限流配置:</td><td>${route.rate_limit ? `${route.rate_limit}/分钟` : '❌ 无限制'}</td></tr>
                        <tr><td>缓存配置:</td><td>${route.cache_ttl ? `${route.cache_ttl}秒` : '❌ 无缓存'}</td></tr>
                    </table>
                </div>
            </div>
            <div class="mt-3">
                <h6>使用示例</h6>
                <pre class="bg-light p-3 rounded"><code>// 前端调用示例
const response = await apiClient.${route.method.toLowerCase()}('${route.path}');

// cURL示例
curl -X ${route.method} \\
  -H "Authorization: Bearer YOUR_TOKEN" \\
  -H "Content-Type: application/json" \\
  "${window.location.origin}${route.path}"</code></pre>
            </div>
        `;

        const modal = new bootstrap.Modal(document.getElementById('routeDetailModal'));
        modal.show();
    }

    /**
     * 测试路由
     */
    async testRoute(routeName) {
        const route = this.routes.find(r => r.name === routeName);
        if (!route) return;

        try {
            // 这里可以实现路由测试功能
            this.showSuccess(`路由 ${routeName} 测试功能开发中...`);
        } catch (error) {
            this.showError('测试失败: ' + error.message);
        }
    }

    /**
     * 刷新路由配置
     */
    async refreshRoutes() {
        await this.loadRoutes();
        this.showSuccess('路由配置已刷新');
    }

    /**
     * 导出路由配置
     */
    exportRoutes() {
        const data = {
            routes: this.routes,
            exported_at: new Date().toISOString(),
            total_count: this.routes.length
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `api-routes-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showSuccess('路由配置已导出');
    }

    /**
     * 工具方法
     */
    getMethodColor(method) {
        const colors = {
            'GET': 'success',
            'POST': 'primary',
            'PUT': 'warning',
            'DELETE': 'danger'
        };
        return colors[method] || 'secondary';
    }

    getPermissionColor(permission) {
        const colors = {
            'public': 'success',
            'authenticated': 'info',
            'system_admin': 'danger',
            'platform_admin': 'warning',
            'provider': 'primary',
            'merchant': 'secondary'
        };
        return colors[permission] || 'light';
    }

    getPermissionText(permission) {
        const texts = {
            'public': '公开',
            'authenticated': '认证',
            'system_admin': '系统管理员',
            'platform_admin': '平台管理员',
            'provider': '码商',
            'merchant': '商户'
        };
        return texts[permission] || permission;
    }

    showSuccess(message) {
        if (typeof window.showAlert === 'function') {
            window.showAlert(message, 'success');
        } else {
            console.log('Success:', message);
        }
    }

    showError(message) {
        if (typeof window.showAlert === 'function') {
            window.showAlert(message, 'error');
        } else {
            console.error('Error:', message);
        }
    }
}

// 导出模块
window.ApiRouterModule = ApiRouterModule;

// 创建全局实例
if (typeof window.apiRouterModule === 'undefined') {
    window.apiRouterModule = new ApiRouterModule();
}

console.log('🎯 ApiRouterModule 模块加载完成'); 