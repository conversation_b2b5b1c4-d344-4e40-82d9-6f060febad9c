<?php
/**
 * 财务管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

try {
    // 检查用户权限 - 系统管理员、平台管理员、商户、码商都可以查看财务数据
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin', 'merchant', 'provider'])) {
        $auth->sendForbidden('无权限访问财务管理');
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    // 检查是否是导出请求
    if ($action === 'export_financial') {
        handleExportFinancialData($auth);
        return;
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetFinancialData($auth);
            break;
        case 'POST':
            handleCreateFinancialRecord($auth);
            break;
        default:
            $auth->sendError('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    error_log("Financial API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 获取财务数据
function handleGetFinancialData($auth) {
    global $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('financial.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $type = isset($_GET['type']) ? $_GET['type'] : 'overview';
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : 'month';
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        global $db;
        $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", array($currentUser['id']));
        if (!$merchant) {
            $auth->sendError('商户信息不存在', 403);
            return;
        }
        $merchantId = $merchant['id'];
    } elseif ($currentUser['user_type'] === 'provider') {
        global $db;
        $provider = $db->fetch("SELECT id FROM payment_providers WHERE user_id = ?", array($currentUser['id']));
        if (!$provider) {
            $auth->sendError('码商信息不存在', 403);
            return;
        }
        $merchantId = 0; // 码商暂时查看所有数据，可根据业务需求调整
    }
    
    switch ($type) {
        case 'overview':
            $data = getFinancialOverview($merchantId, $dateRange);
            break;
        case 'transactions':
            $data = getFinancialTransactions($merchantId, $dateRange);
            break;
        case 'settlement':
            $data = getSettlementData($merchantId, $dateRange);
            break;
        case 'statistics':
            $data = getFinancialStatistics($merchantId, $dateRange);
            break;
        default:
            $auth->sendError('无效的财务数据类型', 400);
            return;
    }
    
    // 记录操作日志
    $auth->logUserAction('view_financial', 'financial', 0, "查看财务数据: {$type}");
    
    $auth->sendSuccess($data, '财务数据获取成功');
}

// 导出财务数据
function handleExportFinancialData($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('financial.export')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $type = isset($_GET['type']) ? $_GET['type'] : 'overview';
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : 'month';
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", array($currentUser['id']));
        if (!$merchant) {
            $auth->sendError('商户信息不存在', 403);
            return;
        }
        $merchantId = $merchant['id'];
    }
    
    $whereClause = '';
    $params = array();
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE t.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    try {
        // 获取要导出的数据
        if ($type === 'transactions') {
            $data = $db->fetchAll(
                "SELECT t.order_id, t.out_trade_no, m.merchant_code as merchant_name,
                        p.name as product_name, t.amount, t.service_fee,
                        t.status, t.created_at, t.paid_at, t.remark
                 FROM transactions t
                 LEFT JOIN merchants m ON t.merchant_id = m.id
                 LEFT JOIN products p ON t.product_id = p.id
                 $whereClause
                 ORDER BY t.created_at DESC",
                $params
            );
            
            $filename = 'financial_transactions_' . date('Y-m-d_H-i-s') . '.csv';
            $headers = array('订单号', '商户订单号', '商户名称', '产品名称', '金额', '手续费', '状态', '创建时间', '支付时间', '备注');
        } else {
            $data = array();
            $filename = 'financial_data_' . date('Y-m-d_H-i-s') . '.csv';
            $headers = array('数据类型', '值');
        }
        
        // 设置CSV下载头
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        // 输出CSV数据
        $output = fopen('php://output', 'w');
        fputcsv($output, $headers);
        
        foreach ($data as $row) {
            fputcsv($output, array_values($row));
        }
        
        fclose($output);
        
        // 记录操作日志
        $auth->logUserAction('export_financial', 'financial', 0, "导出财务数据: {$type}");
        
    } catch (Exception $e) {
        error_log("导出财务数据失败: " . $e->getMessage());
        $auth->sendError('导出失败: ' . $e->getMessage(), 500);
    }
}

// 获取财务概览
function getFinancialOverview($merchantId, $dateRange) {
    global $db;
    
    $whereClause = '';
    $params = array();
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    try {
        $overview = $db->fetch(
            "SELECT 
                COUNT(*) as total_transactions,
                COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_transactions,
                SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as total_amount,
                SUM(CASE WHEN status = 'paid' THEN service_fee ELSE 0 END) as total_fee,
                AVG(CASE WHEN status = 'paid' THEN amount ELSE NULL END) as avg_amount
             FROM transactions $whereClause",
            $params
        );
        
        return $overview;
    } catch (Exception $e) {
        error_log("获取财务概览失败: " . $e->getMessage());
        return array('error' => '获取财务概览失败');
    }
}

// 获取财务交易记录
function getFinancialTransactions($merchantId, $dateRange) {
    global $db;
    
    $whereClause = '';
    $params = array();
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE t.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    try {
        $transactions = $db->fetchAll(
            "SELECT t.*, m.merchant_code as merchant_name, p.name as product_name
             FROM transactions t
             LEFT JOIN merchants m ON t.merchant_id = m.id
             LEFT JOIN products p ON t.product_id = p.id
             $whereClause
             ORDER BY t.created_at DESC
             LIMIT 100",
            $params
        );
        
        return array('transactions' => $transactions);
    } catch (Exception $e) {
        error_log("获取财务交易记录失败: " . $e->getMessage());
        return array('error' => '获取财务交易记录失败');
    }
}

// 获取结算数据
function getSettlementData($merchantId, $dateRange) {
    global $db;
    
    $whereClause = '';
    $params = array();
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    try {
        $settlement = $db->fetch(
            "SELECT 
                SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as total_income,
                SUM(CASE WHEN status = 'paid' THEN service_fee ELSE 0 END) as total_fee,
                SUM(CASE WHEN status = 'paid' THEN amount - service_fee ELSE 0 END) as net_income
             FROM transactions $whereClause",
            $params
        );
        
        return $settlement;
    } catch (Exception $e) {
        error_log("获取结算数据失败: " . $e->getMessage());
        return array('error' => '获取结算数据失败');
    }
}

// 获取财务统计
function getFinancialStatistics($merchantId, $dateRange) {
    global $db;
    
    $whereClause = '';
    $params = array();
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    try {
        $statistics = $db->fetchAll(
            "SELECT 
                DATE(created_at) as date,
                COUNT(*) as transaction_count,
                SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as daily_amount
             FROM transactions $whereClause
             GROUP BY DATE(created_at)
             ORDER BY date DESC
             LIMIT 30",
            $params
        );
        
        return array('statistics' => $statistics);
    } catch (Exception $e) {
        error_log("获取财务统计失败: " . $e->getMessage());
        return array('error' => '获取财务统计失败');
    }
}

// 获取日期范围条件
function getDateRangeCondition($dateRange) {
    switch ($dateRange) {
        case 'today':
            return "DATE(created_at) = CURDATE()";
        case 'week':
            return "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
        case 'month':
            return "created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        case 'year':
            return "created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
        default:
            return '';
    }
}

// 创建财务记录（暂时简化）
function handleCreateFinancialRecord($auth) {
    $auth->sendError('功能开发中', 501);
}
?> 