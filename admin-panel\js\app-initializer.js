;
initBusinessLogic() {
    console.log('Initializing business logic...');
    // 在这里可以初始化所有业务逻辑模块的单例
    
    // 示例：初始化用户管理器并注入依赖
    if (window.UserManager) {
        window.userManager = new window.UserManager();
        window.userManager.apiClient = this.apiClient;
        window.userManager.utils = this.utils;
        console.log('UserManager initialized and dependencies injected.');
    }

    // 其他业务逻辑模块...
} 