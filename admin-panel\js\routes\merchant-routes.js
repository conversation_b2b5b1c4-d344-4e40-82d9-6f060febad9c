/**
 * 商户路由配置
 * 优化版本 - 只加载商户需要的功能
 * 
 * <AUTHOR>
 * @version 2.0.0 - 架构优化版本
 */

const MerchantRoutes = {
    // 租户类型标识
    tenantType: 'merchant',
    
    // 商户菜单配置（16个核心功能）
    menuConfig: [
        {
            id: 'dashboard',
            name: '商户概览',
            icon: 'bi-speedometer2',
            path: '/dashboard',
            module: 'MerchantDashboardModule',
            permission: 'merchant_dashboard',
            description: '商户业务数据概览'
        },
        {
            id: 'orders',
            name: '订单管理',
            icon: 'bi-receipt',
            path: '/orders',
            module: 'MerchantOrderModule',
            permission: 'merchant_orders',
            description: '订单查询和管理'
        },
        {
            id: 'payments',
            name: '支付管理',
            icon: 'bi-credit-card',
            path: '/payments',
            module: 'PaymentManagementModule',
            permission: 'payment_management',
            description: '支付方式配置'
        },
        {
            id: 'api_tools',
            name: 'API工具',
            icon: 'bi-tools',
            path: '/api-tools',
            module: 'ApiModule',
            permission: 'api_tools',
            description: 'API接口工具和文档'
        },
        {
            id: 'balance',
            name: '余额管理',
            icon: 'bi-currency-dollar',
            path: '/balance',
            module: 'MerchantBalanceModule',
            permission: 'balance_management',
            description: '账户余额和充值'
        },
        {
            id: 'transactions',
            name: '交易记录',
            icon: 'bi-list-ul',
            path: '/transactions',
            module: 'MerchantTransactionModule',
            permission: 'transaction_view',
            description: '查看交易流水'
        },
        {
            id: 'settlements',
            name: '结算记录',
            icon: 'bi-calculator',
            path: '/settlements',
            module: 'MerchantSettlementModule',
            permission: 'settlement_view',
            description: '结算和分润记录'
        },
        {
            id: 'reports',
            name: '数据报表',
            icon: 'bi-graph-up',
            path: '/reports',
            module: 'MerchantReportsModule',
            permission: 'merchant_reports',
            description: '业务数据报表'
        },
        {
            id: 'notifications',
            name: '消息通知',
            icon: 'bi-bell',
            path: '/notifications',
            module: 'MerchantNotificationModule',
            permission: 'merchant_notifications',
            description: '系统消息通知'
        },
        {
            id: 'webhook',
            name: '回调配置',
            icon: 'bi-arrow-repeat',
            path: '/webhook',
            module: 'WebhookModule',
            permission: 'webhook_management',
            description: '支付回调配置'
        },
        {
            id: 'security',
            name: '安全设置',
            icon: 'bi-shield-check',
            path: '/security',
            module: 'MerchantSecurityModule',
            permission: 'security_settings',
            description: '账户安全设置'
        },
        {
            id: 'logs',
            name: '操作日志',
            icon: 'bi-journal-text',
            path: '/logs',
            module: 'MerchantLogsModule',
            permission: 'merchant_logs',
            description: '操作记录查询'
        },
        {
            id: 'help',
            name: '帮助中心',
            icon: 'bi-question-circle',
            path: '/help',
            module: 'MerchantHelpModule',
            permission: 'help_center',
            description: '使用帮助文档'
        },
        {
            id: 'feedback',
            name: '意见反馈',
            icon: 'bi-chat-square-text',
            path: '/feedback',
            module: 'MerchantFeedbackModule',
            permission: 'feedback_submit',
            description: '提交意见建议'
        },
        {
            id: 'statistics',
            name: '统计分析',
            icon: 'bi-bar-chart',
            path: '/statistics',
            module: 'MerchantStatisticsModule',
            permission: 'statistics_view',
            description: '业务数据分析'
        },
        {
            id: 'profile',
            name: '个人资料',
            icon: 'bi-person-circle',
            path: '/profile',
            module: 'MerchantProfileModule',
            permission: 'profile_management',
            description: '管理商户资料'
        }
    ],

    // 路由配置
    routes: {
        '/dashboard': {
            component: 'MerchantDashboardModule',
            title: '商户概览',
            requireAuth: true,
            permission: 'merchant_dashboard'
        },
        '/orders': {
            component: 'MerchantOrderModule',
            title: '订单管理',
            requireAuth: true,
            permission: 'merchant_orders'
        },
        '/payments': {
            component: 'PaymentManagementModule',
            title: '支付管理',
            requireAuth: true,
            permission: 'payment_management'
        },
        '/api-tools': {
            component: 'ApiModule',
            title: 'API工具',
            requireAuth: true,
            permission: 'api_tools'
        },
        '/balance': {
            component: 'MerchantBalanceModule',
            title: '余额管理',
            requireAuth: true,
            permission: 'balance_management'
        },
        '/transactions': {
            component: 'MerchantTransactionModule',
            title: '交易记录',
            requireAuth: true,
            permission: 'transaction_view'
        },
        '/settlements': {
            component: 'MerchantSettlementModule',
            title: '结算记录',
            requireAuth: true,
            permission: 'settlement_view'
        },
        '/reports': {
            component: 'MerchantReportsModule',
            title: '数据报表',
            requireAuth: true,
            permission: 'merchant_reports'
        },
        '/notifications': {
            component: 'MerchantNotificationModule',
            title: '消息通知',
            requireAuth: true,
            permission: 'merchant_notifications'
        },
        '/webhook': {
            component: 'WebhookModule',
            title: '回调配置',
            requireAuth: true,
            permission: 'webhook_management'
        },
        '/security': {
            component: 'MerchantSecurityModule',
            title: '安全设置',
            requireAuth: true,
            permission: 'security_settings'
        },
        '/logs': {
            component: 'MerchantLogsModule',
            title: '操作日志',
            requireAuth: true,
            permission: 'merchant_logs'
        },
        '/help': {
            component: 'MerchantHelpModule',
            title: '帮助中心',
            requireAuth: true,
            permission: 'help_center'
        },
        '/feedback': {
            component: 'MerchantFeedbackModule',
            title: '意见反馈',
            requireAuth: true,
            permission: 'feedback_submit'
        },
        '/statistics': {
            component: 'MerchantStatisticsModule',
            title: '统计分析',
            requireAuth: true,
            permission: 'statistics_view'
        },
        '/profile': {
            component: 'MerchantProfileModule',
            title: '个人资料',
            requireAuth: true,
            permission: 'profile_management'
        }
    },

    // 权限配置
    permissions: [
        'merchant_dashboard',
        'merchant_orders',
        'payment_management',
        'api_tools',
        'balance_management',
        'transaction_view',
        'settlement_view',
        'merchant_reports',
        'merchant_notifications',
        'webhook_management',
        'security_settings',
        'merchant_logs',
        'help_center',
        'feedback_submit',
        'statistics_view',
        'profile_management'
    ],

    // 默认路由
    defaultRoute: '/dashboard',

    // 主题配置
    theme: {
        primaryColor: '#fa8c16',
        brandName: '商户管理系统',
        sidebarStyle: 'light',
        headerStyle: 'light'
    },

    // API端点配置
    apiEndpoints: {
        base: '/api/merchant/index.php',
        auth: '/api/merchant/index.php?module=auth&action=login',
        logout: '/api/merchant/index.php?module=auth&action=logout',
        profile: '/api/merchant/index.php?module=profile&action=get'
    },

    // 快速操作配置
    quickActions: [
        {
            name: '查看今日订单',
            icon: 'bi-receipt',
            action: 'today_orders',
            color: 'primary'
        },
        {
            name: '发起支付',
            icon: 'bi-credit-card',
            action: 'create_payment',
            color: 'success'
        },
        {
            name: 'API文档',
            icon: 'bi-book',
            action: 'api_docs',
            color: 'info'
        },
        {
            name: '查看余额',
            icon: 'bi-currency-dollar',
            action: 'check_balance',
            color: 'warning'
        }
    ],

    // 统计信息配置
    dashboardWidgets: [
        'today_orders',
        'today_amount',
        'success_rate',
        'account_balance',
        'pending_amount',
        'total_transactions'
    ]
};

// 导出配置
window.MerchantRoutes = MerchantRoutes;
console.log('✅ 商户路由配置已加载 (16个功能)'); 