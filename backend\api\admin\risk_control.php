<?php
/**
 * 风控管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

// 检查风控权限
if (!$auth->checkPermission('risk.view')) {
    $auth->sendForbidden('无权限访问风控管理');
}

try {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($action) {
        case 'risk_configs':
            handleRiskConfigs($auth);
            break;
        case 'blacklist':
            handleBlacklist($auth);
            break;
        case 'risk_events':
            handleGetRiskEvents($auth);
            break;
        case 'risk_report':
            handleGenerateRiskReport($auth);
            break;
        default:
            // 默认风控配置管理
            handleRiskConfigs($auth);
    }
} catch (Exception $e) {
    error_log("Risk Control API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 风控配置管理
function handleRiskConfigs($auth) {
    global $method;
    
    switch ($method) {
        case 'GET':
            handleGetRiskConfigs($auth);
            break;
        case 'POST':
            handleCreateRiskConfig($auth);
            break;
        default:
            $auth->sendError('不支持的请求方法', 405);
    }
}

// 获取风控配置
function handleGetRiskConfigs($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('risk.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
        $status = isset($_GET['status']) ? $_GET['status'] : '';
        $type = isset($_GET['type']) ? $_GET['type'] : '';
        
        // 数据隔离
        if ($currentUser['user_type'] === 'merchant') {
            $merchantId = $currentUser['profile_id'];
        }
        
        $whereConditions = array();
        $params = array();
        
        if ($merchantId > 0) {
            $whereConditions[] = 'r.merchant_id = ?';
            $params[] = $merchantId;
        }
        
        if (!empty($status)) {
            $whereConditions[] = 'r.status = ?';
            $params[] = $status;
        }
        
        if (!empty($type)) {
            $whereConditions[] = 'r.rule_type = ?';
            $params[] = $type;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // 获取风控配置
        try {
            $riskConfigs = $db->fetchAll(
                "SELECT r.*, 
                        m.company_name as merchant_name,
                        u.real_name as merchant_real_name
                 FROM merchant_risk_configs r
                 LEFT JOIN merchants m ON r.merchant_id = m.id
                 LEFT JOIN users u ON m.user_id = u.id
                 $whereClause
                 ORDER BY r.created_at DESC",
                $params
            );
        } catch (Exception $e) {
            // 如果表不存在，返回空数组
            $riskConfigs = array();
        }
        
        // 计算统计数据
        $stats = array(
            'total_configs' => count($riskConfigs),
            'active_configs' => 0,
            'avg_daily_limit' => 0,
            'avg_single_limit' => 0
        );
        
        $totalDailyLimit = 0;
        $totalSingleLimit = 0;
        $dailyLimitCount = 0;
        $singleLimitCount = 0;
        
        foreach ($riskConfigs as $config) {
            if ($config['status'] === 'active') {
                $stats['active_configs']++;
            }
            
            if (isset($config['daily_limit']) && $config['daily_limit'] > 0) {
                $totalDailyLimit += $config['daily_limit'];
                $dailyLimitCount++;
            }
            
            if (isset($config['single_limit']) && $config['single_limit'] > 0) {
                $totalSingleLimit += $config['single_limit'];
                $singleLimitCount++;
            }
        }
        
        if ($dailyLimitCount > 0) {
            $stats['avg_daily_limit'] = $totalDailyLimit / $dailyLimitCount;
        }
        
        if ($singleLimitCount > 0) {
            $stats['avg_single_limit'] = $totalSingleLimit / $singleLimitCount;
        }
        
        // 记录操作日志
        $auth->logUserAction('view_risk_configs', 'risk', 0, '查看风控配置');
        
        $response = array(
            'configs' => $riskConfigs ?: array(),
            'stats' => $stats
        );
        
        $auth->sendSuccess($response, '风控配置获取成功');
        
    } catch (Exception $e) {
        error_log("获取风控配置失败: " . $e->getMessage());
        $auth->sendError('获取风控配置失败: ' . $e->getMessage(), 500);
    }
}

// 创建风控配置（暂时简化）
function handleCreateRiskConfig($auth) {
    // 检查权限
    if (!$auth->checkPermission('risk.create')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

// 黑名单管理（暂时简化）
function handleBlacklist($auth) {
    // 检查权限
    if (!$auth->checkPermission('blacklist.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

// 获取风控事件
function handleGetRiskEvents($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('risk.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;
        
        $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
        $riskLevel = isset($_GET['risk_level']) ? $_GET['risk_level'] : '';
        $eventType = isset($_GET['event_type']) ? $_GET['event_type'] : '';
        $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : '';
        
        $whereConditions = array();
        $params = array();
        
        // 数据隔离
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = 're.merchant_id = ?';
            $params[] = $currentUser['profile_id'];
        } elseif ($merchantId > 0) {
            $whereConditions[] = 're.merchant_id = ?';
            $params[] = $merchantId;
        }
        
        if (!empty($riskLevel)) {
            $whereConditions[] = 're.risk_level = ?';
            $params[] = $riskLevel;
        }
        
        if (!empty($eventType)) {
            $whereConditions[] = 're.event_type = ?';
            $params[] = $eventType;
        }
        
        // 日期范围筛选
        if (!empty($dateRange)) {
            switch ($dateRange) {
                case 'today':
                    $whereConditions[] = "DATE(re.created_at) = CURDATE()";
                    break;
                case 'week':
                    $whereConditions[] = "re.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                    break;
                case 'month':
                    $whereConditions[] = "re.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                    break;
            }
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // 获取风控事件
        try {
            $events = $db->fetchAll(
                "SELECT re.*, 
                        m.company_name as merchant_name,
                        t.order_id,
                        t.amount as transaction_amount
                 FROM risk_events re
                 LEFT JOIN merchants m ON re.merchant_id = m.id
                 LEFT JOIN transactions t ON re.transaction_id = t.id
                 $whereClause
                 ORDER BY re.created_at DESC
                 LIMIT ? OFFSET ?",
                array_merge($params, array($limit, $offset))
            );
            
            // 获取总数
            $totalResult = $db->fetch(
                "SELECT COUNT(*) as count FROM risk_events re $whereClause",
                $params
            );
            $total = $totalResult['count'] ?: 0;
            
        } catch (Exception $e) {
            // 如果表不存在，返回空数据
            $events = array();
            $total = 0;
        }
        
        // 获取统计信息
        try {
            $stats = $db->fetch(
                "SELECT 
                    COUNT(*) as total_events,
                    COUNT(CASE WHEN re.risk_level = 'high' THEN 1 END) as high_risk_events,
                    COUNT(CASE WHEN re.risk_level = 'medium' THEN 1 END) as medium_risk_events,
                    COUNT(CASE WHEN re.risk_level = 'low' THEN 1 END) as low_risk_events,
                    COUNT(CASE WHEN re.status = 'blocked' THEN 1 END) as blocked_events
                 FROM risk_events re $whereClause",
                $params
            );
        } catch (Exception $e) {
            $stats = array(
                'total_events' => 0,
                'high_risk_events' => 0,
                'medium_risk_events' => 0,
                'low_risk_events' => 0,
                'blocked_events' => 0
            );
        }
        
        // 记录操作日志
        $auth->logUserAction('view_risk_events', 'risk', 0, '查看风控事件');
        
        $response = array(
            'events' => $events ?: array(),
            'stats' => $stats ?: array(),
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        );
        
        $auth->sendSuccess($response, '风控事件获取成功');
        
    } catch (Exception $e) {
        error_log("获取风控事件失败: " . $e->getMessage());
        $auth->sendError('获取风控事件失败: ' . $e->getMessage(), 500);
    }
}

// 生成风控报告（暂时简化）
function handleGenerateRiskReport($auth) {
    // 检查权限
    if (!$auth->checkPermission('risk.report')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}
?> 