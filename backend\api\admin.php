<?php
/**
 * 管理后台路由入口 - 四层架构多租户版本
 * 将请求转发到admin目录下的相应模块
 * 支持基于域名的多租户访问控制
 */

// 定义管理面板常量，用于子模块验证
define('IN_ADMIN_PANEL', true);

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入多租户认证系统
require_once dirname(__FILE__) . '/../utils/TenantAuth.php';
require_once dirname(__FILE__) . '/../config/database.php';
require_once dirname(__FILE__) . '/utils/ErrorHandler.php';

// 引入新的API路由系统
require_once dirname(__FILE__) . '/core/ApiRouter.php';
require_once dirname(__FILE__) . '/core/ApiMiddleware.php';

// 设置全局异常处理器
ErrorHandler::setGlobalExceptionHandler();

// ================================
// 多租户认证系统初始化
// ================================

try {
    // 创建租户认证实例
    $tenantAuth = new TenantAuth();
    $domainInfo = $tenantAuth->getDomainInfo();
    
    // 获取Authorization头（兼容FastCGI）
    if (!function_exists('getAllHeaders')) {
        function getAllHeaders() {
            $headers = array();
            foreach ($_SERVER as $name => $value) {
                if (substr($name, 0, 5) == 'HTTP_') {
                    $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
                }
            }
            return $headers;
        }
    }
    
    $headers = getAllHeaders();
    $token = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = str_replace('Bearer ', '', $token);
    
    // 获取请求参数
    $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
    $action = isset($_GET['action']) ? $_GET['action'] : (isset($input['action']) ? $input['action'] : '');
    $do = isset($input['do']) ? $input['do'] : (isset($_GET['do']) ? $_GET['do'] : '');
    
    // 调试信息
    error_log("Admin.php - GET action: " . (isset($_GET['action']) ? $_GET['action'] : 'none'));
    error_log("Admin.php - POST action: " . (isset($input['action']) ? $input['action'] : 'none'));
    error_log("Admin.php - Final action: " . $action);
    error_log("Admin.php - Full GET: " . print_r($_GET, true));
    
    // 添加域名解析处理
    if ($action === 'resolve_domain') {
        // 直接返回当前域名的解析结果，不接受用户传递的域名参数（防止篡改）
        if ($domainInfo) {
            // 只返回前端必需的信息，避免泄露敏感数据
            $safeData = [
                'tenant_type' => $domainInfo['tenant_type'],
                'custom_config' => $domainInfo['custom_config'],
                'platform_name' => isset($domainInfo['platform_name']) ? $domainInfo['platform_name'] : ''
            ];
            
            echo json_encode([
                'error_code' => 0,
                'message' => '域名解析成功',
                'data' => $safeData
            ]);
        } else {
            http_response_code(404);
            echo json_encode([
                'error_code' => 1,
                'message' => '当前域名配置未找到或已禁用'
            ]);
        }
        exit;
    }
    
    // 跳过认证的接口
    $skipAuthActions = array('login', 'logout', 'resolve_domain');
    $currentAction = $action ? $action : $do;
    
    if (in_array($currentAction, $skipAuthActions)) {
        $currentUser = null;
        $tenantContext = $domainInfo;
    } else {
        // 验证租户用户身份
        $currentUser = $tenantAuth->getCurrentTenantUser($token);
        
        if (!$currentUser) {
            http_response_code(401);
            echo json_encode(array(
                'code' => 401,
                'message' => '未授权访问，请先登录'
            ));
            exit;
        }
        
        // 验证用户权限
        if (!in_array($currentUser['user_type'], array('system_admin', 'platform_admin', 'employee', 'merchant', 'provider'))) {
            http_response_code(403);
            echo json_encode(array(
                'code' => 403,
                'message' => '权限不足，无法访问管理功能'
            ));
            exit;
        }
        
        // 创建租户上下文
        $tenantContext = array_merge($domainInfo, array(
            'current_user' => $currentUser,
            'user_permissions' => getTenantUserPermissions($currentUser, $domainInfo)
        ));
    }
    
    // 设置全局变量供子模块使用
    $GLOBALS['tenant_auth'] = $tenantAuth;
    $GLOBALS['tenant_context'] = $tenantContext;
    $GLOBALS['current_user'] = $currentUser;
    $GLOBALS['domain_info'] = $domainInfo;
    
    // 向后兼容：设置传统变量
    $_REQUEST['domain_info'] = $domainInfo;
    $_POST['domain_info'] = $domainInfo;
    $_GET['domain_info'] = $domainInfo;
    
    // 初始化API中间件
    $apiMiddleware = new ApiMiddleware($tenantAuth);
    $GLOBALS['api_middleware'] = $apiMiddleware;
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(array(
        'code' => 500,
        'message' => '系统初始化失败: ' . $e->getMessage()
    ));
    exit;
}

// ================================
// 租户数据过滤工具函数
// ================================

/**
 * 获取租户用户权限列表
 */
function getTenantUserPermissions($user, $domainInfo) {
    $permissions = array();
    
    switch ($domainInfo['tenant_type']) {
        case 'system_admin':
            if ($user['user_type'] === 'system_admin') {
                $permissions = array('*'); // 所有权限
            }
            break;
            
        case 'platform_admin':
            if ($user['user_type'] === 'platform_admin') {
                $permissions = array(
                    'platform.manage', 'provider.manage', 'merchant.manage',
                    'order.view', 'finance.view', 'user.manage'
                );
            }
            break;
            
        case 'provider':
            if ($user['user_type'] === 'provider') {
                $permissions = array(
                    'provider.self', 'merchant.manage', 'order.view', 'finance.view'
                );
            }
            break;
            
        case 'merchant':
            if ($user['user_type'] === 'merchant') {
                $permissions = array(
                    'merchant.self', 'order.view', 'finance.view'
                );
            }
            break;
    }
    
    return $permissions;
}

/**
 * 构建租户数据过滤条件
 */
function buildTenantDataFilter($tableName = '', $domainInfo = null, $currentUser = null) {
    if (!$domainInfo) {
        $domainInfo = $GLOBALS['domain_info'];
    }
    if (!$currentUser) {
        $currentUser = $GLOBALS['current_user'];
    }
    
    if (!$domainInfo || !$currentUser) {
        return '';
    }
    
    $prefix = $tableName ? $tableName . '.' : '';
    
    switch ($domainInfo['tenant_type']) {
        case 'system_admin':
            return ''; // 无限制
            
        case 'platform_admin':
            if ($domainInfo['platform_id']) {
                return " AND {$prefix}platform_id = " . intval($domainInfo['platform_id']);
            }
            return '';
            
        case 'provider':
            $providerId = getCurrentProviderId($currentUser);
            if ($providerId) {
                return " AND {$prefix}provider_id = " . intval($providerId);
            }
            return " AND 1=0"; // 无权限
            
        case 'merchant':
            $merchantId = getCurrentMerchantId($currentUser);
            if ($merchantId) {
                return " AND {$prefix}merchant_id = " . intval($merchantId);
            }
            return " AND 1=0"; // 无权限
            
        default:
            return " AND 1=0"; // 默认无权限
    }
}

/**
 * 检查租户权限
 */
function checkTenantPermission($permission, $domainInfo = null, $currentUser = null) {
    if (!$domainInfo) {
        $domainInfo = $GLOBALS['domain_info'];
    }
    if (!$currentUser) {
        $currentUser = $GLOBALS['current_user'];
    }
    
    if (!$domainInfo || !$currentUser) {
        return false;
    }
    
    $userPermissions = getTenantUserPermissions($currentUser, $domainInfo);
    
    // 检查是否有全部权限
    if (in_array('*', $userPermissions)) {
        return true;
    }
    
    // 检查具体权限
    return in_array($permission, $userPermissions);
}

/**
 * 获取当前用户的码商ID
 */
function getCurrentProviderId($user) {
    if ($user['user_type'] !== 'provider') {
        return null;
    }
    
    try {
        $db = Database::getInstance();
        $provider = $db->fetch(
            "SELECT id FROM payment_providers WHERE user_id = ?",
            array($user['id'])
        );
        return $provider ? $provider['id'] : null;
    } catch (Exception $e) {
        error_log("getCurrentProviderId Error: " . $e->getMessage());
        return null;
    }
}

/**
 * 获取当前用户的商户ID
 */
function getCurrentMerchantId($user) {
    if ($user['user_type'] !== 'merchant') {
        return null;
    }
    
    try {
        $db = Database::getInstance();
        $merchant = $db->fetch(
            "SELECT id FROM merchants WHERE user_id = ?",
            array($user['id'])
        );
        return $merchant ? $merchant['id'] : null;
    } catch (Exception $e) {
        error_log("getCurrentMerchantId Error: " . $e->getMessage());
        return null;
    }
}

/**
 * 记录租户操作日志
 */
function logTenantAction($action, $targetType, $targetId, $description = '') {
    global $tenantAuth;
    
    if ($tenantAuth && isset($GLOBALS['current_user'])) {
        $tenantAuth->logTenantAction(
            $GLOBALS['current_user']['id'],
            $action,
            $targetType,
            $targetId,
            $description
        );
    }
}

// ================================
// 路由处理
// ================================

// 确定最终的action参数
$finalAction = $do ? $do : $action;

// 使用API中间件进行预处理
if (isset($apiMiddleware) && $finalAction) {
    $middlewareResult = $apiMiddleware->handleRequest($finalAction, $_SERVER['REQUEST_METHOD']);
    
    if (!$middlewareResult['success']) {
        http_response_code($middlewareResult['error']['code']);
        echo json_encode([
            'code' => $middlewareResult['error']['code'],
            'message' => $middlewareResult['error']['message'],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit;
    }
    
    // 记录中间件验证成功
    error_log("API中间件验证成功: {$finalAction}");
}

// 根据action路由到相应的处理模块
switch ($finalAction) {
    // 认证相关
    case 'login':
    case 'logout':
    case 'check_auth':
        require_once dirname(__FILE__) . '/admin/auth.php';
        break;
        
    // 仪表板
    case 'dashboard':
    case 'get_stats':
        require_once dirname(__FILE__) . '/admin/dashboard.php';
        break;
        
    // 用户管理
    case 'users':
    case 'get_users':
    case 'get_user':
    case 'get_user_detail':
    case 'add_user':
    case 'edit_user':
    case 'delete_user':
    case 'update_user_status':
    case 'batch_operation':
        require_once dirname(__FILE__) . '/admin/users.php';
        break;
        
    // 平台管理（系统管理员专用）
    case 'get_platforms_list':
    case 'add_platform':
    case 'edit_platform':
    case 'delete_platform':
    case 'get_platform_details':
        require_once dirname(__FILE__) . '/admin/platforms.php';
        break;
        
    // 域名管理（系统管理员专用）
    case 'get_domains_list':
    case 'add_domain':
    case 'edit_domain':
    case 'delete_domain':
    case 'get_domain_details':
        require_once dirname(__FILE__) . '/admin/domains.php';
        break;
        
    // 商户管理
    case 'merchants':
    case 'get_merchants':
    case 'add_merchant':
    case 'edit_merchant':
    case 'delete_merchant':
    case 'update_merchant_status':
    case 'create_merchant':
    case 'update_merchant':
    case 'approve_merchant':
    case 'reject_merchant':
    case 'merchant_api_keys':
    case 'reset_merchant_password':
    case 'update_merchant_user_status':
    case 'get_merchant_login_logs':
    case 'get_merchant_user_detail':
        require_once dirname(__FILE__) . '/admin/merchants.php';
        break;
        
    // 设备管理
    case 'devices':
    case 'get_devices':
    case 'add_device':
    case 'edit_device':
    case 'delete_device':
    case 'update_device_status':
        require_once dirname(__FILE__) . '/admin/devices.php';
        break;
        
    // 交易管理
    case 'transactions':
    case 'get_transactions':
    case 'get_transaction_stats':
    case 'get_transaction_detail':
    case 'update_transaction_status':
    case 'update_transaction_remark':
    case 'export_transactions':
        require_once dirname(__FILE__) . '/admin/transactions.php';
        break;
        
    // 账户管理 (支付宝等)
    case 'accounts':
    case 'alipay_accounts':
    case 'get_accounts':
    case 'add_account':
    case 'edit_account':
    case 'delete_account':
    case 'update_account_status':
        require_once dirname(__FILE__) . '/admin/payments.php';
        break;
        
    // 权限管理
    case 'permissions':
    case 'get_permissions':
    case 'get_roles':
    case 'update_user_permissions':
        require_once dirname(__FILE__) . '/admin/permissions.php';
        break;
        
    // 岗位管理
    case 'job_positions':
    case 'get_job_positions':
    case 'add_job_position':
    case 'edit_job_position':
    case 'delete_job_position':
    case 'update_job_position_status':
        require_once dirname(__FILE__) . '/admin/job_positions.php';
        break;
        
    // 风控管理
    case 'risk_control':
    case 'risk_configs':
    case 'risk_events':
    case 'risk_report':
    case 'blacklist':
    case 'get_risk_configs':
    case 'get_risk_events':
    case 'generate_risk_report':
    case 'create_risk_config':
    case 'update_risk_config':
    case 'delete_risk_config':
        require_once dirname(__FILE__) . '/admin/risk_control.php';
        break;
        
    // 通知管理
    case 'notifications':
    case 'get_notifications':
    case 'add_notification':
    case 'edit_notification':
    case 'delete_notification':
    case 'update_notification_status':
    case 'send_notification':
    case 'get_notification_stats':
        require_once dirname(__FILE__) . '/admin/notifications.php';
        break;
        
    // 日报管理
    case 'daily_reports':
    case 'get_daily_reports':
    case 'add_daily_report':
    case 'edit_daily_report':
    case 'delete_daily_report':
    case 'today_stats':
    case 'chart_data':
        require_once dirname(__FILE__) . '/admin/daily_reports.php';
        break;
        
    // TOTP管理
    case 'totp':
    case 'get_totp':
    case 'add_totp':
    case 'edit_totp':
    case 'delete_totp':
        require_once dirname(__FILE__) . '/admin/totp.php';
        break;
        
    // 安全日志
    case 'security_logs':
    case 'get_security_logs':
    case 'stats':
    case 'recent':
        require_once dirname(__FILE__) . '/admin/security_logs.php';
        break;
        
    // 性能监控
    case 'performance_monitor':
    case 'get_performance_data':
        require_once dirname(__FILE__) . '/admin/performance_monitor.php';
        break;
        
    // 支付请求
    case 'payment_requests':
    case 'get_payment_requests':
    case 'update_payment_request':
        require_once dirname(__FILE__) . '/admin/payments.php';
        break;
        
    // 脚本管理
    case 'get_script_matrix':
    case 'get_device_brands':
    case 'scripts':
        require_once dirname(__FILE__) . '/admin/devices.php';
        break;
        
    // 支付服务商/码商管理
    case 'providers':
    case 'payment_providers':
    case 'get_providers':
    case 'get_payment_providers':
    case 'add_provider':
    case 'edit_provider':
    case 'update_provider':
    case 'delete_provider':
    case 'update_provider_status':
    case 'get_provider_stats':
    case 'reset_provider_password':
    case 'update_provider_user_status':
    case 'get_provider_login_logs':
    case 'get_provider_user_detail':
        require_once dirname(__FILE__) . '/admin/providers.php';
        break;
        
    // 订单管理
    case 'orders':
    case 'get_orders':
        require_once dirname(__FILE__) . '/admin/orders.php';
        break;
        
    // 员工管理
    case 'employees':
    case 'get_employees':
        require_once dirname(__FILE__) . '/admin/employees.php';
        break;
        
    // 财务管理
    case 'financial':
    case 'get_financial_data':
        require_once dirname(__FILE__) . '/admin/financial.php';
        break;
        
    // 产品管理
    case 'products':
    case 'get_products':
    case 'product_stats':
    case 'product_categories':
    case 'product_rates':
        require_once dirname(__FILE__) . '/admin/products.php';
        break;
        
    // API文档
    case 'api_docs':
    case 'get_api_docs':
        require_once dirname(__FILE__) . '/admin/api_docs.php';
        break;
        
    // API管理
    case 'api_management':
    case 'get_key_info':
    case 'get_call_stats':
    case 'update_callback_url':
    case 'regenerate_api_key':
    case 'get_ip_whitelist':
    case 'update_ip_whitelist':
    case 'api_test':
    case 'download_postman':
        require_once dirname(__FILE__) . '/admin/api_management.php';
        break;
        
    // 商户工具管理
    case 'merchant_tools':
    case 'get_dashboard':
    case 'get_orders':
    case 'get_statistics':
    case 'get_products':
    case 'get_config':
    case 'update_profile':
        require_once dirname(__FILE__) . '/merchant/tools.php';
        break;
        
    // 支付宝账单管理（码商专用）
    case 'alipay_bills_list':
    case 'alipay_bills_stats':
    case 'alipay_bills_detail':
    case 'alipay_bills_manual_match':
    case 'alipay_bills_ignore':
    case 'alipay_bills_export':
        // 转发到支付宝账单API
        $_GET['path'] = str_replace('alipay_bills_', '', $finalAction);
        require_once dirname(__FILE__) . '/alipay_bills.php';
        break;
        
    // API路由管理（系统管理员专用）
    case 'get_api_routes':
    case 'get_route_stats':
    case 'get_api_call_logs':
    case 'test_route':
    case 'export_routes':
    case 'get_route_performance':
        require_once dirname(__FILE__) . '/admin/api_router.php';
        break;
        
    default:
        // 默认显示管理后台首页
        require_once dirname(__FILE__) . '/admin/index.php';
        break;
}
?> 