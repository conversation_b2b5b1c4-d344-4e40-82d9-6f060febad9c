<?php
header('Content-Type: application/json');

$result = array();
$result['step1'] = 'PHP基本功能正常';

// 测试数据库文件
try {
    require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
    $result['step2'] = '数据库文件加载成功';
} catch (Exception $e) {
    $result['step2_error'] = $e->getMessage();
    echo json_encode($result);
    exit;
}

// 测试数据库连接
try {
    $db = Database::getInstance()->getConnection();
    $result['step3'] = '数据库连接成功';
} catch (Exception $e) {
    $result['step3_error'] = $e->getMessage();
    echo json_encode($result);
    exit;
}

// 测试Auth文件
try {
    require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';
    $result['step4'] = 'Auth文件加载成功';
} catch (Exception $e) {
    $result['step4_error'] = $e->getMessage();
    echo json_encode($result);
    exit;
}

// 测试Auth实例化
try {
    $auth = new Auth();
    $result['step5'] = 'Auth实例化成功';
} catch (Exception $e) {
    $result['step5_error'] = $e->getMessage();
    echo json_encode($result);
    exit;
}

// 测试script_versions表
try {
    $stmt = $db->prepare("SELECT COUNT(*) FROM script_versions");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    $result['step6'] = 'script_versions表查询成功，记录数：' . $count;
} catch (Exception $e) {
    $result['step6_error'] = $e->getMessage();
}

echo json_encode($result, JSON_UNESCAPED_UNICODE);
?> 