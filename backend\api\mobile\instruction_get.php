<?php
/**
 * 手机端获取收款指令API
 * 
 * 功能：
 * 1. 设备认证
 * 2. 获取待执行指令
 * 3. 更新指令状态为执行中
 * 
 * <AUTHOR> System
 * @version 1.0
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(__FILE__)) . '/core/PaymentInstructionManager.php';

/**
 * 统一响应格式
 */
function jsonResponse($code, $message, $data = null) {
    echo json_encode([
        'code' => $code,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 验证设备权限
 */
function validateDevice($deviceId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT d.*, aa.id as alipay_account_id, aa.account_name, aa.account_number
            FROM devices d
            LEFT JOIN alipay_accounts aa ON d.id = aa.device_id AND aa.status = 'approved'
            WHERE d.device_id = ? AND d.status = 'active'
        ");
        $stmt->execute([$deviceId]);
        $device = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$device) {
            return [
                'valid' => false,
                'error' => '设备未授权或不存在'
            ];
        }
        
        if (!$device['alipay_account_id']) {
            return [
                'valid' => false,
                'error' => '设备未关联有效的支付宝账户'
            ];
        }
        
        return [
            'valid' => true,
            'device' => $device
        ];
        
    } catch (Exception $e) {
        return [
            'valid' => false,
            'error' => '设备验证失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 更新设备心跳
 */
function updateDeviceHeartbeat($deviceId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            UPDATE devices 
            SET last_online = NOW() 
            WHERE device_id = ?
        ");
        $stmt->execute([$deviceId]);
    } catch (Exception $e) {
        error_log("Failed to update device heartbeat: " . $e->getMessage());
    }
}

// 主程序开始
try {
    $db = new Database();
    $pdo = $db->connect();
    
    // 获取请求参数
    $deviceId = isset($_GET['device_id']) ? $_GET['device_id'] : (isset($_POST['device_id']) ? $_POST['device_id'] : '');
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 5;
    $autoExecute = isset($_GET['auto_execute']) ? boolval($_GET['auto_execute']) : true;
    
    // 验证必需参数
    if (empty($deviceId)) {
        jsonResponse(400, '缺少设备ID参数');
    }
    
    // 限制获取数量
    if ($limit < 1 || $limit > 20) {
        $limit = 5;
    }
    
    // 验证设备
    $deviceValidation = validateDevice($deviceId);
    if (!$deviceValidation['valid']) {
        jsonResponse(401, $deviceValidation['error']);
    }
    
    $device = $deviceValidation['device'];
    $alipayAccountId = $device['alipay_account_id'];
    
    // 更新设备心跳
    updateDeviceHeartbeat($deviceId);
    
    // 获取指令管理器
    $instructionManager = new PaymentInstructionManager();
    
    // 获取待执行指令
    $result = $instructionManager->getPendingInstructions($alipayAccountId, $deviceId, $limit);
    
    if (!$result['success']) {
        jsonResponse(500, '获取指令失败: ' . $result['error']);
    }
    
    $instructions = $result['instructions'];
    $processedInstructions = [];
    
    // 处理每个指令
    foreach ($instructions as $instruction) {
        $instructionId = $instruction['instruction_id'];
        
        // 如果开启自动执行，更新指令状态为执行中
        if ($autoExecute) {
            $updateResult = $instructionManager->updateInstructionStatus(
                $instructionId, 
                PaymentInstructionManager::STATUS_EXECUTING,
                null,
                [
                    'device_response' => [
                        'device_id' => $deviceId,
                        'received_at' => date('Y-m-d H:i:s'),
                        'auto_execute' => true
                    ]
                ]
            );
            
            if ($updateResult['success']) {
                $instruction['status'] = PaymentInstructionManager::STATUS_EXECUTING;
                $instruction['executed_at'] = date('Y-m-d H:i:s');
            }
        }
        
        // 添加设备相关信息
        $instruction['device_info'] = [
            'device_id' => $deviceId,
            'device_name' => $device['device_name'],
            'account_id' => $alipayAccountId
        ];
        
        // 添加执行建议
        $instruction['execution_hints'] = [
            'search_keywords' => [
                '支付宝到账',
                '收款',
                number_format($instruction['payable_amount'], 2),
                number_format($instruction['target_amount'], 2)
            ],
            'time_range' => [
                'start' => date('Y-m-d H:i:s', strtotime('-5 minutes')),
                'end' => date('Y-m-d H:i:s', strtotime('+1 minute'))
            ],
            'priority_amount' => $instruction['payable_amount'],
            'fallback_amount' => $instruction['target_amount']
        ];
        
        $processedInstructions[] = $instruction;
    }
    
    // 记录API调用日志
    error_log("Mobile instruction get: Device {$deviceId}, Account {$alipayAccountId}, Instructions: " . count($processedInstructions));
    
    // 返回响应
    jsonResponse(200, '获取指令成功', [
        'instructions' => $processedInstructions,
        'total_count' => count($processedInstructions),
        'device_info' => [
            'device_id' => $deviceId,
            'device_name' => $device['device_name'],
            'alipay_account' => [
                'id' => $alipayAccountId,
                'account_name' => $device['account_name'],
                'account_number' => $device['account_number']
            ]
        ],
        'polling_config' => [
            'interval_seconds' => 10, // 建议轮询间隔
            'max_retry' => 3,         // 最大重试次数
            'timeout_seconds' => 30   // 单次执行超时
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Mobile instruction get error: " . $e->getMessage());
    jsonResponse(500, '服务器内部错误: ' . $e->getMessage());
}
?> 