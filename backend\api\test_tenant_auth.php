<?php
/**
 * 多租户认证系统测试脚本
 * 测试auth.php的各种功能
 */

echo "<h1>多租户认证系统测试</h1>\n";

// 设置测试域名
$_SERVER['HTTP_HOST'] = 'top005.com'; // 系统管理域名

// 引入必要文件
require_once dirname(dirname(__FILE__)) . '/utils/TenantAuth.php';

// 测试1: 获取租户信息
echo "<h2>测试1: 获取租户信息</h2>\n";
try {
    $tenantAuth = new TenantAuth();
    $domainInfo = $tenantAuth->getDomainInfo();
    echo "域名解析结果: " . json_encode($domainInfo, JSON_UNESCAPED_UNICODE) . "\n\n";
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n\n";
}

// 测试2: 模拟登录请求
echo "<h2>测试2: 模拟登录请求</h2>\n";

// 准备登录数据
$loginData = array(
    'action' => 'login',
    'username' => 'admin',
    'password' => 'admin123'
);

// 模拟POST请求
$_POST = $loginData;
$_SERVER['REQUEST_METHOD'] = 'POST';

// 捕获输出
ob_start();

// 包含auth.php（会执行登录逻辑）
try {
    // 重定向stderr到stdout以捕获所有输出
    $originalErrorReporting = error_reporting(E_ALL);
    
    // 模拟请求体
    $GLOBALS['HTTP_RAW_POST_DATA'] = json_encode($loginData);
    
    // 设置输入流
    $inputStream = fopen('php://memory', 'r+');
    fwrite($inputStream, json_encode($loginData));
    rewind($inputStream);
    
    echo "模拟登录请求数据: " . json_encode($loginData, JSON_UNESCAPED_UNICODE) . "\n";
    echo "开始执行登录逻辑...\n";
    
    // 直接调用登录函数
    if (function_exists('handleTenantLogin')) {
        handleTenantLogin();
    } else {
        // 手动实现登录逻辑
        $tenantAuth = new TenantAuth();
        $loginResult = $tenantAuth->tenantLogin($loginData['username'], $loginData['password']);
        
        if ($loginResult['success']) {
            echo "登录成功!\n";
            echo "Token: " . substr($loginResult['token'], 0, 50) . "...\n";
            echo "用户信息: " . json_encode($loginResult['user'], JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "登录失败: " . $loginResult['message'] . "\n";
        }
    }
    
    error_reporting($originalErrorReporting);
    
} catch (Exception $e) {
    echo "登录测试异常: " . $e->getMessage() . "\n";
    echo "异常跟踪: " . $e->getTraceAsString() . "\n";
}

$output = ob_get_clean();
echo $output;

// 测试3: 直接测试TenantAuth类的登录方法
echo "<h2>测试3: 直接测试TenantAuth登录</h2>\n";
try {
    $tenantAuth = new TenantAuth();
    
    // 测试用户登录
    $testUsers = array(
        array('username' => 'admin', 'password' => 'admin123', 'desc' => '系统管理员'),
        array('username' => 'test_admin', 'password' => 'test123', 'desc' => '测试管理员'),
        array('username' => 'wrong_user', 'password' => 'wrong_pass', 'desc' => '错误用户')
    );
    
    foreach ($testUsers as $testUser) {
        echo "测试登录 - " . $testUser['desc'] . " (" . $testUser['username'] . "):\n";
        
        $result = $tenantAuth->tenantLogin($testUser['username'], $testUser['password']);
        
        if ($result['success']) {
            echo "  ✅ 登录成功\n";
            echo "  用户类型: " . $result['user']['user_type'] . "\n";
            echo "  租户信息: " . json_encode($result['tenant'], JSON_UNESCAPED_UNICODE) . "\n";
            echo "  Token前缀: " . substr($result['token'], 0, 30) . "...\n";
        } else {
            echo "  ❌ 登录失败: " . $result['message'] . "\n";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "TenantAuth测试异常: " . $e->getMessage() . "\n";
}

// 测试4: Token验证
echo "<h2>测试4: Token验证测试</h2>\n";
try {
    $tenantAuth = new TenantAuth();
    
    // 先登录获取token
    $loginResult = $tenantAuth->tenantLogin('admin', 'admin123');
    
    if ($loginResult['success']) {
        $token = $loginResult['token'];
        echo "获取到Token: " . substr($token, 0, 50) . "...\n";
        
        // 验证token
        $tokenData = $tenantAuth->verifyTenantToken($token);
        if ($tokenData) {
            echo "✅ Token验证成功\n";
            echo "Token数据: " . json_encode($tokenData, JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "❌ Token验证失败\n";
        }
        
        // 获取当前用户
        $currentUser = $tenantAuth->getCurrentTenantUser($token);
        if ($currentUser) {
            echo "✅ 获取当前用户成功\n";
            echo "当前用户: " . json_encode($currentUser, JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "❌ 获取当前用户失败\n";
        }
        
    } else {
        echo "无法获取测试Token: " . $loginResult['message'] . "\n";
    }
    
} catch (Exception $e) {
    echo "Token验证测试异常: " . $e->getMessage() . "\n";
}

echo "<h2>测试完成</h2>\n";
?> 