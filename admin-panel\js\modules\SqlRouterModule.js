/**
 * SQL路由管理模块
 * 用于系统管理员查看和管理SQL查询路由
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

class SqlRouterModule {
    constructor() {
        this.currentView = 'overview';
        this.queries = [];
        this.stats = {};
        this.refreshInterval = null;
        
        console.log('SqlRouterModule 初始化完成');
    }

    /**
     * 渲染模块内容
     */
    render() {
        return `
            <div class="sql-router-container">
                <!-- 头部导航 -->
                <div class="module-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="fas fa-database"></i> SQL路由管理</h2>
                            <p class="text-muted">统一管理数据库查询路由，监控性能和缓存</p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-outline-primary" onclick="sqlRouterModule.setView('overview')">
                                <i class="fas fa-chart-line"></i> 概览
                            </button>
                            <button class="btn btn-outline-primary" onclick="sqlRouterModule.setView('queries')">
                                <i class="fas fa-list"></i> 查询列表
                            </button>
                            <button class="btn btn-outline-primary" onclick="sqlRouterModule.setView('performance')">
                                <i class="fas fa-tachometer-alt"></i> 性能监控
                            </button>
                            <button class="btn btn-outline-primary" onclick="sqlRouterModule.setView('logs')">
                                <i class="fas fa-file-alt"></i> 查询日志
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="module-content">
                    <div id="overview-panel" class="content-panel">
                        <div class="row">
                            <!-- 统计卡片 -->
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6 class="card-title">总查询数</h6>
                                                <h3 class="mb-0" id="total-queries">0</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-database fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6 class="card-title">总调用数</h6>
                                                <h3 class="mb-0" id="total-calls">0</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-sync-alt fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6 class="card-title">平均响应时间</h6>
                                                <h3 class="mb-0" id="avg-time">0ms</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-clock fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6 class="card-title">缓存命中率</h6>
                                                <h3 class="mb-0" id="cache-hit-rate">0%</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-memory fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 快速操作 -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>快速操作</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="btn-group mb-3">
                                            <button class="btn btn-success" onclick="sqlRouterModule.refreshStats()">
                                                <i class="fas fa-sync"></i> 刷新统计
                                            </button>
                                            <button class="btn btn-warning" onclick="sqlRouterModule.clearCache()">
                                                <i class="fas fa-trash"></i> 清空缓存
                                            </button>
                                            <button class="btn btn-info" onclick="sqlRouterModule.exportQueries()">
                                                <i class="fas fa-download"></i> 导出配置
                                            </button>
                                        </div>
                                        
                                        <!-- 系统状态 -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>系统状态</h6>
                                                <div id="system-status"></div>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>缓存状态</h6>
                                                <div id="cache-status"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 查询列表面板 -->
                    <div id="queries-panel" class="content-panel" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5>查询配置列表</h5>
                                <div>
                                    <select class="form-select" id="category-filter" onchange="sqlRouterModule.filterQueries()">
                                        <option value="">所有类别</option>
                                    </select>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="queries-table">
                                        <thead>
                                            <tr>
                                                <th>查询名称</th>
                                                <th>类型</th>
                                                <th>描述</th>
                                                <th>缓存TTL</th>
                                                <th>租户过滤</th>
                                                <th>权限要求</th>
                                                <th>参数数量</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="queries-table-body">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 性能监控面板 -->
                    <div id="performance-panel" class="content-panel" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>最慢查询 TOP 10</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="slowest-queries"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>最频繁查询 TOP 10</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="most-used-queries"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>性能报告</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <select class="form-select" id="time-range-select" onchange="sqlRouterModule.updatePerformanceReport()">
                                                    <option value="1 hour">过去1小时</option>
                                                    <option value="6 hours">过去6小时</option>
                                                    <option value="1 day" selected>过去1天</option>
                                                    <option value="7 days">过去7天</option>
                                                </select>
                                            </div>
                                            <div class="col-md-9">
                                                <button class="btn btn-primary" onclick="sqlRouterModule.updatePerformanceReport()">
                                                    <i class="fas fa-sync"></i> 更新报告
                                                </button>
                                            </div>
                                        </div>
                                        <div id="performance-report" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 查询日志面板 -->
                    <div id="logs-panel" class="content-panel" style="display: none;">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5>查询日志</h5>
                                <div>
                                    <select class="form-select d-inline-block w-auto" id="log-time-range" onchange="sqlRouterModule.loadLogs()">
                                        <option value="1 hour" selected>过去1小时</option>
                                        <option value="6 hours">过去6小时</option>
                                        <option value="1 day">过去1天</option>
                                        <option value="7 days">过去7天</option>
                                    </select>
                                    <button class="btn btn-primary ms-2" onclick="sqlRouterModule.loadLogs()">
                                        <i class="fas fa-sync"></i> 刷新
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm" id="logs-table">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>查询名称</th>
                                                <th>用户</th>
                                                <th>执行时间</th>
                                                <th>状态</th>
                                                <th>IP地址</th>
                                            </tr>
                                        </thead>
                                        <tbody id="logs-table-body">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 初始化模块
     */
    init() {
        this.loadQueries();
        this.loadStats();
        this.setView('overview');
        
        // 设置自动刷新
        this.refreshInterval = setInterval(() => {
            if (this.currentView === 'overview') {
                this.loadStats();
            }
        }, 30000); // 30秒刷新一次
    }

    /**
     * 设置当前视图
     */
    setView(viewName) {
        this.currentView = viewName;
        
        // 隐藏所有面板
        document.querySelectorAll('.content-panel').forEach(panel => {
            panel.style.display = 'none';
        });
        
        // 显示当前面板
        const panel = document.getElementById(viewName + '-panel');
        if (panel) {
            panel.style.display = 'block';
        }
        
        // 更新按钮状态
        document.querySelectorAll('.btn-group .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // 根据视图加载相应数据
        switch (viewName) {
            case 'overview':
                this.loadStats();
                break;
            case 'queries':
                this.loadQueries();
                break;
            case 'performance':
                this.loadPerformanceReport();
                break;
            case 'logs':
                this.loadLogs();
                break;
        }
    }

    /**
     * 加载查询配置
     */
    async loadQueries() {
        try {
            const response = await fetch('/api/admin.php?action=sql_router_get_queries');
            const result = await response.json();
            
            if (result.success) {
                this.queries = result.data.queries;
                this.renderQueries();
                this.updateCategoryFilter(result.data.categories);
            } else {
                UIManager.showMessage('加载失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('加载查询配置失败:', error);
            UIManager.showMessage('加载查询配置失败', 'error');
        }
    }

    /**
     * 渲染查询列表
     */
    renderQueries() {
        const tbody = document.getElementById('queries-table-body');
        if (!tbody) return;
        
        tbody.innerHTML = this.queries.map(query => `
            <tr>
                <td><code>${query.name}</code></td>
                <td><span class="badge bg-${this.getTypeColor(query.type)}">${query.type}</span></td>
                <td>${query.description || '-'}</td>
                <td>${query.cache_ttl ? query.cache_ttl + 's' : '无缓存'}</td>
                <td>${query.tenant_filter ? '<i class="fas fa-check text-success"></i>' : '<i class="fas fa-times text-danger"></i>'}</td>
                <td>${query.permission_required || '无限制'}</td>
                <td>${query.param_count}/${query.optional_param_count}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="sqlRouterModule.viewQueryDetail('${query.name}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="sqlRouterModule.testQuery('${query.name}')">
                        <i class="fas fa-play"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 获取查询类型对应的颜色
     */
    getTypeColor(type) {
        const colors = {
            'select': 'primary',
            'insert': 'success',
            'update': 'warning',
            'delete': 'danger'
        };
        return colors[type] || 'secondary';
    }

    /**
     * 更新类别过滤器
     */
    updateCategoryFilter(categories) {
        const select = document.getElementById('category-filter');
        if (!select) return;
        
        const options = categories.map(category => 
            `<option value="${category}">${category}</option>`
        ).join('');
        
        select.innerHTML = '<option value="">所有类别</option>' + options;
    }

    /**
     * 过滤查询
     */
    filterQueries() {
        const category = document.getElementById('category-filter').value;
        const filteredQueries = category ? 
            this.queries.filter(q => q.name.startsWith(category + '.')) : 
            this.queries;
        
        // 临时更新查询列表
        const originalQueries = this.queries;
        this.queries = filteredQueries;
        this.renderQueries();
        this.queries = originalQueries;
    }

    /**
     * 加载统计数据
     */
    async loadStats() {
        try {
            const response = await fetch('/api/admin.php?action=sql_router_get_query_stats');
            const result = await response.json();
            
            if (result.success) {
                this.stats = result.data;
                this.updateStatsDisplay();
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 更新统计显示
     */
    updateStatsDisplay() {
        const summary = this.stats.summary || {};
        
        document.getElementById('total-queries').textContent = summary.total_queries || 0;
        document.getElementById('total-calls').textContent = summary.total_calls || 0;
        document.getElementById('avg-time').textContent = (summary.avg_time || 0).toFixed(2) + 'ms';
        
        // 计算缓存命中率（需要从详细统计中计算）
        const cacheHitRate = this.calculateCacheHitRate();
        document.getElementById('cache-hit-rate').textContent = cacheHitRate.toFixed(1) + '%';
        
        // 更新系统状态
        this.updateSystemStatus();
    }

    /**
     * 计算缓存命中率
     */
    calculateCacheHitRate() {
        const queries = this.stats.queries || {};
        let totalCalls = 0;
        let totalCacheHits = 0;
        
        Object.values(queries).forEach(query => {
            totalCalls += query.total_calls || 0;
            totalCacheHits += query.cache_hits || 0;
        });
        
        return totalCalls > 0 ? (totalCacheHits / totalCalls) * 100 : 0;
    }

    /**
     * 更新系统状态
     */
    updateSystemStatus() {
        const statusElement = document.getElementById('system-status');
        const cacheStatusElement = document.getElementById('cache-status');
        
        if (statusElement) {
            statusElement.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-circle text-success me-2"></i>
                    <span>系统运行正常</span>
                </div>
                <small class="text-muted">最后更新: ${new Date().toLocaleTimeString()}</small>
            `;
        }
        
        if (cacheStatusElement) {
            cacheStatusElement.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-memory text-info me-2"></i>
                    <span>缓存状态良好</span>
                </div>
                <small class="text-muted">命中率: ${this.calculateCacheHitRate().toFixed(1)}%</small>
            `;
        }
    }

    /**
     * 查看查询详情
     */
    async viewQueryDetail(queryName) {
        try {
            const response = await fetch(`/api/admin.php?action=sql_router_get_query_detail&query_name=${queryName}`);
            const result = await response.json();
            
            if (result.success) {
                const config = result.data.config;
                const html = `
                    <div class="modal fade" id="queryDetailModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">查询详情: ${queryName}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>基本信息</h6>
                                            <table class="table table-sm">
                                                <tr><td>类型</td><td>${config.type || 'select'}</td></tr>
                                                <tr><td>缓存TTL</td><td>${config.cache_ttl || 0}秒</td></tr>
                                                <tr><td>租户过滤</td><td>${config.tenant_filter ? '是' : '否'}</td></tr>
                                                <tr><td>权限要求</td><td>${config.permission_required || '无'}</td></tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>参数</h6>
                                            <p><strong>必需参数:</strong> ${(config.params || []).join(', ') || '无'}</p>
                                            <p><strong>可选参数:</strong> ${(config.optional_params || []).join(', ') || '无'}</p>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <h6>SQL语句</h6>
                                            <pre class="bg-light p-3"><code>${config.sql}</code></pre>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-md-12">
                                            <h6>描述</h6>
                                            <p>${config.description || '无描述'}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                document.body.insertAdjacentHTML('beforeend', html);
                const modal = new bootstrap.Modal(document.getElementById('queryDetailModal'));
                modal.show();
                
                // 清理模态框
                document.getElementById('queryDetailModal').addEventListener('hidden.bs.modal', function() {
                    this.remove();
                });
                
            } else {
                UIManager.showMessage('获取查询详情失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('获取查询详情失败:', error);
            UIManager.showMessage('获取查询详情失败', 'error');
        }
    }

    /**
     * 测试查询
     */
    testQuery(queryName) {
        // 简单的测试查询实现
        UIManager.showMessage(`测试查询 ${queryName} 功能正在开发中`, 'info');
    }

    /**
     * 刷新统计数据
     */
    refreshStats() {
        this.loadStats();
        UIManager.showMessage('统计数据已刷新', 'success');
    }

    /**
     * 清空缓存
     */
    async clearCache() {
        try {
            const response = await fetch('/api/admin.php?action=sql_router_clear_cache', {
                method: 'POST'
            });
            const result = await response.json();
            
            if (result.success) {
                UIManager.showMessage('缓存已清空', 'success');
                this.loadStats();
            } else {
                UIManager.showMessage('清空缓存失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('清空缓存失败:', error);
            UIManager.showMessage('清空缓存失败', 'error');
        }
    }

    /**
     * 导出查询配置
     */
    exportQueries() {
        const url = '/api/admin.php?action=sql_router_export_queries&format=json';
        const link = document.createElement('a');
        link.href = url;
        link.download = `sql_queries_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }

    /**
     * 加载性能报告
     */
    async loadPerformanceReport() {
        const timeRange = document.getElementById('time-range-select')?.value || '1 day';
        
        try {
            const response = await fetch(`/api/admin.php?action=sql_router_get_performance_report&time_range=${timeRange}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderPerformanceReport(result.data);
            }
        } catch (error) {
            console.error('加载性能报告失败:', error);
        }
    }

    /**
     * 渲染性能报告
     */
    renderPerformanceReport(data) {
        // 渲染最慢查询
        const slowestElement = document.getElementById('slowest-queries');
        if (slowestElement && data.slowest_queries) {
            const slowestHtml = Object.entries(data.slowest_queries).slice(0, 10).map(([name, stats]) => 
                `<div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-truncate">${name}</span>
                    <span class="badge bg-warning">${stats.avg_time.toFixed(2)}ms</span>
                </div>`
            ).join('');
            slowestElement.innerHTML = slowestHtml || '<p class="text-muted">暂无数据</p>';
        }
        
        // 渲染最频繁查询
        const mostUsedElement = document.getElementById('most-used-queries');
        if (mostUsedElement && data.most_used_queries) {
            const mostUsedHtml = Object.entries(data.most_used_queries).slice(0, 10).map(([name, stats]) => 
                `<div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-truncate">${name}</span>
                    <span class="badge bg-primary">${stats.total_calls}</span>
                </div>`
            ).join('');
            mostUsedElement.innerHTML = mostUsedHtml || '<p class="text-muted">暂无数据</p>';
        }
        
        // 渲染总体报告
        const reportElement = document.getElementById('performance-report');
        if (reportElement && data.summary) {
            const summary = data.summary;
            reportElement.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary">${summary.total_queries}</h4>
                            <p class="text-muted">总查询数</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">${summary.total_time.toFixed(2)}ms</h4>
                            <p class="text-muted">总执行时间</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning">${summary.avg_time.toFixed(2)}ms</h4>
                            <p class="text-muted">平均执行时间</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">${summary.time_range}</h4>
                            <p class="text-muted">统计时间范围</p>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 更新性能报告
     */
    updatePerformanceReport() {
        this.loadPerformanceReport();
    }

    /**
     * 加载查询日志
     */
    async loadLogs() {
        const timeRange = document.getElementById('log-time-range')?.value || '1 hour';
        
        try {
            const response = await fetch(`/api/admin.php?action=sql_router_get_sql_logs&time_range=${timeRange}&limit=100`);
            const result = await response.json();
            
            if (result.success) {
                this.renderLogs(result.data.logs);
            }
        } catch (error) {
            console.error('加载查询日志失败:', error);
        }
    }

    /**
     * 渲染查询日志
     */
    renderLogs(logs) {
        const tbody = document.getElementById('logs-table-body');
        if (!tbody) return;
        
        tbody.innerHTML = logs.map(log => `
            <tr>
                <td>${new Date(log.timestamp).toLocaleString()}</td>
                <td><code>${log.query_name}</code></td>
                <td>${log.user_type} (${log.user_id})</td>
                <td>${log.execute_time}ms</td>
                <td>${log.success ? '<span class="badge bg-success">成功</span>' : '<span class="badge bg-danger">失败</span>'}</td>
                <td>${log.ip_address}</td>
            </tr>
        `).join('');
    }

    /**
     * 销毁模块
     */
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

// 导出模块
window.sqlRouterModule = new SqlRouterModule();

console.log('SqlRouterModule 模块加载完成'); 