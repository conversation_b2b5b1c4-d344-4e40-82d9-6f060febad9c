<?php
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

$auth = TenantAuth::init();
if (!$auth->checkAuth()) {
    $auth->sendUnauthorized('请先登录');
    exit;
}

$currentUser = $auth->getCurrentUser();

// 验证商户权限
if ($currentUser['user_type'] !== 'merchant') {
    $auth->sendForbidden('无权限访问');
    exit;
}

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'generate':
            handleGenerateCode();
            break;
        case 'get_languages':
            handleGetLanguages();
            break;
        default:
            $auth->sendError('接口不存在', 404);
    }
} catch (Exception $e) {
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 生成代码
function handleGenerateCode() {
    global $auth, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证输入参数
    if (!isset($input['language']) || empty($input['language'])) {
        $auth->sendError('缺少参数：language', 400);
        return;
    }
    
    if (!isset($input['api_type']) || empty($input['api_type'])) {
        $auth->sendError('缺少参数：api_type', 400);
        return;
    }
    
    $allowedLanguages = ['php', 'java', 'python', 'nodejs', 'csharp', 'go'];
    if (!in_array($input['language'], $allowedLanguages)) {
        $auth->sendError('不支持的编程语言', 400);
        return;
    }
    
    $allowedApis = ['pay_get_qrcode', 'pay_get_orders_inquiry', 'pay_get_payment_quota', 'callback_verify'];
    if (!in_array($input['api_type'], $allowedApis)) {
        $auth->sendError('不支持的API类型', 400);
        return;
    }
    
    $language = $input['language'];
    $apiType = $input['api_type'];
    
    try {
        $code = generateCodeByLanguage($language, $apiType, $currentUser);
        
        $auth->logUserAction('code_generate', 'merchant_tool', 0, "生成代码: {$language} - {$apiType}");
        
        $auth->sendSuccess(array(
            'language' => $language,
            'api_type' => $apiType,
            'code' => $code,
            'filename' => getFilename($language, $apiType)
        ), '代码生成成功');
        
    } catch (Exception $e) {
        $auth->sendError('代码生成失败: ' . $e->getMessage(), 500);
    }
}

// 获取支持的语言列表
function handleGetLanguages() {
    global $auth;
    
    $languages = array(
        array(
            'key' => 'php',
            'name' => 'PHP',
            'icon' => 'php',
            'description' => 'PHP 5.6+ 兼容代码'
        ),
        array(
            'key' => 'java',
            'name' => 'Java',
            'icon' => 'java',
            'description' => 'Java 8+ 兼容代码'
        ),
        array(
            'key' => 'python',
            'name' => 'Python',
            'icon' => 'python',
            'description' => 'Python 2.7/3.x 兼容代码'
        ),
        array(
            'key' => 'nodejs',
            'name' => 'Node.js',
            'icon' => 'nodejs',
            'description' => 'Node.js ES5/ES6 代码'
        ),
        array(
            'key' => 'csharp',
            'name' => 'C#',
            'icon' => 'csharp',
            'description' => '.NET Framework/Core 代码'
        ),
        array(
            'key' => 'go',
            'name' => 'Go',
            'icon' => 'go',
            'description' => 'Go 1.11+ 模块代码'
        )
    );
    
    $auth->sendSuccess($languages, '语言列表获取成功');
}

// 根据语言生成代码
function generateCodeByLanguage($language, $apiType, $user) {
    switch ($language) {
        case 'php':
            return generatePHPCode($apiType, $user);
        case 'java':
            return generateJavaCode($apiType, $user);
        case 'python':
            return generatePythonCode($apiType, $user);
        case 'nodejs':
            return generateNodeJSCode($apiType, $user);
        case 'csharp':
            return generateCSharpCode($apiType, $user);
        case 'go':
            return generateGoCode($apiType, $user);
        default:
            throw new Exception('不支持的语言类型');
    }
}

// 生成PHP代码
function generatePHPCode($apiType, $user) {
    $developerId = $user['profile_id'];
    $developerKey = '您的API密钥';
    
    switch ($apiType) {
        case 'pay_get_qrcode':
            return "<?php
/**
 * PayPal支付系统 - 获取收款页接口 PHP示例
 * 开发者ID: {$developerId}
 */

class PayPalPayment {
    private \$developerId = '{$developerId}';
    private \$developerKey = '{$developerKey}';
    private \$apiUrl = 'https://qrcode.top670.com/api.php?do=pay_get_qrcode';
    
    /**
     * 生成签名
     */
    private function generateSign(\$params, \$timestamp) {
        // 移除签名相关字段
        unset(\$params['developer_id']);
        unset(\$params['timestamp']);
        unset(\$params['sign']);
        
        // 按键名升序排序
        ksort(\$params, SORT_STRING);
        
        // 拼接为 key=value 格式
        \$postArr = array();
        foreach (\$params as \$k => \$v) {
            \$postArr[] = \$k . '=' . \$v;
        }
        
        // 构建签名字符串
        \$postStr = implode('&', \$postArr);
        \$postStr .= '&' . \$this->developerId . '&' . \$this->developerKey . '&' . \$timestamp;
        
        // 计算SHA-256签名并转大写
        return strtoupper(hash('sha256', \$postStr));
    }
    
    /**
     * 获取收款页面
     */
    public function getQrcode(\$amount, \$productId, \$orderNo, \$notificationUrl = '') {
        \$timestamp = time();
        
        \$params = array(
            'amount' => \$amount,
            'type' => 2,
            'product_id' => \$productId,
            'order_no' => \$orderNo
        );
        
        if (!empty(\$notificationUrl)) {
            \$params['notification_url'] = \$notificationUrl;
        }
        
        // 生成签名
        \$sign = \$this->generateSign(\$params, \$timestamp);
        
        // 添加必要字段
        \$params['developer_id'] = \$this->developerId;
        \$params['timestamp'] = \$timestamp;
        \$params['sign'] = \$sign;
        
        // 发送POST请求
        \$ch = curl_init();
        curl_setopt(\$ch, CURLOPT_URL, \$this->apiUrl);
        curl_setopt(\$ch, CURLOPT_POST, true);
        curl_setopt(\$ch, CURLOPT_POSTFIELDS, http_build_query(\$params));
        curl_setopt(\$ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt(\$ch, CURLOPT_TIMEOUT, 30);
        curl_setopt(\$ch, CURLOPT_SSL_VERIFYPEER, false);
        
        \$response = curl_exec(\$ch);
        \$httpCode = curl_getinfo(\$ch, CURLINFO_HTTP_CODE);
        curl_close(\$ch);
        
        if (\$httpCode === 200) {
            return json_decode(\$response, true);
        } else {
            throw new Exception('API请求失败，HTTP状态码: ' . \$httpCode);
        }
    }
}

// 使用示例
try {
    \$payment = new PayPalPayment();
    \$result = \$payment->getQrcode(100.00, '产品ID', '订单号' . time());
    
    if (\$result['error_code'] === 0) {
        echo '收款页面URL: ' . \$result['url'];
    } else {
        echo '错误: ' . \$result['error_message'];
    }
} catch (Exception \$e) {
    echo '异常: ' . \$e->getMessage();
}
?>";
            
        case 'pay_get_orders_inquiry':
            return "<?php
/**
 * PayPal支付系统 - 查询订单接口 PHP示例
 * 开发者ID: {$developerId}
 */

class PayPalOrderQuery {
    private \$developerId = '{$developerId}';
    private \$developerKey = '{$developerKey}';
    private \$apiUrl = 'https://qrcode.top670.com/api.php?do=pay_get_orders_inquiry';
    
    /**
     * 生成签名
     */
    private function generateSign(\$params, \$timestamp) {
        unset(\$params['developer_id']);
        unset(\$params['timestamp']);
        unset(\$params['sign']);
        
        ksort(\$params, SORT_STRING);
        
        \$postArr = array();
        foreach (\$params as \$k => \$v) {
            \$postArr[] = \$k . '=' . \$v;
        }
        
        \$postStr = implode('&', \$postArr);
        \$postStr .= '&' . \$this->developerId . '&' . \$this->developerKey . '&' . \$timestamp;
        
        return strtoupper(hash('sha256', \$postStr));
    }
    
    /**
     * 查询订单状态
     */
    public function queryOrder(\$orderNo) {
        \$timestamp = time();
        
        \$params = array(
            'order_no' => \$orderNo
        );
        
        \$sign = \$this->generateSign(\$params, \$timestamp);
        
        \$params['developer_id'] = \$this->developerId;
        \$params['timestamp'] = \$timestamp;
        \$params['sign'] = \$sign;
        
        \$ch = curl_init();
        curl_setopt(\$ch, CURLOPT_URL, \$this->apiUrl);
        curl_setopt(\$ch, CURLOPT_POST, true);
        curl_setopt(\$ch, CURLOPT_POSTFIELDS, http_build_query(\$params));
        curl_setopt(\$ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt(\$ch, CURLOPT_TIMEOUT, 30);
        curl_setopt(\$ch, CURLOPT_SSL_VERIFYPEER, false);
        
        \$response = curl_exec(\$ch);
        \$httpCode = curl_getinfo(\$ch, CURLINFO_HTTP_CODE);
        curl_close(\$ch);
        
        if (\$httpCode === 200) {
            return json_decode(\$response, true);
        } else {
            throw new Exception('API请求失败，HTTP状态码: ' . \$httpCode);
        }
    }
}

// 使用示例
try {
    \$query = new PayPalOrderQuery();
    \$result = \$query->queryOrder('您的订单号');
    
    if (\$result['error_code'] === 0) {
        echo '订单状态: ' . \$result['order_data']['status'];
        echo '订单金额: ' . \$result['order_data']['amount'];
    } else {
        echo '错误: ' . \$result['error_message'];
    }
} catch (Exception \$e) {
    echo '异常: ' . \$e->getMessage();
}
?>";
            
        default:
            return "<?php\n// {$apiType} 的PHP代码示例\n// 开发者ID: {$developerId}\n// 请根据API文档实现具体逻辑\n?>";
    }
}

// 生成其他语言代码（简化版本）
function generateJavaCode($apiType, $user) {
    $developerId = $user['profile_id'];
    return "// Java代码示例 - {$apiType}\n// 开发者ID: {$developerId}\n// 请根据API文档实现具体逻辑";
}

function generatePythonCode($apiType, $user) {
    $developerId = $user['profile_id'];
    return "# Python代码示例 - {$apiType}\n# 开发者ID: {$developerId}\n# 请根据API文档实现具体逻辑";
}

function generateNodeJSCode($apiType, $user) {
    $developerId = $user['profile_id'];
    return "// Node.js代码示例 - {$apiType}\n// 开发者ID: {$developerId}\n// 请根据API文档实现具体逻辑";
}

function generateCSharpCode($apiType, $user) {
    $developerId = $user['profile_id'];
    return "// C#代码示例 - {$apiType}\n// 开发者ID: {$developerId}\n// 请根据API文档实现具体逻辑";
}

function generateGoCode($apiType, $user) {
    $developerId = $user['profile_id'];
    return "// Go代码示例 - {$apiType}\n// 开发者ID: {$developerId}\n// 请根据API文档实现具体逻辑";
}

// 获取文件名
function getFilename($language, $apiType) {
    $extensions = array(
        'php' => '.php',
        'java' => '.java',
        'python' => '.py',
        'nodejs' => '.js',
        'csharp' => '.cs',
        'go' => '.go'
    );
    
    return $apiType . '_example' . $extensions[$language];
}
?> 