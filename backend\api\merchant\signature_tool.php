<?php
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

$auth = TenantAuth::init();
if (!$auth->checkAuth()) {
    $auth->sendUnauthorized('请先登录');
    exit;
}

$db = $auth->getDatabase();
$currentUser = $auth->getCurrentUser();

// 验证商户权限
if ($currentUser['user_type'] !== 'merchant') {
    $auth->sendForbidden('无权限访问');
    exit;
}

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'generate_signature':
            handleGenerateSignature();
            break;
        case 'verify_signature':
            handleVerifySignature();
            break;
        case 'test_api':
            handleTestAPI();
            break;
        case 'get_merchant_keys':
            handleGetMerchantKeys();
            break;
        default:
            $auth->sendError('接口不存在', 404);
    }
} catch (Exception $e) {
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 生成签名
function handleGenerateSignature() {
    global $auth, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证输入参数
    if (!isset($input['params']) || !is_array($input['params'])) {
        $auth->sendError('缺少参数：params', 400);
        return;
    }
    
    if (!isset($input['developer_key']) || empty($input['developer_key'])) {
        $auth->sendError('缺少参数：developer_key', 400);
        return;
    }
    
    if (!isset($input['timestamp']) || !is_numeric($input['timestamp'])) {
        $auth->sendError('缺少参数：timestamp', 400);
        return;
    }
    
    $params = $input['params'];
    $developerKey = $input['developer_key'];
    $timestamp = $input['timestamp'];
    $developerId = $currentUser['profile_id']; // 商户ID
    
    try {
        // 生成签名
        $signature = generateSignature($params, $developerId, $developerKey, $timestamp);
        
        // 构建签名字符串用于调试
        $debugInfo = buildSignatureDebugInfo($params, $developerId, $developerKey, $timestamp);
        
        $auth->logUserAction('signature_generate', 'merchant_tool', 0, "生成API签名");
        
        $auth->sendSuccess(array(
            'signature' => $signature,
            'debug_info' => $debugInfo,
            'params_sorted' => $debugInfo['sorted_params'],
            'signature_string' => $debugInfo['signature_string']
        ), '签名生成成功');
        
    } catch (Exception $e) {
        $auth->sendError('签名生成失败: ' . $e->getMessage(), 500);
    }
}

// 验证签名
function handleVerifySignature() {
    global $auth, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证输入参数
    $required = ['params', 'developer_key', 'timestamp', 'expected_signature'];
    foreach ($required as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            $auth->sendError("缺少参数：{$field}", 400);
            return;
        }
    }
    
    $params = $input['params'];
    $developerKey = $input['developer_key'];
    $timestamp = $input['timestamp'];
    $expectedSignature = $input['expected_signature'];
    $developerId = $currentUser['profile_id'];
    
    try {
        // 生成签名
        $calculatedSignature = generateSignature($params, $developerId, $developerKey, $timestamp);
        
        // 验证签名
        $isValid = ($calculatedSignature === strtoupper($expectedSignature));
        
        // 构建调试信息
        $debugInfo = buildSignatureDebugInfo($params, $developerId, $developerKey, $timestamp);
        
        $auth->logUserAction('signature_verify', 'merchant_tool', 0, "验证API签名: " . ($isValid ? '成功' : '失败'));
        
        $auth->sendSuccess(array(
            'is_valid' => $isValid,
            'calculated_signature' => $calculatedSignature,
            'expected_signature' => strtoupper($expectedSignature),
            'debug_info' => $debugInfo,
            'message' => $isValid ? '签名验证成功' : '签名验证失败'
        ), '签名验证完成');
        
    } catch (Exception $e) {
        $auth->sendError('签名验证失败: ' . $e->getMessage(), 500);
    }
}

// 测试API接口
function handleTestAPI() {
    global $auth, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证输入参数
    if (!isset($input['api_type']) || empty($input['api_type'])) {
        $auth->sendError('缺少参数：api_type', 400);
        return;
    }
    
    $allowedApis = ['pay_get_qrcode', 'pay_get_orders_inquiry', 'pay_get_payment_quota'];
    if (!in_array($input['api_type'], $allowedApis)) {
        $auth->sendError('不支持的API类型', 400);
        return;
    }
    
    if (!isset($input['params']) || !is_array($input['params'])) {
        $auth->sendError('缺少参数：params', 400);
        return;
    }
    
    if (!isset($input['developer_key']) || empty($input['developer_key'])) {
        $auth->sendError('缺少参数：developer_key', 400);
        return;
    }
    
    $apiType = $input['api_type'];
    $params = $input['params'];
    $developerKey = $input['developer_key'];
    $developerId = $currentUser['profile_id'];
    
    try {
        // 添加必要的系统参数
        $timestamp = time();
        $params['developer_id'] = $developerId;
        $params['timestamp'] = $timestamp;
        
        // 生成签名
        $signature = generateSignature($params, $developerId, $developerKey, $timestamp);
        $params['sign'] = $signature;
        
        // 构建API URL
        $apiUrls = array(
            'pay_get_qrcode' => 'https://qrcode.top670.com/api.php?do=pay_get_qrcode',
            'pay_get_orders_inquiry' => 'https://qrcode.top670.com/api.php?do=pay_get_orders_inquiry',
            'pay_get_payment_quota' => 'https://qrcode.top670.com/api.php?do=pay_get_payment_quota'
        );
        
        $apiUrl = $apiUrls[$apiType];
        
        // 发送API请求
        $response = sendAPIRequest($apiUrl, $params);
        
        $auth->logUserAction('api_test', 'merchant_tool', 0, "测试API接口: {$apiType}");
        
        $auth->sendSuccess(array(
            'api_type' => $apiType,
            'request_url' => $apiUrl,
            'request_params' => $params,
            'response' => $response,
            'signature_info' => array(
                'timestamp' => $timestamp,
                'signature' => $signature
            )
        ), 'API测试完成');
        
    } catch (Exception $e) {
        $auth->sendError('API测试失败: ' . $e->getMessage(), 500);
    }
}

// 获取商户密钥信息
function handleGetMerchantKeys() {
    global $auth, $db, $currentUser;
    
    try {
        $merchant = $db->fetch("SELECT id, api_key, api_secret FROM merchants WHERE id = ?", array($currentUser['profile_id']));
        
        if (!$merchant) {
            $auth->sendError('商户信息不存在', 404);
            return;
        }
        
        $auth->sendSuccess(array(
            'developer_id' => $merchant['id'],
            'api_key' => $merchant['api_key'],
            'api_secret' => $merchant['api_secret'] ? '已设置' : '未设置'
        ), '密钥信息获取成功');
        
    } catch (Exception $e) {
        $auth->sendError('获取密钥信息失败: ' . $e->getMessage(), 500);
    }
}

// 构建签名调试信息
function buildSignatureDebugInfo($params, $developerId, $developerKey, $timestamp) {
    // 移除签名相关字段
    $signParams = $params;
    unset($signParams['developer_id']);
    unset($signParams['timestamp']);
    unset($signParams['sign']);
    
    // 按键名升序排序
    ksort($signParams, SORT_STRING);
    
    // 拼接为 key=value 格式
    $postArr = array();
    foreach ($signParams as $k => $v) {
        $postArr[] = $k . '=' . $v;
    }
    
    // 构建签名字符串
    $postStr = implode('&', $postArr);
    $signatureString = $postStr . '&' . $developerId . '&' . $developerKey . '&' . $timestamp;
    
    return array(
        'sorted_params' => $signParams,
        'param_string' => $postStr,
        'signature_string' => $signatureString,
        'developer_id' => $developerId,
        'timestamp' => $timestamp
    );
}

// 生成签名
function generateSignature($params, $developerId, $developerKey, $timestamp) {
    // 移除签名相关字段
    $signParams = $params;
    unset($signParams['developer_id']);
    unset($signParams['timestamp']);
    unset($signParams['sign']);
    
    // 按键名升序排序
    ksort($signParams, SORT_STRING);
    
    // 拼接为 key=value 格式
    $postArr = array();
    foreach ($signParams as $k => $v) {
        $postArr[] = $k . '=' . $v;
    }
    
    // 构建签名字符串
    $postStr = implode('&', $postArr);
    $postStr .= '&' . $developerId . '&' . $developerKey . '&' . $timestamp;
    
    // 计算SHA-256签名并转大写
    return strtoupper(hash('sha256', $postStr));
}

// 发送API请求
function sendAPIRequest($url, $params) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'PayPal-Merchant-Tool/1.0');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception('请求失败: ' . $error);
    }
    
    if ($httpCode !== 200) {
        throw new Exception('HTTP错误: ' . $httpCode);
    }
    
    return json_decode($response, true) ?: $response;
}
?> 