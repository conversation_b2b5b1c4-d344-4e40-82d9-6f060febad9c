<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';

$db = new Database();
$auth = new Auth();

// 路由处理
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    // 获取认证用户信息
    $token = $auth->getAuthToken();
    $currentUser = $auth->getCurrentUser($token);
    
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(array(
            'error_code' => 401,
            'error_message' => '未授权访问，请先登录',
            'data' => null
        ));
        exit;
    }
    
    // 检查设备管理权限
    if (!$auth->hasPermission($currentUser, 'manage_devices') && 
        !$auth->hasPermission($currentUser, 'view_devices') && 
        $currentUser['user_type'] !== 'admin') {
        http_response_code(403);
        echo json_encode(array(
            'error_code' => 403,
            'error_message' => '无权限访问设备管理功能',
            'data' => null
        ));
        exit;
    }

    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'list':
                    handleDeviceList($currentUser);
                    break;
                case 'detail':
                    handleDeviceDetail($currentUser);
                    break;
                case 'stats':
                    handleDeviceStats($currentUser);
                    break;
                case 'checkin_status':
                    handleCheckinStatus($currentUser);
                    break;
                default:
                    throw new Exception('不支持的GET操作');
            }
            break;
            
        case 'POST':
            switch ($action) {
                case 'approve':
                    handleDeviceApprove($currentUser);
                    break;
                case 'reject':
                    handleDeviceReject($currentUser);
                    break;
                case 'assign_group':
                    handleAssignGroup($currentUser);
                    break;
                case 'update_status':
                    handleUpdateStatus($currentUser);
                    break;
                case 'batch_operation':
                    handleBatchOperation($currentUser);
                    break;
                default:
                    throw new Exception('不支持的POST操作');
            }
            break;
            
        default:
            throw new Exception('不支持的HTTP方法');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'error_code' => 400,
        'error_message' => $e->getMessage(),
        'data' => null
    ));
}

// 获取设备列表（支持筛选和分页）
function handleDeviceList($currentUser) {
    global $db, $auth;
    
    // 分页参数
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(10, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    // 筛选参数
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $groupId = isset($_GET['group_id']) ? intval($_GET['group_id']) : 0;
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $checkinStatus = isset($_GET['checkin_status']) ? $_GET['checkin_status'] : '';
    
    // 构建查询条件
    $where = array();
    $params = array();
    
    // 根据用户类型限制数据访问范围
    if ($currentUser['user_type'] === 'provider') {
        $where[] = "d.provider_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'employee') {
        // 员工只能看到自己管理的设备
        $jobPosition = getEmployeePosition($currentUser['id']);
        if ($jobPosition === 'team_leader') {
            // 小组长只能看自己小组的设备
            $managedGroups = getManagedGroups($currentUser['id']);
            if (!empty($managedGroups)) {
                $placeholders = str_repeat('?,', count($managedGroups) - 1) . '?';
                $where[] = "d.group_id IN ($placeholders)";
                $params = array_merge($params, $managedGroups);
            } else {
                $where[] = "1 = 0"; // 没有管理的小组
            }
        } elseif ($jobPosition === 'group_manager') {
            // 大组长能看下属小组长管理的所有设备
            $managedGroups = getAllManagedGroups($currentUser['id']);
            if (!empty($managedGroups)) {
                $placeholders = str_repeat('?,', count($managedGroups) - 1) . '?';
                $where[] = "d.group_id IN ($placeholders)";
                $params = array_merge($params, $managedGroups);
            } else {
                $where[] = "1 = 0";
            }
        }
    }
    
    // 状态筛选
    if ($status) {
        $where[] = "d.status = ?";
        $params[] = $status;
    }
    
    // 小组筛选
    if ($groupId > 0) {
        $where[] = "d.group_id = ?";
        $params[] = $groupId;
    }
    
    // 搜索条件（设备ID、设备名称、品牌、型号）
    if ($search) {
        $where[] = "(d.device_id LIKE ? OR d.device_name LIKE ? OR d.device_brand LIKE ? OR d.device_model LIKE ?)";
        $searchParam = '%' . $search . '%';
        $params[] = $searchParam;
        $params[] = $searchParam;
        $params[] = $searchParam;
        $params[] = $searchParam;
    }
    
    // 签到状态筛选
    if ($checkinStatus) {
        $where[] = "d.checkin_status = ?";
        $params[] = $checkinStatus;
    }
    
    $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
    
    // 查询设备列表
    $sql = "SELECT d.*, 
                   p.company_name as provider_name,
                   g.group_code,
                   g.team_leader_id,
                   g.group_manager_id,
                   u1.real_name as team_leader_name,
                   u2.real_name as group_manager_name,
                   u3.real_name as approved_by_name
            FROM devices d
            LEFT JOIN payment_providers p ON d.provider_id = p.id
            LEFT JOIN provider_groups g ON d.group_id = g.id
            LEFT JOIN users u1 ON g.team_leader_id = u1.id
            LEFT JOIN users u2 ON g.group_manager_id = u2.id
            LEFT JOIN users u3 ON d.approved_by = u3.id
            $whereClause
            ORDER BY d.created_at DESC
            LIMIT $limit OFFSET $offset";
    
    $devices = $db->fetchAll($sql, $params);
    
    // 查询总数
    $countSql = "SELECT COUNT(*) as total FROM devices d
                 LEFT JOIN payment_providers p ON d.provider_id = p.id
                 LEFT JOIN provider_groups g ON d.group_id = g.id
                 $whereClause";
    $totalResult = $db->fetch($countSql, $params);
    $total = $totalResult['total'];
    
    // 计算签到状态
    foreach ($devices as &$device) {
        $device['checkin_info'] = calculateCheckinInfo($device);
    }
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'devices' => $devices,
            'pagination' => array(
                'page' => $page,
                'limit' => $limit,
                'total' => intval($total),
                'pages' => ceil($total / $limit)
            )
        )
    ));
}

// 获取设备详情
function handleDeviceDetail($currentUser) {
    global $db;
    
    $deviceId = isset($_GET['device_id']) ? $_GET['device_id'] : '';
    if (!$deviceId) {
        throw new Exception('缺少设备ID参数');
    }
    
    // 构建权限检查条件
    $whereClause = "d.device_id = ?";
    $params = array($deviceId);
    
    if ($currentUser['user_type'] === 'provider') {
        $whereClause .= " AND d.provider_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'employee') {
        $managedGroups = getAllManagedGroups($currentUser['id']);
        if (!empty($managedGroups)) {
            $placeholders = str_repeat('?,', count($managedGroups) - 1) . '?';
            $whereClause .= " AND d.group_id IN ($placeholders)";
            $params = array_merge($params, $managedGroups);
        } else {
            $whereClause .= " AND 1 = 0";
        }
    }
    
    $device = $db->fetch(
        "SELECT d.*, 
                p.company_name as provider_name,
                g.group_code,
                g.team_leader_id,
                g.group_manager_id,
                u1.real_name as team_leader_name,
                u2.real_name as group_manager_name,
                u3.real_name as approved_by_name,
                u4.real_name as rejected_by_name
         FROM devices d
         LEFT JOIN payment_providers p ON d.provider_id = p.id
         LEFT JOIN provider_groups g ON d.group_id = g.id
         LEFT JOIN users u1 ON g.team_leader_id = u1.id
         LEFT JOIN users u2 ON g.group_manager_id = u2.id
         LEFT JOIN users u3 ON d.approved_by = u3.id
         LEFT JOIN users u4 ON d.rejected_by = u4.id
         WHERE $whereClause",
        $params
    );
    
    if (!$device) {
        throw new Exception('设备不存在或无权限访问');
    }
    
    // 获取设备日志
    $logs = $db->fetchAll(
        "SELECT dl.*, u.real_name as operator_name
         FROM device_logs dl
         LEFT JOIN users u ON dl.user_id = u.id
         WHERE dl.device_id = ?
         ORDER BY dl.created_at DESC
         LIMIT 50",
        array($device['id'])
    );
    
    // 计算签到信息
    $device['checkin_info'] = calculateCheckinInfo($device);
    $device['logs'] = $logs;
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => $device
    ));
}

// 获取设备统计信息
function handleDeviceStats($currentUser) {
    global $db;
    
    // 构建权限查询条件
    $whereClause = '';
    $params = array();
    
    if ($currentUser['user_type'] === 'provider') {
        $whereClause = "WHERE d.provider_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'employee') {
        $managedGroups = getAllManagedGroups($currentUser['id']);
        if (!empty($managedGroups)) {
            $placeholders = str_repeat('?,', count($managedGroups) - 1) . '?';
            $whereClause = "WHERE d.group_id IN ($placeholders)";
            $params = $managedGroups;
        } else {
            $whereClause = "WHERE 1 = 0";
        }
    }
    
    // 统计各状态设备数量
    $stats = $db->fetchAll(
        "SELECT d.status, COUNT(*) as count
         FROM devices d
         $whereClause
         GROUP BY d.status",
        $params
    );
    
    // 统计签到状态
    $checkinStats = $db->fetchAll(
        "SELECT d.checkin_status, COUNT(*) as count
         FROM devices d
         $whereClause
         GROUP BY d.checkin_status",
        $params
    );
    
    // 按小组统计
    $groupStats = $db->fetchAll(
        "SELECT g.group_code, g.id as group_id, COUNT(d.id) as device_count
         FROM provider_groups g
         LEFT JOIN devices d ON g.id = d.group_id
         WHERE g.provider_id = ?
         GROUP BY g.id
         ORDER BY device_count DESC",
        array($currentUser['profile_id'])
    );
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'status_stats' => $stats,
            'checkin_stats' => $checkinStats,
            'group_stats' => $groupStats
        )
    ));
}

// 获取签到状态监控
function handleCheckinStatus($currentUser) {
    global $db;
    
    // 构建权限查询条件
    $whereClause = '';
    $params = array();
    
    if ($currentUser['user_type'] === 'provider') {
        $whereClause = "WHERE d.provider_id = ? AND d.status = 'active'";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'employee') {
        $managedGroups = getAllManagedGroups($currentUser['id']);
        if (!empty($managedGroups)) {
            $placeholders = str_repeat('?,', count($managedGroups) - 1) . '?';
            $whereClause = "WHERE d.group_id IN ($placeholders) AND d.status = 'active'";
            $params = $managedGroups;
        } else {
            $whereClause = "WHERE 1 = 0";
        }
    } else {
        $whereClause = "WHERE d.status = 'active'";
    }
    
    // 查询需要签到监控的设备
    $devices = $db->fetchAll(
        "SELECT d.device_id, d.device_name, d.last_checkin_time, d.checkin_status,
                g.group_code, p.company_name as provider_name
         FROM devices d
         LEFT JOIN provider_groups g ON d.group_id = g.id
         LEFT JOIN payment_providers p ON d.provider_id = p.id
         $whereClause
         ORDER BY d.last_checkin_time ASC",
        $params
    );
    
    $overdueDevices = array();
    $normalDevices = array();
    $currentTime = time();
    
    foreach ($devices as $device) {
        $checkinInfo = calculateCheckinInfo($device);
        $device['checkin_info'] = $checkinInfo;
        
        if ($checkinInfo['is_overdue']) {
            $overdueDevices[] = $device;
        } else {
            $normalDevices[] = $device;
        }
    }
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'overdue_devices' => $overdueDevices,
            'normal_devices' => $normalDevices,
            'summary' => array(
                'total_devices' => count($devices),
                'overdue_count' => count($overdueDevices),
                'normal_count' => count($normalDevices)
            )
        )
    ));
}

// 设备审核通过
function handleDeviceApprove($currentUser) {
    global $db, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_devices') && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限进行设备审核操作');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['device_id'])) {
        throw new Exception('缺少设备ID参数');
    }
    
    $deviceId = $input['device_id'];
    $providerId = isset($input['provider_id']) ? intval($input['provider_id']) : 0;
    $groupId = isset($input['group_id']) ? intval($input['group_id']) : 0;
    $remarks = isset($input['remarks']) ? trim($input['remarks']) : '';
    
    // 检查设备是否存在
    $device = $db->fetch("SELECT * FROM devices WHERE device_id = ?", array($deviceId));
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    // 检查权限
    if ($currentUser['user_type'] === 'provider' && $providerId !== $currentUser['profile_id']) {
        throw new Exception('只能审核自己码商下的设备');
    }
    
    $db->beginTransaction();
    try {
        // 更新设备状态
        $db->execute(
            "UPDATE devices SET 
                status = 'active',
                provider_id = ?,
                group_id = ?,
                approved_by = ?,
                approved_at = NOW(),
                rejection_reason = NULL,
                rejected_by = NULL,
                rejected_at = NULL
             WHERE device_id = ?",
            array($providerId, $groupId, $currentUser['id'], $deviceId)
        );
        
        // 记录日志
        $auth->logAction($currentUser['id'], 'approve_device', 'device', $device['id'], "审核通过设备: $deviceId, 备注: $remarks");
        
        // 记录设备日志
        $db->execute(
            "INSERT INTO device_logs (device_id, user_id, log_type, log_level, message, created_at)
             VALUES (?, ?, 'approval', 'info', ?, NOW())",
            array($device['id'], $currentUser['id'], "设备审核通过 - $remarks")
        );
        
        $db->commit();
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array('message' => '设备审核通过')
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 设备审核拒绝
function handleDeviceReject($currentUser) {
    global $db, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_devices') && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限进行设备审核操作');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['device_id']) || !isset($input['reason'])) {
        throw new Exception('缺少必要参数（device_id, reason）');
    }
    
    $deviceId = $input['device_id'];
    $reason = trim($input['reason']);
    
    if (empty($reason)) {
        throw new Exception('拒绝原因不能为空');
    }
    
    // 检查设备是否存在
    $device = $db->fetch("SELECT * FROM devices WHERE device_id = ?", array($deviceId));
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    $db->beginTransaction();
    try {
        // 更新设备状态
        $db->execute(
            "UPDATE devices SET 
                status = 'rejected',
                rejected_by = ?,
                rejected_at = NOW(),
                rejection_reason = ?,
                approved_by = NULL,
                approved_at = NULL
             WHERE device_id = ?",
            array($currentUser['id'], $reason, $deviceId)
        );
        
        // 记录日志
        $auth->logAction($currentUser['id'], 'reject_device', 'device', $device['id'], "拒绝设备: $deviceId, 原因: $reason");
        
        // 记录设备日志
        $db->execute(
            "INSERT INTO device_logs (device_id, user_id, log_type, log_level, message, created_at)
             VALUES (?, ?, 'rejection', 'warning', ?, NOW())",
            array($device['id'], $currentUser['id'], "设备审核拒绝 - $reason")
        );
        
        $db->commit();
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array('message' => '设备审核拒绝')
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 分配设备到小组
function handleAssignGroup($currentUser) {
    global $db, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_devices') && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限进行设备分配操作');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['device_id']) || !isset($input['group_id'])) {
        throw new Exception('缺少必要参数（device_id, group_id）');
    }
    
    $deviceId = $input['device_id'];
    $groupId = intval($input['group_id']);
    
    // 检查设备是否存在
    $device = $db->fetch("SELECT * FROM devices WHERE device_id = ?", array($deviceId));
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    // 检查小组是否存在
    $group = $db->fetch("SELECT * FROM provider_groups WHERE id = ?", array($groupId));
    if (!$group) {
        throw new Exception('小组不存在');
    }
    
    // 检查权限
    if ($currentUser['user_type'] === 'provider' && $group['provider_id'] !== $currentUser['profile_id']) {
        throw new Exception('只能分配设备到自己的小组');
    }
    
    $db->beginTransaction();
    try {
        // 更新设备的小组归属
        $db->execute(
            "UPDATE devices SET group_id = ? WHERE device_id = ?",
            array($groupId, $deviceId)
        );
        
        // 记录日志
        $auth->logAction($currentUser['id'], 'assign_group', 'device', $device['id'], "将设备 $deviceId 分配到小组 {$group['group_code']}");
        
        // 记录设备日志
        $db->execute(
            "INSERT INTO device_logs (device_id, user_id, log_type, log_level, message, created_at)
             VALUES (?, ?, 'assignment', 'info', ?, NOW())",
            array($device['id'], $currentUser['id'], "设备分配到小组: {$group['group_code']}")
        );
        
        $db->commit();
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array('message' => '设备分配成功')
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 更新设备状态
function handleUpdateStatus($currentUser) {
    global $db, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_devices') && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限进行设备状态管理操作');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['device_id']) || !isset($input['status'])) {
        throw new Exception('缺少必要参数（device_id, status）');
    }
    
    $deviceId = $input['device_id'];
    $status = $input['status'];
    $reason = isset($input['reason']) ? trim($input['reason']) : '';
    
    $allowedStatuses = array('active', 'inactive', 'maintenance', 'banned');
    if (!in_array($status, $allowedStatuses)) {
        throw new Exception('无效的设备状态');
    }
    
    // 检查设备是否存在
    $device = $db->fetch("SELECT * FROM devices WHERE device_id = ?", array($deviceId));
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    $db->beginTransaction();
    try {
        // 更新设备状态
        $db->execute(
            "UPDATE devices SET status = ? WHERE device_id = ?",
            array($status, $deviceId)
        );
        
        // 记录日志
        $auth->logAction($currentUser['id'], 'update_status', 'device', $device['id'], "更新设备状态: $deviceId -> $status, 原因: $reason");
        
        // 记录设备日志
        $db->execute(
            "INSERT INTO device_logs (device_id, user_id, log_type, log_level, message, created_at)
             VALUES (?, ?, 'status_change', 'info', ?, NOW())",
            array($device['id'], $currentUser['id'], "设备状态更新为: $status - $reason")
        );
        
        $db->commit();
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array('message' => '设备状态更新成功')
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 批量操作
function handleBatchOperation($currentUser) {
    global $db, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_devices') && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限进行批量操作');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['device_ids']) || !isset($input['operation'])) {
        throw new Exception('缺少必要参数（device_ids, operation）');
    }
    
    $deviceIds = $input['device_ids'];
    $operation = $input['operation'];
    $params = isset($input['params']) ? $input['params'] : array();
    
    if (!is_array($deviceIds) || empty($deviceIds)) {
        throw new Exception('设备ID列表不能为空');
    }
    
    $db->beginTransaction();
    try {
        $successCount = 0;
        $errors = array();
        
        foreach ($deviceIds as $deviceId) {
            try {
                switch ($operation) {
                    case 'approve':
                        batchApproveDevice($deviceId, $params, $currentUser);
                        break;
                    case 'reject':
                        batchRejectDevice($deviceId, $params, $currentUser);
                        break;
                    case 'assign_group':
                        batchAssignGroup($deviceId, $params, $currentUser);
                        break;
                    case 'update_status':
                        batchUpdateStatus($deviceId, $params, $currentUser);
                        break;
                    default:
                        throw new Exception('不支持的批量操作');
                }
                $successCount++;
            } catch (Exception $e) {
                $errors[] = "设备 $deviceId: " . $e->getMessage();
            }
        }
        
        $db->commit();
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array(
                'success_count' => $successCount,
                'total_count' => count($deviceIds),
                'errors' => $errors
            )
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 辅助函数：计算签到信息
function calculateCheckinInfo($device) {
    $checkinInterval = 7200; // 2小时 = 7200秒
    $currentTime = time();
    
    if (!$device['last_checkin_time']) {
        return array(
            'status' => 'never',
            'is_overdue' => true,
            'next_checkin_time' => null,
            'overdue_minutes' => null,
            'message' => '从未签到'
        );
    }
    
    $lastCheckinTime = strtotime($device['last_checkin_time']);
    $nextCheckinTime = $lastCheckinTime + $checkinInterval;
    
    if ($currentTime > $nextCheckinTime) {
        $overdueSeconds = $currentTime - $nextCheckinTime;
        $overdueMinutes = floor($overdueSeconds / 60);
        
        return array(
            'status' => 'overdue',
            'is_overdue' => true,
            'next_checkin_time' => date('Y-m-d H:i:s', $nextCheckinTime),
            'overdue_minutes' => $overdueMinutes,
            'message' => "已超时 {$overdueMinutes} 分钟"
        );
    } else {
        $remainingSeconds = $nextCheckinTime - $currentTime;
        $remainingMinutes = floor($remainingSeconds / 60);
        
        return array(
            'status' => 'normal',
            'is_overdue' => false,
            'next_checkin_time' => date('Y-m-d H:i:s', $nextCheckinTime),
            'remaining_minutes' => $remainingMinutes,
            'message' => "还有 {$remainingMinutes} 分钟需签到"
        );
    }
}

// 辅助函数：获取员工职位
function getEmployeePosition($userId) {
    global $db;
    
    $result = $db->fetch(
        "SELECT jp.position_name FROM users u
         LEFT JOIN job_positions jp ON u.job_position_id = jp.id
         WHERE u.id = ?",
        array($userId)
    );
    
    return $result ? $result['position_name'] : null;
}

// 辅助函数：获取管理的小组ID列表（小组长）
function getManagedGroups($userId) {
    global $db;
    
    $groups = $db->fetchAll(
        "SELECT id FROM provider_groups WHERE team_leader_id = ?",
        array($userId)
    );
    
    return array_column($groups, 'id');
}

// 辅助函数：获取所有管理的小组ID列表（包括大组长管理的所有小组）
function getAllManagedGroups($userId) {
    global $db;
    
    $position = getEmployeePosition($userId);
    
    if ($position === 'team_leader') {
        return getManagedGroups($userId);
    } elseif ($position === 'group_manager') {
        // 大组长能管理所有下属小组长的小组
        $groups = $db->fetchAll(
            "SELECT id FROM provider_groups WHERE group_manager_id = ?",
            array($userId)
        );
        return array_column($groups, 'id');
    }
    
    return array();
}

// 批量操作辅助函数
function batchApproveDevice($deviceId, $params, $currentUser) {
    global $db, $auth;
    
    $providerId = isset($params['provider_id']) ? intval($params['provider_id']) : 0;
    $groupId = isset($params['group_id']) ? intval($params['group_id']) : 0;
    
    $device = $db->fetch("SELECT * FROM devices WHERE device_id = ?", array($deviceId));
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    $db->execute(
        "UPDATE devices SET 
            status = 'active',
            provider_id = ?,
            group_id = ?,
            approved_by = ?,
            approved_at = NOW()
         WHERE device_id = ?",
        array($providerId, $groupId, $currentUser['id'], $deviceId)
    );
}

function batchRejectDevice($deviceId, $params, $currentUser) {
    global $db;
    
    $reason = isset($params['reason']) ? $params['reason'] : '批量拒绝';
    
    $device = $db->fetch("SELECT * FROM devices WHERE device_id = ?", array($deviceId));
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    $db->execute(
        "UPDATE devices SET 
            status = 'rejected',
            rejected_by = ?,
            rejected_at = NOW(),
            rejection_reason = ?
         WHERE device_id = ?",
        array($currentUser['id'], $reason, $deviceId)
    );
}

function batchAssignGroup($deviceId, $params, $currentUser) {
    global $db;
    
    $groupId = isset($params['group_id']) ? intval($params['group_id']) : 0;
    
    if ($groupId === 0) {
        throw new Exception('缺少小组ID参数');
    }
    
    $device = $db->fetch("SELECT * FROM devices WHERE device_id = ?", array($deviceId));
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    $db->execute(
        "UPDATE devices SET group_id = ? WHERE device_id = ?",
        array($groupId, $deviceId)
    );
}

function batchUpdateStatus($deviceId, $params, $currentUser) {
    global $db;
    
    $status = isset($params['status']) ? $params['status'] : '';
    
    $allowedStatuses = array('active', 'inactive', 'maintenance', 'banned');
    if (!in_array($status, $allowedStatuses)) {
        throw new Exception('无效的设备状态');
    }
    
    $device = $db->fetch("SELECT * FROM devices WHERE device_id = ?", array($deviceId));
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    $db->execute(
        "UPDATE devices SET status = ? WHERE device_id = ?",
        array($status, $deviceId)
    );
}

?>
</rewritten_file>