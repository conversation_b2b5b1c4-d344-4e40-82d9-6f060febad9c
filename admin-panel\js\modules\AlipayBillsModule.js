/**
 * 支付宝账单管理模块 - 码商专用
 * 负责支付宝账单数据的查看、匹配和管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

class AlipayBillsModule {
    constructor() {
        this.apiClient = null;
        this.utils = null;
        this.authManager = null;
        this.tenantInfo = null;
        
        this.currentPage = 1;
        this.pageSize = 20;
        this.bills = [];
        this.selectedBills = [];
        this.currentFilters = {};
        
        console.log('📱 AlipayBillsModule initialized');
    }

    /**
     * 初始化模块
     */
    async init(dependencies) {
        this.apiClient = dependencies.apiClient;
        this.utils = dependencies.utils;
        this.authManager = dependencies.authManager;
        this.tenantInfo = dependencies.tenantInfo;
        
        console.log('✅ 支付宝账单模块初始化完成');
    }

    /**
     * 渲染支付宝账单管理页面
     */
    async render(container, params = {}) {
        try {
            const { role } = params;
            
            container.innerHTML = `
                <div class="alipay-bills-module">
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2><i class="bi bi-credit-card me-2"></i>支付宝账单管理</h2>
                                <p class="text-muted mb-0">管理设备上报的支付宝账单数据，处理匹配状态</p>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-primary" onclick="alipayBillsModule.showManualMatchModal()">
                                    <i class="bi bi-link-45deg me-2"></i>手动匹配
                                </button>
                                <button class="btn btn-outline-info" onclick="alipayBillsModule.refreshBills()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>刷新
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 账单统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="stat-card">
                                <div class="stat-icon bg-primary">
                                    <i class="bi bi-receipt"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="totalBillsCount">0</div>
                                    <div class="stat-label">总账单数</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="stat-card">
                                <div class="stat-icon bg-success">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="matchedBillsCount">0</div>
                                    <div class="stat-label">已匹配</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="stat-card">
                                <div class="stat-icon bg-warning">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="unmatchedBillsCount">0</div>
                                    <div class="stat-label">未匹配</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="stat-card">
                                <div class="stat-icon bg-secondary">
                                    <i class="bi bi-eye-slash"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="ignoredBillsCount">0</div>
                                    <div class="stat-label">已忽略</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选和搜索 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-funnel me-2"></i>筛选条件
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">匹配状态</label>
                                    <select class="form-select" id="filterMatchStatus">
                                        <option value="">全部状态</option>
                                        <option value="unmatched">未匹配</option>
                                        <option value="matched">已匹配</option>
                                        <option value="manual">手动处理</option>
                                        <option value="ignored">已忽略</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">开始日期</label>
                                    <input type="date" class="form-control" id="filterStartDate">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">结束日期</label>
                                    <input type="date" class="form-control" id="filterEndDate">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">操作</label>
                                    <div class="d-grid">
                                        <button class="btn btn-primary" onclick="alipayBillsModule.applyFilters()">
                                            <i class="bi bi-search me-2"></i>筛选
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 账单列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-list me-2"></i>账单列表
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>账单ID</th>
                                            <th>交易时间</th>
                                            <th>金额</th>
                                            <th>交易对方</th>
                                            <th>匹配状态</th>
                                            <th>关联订单</th>
                                            <th>设备</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="billsTableBody">
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                <div class="mt-2">正在加载账单数据...</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 -->
                            <nav aria-label="Page navigation" id="paginationContainer">
                                <!-- 分页内容将在这里动态生成 -->
                            </nav>
                        </div>
                    </div>
                </div>

                <!-- 账单详情模态框 -->
                <div class="modal fade" id="billDetailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">账单详情</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body" id="billDetailContent">
                                <!-- 账单详情内容 -->
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 初始化页面
            await this.initializePage();
            
        } catch (error) {
            console.error('❌ 支付宝账单模块渲染失败:', error);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h4>加载失败</h4>
                    <p>支付宝账单模块加载失败: ${error.message}</p>
                </div>
            `;
        }
    }

    /**
     * 初始化页面
     */
    async initializePage() {
        try {
            // 加载账单数据
            await this.loadBills();
            
            // 加载统计数据
            await this.loadBillStats();
            
        } catch (error) {
            console.error('页面初始化失败:', error);
        }
    }

    /**
     * 加载账单数据
     */
    async loadBills() {
        try {
            const params = {
                page: this.currentPage,
                pageSize: this.pageSize,
                ...this.currentFilters
            };

            const response = await this.apiClient.get('/api/admin.php?action=alipay_bills_list', { params });

            if (response.success) {
                this.bills = response.data.bills || [];
                this.renderBillsTable();
                if (response.data.pagination) {
                    this.renderPagination(response.data.pagination);
                }
            } else {
                throw new Error(response.message || '加载账单失败');
            }

        } catch (error) {
            console.error('加载账单数据失败:', error);
            this.showError('加载账单数据失败: ' + error.message);
        }
    }

    /**
     * 加载统计数据
     */
    async loadBillStats() {
        try {
            const response = await this.apiClient.get('/api/admin.php?action=alipay_bills_stats', {
                params: this.currentFilters
            });

            if (response.success) {
                const stats = response.data;
                document.getElementById('totalBillsCount').textContent = stats.total || 0;
                document.getElementById('matchedBillsCount').textContent = stats.matched || 0;
                document.getElementById('unmatchedBillsCount').textContent = stats.unmatched || 0;
                document.getElementById('ignoredBillsCount').textContent = stats.ignored || 0;
            }

        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 渲染账单表格
     */
    renderBillsTable() {
        const tbody = document.getElementById('billsTableBody');
        
        if (this.bills.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="bi bi-inbox text-muted" style="font-size: 2rem;"></i>
                        <div class="mt-2 text-muted">暂无账单数据</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.bills.map(bill => `
            <tr>
                <td>
                    <div class="fw-medium">${bill.bill_id || '-'}</div>
                    <small class="text-muted">#${bill.id}</small>
                </td>
                <td>${this.formatDateTime(bill.transaction_time)}</td>
                <td class="fw-medium ${bill.amount > 0 ? 'text-success' : 'text-danger'}">
                    ¥${this.formatAmount(bill.amount)}
                </td>
                <td>${bill.counterpart || '-'}</td>
                <td>
                    <span class="badge bg-${this.getMatchStatusColor(bill.match_status)}">
                        ${this.getMatchStatusText(bill.match_status)}
                    </span>
                </td>
                <td>
                    ${bill.matched_order_id ? 
                        `<a href="#" onclick="alipayBillsModule.viewOrderDetail(${bill.matched_order_id})">#${bill.matched_order_id}</a>` : 
                        '-'
                    }
                </td>
                <td>
                    <small class="text-muted">${bill.device_id || '-'}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="alipayBillsModule.viewBillDetail(${bill.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${bill.match_status === 'unmatched' ? `
                            <button class="btn btn-outline-success" onclick="alipayBillsModule.showMatchModal(${bill.id})" title="手动匹配">
                                <i class="bi bi-link-45deg"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 渲染分页
     */
    renderPagination(pagination) {
        const container = document.getElementById('paginationContainer');
        
        if (!pagination || pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHtml = '<ul class="pagination justify-content-center">';
        
        // 上一页
        if (pagination.current_page > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="alipayBillsModule.goToPage(${pagination.current_page - 1})">上一页</a>
                </li>
            `;
        }

        // 页码
        for (let i = Math.max(1, pagination.current_page - 2); 
             i <= Math.min(pagination.total_pages, pagination.current_page + 2); 
             i++) {
            paginationHtml += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="alipayBillsModule.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (pagination.current_page < pagination.total_pages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="alipayBillsModule.goToPage(${pagination.current_page + 1})">下一页</a>
                </li>
            `;
        }

        paginationHtml += '</ul>';
        container.innerHTML = paginationHtml;
    }

    /**
     * 应用筛选条件
     */
    applyFilters() {
        this.currentFilters = {
            match_status: document.getElementById('filterMatchStatus').value,
            start_date: document.getElementById('filterStartDate').value,
            end_date: document.getElementById('filterEndDate').value
        };

        // 移除空值
        Object.keys(this.currentFilters).forEach(key => {
            if (!this.currentFilters[key]) {
                delete this.currentFilters[key];
            }
        });

        this.currentPage = 1;
        this.loadBills();
        this.loadBillStats();
    }

    /**
     * 刷新账单数据
     */
    async refreshBills() {
        await this.loadBills();
        await this.loadBillStats();
        this.showSuccess('账单数据已刷新');
    }

    /**
     * 跳转到指定页码
     */
    goToPage(page) {
        this.currentPage = page;
        this.loadBills();
    }

    /**
     * 查看账单详情
     */
    async viewBillDetail(billId) {
        try {
            const response = await this.apiClient.get(`/api/admin.php?action=alipay_bills_detail&id=${billId}`);
            
            if (response.success) {
                const bill = response.data;
                const modalContent = document.getElementById('billDetailContent');
                
                modalContent.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <table class="table table-sm">
                                <tr><td>账单ID:</td><td>${bill.bill_id || '-'}</td></tr>
                                <tr><td>交易流水号:</td><td>${bill.transaction_id || '-'}</td></tr>
                                <tr><td>金额:</td><td class="fw-medium">¥${this.formatAmount(bill.amount)}</td></tr>
                                <tr><td>交易类型:</td><td>${bill.transaction_type || '-'}</td></tr>
                                <tr><td>交易对方:</td><td>${bill.counterpart || '-'}</td></tr>
                                <tr><td>交易时间:</td><td>${this.formatDateTime(bill.transaction_time)}</td></tr>
                                <tr><td>描述:</td><td>${bill.description || '-'}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>匹配信息</h6>
                            <table class="table table-sm">
                                <tr><td>匹配状态:</td><td><span class="badge bg-${this.getMatchStatusColor(bill.match_status)}">${this.getMatchStatusText(bill.match_status)}</span></td></tr>
                                <tr><td>关联订单:</td><td>${bill.matched_order_id || '-'}</td></tr>
                                <tr><td>匹配时间:</td><td>${bill.matched_at ? this.formatDateTime(bill.matched_at) : '-'}</td></tr>
                                <tr><td>设备ID:</td><td>${bill.device_id || '-'}</td></tr>
                                <tr><td>上报时间:</td><td>${this.formatDateTime(bill.uploaded_at)}</td></tr>
                                <tr><td>处理状态:</td><td>${bill.is_processed ? '已处理' : '未处理'}</td></tr>
                            </table>
                        </div>
                    </div>
                `;

                const modal = new bootstrap.Modal(document.getElementById('billDetailModal'));
                modal.show();
            }

        } catch (error) {
            console.error('查看账单详情失败:', error);
            this.showError('查看账单详情失败: ' + error.message);
        }
    }

    /**
     * 工具方法
     */
    getMatchStatusColor(status) {
        const colors = {
            'unmatched': 'warning',
            'matched': 'success',
            'manual': 'info',
            'ignored': 'secondary'
        };
        return colors[status] || 'secondary';
    }

    getMatchStatusText(status) {
        const texts = {
            'unmatched': '未匹配',
            'matched': '已匹配',
            'manual': '手动处理',
            'ignored': '已忽略'
        };
        return texts[status] || status;
    }

    formatAmount(amount) {
        return parseFloat(amount || 0).toFixed(2);
    }

    formatDateTime(datetime) {
        if (!datetime) return '-';
        return new Date(datetime).toLocaleString('zh-CN');
    }

    showSuccess(message) {
        if (typeof window.showAlert === 'function') {
            window.showAlert(message, 'success');
        } else {
            console.log('Success:', message);
        }
    }

    showError(message) {
        if (typeof window.showAlert === 'function') {
            window.showAlert(message, 'error');
        } else {
            console.error('Error:', message);
        }
    }
}

// 导出模块
window.AlipayBillsModule = AlipayBillsModule;

// 创建全局实例
if (typeof window.alipayBillsModule === 'undefined') {
    window.alipayBillsModule = new AlipayBillsModule();
}

console.log('🎯 AlipayBillsModule 模块加载完成'); 