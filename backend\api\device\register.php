<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';

$db = new Database();
$auth = new Auth();

// 路由处理
$method = $_SERVER['REQUEST_METHOD'];
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uriSegments = explode('/', trim($uri, '/'));

// 获取操作类型
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($method) {
        case 'POST':
            if ($action === 'register') {
                handleDeviceRegister();
            } elseif ($action === 'login') {
                handleDeviceLogin();
            } else {
                throw new Exception('不支持的操作');
            }
            break;
            
        case 'GET':
            if ($action === 'status') {
                handleDeviceStatus();
            } else {
                throw new Exception('不支持的操作');
            }
            break;
            
        default:
            throw new Exception('不支持的HTTP方法');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'code' => 400,
        'message' => $e->getMessage(),
        'data' => null
    ));
}

// 设备注册/验证
function handleDeviceRegister() {
    global $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['device_fingerprint'])) {
        throw new Exception('缺少设备指纹参数');
    }
    
    $fingerprint = $input['device_fingerprint'];
    $brand = isset($input['device_brand']) ? $input['device_brand'] : '';
    $model = isset($input['device_model']) ? $input['device_model'] : '';
    $deviceName = isset($input['device_name']) ? $input['device_name'] : '';
    $systemVersion = isset($input['system_version']) ? $input['system_version'] : '';
    $appVersion = isset($input['app_version']) ? $input['app_version'] : '';
    
    // 详细设备信息
    $detailedInfo = isset($input['detailed_info']) ? json_encode($input['detailed_info']) : '';
    $operatorName = isset($input['operator_name']) ? $input['operator_name'] : '';
    $networkType = isset($input['network_type']) ? $input['network_type'] : '';
    $countryCode = isset($input['country_code']) ? $input['country_code'] : '';
    $alipayVersion = isset($input['alipay_version']) ? $input['alipay_version'] : '';
    $wechatVersion = isset($input['wechat_version']) ? $input['wechat_version'] : '';
    
    // 检查设备是否已存在
    $device = $db->fetch(
        "SELECT d.*, p.company_name as provider_name 
         FROM devices d 
         LEFT JOIN payment_providers p ON d.provider_id = p.id 
         WHERE d.device_fingerprint = ?",
        array($fingerprint)
    );
    
    if ($device) {
        // 更新设备信息
        $db->execute(
            "UPDATE devices SET 
                device_brand = ?, 
                device_model = ?, 
                device_name = ?,
                system_version = ?,
                detailed_info = ?,
                operator_name = ?,
                network_type = ?,
                country_code = ?,
                alipay_version = ?,
                wechat_version = ?,
                last_online = NOW() 
             WHERE device_fingerprint = ?",
            array($brand, $model, $deviceName, $systemVersion, $detailedInfo, $operatorName, $networkType, $countryCode, $alipayVersion, $wechatVersion, $fingerprint)
        );
        
        echo json_encode(array(
            'code' => 200,
            'message' => 'success',
            'data' => array(
                'device_id' => $device['device_id'],
                'status' => $device['status'],
                'need_wait' => $device['status'] !== 'active',
                'provider_name' => $device['provider_name']
            )
        ));
    } else {
        // 新设备，创建设备记录（等待分配给码商）
        $deviceId = 'DEV_' . date('Ymd') . '_' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        $db->execute(
            "INSERT INTO devices (device_id, device_fingerprint, device_brand, device_model, device_name, system_version, detailed_info, operator_name, network_type, country_code, alipay_version, wechat_version, status) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')",
            array($deviceId, $fingerprint, $brand, $model, $deviceName, $systemVersion, $detailedInfo, $operatorName, $networkType, $countryCode, $alipayVersion, $wechatVersion)
        );
        
        echo json_encode(array(
            'code' => 200,
            'message' => 'success',
            'data' => array(
                'device_id' => $deviceId,
                'status' => 'pending',
                'need_wait' => true,
                'provider_name' => null
            )
        ));
    }
}

// 设备登录验证（新版本：支持密码）
function handleDeviceLogin() {
    global $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['device_id']) || !isset($input['password'])) {
        throw new Exception('缺少必要参数（device_id, password）');
    }
    
    $deviceId = $input['device_id'];
    $password = $input['password'];
    $deviceFingerprint = isset($input['device_fingerprint']) ? $input['device_fingerprint'] : '';
    $isFirstTime = isset($input['is_first_time']) ? $input['is_first_time'] : false;
    $action = isset($input['action']) ? $input['action'] : '';
    
    // 获取设备信息
    $device = $db->fetch(
        "SELECT d.*, p.company_name as provider_name, u.real_name as provider_contact
         FROM devices d 
         LEFT JOIN payment_providers p ON d.provider_id = p.id 
         LEFT JOIN users u ON p.user_id = u.id
         WHERE d.device_id = ? AND d.status = 'active'",
        array($deviceId)
    );
    
    if (!$device) {
        echo json_encode(array(
            'code' => 401,
            'message' => '设备未授权或不存在',
            'data' => null
        ));
        return;
    }
    
    // 验证设备指纹
    if ($deviceFingerprint && $device['device_fingerprint'] !== $deviceFingerprint) {
        echo json_encode(array(
            'code' => 401,
            'message' => '设备指纹不匹配',
            'data' => null
        ));
        return;
    }
    
    $hasPassword = !empty($device['password']);
    
    if ($action === 'set_password' || ($isFirstTime && !$hasPassword)) {
        // 设置密码模式
        if ($hasPassword) {
            echo json_encode(array(
                'code' => 400,
                'message' => '设备已设置密码，请使用验证模式',
                'data' => null
            ));
            return;
        }
        
        // 密码强度验证
        if (strlen($password) < 6) {
            echo json_encode(array(
                'code' => 400,
                'message' => '密码长度至少6位',
                'data' => null
            ));
            return;
        }
        
        // 加密并保存密码
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $db->execute(
            "UPDATE devices SET password = ?, password_set_at = NOW() WHERE id = ?",
            array($hashedPassword, $device['id'])
        );
        
        $successMessage = '密码设置成功';
        $isPasswordSet = true;
        
    } else {
        // 验证密码模式
        if (!$hasPassword) {
            echo json_encode(array(
                'code' => 400,
                'message' => '设备未设置密码，请先设置密码',
                'data' => null
            ));
            return;
        }
        
        // 验证密码
        if (!password_verify($password, $device['password'])) {
            echo json_encode(array(
                'code' => 401,
                'message' => '密码错误',
                'data' => null
            ));
            return;
        }
        
        $successMessage = '登录成功';
        $isPasswordSet = true;
    }
    
    // 更新最后在线时间
    $db->execute(
        "UPDATE devices SET last_online = NOW() WHERE id = ?",
        array($device['id'])
    );
    
    // 生成设备token
    $token = base64_encode(json_encode(array(
        'device_id' => $deviceId,
        'device_fingerprint' => $deviceFingerprint,
        'exp' => time() + (7 * 24 * 60 * 60) // 7天过期
    )));
    
    echo json_encode(array(
        'code' => 200,
        'message' => $successMessage,
        'data' => array(
            'token' => $token,
            'password_set' => $isPasswordSet,
            'device_info' => array(
                'device_id' => $device['device_id'],
                'device_name' => $device['device_name'],
                'status' => $device['status'],
                'provider_name' => $device['provider_name'],
                'provider_contact' => $device['provider_contact']
            )
        )
    ));
}

// 获取设备状态
function handleDeviceStatus() {
    global $db;
    
    $deviceId = isset($_GET['device_id']) ? $_GET['device_id'] : '';
    $checkPassword = isset($_GET['check_password']) ? $_GET['check_password'] : false;
    
    if (!$deviceId) {
        throw new Exception('缺少设备ID参数');
    }
    
    $device = $db->fetch(
        "SELECT d.*, p.company_name as provider_name, u.real_name as provider_contact
         FROM devices d 
         LEFT JOIN payment_providers p ON d.provider_id = p.id 
         LEFT JOIN users u ON p.user_id = u.id
         WHERE d.device_id = ?",
        array($deviceId)
    );
    
    if (!$device) {
        echo json_encode(array(
            'code' => 404,
            'message' => '设备不存在',
            'data' => null
        ));
        return;
    }
    
    // 根据设备状态返回不同信息
    $responseData = array(
        'device_id' => $device['device_id'],
        'status' => $device['status'],
        'device_name' => $device['device_name'],
        'last_online' => $device['last_online']
    );
    
    // 如果请求检查密码状态
    if ($checkPassword) {
        $responseData['has_password'] = !empty($device['password']);
    }
    
    if ($device['status'] === 'active') {
        $responseData['provider_name'] = $device['provider_name'];
        $responseData['provider_contact'] = $device['provider_contact'];
    } elseif ($device['status'] === 'pending') {
        $responseData['message'] = '设备等待分配给码商';
    } elseif ($device['status'] === 'disabled') {
        $responseData['message'] = '设备已被禁用';
    }
    
    echo json_encode(array(
        'code' => 200,
        'data' => $responseData
    ));
}
?> 