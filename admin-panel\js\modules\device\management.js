/**
 * 设备管理模块
 * 对接后端设备管理API，提供完整的设备管理功能
 */
class DeviceManager {
    constructor() {
        this.apiBase = '/api/device';
        this.currentPage = 1;
        this.pageSize = 20;
        this.currentFilters = {};
        this.devices = [];
        this.groups = [];
        this.isInitialized = false;
        
        console.log('DeviceManager initialized');
    }

    /**
     * 初始化设备管理模块
     */
    async init(container) {
        try {
            await this.loadGroups();
            this.renderDeviceManagement(container);
            await this.loadDevices();
            this.bindEvents();
        } catch (error) {
            console.error('设备管理模块初始化失败:', error);
            this.showError('设备管理模块初始化失败');
        }
    }

    /**
     * 渲染设备管理界面
     */
    renderDeviceManagement(container) {
        container.innerHTML = `
            <div class="device-management-module">
                <div class="row">
                    <!-- 左侧主要内容 -->
                    <div class="col-md-9">
                        <!-- 页面头部 -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4><i class="bi bi-phone me-2"></i>设备管理</h4>
                            <div class="btn-group">
                                <button class="btn btn-primary" id="refreshDevices">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                                <button class="btn btn-success" id="exportDevices">
                                    <i class="bi bi-download"></i> 导出
                                </button>
                            </div>
                        </div>

                        <!-- 筛选栏 -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">设备状态</label>
                                        <select class="form-select" id="statusFilter">
                                            <option value="">全部状态</option>
                                            <option value="pending">待审核</option>
                                            <option value="active">已激活</option>
                                            <option value="inactive">已停用</option>
                                            <option value="maintenance">维护中</option>
                                            <option value="banned">已禁用</option>
                                            <option value="rejected">已拒绝</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">所属小组</label>
                                        <select class="form-select" id="groupFilter">
                                            <option value="0">全部小组</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">签到状态</label>
                                        <select class="form-select" id="checkinStatusFilter">
                                            <option value="">全部</option>
                                            <option value="normal">正常</option>
                                            <option value="overdue">超时</option>
                                            <option value="never">从未签到</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">搜索设备</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="searchInput" 
                                                   placeholder="设备ID、名称、品牌...">
                                            <button class="btn btn-outline-secondary" id="searchBtn">
                                                <i class="bi bi-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <button class="btn btn-outline-primary me-2" id="clearFilters">
                                            <i class="bi bi-x-circle"></i> 清除筛选
                                        </button>
                                        <button class="btn btn-outline-success me-2" id="batchApprove">
                                            <i class="bi bi-check-circle"></i> 批量审核
                                        </button>
                                        <button class="btn btn-outline-danger" id="batchReject">
                                            <i class="bi bi-x-circle"></i> 批量拒绝
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 设备列表 -->
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span>设备列表</span>
                                <div id="deviceStats" class="text-muted small"></div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="40">
                                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                                </th>
                                                <th>设备ID</th>
                                                <th>设备信息</th>
                                                <th>所属小组</th>
                                                <th>状态</th>
                                                <th>签到状态</th>
                                                <th>最后签到</th>
                                                <th>创建时间</th>
                                                <th width="150">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="deviceTableBody">
                                            <!-- 设备列表将在这里动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div id="loadingIndicator" class="text-center p-4" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2 text-muted">正在加载设备数据...</p>
                                </div>
                                <div id="noDataIndicator" class="text-center p-4" style="display: none;">
                                    <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                                    <p class="mt-2 text-muted">暂无设备数据</p>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="text-muted small" id="paginationInfo"></div>
                                    <nav>
                                        <ul class="pagination pagination-sm mb-0" id="pagination">
                                            <!-- 分页将在这里动态生成 -->
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧信息面板 -->
                    <div class="col-md-3">
                        <!-- 统计信息 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-bar-chart me-2"></i>设备统计</h6>
                            </div>
                            <div class="card-body">
                                <div id="deviceStatsCards">
                                    <!-- 统计卡片将在这里动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 签到监控 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-clock me-2"></i>签到监控</h6>
                            </div>
                            <div class="card-body">
                                <div id="checkinMonitor">
                                    <!-- 签到监控信息将在这里动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 快速操作 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-lightning me-2"></i>快速操作</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-info btn-sm" id="viewOverdueDevices">
                                        <i class="bi bi-exclamation-triangle"></i> 查看超时设备
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" id="viewPendingDevices">
                                        <i class="bi bi-clock-history"></i> 查看待审核设备
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" id="remindCheckin">
                                        <i class="bi bi-bell"></i> 签到提醒
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备详情模态框 -->
            <div class="modal fade" id="deviceDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">设备详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="deviceDetailContent">
                            <!-- 设备详情内容 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备审核模态框 -->
            <div class="modal fade" id="deviceApprovalModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">设备审核</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="approvalForm">
                                <input type="hidden" id="approvalDeviceId">
                                <div class="mb-3">
                                    <label class="form-label">审核操作</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="approvalAction" 
                                               id="approveAction" value="approve" checked>
                                        <label class="form-check-label" for="approveAction">
                                            <i class="bi bi-check-circle text-success"></i> 审核通过
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="approvalAction" 
                                               id="rejectAction" value="reject">
                                        <label class="form-check-label" for="rejectAction">
                                            <i class="bi bi-x-circle text-danger"></i> 审核拒绝
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3" id="providerGroupSelection">
                                    <label class="form-label">分配小组</label>
                                    <select class="form-select" id="assignGroupSelect" required>
                                        <option value="">请选择小组</option>
                                    </select>
                                </div>
                                <div class="mb-3" id="rejectionReasonDiv" style="display: none;">
                                    <label class="form-label">拒绝原因</label>
                                    <textarea class="form-control" id="rejectionReason" rows="3" 
                                              placeholder="请输入拒绝原因..."></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">备注信息</label>
                                    <textarea class="form-control" id="approvalRemarks" rows="2" 
                                              placeholder="可选的备注信息..."></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmApproval">确认提交</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 筛选事件
        document.getElementById('statusFilter').addEventListener('change', () => {
            this.currentFilters.status = document.getElementById('statusFilter').value;
            this.currentPage = 1;
            this.loadDevices();
        });

        document.getElementById('groupFilter').addEventListener('change', () => {
            this.currentFilters.group_id = parseInt(document.getElementById('groupFilter').value);
            this.currentPage = 1;
            this.loadDevices();
        });

        document.getElementById('checkinStatusFilter').addEventListener('change', () => {
            this.currentFilters.checkin_status = document.getElementById('checkinStatusFilter').value;
            this.currentPage = 1;
            this.loadDevices();
        });

        // 搜索事件
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.currentFilters.search = document.getElementById('searchInput').value.trim();
            this.currentPage = 1;
            this.loadDevices();
        });

        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.currentFilters.search = e.target.value.trim();
                this.currentPage = 1;
                this.loadDevices();
            }
        });

        // 清除筛选
        document.getElementById('clearFilters').addEventListener('click', () => {
            this.clearFilters();
        });

        // 刷新按钮
        document.getElementById('refreshDevices').addEventListener('click', () => {
            this.loadDevices();
        });

        // 全选复选框
        document.getElementById('selectAll').addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('.device-checkbox');
            checkboxes.forEach(cb => cb.checked = e.target.checked);
        });

        // 批量操作
        document.getElementById('batchApprove').addEventListener('click', () => {
            this.handleBatchOperation('approve');
        });

        document.getElementById('batchReject').addEventListener('click', () => {
            this.handleBatchOperation('reject');
        });

        // 快速操作
        document.getElementById('viewOverdueDevices').addEventListener('click', () => {
            this.currentFilters.checkin_status = 'overdue';
            document.getElementById('checkinStatusFilter').value = 'overdue';
            this.currentPage = 1;
            this.loadDevices();
        });

        document.getElementById('viewPendingDevices').addEventListener('click', () => {
            this.currentFilters.status = 'pending';
            document.getElementById('statusFilter').value = 'pending';
            this.currentPage = 1;
            this.loadDevices();
        });

        // 审核表单事件
        document.querySelectorAll('input[name="approvalAction"]').forEach(radio => {
            radio.addEventListener('change', () => {
                this.toggleApprovalForm();
            });
        });

        document.getElementById('confirmApproval').addEventListener('click', () => {
            this.submitApproval();
        });
    }

    /**
     * 加载小组列表
     */
    async loadGroups() {
        try {
            const response = await this.makeApiRequest('/group_management.php?action=list', 'GET');
            if (response.error_code === 0) {
                this.groups = response.data.groups;
                this.updateGroupFilters();
            }
        } catch (error) {
            console.error('加载小组列表失败:', error);
        }
    }

    /**
     * 更新小组筛选选项
     */
    updateGroupFilters() {
        const groupFilter = document.getElementById('groupFilter');
        const assignGroupSelect = document.getElementById('assignGroupSelect');
        
        // 清空现有选项
        groupFilter.innerHTML = '<option value="0">全部小组</option>';
        assignGroupSelect.innerHTML = '<option value="">请选择小组</option>';
        
        // 添加小组选项
        this.groups.forEach(group => {
            groupFilter.innerHTML += `<option value="${group.id}">${group.group_code}</option>`;
            assignGroupSelect.innerHTML += `<option value="${group.id}">${group.group_code}</option>`;
        });
    }

    /**
     * 加载设备列表
     */
    async loadDevices() {
        try {
            this.showLoading(true);
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                ...this.currentFilters
            });
            
            const response = await this.makeApiRequest(`/management.php?action=list&${params}`, 'GET');
            
            if (response.error_code === 0) {
                this.devices = response.data.devices;
                this.renderDeviceList();
                this.renderPagination(response.data.pagination);
                await this.loadStats();
                await this.loadCheckinMonitor();
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('加载设备列表失败:', error);
            this.showError('加载设备列表失败');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 渲染设备列表
     */
    renderDeviceList() {
        const tbody = document.getElementById('deviceTableBody');
        
        if (this.devices.length === 0) {
            tbody.innerHTML = '';
            document.getElementById('noDataIndicator').style.display = 'block';
            return;
        }
        
        document.getElementById('noDataIndicator').style.display = 'none';
        
        tbody.innerHTML = this.devices.map(device => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input device-checkbox" 
                           value="${device.device_id}">
                </td>
                <td>
                    <div class="fw-bold">${device.device_id}</div>
                    <small class="text-muted">${device.device_fingerprint || '无指纹'}</small>
                </td>
                <td>
                    <div>${device.device_name || '未命名'}</div>
                    <small class="text-muted">${device.device_brand} ${device.device_model}</small>
                </td>
                <td>
                    ${device.group_code ? `<span class="badge bg-info">${device.group_code}</span>` : 
                      '<span class="text-muted">未分配</span>'}
                </td>
                <td>${this.renderStatusBadge(device.status)}</td>
                <td>${this.renderCheckinStatus(device.checkin_info)}</td>
                <td>
                    ${device.last_checkin_time ? 
                      `<div>${new Date(device.last_checkin_time).toLocaleString()}</div>` : 
                      '<span class="text-muted">从未签到</span>'}
                </td>
                <td>
                    <small>${new Date(device.created_at).toLocaleString()}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="deviceManager.showDeviceDetail('${device.device_id}')" 
                                title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${device.status === 'pending' ? `
                            <button class="btn btn-outline-success" onclick="deviceManager.showApprovalModal('${device.device_id}')" 
                                    title="审核设备">
                                <i class="bi bi-check-circle"></i>
                            </button>
                        ` : ''}
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" onclick="deviceManager.editDevice('${device.device_id}')">
                                    <i class="bi bi-pencil"></i> 编辑
                                </a></li>
                                <li><a class="dropdown-item" onclick="deviceManager.assignGroup('${device.device_id}')">
                                    <i class="bi bi-people"></i> 分配小组
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" onclick="deviceManager.deleteDevice('${device.device_id}')">
                                    <i class="bi bi-trash"></i> 删除
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 渲染状态徽章
     */
    renderStatusBadge(status) {
        const statusConfig = {
            pending: { class: 'warning', text: '待审核' },
            active: { class: 'success', text: '已激活' },
            inactive: { class: 'secondary', text: '已停用' },
            maintenance: { class: 'info', text: '维护中' },
            banned: { class: 'danger', text: '已禁用' },
            rejected: { class: 'danger', text: '已拒绝' }
        };
        
        const config = statusConfig[status] || { class: 'secondary', text: status };
        return `<span class="badge bg-${config.class}">${config.text}</span>`;
    }

    /**
     * 渲染签到状态
     */
    renderCheckinStatus(checkinInfo) {
        if (!checkinInfo) return '<span class="text-muted">无数据</span>';
        
        if (checkinInfo.status === 'never') {
            return '<span class="badge bg-secondary">从未签到</span>';
        } else if (checkinInfo.is_overdue) {
            return `<span class="badge bg-danger" title="${checkinInfo.message}">超时</span>`;
        } else {
            return `<span class="badge bg-success" title="${checkinInfo.message}">正常</span>`;
        }
    }

    /**
     * 渲染分页
     */
    renderPagination(pagination) {
        const paginationEl = document.getElementById('pagination');
        const paginationInfo = document.getElementById('paginationInfo');
        
        // 更新分页信息
        const start = (pagination.page - 1) * pagination.limit + 1;
        const end = Math.min(pagination.page * pagination.limit, pagination.total);
        paginationInfo.textContent = `显示 ${start}-${end} 条，共 ${pagination.total} 条记录`;
        
        // 生成分页按钮
        if (pagination.pages <= 1) {
            paginationEl.innerHTML = '';
            return;
        }
        
        let paginationHtml = '';
        
        // 上一页
        paginationHtml += `
            <li class="page-item ${pagination.page <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="deviceManager.goToPage(${pagination.page - 1})">&laquo;</a>
            </li>
        `;
        
        // 页码
        for (let i = 1; i <= pagination.pages; i++) {
            if (i === 1 || i === pagination.pages || Math.abs(i - pagination.page) <= 2) {
                paginationHtml += `
                    <li class="page-item ${i === pagination.page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="deviceManager.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (Math.abs(i - pagination.page) === 3) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // 下一页
        paginationHtml += `
            <li class="page-item ${pagination.page >= pagination.pages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="deviceManager.goToPage(${pagination.page + 1})">&raquo;</a>
            </li>
        `;
        
        paginationEl.innerHTML = paginationHtml;
    }

    /**
     * 加载统计信息
     */
    async loadStats() {
        try {
            const response = await this.makeApiRequest('/management.php?action=stats', 'GET');
            if (response.error_code === 0) {
                this.renderStats(response.data);
            }
        } catch (error) {
            console.error('加载统计信息失败:', error);
        }
    }

    /**
     * 渲染统计卡片
     */
    renderStats(stats) {
        const container = document.getElementById('deviceStatsCards');
        
        // 处理状态统计
        const statusStats = {};
        stats.status_stats.forEach(item => {
            statusStats[item.status] = item.count;
        });
        
        container.innerHTML = `
            <div class="row g-2">
                <div class="col-6">
                    <div class="stat-card bg-warning bg-opacity-10 border-warning">
                        <div class="stat-value">${statusStats.pending || 0}</div>
                        <div class="stat-label">待审核</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stat-card bg-success bg-opacity-10 border-success">
                        <div class="stat-value">${statusStats.active || 0}</div>
                        <div class="stat-label">已激活</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stat-card bg-info bg-opacity-10 border-info">
                        <div class="stat-value">${statusStats.maintenance || 0}</div>
                        <div class="stat-label">维护中</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stat-card bg-danger bg-opacity-10 border-danger">
                        <div class="stat-value">${statusStats.banned || 0}</div>
                        <div class="stat-label">已禁用</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载签到监控信息
     */
    async loadCheckinMonitor() {
        try {
            const response = await this.makeApiRequest('/checkin_management.php?action=overdue_devices', 'GET');
            if (response.error_code === 0) {
                this.renderCheckinMonitor(response.data);
            }
        } catch (error) {
            console.error('加载签到监控失败:', error);
        }
    }

    /**
     * 渲染签到监控
     */
    renderCheckinMonitor(data) {
        const container = document.getElementById('checkinMonitor');
        
        container.innerHTML = `
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-danger">超时设备</span>
                    <span class="badge bg-danger">${data.summary.total_overdue}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-warning">严重超时</span>
                    <span class="badge bg-warning">${data.summary.critical_overdue}</span>
                </div>
            </div>
            ${data.summary.total_overdue > 0 ? `
                <div class="text-center">
                    <button class="btn btn-outline-warning btn-sm" onclick="deviceManager.handleOverdueDevices()">
                        <i class="bi bi-bell"></i> 批量提醒
                    </button>
                </div>
            ` : '<div class="text-center text-success"><i class="bi bi-check-circle"></i> 签到状态良好</div>'}
        `;
    }

    /**
     * API请求封装
     */
    async makeApiRequest(endpoint, method = 'GET', data = null) {
        const url = `${this.apiBase}${endpoint}`;
        const token = this.getAuthToken();
        
        // 调试信息
        console.log('🔍 API请求调试信息:', {
            url,
            method,
            token: token ? token.substring(0, 20) + '...' : 'null',
            tokenLength: token ? token.length : 0,
            localStorage: {
                admin_token: localStorage.getItem('admin_token') ? '存在' : '不存在',
                authToken: localStorage.getItem('authToken') ? '存在' : '不存在'
            }
        });
        
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        };
        
        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        return await response.json();
    }

    /**
     * 获取认证token
     */
    getAuthToken() {
        return localStorage.getItem('admin_token') || sessionStorage.getItem('admin_token') || '';
    }

    /**
     * 显示加载状态
     */
    showLoading(show) {
        document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        // 这里可以接入现有的错误提示系统
        alert(message);
    }

    /**
     * 清除筛选条件
     */
    clearFilters() {
        this.currentFilters = {
            status: '',
            group_id: 0,
            search: '',
            checkin_status: ''
        };
        
        document.getElementById('statusFilter').value = '';
        document.getElementById('groupFilter').value = '0';
        document.getElementById('checkinStatusFilter').value = '';
        document.getElementById('searchInput').value = '';
        
        this.currentPage = 1;
        this.loadDevices();
    }

    /**
     * 跳转到指定页码
     */
    goToPage(page) {
        this.currentPage = page;
        this.loadDevices();
    }

    /**
     * 显示设备详情
     */
    async showDeviceDetail(deviceId) {
        try {
            const response = await this.makeApiRequest(`/management.php?action=detail&device_id=${deviceId}`, 'GET');
            if (response.error_code === 0) {
                this.renderDeviceDetail(response.data);
                new bootstrap.Modal(document.getElementById('deviceDetailModal')).show();
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('获取设备详情失败:', error);
            this.showError('获取设备详情失败');
        }
    }

    /**
     * 渲染设备详情
     */
    renderDeviceDetail(device) {
        const content = document.getElementById('deviceDetailContent');
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>设备ID</td><td>${device.device_id}</td></tr>
                        <tr><td>设备名称</td><td>${device.device_name || '未命名'}</td></tr>
                        <tr><td>设备品牌</td><td>${device.device_brand}</td></tr>
                        <tr><td>设备型号</td><td>${device.device_model}</td></tr>
                        <tr><td>系统版本</td><td>${device.system_version}</td></tr>
                        <tr><td>状态</td><td>${this.renderStatusBadge(device.status)}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>签到信息</h6>
                    <table class="table table-sm">
                        <tr><td>签到状态</td><td>${this.renderCheckinStatus(device.checkin_info)}</td></tr>
                        <tr><td>最后签到</td><td>${device.last_checkin_time || '从未签到'}</td></tr>
                        <tr><td>下次签到</td><td>${device.checkin_info?.next_checkin_time || '无'}</td></tr>
                        <tr><td>所属小组</td><td>${device.group_code || '未分配'}</td></tr>
                        <tr><td>创建时间</td><td>${device.created_at}</td></tr>
                    </table>
                </div>
            </div>
            ${device.logs && device.logs.length > 0 ? `
                <div class="mt-4">
                    <h6>操作日志</h6>
                    <div class="table-responsive" style="max-height: 300px;">
                        <table class="table table-sm">
                            <thead>
                                <tr><th>时间</th><th>类型</th><th>内容</th></tr>
                            </thead>
                            <tbody>
                                ${device.logs.map(log => `
                                    <tr>
                                        <td><small>${new Date(log.created_at).toLocaleString()}</small></td>
                                        <td><span class="badge bg-${log.log_level === 'warning' ? 'warning' : 'info'}">${log.log_type}</span></td>
                                        <td><small>${log.message}</small></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            ` : ''}
        `;
    }

    /**
     * 显示审核模态框
     */
    showApprovalModal(deviceId) {
        document.getElementById('approvalDeviceId').value = deviceId;
        document.getElementById('approveAction').checked = true;
        this.toggleApprovalForm();
        new bootstrap.Modal(document.getElementById('deviceApprovalModal')).show();
    }

    /**
     * 切换审核表单显示
     */
    toggleApprovalForm() {
        const isApprove = document.getElementById('approveAction').checked;
        document.getElementById('providerGroupSelection').style.display = isApprove ? 'block' : 'none';
        document.getElementById('rejectionReasonDiv').style.display = isApprove ? 'none' : 'block';
        
        if (isApprove) {
            document.getElementById('assignGroupSelect').required = true;
            document.getElementById('rejectionReason').required = false;
        } else {
            document.getElementById('assignGroupSelect').required = false;
            document.getElementById('rejectionReason').required = true;
        }
    }

    /**
     * 提交审核
     */
    async submitApproval() {
        try {
            const deviceId = document.getElementById('approvalDeviceId').value;
            const isApprove = document.getElementById('approveAction').checked;
            const remarks = document.getElementById('approvalRemarks').value;
            
            let data = { device_id: deviceId, remarks };
            let endpoint = '';
            
            if (isApprove) {
                const groupId = document.getElementById('assignGroupSelect').value;
                if (!groupId) {
                    this.showError('请选择分配的小组');
                    return;
                }
                data.group_id = parseInt(groupId);
                endpoint = '/management.php?action=approve';
            } else {
                const reason = document.getElementById('rejectionReason').value;
                if (!reason.trim()) {
                    this.showError('请输入拒绝原因');
                    return;
                }
                data.reason = reason;
                endpoint = '/management.php?action=reject';
            }
            
            const response = await this.makeApiRequest(endpoint, 'POST', data);
            if (response.error_code === 0) {
                bootstrap.Modal.getInstance(document.getElementById('deviceApprovalModal')).hide();
                this.loadDevices();
                this.showSuccess('设备审核成功');
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('设备审核失败:', error);
            this.showError('设备审核失败');
        }
    }

    /**
     * 处理批量操作
     */
    async handleBatchOperation(operation) {
        const selectedDevices = Array.from(document.querySelectorAll('.device-checkbox:checked'))
            .map(cb => cb.value);
        
        if (selectedDevices.length === 0) {
            this.showError('请先选择要操作的设备');
            return;
        }
        
        if (!confirm(`确认对选中的 ${selectedDevices.length} 台设备执行${operation === 'approve' ? '批量审核' : '批量拒绝'}操作？`)) {
            return;
        }
        
        try {
            const data = {
                device_ids: selectedDevices,
                operation: operation,
                params: operation === 'approve' ? 
                    { group_id: 1 } : // 这里需要实际的小组选择逻辑
                    { reason: '批量拒绝' }
            };
            
            const response = await this.makeApiRequest('/management.php?action=batch_operation', 'POST', data);
            if (response.error_code === 0) {
                this.loadDevices();
                this.showSuccess(`批量操作完成：成功处理 ${response.data.success_count} 台设备`);
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('批量操作失败:', error);
            this.showError('批量操作失败');
        }
    }

    /**
     * 处理超时设备
     */
    async handleOverdueDevices() {
        try {
            const response = await this.makeApiRequest('/checkin_management.php?action=overdue_devices', 'GET');
            if (response.error_code === 0 && response.data.overdue_devices.length > 0) {
                const deviceIds = response.data.overdue_devices.map(device => device.device_id);
                
                const remindData = {
                    device_ids: deviceIds,
                    message: '您的设备签到已超时，请及时签到'
                };
                
                const remindResponse = await this.makeApiRequest('/checkin_management.php?action=batch_remind', 'POST', remindData);
                if (remindResponse.error_code === 0) {
                    this.showSuccess(`已向 ${remindResponse.data.success_count} 台设备发送签到提醒`);
                }
            }
        } catch (error) {
            console.error('处理超时设备失败:', error);
            this.showError('处理超时设备失败');
        }
    }

    /**
     * 显示成功信息
     */
    showSuccess(message) {
        // 这里可以接入现有的成功提示系统
        alert(message);
    }
}

// 立即导出模块到全局作用域，确保模块加载器能够找到
window.DeviceManager = DeviceManager;

// 为模块加载器导出整个模块
window.DeviceManagementModule = {
    DeviceManager,
    version: '1.0.0',
    initialized: true,
    init: async function(container) {
        const instance = new DeviceManager();
        await instance.init(container);
        return instance;
    }
};

// 创建全局实例
window.deviceManager = new DeviceManager();

// 导出模块（Node.js环境兼容）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DeviceManager };
}

console.log('✅ 设备管理模块已导出到全局作用域，DeviceManagementModule:', window.DeviceManagementModule);

// 移除ES6 export语法，避免在浏览器环境中产生问题
// // export default DeviceManager; // 注释掉ES6导出，使用全局变量
window.DeviceManager = DeviceManager; 