<?php
/**
 * 多租户设备管理接口 - 四层架构版本
 * 支持基于租户的设备数据过滤和权限控制
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';
require_once dirname(dirname(__FILE__)) . '/utils/ErrorHandler.php';

// 设置全局异常处理器
ErrorHandler::setGlobalExceptionHandler();

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'devices':
        case 'get_devices':
            handleGetDevices($auth);
            break;
            
        case 'enable_device':
            handleEnableDevice($auth);
            break;
            
        case 'disable_device':
            handleDisableDevice($auth);
            break;
            
        case 'get_alipay_accounts':
            handleGetAlipayAccounts($auth);
            break;
            
        case 'update_alipay_account':
            handleUpdateAlipayAccount($auth);
            break;
            
        case 'update_device':
        case 'edit_device':
            handleUpdateDevice($auth);
            break;
            
        case 'delete_device':
            handleDeleteDevice($auth);
            break;
            
        case 'get_script_matrix':
            handleGetTenantScriptMatrix();
            break;
            
        case 'get_device_brands':
            handleGetTenantDeviceBrands();
            break;
            
        case 'get_providers':
            handleGetAvailableProviders();
            break;
            
        default:
            handleGetDevices($auth);
    }
} catch (Exception $e) {
    error_log("Devices API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

/**
 * 获取租户设备列表
 */
function handleGetDevices($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('device.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $providerId = isset($_GET['provider_id']) ? intval($_GET['provider_id']) : 0;
    
    $whereConditions = array();
    $params = array();
    
    // 数据隔离：非管理员只能查看自己相关的设备
    if ($currentUser['user_type'] === 'provider') {
        $whereConditions[] = "d.provider_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "d.id IN (SELECT device_id FROM merchant_devices WHERE merchant_id = ?)";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin' && $providerId > 0) {
        $whereConditions[] = "d.provider_id = ?";
        $params[] = $providerId;
    }
    
    if ($search) {
        $whereConditions[] = "(d.device_name LIKE ? OR d.device_key LIKE ? OR d.remark LIKE ?)";
        $searchTerm = '%' . $search . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if ($status && in_array($status, array('active', 'inactive', 'maintenance'))) {
        $whereConditions[] = "d.status = ?";
        $params[] = $status;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    try {
        // 获取设备列表
        $devices = $db->fetchAll(
            "SELECT d.*, 
                    p.company_name as provider_name,
                    u.real_name as provider_real_name
             FROM devices d
             LEFT JOIN payment_providers p ON d.provider_id = p.id
             LEFT JOIN users u ON p.user_id = u.id
             $whereClause
             ORDER BY d.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, array($limit, $offset))
        );
        
        // 获取总数
        $totalResult = $db->fetch(
            "SELECT COUNT(*) as count FROM devices d $whereClause",
            $params
        );
        $total = $totalResult['count'];
        
        // 获取统计信息
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_devices,
                COUNT(CASE WHEN d.status = 'active' THEN 1 END) as active_devices,
                COUNT(CASE WHEN d.status = 'inactive' THEN 1 END) as inactive_devices,
                COUNT(CASE WHEN d.status = 'maintenance' THEN 1 END) as maintenance_devices
             FROM devices d $whereClause",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_devices', 'device', 0, '查看设备列表');
        
        $response = array(
            'devices' => $devices,
            'stats' => $stats,
            'pagination' => array(
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            )
        );
        
        $auth->sendSuccess($response, '设备列表获取成功');
        
    } catch (Exception $e) {
        error_log("获取设备列表失败: " . $e->getMessage());
        $auth->sendError('获取设备列表失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 审核通过设备
 */
function handleEnableDevice($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('device.manage')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $deviceId = isset($_POST['device_id']) ? intval($_POST['device_id']) : 0;
    
    if ($deviceId <= 0) {
        $auth->sendError('无效的设备ID', 400);
        return;
    }
    
    try {
        // 检查设备是否存在
        $device = $db->fetch("SELECT * FROM devices WHERE id = ?", array($deviceId));
        if (!$device) {
            $auth->sendError('设备不存在', 404);
            return;
        }
        
        // 数据隔离检查
        if ($currentUser['user_type'] === 'provider' && $device['provider_id'] != $currentUser['profile_id']) {
            $auth->sendForbidden('无权限操作此设备');
            return;
        }
        
        // 启用设备
        $db->execute(
            "UPDATE devices SET status = 'active', updated_at = NOW() WHERE id = ?",
            array($deviceId)
        );
        
        // 记录操作日志
        $auth->logUserAction('enable_device', 'device', $deviceId, "启用设备: {$device['device_name']}");
        
        $auth->sendSuccess(null, '设备启用成功');
        
    } catch (Exception $e) {
        error_log("启用设备失败: " . $e->getMessage());
        $auth->sendError('启用设备失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 停用设备
 */
function handleDisableDevice($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('device.manage')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $deviceId = isset($_POST['device_id']) ? intval($_POST['device_id']) : 0;
    
    if ($deviceId <= 0) {
        $auth->sendError('无效的设备ID', 400);
        return;
    }
    
    try {
        // 检查设备是否存在
        $device = $db->fetch("SELECT * FROM devices WHERE id = ?", array($deviceId));
        if (!$device) {
            $auth->sendError('设备不存在', 404);
            return;
        }
        
        // 数据隔离检查
        if ($currentUser['user_type'] === 'provider' && $device['provider_id'] != $currentUser['profile_id']) {
            $auth->sendForbidden('无权限操作此设备');
            return;
        }
        
        // 禁用设备
        $db->execute(
            "UPDATE devices SET status = 'inactive', updated_at = NOW() WHERE id = ?",
            array($deviceId)
        );
        
        // 记录操作日志
        $auth->logUserAction('disable_device', 'device', $deviceId, "禁用设备: {$device['device_name']}");
        
        $auth->sendSuccess(null, '设备禁用成功');
        
    } catch (Exception $e) {
        error_log("禁用设备失败: " . $e->getMessage());
        $auth->sendError('禁用设备失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 获取可用的码商列表
 */
function handleGetAvailableProviders() {
    global $domainInfo;
    
    $providers = getAvailableProvidersForTenant();
    
    echo json_encode(array(
        'code' => 200,
        'message' => '获取成功',
        'data' => $providers
    ));
}

/**
 * 获取租户可用的码商列表
 */
function getAvailableProvidersForTenant() {
    global $tenantAuth, $domainInfo;
    
    $db = $tenantAuth->db;
    $providers = array();
    
    switch ($domainInfo['tenant_type']) {
        case 'system_admin':
            // 系统管理员可以看到所有码商
            $providers = $db->fetchAll("
                SELECT p.id, p.company_name, pl.platform_name 
                FROM payment_providers p 
                LEFT JOIN platforms pl ON p.platform_id = pl.id
                ORDER BY p.company_name
            ");
            break;
            
        case 'platform_admin':
            // 平台管理员只能看到本平台的码商
            $providers = $db->fetchAll("
                SELECT p.id, p.company_name, pl.platform_name 
                FROM payment_providers p 
                LEFT JOIN platforms pl ON p.platform_id = pl.id
                WHERE p.platform_id = ?
                ORDER BY p.company_name
            ", array($domainInfo['platform_id']));
            break;
            
        case 'provider':
            // 码商只能看到自己
            $providers = $db->fetchAll("
                SELECT p.id, p.company_name, pl.platform_name 
                FROM payment_providers p 
                LEFT JOIN platforms pl ON p.platform_id = pl.id
                WHERE p.id = ?
            ", array($domainInfo['tenant_id']));
            break;
            
        case 'merchant':
            // 商户不能创建设备，返回空数组
            break;
    }
    
    return $providers;
}

/**
 * 检查是否可以分配给指定码商
 */
function canAssignToProvider($providerId) {
    global $domainInfo;
    
    if (!$providerId) {
        return true; // 允许不分配码商
    }
    
    $availableProviders = getAvailableProvidersForTenant();
    $providerIds = array_column($availableProviders, 'id');
    
    return in_array($providerId, $providerIds);
}

/**
 * 获取租户设备统计
 */
function getTenantDeviceStats($tenantFilter) {
    global $tenantAuth;
    
    $db = $tenantAuth->db;
    $whereClause = !empty($tenantFilter['conditions']) ? 
        'WHERE ' . implode(' AND ', $tenantFilter['conditions']) : '';
    
    // 基础统计
    $totalSql = "SELECT COUNT(*) as count FROM devices d 
                 LEFT JOIN payment_providers p ON d.provider_id = p.id 
                 {$whereClause}";
    $total = $db->fetch($totalSql, $tenantFilter['params'])['count'];
    
    // 按状态统计
    $activeSql = "SELECT COUNT(*) as count FROM devices d 
                  LEFT JOIN payment_providers p ON d.provider_id = p.id 
                  {$whereClause} AND d.status = 'active'";
    $active = $db->fetch($activeSql, $tenantFilter['params'])['count'];
    
    $pendingSql = "SELECT COUNT(*) as count FROM devices d 
                   LEFT JOIN payment_providers p ON d.provider_id = p.id 
                   {$whereClause} AND d.status = 'pending'";
    $pending = $db->fetch($pendingSql, $tenantFilter['params'])['count'];
    
    // 在线设备统计（5分钟内有签到）
    $onlineSql = "SELECT COUNT(*) as count FROM devices d 
                  LEFT JOIN payment_providers p ON d.provider_id = p.id 
                  {$whereClause} AND d.last_checkin_time > DATE_SUB(NOW(), INTERVAL 5 MINUTE)";
    $online = $db->fetch($onlineSql, $tenantFilter['params'])['count'];
    
    return array(
        'total' => $total,
        'active' => $active,
        'pending' => $pending,
        'online' => $online,
        'offline' => $total - $online
    );
}

/**
 * 生成设备密钥
 */
function generateDeviceKey() {
    return 'DEV_' . strtoupper(bin2hex(random_bytes(16)));
}

// 其他函数（更新、删除等）将在后续实现
function handleUpdateDevice($auth) {
    // 检查权限
    if (!$auth->checkPermission('device.manage')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

function handleDeleteDevice($auth) {
    // 检查权限
    if (!$auth->checkPermission('device.manage')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

function handleGetTenantScriptMatrix() {
    echo json_encode(array('code' => 501, 'message' => '功能开发中'));
}

function handleGetTenantDeviceBrands() {
    echo json_encode(array('code' => 501, 'message' => '功能开发中'));
}

/**
 * 获取设备的支付宝账户列表
 */
function handleGetAlipayAccounts($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('alipay.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $whereConditions = array();
    $params = array();
    
    // 数据隔离
    if ($currentUser['user_type'] === 'provider') {
        $whereConditions[] = "provider_id = ?";
        $params[] = $currentUser['profile_id'];
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    try {
        $accounts = $db->fetchAll(
            "SELECT a.*, p.company_name as provider_name
             FROM alipay_accounts a
             LEFT JOIN payment_providers p ON a.provider_id = p.id
             $whereClause
             ORDER BY a.created_at DESC",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_alipay_accounts', 'alipay', 0, '查看支付宝账户');
        
        $auth->sendSuccess(array('accounts' => $accounts), '支付宝账户获取成功');
        
    } catch (Exception $e) {
        error_log("获取支付宝账户失败: " . $e->getMessage());
        $auth->sendError('获取支付宝账户失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 更新支付宝账户配置
 */
function handleUpdateAlipayAccount($auth) {
    // 检查权限
    if (!$auth->checkPermission('alipay.edit')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

/**
 * 检查是否可以管理设备（小组长或大组长）
 */
function canManageDevice($device) {
    global $currentUser;
    
    // 检查是否是小组长或大组长
    return ($device['team_leader_id'] == $currentUser['id']) || 
           ($device['group_manager_id'] == $currentUser['id']);
}

/**
 * 检查是否可以管理支付宝账户（小组长或大组长）
 */
function canManageAlipayAccount($account) {
    global $currentUser;
    
    // 检查是否是小组长或大组长
    return ($account['team_leader_id'] == $currentUser['id']) || 
           ($account['group_manager_id'] == $currentUser['id']);
}
?> 