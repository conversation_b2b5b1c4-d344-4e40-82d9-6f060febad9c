<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';

$db = new Database();
$auth = new Auth();

// 获取Authorization头（兼容FastCGI）
function getAllHeaders() {
    $headers = array();
    foreach ($_SERVER as $name => $value) {
        if (substr($name, 0, 5) == 'HTTP_') {
            $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5))))] = $value;
        }
    }
    return $headers;
}

$headers = getAllHeaders();
$token = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = str_replace('Bearer ', '', $token);

// 对于登录接口和脚本上传接口，跳过token验证
if (isset($_GET['action']) && ($_GET['action'] === 'login' || $_GET['action'] === 'upload_script')) {
    $currentUser = null;
} else {
    // 验证用户身份
    $currentUser = $auth->getCurrentUser($token);
    if (!$currentUser) {
        // 获取更详细的token验证信息
        $userId = $auth->verifyToken($token);
        $tokenParts = explode('.', $token);
        
        $debugInfo = array(
            'token_present' => !empty($token),
            'token_length' => strlen($token),
            'token_parts' => count($tokenParts),
            'token_preview' => substr($token, 0, 20) . '...',
            'action' => isset($_GET['action']) ? $_GET['action'] : null,
            'user_id_from_token' => $userId,
            'headers' => getAllHeaders()
        );
        
        // 如果token长度为4且内容为null，说明前端传递了字符串"null"
        if (strlen($token) === 4 && $token === 'null') {
            $debugInfo['issue'] = 'Token is literal string "null"';
        }
        
        http_response_code(401);
        echo json_encode(array(
            'code' => 401, 
            'message' => '未授权访问',
            'debug' => $debugInfo
        ));
        exit;
    }
}

// 获取操作类型
$action = isset($_GET['action']) ? $_GET['action'] : '';
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($action) {
        // 登录
        case 'login':
            handleLogin();
            break;
            
        // 仪表板数据
        case 'dashboard':
            handleDashboard();
            break;
            
        // 用户管理
        case 'users':
            handleUsers();
            break;
            
        // 员工管理
        case 'employees':
            handleEmployees();
            break;
            
        // 职位管理（包含权限配置）
        case 'job_positions':
            handleJobPositions();
            break;
            
        // 设备管理
        case 'devices':
            handleDevices();
            break;
            
        // 码商管理
        case 'providers':
            handleProviders();
            break;
            
        // 商户管理
        case 'merchants':
            handleMerchants();
            break;
            
        // 商户API密钥管理
        case 'merchant_api_keys':
            handleMerchantApiKeys();
            break;
            
        // 产品管理
        case 'products':
            handleProducts();
            break;
            
        // 费率管理
        case 'rates':
            handleRates();
            break;
            
        // 支付请求管理
        case 'payment_requests':
            handlePaymentRequests();
            break;
            
        // 支付请求详情
        case 'payment_request_detail':
            handlePaymentRequestDetail();
            break;
            
        // 更新支付状态
        case 'update_payment_status':
            handleUpdatePaymentStatus();
            break;
            
        // 重试支付请求
        case 'retry_payment_request':
            handleRetryPaymentRequest();
            break;
            
        // 订单管理
        case 'orders':
            handleOrders();
            break;
            
        // 财务管理
        case 'financial':
            handleFinancial();
            break;
            
        // 通知管理
        case 'notifications':
            handleNotifications();
            break;
            
        // 日报表管理
        case 'daily_reports':
            handleDailyReports();
            break;
            
        // 风控管理
        case 'risk_control':
            handleRiskControl();
            break;
            
        // 黑名单管理
        case 'blacklist':
            handleBlacklist();
            break;
            
        // 性能监控
        case 'performance_monitor':
            handlePerformanceMonitor();
            break;
            
        // TOTP双因子认证
        case 'totp':
            handleTOTP();
            break;
            
        // 安全日志
        case 'security_logs':
            handleSecurityLogs();
            break;
            
        // 支付宝账户管理
        case 'alipay_accounts':
            handleAlipayAccounts();
            break;
            
        // 交易记录
        case 'transactions':
            handleTransactions();
            break;
            
        // 脚本管理相关接口
        case 'get_device_brands':
            handleGetDeviceBrands();
            break;
            
        case 'get_script_matrix':
            handleGetScriptMatrix();
            break;
            
        case 'get_brand_script':
            handleGetBrandScript();
            break;
            
        case 'upload_brand_script':
            handleUploadBrandScript();
            break;
            
        case 'upload_script':
            handleUploadScriptEncrypted();
            break;
            
        default:
            throw new Exception('不支持的操作');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'code' => 400,
        'message' => $e->getMessage(),
        'data' => null
    ));
}

// 处理登录（不需要token验证）
function handleLogin() {
    global $auth, $currentUser;
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $username = isset($input['username']) ? $input['username'] : '';
    $password = isset($input['password']) ? $input['password'] : '';
    
    if (!$username || !$password) {
        throw new Exception('用户名和密码不能为空');
    }
    
    $result = $auth->login($username, $password);
    echo json_encode($result);
}

// 仪表板数据
function handleDashboard() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'view_dashboard')) {
        throw new Exception('无权限访问');
    }
    
    // 根据用户类型返回不同的仪表板数据
    $data = array();
    
    if ($currentUser['user_type'] === 'admin') {
        // 管理员仪表板
        $data = array(
            'total_users' => $db->fetch("SELECT COUNT(*) as count FROM users")['count'],
            'total_devices' => $db->fetch("SELECT COUNT(*) as count FROM devices")['count'],
            'total_providers' => $db->fetch("SELECT COUNT(*) as count FROM payment_providers WHERE status = 'approved'")['count'],
            'total_merchants' => $db->fetch("SELECT COUNT(*) as count FROM merchants WHERE status = 'approved'")['count'],
            'pending_providers' => $db->fetch("SELECT COUNT(*) as count FROM payment_providers WHERE status = 'pending'")['count'],
            'pending_merchants' => $db->fetch("SELECT COUNT(*) as count FROM merchants WHERE status = 'pending'")['count'],
            'today_transactions' => $db->fetch("SELECT COUNT(*) as count FROM transactions WHERE DATE(created_at) = CURDATE()")['count'],
            'today_amount' => $db->fetch("SELECT COALESCE(SUM(amount), 0) as amount FROM transactions WHERE DATE(created_at) = CURDATE() AND status = 'success'")['amount']
        );
    } elseif ($currentUser['user_type'] === 'provider') {
        // 码商仪表板
        $providerId = $currentUser['profile_id'];
        $data = array(
            'my_devices' => $db->fetch("SELECT COUNT(*) as count FROM devices WHERE provider_id = ?", array($providerId))['count'],
            'active_devices' => $db->fetch("SELECT COUNT(*) as count FROM devices WHERE provider_id = ? AND status = 'active'", array($providerId))['count'],
            'my_alipay_accounts' => $db->fetch("SELECT COUNT(*) as count FROM alipay_accounts WHERE provider_id = ?", array($providerId))['count'],
            'today_transactions' => $db->fetch("SELECT COUNT(*) as count FROM transactions WHERE provider_id = ? AND DATE(created_at) = CURDATE()", array($providerId))['count'],
            'today_amount' => $db->fetch("SELECT COALESCE(SUM(amount), 0) as amount FROM transactions WHERE provider_id = ? AND DATE(created_at) = CURDATE() AND status = 'success'", array($providerId))['amount']
        );
    } elseif ($currentUser['user_type'] === 'merchant') {
        // 商户仪表板
        $merchantId = $currentUser['profile_id'];
        $successRateResult = $db->fetch("SELECT ROUND(AVG(CASE WHEN status = 'success' THEN 100 ELSE 0 END), 2) as rate FROM transactions WHERE merchant_id = ?", array($merchantId));
        $data = array(
            'my_transactions' => $db->fetch("SELECT COUNT(*) as count FROM transactions WHERE merchant_id = ?", array($merchantId))['count'],
            'today_transactions' => $db->fetch("SELECT COUNT(*) as count FROM transactions WHERE merchant_id = ? AND DATE(created_at) = CURDATE()", array($merchantId))['count'],
            'today_amount' => $db->fetch("SELECT COALESCE(SUM(amount), 0) as amount FROM transactions WHERE merchant_id = ? AND DATE(created_at) = CURDATE() AND status = 'success'", array($merchantId))['amount'],
            'success_rate' => isset($successRateResult['rate']) ? $successRateResult['rate'] : 0
        );
    }
    
    echo json_encode(array('code' => 200, 'data' => $data));
}

// 用户管理 - 完整的CRUD功能
function handleUsers() {
    global $db, $currentUser, $auth;
    
    // 检查用户权限：只有管理员可以管理用户
    if (!in_array($currentUser['user_type'], ['admin'])) {
        throw new Exception('无权限访问用户管理');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetUsers();
            break;
        case 'POST':
            handleCreateUser();
            break;
        case 'PUT':
            handleUpdateUser();
            break;
        case 'DELETE':
            handleDeleteUser();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取用户列表
function handleGetUsers() {
    global $db;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $userType = isset($_GET['user_type']) ? $_GET['user_type'] : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    if (!empty($userType) && in_array($userType, array('admin', 'provider', 'merchant'))) {
        $whereConditions[] = "u.user_type = ?";
        $params[] = $userType;
    }
    
    if (!empty($status) && in_array($status, array('active', 'inactive', 'suspended'))) {
        $whereConditions[] = "u.status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(u.username LIKE ? OR u.real_name LIKE ? OR u.email LIKE ? OR u.phone LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM users u $whereClause",
        $params
    );
    $total = $totalResult['count'];
    
    // 获取用户列表
    $users = $db->fetchAll(
        "SELECT u.*, 
                CASE 
                    WHEN u.user_type = 'provider' THEN p.company_name
                    WHEN u.user_type = 'merchant' THEN m.company_name
                    ELSE u.real_name
                END as display_name,
                CASE 
                    WHEN u.user_type = 'provider' THEN p.id
                    WHEN u.user_type = 'merchant' THEN m.id
                    ELSE NULL
                END as profile_id,
                CASE 
                    WHEN u.user_type = 'provider' THEN p.status
                    WHEN u.user_type = 'merchant' THEN m.status
                    ELSE u.status
                END as profile_status,
                u.last_login,
                (SELECT COUNT(*) FROM user_sessions WHERE user_id = u.id AND expires_at > NOW()) as active_sessions
         FROM users u
         LEFT JOIN payment_providers p ON u.id = p.user_id AND u.user_type = 'provider'
         LEFT JOIN merchants m ON u.id = m.user_id AND u.user_type = 'merchant'
         $whereClause
         ORDER BY u.created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取统计信息
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN u.user_type = 'admin' THEN 1 END) as admin_users,
            COUNT(CASE WHEN u.user_type = 'provider' THEN 1 END) as provider_users,
            COUNT(CASE WHEN u.user_type = 'merchant' THEN 1 END) as merchant_users,
            COUNT(CASE WHEN u.status = 'active' THEN 1 END) as active_users,
            COUNT(CASE WHEN u.status = 'inactive' THEN 1 END) as inactive_users,
            COUNT(CASE WHEN u.status = 'suspended' THEN 1 END) as suspended_users,
            COUNT(CASE WHEN u.last_login > DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as daily_active_users,
            COUNT(CASE WHEN u.last_login > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekly_active_users
         FROM users u"
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'users' => $users,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        )
    ));
}

// 创建用户
function handleCreateUser() {
    global $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    $required_fields = ['username', 'real_name', 'email', 'password', 'user_type'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    // 验证用户类型
    if (!in_array($input['user_type'], ['admin', 'provider', 'merchant'])) {
        throw new Exception('无效的用户类型');
    }
    
    // 检查用户名和邮箱是否已存在
    $existingUser = $db->fetch("SELECT id FROM users WHERE username = ? OR email = ?", 
                               array($input['username'], $input['email']));
    if ($existingUser) {
        throw new Exception('用户名或邮箱已存在');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 创建用户账户
        $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);
        $db->query(
            "INSERT INTO users (username, real_name, email, phone, password, user_type, status, created_at) 
             VALUES (?, ?, ?, ?, ?, ?, ?, NOW())",
            array(
                $input['username'],
                $input['real_name'],
                $input['email'],
                isset($input['phone']) ? $input['phone'] : '',
                $hashedPassword,
                $input['user_type'],
                isset($input['status']) ? $input['status'] : 'active'
            )
        );
        
        $userId = $db->lastInsertId();
        
        // 根据用户类型创建对应的配置文件
        if ($input['user_type'] === 'provider' && !empty($input['company_name'])) {
            $db->query(
                "INSERT INTO payment_providers (
                    user_id, company_name, business_license, business_type, contact_person, 
                    contact_phone, contact_email, settlement_rate, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())",
                array(
                    $userId,
                    $input['company_name'],
                    isset($input['business_license']) ? $input['business_license'] : '',
                    isset($input['business_type']) ? $input['business_type'] : '',
                    $input['real_name'],
                    isset($input['phone']) ? $input['phone'] : '',
                    $input['email'],
                    floatval(isset($input['settlement_rate']) ? $input['settlement_rate'] : 0.95)
                )
            );
        } elseif ($input['user_type'] === 'merchant' && !empty($input['company_name'])) {
            $db->query(
                "INSERT INTO merchants (
                    user_id, company_name, business_license, contact_person, contact_phone, 
                    contact_email, website, business_type, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())",
                array(
                    $userId,
                    $input['company_name'],
                    isset($input['business_license']) ? $input['business_license'] : '',
                    $input['real_name'],
                    isset($input['phone']) ? $input['phone'] : '',
                    $input['email'],
                    isset($input['website']) ? $input['website'] : '',
                    isset($input['business_type']) ? $input['business_type'] : ''
                )
            );
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '用户创建成功',
            'data' => array('user_id' => $userId)
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 更新用户
function handleUpdateUser() {
    global $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $userId = isset($input['id']) ? intval($input['id']) : 0;
    
    if (!$userId) {
        throw new Exception('缺少用户ID');
    }
    
    // 检查用户是否存在
    $user = $db->fetch("SELECT * FROM users WHERE id = ?", array($userId));
    if (!$user) {
        throw new Exception('用户不存在');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 更新用户基本信息
        $userFields = array();
        $userParams = array();
        
        $updateableFields = array('real_name', 'email', 'phone', 'status');
        
        foreach ($updateableFields as $field) {
            if (isset($input[$field])) {
                if ($field === 'email' && !empty($input[$field])) {
                    // 检查邮箱是否被其他用户使用
                    $existingUser = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", 
                                             array($input[$field], $userId));
                    if ($existingUser) {
                        throw new Exception('邮箱已被其他用户使用');
                    }
                }
                $userFields[] = "$field = ?";
                $userParams[] = $input[$field];
            }
        }
        
        // 更新密码（如果提供）
        if (!empty($input['password'])) {
            $userFields[] = "password = ?";
            $userParams[] = password_hash($input['password'], PASSWORD_DEFAULT);
        }
        
        if (!empty($userFields)) {
            $userParams[] = $userId;
            $db->query(
                "UPDATE users SET " . implode(', ', $userFields) . " WHERE id = ?",
                $userParams
            );
        }
        
        // 更新对应的配置文件
        if ($user['user_type'] === 'provider' && isset($input['company_name'])) {
            $providerFields = array();
            $providerParams = array();
            
            $providerUpdateableFields = array('company_name', 'business_license', 'business_type', 
                                             'contact_person', 'contact_phone', 'contact_email', 'settlement_rate');
            
            foreach ($providerUpdateableFields as $field) {
                if (isset($input[$field])) {
                    $providerFields[] = "$field = ?";
                    $providerParams[] = $input[$field];
                }
            }
            
            if (!empty($providerFields)) {
                $providerParams[] = $userId;
                $db->query(
                    "UPDATE payment_providers SET " . implode(', ', $providerFields) . " WHERE user_id = ?",
                    $providerParams
                );
            }
        } elseif ($user['user_type'] === 'merchant' && isset($input['company_name'])) {
            $merchantFields = array();
            $merchantParams = array();
            
            $merchantUpdateableFields = array('company_name', 'business_license', 'contact_person', 
                                             'contact_phone', 'contact_email', 'website', 'business_type');
            
            foreach ($merchantUpdateableFields as $field) {
                if (isset($input[$field])) {
                    $merchantFields[] = "$field = ?";
                    $merchantParams[] = $input[$field];
                }
            }
            
            if (!empty($merchantFields)) {
                $merchantParams[] = $userId;
                $db->query(
                    "UPDATE merchants SET " . implode(', ', $merchantFields) . " WHERE user_id = ?",
                    $merchantParams
                );
            }
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '用户信息更新成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 删除用户
function handleDeleteUser() {
    global $db;
    
    $userId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if (!$userId) {
        throw new Exception('缺少用户ID');
    }
    
    // 检查用户是否存在
    $user = $db->fetch("SELECT * FROM users WHERE id = ?", array($userId));
    if (!$user) {
        throw new Exception('用户不存在');
    }
    
    // 检查是否有关联数据
    $deviceCount = 0;
    $transactionCount = 0;
    
    if ($user['user_type'] === 'provider') {
        $provider = $db->fetch("SELECT id FROM payment_providers WHERE user_id = ?", array($userId));
        if ($provider) {
            $deviceCount = $db->fetch("SELECT COUNT(*) as count FROM devices WHERE provider_id = ?", array($provider['id']));
            $transactionCount = $db->fetch("SELECT COUNT(*) as count FROM transactions WHERE provider_id = ?", array($provider['id']));
        }
    } elseif ($user['user_type'] === 'merchant') {
        $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", array($userId));
        if ($merchant) {
            $transactionCount = $db->fetch("SELECT COUNT(*) as count FROM transactions WHERE merchant_id = ?", array($merchant['id']));
        }
    }
    
    if ($deviceCount['count'] > 0 || $transactionCount['count'] > 0) {
        throw new Exception('该用户还有关联的设备或交易记录，无法删除');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 删除用户对应的配置文件
        if ($user['user_type'] === 'provider') {
            $db->query("DELETE FROM payment_providers WHERE user_id = ?", array($userId));
        } elseif ($user['user_type'] === 'merchant') {
            $db->query("DELETE FROM merchants WHERE user_id = ?", array($userId));
        }
        
        // 删除用户会话
        $db->query("DELETE FROM user_sessions WHERE user_id = ?", array($userId));
        
        // 删除用户账户
        $db->query("DELETE FROM users WHERE id = ?", array($userId));
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '用户删除成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 设备管理
function handleDevices() {
    global $db, $currentUser, $auth;
    
    // 检查用户权限：管理员和码商可以访问设备管理
    if (!in_array($currentUser['user_type'], ['admin', 'provider'])) {
        throw new Exception('无权限访问设备管理');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        handleGetDevices();
    } elseif ($method === 'POST') {
        handleCreateOrUpdateDevice();
    } elseif ($method === 'DELETE') {
        handleDeleteDevice();
    } else {
        throw new Exception('不支持的HTTP方法');
    }
}

// 获取设备列表 - 添加数据隔离
function handleGetDevices() {
    global $db, $currentUser, $auth;
    
    // 检查用户权限：管理员和码商可以访问设备管理
    if (!in_array($currentUser['user_type'], ['admin', 'provider'])) {
        throw new Exception('无权限访问设备管理');
    }
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 10);
    $offset = ($page - 1) * $limit;
    
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $brand = isset($_GET['brand']) ? $_GET['brand'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    // 构建查询条件
    $whereConditions = array();
    $params = array();
    
    // 根据用户类型进行数据隔离
    if ($currentUser['user_type'] === 'provider') {
        // 码商只能看到分配给自己的设备
        $whereConditions[] = "d.provider_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'merchant') {
        // 商户只能看到通过自己码商的设备（如果有的话）
        $whereConditions[] = "p.id IN (SELECT assigned_provider_id FROM merchants WHERE user_id = ?)";
        $params[] = $currentUser['id'];
    }
    // 管理员可以看到所有设备（不添加额外条件）
    
    if (!empty($status) && in_array($status, array('pending', 'active', 'disabled'))) {
        $whereConditions[] = "d.status = ?";
        $params[] = $status;
    }
    
    if (!empty($brand)) {
        $whereConditions[] = "d.device_brand = ?";
        $params[] = $brand;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(d.device_id LIKE ? OR d.device_name LIKE ? OR d.device_brand LIKE ? OR d.device_model LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取设备总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count 
         FROM devices d 
         LEFT JOIN payment_providers p ON d.provider_id = p.id 
         $whereClause",
        $params
    );
    $total = $totalResult['count'];
    
    // 获取设备列表
    $devices = $db->fetchAll(
        "SELECT d.*, 
                p.company_name as provider_name,
                p.id as provider_id,
                CASE 
                    WHEN d.last_online IS NULL THEN '从未登录'
                    WHEN d.last_online > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN '在线'
                    WHEN d.last_online > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN '1小时内活跃'
                    WHEN d.last_online > DATE_SUB(NOW(), INTERVAL 1 DAY) THEN '今日活跃'
                    ELSE '离线'
                END as online_status
         FROM devices d 
         LEFT JOIN payment_providers p ON d.provider_id = p.id 
         $whereClause
         ORDER BY d.created_at DESC 
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取统计信息（基于用户权限）
    $statsParams = array();
    $statsWhere = "";
    
    if ($currentUser['user_type'] === 'provider') {
        $statsWhere = "WHERE provider_id = ?";
        $statsParams[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'merchant') {
        $statsWhere = "WHERE provider_id IN (SELECT assigned_provider_id FROM merchants WHERE user_id = ?)";
        $statsParams[] = $currentUser['id'];
    }
    
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_devices,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_devices,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_devices,
            COUNT(CASE WHEN status = 'disabled' THEN 1 END) as disabled_devices,
            COUNT(CASE WHEN last_online > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1 END) as online_devices
         FROM devices $statsWhere",
        $statsParams
    );
    
    // 获取设备品牌列表（基于用户权限）
    $brands = $db->fetchAll(
        "SELECT DISTINCT device_brand 
         FROM devices d 
         LEFT JOIN payment_providers p ON d.provider_id = p.id 
         $whereClause AND device_brand IS NOT NULL AND device_brand != '' 
         ORDER BY device_brand",
        $params
    );
    
    // 获取码商列表（仅管理员可见）
    $providers = array();
    if ($currentUser['user_type'] === 'admin') {
        $providers = $db->fetchAll("SELECT id, company_name FROM payment_providers WHERE status = 'approved' ORDER BY company_name");
    }
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'devices' => $devices,
            'stats' => $stats,
            'brands' => array_column($brands, 'device_brand'),
            'providers' => $providers,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            ),
            'user_info' => array(
                'user_type' => $currentUser['user_type'],
                'provider_id' => $currentUser['user_type'] === 'provider' ? $currentUser['profile_id'] : null
            )
        )
    ));
}

// 更新设备状态或分配码商
function handleCreateOrUpdateDevice() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = isset($input['action']) ? $input['action'] : '';
    
    switch ($action) {
        case 'assign_provider':
            handleAssignProvider($input);
            break;
        case 'update_status':
            handleUpdateStatus($input);
            break;
        case 'reset_password':
            handleResetPassword($input);
            break;
        default:
            throw new Exception('不支持的操作');
    }
}

// 分配码商
function handleAssignProvider($input) {
    global $db, $currentUser;
    
    if ($currentUser['user_type'] !== 'admin') {
        throw new Exception('只有管理员可以分配码商');
    }
    
    $deviceId = isset($input['device_id']) ? $input['device_id'] : '';
    $providerId = isset($input['provider_id']) ? $input['provider_id'] : '';
    
    if (!$deviceId || !$providerId) {
        throw new Exception('缺少必要参数');
    }
    
    // 验证设备存在
    $device = $db->fetch("SELECT * FROM devices WHERE device_id = ?", array($deviceId));
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    // 验证码商存在
    $provider = $db->fetch("SELECT * FROM payment_providers WHERE id = ? AND status = 'approved'", array($providerId));
    if (!$provider) {
        throw new Exception('码商不存在或未审核通过');
    }
    
    // 更新设备
    $db->execute(
        "UPDATE devices SET provider_id = ?, status = 'active', updated_at = NOW() WHERE device_id = ?",
        array($providerId, $deviceId)
    );
    
    echo json_encode(array(
        'code' => 200,
        'message' => '设备分配成功',
        'data' => null
    ));
}

// 更新设备状态
function handleUpdateStatus($input) {
    global $db, $currentUser;
    
    $deviceId = isset($input['device_id']) ? $input['device_id'] : '';
    $status = isset($input['status']) ? $input['status'] : '';
    
    if (!$deviceId || !$status) {
        throw new Exception('缺少必要参数');
    }
    
    if (!in_array($status, array('active', 'disabled', 'pending'))) {
        throw new Exception('无效的设备状态');
    }
    
    // 验证权限
    if ($currentUser['user_type'] === 'provider') {
        // 码商只能管理自己的设备
        $device = $db->fetch("SELECT * FROM devices WHERE device_id = ? AND provider_id = ?", 
                            array($deviceId, $currentUser['profile_id']));
        if (!$device) {
            throw new Exception('设备不存在或无权限操作');
        }
    }
    
    $db->execute(
        "UPDATE devices SET status = ?, updated_at = NOW() WHERE device_id = ?",
        array($status, $deviceId)
    );
    
    echo json_encode(array(
        'code' => 200,
        'message' => '设备状态更新成功',
        'data' => null
    ));
}

// 重置设备密码
function handleResetPassword($input) {
    global $db, $currentUser;
    
    $deviceId = isset($input['device_id']) ? $input['device_id'] : '';
    
    if (!$deviceId) {
        throw new Exception('缺少设备ID参数');
    }
    
    // 验证权限
    if ($currentUser['user_type'] === 'provider') {
        // 码商只能管理自己的设备
        $device = $db->fetch("SELECT * FROM devices WHERE device_id = ? AND provider_id = ?", 
                            array($deviceId, $currentUser['profile_id']));
        if (!$device) {
            throw new Exception('设备不存在或无权限操作');
        }
    }
    
    $db->execute(
        "UPDATE devices SET password = NULL, password_set_at = NULL, updated_at = NOW() WHERE device_id = ?",
        array($deviceId)
    );
    
    echo json_encode(array(
        'code' => 200,
        'message' => '设备密码重置成功，用户下次登录时需要重新设置密码',
        'data' => null
    ));
}

// 删除设备
function handleDeleteDevice() {
    global $db, $currentUser;
    
    if ($currentUser['user_type'] !== 'admin') {
        throw new Exception('只有管理员可以删除设备');
    }
    
    $deviceId = isset($_GET['device_id']) ? $_GET['device_id'] : '';
    
    if (!$deviceId) {
        throw new Exception('缺少设备ID参数');
    }
    
    // 检查设备是否存在
    $device = $db->fetch("SELECT * FROM devices WHERE device_id = ?", array($deviceId));
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    // 删除设备（注意：这会级联删除相关数据）
    $db->execute("DELETE FROM devices WHERE device_id = ?", array($deviceId));
    
    echo json_encode(array(
        'code' => 200,
        'message' => '设备删除成功',
        'data' => null
    ));
}

// 码商管理 - 完整的CRUD功能
function handleProviders() {
    global $db, $currentUser, $auth;
    
    // 检查用户权限：只有管理员可以管理码商
    if (!in_array($currentUser['user_type'], ['admin'])) {
        throw new Exception('无权限访问码商管理');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetProviders();
            break;
        case 'POST':
            handleCreateProvider();
            break;
        case 'PUT':
            handleUpdateProvider();
            break;
        case 'DELETE':
            handleDeleteProvider();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取码商列表
function handleGetProviders() {
    global $db;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    if (!empty($status) && in_array($status, array('pending', 'approved', 'suspended', 'rejected'))) {
        $whereConditions[] = "p.status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(u.real_name LIKE ? OR u.username LIKE ? OR u.email LIKE ? OR p.company_name LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM payment_providers p 
         JOIN users u ON p.user_id = u.id 
         $whereClause",
        $params
    );
    $total = $totalResult['count'];
    
    // 获取码商列表
    $providers = $db->fetchAll(
        "SELECT p.*, 
                u.username, u.real_name, u.email, u.phone,
                u.created_at as user_created_at,
                (SELECT COUNT(*) FROM devices d WHERE d.provider_id = p.id) as device_count,
                (SELECT COUNT(*) FROM alipay_accounts a WHERE a.provider_id = p.id) as account_count,
                (SELECT COUNT(*) FROM transactions t WHERE t.provider_id = p.id) as transaction_count,
                (SELECT SUM(amount) FROM transactions t WHERE t.provider_id = p.id AND t.status = 'success') as total_amount
         FROM payment_providers p
         JOIN users u ON p.user_id = u.id
         $whereClause
         ORDER BY p.created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取统计信息
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_providers,
            COUNT(CASE WHEN p.status = 'pending' THEN 1 END) as pending_providers,
            COUNT(CASE WHEN p.status = 'approved' THEN 1 END) as approved_providers,
            COUNT(CASE WHEN p.status = 'suspended' THEN 1 END) as suspended_providers,
            COUNT(CASE WHEN p.status = 'rejected' THEN 1 END) as rejected_providers
         FROM payment_providers p"
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'providers' => $providers,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        )
    ));
}

// 创建码商
function handleCreateProvider() {
    global $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    $required_fields = ['username', 'real_name', 'email', 'password', 'company_name'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    // 检查用户名和邮箱是否已存在
    $existingUser = $db->fetch("SELECT id FROM users WHERE username = ? OR email = ?", 
                               array($input['username'], $input['email']));
    if ($existingUser) {
        throw new Exception('用户名或邮箱已存在');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 创建用户账户
        $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);
        $db->query(
            "INSERT INTO users (username, real_name, email, phone, password, user_type, status, created_at) 
             VALUES (?, ?, ?, ?, ?, 'provider', 'active', NOW())",
            array(
                $input['username'],
                $input['real_name'],
                $input['email'],
                isset($input['phone']) ? $input['phone'] : '',
                $hashedPassword
            )
        );
        
        $userId = $db->lastInsertId();
        
        // 创建码商记录
        $db->query(
            "INSERT INTO payment_providers (
                user_id, company_name, business_license, business_type, contact_person, 
                contact_phone, contact_email, settlement_rate, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())",
            array(
                $userId,
                $input['company_name'],
                isset($input['business_license']) ? $input['business_license'] : '',
                isset($input['business_type']) ? $input['business_type'] : '',
                $input['real_name'],
                isset($input['phone']) ? $input['phone'] : '',
                $input['email'],
                floatval(isset($input['settlement_rate']) ? $input['settlement_rate'] : 0.95) // 默认95%结算率
            )
        );
        
        $providerId = $db->lastInsertId();
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '码商创建成功',
            'data' => array(
                'provider_id' => $providerId,
                'user_id' => $userId
            )
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 更新码商
function handleUpdateProvider() {
    global $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $providerId = isset($input['id']) ? intval($input['id']) : 0;
    
    if (!$providerId) {
        throw new Exception('缺少码商ID');
    }
    
    // 检查码商是否存在
    $provider = $db->fetch("SELECT * FROM payment_providers WHERE id = ?", array($providerId));
    if (!$provider) {
        throw new Exception('码商不存在');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 更新用户信息
        if (isset($input['real_name']) || isset($input['email']) || isset($input['phone'])) {
            $userFields = array();
            $userParams = array();
            
            if (isset($input['real_name']) && !empty($input['real_name'])) {
                $userFields[] = "real_name = ?";
                $userParams[] = $input['real_name'];
            }
            
            if (isset($input['email']) && !empty($input['email'])) {
                // 检查邮箱是否被其他用户使用
                $existingUser = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", 
                                         array($input['email'], $provider['user_id']));
                if ($existingUser) {
                    throw new Exception('邮箱已被其他用户使用');
                }
                $userFields[] = "email = ?";
                $userParams[] = $input['email'];
            }
            
            if (isset($input['phone'])) {
                $userFields[] = "phone = ?";
                $userParams[] = $input['phone'];
            }
            
            if (!empty($userFields)) {
                $userParams[] = $provider['user_id'];
                $db->query(
                    "UPDATE users SET " . implode(', ', $userFields) . " WHERE id = ?",
                    $userParams
                );
            }
        }
        
        // 更新码商信息
        $providerFields = array();
        $providerParams = array();
        
        $updateableFields = array('company_name', 'business_license', 'business_type', 
                                 'contact_person', 'contact_phone', 'contact_email', 
                                 'settlement_rate', 'status');
        
        foreach ($updateableFields as $field) {
            if (isset($input[$field])) {
                $providerFields[] = "$field = ?";
                $providerParams[] = $input[$field];
            }
        }
        
        if (!empty($providerFields)) {
            $providerParams[] = $providerId;
            $db->query(
                "UPDATE payment_providers SET " . implode(', ', $providerFields) . " WHERE id = ?",
                $providerParams
            );
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '码商信息更新成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 删除码商
function handleDeleteProvider() {
    global $db;
    
    $providerId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if (!$providerId) {
        throw new Exception('缺少码商ID');
    }
    
    // 检查码商是否存在
    $provider = $db->fetch("SELECT * FROM payment_providers WHERE id = ?", array($providerId));
    if (!$provider) {
        throw new Exception('码商不存在');
    }
    
    // 检查是否有关联的设备或交易
    $deviceCount = $db->fetch("SELECT COUNT(*) as count FROM devices WHERE provider_id = ?", array($providerId));
    $transactionCount = $db->fetch("SELECT COUNT(*) as count FROM transactions WHERE provider_id = ?", array($providerId));
    
    if ($deviceCount['count'] > 0 || $transactionCount['count'] > 0) {
        throw new Exception('该码商还有关联的设备或交易记录，无法删除');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 删除码商记录
        $db->query("DELETE FROM payment_providers WHERE id = ?", array($providerId));
        
        // 删除用户账户
        $db->query("DELETE FROM users WHERE id = ?", array($provider['user_id']));
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '码商删除成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 商户管理 - 完整的CRUD功能
function handleMerchants() {
    global $db, $currentUser, $auth;
    
    // 检查用户权限：只有管理员可以管理商户
    if (!in_array($currentUser['user_type'], ['admin'])) {
        throw new Exception('无权限访问商户管理');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetMerchants();
            break;
        case 'POST':
            handleCreateMerchant();
            break;
        case 'PUT':
            handleUpdateMerchant();
            break;
        case 'DELETE':
            handleDeleteMerchant();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取商户列表
function handleGetMerchants() {
    global $db;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    if (!empty($status) && in_array($status, array('pending', 'active', 'suspended', 'rejected'))) {
        $whereConditions[] = "m.status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(u.real_name LIKE ? OR u.username LIKE ? OR u.email LIKE ? OR m.company_name LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM merchants m 
         JOIN users u ON m.user_id = u.id 
         $whereClause",
        $params
    );
    $total = $totalResult['count'];
    
    // 获取商户列表
    $merchants = $db->fetchAll(
        "SELECT m.*, 
                u.username, u.real_name, u.email, u.phone,
                u.created_at as user_created_at,
                (SELECT COUNT(*) FROM products p WHERE p.merchant_id = m.id) as product_count,
                (SELECT COUNT(*) FROM transactions t WHERE t.merchant_id = m.id) as transaction_count,
                (SELECT SUM(amount) FROM transactions t WHERE t.merchant_id = m.id AND t.status = 'paid') as total_amount
         FROM merchants m
         JOIN users u ON m.user_id = u.id
         $whereClause
         ORDER BY m.created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取统计信息
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_merchants,
            COUNT(CASE WHEN m.status = 'pending' THEN 1 END) as pending_merchants,
            COUNT(CASE WHEN m.status = 'active' THEN 1 END) as active_merchants,
            COUNT(CASE WHEN m.status = 'suspended' THEN 1 END) as suspended_merchants,
            COUNT(CASE WHEN m.status = 'rejected' THEN 1 END) as rejected_merchants
         FROM merchants m"
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'merchants' => $merchants,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        )
    ));
}

// 创建商户
function handleCreateMerchant() {
    global $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    $required_fields = ['username', 'real_name', 'email', 'password'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    // 检查用户名和邮箱是否已存在
    $existingUser = $db->fetch("SELECT id FROM users WHERE username = ? OR email = ?", 
                               [$input['username'], $input['email']]);
    if ($existingUser) {
        throw new Exception('用户名或邮箱已存在');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 创建用户账户
        $hashedPassword = password_hash($input['password'], PASSWORD_DEFAULT);
        $db->query(
            "INSERT INTO users (username, real_name, email, phone, password, user_type, status, created_at) 
             VALUES (?, ?, ?, ?, ?, 'merchant', 'active', NOW())",
            [
                $input['username'],
                $input['real_name'],
                $input['email'],
                isset($input['phone']) ? $input['phone'] : '',
                $hashedPassword
            ]
        );
        
        $userId = $db->lastInsertId();
        
        // 生成API密钥
        $apiKey = generateApiKey();
        
        // 创建商户记录
        $db->query(
            "INSERT INTO merchants (
                user_id, company_name, business_license, contact_person, 
                contact_phone, contact_email, api_key, ip_whitelist, 
                service_rate, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())",
            [
                $userId,
                isset($input['company_name']) ? $input['company_name'] : $input['real_name'],
                isset($input['business_license']) ? $input['business_license'] : '',
                $input['real_name'],
                isset($input['phone']) ? $input['phone'] : '',
                $input['email'],
                $apiKey,
                isset($input['ip_whitelist']) ? $input['ip_whitelist'] : '',
                floatval(isset($input['service_rate']) ? $input['service_rate'] : 0.02) // 默认2%费率
            ]
        );
        
        $merchantId = $db->lastInsertId();
        
        // 为商户创建默认产品
        $db->query(
            "INSERT INTO products (merchant_id, name, min_amount, max_amount, status, created_at) 
             VALUES (?, ?, 1.00, 50000.00, 'active', NOW())",
            [$merchantId, $input['real_name'] . '默认产品']
        );
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '商户创建成功',
            'data' => array(
                'merchant_id' => $merchantId,
                'user_id' => $userId,
                'api_key' => $apiKey
            )
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 更新商户
function handleUpdateMerchant() {
    global $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        throw new Exception('缺少商户ID');
    }
    
    $merchantId = intval($input['id']);
    
    // 检查商户是否存在
    $merchant = $db->fetch("SELECT * FROM merchants WHERE id = ?", [$merchantId]);
    if (!$merchant) {
        throw new Exception('商户不存在');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 更新用户信息
        if (isset($input['real_name']) || isset($input['email']) || isset($input['phone'])) {
            $userUpdateFields = [];
            $userParams = [];
            
            if (isset($input['real_name'])) {
                $userUpdateFields[] = "real_name = ?";
                $userParams[] = $input['real_name'];
            }
            
            if (isset($input['email'])) {
                // 检查邮箱是否被其他用户使用
                $existingUser = $db->fetch("SELECT id FROM users WHERE email = ? AND id != ?", 
                                          [$input['email'], $merchant['user_id']]);
                if ($existingUser) {
                    throw new Exception('邮箱已被其他用户使用');
                }
                $userUpdateFields[] = "email = ?";
                $userParams[] = $input['email'];
            }
            
            if (isset($input['phone'])) {
                $userUpdateFields[] = "phone = ?";
                $userParams[] = $input['phone'];
            }
            
            if (!empty($userUpdateFields)) {
                $userParams[] = $merchant['user_id'];
                $db->query(
                    "UPDATE users SET " . implode(', ', $userUpdateFields) . ", updated_at = NOW() WHERE id = ?",
                    $userParams
                );
            }
        }
        
        // 更新商户信息
        $merchantUpdateFields = [];
        $merchantParams = [];
        
        $updateableFields = ['company_name', 'business_license', 'contact_person', 
                           'contact_phone', 'contact_email', 'ip_whitelist', 
                           'service_rate', 'status'];
        
        foreach ($updateableFields as $field) {
            if (isset($input[$field])) {
                $merchantUpdateFields[] = "$field = ?";
                $merchantParams[] = $input[$field];
            }
        }
        
        if (!empty($merchantUpdateFields)) {
            $merchantParams[] = $merchantId;
            $db->query(
                "UPDATE merchants SET " . implode(', ', $merchantUpdateFields) . ", updated_at = NOW() WHERE id = ?",
                $merchantParams
            );
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '商户更新成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 删除商户
function handleDeleteMerchant() {
    global $db;
    
    $merchantId = intval(isset($_GET['id']) ? $_GET['id'] : 0);
    
    if ($merchantId <= 0) {
        throw new Exception('无效的商户ID');
    }
    
    // 检查商户是否存在
    $merchant = $db->fetch("SELECT * FROM merchants WHERE id = ?", [$merchantId]);
    if (!$merchant) {
        throw new Exception('商户不存在');
    }
    
    // 检查是否有未完成的交易
    $pendingTransactions = $db->fetch(
        "SELECT COUNT(*) as count FROM transactions WHERE merchant_id = ? AND status = 'pending'",
        [$merchantId]
    );
    
    if ($pendingTransactions['count'] > 0) {
        throw new Exception('该商户还有未完成的交易，无法删除');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 删除相关数据
        $db->query("DELETE FROM products WHERE merchant_id = ?", [$merchantId]);
        $db->query("DELETE FROM merchant_notification_tasks WHERE merchant_id = ?", [$merchantId]);
        $db->query("DELETE FROM merchant_risk_configs WHERE merchant_id = ?", [$merchantId]);
        $db->query("DELETE FROM merchant_totp WHERE merchant_id = ?", [$merchantId]);
        $db->query("DELETE FROM merchant_daily_reports WHERE merchant_id = ?", [$merchantId]);
        $db->query("DELETE FROM api_logs WHERE merchant_id = ?", [$merchantId]);
        
        // 删除商户记录
        $db->query("DELETE FROM merchants WHERE id = ?", [$merchantId]);
        
        // 删除用户账户
        $db->query("DELETE FROM users WHERE id = ?", [$merchant['user_id']]);
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '商户删除成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 生成API密钥
function generateApiKey() {
    return bin2hex(openssl_random_pseudo_bytes(32));
}

// API密钥管理
function handleMerchantApiKeys() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_merchants')) {
        throw new Exception('无权限访问');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            handleRegenerateApiKey();
            break;
        case 'PUT':
            handleUpdateApiKeySettings();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 重新生成API密钥
function handleRegenerateApiKey() {
    global $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['merchant_id'])) {
        throw new Exception('缺少商户ID');
    }
    
    $merchantId = intval($input['merchant_id']);
    
    // 检查商户是否存在
    $merchant = $db->fetch("SELECT * FROM merchants WHERE id = ?", [$merchantId]);
    if (!$merchant) {
        throw new Exception('商户不存在');
    }
    
    // 生成新的API密钥
    $newApiKey = generateApiKey();
    
    // 更新数据库
    $db->query(
        "UPDATE merchants SET api_key = ?, updated_at = NOW() WHERE id = ?",
        [$newApiKey, $merchantId]
    );
    
    // 记录操作日志
    $db->query(
        "INSERT INTO api_logs (merchant_id, api_name, request_data, response_data, status, client_ip, create_time) 
         VALUES (?, 'regenerate_api_key', ?, ?, 'success', ?, NOW())",
        [
            $merchantId,
            json_encode(['action' => 'regenerate_api_key', 'operator' => $currentUser['username']]),
            json_encode(['new_api_key_length' => strlen($newApiKey)]),
            isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0'
        ]
    );
    
    echo json_encode(array(
        'code' => 200,
        'message' => 'API密钥重新生成成功',
        'data' => array(
            'api_key' => $newApiKey
        )
    ));
}

// 更新API密钥设置
function handleUpdateApiKeySettings() {
    global $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['merchant_id'])) {
        throw new Exception('缺少商户ID');
    }
    
    $merchantId = intval($input['merchant_id']);
    
    // 检查商户是否存在
    $merchant = $db->fetch("SELECT * FROM merchants WHERE id = ?", [$merchantId]);
    if (!$merchant) {
        throw new Exception('商户不存在');
    }
    
    $updateFields = [];
    $params = [];
    
    // 更新IP白名单
    if (isset($input['ip_whitelist'])) {
        $updateFields[] = "ip_whitelist = ?";
        $params[] = $input['ip_whitelist'];
    }
    
    // 更新服务费率
    if (isset($input['service_rate'])) {
        $rate = floatval($input['service_rate']);
        if ($rate < 0 || $rate > 1) {
            throw new Exception('服务费率必须在0-100%之间');
        }
        $updateFields[] = "service_rate = ?";
        $params[] = $rate;
    }
    
    if (!empty($updateFields)) {
        $params[] = $merchantId;
        $db->query(
            "UPDATE merchants SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?",
            $params
        );
    }
    
    echo json_encode(array(
        'code' => 200,
        'message' => 'API设置更新成功'
    ));
}

// 产品管理 - 完整的CRUD功能
function handleProducts() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_merchants')) {
        throw new Exception('无权限访问');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetProducts();
            break;
        case 'POST':
            handleCreateProduct();
            break;
        case 'PUT':
            handleUpdateProduct();
            break;
        case 'DELETE':
            handleDeleteProduct();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取产品列表
function handleGetProducts() {
    global $db, $currentUser;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $export = isset($_GET['export']) ? $_GET['export'] : '';
    
    $whereConditions = array();
    $params = array();
    
    // 数据隔离：根据用户类型限制访问
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "p.merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if ($merchantId > 0) {
            $whereConditions[] = "p.merchant_id = ?";
            $params[] = $merchantId;
        }
    } else {
        throw new Exception('无权限访问产品管理');
    }
    
    if (!empty($status) && in_array($status, array('active', 'inactive', 'suspended'))) {
        $whereConditions[] = "p.status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 如果是导出请求，导出所有数据
    if ($export === '1') {
        $products = $db->fetchAll(
            "SELECT p.*, 
                    m.company_name as merchant_name,
                    u.real_name as merchant_real_name,
                    (SELECT COUNT(*) FROM transactions t WHERE t.product_id = p.id) as transaction_count,
                    (SELECT SUM(amount) FROM transactions t WHERE t.product_id = p.id AND t.status = 'paid') as total_amount,
                    (SELECT COUNT(*) FROM transactions t WHERE t.product_id = p.id AND DATE(t.created_at) = CURDATE()) as today_transactions
             FROM products p
             LEFT JOIN merchants m ON p.merchant_id = m.id
             LEFT JOIN users u ON m.user_id = u.id
             $whereClause
             ORDER BY p.created_at DESC",
            $params
        );
        
        // 生成CSV文件
        $filename = 'products_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');
        
        $output = fopen('php://output', 'w');
        
        // 输出BOM以支持中文
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // CSV标题行
        $headers = array(
            '产品ID', '产品名称', '产品代码', '产品类型', '描述',
            '所属商户', '商户姓名', '最小金额', '最大金额', '服务费率',
            '状态', '总交易数', '总交易额', '今日交易', '创建时间', '更新时间'
        );
        fputcsv($output, $headers);
        
        // 输出数据行
        foreach ($products as $product) {
            $row = array(
                $product['id'],
                $product['name'],
                $product['product_code'] ? $product['product_code'] : '',
                $product['type'] ? $product['type'] : 'payment',
                $product['description'] ? $product['description'] : '',
                $product['merchant_name'] ? $product['merchant_name'] : '',
                $product['merchant_real_name'] ? $product['merchant_real_name'] : '',
                number_format($product['min_amount'], 2),
                number_format($product['max_amount'], 2),
                number_format($product['service_rate'] * 100, 2) . '%',
                $product['status'] === 'active' ? '启用' : ($product['status'] === 'inactive' ? '禁用' : '暂停'),
                $product['transaction_count'] ? $product['transaction_count'] : 0,
                number_format($product['total_amount'] ? $product['total_amount'] : 0, 2),
                $product['today_transactions'] ? $product['today_transactions'] : 0,
                $product['created_at'],
                $product['updated_at']
            );
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit();
    }
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM products p $whereClause",
        $params
    );
    $total = $totalResult['count'];
    
    // 获取产品列表
    $products = $db->fetchAll(
        "SELECT p.*, 
                m.company_name as merchant_name,
                u.real_name as merchant_real_name,
                (SELECT COUNT(*) FROM transactions t WHERE t.product_id = p.id) as transaction_count,
                (SELECT SUM(amount) FROM transactions t WHERE t.product_id = p.id AND t.status = 'paid') as total_amount,
                (SELECT COUNT(*) FROM transactions t WHERE t.product_id = p.id AND DATE(t.created_at) = CURDATE()) as today_transactions
         FROM products p
         LEFT JOIN merchants m ON p.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         $whereClause
         ORDER BY p.created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取统计信息
    $statsParams = $params;
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_products,
            COUNT(CASE WHEN p.status = 'active' THEN 1 END) as active_products,
            COUNT(CASE WHEN p.status = 'inactive' THEN 1 END) as inactive_products,
            COUNT(CASE WHEN p.status = 'suspended' THEN 1 END) as suspended_products,
            SUM(p.max_amount) as total_max_amount,
            AVG(p.max_amount) as avg_max_amount
         FROM products p $whereClause",
        $statsParams
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'products' => $products,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            ),
            'user_info' => array(
                'user_type' => $currentUser['user_type'],
                'merchant_id' => $currentUser['user_type'] === 'merchant' ? $currentUser['profile_id'] : null
            )
        )
    ));
}

// 创建产品
function handleCreateProduct() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    $required_fields = ['name', 'min_amount', 'max_amount'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    // 确定商户ID
    $merchantId = null;
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if (empty($input['merchant_id'])) {
            throw new Exception('管理员创建产品时必须指定商户ID');
        }
        $merchantId = intval($input['merchant_id']);
    } else {
        throw new Exception('无权限创建产品');
    }
    
    // 验证商户是否存在
    $merchant = $db->fetch("SELECT id FROM merchants WHERE id = ?", [$merchantId]);
    if (!$merchant) {
        throw new Exception('指定的商户不存在');
    }
    
    // 验证金额范围
    $minAmount = floatval($input['min_amount']);
    $maxAmount = floatval($input['max_amount']);
    
    if ($minAmount <= 0 || $maxAmount <= 0) {
        throw new Exception('金额必须大于0');
    }
    
    if ($minAmount >= $maxAmount) {
        throw new Exception('最小金额必须小于最大金额');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 创建产品
        $db->query(
            "INSERT INTO products (
                merchant_id, name, product_code, type, description, 
                min_amount, max_amount, service_rate, status, 
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
            [
                $merchantId,
                $input['name'],
                isset($input['product_code']) ? $input['product_code'] : 'PROD_' . time(),
                isset($input['type']) ? $input['type'] : 'payment',
                isset($input['description']) ? $input['description'] : '',
                $minAmount,
                $maxAmount,
                isset($input['service_rate']) ? floatval($input['service_rate']) : 0.02,
                isset($input['status']) ? $input['status'] : 'active'
            ]
        );
        
        $productId = $db->lastInsertId();
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '产品创建成功',
            'data' => array(
                'product_id' => $productId
            )
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 更新产品
function handleUpdateProduct() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        throw new Exception('缺少产品ID');
    }
    
    $productId = intval($input['id']);
    
    // 检查产品是否存在并验证权限
    $product = $db->fetch("SELECT * FROM products WHERE id = ?", [$productId]);
    if (!$product) {
        throw new Exception('产品不存在');
    }
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        if ($product['merchant_id'] != $currentUser['profile_id']) {
            throw new Exception('无权限修改此产品');
        }
    } elseif ($currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限修改产品');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 更新产品信息
        $updateFields = [];
        $params = [];
        
        $updateableFields = ['name', 'product_code', 'type', 'description', 'min_amount', 'max_amount', 'service_rate', 'status'];
        
        foreach ($updateableFields as $field) {
            if (isset($input[$field])) {
                if (in_array($field, ['min_amount', 'max_amount', 'service_rate'])) {
                    $value = floatval($input[$field]);
                    if ($value < 0) {
                        throw new Exception("$field 不能为负数");
                    }
                    $updateFields[] = "$field = ?";
                    $params[] = $value;
                } else {
                    $updateFields[] = "$field = ?";
                    $params[] = $input[$field];
                }
            }
        }
        
        // 添加更新时间
        if (!empty($updateFields)) {
            $updateFields[] = "updated_at = NOW()";
        }
        
        // 验证金额范围
        if (isset($input['min_amount']) && isset($input['max_amount'])) {
            if (floatval($input['min_amount']) >= floatval($input['max_amount'])) {
                throw new Exception('最小金额必须小于最大金额');
            }
        }
        
        if (!empty($updateFields)) {
            $params[] = $productId;
            $db->query(
                "UPDATE products SET " . implode(', ', $updateFields) . " WHERE id = ?",
                $params
            );
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '产品更新成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 删除产品
function handleDeleteProduct() {
    global $db, $currentUser;
    
    $productId = intval(isset($_GET['id']) ? $_GET['id'] : 0);
    
    if ($productId <= 0) {
        throw new Exception('无效的产品ID');
    }
    
    // 检查产品是否存在并验证权限
    $product = $db->fetch("SELECT * FROM products WHERE id = ?", [$productId]);
    if (!$product) {
        throw new Exception('产品不存在');
    }
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        if ($product['merchant_id'] != $currentUser['profile_id']) {
            throw new Exception('无权限删除此产品');
        }
    } elseif ($currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限删除产品');
    }
    
    // 检查是否有未完成的交易
    $pendingTransactions = $db->fetch(
        "SELECT COUNT(*) as count FROM transactions WHERE product_id = ? AND status IN ('pending', 'processing')",
        [$productId]
    );
    
    if ($pendingTransactions['count'] > 0) {
        throw new Exception('该产品还有未完成的交易，无法删除');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 删除产品
        $db->query("DELETE FROM products WHERE id = ?", [$productId]);
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '产品删除成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 费率管理
function handleRates() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_merchants')) {
        throw new Exception('无权限访问');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetRates();
            break;
        case 'PUT':
            handleUpdateRates();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取费率信息
function handleGetRates() {
    global $db, $currentUser;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if ($merchantId <= 0) {
            throw new Exception('管理员查询费率时必须指定商户ID');
        }
    } else {
        throw new Exception('无权限访问费率信息');
    }
    
    // 获取商户基础费率
    $merchant = $db->fetch(
        "SELECT m.*, u.real_name, u.username 
         FROM merchants m 
         JOIN users u ON m.user_id = u.id 
         WHERE m.id = ?", 
        [$merchantId]
    );
    
    if (!$merchant) {
        throw new Exception('商户不存在');
    }
    
    // 获取产品费率
    $products = $db->fetchAll(
        "SELECT id, name, service_rate, status, min_amount, max_amount, created_at
         FROM products 
         WHERE merchant_id = ? 
         ORDER BY created_at DESC",
        [$merchantId]
    );
    
    // 计算费率统计
    $rateStats = $db->fetch(
        "SELECT 
            AVG(service_rate) as avg_rate,
            MIN(service_rate) as min_rate,
            MAX(service_rate) as max_rate,
            COUNT(*) as product_count
         FROM products 
         WHERE merchant_id = ? AND status = 'active'",
        [$merchantId]
    );
    
    // 获取费率历史（最近的费率变更记录）
    $rateHistory = $db->fetchAll(
        "SELECT 
            'merchant' as type,
            m.service_rate as rate,
            m.updated_at as change_time,
            'merchant_rate_change' as change_type
         FROM merchants m 
         WHERE m.id = ?
         UNION ALL
         SELECT 
            'product' as type,
            p.service_rate as rate,
            p.updated_at as change_time,
            CONCAT('product_', p.id, '_rate_change') as change_type
         FROM products p 
         WHERE p.merchant_id = ?
         ORDER BY change_time DESC 
         LIMIT 20",
        [$merchantId, $merchantId]
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'merchant' => $merchant,
            'products' => $products,
            'stats' => $rateStats,
            'history' => $rateHistory
        )
    ));
}

// 更新费率
function handleUpdateRates() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['merchant_id'])) {
        throw new Exception('缺少商户ID');
    }
    
    $merchantId = intval($input['merchant_id']);
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        if ($merchantId != $currentUser['profile_id']) {
            throw new Exception('无权限修改此商户的费率');
        }
    } elseif ($currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限修改费率');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 更新商户基础费率
        if (isset($input['merchant_rate'])) {
            $merchantRate = floatval($input['merchant_rate']);
            if ($merchantRate < 0 || $merchantRate > 1) {
                throw new Exception('商户费率必须在0-100%之间');
            }
            
            $db->query(
                "UPDATE merchants SET service_rate = ?, updated_at = NOW() WHERE id = ?",
                [$merchantRate, $merchantId]
            );
        }
        
        // 更新产品费率
        if (isset($input['product_rates']) && is_array($input['product_rates'])) {
            foreach ($input['product_rates'] as $productRate) {
                if (!isset($productRate['product_id']) || !isset($productRate['rate'])) {
                    continue;
                }
                
                $productId = intval($productRate['product_id']);
                $rate = floatval($productRate['rate']);
                
                if ($rate < 0 || $rate > 1) {
                    throw new Exception("产品ID {$productId} 的费率必须在0-100%之间");
                }
                
                // 验证产品属于该商户
                $product = $db->fetch(
                    "SELECT id FROM products WHERE id = ? AND merchant_id = ?",
                    [$productId, $merchantId]
                );
                
                if ($product) {
                    $db->query(
                        "UPDATE products SET service_rate = ?, updated_at = NOW() WHERE id = ?",
                        [$rate, $productId]
                    );
                }
            }
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '费率更新成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 支付请求管理
function handlePaymentRequests() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'view_transactions')) {
        throw new Exception('无权限访问');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetPaymentRequests();
            break;
        case 'POST':
            handleCreatePaymentRequest();
            break;
        case 'PUT':
            handleUpdatePaymentRequest();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取支付请求列表
function handleGetPaymentRequests() {
    global $db, $currentUser;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "pr.merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if ($merchantId > 0) {
            $whereConditions[] = "pr.merchant_id = ?";
            $params[] = $merchantId;
        }
    } else {
        throw new Exception('无权限访问支付请求');
    }
    
    if (!empty($status)) {
        $whereConditions[] = "pr.status = ?";
        $params[] = $status;
    }
    
    // 日期范围筛选
    if (!empty($dateRange)) {
        switch ($dateRange) {
            case 'today':
                $whereConditions[] = "DATE(pr.created_at) = CURDATE()";
                break;
            case 'yesterday':
                $whereConditions[] = "DATE(pr.created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
                break;
            case 'week':
                $whereConditions[] = "pr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $whereConditions[] = "pr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
        }
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(pr.request_id LIKE ? OR pr.merchant_order_no LIKE ? OR pr.subject LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM payment_requests pr $whereClause",
        $params
    );
    $total = $totalResult['count'];
    
    // 获取支付请求列表
    $requests = $db->fetchAll(
        "SELECT pr.*, 
                m.company_name as merchant_name,
                u.real_name as merchant_real_name,
                p.name as product_name,
                a.account_name as alipay_account_name,
                a.account_number as alipay_account_number,
                pr.request_id as out_trade_no,
                pr.merchant_order_no as order_id,
                'alipay' as payment_method,
                pr.fee as service_fee
         FROM payment_requests pr
         LEFT JOIN merchants m ON pr.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         LEFT JOIN products p ON pr.product_id = p.id
         LEFT JOIN alipay_accounts a ON pr.alipay_account_id = a.id
         $whereClause
         ORDER BY pr.created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取统计信息
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_requests,
            COUNT(CASE WHEN pr.status = 'pending' THEN 1 END) as pending_requests,
            COUNT(CASE WHEN pr.status = 'processing' THEN 1 END) as processing_requests,
            COUNT(CASE WHEN pr.status = 'success' OR pr.status = 'paid' THEN 1 END) as success_requests,
            COUNT(CASE WHEN pr.status = 'failed' THEN 1 END) as failed_requests,
            COUNT(CASE WHEN pr.status = 'cancelled' THEN 1 END) as cancelled_requests,
            SUM(CASE WHEN pr.status IN ('success', 'paid') THEN pr.amount ELSE 0 END) as total_amount,
            AVG(CASE WHEN pr.status IN ('success', 'paid') THEN pr.amount ELSE NULL END) as avg_amount
         FROM payment_requests pr $whereClause",
        $params
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'payment_requests' => $requests,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        )
    ));
}

// 创建支付请求
function handleCreatePaymentRequest() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    $required_fields = ['product_id', 'amount', 'out_trade_no'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    $productId = intval($input['product_id']);
    $amount = floatval($input['amount']);
    $outTradeNo = $input['out_trade_no'];
    
    // 确定商户ID
    $merchantId = null;
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if (empty($input['merchant_id'])) {
            throw new Exception('管理员创建支付请求时必须指定商户ID');
        }
        $merchantId = intval($input['merchant_id']);
    } else {
        throw new Exception('无权限创建支付请求');
    }
    
    // 验证产品
    $product = $db->fetch(
        "SELECT * FROM products WHERE id = ? AND merchant_id = ? AND status = 'active'",
        [$productId, $merchantId]
    );
    
    if (!$product) {
        throw new Exception('产品不存在或已禁用');
    }
    
    // 验证金额范围
    if ($amount < $product['min_amount'] || $amount > $product['max_amount']) {
        throw new Exception("支付金额必须在 {$product['min_amount']} - {$product['max_amount']} 之间");
    }
    
    // 检查订单号重复
    $existingOrder = $db->fetch(
        "SELECT id FROM transactions WHERE out_trade_no = ? AND merchant_id = ?",
        [$outTradeNo, $merchantId]
    );
    
    if ($existingOrder) {
        throw new Exception('订单号已存在');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 分配支付宝账户
        $alipayAccount = $db->fetch(
            "SELECT * FROM alipay_accounts 
             WHERE status = 'approved' AND provider_id IS NOT NULL 
             ORDER BY RAND() 
             LIMIT 1"
        );
        
        if (!$alipayAccount) {
            throw new Exception('暂无可用的支付账户');
        }
        
        // 生成订单ID
        $orderId = 'PAY' . date('YmdHis') . rand(1000, 9999);
        
        // 创建交易记录
        $db->query(
            "INSERT INTO transactions (
                order_id, out_trade_no, merchant_id, product_id, provider_id, 
                alipay_account_id, amount, service_fee, actual_amount, 
                status, remark, client_ip, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, ?, NOW())",
            [
                $orderId,
                $outTradeNo,
                $merchantId,
                $productId,
                $alipayAccount['provider_id'],
                $alipayAccount['id'],
                $amount,
                $amount * $product['service_rate'],
                $amount * (1 - $product['service_rate']),
                isset($input['remark']) ? $input['remark'] : '',
                isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0'
            ]
        );
        
        $transactionId = $db->lastInsertId();
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '支付请求创建成功',
            'data' => array(
                'transaction_id' => $transactionId,
                'order_id' => $orderId,
                'alipay_account' => array(
                    'name' => $alipayAccount['account_name'],
                    'number' => $alipayAccount['account_number']
                )
            )
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 更新支付请求状态
function handleUpdatePaymentRequest() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['transaction_id'])) {
        throw new Exception('缺少交易ID');
    }
    
    $transactionId = intval($input['transaction_id']);
    $newStatus = isset($input['status']) ? $input['status'] : '';
    
    if (!in_array($newStatus, ['pending', 'paid', 'failed', 'cancelled'])) {
        throw new Exception('无效的状态');
    }
    
    // 检查交易是否存在并验证权限
    $transaction = $db->fetch("SELECT * FROM transactions WHERE id = ?", [$transactionId]);
    if (!$transaction) {
        throw new Exception('交易不存在');
    }
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        if ($transaction['merchant_id'] != $currentUser['profile_id']) {
            throw new Exception('无权限修改此交易');
        }
    } elseif ($currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限修改交易状态');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 更新交易状态
        $db->query(
            "UPDATE transactions SET status = ?, updated_at = NOW() WHERE id = ?",
            [$newStatus, $transactionId]
        );
        
        // 如果是支付成功，更新支付时间
        if ($newStatus === 'paid') {
            $db->query(
                "UPDATE transactions SET paid_at = NOW() WHERE id = ?",
                [$transactionId]
            );
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '交易状态更新成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 订单管理（扩展的交易管理）
function handleOrders() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'view_transactions')) {
        throw new Exception('无权限访问');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetOrders();
            break;
        case 'PUT':
            handleUpdateOrder();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取订单列表（增强版）
function handleGetOrders() {
    global $db, $currentUser;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : '';
    $amountRange = isset($_GET['amount_range']) ? $_GET['amount_range'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "t.merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if ($merchantId > 0) {
            $whereConditions[] = "t.merchant_id = ?";
            $params[] = $merchantId;
        }
    } else {
        throw new Exception('无权限访问订单');
    }
    
    if (!empty($status)) {
        $whereConditions[] = "t.status = ?";
        $params[] = $status;
    }
    
    // 金额范围筛选
    if (!empty($amountRange)) {
        switch ($amountRange) {
            case 'small':
                $whereConditions[] = "t.amount <= 100";
                break;
            case 'medium':
                $whereConditions[] = "t.amount > 100 AND t.amount <= 1000";
                break;
            case 'large':
                $whereConditions[] = "t.amount > 1000";
                break;
        }
    }
    
    // 日期范围筛选
    if (!empty($dateRange)) {
        switch ($dateRange) {
            case 'today':
                $whereConditions[] = "DATE(t.created_at) = CURDATE()";
                break;
            case 'yesterday':
                $whereConditions[] = "DATE(t.created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
                break;
            case 'week':
                $whereConditions[] = "t.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $whereConditions[] = "t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
        }
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(t.order_id LIKE ? OR t.out_trade_no LIKE ? OR t.remark LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM transactions t $whereClause",
        $params
    );
    $total = $totalResult['count'];
    
    // 获取订单列表
    $orders = $db->fetchAll(
        "SELECT t.*, 
                m.company_name as merchant_name,
                u.real_name as merchant_real_name,
                p.name as product_name,
                p.service_rate as product_rate,
                a.account_name as alipay_account_name,
                a.account_number as alipay_account_number,
                pr.company_name as provider_name,
                TIMESTAMPDIFF(MINUTE, t.created_at, COALESCE(t.paid_at, NOW())) as duration_minutes
         FROM transactions t
         LEFT JOIN merchants m ON t.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         LEFT JOIN products p ON t.product_id = p.id
         LEFT JOIN alipay_accounts a ON t.alipay_account_id = a.id
         LEFT JOIN payment_providers pr ON t.provider_id = pr.id
         $whereClause
         ORDER BY t.created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取详细统计信息
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN t.status = 'paid' THEN 1 END) as paid_orders,
            COUNT(CASE WHEN t.status = 'failed' THEN 1 END) as failed_orders,
            COUNT(CASE WHEN t.status = 'cancelled' THEN 1 END) as cancelled_orders,
            SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as total_amount,
            SUM(CASE WHEN t.status = 'paid' THEN t.service_fee ELSE 0 END) as total_service_fee,
            SUM(CASE WHEN t.status = 'paid' THEN t.actual_amount ELSE 0 END) as total_actual_amount,
            AVG(CASE WHEN t.status = 'paid' THEN t.amount ELSE NULL END) as avg_amount,
            ROUND(COUNT(CASE WHEN t.status = 'paid' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
         FROM transactions t $whereClause",
        $params
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'orders' => $orders,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        )
    ));
}

// 更新订单
function handleUpdateOrder() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['order_id'])) {
        throw new Exception('缺少订单ID');
    }
    
    $orderId = intval($input['order_id']);
    
    // 检查订单是否存在并验证权限
    $order = $db->fetch("SELECT * FROM transactions WHERE id = ?", [$orderId]);
    if (!$order) {
        throw new Exception('订单不存在');
    }
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        if ($order['merchant_id'] != $currentUser['profile_id']) {
            throw new Exception('无权限修改此订单');
        }
    } elseif ($currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限修改订单');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 更新订单信息
        $updateFields = [];
        $params = [];
        
        $updateableFields = ['status', 'remark'];
        
        foreach ($updateableFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "$field = ?";
                $params[] = $input[$field];
            }
        }
        
        if (!empty($updateFields)) {
            $params[] = $orderId;
            $db->query(
                "UPDATE transactions SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?",
                $params
            );
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '订单更新成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 财务管理
function handleFinancial() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'view_transactions')) {
        throw new Exception('无权限访问');
    }
    
    // 检查是否是导出请求
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    if ($action === 'export_financial') {
        handleExportFinancialData();
        return;
    }
    
    if ($action === 'export_transaction_detail') {
        handleExportTransactionDetail();
        return;
    }
    
    if ($action === 'generate_financial_report') {
        handleGenerateFinancialReport();
        return;
    }
    
    if ($action === 'export_financial_report') {
        handleExportFinancialReport();
        return;
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetFinancialData();
            break;
        case 'POST':
            handleCreateFinancialRecord();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 导出财务数据
function handleExportFinancialData() {
    global $db, $currentUser;
    
    $type = isset($_GET['type']) ? $_GET['type'] : 'overview';
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : 'month';
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    }
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE t.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    // 获取要导出的数据
    if ($type === 'transactions') {
        $data = $db->fetchAll(
            "SELECT t.transaction_id, t.merchant_order_no, m.company_name as merchant_name,
                    p.name as product_name, t.amount, t.actual_amount, t.service_fee,
                    t.service_rate, t.status, t.created_at, t.paid_at, t.remark
             FROM transactions t
             LEFT JOIN merchants m ON t.merchant_id = m.id
             LEFT JOIN products p ON t.product_id = p.id
             $whereClause
             ORDER BY t.created_at DESC",
            $params
        );
        
        $filename = 'financial_transactions_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = array(
            '交易ID', '商户订单号', '商户名称', '产品名称', '订单金额', '实收金额',
            '服务费', '费率', '状态', '创建时间', '支付时间', '备注'
        );
        
    } else {
        // 导出概览数据
        $data = $db->fetchAll(
            "SELECT DATE(t.created_at) as date,
                    COUNT(*) as transaction_count,
                    SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as total_amount,
                    SUM(CASE WHEN t.status = 'paid' THEN t.service_fee ELSE 0 END) as service_fee,
                    COUNT(CASE WHEN t.status = 'paid' THEN 1 END) as paid_count,
                    ROUND(COUNT(CASE WHEN t.status = 'paid' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
             FROM transactions t $whereClause
             GROUP BY DATE(t.created_at)
             ORDER BY DATE(t.created_at) DESC",
            $params
        );
        
        $filename = 'financial_overview_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = array(
            '日期', '交易笔数', '交易金额', '服务费收入', '成功笔数', '成功率(%)'
        );
    }
    
    // 生成CSV文件
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=' . $filename);
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $output = fopen('php://output', 'w');
    
    // 输出BOM以支持中文
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // 输出标题行
    fputcsv($output, $headers);
    
    // 输出数据行
    foreach ($data as $row) {
        if ($type === 'transactions') {
            $csvRow = array(
                $row['transaction_id'],
                $row['merchant_order_no'],
                $row['merchant_name'],
                $row['product_name'],
                number_format($row['amount'], 2),
                number_format($row['actual_amount'], 2),
                number_format($row['service_fee'], 2),
                number_format($row['service_rate'] * 100, 2) . '%',
                $row['status'] === 'paid' ? '已支付' : ($row['status'] === 'pending' ? '待支付' : '失败'),
                $row['created_at'],
                $row['paid_at'],
                $row['remark']
            );
        } else {
            $csvRow = array(
                $row['date'],
                $row['transaction_count'],
                number_format($row['total_amount'], 2),
                number_format($row['service_fee'], 2),
                $row['paid_count'],
                $row['success_rate']
            );
        }
        fputcsv($output, $csvRow);
    }
    
    fclose($output);
    exit();
}

// 导出交易详情
function handleExportTransactionDetail() {
    global $db, $currentUser;
    
    $transactionId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    if (!$transactionId) {
        throw new Exception('交易ID不能为空');
    }
    
    // 获取交易详情
    $whereConditions = array("t.id = ?");
    $params = array($transactionId);
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "t.merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    }
    
    $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
    
    $transaction = $db->fetch(
        "SELECT t.*, m.company_name as merchant_name, u.real_name as merchant_real_name,
                p.name as product_name, a.account_name as alipay_account_name
         FROM transactions t
         LEFT JOIN merchants m ON t.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         LEFT JOIN products p ON t.product_id = p.id
         LEFT JOIN alipay_accounts a ON t.alipay_account_id = a.id
         $whereClause",
        $params
    );
    
    if (!$transaction) {
        throw new Exception('交易不存在');
    }
    
    $filename = 'transaction_detail_' . $transaction['transaction_id'] . '_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=' . $filename);
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $output = fopen('php://output', 'w');
    
    // 输出BOM以支持中文
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // 输出交易详情
    $details = array(
        array('字段', '值'),
        array('交易ID', $transaction['transaction_id']),
        array('商户订单号', $transaction['merchant_order_no']),
        array('商户名称', $transaction['merchant_name']),
        array('商户联系人', $transaction['merchant_real_name']),
        array('产品名称', $transaction['product_name']),
        array('支付账户', $transaction['alipay_account_name']),
        array('订单金额', number_format($transaction['amount'], 2)),
        array('实收金额', number_format($transaction['actual_amount'], 2)),
        array('服务费', number_format($transaction['service_fee'], 2)),
        array('费率', number_format($transaction['service_rate'] * 100, 2) . '%'),
        array('净收入', number_format($transaction['actual_amount'] - $transaction['service_fee'], 2)),
        array('状态', $transaction['status'] === 'paid' ? '已支付' : ($transaction['status'] === 'pending' ? '待支付' : '失败')),
        array('创建时间', $transaction['created_at']),
        array('支付时间', $transaction['paid_at']),
        array('更新时间', $transaction['updated_at']),
        array('备注', $transaction['remark']),
        array('客户端IP', $transaction['client_ip'])
    );
    
    foreach ($details as $detail) {
        fputcsv($output, $detail);
    }
    
    fclose($output);
    exit();
}

// 生成财务报表
function handleGenerateFinancialReport() {
    global $db, $currentUser;
    
    $type = isset($_GET['type']) ? $_GET['type'] : 'daily';
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : 'month';
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    }
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE t.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    // 获取总体统计
    $summary = $db->fetch(
        "SELECT 
            COUNT(*) as total_transactions,
            SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as total_revenue,
            SUM(CASE WHEN t.status = 'paid' THEN t.service_fee ELSE 0 END) as total_profit,
            ROUND(COUNT(CASE WHEN t.status = 'paid' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate
         FROM transactions t $whereClause",
        $params
    );
    
    // 获取每日数据
    $dailyData = $db->fetchAll(
        "SELECT 
            DATE(t.created_at) as date,
            COUNT(*) as transaction_count,
            SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as total_amount,
            SUM(CASE WHEN t.status = 'paid' THEN t.service_fee ELSE 0 END) as service_fee,
            ROUND(COUNT(CASE WHEN t.status = 'paid' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate,
            AVG(CASE WHEN t.status = 'paid' THEN t.amount ELSE NULL END) as avg_amount
         FROM transactions t $whereClause
         GROUP BY DATE(t.created_at)
         ORDER BY DATE(t.created_at) DESC
         LIMIT 30",
        $params
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'total_revenue' => $summary['total_revenue'],
            'total_profit' => $summary['total_profit'],
            'total_transactions' => $summary['total_transactions'],
            'success_rate' => $summary['success_rate'],
            'daily_data' => $dailyData
        )
    ));
}

// 导出财务报表
function handleExportFinancialReport() {
    global $db, $currentUser;
    
    $type = isset($_GET['type']) ? $_GET['type'] : 'daily';
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : 'month';
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    }
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE t.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    // 获取报表数据
    $dailyData = $db->fetchAll(
        "SELECT 
            DATE(t.created_at) as date,
            COUNT(*) as transaction_count,
            SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as total_amount,
            SUM(CASE WHEN t.status = 'paid' THEN t.service_fee ELSE 0 END) as service_fee,
            ROUND(COUNT(CASE WHEN t.status = 'paid' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate,
            AVG(CASE WHEN t.status = 'paid' THEN t.amount ELSE NULL END) as avg_amount,
            COUNT(CASE WHEN t.status = 'paid' THEN 1 END) as success_count,
            COUNT(CASE WHEN t.status = 'failed' THEN 1 END) as failed_count
         FROM transactions t $whereClause
         GROUP BY DATE(t.created_at)
         ORDER BY DATE(t.created_at) DESC",
        $params
    );
    
    $filename = 'financial_report_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=' . $filename);
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $output = fopen('php://output', 'w');
    
    // 输出BOM以支持中文
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // 输出标题行
    $headers = array(
        '日期', '交易笔数', '交易金额', '服务费收入', '成功笔数', '失败笔数', '成功率(%)', '平均金额'
    );
    fputcsv($output, $headers);
    
    // 输出数据行
    foreach ($dailyData as $row) {
        $csvRow = array(
            $row['date'],
            $row['transaction_count'],
            number_format($row['total_amount'], 2),
            number_format($row['service_fee'], 2),
            $row['success_count'],
            $row['failed_count'],
            $row['success_rate'],
            number_format($row['avg_amount'], 2)
        );
        fputcsv($output, $csvRow);
    }
    
    fclose($output);
    exit();
}

// 获取财务数据
function handleGetFinancialData() {
    global $db, $currentUser;
    
    $type = isset($_GET['type']) ? $_GET['type'] : 'overview';
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : 'month';
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        // 管理员可以查看所有或指定商户
    } else {
        throw new Exception('无权限访问财务数据');
    }
    
    switch ($type) {
        case 'overview':
            echo json_encode(getFinancialOverview($merchantId, $dateRange));
            break;
        case 'transactions':
            echo json_encode(getFinancialTransactions($merchantId, $dateRange));
            break;
        case 'settlement':
            echo json_encode(getSettlementData($merchantId, $dateRange));
            break;
        case 'statistics':
            echo json_encode(getFinancialStatistics($merchantId, $dateRange));
            break;
        default:
            throw new Exception('无效的财务数据类型');
    }
}

// 获取财务概览
function getFinancialOverview($merchantId, $dateRange) {
    global $db;
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE t.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    // 根据日期范围添加条件
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    // 基础统计
    $overview = $db->fetch(
        "SELECT 
            COUNT(*) as total_transactions,
            COUNT(CASE WHEN t.status = 'paid' THEN 1 END) as successful_transactions,
            COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_transactions,
            COUNT(CASE WHEN t.status = 'failed' THEN 1 END) as failed_transactions,
            SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as total_amount,
            SUM(CASE WHEN t.status = 'paid' THEN t.service_fee ELSE 0 END) as total_service_fee,
            SUM(CASE WHEN t.status = 'paid' THEN t.actual_amount ELSE 0 END) as total_actual_amount,
            AVG(CASE WHEN t.status = 'paid' THEN t.amount ELSE NULL END) as avg_transaction_amount,
            ROUND(COUNT(CASE WHEN t.status = 'paid' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate
         FROM transactions t $whereClause",
        $params
    );
    
    // 今日数据
    $todayParams = $params;
    $todayWhereClause = $whereClause . ($whereClause ? ' AND ' : 'WHERE ') . "DATE(t.created_at) = CURDATE()";
    
    $todayData = $db->fetch(
        "SELECT 
            COUNT(*) as today_transactions,
            SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as today_amount,
            SUM(CASE WHEN t.status = 'paid' THEN t.service_fee ELSE 0 END) as today_service_fee
         FROM transactions t $todayWhereClause",
        $todayParams
    );
    
    // 趋势数据（最近7天）
    $trendData = $db->fetchAll(
        "SELECT 
            DATE(t.created_at) as date,
            COUNT(*) as transaction_count,
            SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as daily_amount,
            SUM(CASE WHEN t.status = 'paid' THEN t.service_fee ELSE 0 END) as daily_service_fee
         FROM transactions t 
         " . ($whereClause ? $whereClause . ' AND ' : 'WHERE ') . "
         t.created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
         GROUP BY DATE(t.created_at)
         ORDER BY DATE(t.created_at) DESC",
        $params
    );
    
    return array(
        'code' => 200,
        'data' => array(
            'overview' => $overview,
            'today' => $todayData,
            'trend' => $trendData
        )
    );
}

// 获取财务交易记录
function getFinancialTransactions($merchantId, $dateRange) {
    global $db;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE t.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    // 只查询已支付的交易
    $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . "t.status = 'paid'";
    
    $transactions = $db->fetchAll(
        "SELECT t.*, 
                m.company_name as merchant_name,
                p.name as product_name,
                a.account_name as alipay_account_name
         FROM transactions t
         LEFT JOIN merchants m ON t.merchant_id = m.id
         LEFT JOIN products p ON t.product_id = p.id
         LEFT JOIN alipay_accounts a ON t.alipay_account_id = a.id
         $whereClause
         ORDER BY t.paid_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM transactions t $whereClause",
        $params
    );
    
    return array(
        'code' => 200,
        'data' => array(
            'transactions' => $transactions,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($totalResult['count'] / $limit),
                'total_records' => $totalResult['count'],
                'per_page' => $limit
            )
        )
    );
}

// 获取结算数据
function getSettlementData($merchantId, $dateRange) {
    global $db;
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE t.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    // 按商户分组的结算数据
    $settlementData = $db->fetchAll(
        "SELECT 
            t.merchant_id,
            m.company_name as merchant_name,
            u.real_name as merchant_real_name,
            COUNT(*) as transaction_count,
            SUM(t.amount) as total_amount,
            SUM(t.service_fee) as total_service_fee,
            SUM(t.actual_amount) as settlement_amount,
            MIN(t.paid_at) as first_transaction,
            MAX(t.paid_at) as last_transaction
         FROM transactions t
         LEFT JOIN merchants m ON t.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         $whereClause AND t.status = 'paid'
         GROUP BY t.merchant_id, m.company_name, u.real_name
         ORDER BY settlement_amount DESC",
        $params
    );
    
    return array(
        'code' => 200,
        'data' => array(
            'settlements' => $settlementData
        )
    );
}

// 获取财务统计
function getFinancialStatistics($merchantId, $dateRange) {
    global $db;
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE t.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    $dateCondition = getDateRangeCondition($dateRange);
    if ($dateCondition) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . $dateCondition;
    }
    
    // 按产品统计
    $productStats = $db->fetchAll(
        "SELECT 
            p.id as product_id,
            p.name as product_name,
            COUNT(*) as transaction_count,
            SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as total_amount,
            SUM(CASE WHEN t.status = 'paid' THEN t.service_fee ELSE 0 END) as total_service_fee,
            AVG(CASE WHEN t.status = 'paid' THEN t.amount ELSE NULL END) as avg_amount,
            ROUND(COUNT(CASE WHEN t.status = 'paid' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate
         FROM transactions t
         LEFT JOIN products p ON t.product_id = p.id
         $whereClause
         GROUP BY p.id, p.name
         ORDER BY total_amount DESC",
        $params
    );
    
    // 按时间统计（按小时）
    $hourlyStats = $db->fetchAll(
        "SELECT 
            HOUR(t.created_at) as hour,
            COUNT(*) as transaction_count,
            SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as hourly_amount
         FROM transactions t
         $whereClause
         GROUP BY HOUR(t.created_at)
         ORDER BY hour",
        $params
    );
    
    return array(
        'code' => 200,
        'data' => array(
            'product_stats' => $productStats,
            'hourly_stats' => $hourlyStats
        )
    );
}

// 日期范围条件辅助函数
function getDateRangeCondition($dateRange) {
    switch ($dateRange) {
        case 'today':
            return "DATE(t.created_at) = CURDATE()";
        case 'yesterday':
            return "DATE(t.created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
        case 'week':
            return "t.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
        case 'month':
            return "t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
        case 'quarter':
            return "t.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)";
        case 'year':
            return "t.created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)";
        case 'custom':
            $startDate = isset($_GET['start_date']) ? $_GET['start_date'] : '';
            $endDate = isset($_GET['end_date']) ? $_GET['end_date'] : '';
            if ($startDate && $endDate) {
                return "DATE(t.created_at) BETWEEN '$startDate' AND '$endDate'";
            }
            return '';
        default:
            return '';
    }
}

// 通知管理
function handleNotifications() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_merchants')) {
        throw new Exception('无权限访问');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetNotifications();
            break;
        case 'POST':
            handleCreateNotification();
            break;
        case 'PUT':
            handleUpdateNotification();
            break;
        case 'DELETE':
            handleDeleteNotification();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取通知列表
function handleGetNotifications() {
    global $db, $currentUser;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $type = isset($_GET['type']) ? $_GET['type'] : '';
    
    $whereConditions = array();
    $params = array();
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "n.merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if ($merchantId > 0) {
            $whereConditions[] = "n.merchant_id = ?";
            $params[] = $merchantId;
        }
    } else {
        throw new Exception('无权限访问通知');
    }
    
    if (!empty($status)) {
        $whereConditions[] = "n.status = ?";
        $params[] = $status;
    }
    
    if (!empty($type)) {
        $whereConditions[] = "n.notification_type = ?";
        $params[] = $type;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取通知列表
    $notifications = $db->fetchAll(
        "SELECT n.*, 
                m.company_name as merchant_name,
                u.real_name as merchant_real_name
         FROM merchant_notification_tasks n
         LEFT JOIN merchants m ON n.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         $whereClause
         ORDER BY n.created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM merchant_notification_tasks n $whereClause",
        $params
    );
    
    // 获取统计信息
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_notifications,
            COUNT(CASE WHEN n.status = 'pending' THEN 1 END) as pending_notifications,
            COUNT(CASE WHEN n.status = 'sent' THEN 1 END) as sent_notifications,
            COUNT(CASE WHEN n.status = 'failed' THEN 1 END) as failed_notifications
         FROM merchant_notification_tasks n $whereClause",
        $params
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'notifications' => $notifications,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($totalResult['count'] / $limit),
                'total_records' => $totalResult['count'],
                'per_page' => $limit
            )
        )
    ));
}

// 创建通知
function handleCreateNotification() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    $required_fields = ['merchant_id', 'notification_type', 'title', 'content'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    $merchantId = intval($input['merchant_id']);
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        if ($merchantId != $currentUser['profile_id']) {
            throw new Exception('无权限为此商户创建通知');
        }
    } elseif ($currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限创建通知');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 创建通知任务
        $db->query(
            "INSERT INTO merchant_notification_tasks (
                merchant_id, notification_type, title, content, 
                scheduled_at, status, created_at
            ) VALUES (?, ?, ?, ?, ?, 'pending', NOW())",
            [
                $merchantId,
                $input['notification_type'],
                $input['title'],
                $input['content'],
                isset($input['scheduled_at']) ? $input['scheduled_at'] : date('Y-m-d H:i:s')
            ]
        );
        
        $notificationId = $db->lastInsertId();
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '通知创建成功',
            'data' => array(
                'notification_id' => $notificationId
            )
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 日报表管理
function handleDailyReports() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'view_transactions')) {
        throw new Exception('无权限访问');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetDailyReports();
            break;
        case 'POST':
            handleGenerateDailyReport();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取日报表
function handleGetDailyReports() {
    global $db, $currentUser;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        // 管理员可以查看所有或指定商户
    } else {
        throw new Exception('无权限访问日报表');
    }
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE r.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    if ($date) {
        $whereClause .= ($whereClause ? ' AND ' : 'WHERE ') . 'r.report_date = ?';
        $params[] = $date;
    }
    
    // 获取日报表数据
    $reports = $db->fetchAll(
        "SELECT r.*, 
                m.company_name as merchant_name,
                u.real_name as merchant_real_name
         FROM merchant_daily_reports r
         LEFT JOIN merchants m ON r.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         $whereClause
         ORDER BY r.report_date DESC, r.merchant_id",
        $params
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'reports' => $reports
        )
    ));
}

// 生成日报表
function handleGenerateDailyReport() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $date = isset($input['date']) ? $input['date'] : date('Y-m-d');
    $merchantId = isset($input['merchant_id']) ? intval($input['merchant_id']) : 0;
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限生成日报表');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 获取需要生成报表的商户列表
        $merchants = [];
        if ($merchantId > 0) {
            $merchant = $db->fetch("SELECT id FROM merchants WHERE id = ?", [$merchantId]);
            if ($merchant) {
                $merchants[] = $merchant;
            }
        } else {
            $merchants = $db->fetchAll("SELECT id FROM merchants WHERE status = 'active'");
        }
        
        $generatedCount = 0;
        
        foreach ($merchants as $merchant) {
            $mid = $merchant['id'];
            
            // 检查是否已存在该日期的报表
            $existingReport = $db->fetch(
                "SELECT id FROM merchant_daily_reports WHERE merchant_id = ? AND report_date = ?",
                [$mid, $date]
            );
            
            if ($existingReport) {
                continue; // 跳过已存在的报表
            }
            
            // 计算该商户当日的统计数据
            $stats = $db->fetch(
                "SELECT 
                    COUNT(*) as total_transactions,
                    COUNT(CASE WHEN status = 'paid' THEN 1 END) as successful_transactions,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions,
                    SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as total_amount,
                    SUM(CASE WHEN status = 'paid' THEN service_fee ELSE 0 END) as total_service_fee,
                    SUM(CASE WHEN status = 'paid' THEN actual_amount ELSE 0 END) as settlement_amount
                 FROM transactions 
                 WHERE merchant_id = ? AND DATE(created_at) = ?",
                [$mid, $date]
            );
            
            // 插入日报表记录
            $db->query(
                "INSERT INTO merchant_daily_reports (
                    merchant_id, report_date, total_transactions, successful_transactions,
                    failed_transactions, total_amount, total_service_fee, settlement_amount,
                    created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())",
                [
                    $mid,
                    $date,
                    $stats['total_transactions'],
                    $stats['successful_transactions'],
                    $stats['failed_transactions'],
                    $stats['total_amount'],
                    $stats['total_service_fee'],
                    $stats['settlement_amount']
                ]
            );
            
            $generatedCount++;
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => "成功生成 {$generatedCount} 份日报表",
            'data' => array(
                'generated_count' => $generatedCount,
                'date' => $date
            )
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 风控管理
function handleRiskControl() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_merchants')) {
        throw new Exception('无权限访问');
    }
    
    // 检查是否是特殊操作请求
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    if ($action === 'risk_events') {
        handleGetRiskEvents();
        return;
    }
    
    if ($action === 'risk_report') {
        handleGenerateRiskReport();
        return;
    }
    
    if ($action === 'export_risk_report') {
        handleExportRiskReport();
        return;
    }
    
    if ($action === 'export_blacklist') {
        handleExportBlacklist();
        return;
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetRiskConfigs();
            break;
        case 'POST':
            handleCreateRiskConfig();
            break;
        case 'PUT':
            handleUpdateRiskConfig();
            break;
        case 'DELETE':
            handleDeleteRiskConfig();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取风控配置
function handleGetRiskConfigs() {
    global $db, $currentUser;
    
    // 如果请求单个配置详情
    if (isset($_GET['id'])) {
        $configId = intval($_GET['id']);
        
        $config = $db->fetch(
            "SELECT r.*, 
                    m.company_name as merchant_name,
                    u.real_name as merchant_real_name
             FROM merchant_risk_configs r
             LEFT JOIN merchants m ON r.merchant_id = m.id
             LEFT JOIN users u ON m.user_id = u.id
             WHERE r.id = ?",
            [$configId]
        );
        
        if (!$config) {
            throw new Exception('风控配置不存在');
        }
        
        // 权限检查
        if ($currentUser['user_type'] === 'merchant') {
            if ($config['merchant_id'] != $currentUser['profile_id']) {
                throw new Exception('无权限访问此风控配置');
            }
        } elseif ($currentUser['user_type'] !== 'admin') {
            throw new Exception('无权限访问风控配置');
        }
        
        echo json_encode(array(
            'code' => 200,
            'data' => $config
        ));
        return;
    }
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $type = isset($_GET['type']) ? $_GET['type'] : '';
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        // 管理员可以查看所有或指定商户
    } else {
        throw new Exception('无权限访问风控配置');
    }
    
    $whereConditions = [];
    $params = [];
    
    if ($merchantId > 0) {
        $whereConditions[] = 'r.merchant_id = ?';
        $params[] = $merchantId;
    }
    
    if (!empty($status)) {
        $whereConditions[] = 'r.status = ?';
        $params[] = $status;
    }
    
    if (!empty($type)) {
        $whereConditions[] = 'r.rule_type = ?';
        $params[] = $type;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取风控配置
    $riskConfigs = $db->fetchAll(
        "SELECT r.*, 
                m.company_name as merchant_name,
                u.real_name as merchant_real_name
         FROM merchant_risk_configs r
         LEFT JOIN merchants m ON r.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         $whereClause
         ORDER BY r.created_at DESC",
        $params
    );
    
    // 获取风控统计
    $riskStats = $db->fetch(
        "SELECT 
            COUNT(*) as total_configs,
            COUNT(CASE WHEN r.status = 'active' THEN 1 END) as active_configs,
            COUNT(CASE WHEN r.status = 'inactive' THEN 1 END) as inactive_configs,
            AVG(r.daily_limit) as avg_daily_limit,
            AVG(r.single_limit) as avg_single_limit
         FROM merchant_risk_configs r $whereClause",
        $params
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'configs' => $riskConfigs,
            'stats' => $riskStats
        )
    ));
}

// 创建风控配置
function handleCreateRiskConfig() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    $required_fields = ['rule_type', 'rule_name'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    // 确定商户ID
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if (!isset($input['merchant_id']) || $input['merchant_id'] === '') {
            throw new Exception('管理员必须指定商户');
        }
        $merchantId = intval($input['merchant_id']);
    } else {
        throw new Exception('无权限创建风控配置');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 创建风控配置
        $db->query(
            "INSERT INTO merchant_risk_configs (
                merchant_id, rule_type, rule_name, rule_config, 
                daily_limit, single_limit, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())",
            [
                $merchantId,
                $input['rule_type'],
                $input['rule_name'],
                json_encode(isset($input['rule_config']) ? $input['rule_config'] : array()),
                floatval(isset($input['daily_limit']) ? $input['daily_limit'] : 0),
                floatval(isset($input['single_limit']) ? $input['single_limit'] : 0),
                isset($input['status']) ? $input['status'] : 'active'
            ]
        );
        
        $configId = $db->lastInsertId();
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '风控配置创建成功',
            'data' => array(
                'config_id' => $configId
            )
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 更新风控配置
function handleUpdateRiskConfig() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (empty($input['id'])) {
        throw new Exception('缺少配置ID');
    }
    
    $configId = intval($input['id']);
    
    // 检查配置是否存在并验证权限
    $config = $db->fetch("SELECT * FROM merchant_risk_configs WHERE id = ?", [$configId]);
    if (!$config) {
        throw new Exception('风控配置不存在');
    }
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        if ($config['merchant_id'] != $currentUser['profile_id']) {
            throw new Exception('无权限修改此风控配置');
        }
    } elseif ($currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限修改风控配置');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 更新风控配置
        $updateFields = [];
        $params = [];
        
        $updateableFields = ['rule_name', 'rule_config', 'daily_limit', 'single_limit', 'status'];
        
        foreach ($updateableFields as $field) {
            if (isset($input[$field])) {
                if ($field === 'rule_config') {
                    $updateFields[] = "$field = ?";
                    $params[] = json_encode($input[$field]);
                } elseif (in_array($field, ['daily_limit', 'single_limit'])) {
                    $updateFields[] = "$field = ?";
                    $params[] = floatval($input[$field]);
                } else {
                    $updateFields[] = "$field = ?";
                    $params[] = $input[$field];
                }
            }
        }
        
        if (!empty($updateFields)) {
            $params[] = $configId;
            $db->query(
                "UPDATE merchant_risk_configs SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?",
                $params
            );
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '风控配置更新成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 删除风控配置
function handleDeleteRiskConfig() {
    global $db, $currentUser;
    
    $configId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if (!$configId) {
        throw new Exception('缺少配置ID');
    }
    
    // 检查配置是否存在并验证权限
    $config = $db->fetch("SELECT * FROM merchant_risk_configs WHERE id = ?", [$configId]);
    if (!$config) {
        throw new Exception('风控配置不存在');
    }
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        if ($config['merchant_id'] != $currentUser['profile_id']) {
            throw new Exception('无权限删除此风控配置');
        }
    } elseif ($currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限删除风控配置');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 删除风控配置
        $db->query("DELETE FROM merchant_risk_configs WHERE id = ?", [$configId]);
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '风控配置删除成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 黑名单管理
function handleBlacklist() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_merchants')) {
        throw new Exception('无权限访问');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetBlacklist();
            break;
        case 'POST':
            handleAddToBlacklist();
            break;
        case 'PUT':
            handleUpdateBlacklist();
            break;
        case 'DELETE':
            handleRemoveFromBlacklist();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取黑名单
function handleGetBlacklist() {
    global $db, $currentUser;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $type = isset($_GET['type']) ? $_GET['type'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    if (!empty($type)) {
        $whereConditions[] = "b.blacklist_type = ?";
        $params[] = $type;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(b.blacklist_value LIKE ? OR b.reason LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取黑名单列表
    $blacklist = $db->fetchAll(
        "SELECT b.*, 
                u.username as created_by_name
         FROM blacklist b
         LEFT JOIN users u ON b.created_by = u.id
         $whereClause
         ORDER BY b.created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM blacklist b $whereClause",
        $params
    );
    
    // 获取统计信息
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_blacklist,
            COUNT(CASE WHEN b.status = 'active' THEN 1 END) as active_blacklist,
            COUNT(CASE WHEN b.blacklist_type = 'ip' THEN 1 END) as ip_blacklist,
            COUNT(CASE WHEN b.blacklist_type IN ('device', 'device_id') THEN 1 END) as device_blacklist
         FROM blacklist b $whereClause",
        $params
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'blacklist' => $blacklist,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($totalResult['count'] / $limit),
                'total_records' => $totalResult['count'],
                'per_page' => $limit
            )
        )
    ));
}

// 添加到黑名单
function handleAddToBlacklist() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    $required_fields = ['blacklist_type', 'blacklist_value', 'reason'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            throw new Exception("缺少必需字段: $field");
        }
    }
    
    // 验证黑名单类型
    $validTypes = ['ip', 'device', 'device_id', 'user_agent', 'phone', 'email'];
    if (!in_array($input['blacklist_type'], $validTypes)) {
        throw new Exception('无效的黑名单类型');
    }
    
    // 检查是否已存在
    $existing = $db->fetch(
        "SELECT id FROM blacklist WHERE blacklist_type = ? AND blacklist_value = ?",
        [$input['blacklist_type'], $input['blacklist_value']]
    );
    
    if ($existing) {
        throw new Exception('该项目已在黑名单中');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 添加到黑名单
        $db->query(
            "INSERT INTO blacklist (
                blacklist_type, blacklist_value, reason, 
                status, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, NOW())",
            [
                $input['blacklist_type'],
                $input['blacklist_value'],
                $input['reason'],
                'active',
                $currentUser['id']
            ]
        );
        
        $blacklistId = $db->lastInsertId();
        
        // 记录安全日志
        logSecurityEvent($currentUser['id'], 'blacklist_add', [
            'blacklist_id' => $blacklistId,
            'type' => $input['blacklist_type'],
            'value' => $input['blacklist_value']
        ]);
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '成功添加到黑名单',
            'data' => array(
                'blacklist_id' => $blacklistId
            )
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 更新黑名单状态
function handleUpdateBlacklist() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['id']) || !is_numeric($input['id'])) {
        throw new Exception('缺少有效的黑名单ID');
    }
    
    $blacklistId = intval($input['id']);
    
    // 检查黑名单是否存在
    $blacklist = $db->fetch(
        "SELECT * FROM blacklist WHERE id = ?",
        [$blacklistId]
    );
    
    if (!$blacklist) {
        throw new Exception('黑名单项目不存在');
    }
    
    // 确定操作类型
    $action = isset($input['action']) ? $input['action'] : '';
    
    switch ($action) {
        case 'enable':
            $newStatus = 'active';
            $message = '黑名单项目已启用';
            $eventType = 'blacklist_enable';
            break;
        case 'disable':
            $newStatus = 'inactive';
            $message = '黑名单项目已禁用';
            $eventType = 'blacklist_disable';
            break;
        default:
            throw new Exception('无效的操作类型');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 更新状态
        $db->query(
            "UPDATE blacklist SET status = ?, updated_at = NOW() WHERE id = ?",
            [$newStatus, $blacklistId]
        );
        
        // 记录安全日志
        logSecurityEvent($currentUser['id'], $eventType, [
            'blacklist_id' => $blacklistId,
            'type' => $blacklist['blacklist_type'],
            'value' => $blacklist['blacklist_value'],
            'old_status' => $blacklist['status'],
            'new_status' => $newStatus
        ]);
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => $message
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 删除黑名单项目
function handleRemoveFromBlacklist() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['id']) || !is_numeric($input['id'])) {
        throw new Exception('缺少有效的黑名单ID');
    }
    
    $blacklistId = intval($input['id']);
    
    // 检查黑名单是否存在
    $blacklist = $db->fetch(
        "SELECT * FROM blacklist WHERE id = ?",
        [$blacklistId]
    );
    
    if (!$blacklist) {
        throw new Exception('黑名单项目不存在');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 删除黑名单项目
        $db->query(
            "DELETE FROM blacklist WHERE id = ?",
            [$blacklistId]
        );
        
        // 记录安全日志
        logSecurityEvent($currentUser['id'], 'blacklist_delete', [
            'blacklist_id' => $blacklistId,
            'type' => $blacklist['blacklist_type'],
            'value' => $blacklist['blacklist_value']
        ]);
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '黑名单项目已删除'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// TOTP双因子认证管理
function handleTOTP() {
    global $db, $currentUser, $auth;
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetTOTPStatus();
            break;
        case 'POST':
            handleSetupTOTP();
            break;
        case 'PUT':
            handleVerifyTOTP();
            break;
        case 'DELETE':
            handleDisableTOTP();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取TOTP状态
function handleGetTOTPStatus() {
    global $db, $currentUser;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if ($merchantId <= 0) {
            throw new Exception('管理员查询TOTP时必须指定商户ID');
        }
    } else {
        throw new Exception('无权限访问TOTP设置');
    }
    
    // 获取TOTP配置
    $totpConfig = $db->fetch(
        "SELECT * FROM merchant_totp WHERE merchant_id = ?",
        [$merchantId]
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'enabled' => $totpConfig ? true : false,
            'setup_at' => $totpConfig ? $totpConfig['created_at'] : null,
            'last_used' => $totpConfig ? $totpConfig['last_used_at'] : null
        )
    ));
}

// 设置TOTP
function handleSetupTOTP() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $merchantId = isset($input['merchant_id']) ? intval($input['merchant_id']) : 0;
    
    // 权限检查
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if ($merchantId <= 0) {
            throw new Exception('管理员设置TOTP时必须指定商户ID');
        }
    } else {
        throw new Exception('无权限设置TOTP');
    }
    
    // 生成TOTP密钥
    $secretKey = generateTOTPSecret();
    
    try {
        $db->query("START TRANSACTION");
        
        // 删除现有的TOTP配置
        $db->query("DELETE FROM merchant_totp WHERE merchant_id = ?", [$merchantId]);
        
        // 创建新的TOTP配置
        $db->query(
            "INSERT INTO merchant_totp (
                merchant_id, secret_key, status, created_at
            ) VALUES (?, ?, 'pending', NOW())",
            [$merchantId, $secretKey]
        );
        
        $db->query("COMMIT");
        
        // 生成二维码URL
        $merchant = $db->fetch("SELECT company_name FROM merchants WHERE id = ?", [$merchantId]);
        $qrCodeUrl = generateTOTPQRCode($secretKey, $merchant['company_name']);
        
        echo json_encode(array(
            'code' => 200,
            'message' => 'TOTP设置成功',
            'data' => array(
                'secret_key' => $secretKey,
                'qr_code_url' => $qrCodeUrl
            )
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 安全日志管理
function handleSecurityLogs() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'view_transactions')) {
        throw new Exception('无权限访问');
    }
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetSecurityLogs();
            break;
        default:
            throw new Exception('不支持的请求方法');
    }
}

// 获取安全日志
function handleGetSecurityLogs() {
    global $db, $currentUser;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $eventType = isset($_GET['event_type']) ? $_GET['event_type'] : '';
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : '';
    
    $whereConditions = array();
    $params = array();
    
    if (!empty($eventType)) {
        $whereConditions[] = "l.event_type = ?";
        $params[] = $eventType;
    }
    
    // 日期范围筛选
    if (!empty($dateRange)) {
        $dateCondition = getDateRangeCondition($dateRange);
        if ($dateCondition) {
            // 替换表别名
            $dateCondition = str_replace('t.created_at', 'l.created_at', $dateCondition);
            $whereConditions[] = $dateCondition;
        }
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 模拟安全日志表（实际应该有专门的security_logs表）
    $logs = $db->fetchAll(
        "SELECT 
            'login' as event_type,
            u.username,
            u.real_name,
            'user_login' as event_description,
            u.last_login_at as created_at,
            u.last_login_ip as ip_address
         FROM users u 
         WHERE u.last_login_at IS NOT NULL
         ORDER BY u.last_login_at DESC
         LIMIT ? OFFSET ?",
        array($limit, $offset)
    );
    
    // 获取总数（模拟）
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM users WHERE last_login_at IS NOT NULL"
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'logs' => $logs,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($totalResult['count'] / $limit),
                'total_records' => $totalResult['count'],
                'per_page' => $limit
            )
        )
    ));
}

// 辅助函数：生成TOTP密钥
function generateTOTPSecret($length = 32) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $secret = '';
    for ($i = 0; $i < $length; $i++) {
        $secret .= $chars[mt_rand(0, strlen($chars) - 1)];
    }
    return $secret;
}

// 辅助函数：生成TOTP二维码URL
function generateTOTPQRCode($secret, $label) {
    $issuer = 'PayPal Payment System';
    $url = "otpauth://totp/{$issuer}:{$label}?secret={$secret}&issuer={$issuer}";
    return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($url);
}

// 辅助函数：记录安全事件
function logSecurityEvent($userId, $eventType, $eventData = []) {
    global $db;
    
    // 这里应该插入到专门的安全日志表
    // 目前简化处理，可以扩展为完整的安全日志系统
    try {
        $db->query(
            "INSERT INTO user_logs (user_id, event_type, event_data, ip_address, created_at) 
             VALUES (?, ?, ?, ?, NOW())
             ON DUPLICATE KEY UPDATE user_id = user_id", // 防止表不存在时报错
            [
                $userId,
                $eventType,
                json_encode($eventData),
                isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0'
            ]
        );
    } catch (Exception $e) {
        // 忽略日志记录错误，不影响主要业务
        error_log("Security log error: " . $e->getMessage());
    }
}

// 支付宝账户管理 - 强化数据隔离
function handleAlipayAccounts() {
    global $db, $currentUser, $auth;
    
    // 检查用户权限：管理员和码商可以访问支付宝账户管理
    if (!in_array($currentUser['user_type'], ['admin', 'provider'])) {
        throw new Exception('无权限访问支付宝账户管理');
    }
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    // 强制数据隔离：码商只能查看自己的账户
    if ($currentUser['user_type'] === 'provider') {
        $whereConditions[] = "a.provider_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        // 管理员可以看到所有账户，但如果指定了provider_id则过滤
        $providerId = isset($_GET['provider_id']) ? intval($_GET['provider_id']) : 0;
        if ($providerId > 0) {
            $whereConditions[] = "a.provider_id = ?";
            $params[] = $providerId;
        }
    } else {
        // 其他用户类型无权访问
        throw new Exception('无权限访问支付宝账户管理');
    }
    
    if (!empty($status) && in_array($status, array('pending', 'approved', 'rejected', 'disabled'))) {
        $whereConditions[] = "a.status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(a.account_name LIKE ? OR a.account_number LIKE ? OR a.real_name LIKE ? OR a.phone LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM alipay_accounts a $whereClause",
        $params
    );
    $total = $totalResult['count'];
    
    // 获取账户列表
    $accounts = $db->fetchAll(
        "SELECT a.*, 
                p.company_name as provider_name,
                u.real_name as approved_by_name,
                d.device_name,
                d.device_id as device_string_id
         FROM alipay_accounts a
         LEFT JOIN payment_providers p ON a.provider_id = p.id
         LEFT JOIN users u ON a.approved_by = u.id
         LEFT JOIN devices d ON a.device_id = d.id
         $whereClause
         ORDER BY a.created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取统计信息（基于当前用户权限）
    $statsParams = $params; // 使用相同的权限过滤条件
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_accounts,
            COUNT(CASE WHEN a.status = 'pending' THEN 1 END) as pending_accounts,
            COUNT(CASE WHEN a.status = 'approved' THEN 1 END) as approved_accounts,
            COUNT(CASE WHEN a.status = 'rejected' THEN 1 END) as rejected_accounts,
            COUNT(CASE WHEN a.status = 'disabled' THEN 1 END) as disabled_accounts,
            SUM(CASE WHEN a.status = 'approved' THEN a.daily_limit ELSE 0 END) as total_daily_limit
         FROM alipay_accounts a $whereClause",
        $statsParams
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'accounts' => $accounts,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            ),
            'user_info' => array(
                'user_type' => $currentUser['user_type'],
                'provider_id' => $currentUser['user_type'] === 'provider' ? $currentUser['profile_id'] : null
            )
        )
    ));
}

// 交易记录 - 确保数据隔离
function handleTransactions() {
    global $db, $currentUser, $auth;
    
    // 检查用户权限：管理员、码商和商户可以访问交易记录
    if (!in_array($currentUser['user_type'], ['admin', 'provider', 'merchant'])) {
        throw new Exception('无权限访问交易记录');
    }
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 10);
    $offset = ($page - 1) * $limit;
    
    $whereConditions = array();
    $params = array();
    
    // 严格的数据隔离
    if ($currentUser['user_type'] === 'provider') {
        $whereConditions[] = "t.provider_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "t.merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        // 管理员可以查看所有交易，但如果指定了过滤条件则应用过滤
        $providerId = isset($_GET['provider_id']) ? intval($_GET['provider_id']) : 0;
        $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
        
        if ($providerId > 0) {
            $whereConditions[] = "t.provider_id = ?";
            $params[] = $providerId;
        }
        
        if ($merchantId > 0) {
            $whereConditions[] = "t.merchant_id = ?";
            $params[] = $merchantId;
        }
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    $transactions = $db->fetchAll(
        "SELECT t.*, 
                p.company_name as provider_name, 
                m.company_name as merchant_name,
                a.account_name as alipay_account_name
         FROM transactions t
         LEFT JOIN payment_providers p ON t.provider_id = p.id
         LEFT JOIN merchants m ON t.merchant_id = m.id
         LEFT JOIN alipay_accounts a ON t.alipay_account_id = a.id
         $whereClause
         ORDER BY t.created_at DESC 
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    $totalSql = "SELECT COUNT(*) as count FROM transactions t " . $whereClause;
    $total = $db->fetch($totalSql, $params)['count'];
    
    // 统计信息（基于当前用户权限）
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_transactions,
            COUNT(CASE WHEN t.status = 'success' THEN 1 END) as success_transactions,
            COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_transactions,
            COUNT(CASE WHEN t.status = 'failed' THEN 1 END) as failed_transactions,
            SUM(CASE WHEN t.status = 'success' THEN t.amount ELSE 0 END) as total_amount
         FROM transactions t $whereClause",
        $params
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'transactions' => $transactions,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            ),
            'user_info' => array(
                'user_type' => $currentUser['user_type'],
                'provider_id' => $currentUser['user_type'] === 'provider' ? $currentUser['profile_id'] : null,
                'merchant_id' => $currentUser['user_type'] === 'merchant' ? $currentUser['profile_id'] : null
            )
        )
    ));
}

// ==================== 脚本管理相关函数 ====================

/**
 * AES加密脚本内容（与script_match.php保持一致）
 */
function encryptScript($content) {
    $salt = "PayPal_Script_2025";
    $keySource = $salt . "UNIFIED_KEY";
    $key = hash('sha256', $keySource, true);
    
    // 生成随机IV
    $iv = openssl_random_pseudo_bytes(16);
    
    // AES-256-CBC加密
    $encrypted = openssl_encrypt($content, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
    
    if ($encrypted === false) {
        return null;
    }
    
    // 组合IV + 密文并Base64编码
    return base64_encode($iv . $encrypted);
}

/**
 * 解密脚本内容（用于查看）
 */
function decryptScript($encryptedContent) {
    $salt = "PayPal_Script_2025";
    $keySource = $salt . "UNIFIED_KEY";
    $key = hash('sha256', $keySource, true);
    
    // Base64解码
    $combined = base64_decode($encryptedContent);
    if ($combined === false || strlen($combined) <= 16) {
        return null;
    }
    
    // 分离IV和密文
    $iv = substr($combined, 0, 16);
    $encrypted = substr($combined, 16);
    
    // AES-256-CBC解密
    $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
    
    return $decrypted;
}

// 获取设备品牌统计（基于实际注册设备）
function handleGetDeviceBrands() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_scripts')) {
        throw new Exception('无权限访问');
    }
    
    // 从实际注册的设备中统计品牌信息
    $brands = $db->fetchAll("
        SELECT 
            device_brand,
            COUNT(*) as device_count,
            COUNT(CASE WHEN alipay_version != '未安装' AND alipay_version != '' THEN 1 END) as alipay_devices,
            COUNT(CASE WHEN wechat_version != '未安装' AND wechat_version != '' THEN 1 END) as wechat_devices,
            GROUP_CONCAT(DISTINCT alipay_version ORDER BY alipay_version) as alipay_versions,
            GROUP_CONCAT(DISTINCT wechat_version ORDER BY wechat_version) as wechat_versions
        FROM devices 
        WHERE device_brand IS NOT NULL AND device_brand != ''
        GROUP BY device_brand
        ORDER BY device_count DESC
    ");
    
    echo json_encode(array(
        'success' => true,
        'data' => $brands
    ));
}

// 获取脚本矩阵（每个品牌的脚本覆盖情况）
function handleGetScriptMatrix() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_scripts')) {
        throw new Exception('无权限访问');
    }
    
    // 获取所有脚本记录，按品牌和类型分组
    $scripts = $db->fetchAll("
        SELECT 
            device_brand,
            CONCAT(app_type, '_', environment) as script_key,
            COUNT(*) as script_count,
            MAX(updated_at) as last_updated,
            GROUP_CONCAT(app_version ORDER BY app_version) as versions
        FROM script_versions 
        WHERE is_active = 1
        GROUP BY device_brand, app_type, environment
    ");
    
    // 重组数据为矩阵格式
    $matrix = array();
    foreach ($scripts as $script) {
        $brand = $script['device_brand'];
        $key = $script['script_key'];
        
        if (!isset($matrix[$brand])) {
            $matrix[$brand] = array();
        }
        
        $matrix[$brand][$key] = array(
            'count' => $script['script_count'],
            'last_updated' => $script['last_updated'],
            'versions' => $script['versions']
        );
    }
    
    echo json_encode(array(
        'success' => true,
        'data' => $matrix
    ));
}

// 获取指定品牌的脚本内容
function handleGetBrandScript() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_scripts')) {
        throw new Exception('无权限访问');
    }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $brandCode = isset($input['brand_code']) ? $input['brand_code'] : '';
    $appType = isset($input['app_type']) ? $input['app_type'] : '';
    $environment = isset($input['environment']) ? $input['environment'] : '';
    
    if (!$brandCode || !$appType || !$environment) {
        throw new Exception('缺少必要参数');
    }
    
    // 查找最新的脚本
    $script = $db->fetch("
        SELECT * FROM script_versions 
        WHERE device_brand = ? AND app_type = ? AND environment = ? AND is_active = 1
        ORDER BY updated_at DESC 
        LIMIT 1
    ", array($brandCode, $appType, $environment));
    
    if (!$script) {
        throw new Exception('未找到对应的脚本');
    }
    
    // 读取加密的脚本文件内容
    $scriptPath = '../scripts/' . $script['file_path'];
    if (!file_exists($scriptPath)) {
        throw new Exception('脚本文件不存在');
    }
    
    $encryptedContent = file_get_contents($scriptPath);
    if ($encryptedContent === false) {
        throw new Exception('读取脚本文件失败');
    }
    
    // 解密脚本内容用于查看
    $content = decryptScript($encryptedContent);
    if ($content === false) {
        throw new Exception('脚本解密失败');
    }
    
    echo json_encode(array(
        'success' => true,
        'data' => array(
            'script_name' => $script['script_name'],
            'content' => $content,
            'version' => $script['version'],
            'description' => $script['description'],
            'file_size' => $script['file_size'],
            'updated_at' => $script['updated_at']
        )
    ));
}

// 上传品牌脚本
function handleUploadBrandScript() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_scripts')) {
        throw new Exception('无权限访问');
    }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $brandCode = isset($input['device_brand']) ? $input['device_brand'] : '';
    $appType = isset($input['app_type']) ? $input['app_type'] : '';
    $appVersion = isset($input['app_version']) ? $input['app_version'] : '*';
    $environment = isset($input['environment']) ? $input['environment'] : 'release';
    $description = isset($input['description']) ? $input['description'] : '';
    $scriptContent = isset($input['script_content']) ? $input['script_content'] : '';
    $originalFilename = isset($input['original_filename']) ? $input['original_filename'] : 'unknown.js';
    
    if (!$brandCode || !$appType || !$scriptContent) {
        throw new Exception('缺少必要参数');
    }
    
    // 生成脚本文件名和路径（使用版本号作为文件名）
    // 特殊处理：* 版本改为 universal
    $safeVersion = ($appVersion === '*') ? 'universal' : $appVersion;
    $fileName = $safeVersion . '.js';
    $relativePath = $appType . '/' . $brandCode . '/' . $environment . '/' . $fileName;
    $fullPath = dirname(dirname(dirname(__FILE__))) . '/scripts/' . $relativePath;
    
    // 创建目录结构
    $dir = dirname($fullPath);
    if (!is_dir($dir)) {
        if (!mkdir($dir, 0755, true)) {
            throw new Exception('创建脚本目录失败');
        }
    }
    
    // 加密脚本内容
    $encryptedContent = encryptScript($scriptContent);
    if ($encryptedContent === null) {
        throw new Exception('脚本加密失败');
    }
    
    // 保存加密后的脚本文件到服务器
    if (file_put_contents($fullPath, $encryptedContent) === false) {
        throw new Exception('保存加密脚本文件失败');
    }
    
    // 同时保存原始脚本到本地（如果是本地环境）
    $localPath = dirname(dirname(dirname(__FILE__))) . '/scripts/' . $relativePath;
    $localDir = dirname($localPath);
    if (!is_dir($localDir)) {
        mkdir($localDir, 0755, true);
    }
    file_put_contents($localPath, $scriptContent);
    
    $fileSize = filesize($fullPath);
    
    // 生成脚本名称（更简洁的格式）
    $scriptName = $brandCode . '_' . $appType . '_' . $environment . '_' . $appVersion;
    
    // 保存到数据库（如果已存在则更新）
    $existingScript = $db->fetch("
        SELECT id FROM script_versions 
        WHERE app_type = ? AND app_version = ? AND device_brand = ? AND environment = ?
    ", array($appType, $appVersion, $brandCode, $environment));
    
    if ($existingScript) {
        // 更新现有记录
        $db->execute("
            UPDATE script_versions 
            SET script_name = ?, file_path = ?, file_size = ?, description = ?, updated_at = NOW()
            WHERE id = ?
        ", array($scriptName, $relativePath, $fileSize, $description, $existingScript['id']));
    } else {
        // 插入新记录
        $db->execute("
            INSERT INTO script_versions 
            (script_name, app_type, app_version, device_brand, environment, file_path, file_size, description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ", array($scriptName, $appType, $appVersion, $brandCode, $environment, $relativePath, $fileSize, $description));
    }
    
    echo json_encode(array(
        'success' => true,
        'message' => '脚本上传成功',
        'data' => array(
            'script_name' => $scriptName,
            'file_path' => $relativePath,
            'file_size' => $fileSize,
            'original_filename' => $originalFilename,
            'saved_as' => $fileName
        )
    ));
}

// 处理加密脚本上传（来自Python工具）
function handleUploadScriptEncrypted() {
    global $db, $currentUser, $auth;
    
    // 对于Python工具上传，暂时跳过权限验证（后续可以添加API密钥验证）
    // if (!$auth->hasPermission($currentUser, 'manage_scripts')) {
    //     throw new Exception('无权限访问');
    // }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $appType = isset($input['app_type']) ? $input['app_type'] : '';
    $deviceBrand = isset($input['device_brand']) ? $input['device_brand'] : '';
    $appVersion = isset($input['app_version']) ? $input['app_version'] : '';
    $environment = isset($input['environment']) ? $input['environment'] : 'release';
    $description = isset($input['description']) ? $input['description'] : '';
    $scriptContent = isset($input['script_content']) ? $input['script_content'] : '';
    $uploadTime = isset($input['upload_time']) ? $input['upload_time'] : date('Y-m-d H:i:s');
    $isEncrypted = isset($input['encrypted']) ? $input['encrypted'] : false;
    
    if (!$appType || !$deviceBrand || !$appVersion || !$scriptContent) {
        throw new Exception('缺少必要参数: app_type, device_brand, app_version, script_content');
    }
    
    // 验证参数值
    $validApps = array('alipay', 'wechat');
    $validBrands = array('xiaomi', 'huawei', 'oppo', 'vivo', 'samsung');
    $validEnvironments = array('debug', 'release');
    
    if (!in_array($appType, $validApps)) {
        throw new Exception('无效的应用类型: ' . $appType);
    }
    
    if (!in_array($deviceBrand, $validBrands)) {
        throw new Exception('无效的设备品牌: ' . $deviceBrand);
    }
    
    if (!in_array($environment, $validEnvironments)) {
        throw new Exception('无效的环境类型: ' . $environment);
    }
    
    // 生成文件路径
    $fileName = $appVersion . '.js';
    $relativePath = $appType . '/' . $deviceBrand . '/' . $environment . '/' . $fileName;
    $fullPath = dirname(dirname(dirname(__FILE__))) . '/scripts/' . $relativePath;
    
    // 创建目录结构
    $dir = dirname($fullPath);
    if (!is_dir($dir)) {
        if (!mkdir($dir, 0755, true)) {
            throw new Exception('创建脚本目录失败: ' . $dir);
        }
    }
    
    // 如果内容已经加密，直接保存；否则先加密
    if ($isEncrypted) {
        $encryptedContent = $scriptContent;
    } else {
        $encryptedContent = encryptScript($scriptContent);
        if ($encryptedContent === null) {
            throw new Exception('脚本加密失败');
        }
    }
    
    // 保存加密后的脚本文件到服务器
    if (file_put_contents($fullPath, $encryptedContent) === false) {
        throw new Exception('保存加密脚本文件失败: ' . $fullPath);
    }
    
    $fileSize = filesize($fullPath);
    
    // 生成脚本名称
    $scriptName = $deviceBrand . '_' . $appType . '_' . $environment . '_' . $appVersion;
    
    // 检查是否已存在相同的脚本
    $existingScript = $db->fetch("
        SELECT id FROM script_versions 
        WHERE app_type = ? AND app_version = ? AND device_brand = ? AND environment = ?
    ", array($appType, $appVersion, $deviceBrand, $environment));
    
    if ($existingScript) {
        // 更新现有记录
        $db->execute("
            UPDATE script_versions 
            SET script_name = ?, file_path = ?, file_size = ?, description = ?, updated_at = NOW()
            WHERE id = ?
        ", array($scriptName, $relativePath, $fileSize, $description, $existingScript['id']));
        
        $message = '脚本更新成功';
    } else {
        // 插入新记录
        $db->execute("
            INSERT INTO script_versions 
            (script_name, app_type, app_version, device_brand, environment, file_path, file_size, description, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ", array($scriptName, $appType, $appVersion, $deviceBrand, $environment, $relativePath, $fileSize, $description));
        
        $message = '脚本上传成功';
    }
    
    // 记录上传日志
    error_log("Script uploaded: {$scriptName} by Python tool at {$uploadTime}");
    
    echo json_encode(array(
        'success' => true,
        'message' => $message,
        'data' => array(
            'script_name' => $scriptName,
            'file_path' => $relativePath,
            'file_size' => $fileSize,
            'app_type' => $appType,
            'device_brand' => $deviceBrand,
            'app_version' => $appVersion,
            'environment' => $environment,
            'upload_time' => $uploadTime,
            'encrypted' => $isEncrypted
        )
    ));
}

// 员工管理
function handleEmployees() {
    global $db, $currentUser, $auth;
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        handleGetEmployees();
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = isset($input['action']) ? $input['action'] : '';
        
        switch ($action) {
            case 'create':
                handleCreateEmployee($input);
                break;
            case 'update':
                handleUpdateEmployee($input);
                break;
            case 'delete':
                handleDeleteEmployee($input);
                break;
            case 'update_permissions':
                handleUpdateEmployeePermissions($input);
                break;
            default:
                throw new Exception('不支持的操作');
        }
    } else {
        throw new Exception('不支持的请求方法');
    }
}

// 获取员工列表
function handleGetEmployees() {
    global $db, $currentUser, $auth;
    
    // 只有主账户（admin、provider、merchant）可以管理自己的员工
    if ($currentUser['is_staff'] == 1) {
        throw new Exception('员工账户无权限管理其他员工');
    }
    
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $jobPositionId = isset($_GET['job_position_id']) ? intval($_GET['job_position_id']) : 0;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    
    // 构建查询条件
    $whereConditions = array('u.parent_id = ? AND u.is_staff = 1');
    $params = array($currentUser['id']);
    
    if ($search) {
        $whereConditions[] = '(u.username LIKE ? OR u.email LIKE ? OR u.full_name LIKE ?)';
        $searchTerm = '%' . $search . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if ($jobPositionId > 0) {
        $whereConditions[] = 'u.job_position_id = ?';
        $params[] = $jobPositionId;
    }
    
    if ($status && in_array($status, array('active', 'inactive', 'suspended'))) {
        $whereConditions[] = 'u.status = ?';
        $params[] = $status;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 获取员工列表
    $sql = "
        SELECT 
            u.id,
            u.username,
            u.email,
            u.full_name,
            u.status,
            u.job_position_id,
            u.created_at,
            u.last_login,
            jp.position_name,
            jp.description as position_description
        FROM users u
        LEFT JOIN job_positions jp ON u.job_position_id = jp.id
        WHERE {$whereClause}
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $employees = $db->fetchAll($sql, $params);
    
    // 获取总数
    $countSql = "
        SELECT COUNT(*) as total
        FROM users u
        WHERE {$whereClause}
    ";
    $countParams = array_slice($params, 0, -2); // 移除limit和offset参数
    $totalCount = $db->fetch($countSql, $countParams)['total'];
    
    // 获取可用的职位列表（仅限当前用户类型）
    $jobPositions = $db->fetchAll("
        SELECT id, position_name, description
        FROM job_positions
        WHERE user_type = ?
        ORDER BY position_name
    ", array($currentUser['user_type']));
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'employees' => $employees,
            'pagination' => array(
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $totalCount,
                'total_pages' => ceil($totalCount / $limit)
            ),
            'job_positions' => $jobPositions
        )
    ));
}

// 创建员工
function handleCreateEmployee($input) {
    global $db, $currentUser, $auth;
    
    // 只有主账户可以创建员工
    if ($currentUser['is_staff'] == 1) {
        throw new Exception('员工账户无权限创建其他员工');
    }
    
    $username = isset($input['username']) ? trim($input['username']) : '';
    $email = isset($input['email']) ? trim($input['email']) : '';
    $fullName = isset($input['full_name']) ? trim($input['full_name']) : '';
    $password = isset($input['password']) ? trim($input['password']) : '';
    $jobPositionId = isset($input['job_position_id']) ? intval($input['job_position_id']) : 0;
    $permissions = isset($input['permissions']) ? $input['permissions'] : array();
    
    // 验证必填字段
    if (!$username || !$email || !$fullName || !$password) {
        throw new Exception('用户名、邮箱、姓名和密码不能为空');
    }
    
    if ($jobPositionId <= 0) {
        throw new Exception('请选择职位');
    }
    
    // 验证职位是否属于当前用户类型
    $jobPosition = $db->fetch("
        SELECT id, position_name, user_type
        FROM job_positions
        WHERE id = ? AND user_type = ?
    ", array($jobPositionId, $currentUser['user_type']));
    
    if (!$jobPosition) {
        throw new Exception('无效的职位选择');
    }
    
    // 检查用户名和邮箱是否已存在
    $existingUser = $db->fetch("
        SELECT id FROM users WHERE username = ? OR email = ?
    ", array($username, $email));
    
    if ($existingUser) {
        throw new Exception('用户名或邮箱已存在');
    }
    
    // 验证密码强度
    if (strlen($password) < 6) {
        throw new Exception('密码长度至少6位');
    }
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 创建员工用户
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $db->execute("
            INSERT INTO users (
                username, email, full_name, password, user_type, 
                parent_id, is_staff, job_position_id, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, 1, ?, 'active', NOW())
        ", array(
            $username, $email, $fullName, $hashedPassword, 
            $currentUser['user_type'], $currentUser['id'], $jobPositionId
        ));
        
        $employeeId = $db->lastInsertId();
        
        // 设置员工权限
        if (!empty($permissions)) {
            foreach ($permissions as $permissionId) {
                $permissionId = intval($permissionId);
                if ($permissionId > 0) {
                    // 验证权限是否存在且属于当前模块
                    $permission = $db->fetch("
                        SELECT id FROM permissions WHERE id = ?
                    ", array($permissionId));
                    
                    if ($permission) {
                        $db->execute("
                            INSERT INTO user_permissions (user_id, permission_id, granted_by, created_at)
                            VALUES (?, ?, ?, NOW())
                        ", array($employeeId, $permissionId, $currentUser['id']));
                    }
                }
            }
        }
        
        // 记录操作日志
        logSecurityEvent($currentUser['id'], 'employee_created', array(
            'employee_id' => $employeeId,
            'username' => $username,
            'job_position_id' => $jobPositionId
        ));
        
        $db->commit();
        
        echo json_encode(array(
            'code' => 200,
            'message' => '员工创建成功',
            'data' => array('employee_id' => $employeeId)
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 更新员工信息
function handleUpdateEmployee($input) {
    global $db, $currentUser, $auth;
    
    // 只有主账户可以更新员工
    if ($currentUser['is_staff'] == 1) {
        throw new Exception('员工账户无权限更新其他员工');
    }
    
    $employeeId = isset($input['employee_id']) ? intval($input['employee_id']) : 0;
    $email = isset($input['email']) ? trim($input['email']) : '';
    $fullName = isset($input['full_name']) ? trim($input['full_name']) : '';
    $jobPositionId = isset($input['job_position_id']) ? intval($input['job_position_id']) : 0;
    $status = isset($input['status']) ? trim($input['status']) : '';
    $password = isset($input['password']) ? trim($input['password']) : '';
    
    if ($employeeId <= 0) {
        throw new Exception('员工ID无效');
    }
    
    // 验证员工是否存在且属于当前用户
    $employee = $db->fetch("
        SELECT u.id, u.username, u.status, e.real_name, e.job_position_id, e.status as employee_status
        FROM users u
        INNER JOIN employees e ON u.id = e.user_id
        WHERE u.id = ? AND u.parent_id = ? AND u.user_type = 'employee'
    ", array($employeeId, $currentUser['id']));
    
    if (!$employee) {
        throw new Exception('员工不存在或无权限访问');
    }
    
    // 验证邮箱是否被其他用户使用
    if ($email && $email !== $employee['email']) {
        $existingUser = $db->fetch("
            SELECT id FROM users WHERE email = ? AND id != ?
        ", array($email, $employeeId));
        
        if ($existingUser) {
            throw new Exception('邮箱已被其他用户使用');
        }
    }
    
    // 验证职位
    if ($jobPositionId > 0) {
        $jobPosition = $db->fetch("
            SELECT id FROM job_positions
            WHERE id = ? AND user_type = ?
        ", array($jobPositionId, $currentUser['user_type']));
        
        if (!$jobPosition) {
            throw new Exception('无效的职位选择');
        }
    }
    
    // 验证状态
    if ($status && !in_array($status, array('active', 'inactive', 'suspended'))) {
        throw new Exception('无效的状态值');
    }
    
    // 构建更新字段
    $updateFields = array();
    $params = array();
    
    if ($email) {
        $updateFields[] = 'email = ?';
        $params[] = $email;
    }
    
    if ($fullName) {
        $updateFields[] = 'full_name = ?';
        $params[] = $fullName;
    }
    
    if ($jobPositionId > 0) {
        $updateFields[] = 'job_position_id = ?';
        $params[] = $jobPositionId;
    }
    
    if ($status) {
        $updateFields[] = 'status = ?';
        $params[] = $status;
    }
    
    if ($password) {
        if (strlen($password) < 6) {
            throw new Exception('密码长度至少6位');
        }
        $updateFields[] = 'password = ?';
        $params[] = password_hash($password, PASSWORD_DEFAULT);
    }
    
    if (empty($updateFields)) {
        throw new Exception('没有需要更新的字段');
    }
    
    $updateFields[] = 'updated_at = NOW()';
    $params[] = $employeeId;
    
    $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $db->execute($sql, $params);
    
    // 记录操作日志
    logSecurityEvent($currentUser['id'], 'employee_updated', array(
        'employee_id' => $employeeId,
        'updated_fields' => array_keys($input)
    ));
    
    echo json_encode(array(
        'code' => 200,
        'message' => '员工信息更新成功'
    ));
}

// 删除员工
function handleDeleteEmployee($input) {
    global $db, $currentUser, $auth;
    
    // 只有主账户可以删除员工
    if ($currentUser['is_staff'] == 1) {
        throw new Exception('员工账户无权限删除其他员工');
    }
    
    $employeeId = isset($input['employee_id']) ? intval($input['employee_id']) : 0;
    
    if ($employeeId <= 0) {
        throw new Exception('员工ID无效');
    }
    
    // 验证员工是否存在且属于当前用户
    $employee = $db->fetch("
        SELECT id, username FROM users
        WHERE id = ? AND parent_id = ? AND is_staff = 1
    ", array($employeeId, $currentUser['id']));
    
    if (!$employee) {
        throw new Exception('员工不存在或无权限访问');
    }
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 删除员工的权限记录
        $db->execute("DELETE FROM user_permissions WHERE user_id = ?", array($employeeId));
        
        // 删除员工的会话记录
        $db->execute("DELETE FROM user_sessions WHERE user_id = ?", array($employeeId));
        
        // 删除员工用户
        $db->execute("DELETE FROM users WHERE id = ?", array($employeeId));
        
        // 记录操作日志
        logSecurityEvent($currentUser['id'], 'employee_deleted', array(
            'employee_id' => $employeeId,
            'username' => $employee['username']
        ));
        
        $db->commit();
        
        echo json_encode(array(
            'code' => 200,
            'message' => '员工删除成功'
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 更新员工权限
function handleUpdateEmployeePermissions($input) {
    global $db, $currentUser, $auth;
    
    // 只有主账户可以管理员工权限
    if ($currentUser['is_staff'] == 1) {
        throw new Exception('员工账户无权限管理其他员工权限');
    }
    
    $employeeId = isset($input['employee_id']) ? intval($input['employee_id']) : 0;
    $permissions = isset($input['permissions']) ? $input['permissions'] : array();
    
    if ($employeeId <= 0) {
        throw new Exception('员工ID无效');
    }
    
    // 验证员工是否存在且属于当前用户
    $employee = $db->fetch("
        SELECT id, username FROM users
        WHERE id = ? AND parent_id = ? AND is_staff = 1
    ", array($employeeId, $currentUser['id']));
    
    if (!$employee) {
        throw new Exception('员工不存在或无权限访问');
    }
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 删除现有权限
        $db->execute("DELETE FROM user_permissions WHERE user_id = ?", array($employeeId));
        
        // 添加新权限
        if (!empty($permissions)) {
            foreach ($permissions as $permissionId) {
                $permissionId = intval($permissionId);
                if ($permissionId > 0) {
                    // 验证权限是否存在
                    $permission = $db->fetch("
                        SELECT id FROM permissions WHERE id = ?
                    ", array($permissionId));
                    
                    if ($permission) {
                        $db->execute("
                            INSERT INTO user_permissions (user_id, permission_id, granted_by, created_at)
                            VALUES (?, ?, ?, NOW())
                        ", array($employeeId, $permissionId, $currentUser['id']));
                    }
                }
            }
        }
        
        // 记录操作日志
        logSecurityEvent($currentUser['id'], 'employee_permissions_updated', array(
            'employee_id' => $employeeId,
            'permissions_count' => count($permissions)
        ));
        
        $db->commit();
        
        echo json_encode(array(
            'code' => 200,
            'message' => '员工权限更新成功'
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 权限管理
function handlePermissions() {
    global $db, $currentUser, $auth;
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        handleGetPermissions();
    } else {
        throw new Exception('不支持的请求方法');
    }
}

// 获取权限列表
function handleGetPermissions() {
    global $db, $currentUser, $auth;
    
    // 只有主账户可以查看权限
    if ($currentUser['is_staff'] == 1) {
        throw new Exception('员工账户无权限查看权限列表');
    }
    
    $employeeId = isset($_GET['employee_id']) ? intval($_GET['employee_id']) : 0;
    
    // 获取所有权限，按模块分组
    $permissions = $db->fetchAll("
        SELECT id, permission_name, module, description
        FROM permissions
        ORDER BY module, permission_name
    ");
    
    // 按模块分组
    $groupedPermissions = array();
    foreach ($permissions as $permission) {
        $module = $permission['module'];
        if (!isset($groupedPermissions[$module])) {
            $groupedPermissions[$module] = array();
        }
        $groupedPermissions[$module][] = $permission;
    }
    
    $response = array(
        'code' => 200,
        'data' => array(
            'permissions' => $groupedPermissions
        )
    );
    
    // 如果指定了员工ID，获取该员工的权限
    if ($employeeId > 0) {
        // 验证员工是否属于当前用户
        $employee = $db->fetch("
            SELECT id FROM users
            WHERE id = ? AND parent_id = ? AND is_staff = 1
        ", array($employeeId, $currentUser['id']));
        
        if ($employee) {
            $employeePermissions = $db->fetchAll("
                SELECT permission_id
                FROM user_permissions
                WHERE user_id = ?
            ", array($employeeId));
            
            $employeePermissionIds = array();
            foreach ($employeePermissions as $perm) {
                $employeePermissionIds[] = $perm['permission_id'];
            }
            
            $response['data']['employee_permissions'] = $employeePermissionIds;
        }
    }
    
    echo json_encode($response);
}

// 职位管理
function handleJobPositions() {
    global $db, $currentUser, $auth;
    
    // 只有主账户可以管理职位
    if ($currentUser['is_staff'] == 1) {
        throw new Exception('员工账户无权限管理职位');
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        handleGetJobPositions();
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = isset($input['action']) ? $input['action'] : '';
        
        switch ($action) {
            case 'create':
                if (isset($input['save_with_permissions']) && $input['save_with_permissions']) {
                    handleCreateJobPositionWithPermissions($input);
                } else {
                    handleCreateJobPosition($input);
                }
                break;
            case 'update':
                if (isset($input['save_with_permissions']) && $input['save_with_permissions']) {
                    handleUpdateJobPositionWithPermissions($input);
                } else {
                    handleUpdateJobPosition($input);
                }
                break;
            case 'delete':
                handleDeleteJobPosition($input);
                break;
            case 'get_permissions':
                handleGetJobPositionPermissions($input);
                break;
            case 'update_permissions':
                handleUpdateJobPositionPermissions($input);
                break;
            default:
                throw new Exception('不支持的操作');
        }
    } else {
        throw new Exception('不支持的请求方法');
    }
}

// 获取职位列表
function handleGetJobPositions() {
    global $db, $currentUser, $auth;
    
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    
    // 构建查询条件
    $whereConditions = array('(created_by = ? OR is_system = 1) AND user_type = ?');
    $params = array($currentUser['id'], $currentUser['user_type']);
    
    if ($search) {
        $whereConditions[] = '(position_name LIKE ? OR description LIKE ?)';
        $searchTerm = '%' . $search . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if ($status && in_array($status, array('active', 'inactive'))) {
        $whereConditions[] = 'status = ?';
        $params[] = $status;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 获取职位列表
    $sql = "
        SELECT 
            jp.id,
            jp.name,
            jp.position_name,
            jp.description,
            jp.user_type,
            jp.created_by,
            jp.is_system,
            jp.status,
            jp.created_at,
            jp.updated_at,
            (SELECT COUNT(*) FROM users WHERE job_position_id = jp.id) as employee_count,
            (SELECT COUNT(*) FROM job_position_permissions WHERE job_position_id = jp.id) as permission_count
        FROM job_positions jp
        WHERE {$whereClause}
        ORDER BY is_system DESC, created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $positions = $db->fetchAll($sql, $params);
    
    // 获取总数
    $countSql = "
        SELECT COUNT(*) as total
        FROM job_positions jp
        WHERE {$whereClause}
    ";
    $countParams = array_slice($params, 0, -2); // 移除limit和offset参数
    $totalCount = $db->fetch($countSql, $countParams)['total'];
    
    // 获取所有可用权限（用于权限配置）
    $permissions = $db->fetchAll("
        SELECT 
            id,
            permission_key,
            permission_name,
            description,
            module
        FROM permissions 
        WHERE user_type = ? OR user_type = 'all'
        ORDER BY module, permission_name
    ", array($currentUser['user_type']));
    
    // 按模块分组权限
    $permissionsByModule = array();
    foreach ($permissions as $permission) {
        $module = $permission['module'] ?: '其他';
        if (!isset($permissionsByModule[$module])) {
            $permissionsByModule[$module] = array();
        }
        $permissionsByModule[$module][] = $permission;
    }
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'positions' => $positions,
            'permissions' => $permissionsByModule,
            'pagination' => array(
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $totalCount,
                'total_pages' => ceil($totalCount / $limit)
            )
        )
    ));
}

// 创建职位
function handleCreateJobPosition($input) {
    global $db, $currentUser, $auth;
    
    $name = isset($input['name']) ? trim($input['name']) : '';
    $positionName = isset($input['position_name']) ? trim($input['position_name']) : '';
    $description = isset($input['description']) ? trim($input['description']) : '';
    
    // 验证必填字段
    if (!$name || !$positionName) {
        throw new Exception('职位代码和显示名称不能为空');
    }
    
    // 检查职位代码是否已存在（同一用户类型下不能重复）
    $existingPosition = $db->fetch("
        SELECT id FROM job_positions 
        WHERE name = ? AND user_type = ? AND (created_by = ? OR is_system = 1)
    ", array($name, $currentUser['user_type'], $currentUser['id']));
    
    if ($existingPosition) {
        throw new Exception('职位代码已存在');
    }
    
    // 创建职位
    $db->execute("
        INSERT INTO job_positions (
            name, position_name, description, user_type, 
            created_by, is_system, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, 0, 'active', NOW(), NOW())
    ", array(
        $name, $positionName, $description, 
        $currentUser['user_type'], $currentUser['id']
    ));
    
    $positionId = $db->lastInsertId();
    
    // 记录操作日志
    logSecurityEvent($currentUser['id'], 'job_position_created', array(
        'position_id' => $positionId,
        'position_name' => $positionName
    ));
    
    echo json_encode(array(
        'code' => 200,
        'message' => '职位创建成功',
        'data' => array('position_id' => $positionId)
    ));
}

// 更新职位
function handleUpdateJobPosition($input) {
    global $db, $currentUser, $auth;
    
    $positionId = isset($input['position_id']) ? intval($input['position_id']) : 0;
    $name = isset($input['name']) ? trim($input['name']) : '';
    $positionName = isset($input['position_name']) ? trim($input['position_name']) : '';
    $description = isset($input['description']) ? trim($input['description']) : '';
    $status = isset($input['status']) ? trim($input['status']) : '';
    
    if ($positionId <= 0) {
        throw new Exception('职位ID无效');
    }
    
    // 验证职位是否存在且属于当前用户
    $position = $db->fetch("
        SELECT id, name, position_name, is_system, created_by
        FROM job_positions
        WHERE id = ?
    ", array($positionId));
    
    if (!$position) {
        throw new Exception('职位不存在');
    }
    
    // 检查权限：只能修改自己创建的职位
    if ($position['is_system'] == 1) {
        throw new Exception('不能修改系统预设职位');
    }
    
    if ($position['created_by'] != $currentUser['id']) {
        throw new Exception('无权限修改此职位');
    }
    
    // 验证职位代码是否重复
    if ($name && $name !== $position['name']) {
        $existingPosition = $db->fetch("
            SELECT id FROM job_positions 
            WHERE name = ? AND user_type = ? AND id != ? AND (created_by = ? OR is_system = 1)
        ", array($name, $currentUser['user_type'], $positionId, $currentUser['id']));
        
        if ($existingPosition) {
            throw new Exception('职位代码已存在');
        }
    }
    
    // 构建更新字段
    $updateFields = array();
    $params = array();
    
    if ($name) {
        $updateFields[] = 'name = ?';
        $params[] = $name;
    }
    
    if ($positionName) {
        $updateFields[] = 'position_name = ?';
        $params[] = $positionName;
    }
    
    if ($description !== null) {
        $updateFields[] = 'description = ?';
        $params[] = $description;
    }
    
    if ($status && in_array($status, array('active', 'inactive'))) {
        $updateFields[] = 'status = ?';
        $params[] = $status;
    }
    
    if (empty($updateFields)) {
        throw new Exception('没有需要更新的字段');
    }
    
    $updateFields[] = 'updated_at = NOW()';
    $params[] = $positionId;
    
    $sql = "UPDATE job_positions SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $db->execute($sql, $params);
    
    // 记录操作日志
    logSecurityEvent($currentUser['id'], 'job_position_updated', array(
        'position_id' => $positionId,
        'updated_fields' => array_keys($input)
    ));
    
    echo json_encode(array(
        'code' => 200,
        'message' => '职位更新成功'
    ));
}

// 删除职位
function handleDeleteJobPosition($input) {
    global $db, $currentUser, $auth;
    
    $positionId = isset($input['position_id']) ? intval($input['position_id']) : 0;
    
    if ($positionId <= 0) {
        throw new Exception('职位ID无效');
    }
    
    // 验证职位是否存在且属于当前用户
    $position = $db->fetch("
        SELECT id, position_name, is_system, created_by
        FROM job_positions
        WHERE id = ?
    ", array($positionId));
    
    if (!$position) {
        throw new Exception('职位不存在');
    }
    
    // 检查权限
    if ($position['is_system'] == 1) {
        throw new Exception('不能删除系统预设职位');
    }
    
    if ($position['created_by'] != $currentUser['id']) {
        throw new Exception('无权限删除此职位');
    }
    
    // 检查是否有员工在使用此职位
    $employeeCount = $db->fetch("
        SELECT COUNT(*) as count FROM users WHERE job_position_id = ?
    ", array($positionId))['count'];
    
    if ($employeeCount > 0) {
        throw new Exception("此职位下还有 {$employeeCount} 名员工，不能删除");
    }
    
    // 删除职位
    $db->execute("DELETE FROM job_positions WHERE id = ?", array($positionId));
    
    // 记录操作日志
    logSecurityEvent($currentUser['id'], 'job_position_deleted', array(
        'position_id' => $positionId,
        'position_name' => $position['position_name']
    ));
    
    echo json_encode(array(
        'code' => 200,
        'message' => '职位删除成功'
    ));
}

// 获取职位权限
function handleGetJobPositionPermissions($input) {
    global $db, $currentUser, $auth;
    
    $positionId = isset($input['position_id']) ? intval($input['position_id']) : 0;
    
    $position = null;
    $positionPermissionIds = array();
    
    // 如果positionId为0，表示是新建职位，只获取所有权限
    if ($positionId > 0) {
        // 验证职位是否存在且有权限访问
        $position = $db->fetch("
            SELECT id, position_name, is_system, created_by, user_type
            FROM job_positions
            WHERE id = ? AND ((created_by = ? OR is_system = 1) AND user_type = ?)
        ", array($positionId, $currentUser['id'], $currentUser['user_type']));
        
        if (!$position) {
            throw new Exception('职位不存在或无权限访问');
        }
        
        // 获取职位已有权限
        $positionPermissions = $db->fetchAll("
            SELECT permission_id
            FROM job_position_permissions
            WHERE job_position_id = ?
        ", array($positionId));
        
        $positionPermissionIds = array_column($positionPermissions, 'permission_id');
    }
    
    // 获取所有可用权限
    $allPermissions = $db->fetchAll("
        SELECT 
            id,
            permission_key,
            permission_name,
            description,
            module
        FROM permissions 
        WHERE user_type = ? OR user_type = 'all'
        ORDER BY module, permission_name
    ", array($currentUser['user_type']));
    

    
    // 按模块分组权限
    $permissionsByModule = array();
    foreach ($allPermissions as $permission) {
        $module = $permission['module'] ?: '其他';
        if (!isset($permissionsByModule[$module])) {
            $permissionsByModule[$module] = array();
        }
        $permissionsByModule[$module][] = $permission;
    }
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'position' => $position,
            'permissions' => $permissionsByModule,
            'position_permissions' => $positionPermissionIds
        )
    ));
}

// 更新职位权限
function handleUpdateJobPositionPermissions($input) {
    global $db, $currentUser, $auth;
    
    $positionId = isset($input['position_id']) ? intval($input['position_id']) : 0;
    $permissions = isset($input['permissions']) ? $input['permissions'] : array();
    
    if ($positionId <= 0) {
        throw new Exception('职位ID无效');
    }
    
    // 验证职位是否存在且有权限修改
    $position = $db->fetch("
        SELECT id, position_name, is_system, created_by
        FROM job_positions
        WHERE id = ?
    ", array($positionId));
    
    if (!$position) {
        throw new Exception('职位不存在');
    }
    
    // 检查权限：只能修改自己创建的职位
    if ($position['is_system'] == 1) {
        throw new Exception('不能修改系统预设职位的权限');
    }
    
    if ($position['created_by'] != $currentUser['id']) {
        throw new Exception('无权限修改此职位的权限');
    }
    
    // 验证权限ID的有效性
    $permissions = validatePermissionIds($permissions, $currentUser['user_type'], $db);
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 删除现有权限
        $db->execute("DELETE FROM job_position_permissions WHERE job_position_id = ?", array($positionId));
        
        // 添加新权限
        if (!empty($permissions)) {
            $values = array();
            $params = array();
            
            foreach ($permissions as $permissionId) {
                $values[] = '(?, ?)';
                $params[] = $positionId;
                $params[] = $permissionId;
            }
            
            $sql = "INSERT INTO job_position_permissions (job_position_id, permission_id) VALUES " . implode(', ', $values);
            $db->execute($sql, $params);
        }
        
        // 提交事务
        $db->commit();
        
        // 记录操作日志
        logSecurityEvent($currentUser['id'], 'job_position_permissions_updated', array(
            'position_id' => $positionId,
            'position_name' => $position['position_name'],
            'permission_count' => count($permissions)
        ));
        
        echo json_encode(array(
            'code' => 200,
            'message' => '职位权限更新成功'
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 创建职位并同时设置权限
function handleCreateJobPositionWithPermissions($input) {
    global $db, $currentUser, $auth;
    
    $name = isset($input['name']) ? trim($input['name']) : '';
    $positionName = isset($input['position_name']) ? trim($input['position_name']) : '';
    $description = isset($input['description']) ? trim($input['description']) : '';
    $permissions = isset($input['permissions']) ? $input['permissions'] : array();
    
    // 验证必填字段
    if (!$name || !$positionName) {
        throw new Exception('职位代码和显示名称不能为空');
    }
    
    // 检查职位代码是否已存在（同一用户类型下不能重复）
    $existingPosition = $db->fetch("
        SELECT id FROM job_positions 
        WHERE name = ? AND user_type = ? AND (created_by = ? OR is_system = 1)
    ", array($name, $currentUser['user_type'], $currentUser['id']));
    
    if ($existingPosition) {
        throw new Exception('职位代码已存在');
    }
    
    // 验证权限ID的有效性
    $permissions = validatePermissionIds($permissions, $currentUser['user_type'], $db);
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 创建职位
        $db->execute("
            INSERT INTO job_positions (
                name, position_name, description, user_type, 
                created_by, is_system, status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, 0, 'active', NOW(), NOW())
        ", array(
            $name, $positionName, $description, 
            $currentUser['user_type'], $currentUser['id']
        ));
        
        $positionId = $db->lastInsertId();
        
        // 添加权限
        if (!empty($permissions)) {
            $values = array();
            $params = array();
            
            foreach ($permissions as $permissionId) {
                $values[] = '(?, ?)';
                $params[] = $positionId;
                $params[] = $permissionId;
            }
            
            $sql = "INSERT INTO job_position_permissions (job_position_id, permission_id) VALUES " . implode(', ', $values);
            $db->execute($sql, $params);
        }
        
        // 提交事务
        $db->commit();
        
        // 记录操作日志
        logSecurityEvent($currentUser['id'], 'job_position_created_with_permissions', array(
            'position_id' => $positionId,
            'position_name' => $positionName,
            'permission_count' => count($permissions)
        ));
        
        echo json_encode(array(
            'code' => 200,
            'message' => '职位创建成功，已配置权限',
            'data' => array('position_id' => $positionId)
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 更新职位并同时设置权限
function handleUpdateJobPositionWithPermissions($input) {
    global $db, $currentUser, $auth;
    
    $positionId = isset($input['position_id']) ? intval($input['position_id']) : 0;
    $name = isset($input['name']) ? trim($input['name']) : '';
    $positionName = isset($input['position_name']) ? trim($input['position_name']) : '';
    $description = isset($input['description']) ? trim($input['description']) : '';
    $status = isset($input['status']) ? trim($input['status']) : '';
    $permissions = isset($input['permissions']) ? $input['permissions'] : array();
    
    if ($positionId <= 0) {
        throw new Exception('职位ID无效');
    }
    
    // 验证职位是否存在且属于当前用户
    $position = $db->fetch("
        SELECT id, name, position_name, is_system, created_by
        FROM job_positions
        WHERE id = ?
    ", array($positionId));
    
    if (!$position) {
        throw new Exception('职位不存在');
    }
    
    // 检查权限
    if ($position['is_system'] == 1) {
        throw new Exception('不能修改系统预设职位');
    }
    
    if ($position['created_by'] != $currentUser['id']) {
        throw new Exception('无权限修改此职位');
    }
    
    // 验证必填字段
    if (!$name || !$positionName) {
        throw new Exception('职位代码和显示名称不能为空');
    }
    
    // 检查职位代码是否与其他职位重复
    if ($name != $position['name']) {
        $existingPosition = $db->fetch("
            SELECT id FROM job_positions 
            WHERE name = ? AND user_type = ? AND id != ? AND (created_by = ? OR is_system = 1)
        ", array($name, $currentUser['user_type'], $positionId, $currentUser['id']));
        
        if ($existingPosition) {
            throw new Exception('职位代码已存在');
        }
    }
    
    // 验证权限ID的有效性
    $permissions = validatePermissionIds($permissions, $currentUser['user_type'], $db);
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 更新职位信息
        $db->execute("
            UPDATE job_positions 
            SET name = ?, position_name = ?, description = ?, status = ?, updated_at = NOW()
            WHERE id = ?
        ", array($name, $positionName, $description, $status, $positionId));
        
        // 更新权限：先删除现有权限
        $db->execute("DELETE FROM job_position_permissions WHERE job_position_id = ?", array($positionId));
        
        // 添加新权限
        if (!empty($permissions)) {
            $values = array();
            $params = array();
            
            foreach ($permissions as $permissionId) {
                $values[] = '(?, ?)';
                $params[] = $positionId;
                $params[] = $permissionId;
            }
            
            $sql = "INSERT INTO job_position_permissions (job_position_id, permission_id) VALUES " . implode(', ', $values);
            $db->execute($sql, $params);
        }
        
        // 提交事务
        $db->commit();
        
        // 记录操作日志
        logSecurityEvent($currentUser['id'], 'job_position_updated_with_permissions', array(
            'position_id' => $positionId,
            'position_name' => $positionName,
            'permission_count' => count($permissions)
        ));
        
        echo json_encode(array(
            'code' => 200,
            'message' => '职位更新成功，已配置权限'
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 验证权限ID数组的有效性
function validatePermissionIds($permissions, $userType, $db) {
    if (empty($permissions)) {
        return array();
    }
    
    // 确保权限数组中的值都是整数
    $permissions = array_filter(array_map('intval', $permissions));
    if (empty($permissions)) {
        return array();
    }
    
    $placeholders = implode(',', array_fill(0, count($permissions), '?'));
    $validPermissions = $db->fetchAll("
        SELECT id FROM permissions 
        WHERE id IN ($placeholders)
        AND (user_type = ? OR user_type = 'all')
    ", array_merge($permissions, array($userType)));
    
    $validPermissionIds = array_column($validPermissions, 'id');
    return array_intersect($permissions, $validPermissionIds);
}

// 获取支付请求详情
function handlePaymentRequestDetail() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'view_transactions')) {
        throw new Exception('无权限访问');
    }
    
    $requestId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    if (!$requestId) {
        throw new Exception('请求ID不能为空');
    }
    
    $whereConditions = array("pr.id = ?");
    $params = array($requestId);
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "pr.merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    }
    
    $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
    
    $request = $db->fetch(
        "SELECT pr.*, 
                m.company_name as merchant_name,
                u.real_name as merchant_real_name,
                p.name as product_name,
                a.account_name as alipay_account_name,
                a.account_number as alipay_account_number,
                pr.request_id as out_trade_no,
                pr.merchant_order_no as order_id,
                'alipay' as payment_method,
                pr.fee as service_fee
         FROM payment_requests pr
         LEFT JOIN merchants m ON pr.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         LEFT JOIN products p ON pr.product_id = p.id
         LEFT JOIN alipay_accounts a ON pr.alipay_account_id = a.id
         $whereClause",
        $params
    );
    
    if (!$request) {
        throw new Exception('支付请求不存在');
    }
    
    echo json_encode(array(
        'code' => 200,
        'data' => $request
    ));
}

// 更新支付状态
function handleUpdatePaymentStatus() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_transactions')) {
        throw new Exception('无权限操作');
    }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $requestId = isset($input['request_id']) ? intval($input['request_id']) : 0;
    $newStatus = isset($input['status']) ? $input['status'] : '';
    $remark = isset($input['remark']) ? $input['remark'] : '';
    
    if (!$requestId || !$newStatus) {
        throw new Exception('请求ID和状态不能为空');
    }
    
    $allowedStatuses = array('success', 'failed', 'cancelled', 'paid');
    if (!in_array($newStatus, $allowedStatuses)) {
        throw new Exception('无效的状态值');
    }
    
    // 检查请求是否存在
    $whereConditions = array("id = ?");
    $params = array($requestId);
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    }
    
    $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
    
    $request = $db->fetch("SELECT * FROM payment_requests $whereClause", $params);
    
    if (!$request) {
        throw new Exception('支付请求不存在');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 更新状态
        $updateData = array(
            'status' => $newStatus,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        if ($newStatus === 'success' || $newStatus === 'paid') {
            $updateData['paid_at'] = date('Y-m-d H:i:s');
        }
        
        if (!empty($remark)) {
            $updateData['remark'] = $remark;
        }
        
        $setParts = array();
        $updateParams = array();
        foreach ($updateData as $field => $value) {
            $setParts[] = "$field = ?";
            $updateParams[] = $value;
        }
        $updateParams[] = $requestId;
        
        if ($currentUser['user_type'] === 'merchant') {
            $updateParams[] = $currentUser['profile_id'];
            $db->query(
                "UPDATE payment_requests SET " . implode(', ', $setParts) . " WHERE id = ? AND merchant_id = ?",
                $updateParams
            );
        } else {
            $db->query(
                "UPDATE payment_requests SET " . implode(', ', $setParts) . " WHERE id = ?",
                $updateParams
            );
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '支付状态更新成功'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 重试支付请求
function handleRetryPaymentRequest() {
    global $db, $currentUser, $auth;
    
    if (!$auth->hasPermission($currentUser, 'manage_transactions')) {
        throw new Exception('无权限操作');
    }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $requestId = isset($input['request_id']) ? intval($input['request_id']) : 0;
    
    if (!$requestId) {
        throw new Exception('请求ID不能为空');
    }
    
    // 检查请求是否存在
    $whereConditions = array("id = ?");
    $params = array($requestId);
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    }
    
    $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
    
    $request = $db->fetch("SELECT * FROM payment_requests $whereClause", $params);
    
    if (!$request) {
        throw new Exception('支付请求不存在');
    }
    
    if ($request['status'] !== 'failed') {
        throw new Exception('只能重试失败的支付请求');
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 重置状态为pending
        $updateParams = array('pending', date('Y-m-d H:i:s'), $requestId);
        
        if ($currentUser['user_type'] === 'merchant') {
            $updateParams[] = $currentUser['profile_id'];
            $db->query(
                "UPDATE payment_requests SET status = ?, updated_at = ? WHERE id = ? AND merchant_id = ?",
                $updateParams
            );
        } else {
            $db->query(
                "UPDATE payment_requests SET status = ?, updated_at = ? WHERE id = ?",
                $updateParams
            );
        }
        
        $db->query("COMMIT");
        
        echo json_encode(array(
            'code' => 200,
            'message' => '支付请求重试成功，状态已重置为待处理'
        ));
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 获取风控事件
function handleGetRiskEvents() {
    global $db, $currentUser;
    
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 50);
    
    // 获取最近24小时的风控事件
    $recentEvents = $db->fetch(
        "SELECT COUNT(*) as count 
         FROM risk_events 
         WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)"
    );
    
    // 获取总拦截交易数
    $totalBlocked = $db->fetch(
        "SELECT COUNT(*) as count 
         FROM risk_events 
         WHERE action = 'blocked'"
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'recent_events' => $recentEvents['count'],
            'total_blocked' => $totalBlocked['count']
        )
    ));
}

// 生成风控报告
function handleGenerateRiskReport() {
    global $db, $currentUser;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    }
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE merchant_id = ?';
        $params[] = $merchantId;
    }
    
    // 获取总体统计
    $summary = $db->fetch(
        "SELECT 
            COUNT(CASE WHEN action = 'blocked' THEN 1 END) as total_blocked,
            COUNT(*) as total_triggered,
            SUM(CASE WHEN action = 'blocked' THEN amount ELSE 0 END) as prevented_loss,
            ROUND(COUNT(CASE WHEN action = 'blocked' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as efficiency_rate
         FROM risk_events re $whereClause",
        $params
    );
    
    // 获取每日统计
    $dailyStats = $db->fetchAll(
        "SELECT 
            DATE(re.created_at) as date,
            COUNT(*) as triggered_count,
            COUNT(CASE WHEN re.action = 'blocked' THEN 1 END) as blocked_count,
            SUM(CASE WHEN re.action = 'blocked' THEN re.amount ELSE 0 END) as blocked_amount,
            ROUND(COUNT(CASE WHEN re.action = 'blocked' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as block_rate
         FROM risk_events re $whereClause
         GROUP BY DATE(re.created_at)
         ORDER BY DATE(re.created_at) DESC
         LIMIT 30",
        $params
    );
    
    echo json_encode(array(
        'code' => 200,
        'data' => array(
            'total_blocked' => $summary['total_blocked'],
            'total_triggered' => $summary['total_triggered'],
            'prevented_loss' => $summary['prevented_loss'],
            'efficiency_rate' => $summary['efficiency_rate'],
            'daily_stats' => $dailyStats
        )
    ));
}

// 导出风控报告
function handleExportRiskReport() {
    global $db, $currentUser;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    }
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE merchant_id = ?';
        $params[] = $merchantId;
    }
    
    // 获取风控事件数据
    $riskEvents = $db->fetchAll(
        "SELECT 
            DATE(re.created_at) as date,
            COUNT(*) as triggered_count,
            COUNT(CASE WHEN re.action = 'blocked' THEN 1 END) as blocked_count,
            SUM(CASE WHEN re.action = 'blocked' THEN re.amount ELSE 0 END) as blocked_amount,
            ROUND(COUNT(CASE WHEN re.action = 'blocked' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as block_rate,
            AVG(re.amount) as avg_amount
         FROM risk_events re $whereClause
         GROUP BY DATE(re.created_at)
         ORDER BY DATE(re.created_at) DESC",
        $params
    );
    
    $filename = 'risk_report_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=' . $filename);
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $output = fopen('php://output', 'w');
    
    // 输出BOM以支持中文
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // 输出标题行
    $headers = array(
        '日期', '触发次数', '拦截次数', '拦截金额', '拦截率(%)', '平均金额'
    );
    fputcsv($output, $headers);
    
    // 输出数据行
    foreach ($riskEvents as $row) {
        $csvRow = array(
            $row['date'],
            $row['triggered_count'],
            $row['blocked_count'],
            number_format($row['blocked_amount'], 2),
            $row['block_rate'],
            number_format($row['avg_amount'], 2)
        );
        fputcsv($output, $csvRow);
    }
    
    fclose($output);
    exit();
}

// 导出黑名单
function handleExportBlacklist() {
    global $db, $currentUser;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    }
    
    $whereClause = '';
    $params = [];
    
    if ($merchantId > 0) {
        $whereClause = 'WHERE merchant_id = ?';
        $params[] = $merchantId;
    }
    
    // 获取黑名单数据
    $blacklist = $db->fetchAll(
        "SELECT blacklist_type, blacklist_value, reason, created_at 
         FROM blacklist $whereClause
         ORDER BY created_at DESC",
        $params
    );
    
    $filename = 'blacklist_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename=' . $filename);
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $output = fopen('php://output', 'w');
    
    // 输出BOM以支持中文
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // 输出标题行
    $headers = array('类型', '值', '原因', '添加时间');
    fputcsv($output, $headers);
    
    // 输出数据行
    foreach ($blacklist as $row) {
        $typeText = '';
        switch($row['blacklist_type']) {
            case 'ip': $typeText = 'IP地址'; break;
            case 'phone': $typeText = '手机号'; break;
            case 'email': $typeText = '邮箱'; break;
            case 'device': 
            case 'device_id': $typeText = '设备ID'; break;
            case 'user_agent': $typeText = '用户代理'; break;
            default: $typeText = $row['blacklist_type'];
        }
        
        $csvRow = array(
            $typeText,
            $row['blacklist_value'],
            $row['reason'] ?: '无',
            $row['created_at']
        );
        fputcsv($output, $csvRow);
    }
    
    fclose($output);
    exit();
}

// ==================== 性能监控系统 ====================

// 性能监控主函数
function handlePerformanceMonitor() {
    global $method;
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['sub_action'])) {
                switch ($_GET['sub_action']) {
                    case 'system_stats':
                        handleGetSystemStats();
                        break;
                    case 'database_performance':
                        handleGetDatabasePerformance();
                        break;
                    case 'api_performance':
                        handleGetApiPerformance();
                        break;
                    case 'slow_queries':
                        handleGetSlowQueries();
                        break;
                    case 'cache_stats':
                        handleGetCacheStats();
                        break;
                    case 'export_performance_report':
                        handleExportPerformanceReport();
                        break;
                    default:
                        handleGetPerformanceOverview();
                        break;
                }
            } else {
                handleGetPerformanceOverview();
            }
            break;
        case 'POST':
            if (isset($_GET['sub_action'])) {
                switch ($_GET['sub_action']) {
                    case 'clear_cache':
                        handleClearCache();
                        break;
                    case 'optimize_database':
                        handleOptimizeDatabase();
                        break;
                    default:
                        http_response_code(404);
                        echo json_encode(array('code' => 404, 'message' => '操作不存在'));
                        break;
                }
            }
            break;
        default:
            http_response_code(405);
            echo json_encode(array('code' => 405, 'message' => '不支持的请求方法'));
            break;
    }
}

// 获取性能概览
function handleGetPerformanceOverview() {
    global $db, $currentUser;
    
    try {
        // 系统基本信息
        $systemInfo = array(
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'current_memory_usage' => formatBytes(memory_get_usage(true)),
            'peak_memory_usage' => formatBytes(memory_get_peak_usage(true)),
            'server_time' => date('Y-m-d H:i:s'),
            'uptime' => getServerUptime()
        );
        
        // 数据库连接信息
        $dbInfo = array(
            'version' => $db->fetch("SELECT VERSION() as version")['version'],
            'connections' => $db->fetch("SHOW STATUS LIKE 'Threads_connected'")['Value'],
            'max_connections' => $db->fetch("SHOW VARIABLES LIKE 'max_connections'")['Value'],
            'queries_per_second' => $db->fetch("SHOW STATUS LIKE 'Queries'")['Value']
        );
        
        // 今日API调用统计
        $apiStats = getApiCallStats();
        
        // 慢查询统计
        $slowQueryCount = $db->fetch("SHOW STATUS LIKE 'Slow_queries'")['Value'];
        
        // 缓存命中率（模拟数据，实际项目中应该从Redis等缓存系统获取）
        $cacheStats = array(
            'hit_rate' => 85.6,
            'total_requests' => 12450,
            'cache_hits' => 10657,
            'cache_misses' => 1793
        );
        
        echo json_encode(array(
            'code' => 200,
            'data' => array(
                'system_info' => $systemInfo,
                'database_info' => $dbInfo,
                'api_stats' => $apiStats,
                'slow_query_count' => $slowQueryCount,
                'cache_stats' => $cacheStats,
                'performance_score' => calculatePerformanceScore($dbInfo, $cacheStats, $slowQueryCount)
            )
        ));
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(array(
            'code' => 500,
            'message' => '获取性能数据失败: ' . $e->getMessage()
        ));
    }
}

// 获取系统统计信息
function handleGetSystemStats() {
    global $db;
    
    try {
        // 获取各表的记录数量
        $tableStats = array();
        $tables = array(
            'users' => '用户',
            'merchants' => '商户',
            'payment_requests' => '支付请求',
            'products' => '产品',
            'transactions' => '交易记录',
            'risk_events' => '风控事件',
            'blacklist' => '黑名单'
        );
        
        foreach ($tables as $table => $name) {
            try {
                $count = $db->fetch("SELECT COUNT(*) as count FROM `$table`")['count'];
                $tableStats[] = array(
                    'table_name' => $table,
                    'display_name' => $name,
                    'record_count' => $count
                );
            } catch (Exception $e) {
                // 表不存在时跳过
                continue;
            }
        }
        
        // 获取今日新增数据统计
        $todayStats = array();
        $today = date('Y-m-d');
        
        foreach ($tables as $table => $name) {
            try {
                $count = $db->fetch("SELECT COUNT(*) as count FROM `$table` WHERE DATE(created_at) = ?", array($today))['count'];
                $todayStats[] = array(
                    'table_name' => $table,
                    'display_name' => $name,
                    'today_count' => $count
                );
            } catch (Exception $e) {
                continue;
            }
        }
        
        // 磁盘使用情况
        $diskInfo = array(
            'total_space' => formatBytes(disk_total_space('.')),
            'free_space' => formatBytes(disk_free_space('.')),
            'used_space' => formatBytes(disk_total_space('.') - disk_free_space('.')),
            'usage_percentage' => round((disk_total_space('.') - disk_free_space('.')) / disk_total_space('.') * 100, 2)
        );
        
        echo json_encode(array(
            'code' => 200,
            'data' => array(
                'table_stats' => $tableStats,
                'today_stats' => $todayStats,
                'disk_info' => $diskInfo
            )
        ));
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(array(
            'code' => 500,
            'message' => '获取系统统计失败: ' . $e->getMessage()
        ));
    }
}

// 获取数据库性能信息
function handleGetDatabasePerformance() {
    global $db;
    
    try {
        // 获取数据库状态信息
        $dbStatus = array();
        $statusVars = array(
            'Uptime' => '运行时间(秒)',
            'Queries' => '总查询数',
            'Slow_queries' => '慢查询数',
            'Connections' => '连接数',
            'Aborted_connects' => '中断连接数',
            'Bytes_received' => '接收字节数',
            'Bytes_sent' => '发送字节数',
            'Com_select' => 'SELECT查询数',
            'Com_insert' => 'INSERT查询数',
            'Com_update' => 'UPDATE查询数',
            'Com_delete' => 'DELETE查询数'
        );
        
        foreach ($statusVars as $var => $desc) {
            try {
                $result = $db->fetch("SHOW STATUS LIKE '$var'");
                if ($result) {
                    $value = $result['Value'];
                    // 格式化字节数
                    if (strpos($var, 'Bytes') !== false) {
                        $value = formatBytes($value);
                    }
                    $dbStatus[] = array(
                        'variable' => $var,
                        'description' => $desc,
                        'value' => $value
                    );
                }
            } catch (Exception $e) {
                continue;
            }
        }
        
        // 获取表大小信息
        $tableSizes = $db->fetchAll("
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                table_rows,
                ROUND((data_length / 1024 / 1024), 2) AS data_mb,
                ROUND((index_length / 1024 / 1024), 2) AS index_mb
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
            ORDER BY (data_length + index_length) DESC
            LIMIT 20
        ");
        
        // 计算查询性能指标
        $uptime = $db->fetch("SHOW STATUS LIKE 'Uptime'")['Value'];
        $queries = $db->fetch("SHOW STATUS LIKE 'Queries'")['Value'];
        $slowQueries = $db->fetch("SHOW STATUS LIKE 'Slow_queries'")['Value'];
        
        $performanceMetrics = array(
            'queries_per_second' => round($queries / $uptime, 2),
            'slow_query_percentage' => $queries > 0 ? round($slowQueries / $queries * 100, 2) : 0,
            'avg_query_time' => $queries > 0 ? round($uptime / $queries * 1000, 2) : 0 // 毫秒
        );
        
        echo json_encode(array(
            'code' => 200,
            'data' => array(
                'database_status' => $dbStatus,
                'table_sizes' => $tableSizes,
                'performance_metrics' => $performanceMetrics
            )
        ));
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(array(
            'code' => 500,
            'message' => '获取数据库性能信息失败: ' . $e->getMessage()
        ));
    }
}

// 获取API性能统计
function handleGetApiPerformance() {
    try {
        // 模拟API性能数据（实际项目中应该从日志或监控系统获取）
        $apiStats = array(
            array(
                'endpoint' => '/api/admin.php?action=payment_requests',
                'method' => 'GET',
                'avg_response_time' => 245,
                'total_calls' => 1250,
                'error_rate' => 2.1,
                'last_24h_calls' => 89
            ),
            array(
                'endpoint' => '/api/admin.php?action=financial',
                'method' => 'GET',
                'avg_response_time' => 180,
                'total_calls' => 980,
                'error_rate' => 1.5,
                'last_24h_calls' => 67
            ),
            array(
                'endpoint' => '/api/admin.php?action=products',
                'method' => 'GET',
                'avg_response_time' => 120,
                'total_calls' => 2100,
                'error_rate' => 0.8,
                'last_24h_calls' => 156
            ),
            array(
                'endpoint' => '/api/admin.php?action=risk_control',
                'method' => 'GET',
                'avg_response_time' => 310,
                'total_calls' => 450,
                'error_rate' => 3.2,
                'last_24h_calls' => 23
            ),
            array(
                'endpoint' => '/api/admin.php?action=merchants',
                'method' => 'POST',
                'avg_response_time' => 890,
                'total_calls' => 145,
                'error_rate' => 5.5,
                'last_24h_calls' => 8
            )
        );
        
        // 响应时间分布
        $responseTimeDistribution = array(
            '0-100ms' => 35,
            '100-200ms' => 28,
            '200-500ms' => 22,
            '500ms-1s' => 10,
            '1s+' => 5
        );
        
        // 错误统计
        $errorStats = array(
            'total_errors' => 89,
            'error_types' => array(
                '400 Bad Request' => 23,
                '401 Unauthorized' => 12,
                '403 Forbidden' => 8,
                '404 Not Found' => 15,
                '500 Internal Server Error' => 31
            )
        );
        
        echo json_encode(array(
            'code' => 200,
            'data' => array(
                'api_endpoints' => $apiStats,
                'response_time_distribution' => $responseTimeDistribution,
                'error_stats' => $errorStats
            )
        ));
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(array(
            'code' => 500,
            'message' => '获取API性能统计失败: ' . $e->getMessage()
        ));
    }
}

// 获取慢查询信息
function handleGetSlowQueries() {
    global $db;
    
    try {
        // 获取慢查询日志状态
        $slowQueryLog = $db->fetch("SHOW VARIABLES LIKE 'slow_query_log'");
        $longQueryTime = $db->fetch("SHOW VARIABLES LIKE 'long_query_time'");
        $slowQueryCount = $db->fetch("SHOW STATUS LIKE 'Slow_queries'");
        
        $slowQueryInfo = array(
            'slow_query_log_enabled' => $slowQueryLog['Value'] === 'ON',
            'long_query_time' => $longQueryTime['Value'],
            'slow_query_count' => $slowQueryCount['Value']
        );
        
        // 模拟慢查询数据（实际项目中应该从慢查询日志文件解析）
        $slowQueries = array(
            array(
                'query_time' => 2.34,
                'lock_time' => 0.01,
                'rows_sent' => 1250,
                'rows_examined' => 45000,
                'query' => 'SELECT * FROM payment_requests pr LEFT JOIN merchants m ON pr.merchant_id = m.id WHERE pr.created_at >= \'2024-01-01\' ORDER BY pr.created_at DESC',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            ),
            array(
                'query_time' => 1.89,
                'lock_time' => 0.05,
                'rows_sent' => 890,
                'rows_examined' => 23000,
                'query' => 'SELECT COUNT(*) as total, SUM(amount) as total_amount FROM transactions WHERE status = \'completed\' AND created_at BETWEEN \'2024-01-01\' AND \'2024-12-31\'',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-4 hours'))
            ),
            array(
                'query_time' => 3.12,
                'lock_time' => 0.02,
                'rows_sent' => 5,
                'rows_examined' => 67000,
                'query' => 'UPDATE payment_requests SET status = \'completed\' WHERE merchant_id IN (SELECT id FROM merchants WHERE status = \'active\')',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-6 hours'))
            )
        );
        
        echo json_encode(array(
            'code' => 200,
            'data' => array(
                'slow_query_info' => $slowQueryInfo,
                'slow_queries' => $slowQueries
            )
        ));
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(array(
            'code' => 500,
            'message' => '获取慢查询信息失败: ' . $e->getMessage()
        ));
    }
}

// 获取缓存统计
function handleGetCacheStats() {
    try {
        // 模拟缓存统计数据（实际项目中应该从Redis等缓存系统获取）
        $cacheStats = array(
            'total_keys' => 1250,
            'memory_usage' => '45.2MB',
            'hit_rate' => 87.3,
            'miss_rate' => 12.7,
            'evicted_keys' => 23,
            'expired_keys' => 156,
            'keyspace_hits' => 12450,
            'keyspace_misses' => 1793
        );
        
        // 缓存分类统计
        $cacheCategories = array(
            array(
                'category' => 'user_sessions',
                'key_count' => 450,
                'memory_usage' => '12.3MB',
                'hit_rate' => 92.1
            ),
            array(
                'category' => 'api_responses',
                'key_count' => 320,
                'memory_usage' => '18.7MB',
                'hit_rate' => 85.6
            ),
            array(
                'category' => 'database_queries',
                'key_count' => 280,
                'memory_usage' => '8.9MB',
                'hit_rate' => 78.4
            ),
            array(
                'category' => 'static_data',
                'key_count' => 200,
                'memory_usage' => '5.3MB',
                'hit_rate' => 95.2
            )
        );
        
        // 热门缓存键
        $topKeys = array(
            array(
                'key' => 'merchant_list_active',
                'hits' => 2340,
                'size' => '234KB',
                'ttl' => 3600
            ),
            array(
                'key' => 'product_categories',
                'hits' => 1890,
                'size' => '45KB',
                'ttl' => 7200
            ),
            array(
                'key' => 'risk_config_global',
                'hits' => 1567,
                'size' => '12KB',
                'ttl' => 1800
            )
        );
        
        echo json_encode(array(
            'code' => 200,
            'data' => array(
                'cache_stats' => $cacheStats,
                'cache_categories' => $cacheCategories,
                'top_keys' => $topKeys
            )
        ));
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(array(
            'code' => 500,
            'message' => '获取缓存统计失败: ' . $e->getMessage()
        ));
    }
}

// 清理缓存
function handleClearCache() {
    try {
        // 这里应该实现实际的缓存清理逻辑
        // 例如：Redis::flushAll() 或者删除文件缓存等
        
        // 模拟清理结果
        $result = array(
            'cleared_keys' => 1250,
            'freed_memory' => '45.2MB',
            'clear_time' => date('Y-m-d H:i:s')
        );
        
        echo json_encode(array(
            'code' => 200,
            'message' => '缓存清理完成',
            'data' => $result
        ));
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(array(
            'code' => 500,
            'message' => '缓存清理失败: ' . $e->getMessage()
        ));
    }
}

// 数据库优化
function handleOptimizeDatabase() {
    global $db;
    
    try {
        $optimizeResults = array();
        
        // 获取需要优化的表
        $tables = $db->fetchAll("SHOW TABLES");
        
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            try {
                // 优化表
                $result = $db->fetch("OPTIMIZE TABLE `$tableName`");
                $optimizeResults[] = array(
                    'table' => $tableName,
                    'status' => 'success',
                    'msg_type' => isset($result['Msg_type']) ? $result['Msg_type'] : 'status',
                    'msg_text' => isset($result['Msg_text']) ? $result['Msg_text'] : 'OK'
                );
            } catch (Exception $e) {
                $optimizeResults[] = array(
                    'table' => $tableName,
                    'status' => 'error',
                    'msg_type' => 'error',
                    'msg_text' => $e->getMessage()
                );
            }
        }
        
        echo json_encode(array(
            'code' => 200,
            'message' => '数据库优化完成',
            'data' => array(
                'optimize_results' => $optimizeResults,
                'optimize_time' => date('Y-m-d H:i:s')
            )
        ));
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(array(
            'code' => 500,
            'message' => '数据库优化失败: ' . $e->getMessage()
        ));
    }
}

// 导出性能报告
function handleExportPerformanceReport() {
    global $db;
    
    try {
        $filename = 'performance_report_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');
        
        $output = fopen('php://output', 'w');
        
        // 输出BOM以支持中文
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // 系统信息部分
        fputcsv($output, array('=== 系统信息 ==='));
        fputcsv($output, array('项目', '值'));
        fputcsv($output, array('PHP版本', PHP_VERSION));
        fputcsv($output, array('内存限制', ini_get('memory_limit')));
        fputcsv($output, array('当前内存使用', formatBytes(memory_get_usage(true))));
        fputcsv($output, array('峰值内存使用', formatBytes(memory_get_peak_usage(true))));
        fputcsv($output, array('服务器时间', date('Y-m-d H:i:s')));
        fputcsv($output, array(''));
        
        // 数据库信息部分
        fputcsv($output, array('=== 数据库信息 ==='));
        fputcsv($output, array('项目', '值'));
        
        try {
            $dbVersion = $db->fetch("SELECT VERSION() as version")['version'];
            $connections = $db->fetch("SHOW STATUS LIKE 'Threads_connected'")['Value'];
            $maxConnections = $db->fetch("SHOW VARIABLES LIKE 'max_connections'")['Value'];
            $slowQueries = $db->fetch("SHOW STATUS LIKE 'Slow_queries'")['Value'];
            
            fputcsv($output, array('MySQL版本', $dbVersion));
            fputcsv($output, array('当前连接数', $connections));
            fputcsv($output, array('最大连接数', $maxConnections));
            fputcsv($output, array('慢查询数量', $slowQueries));
        } catch (Exception $e) {
            fputcsv($output, array('数据库信息获取失败', $e->getMessage()));
        }
        
        fputcsv($output, array(''));
        
        // 表大小信息
        fputcsv($output, array('=== 数据表大小 ==='));
        fputcsv($output, array('表名', '大小(MB)', '记录数', '数据大小(MB)', '索引大小(MB)'));
        
        try {
            $tableSizes = $db->fetchAll("
                SELECT 
                    table_name,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                    table_rows,
                    ROUND((data_length / 1024 / 1024), 2) AS data_mb,
                    ROUND((index_length / 1024 / 1024), 2) AS index_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                ORDER BY (data_length + index_length) DESC
            ");
            
            foreach ($tableSizes as $table) {
                fputcsv($output, array(
                    $table['table_name'],
                    $table['size_mb'],
                    $table['table_rows'],
                    $table['data_mb'],
                    $table['index_mb']
                ));
            }
        } catch (Exception $e) {
            fputcsv($output, array('表大小信息获取失败', $e->getMessage()));
        }
        
        fclose($output);
        exit();
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(array(
            'code' => 500,
            'message' => '导出性能报告失败: ' . $e->getMessage()
        ));
    }
}

// ==================== 辅助函数 ====================

// 格式化字节数
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// 获取服务器运行时间
function getServerUptime() {
    if (function_exists('sys_getloadavg') && file_exists('/proc/uptime')) {
        $uptime = file_get_contents('/proc/uptime');
        $uptime = explode(' ', $uptime);
        $seconds = intval($uptime[0]);
        
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        
        return "{$days}天 {$hours}小时 {$minutes}分钟";
    }
    
    return '无法获取';
}

// 获取API调用统计
function getApiCallStats() {
    // 模拟数据（实际项目中应该从日志或数据库获取）
    return array(
        'today_calls' => 1250,
        'yesterday_calls' => 1180,
        'this_week_calls' => 8760,
        'this_month_calls' => 35420,
        'avg_response_time' => 245, // 毫秒
        'error_rate' => 2.3 // 百分比
    );
}

// 计算性能评分
function calculatePerformanceScore($dbInfo, $cacheStats, $slowQueryCount) {
    $score = 100;
    
    // 数据库连接使用率影响（超过80%扣分）
    $connectionUsage = ($dbInfo['connections'] / $dbInfo['max_connections']) * 100;
    if ($connectionUsage > 80) {
        $score -= ($connectionUsage - 80) * 2;
    }
    
    // 缓存命中率影响（低于85%扣分）
    if ($cacheStats['hit_rate'] < 85) {
        $score -= (85 - $cacheStats['hit_rate']) * 2;
    }
    
    // 慢查询影响
    if ($slowQueryCount > 10) {
        $score -= min(($slowQueryCount - 10) * 5, 30);
    }
    
    // 内存使用率影响
    $memoryUsage = (memory_get_usage(true) / (ini_get('memory_limit') * 1024 * 1024)) * 100;
    if ($memoryUsage > 80) {
        $score -= ($memoryUsage - 80) * 1.5;
    }
    
    return max(0, min(100, round($score, 1)));
}

?> 