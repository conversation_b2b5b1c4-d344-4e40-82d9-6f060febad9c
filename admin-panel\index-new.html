<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal管理系统</title>
    
    <!-- Bootstrap 5 CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        /* 全局样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            color: #212529;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            height: 100vh;
            background: white;
            border-right: 1px solid #e9ecef;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 1.5rem 1rem;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }
        
        .sidebar-header h4 {
            margin: 0;
            color: #495057;
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .sidebar-menu {
            padding: 1rem 0;
        }
        
        .menu-item {
            display: block;
            padding: 0.75rem 1.5rem;
            color: #495057;
            text-decoration: none;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            transition: all 0.2s;
        }
        
        .menu-item:hover {
            background-color: #f8f9fa;
            color: #0d6efd;
        }
        
        .menu-item.active {
            background-color: #0d6efd;
            color: white;
        }
        
        .menu-item i {
            margin-right: 0.5rem;
            width: 1rem;
        }
        
        /* 主内容区域 */
        .main-content {
            margin-left: 250px;
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 1rem 1.5rem;
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .content-area {
            padding: 1.5rem;
        }
        
        /* 卡片样式 */
        .card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 1rem 1.25rem;
            font-weight: 600;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        /* 统计卡片 */
        .stats-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.25rem;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #0d6efd;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        /* 表格样式 */
        .table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            font-weight: 600;
            color: #495057;
        }
        
        /* 按钮样式 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            padding: 0.5rem 1rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 加载动画 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0d6efd;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4><i class="bi-credit-card"></i> PayPal管理</h4>
        </div>
        <div class="sidebar-menu">
            <a href="#dashboard" class="menu-item active" data-page="dashboard">
                <i class="bi-speedometer2"></i> 仪表板
            </a>
            <a href="#merchants" class="menu-item" data-page="merchants">
                <i class="bi-shop"></i> 商户管理
            </a>
            <a href="#transactions" class="menu-item" data-page="transactions">
                <i class="bi-credit-card"></i> 交易记录
            </a>
            <a href="#users" class="menu-item" data-page="users">
                <i class="bi-people"></i> 用户管理
            </a>
            <a href="#settings" class="menu-item" data-page="settings">
                <i class="bi-gear"></i> 系统设置
            </a>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航 -->
        <div class="top-navbar">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <button class="btn btn-outline-secondary d-md-none" id="sidebarToggle">
                        <i class="bi-list"></i>
                    </button>
                    <span class="ms-2 fw-bold" id="pageTitle">仪表板</span>
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi-person-circle"></i> 管理员
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#profile"><i class="bi-person"></i> 个人资料</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#logout"><i class="bi-box-arrow-right"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area" id="contentArea">
            <!-- 默认显示仪表板 -->
            <div id="dashboard-content">
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="stats-number">156</div>
                            <div class="stats-label">总商户数</div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="stats-number">2,847</div>
                            <div class="stats-label">今日交易</div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="stats-number">¥1.2M</div>
                            <div class="stats-label">交易金额</div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="stats-number">98.5%</div>
                            <div class="stats-label">成功率</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">最近交易</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>交易ID</th>
                                        <th>商户</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>#TXN001</td>
                                        <td>测试商户A</td>
                                        <td>¥299.00</td>
                                        <td><span class="badge bg-success">成功</span></td>
                                        <td>2024-06-28 14:30</td>
                                    </tr>
                                    <tr>
                                        <td>#TXN002</td>
                                        <td>测试商户B</td>
                                        <td>¥156.50</td>
                                        <td><span class="badge bg-success">成功</span></td>
                                        <td>2024-06-28 14:25</td>
                                    </tr>
                                    <tr>
                                        <td>#TXN003</td>
                                        <td>测试商户C</td>
                                        <td>¥89.99</td>
                                        <td><span class="badge bg-warning">处理中</span></td>
                                        <td>2024-06-28 14:20</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他页面内容将通过JavaScript动态加载 -->
            <div id="dynamic-content" style="display: none;">
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 简单的页面管理
        class PayPalAdmin {
            constructor() {
                this.currentPage = 'dashboard';
                this.init();
            }

            init() {
                // 绑定菜单点击事件
                document.querySelectorAll('.menu-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const page = item.dataset.page;
                        this.navigateTo(page);
                    });
                });

                // 绑定侧边栏切换
                const sidebarToggle = document.getElementById('sidebarToggle');
                if (sidebarToggle) {
                    sidebarToggle.addEventListener('click', () => {
                        document.getElementById('sidebar').classList.toggle('show');
                    });
                }

                console.log('PayPal管理系统已初始化');
            }

            navigateTo(page) {
                // 更新菜单状态
                document.querySelectorAll('.menu-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-page="${page}"]`).classList.add('active');

                // 更新页面标题
                const titles = {
                    dashboard: '仪表板',
                    merchants: '商户管理',
                    transactions: '交易记录',
                    users: '用户管理',
                    settings: '系统设置'
                };
                document.getElementById('pageTitle').textContent = titles[page] || page;

                // 加载页面内容
                this.loadPageContent(page);
                this.currentPage = page;
            }

            loadPageContent(page) {
                const dashboardContent = document.getElementById('dashboard-content');
                const dynamicContent = document.getElementById('dynamic-content');

                if (page === 'dashboard') {
                    dashboardContent.style.display = 'block';
                    dynamicContent.style.display = 'none';
                } else {
                    dashboardContent.style.display = 'none';
                    dynamicContent.style.display = 'block';
                    
                    // 这里可以加载具体的页面内容
                    setTimeout(() => {
                        this.renderPageContent(page);
                    }, 500);
                }
            }

            renderPageContent(page) {
                const content = document.getElementById('dynamic-content');
                
                const pageContents = {
                    merchants: this.getMerchantsContent(),
                    transactions: this.getTransactionsContent(),
                    users: this.getUsersContent(),
                    settings: this.getSettingsContent()
                };

                content.innerHTML = pageContents[page] || '<p>页面内容加载中...</p>';
            }

            getMerchantsContent() {
                return `
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">商户管理</h5>
                            <button class="btn btn-primary btn-sm">
                                <i class="bi-plus"></i> 添加商户
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>商户ID</th>
                                            <th>商户名称</th>
                                            <th>状态</th>
                                            <th>注册时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>M001</td>
                                            <td>测试商户A</td>
                                            <td><span class="badge bg-success">活跃</span></td>
                                            <td>2024-06-01</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary">编辑</button>
                                                <button class="btn btn-sm btn-outline-danger">禁用</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>M002</td>
                                            <td>测试商户B</td>
                                            <td><span class="badge bg-success">活跃</span></td>
                                            <td>2024-06-15</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary">编辑</button>
                                                <button class="btn btn-sm btn-outline-danger">禁用</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }

            getTransactionsContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">交易记录</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <input type="date" class="form-control" placeholder="开始日期">
                                </div>
                                <div class="col-md-3">
                                    <input type="date" class="form-control" placeholder="结束日期">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select">
                                        <option>全部状态</option>
                                        <option>成功</option>
                                        <option>失败</option>
                                        <option>处理中</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-primary">查询</button>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>交易ID</th>
                                            <th>商户</th>
                                            <th>金额</th>
                                            <th>手续费</th>
                                            <th>状态</th>
                                            <th>时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>#TXN001</td>
                                            <td>测试商户A</td>
                                            <td>¥299.00</td>
                                            <td>¥8.97</td>
                                            <td><span class="badge bg-success">成功</span></td>
                                            <td>2024-06-28 14:30</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }

            getUsersContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">用户管理</h5>
                        </div>
                        <div class="card-body">
                            <p>用户管理功能开发中...</p>
                        </div>
                    </div>
                `;
            }

            getSettingsContent() {
                return `
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">系统设置</h5>
                        </div>
                        <div class="card-body">
                            <p>系统设置功能开发中...</p>
                        </div>
                    </div>
                `;
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new PayPalAdmin();
        });
    </script>
</body>
</html>
