/**
 * 商户统计管理模块
 * 从admin.js第21732-22676行提取
 * 负责商户数据统计、报表生成、图表展示等功能
 */
// 商户统计报表管理类
class MerchantStatsManager {
    constructor() {
        this.auth = new AuthManager();
        this.statsData = null;
        this.currentPeriod = '30d';
        this.currentTab = 'overview';
    }

    async initialize() {
        try {
            await this.loadStatsData();
            this.renderStatsManagement();
        } catch (error) {
            console.error('初始化统计报表失败:', error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }

    async loadStatsData(period = '30d') {
        this.currentPeriod = period;
        
        try {
            const response = await fetch(`/api/merchant/index.php?module=tools&action=get_stats&period=${period}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.statsData = data.data || data;
                return true;
            } else {
                this.showError('加载统计数据失败: ' + (data.message || '未知错误'));
                return false;
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
            this.showError('网络错误，请重试');
            return false;
        }
    }

    renderStatsManagement() {
        const container = document.getElementById('merchantStatsContainer');
        if (!container) return;

        container.innerHTML = `
            <!-- 时间周期选择 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="mb-0">统计周期</h6>
                            <small class="text-muted">选择要查看的时间范围</small>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group w-100">
                                <button class="btn ${this.currentPeriod === '7d' ? 'btn-primary' : 'btn-outline-primary'}" 
                                        onclick="window.merchantStatsManager.switchPeriod('7d')">最近7天</button>
                                <button class="btn ${this.currentPeriod === '30d' ? 'btn-primary' : 'btn-outline-primary'}" 
                                        onclick="window.merchantStatsManager.switchPeriod('30d')">最近30天</button>
                                <button class="btn ${this.currentPeriod === '90d' ? 'btn-primary' : 'btn-outline-primary'}" 
                                        onclick="window.merchantStatsManager.switchPeriod('90d')">最近90天</button>
                                <button class="btn ${this.currentPeriod === 'custom' ? 'btn-primary' : 'btn-outline-primary'}" 
                                        onclick="window.merchantStatsManager.showCustomPeriod()">自定义</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计标签页 -->
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs">
                        <li class="nav-item">
                            <a class="nav-link ${this.currentTab === 'overview' ? 'active' : ''}" 
                               href="#" onclick="window.merchantStatsManager.switchTab('overview')">
                                <i class="bi bi-graph-up me-2"></i>概览统计
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link ${this.currentTab === 'revenue' ? 'active' : ''}" 
                               href="#" onclick="window.merchantStatsManager.switchTab('revenue')">
                                <i class="bi bi-cash-coin me-2"></i>收入分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link ${this.currentTab === 'orders' ? 'active' : ''}" 
                               href="#" onclick="window.merchantStatsManager.switchTab('orders')">
                                <i class="bi bi-receipt me-2"></i>订单分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link ${this.currentTab === 'payment' ? 'active' : ''}" 
                               href="#" onclick="window.merchantStatsManager.switchTab('payment')">
                                <i class="bi bi-credit-card me-2"></i>支付方式
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link ${this.currentTab === 'trend' ? 'active' : ''}" 
                               href="#" onclick="window.merchantStatsManager.switchTab('trend')">
                                <i class="bi bi-graph-down me-2"></i>趋势分析
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div id="statsTabContent">
                        ${this.renderCurrentTab()}
                    </div>
                </div>
            </div>
        `;
    }

    renderCurrentTab() {
        switch (this.currentTab) {
            case 'overview':
                return this.renderOverviewStats();
            case 'revenue':
                return this.renderRevenueStats();
            case 'orders':
                return this.renderOrderStats();
            case 'payment':
                return this.renderPaymentStats();
            case 'trend':
                return this.renderTrendStats();
            default:
                return this.renderOverviewStats();
        }
    }

    renderOverviewStats() {
        const data = this.statsData || {};
        const overview = data.overview || {};

        return `
            <!-- 核心指标卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-gradient-primary text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-receipt display-4 mb-3"></i>
                            <h3 class="mb-1">${overview.total_orders || 0}</h3>
                            <p class="mb-0">总订单数</p>
                            <small class="text-light">
                                <i class="bi bi-arrow-${overview.orders_growth >= 0 ? 'up' : 'down'}"></i>
                                ${Math.abs(overview.orders_growth || 0)}% 较上期
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-gradient-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-cash-coin display-4 mb-3"></i>
                            <h3 class="mb-1">¥${overview.total_revenue || '0.00'}</h3>
                            <p class="mb-0">总收入</p>
                            <small class="text-light">
                                <i class="bi bi-arrow-${overview.revenue_growth >= 0 ? 'up' : 'down'}"></i>
                                ${Math.abs(overview.revenue_growth || 0)}% 较上期
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-gradient-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-check-circle display-4 mb-3"></i>
                            <h3 class="mb-1">${overview.success_rate || 0}%</h3>
                            <p class="mb-0">成功率</p>
                            <small class="text-light">
                                <i class="bi bi-arrow-${overview.success_rate_growth >= 0 ? 'up' : 'down'}"></i>
                                ${Math.abs(overview.success_rate_growth || 0)}% 较上期
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-gradient-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-calculator display-4 mb-3"></i>
                            <h3 class="mb-1">¥${overview.avg_order_amount || '0.00'}</h3>
                            <p class="mb-0">平均订单金额</p>
                            <small class="text-light">
                                <i class="bi bi-arrow-${overview.avg_amount_growth >= 0 ? 'up' : 'down'}"></i>
                                ${Math.abs(overview.avg_amount_growth || 0)}% 较上期
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细统计表格 -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-bar-chart me-2"></i>每日统计趋势</h6>
                        </div>
                        <div class="card-body">
                            <div id="dailyStatsChart" style="height: 300px;">
                                ${this.renderDailyStatsChart()}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-pie-chart me-2"></i>订单状态分布</h6>
                        </div>
                        <div class="card-body">
                            <div id="statusDistribution">
                                ${this.renderStatusDistribution()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 关键指标对比 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-speedometer2 me-2"></i>关键指标对比</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-borderless">
                                    <thead>
                                        <tr>
                                            <th>指标</th>
                                            <th>当前周期</th>
                                            <th>上个周期</th>
                                            <th>变化幅度</th>
                                            <th>趋势</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${this.renderMetricsComparison()}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderRevenueStats() {
        const data = this.statsData || {};
        const revenue = data.revenue || {};

        return `
            <div class="row">
                <!-- 收入趋势图 -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-graph-up me-2"></i>收入趋势分析</h6>
                        </div>
                        <div class="card-body">
                            <div id="revenueChart" style="height: 350px;">
                                ${this.renderRevenueChart()}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 收入统计 -->
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-wallet me-2"></i>收入统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="revenue-stats">
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>总收入</span>
                                        <strong class="text-success">¥${revenue.total || '0.00'}</strong>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>日均收入</span>
                                        <strong>¥${revenue.daily_avg || '0.00'}</strong>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>最高单日</span>
                                        <strong class="text-primary">¥${revenue.highest_day || '0.00'}</strong>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>最低单日</span>
                                        <strong class="text-muted">¥${revenue.lowest_day || '0.00'}</strong>
                                    </div>
                                </div>
                                <hr>
                                <div class="stat-item">
                                    <div class="d-flex justify-content-between">
                                        <span>增长率</span>
                                        <span class="badge ${revenue.growth_rate >= 0 ? 'bg-success' : 'bg-danger'}">
                                            ${revenue.growth_rate >= 0 ? '+' : ''}${revenue.growth_rate || 0}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 收入分布 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-pie-chart me-2"></i>收入来源分布</h6>
                        </div>
                        <div class="card-body">
                            <div id="revenueSourceChart">
                                ${this.renderRevenueSourceChart()}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-clock me-2"></i>收入时段分布</h6>
                        </div>
                        <div class="card-body">
                            <div id="revenueTimeChart">
                                ${this.renderRevenueTimeChart()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderOrderStats() {
        const data = this.statsData || {};
        const orders = data.orders || {};

        return `
            <div class="row">
                <!-- 订单量趋势 -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-graph-up me-2"></i>订单量趋势</h6>
                        </div>
                        <div class="card-body">
                            <div id="orderTrendChart" style="height: 300px;">
                                ${this.renderOrderTrendChart()}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 订单统计 -->
                <div class="col-md-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-list-check me-2"></i>订单统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="order-stats">
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>总订单数</span>
                                        <strong class="text-primary">${orders.total || 0}</strong>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>成功订单</span>
                                        <strong class="text-success">${orders.success || 0}</strong>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>失败订单</span>
                                        <strong class="text-danger">${orders.failed || 0}</strong>
                                    </div>
                                </div>
                                <div class="stat-item mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>待处理</span>
                                        <strong class="text-warning">${orders.pending || 0}</strong>
                                    </div>
                                </div>
                                <hr>
                                <div class="stat-item">
                                    <div class="d-flex justify-content-between">
                                        <span>成功率</span>
                                        <span class="badge bg-info">${orders.success_rate || 0}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单详细分析 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-table me-2"></i>订单状态详细分析</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>状态</th>
                                            <th>订单数量</th>
                                            <th>占比</th>
                                            <th>金额</th>
                                            <th>平均金额</th>
                                            <th>趋势</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${this.renderOrderStatusTable()}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderPaymentStats() {
        const data = this.statsData || {};
        const payment = data.payment || {};

        return `
            <div class="row">
                <!-- 支付方式分布 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-pie-chart me-2"></i>支付方式分布</h6>
                        </div>
                        <div class="card-body">
                            <div id="paymentMethodChart" style="height: 300px;">
                                ${this.renderPaymentMethodChart()}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 支付方式统计 -->
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-credit-card me-2"></i>支付方式统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="payment-stats">
                                ${this.renderPaymentMethodStats()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 支付成功率分析 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-graph-up me-2"></i>支付成功率趋势</h6>
                        </div>
                        <div class="card-body">
                            <div id="paymentSuccessChart" style="height: 300px;">
                                ${this.renderPaymentSuccessChart()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderTrendStats() {
        const data = this.statsData || {};
        const trend = data.trend || {};

        return `
            <div class="row">
                <!-- 综合趋势图 -->
                <div class="col-md-12">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-graph-down me-2"></i>综合趋势分析</h6>
                        </div>
                        <div class="card-body">
                            <div id="comprehensiveTrendChart" style="height: 400px;">
                                ${this.renderComprehensiveTrendChart()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预测分析 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-graph-up-arrow me-2"></i>趋势预测</h6>
                        </div>
                        <div class="card-body">
                            <div class="trend-prediction">
                                <div class="alert alert-info">
                                    <h6><i class="bi bi-lightbulb me-2"></i>数据洞察</h6>
                                    <ul class="mb-0">
                                        <li>根据历史数据分析，预计下周收入将${trend.revenue_prediction >= 0 ? '增长' : '下降'} ${Math.abs(trend.revenue_prediction || 0)}%</li>
                                        <li>订单量趋势${trend.order_trend === 'up' ? '上升' : trend.order_trend === 'down' ? '下降' : '平稳'}</li>
                                        <li>建议关注${trend.recommendation || '数据质量和用户体验'}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>异常检测</h6>
                        </div>
                        <div class="card-body">
                            <div class="anomaly-detection">
                                ${this.renderAnomalyDetection()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 图表渲染函数
    renderDailyStatsChart() {
        const chartData = this.statsData?.daily_stats || [];
        
        if (chartData.length === 0) {
            return `
                <div class="text-center py-5 text-muted">
                    <i class="bi bi-graph-up" style="font-size: 3rem;"></i>
                    <p class="mt-3">暂无每日统计数据</p>
                </div>
            `;
        }

        // 简化的图表实现
        const maxValue = Math.max(...chartData.map(item => Math.max(item.orders, item.revenue / 10)));
        const chartHtml = chartData.map((item, index) => {
            const orderHeight = maxValue > 0 ? (item.orders / maxValue) * 200 : 0;
            const revenueHeight = maxValue > 0 ? ((item.revenue / 10) / maxValue) * 200 : 0;
            
            return `
                <div class="chart-group" style="display: inline-block; width: ${100/chartData.length}%; text-align: center; vertical-align: bottom; padding: 0 2px;">
                    <div style="position: relative; height: 220px; display: flex; align-items: end; justify-content: center;">
                        <div style="width: 40%; height: ${orderHeight}px; background: #007bff; margin-right: 2px; border-radius: 2px 2px 0 0;" title="订单: ${item.orders}"></div>
                        <div style="width: 40%; height: ${revenueHeight}px; background: #28a745; border-radius: 2px 2px 0 0;" title="收入: ¥${item.revenue}"></div>
                    </div>
                    <small class="text-muted">${item.date}</small>
                </div>
            `;
        }).join('');

        return `
            <div style="height: 250px; display: flex; align-items: end; padding: 20px;">
                ${chartHtml}
            </div>
            <div class="mt-3 text-center">
                <span class="badge bg-primary me-2">■ 订单量</span>
                <span class="badge bg-success">■ 收入(÷10)</span>
            </div>
        `;
    }

    renderStatusDistribution() {
        const statusData = this.statsData?.order_status || {};
        const total = Object.values(statusData).reduce((sum, count) => sum + count, 0);
        
        if (total === 0) {
            return `
                <div class="text-center py-3 text-muted">
                    <i class="bi bi-pie-chart" style="font-size: 2rem;"></i>
                    <p class="mt-2">暂无状态数据</p>
                </div>
            `;
        }

        const statusNames = {
            'success': '成功',
            'pending': '待处理',
            'failed': '失败',
            'cancelled': '已取消'
        };

        const colors = {
            'success': '#28a745',
            'pending': '#ffc107',
            'failed': '#dc3545',
            'cancelled': '#6c757d'
        };

        return Object.entries(statusData).map(([status, count]) => {
            const percentage = ((count / total) * 100).toFixed(1);
            return `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="d-flex align-items-center">
                        <div style="width: 12px; height: 12px; background: ${colors[status]}; border-radius: 2px; margin-right: 8px;"></div>
                        <span>${statusNames[status] || status}</span>
                    </div>
                    <div>
                        <strong>${count}</strong>
                        <small class="text-muted ms-1">(${percentage}%)</small>
                    </div>
                </div>
            `;
        }).join('');
    }

    renderMetricsComparison() {
        const comparison = this.statsData?.comparison || {};
        const metrics = [
            { name: '总订单数', current: comparison.orders_current || 0, previous: comparison.orders_previous || 0 },
            { name: '总收入', current: comparison.revenue_current || 0, previous: comparison.revenue_previous || 0, isCurrency: true },
            { name: '成功率', current: comparison.success_rate_current || 0, previous: comparison.success_rate_previous || 0, isPercentage: true },
            { name: '平均订单金额', current: comparison.avg_amount_current || 0, previous: comparison.avg_amount_previous || 0, isCurrency: true }
        ];

        return metrics.map(metric => {
            const change = metric.current - metric.previous;
            const changePercent = metric.previous > 0 ? ((change / metric.previous) * 100).toFixed(1) : 0;
            const isPositive = change >= 0;
            
            const formatValue = (value) => {
                if (metric.isCurrency) return `¥${value}`;
                if (metric.isPercentage) return `${value}%`;
                return value;
            };

            return `
                <tr>
                    <td><strong>${metric.name}</strong></td>
                    <td>${formatValue(metric.current)}</td>
                    <td>${formatValue(metric.previous)}</td>
                    <td>
                        <span class="${isPositive ? 'text-success' : 'text-danger'}">
                            ${isPositive ? '+' : ''}${changePercent}%
                        </span>
                    </td>
                    <td>
                        <i class="bi bi-arrow-${isPositive ? 'up' : 'down'} ${isPositive ? 'text-success' : 'text-danger'}"></i>
                    </td>
                </tr>
            `;
        }).join('');
    }

    renderRevenueChart() {
        // 简化实现，实际项目中可以使用专业图表库
        return `
            <div class="text-center py-5 text-muted">
                <i class="bi bi-graph-up" style="font-size: 3rem;"></i>
                <p class="mt-3">收入趋势图</p>
                <p>可集成 Chart.js 等图表库实现更丰富的可视化</p>
            </div>
        `;
    }

    renderRevenueSourceChart() {
        return `
            <div class="text-center py-3 text-muted">
                <i class="bi bi-pie-chart" style="font-size: 2rem;"></i>
                <p class="mt-2">收入来源分布图</p>
            </div>
        `;
    }

    renderRevenueTimeChart() {
        return `
            <div class="text-center py-3 text-muted">
                <i class="bi bi-clock" style="font-size: 2rem;"></i>
                <p class="mt-2">收入时段分布图</p>
            </div>
        `;
    }

    renderOrderTrendChart() {
        return `
            <div class="text-center py-5 text-muted">
                <i class="bi bi-graph-up" style="font-size: 3rem;"></i>
                <p class="mt-3">订单量趋势图</p>
            </div>
        `;
    }

    renderOrderStatusTable() {
        const orderStats = this.statsData?.order_details || {};
        const statuses = [
            { key: 'success', name: '成功', class: 'success' },
            { key: 'pending', name: '待处理', class: 'warning' },
            { key: 'failed', name: '失败', class: 'danger' },
            { key: 'cancelled', name: '已取消', class: 'secondary' }
        ];

        return statuses.map(status => {
            const data = orderStats[status.key] || {};
            return `
                <tr>
                    <td><span class="badge bg-${status.class}">${status.name}</span></td>
                    <td><strong>${data.count || 0}</strong></td>
                    <td>${data.percentage || 0}%</td>
                    <td>¥${data.amount || '0.00'}</td>
                    <td>¥${data.avg_amount || '0.00'}</td>
                    <td>
                        <i class="bi bi-arrow-${data.trend === 'up' ? 'up text-success' : data.trend === 'down' ? 'down text-danger' : 'right text-muted'}"></i>
                    </td>
                </tr>
            `;
        }).join('');
    }

    renderPaymentMethodChart() {
        return `
            <div class="text-center py-5 text-muted">
                <i class="bi bi-pie-chart" style="font-size: 3rem;"></i>
                <p class="mt-3">支付方式分布图</p>
            </div>
        `;
    }

    renderPaymentMethodStats() {
        const paymentData = this.statsData?.payment_methods || {};
        const methods = [
            { key: 'alipay', name: '支付宝', icon: 'bi-alipay', color: 'primary' },
            { key: 'wechat', name: '微信支付', icon: 'bi-wechat', color: 'success' },
            { key: 'qq', name: 'QQ钱包', icon: 'bi-qq', color: 'info' }
        ];

        return methods.map(method => {
            const data = paymentData[method.key] || {};
            return `
                <div class="payment-method-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="${method.icon} text-${method.color} me-2"></i>
                            <span>${method.name}</span>
                        </div>
                        <div class="text-end">
                            <div><strong>${data.count || 0}</strong> 笔</div>
                            <small class="text-muted">¥${data.amount || '0.00'}</small>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 6px;">
                        <div class="progress-bar bg-${method.color}" style="width: ${data.percentage || 0}%"></div>
                    </div>
                    <small class="text-muted">${data.percentage || 0}% 占比</small>
                </div>
            `;
        }).join('');
    }

    renderPaymentSuccessChart() {
        return `
            <div class="text-center py-5 text-muted">
                <i class="bi bi-graph-up" style="font-size: 3rem;"></i>
                <p class="mt-3">支付成功率趋势图</p>
            </div>
        `;
    }

    renderComprehensiveTrendChart() {
        return `
            <div class="text-center py-5 text-muted">
                <i class="bi bi-graph-down" style="font-size: 3rem;"></i>
                <p class="mt-3">综合趋势分析图</p>
                <p>展示订单量、收入、成功率等多指标综合趋势</p>
            </div>
        `;
    }

    renderAnomalyDetection() {
        const anomalies = this.statsData?.anomalies || [];
        
        if (anomalies.length === 0) {
            return `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>
                    <strong>状态正常</strong><br>
                    未检测到异常数据模式
                </div>
            `;
        }

        return anomalies.map(anomaly => `
            <div class="alert alert-${anomaly.level === 'high' ? 'danger' : anomaly.level === 'medium' ? 'warning' : 'info'}">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <strong>${anomaly.title}</strong><br>
                ${anomaly.description}
            </div>
        `).join('');
    }

    // 交互功能
    async switchPeriod(period) {
        if (period === this.currentPeriod) return;
        
        const container = document.getElementById('merchantStatsContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-3">正在切换统计周期...</div>
                </div>
            `;
        }
        
        await this.loadStatsData(period);
        this.renderStatsManagement();
        this.showSuccess(`已切换到${period === '7d' ? '7天' : period === '30d' ? '30天' : '90天'}统计`);
    }

    switchTab(tab) {
        this.currentTab = tab;
        const tabContent = document.getElementById('statsTabContent');
        if (tabContent) {
            tabContent.innerHTML = this.renderCurrentTab();
        }
        
        // 更新标签状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[onclick*="'${tab}'"]`).classList.add('active');
    }

    showCustomPeriod() {
        // 这里可以实现自定义时间范围选择
        this.showInfo('自定义时间范围功能开发中...');
    }

    async exportStats() {
        try {
            const response = await fetch(`/api/merchant/index.php?module=tools&action=export_stats&period=${this.currentPeriod}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `stats-report-${this.currentPeriod}-${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                this.showSuccess('统计报表已导出');
            } else {
                this.showError('导出统计报表失败');
            }
        } catch (error) {
            console.error('导出统计报表失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    async refreshStats() {
        const container = document.getElementById('merchantStatsContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">刷新中...</span>
                    </div>
                    <div class="mt-3">正在刷新统计数据...</div>
                </div>
            `;
        }
        
        await this.loadStatsData(this.currentPeriod);
        this.renderStatsManagement();
        this.showSuccess('统计数据已刷新');
    }

    showSuccess(message) {
        console.log('Success:', message);
        // 这里可以集成toast通知组件
    }

    showError(message) {
        console.error('Error:', message);
        // 这里可以集成toast通知组件
        
        const container = document.getElementById('merchantStatsContainer');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    ${message}
                    <button class="btn btn-outline-danger btn-sm ms-3" onclick="window.merchantStatsManager.refreshStats()">
                        重试
                    </button>
                </div>
            `;
        }
    }

    showInfo(message) {
        console.log('Info:', message);
        // 这里可以集成toast通知组件
    }
}


// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = MerchantStatsManager;
} else if (typeof window !== "undefined") {
    window.MerchantStatsManager = MerchantStatsManager;
} 