<?php
/**
 * 商户认证接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'login':
            handleLogin();
            break;
        case 'logout':
            handleLogout();
            break;
        case 'profile':
            handleGetProfile();
            break;
        case 'update_profile':
            handleUpdateProfile();
            break;
        case 'change_password':
            handleChangePassword();
            break;
        default:
            http_response_code(404);
            echo json_encode(array('code' => 404, 'message' => '接口不存在'));
    }
} catch (Exception $e) {
    error_log("Merchant Auth API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(array('code' => 500, 'message' => '服务器错误: ' . $e->getMessage()));
}

// 处理登录
function handleLogin() {
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证必填字段
    if (empty($input['username']) || empty($input['password'])) {
        http_response_code(400);
        echo json_encode(array('code' => 400, 'message' => '用户名和密码不能为空'));
        return;
    }
    
    $username = trim($input['username']);
    $password = trim($input['password']);
    
    try {
        // 初始化认证系统
        $auth = TenantAuth::init();
        $db = $auth->getDatabase();
        
        // 验证用户凭据
        $user = $auth->authenticate($username, $password);
        if (!$user) {
            http_response_code(401);
            echo json_encode(array('code' => 401, 'message' => '用户名或密码错误'));
            return;
        }
        
        // 检查是否为商户用户
        if ($user['user_type'] !== 'merchant') {
            http_response_code(403);
            echo json_encode(array('code' => 403, 'message' => '只有商户用户才能登录商户后台'));
            return;
        }
        
        // 获取商户信息
        $merchant = $db->fetch(
            "SELECT * FROM merchants WHERE user_id = ?",
            array($user['id'])
        );
        
        if (!$merchant) {
            http_response_code(403);
            echo json_encode(array('code' => 403, 'message' => '商户信息不存在，请联系管理员'));
            return;
        }
        
        if ($merchant['status'] !== 'active') {
            http_response_code(403);
            echo json_encode(array('code' => 403, 'message' => '商户账户已被禁用，请联系管理员'));
            return;
        }
        
        // 生成访问令牌
        $token = $auth->generateToken($user);
        
        // 更新最后登录时间
        $db->execute("UPDATE users SET last_login_at = NOW() WHERE id = ?", array($user['id']));
        
        // 记录登录日志
        $auth->logUserAction('merchant_login', 'auth', $merchant['id'], '商户登录');
        
        echo json_encode(array(
            'code' => 200,
            'message' => '登录成功',
            'data' => array(
                'token' => $token,
                'user' => array(
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'real_name' => $user['real_name'],
                    'user_type' => $user['user_type']
                ),
                'merchant' => array(
                    'id' => $merchant['id'],
                    'company_name' => $merchant['company_name'],
                    'status' => $merchant['status'],
                    'created_at' => $merchant['created_at']
                )
            )
        ));
        
    } catch (Exception $e) {
        error_log('商户登录失败: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(array('code' => 500, 'message' => '登录失败: ' . $e->getMessage()));
    }
}

// 处理登出
function handleLogout() {
    try {
        // 初始化认证系统
        $auth = TenantAuth::init();
        
        // 检查认证
        if (!$auth->checkAuth()) {
            http_response_code(401);
            echo json_encode(array('code' => 401, 'message' => '未授权访问'));
            return;
        }
        
        $currentUser = $auth->getCurrentUser();
        
        // 记录登出日志
        $auth->logUserAction('merchant_logout', 'auth', 0, '商户登出');
        
        echo json_encode(array('code' => 200, 'message' => '登出成功'));
        
    } catch (Exception $e) {
        error_log('商户登出失败: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(array('code' => 500, 'message' => '登出失败: ' . $e->getMessage()));
    }
}

// 获取用户资料
function handleGetProfile() {
    try {
        // 初始化认证系统
        $auth = TenantAuth::init();
        
        // 检查认证
        if (!$auth->checkAuth()) {
            http_response_code(401);
            echo json_encode(array('code' => 401, 'message' => '未授权访问'));
            return;
        }
        
        $currentUser = $auth->getCurrentUser();
        $db = $auth->getDatabase();
        
        // 获取商户信息
        $merchant = $db->fetch(
            "SELECT * FROM merchants WHERE user_id = ?",
            array($currentUser['id'])
        );
        
        if (!$merchant) {
            http_response_code(404);
            echo json_encode(array('code' => 404, 'message' => '商户信息不存在'));
            return;
        }
        
        // 记录操作日志
        $auth->logUserAction('view_profile', 'merchant', $merchant['id'], '查看商户资料');
        
        $profile = array(
            'user' => array(
                'id' => $currentUser['id'],
                'username' => $currentUser['username'],
                'email' => $currentUser['email'],
                'real_name' => $currentUser['real_name'],
                'phone' => $currentUser['phone'],
                'created_at' => $currentUser['created_at'],
                'last_login_at' => $currentUser['last_login_at']
            ),
            'merchant' => array(
                'id' => $merchant['id'],
                'company_name' => $merchant['company_name'],
                'contact_person' => $merchant['contact_person'],
                'contact_phone' => $merchant['contact_phone'],
                'contact_email' => $merchant['contact_email'],
                'status' => $merchant['status'],
                'created_at' => $merchant['created_at']
            )
        );
        
        echo json_encode(array(
            'code' => 200,
            'message' => '用户资料获取成功',
            'data' => $profile
        ));
        
    } catch (Exception $e) {
        error_log('获取用户资料失败: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(array('code' => 500, 'message' => '获取用户资料失败: ' . $e->getMessage()));
    }
}

// 更新用户资料（暂时简化）
function handleUpdateProfile() {
    try {
        // 初始化认证系统
        $auth = TenantAuth::init();
        
        // 检查认证
        if (!$auth->checkAuth()) {
            http_response_code(401);
            echo json_encode(array('code' => 401, 'message' => '未授权访问'));
            return;
        }
        
        http_response_code(501);
        echo json_encode(array('code' => 501, 'message' => '功能开发中'));
        
    } catch (Exception $e) {
        error_log('更新用户资料失败: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(array('code' => 500, 'message' => '更新用户资料失败: ' . $e->getMessage()));
    }
}

// 修改密码（暂时简化）
function handleChangePassword() {
    try {
        // 初始化认证系统
        $auth = TenantAuth::init();
        
        // 检查认证
        if (!$auth->checkAuth()) {
            http_response_code(401);
            echo json_encode(array('code' => 401, 'message' => '未授权访问'));
            return;
        }
        
        http_response_code(501);
        echo json_encode(array('code' => 501, 'message' => '功能开发中'));
        
    } catch (Exception $e) {
        error_log('修改密码失败: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(array('code' => 500, 'message' => '修改密码失败: ' . $e->getMessage()));
    }
}
?> 