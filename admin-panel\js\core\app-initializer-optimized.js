/**
 * 优化版应用初始化器
 * 基于租户配置的按需初始化系统
 * 
 * <AUTHOR>
 * @version 2.0.0 - 架构优化版本
 */

class AppInitializerOptimized {
    constructor(tenantConfig) {
        this.tenantConfig = tenantConfig;
        this.isInitialized = false;
        this.startTime = Date.now();
        this.modules = new Map();
        this.currentUser = null;
        this.isAuthenticated = false;
        
        console.log('🚀 优化版应用初始化器启动:', tenantConfig.tenantType);
    }

    /**
     * 初始化应用
     */
    async initialize() {
        try {
            console.log('📋 开始应用初始化流程...');
            
            // 第一步：初始化基础系统
            await this._initializeCore();
            
            // 第二步：检查认证状态
            await this._checkAuthentication();
            
            // 第三步：根据认证状态决定加载内容
            if (this.isAuthenticated) {
                await this._initializeMainApp();
            } else {
                await this._initializeLoginInterface();
            }
            
            // 第四步：完成初始化
            this._completeInitialization();
            
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this._showInitializationError(error);
        }
    }

    /**
     * 初始化核心系统
     * @private
     */
    async _initializeCore() {
        console.log('⚙️ 初始化核心系统...');
        
        // 设置全局主题
        this._applyTheme();
        
        // 初始化工具类
        if (typeof window.Utils !== 'undefined') {
            this.utils = new window.Utils();
            console.log('✅ 工具类初始化完成');
        }
        
        // 初始化认证管理器
        if (typeof window.AuthManager !== 'undefined') {
            this.authManager = new window.AuthManager();
            console.log('✅ 认证管理器初始化完成');
        }
        
        console.log('✅ 核心系统初始化完成');
    }

    /**
     * 检查认证状态
     * @private
     */
    async _checkAuthentication() {
        console.log('🔐 检查用户认证状态...');
        
        try {
            // 检查本地存储的token
            const token = localStorage.getItem('auth_token');
            if (!token) {
                console.log('❌ 未找到认证token');
                this.isAuthenticated = false;
                return;
            }

            // 验证token有效性
            const response = await fetch(this.tenantConfig.apiEndpoints.profile, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.error_code === 0) {
                    this.currentUser = result.data;
                    this.isAuthenticated = true;
                    console.log('✅ 用户认证有效:', this.currentUser.username);
                } else {
                    throw new Error('Token验证失败');
                }
            } else {
                throw new Error('认证请求失败');
            }
            
        } catch (error) {
            console.log('❌ 认证验证失败:', error.message);
            // 清除无效token
            localStorage.removeItem('auth_token');
            this.isAuthenticated = false;
        }
    }

    /**
     * 初始化主应用界面
     * @private
     */
    async _initializeMainApp() {
        console.log('🏠 初始化主应用界面...');
        
        // 隐藏登录容器，显示应用容器
        const loginContainer = document.getElementById('loginContainer');
        const appContainer = document.getElementById('appContainer');
        
        if (loginContainer) loginContainer.style.display = 'none';
        if (appContainer) appContainer.style.display = 'block';
        
        // 创建应用布局
        this._createAppLayout();
        
        // 加载租户专用路由配置
        await this._loadTenantRoutes();
        
        // 初始化路由系统
        this._initializeRouter();
        
        // 初始化UI管理器
        this._initializeUIManager();
        
        // 设置应用标题
        document.title = `${this.tenantConfig.theme.brandName} - ${this.currentUser.username}`;
        
        console.log('✅ 主应用界面初始化完成');
    }

    /**
     * 初始化登录界面
     * @private
     */
    async _initializeLoginInterface() {
        console.log('🔑 初始化登录界面...');
        
        // 隐藏应用容器，显示登录容器
        const loginContainer = document.getElementById('loginContainer');
        const appContainer = document.getElementById('appContainer');
        
        if (appContainer) appContainer.style.display = 'none';
        if (loginContainer) {
            loginContainer.style.display = 'flex';
            loginContainer.style.alignItems = 'center';
            loginContainer.style.justifyContent = 'center';
            loginContainer.style.minHeight = '100vh';
            loginContainer.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            loginContainer.style.padding = '20px';
            loginContainer.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        }
        
        // 创建登录界面
        this._createLoginInterface();
        
        // 设置应用标题
        document.title = `${this.tenantConfig.theme.brandName} - 用户登录`;
        
        console.log('✅ 登录界面初始化完成');
    }

    /**
     * 应用主题
     * @private
     */
    _applyTheme() {
        const theme = this.tenantConfig.theme;
        
        // 设置CSS变量
        document.documentElement.style.setProperty('--primary-color', theme.primaryColor);
        
        // 添加主题样式
        const themeStyle = document.createElement('style');
        themeStyle.id = 'tenantTheme';
        themeStyle.textContent = `
            :root {
                --primary-color: ${theme.primaryColor};
                --brand-name: "${theme.brandName}";
            }
            
            .btn-primary {
                background-color: ${theme.primaryColor} !important;
                border-color: ${theme.primaryColor} !important;
            }
            
            .text-primary {
                color: ${theme.primaryColor} !important;
            }
            
            .navbar-brand {
                color: ${theme.primaryColor} !important;
            }
        `;
        
        document.head.appendChild(themeStyle);
        console.log('🎨 主题应用完成:', theme.brandName);
    }

    /**
     * 创建应用布局
     * @private
     */
    _createAppLayout() {
        const appContainer = document.getElementById('appContainer');
        
        const layoutHtml = `
            <div class="app-wrapper">
                <!-- 顶部导航 -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                    <div class="container-fluid">
                        <a class="navbar-brand fw-bold" href="#">
                            <i class="bi-credit-card me-2"></i>
                            ${this.tenantConfig.theme.brandName}
                        </a>
                        
                        <div class="navbar-nav ms-auto">
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi-person-circle me-2"></i>
                                    ${this.currentUser.username}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#/profile"><i class="bi-person me-2"></i>个人资料</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="app.logout()"><i class="bi-box-arrow-right me-2"></i>退出登录</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>

                <div class="app-content d-flex">
                    <!-- 侧边栏 -->
                    <div class="sidebar bg-light border-end" style="width: 250px; min-height: calc(100vh - 56px);">
                        <div class="sidebar-content p-3">
                            <div id="sidebarMenu"></div>
                        </div>
                    </div>

                    <!-- 主内容区 -->
                    <div class="main-content flex-grow-1">
                        <div class="content-wrapper p-4">
                            <div id="mainContent">
                                <!-- 动态内容区域 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        appContainer.innerHTML = layoutHtml;
        console.log('🏗️ 应用布局创建完成');
    }

    /**
     * 创建登录界面
     * @private
     */
    _createLoginInterface() {
        const loginContainer = document.getElementById('loginContainer');
        
        // 添加简洁登录样式
        if (!document.getElementById('loginSimpleCSS')) {
            const cssLink = document.createElement('link');
            cssLink.id = 'loginSimpleCSS';
            cssLink.rel = 'stylesheet';
            cssLink.href = './css/login-simple.css';
            document.head.appendChild(cssLink);
        }
        
        const loginHtml = `
            <div class="login-card">
                <h1 class="login-title">PayPal</h1>
                <p class="login-subtitle">请输入您的登录凭据</p>
                
                <div id="loginAlert"></div>
                
                <form id="loginForm">
                    <div class="form-group">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" placeholder="请输入用户名" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" placeholder="请输入密码" required>
                    </div>
                    
                    <div class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <label for="rememberMe">记住我</label>
                    </div>
                    
                    <button type="submit" class="btn-login">
                        登录
                    </button>
                </form>
                
                <div class="test-info">
                    <strong>测试账号：</strong><br>
                    用户名: <strong>platform_admin_001</strong><br>
                    密码: <strong>password</strong>
                </div>
            </div>
        `;
        
        loginContainer.innerHTML = loginHtml;
        
        // 绑定登录表单事件
        this._bindLoginEvents();
        
        console.log('🔑 登录界面创建完成');
    }

    /**
     * 加载租户专用路由配置
     * @private
     */
    async _loadTenantRoutes() {
        const tenantType = this.tenantConfig.tenantType;
        
        // 正确处理下划线分隔的租户类型，如 platform_admin -> PlatformAdminRoutes
        const routeConfigName = tenantType
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('') + 'Routes';
        
        console.log(`🔍 查找租户路由配置: ${routeConfigName} (租户类型: ${tenantType})`);
        
        if (window[routeConfigName]) {
            this.routeConfig = window[routeConfigName];
            console.log(`✅ 租户路由配置加载完成: ${routeConfigName}`);
        } else {
            // 提供更详细的错误信息
            const availableConfigs = Object.keys(window).filter(key => key.endsWith('Routes'));
            console.error(`❌ 可用的路由配置:`, availableConfigs);
            throw new Error(`租户路由配置未找到: ${routeConfigName}`);
        }
    }

    /**
     * 初始化路由系统
     * @private
     */
    _initializeRouter() {
        // 简化的路由系统
        this.router = {
            currentRoute: null,
            navigate: (path) => {
                console.log('🧭 导航到:', path);
                this._renderRoute(path);
            }
        };
        
        // 监听hash变化
        window.addEventListener('hashchange', () => {
            const path = window.location.hash.slice(1) || this.routeConfig.defaultRoute;
            this.router.navigate(path);
        });
        
        // 初始路由
        const initialPath = window.location.hash.slice(1) || this.routeConfig.defaultRoute;
        this.router.navigate(initialPath);
        
        console.log('🧭 路由系统初始化完成');
    }

    /**
     * 初始化UI管理器
     * @private
     */
    _initializeUIManager() {
        // 渲染侧边栏菜单
        this._renderSidebarMenu();
        
        console.log('🎨 UI管理器初始化完成');
    }

    /**
     * 渲染侧边栏菜单
     * @private
     */
    _renderSidebarMenu() {
        const menuContainer = document.getElementById('sidebarMenu');
        if (!menuContainer) return;
        
        const menuItems = this.routeConfig.menuConfig.map(item => `
            <div class="menu-item mb-2">
                <a href="#${item.path}" class="btn btn-outline-secondary w-100 text-start">
                    <i class="${item.icon} me-2"></i>
                    ${item.name}
                </a>
            </div>
        `).join('');
        
        menuContainer.innerHTML = `
            <div class="menu-header mb-3">
                <h6 class="text-muted">功能菜单</h6>
            </div>
            ${menuItems}
        `;
    }

    /**
     * 渲染路由页面
     * @private
     */
    _renderRoute(path) {
        const route = this.routeConfig.routes[path];
        if (!route) {
            console.warn('⚠️ 路由未找到:', path);
            return;
        }
        
        const mainContent = document.getElementById('mainContent');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="route-content">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">${route.title}</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="#/dashboard">首页</a></li>
                                <li class="breadcrumb-item active">${route.title}</li>
                            </ol>
                        </nav>
                    </div>
                    
                    <div class="module-container">
                        <div class="alert alert-info">
                            <i class="bi-info-circle me-2"></i>
                            正在加载 ${route.component} 模块...
                        </div>
                    </div>
                </div>
            `;
        }
        
        this.router.currentRoute = path;
        console.log('📄 路由渲染完成:', route.title);
    }

    /**
     * 绑定登录事件
     * @private
     */
    _bindLoginEvents() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this._handleLogin();
            });
        }
    }

    /**
     * 处理登录
     * @private
     */
    async _handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const alertContainer = document.getElementById('loginAlert');
        
        if (!username || !password) {
            alertContainer.innerHTML = `
                <div class="alert alert-warning">
                    <i class="bi-exclamation-triangle me-2"></i>请输入用户名和密码
                </div>
            `;
            return;
        }
        
        try {
            alertContainer.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi-hourglass-split me-2"></i>正在验证登录信息...
                </div>
            `;
            
            // 使用全局CONFIG中的API地址
            const apiUrl = `${window.CONFIG?.API_BASE_URL || '/api'}/admin.php?action=login`;
            
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password })
            });
            
            if (!response.ok) {
                throw new Error(`网络错误: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.code === 200 && result.data) {
                // 登录成功 - 适配后端返回格式
                localStorage.setItem('auth_token', result.data.token);
                localStorage.setItem('user_info', JSON.stringify(result.data.user));
                this.currentUser = result.data.user;
                this.isAuthenticated = true;
                
                alertContainer.innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi-check-circle me-2"></i>登录成功，正在跳转...
                    </div>
                `;
                
                setTimeout(() => {
                    this._initializeMainApp();
                }, 1000);
                
            } else {
                throw new Error(result.message || '登录失败，请检查用户名和密码');
            }
            
        } catch (error) {
            console.error('登录错误:', error);
            alertContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi-exclamation-triangle me-2"></i>${error.message}
                </div>
            `;
        }
    }

    /**
     * 完成初始化
     * @private
     */
    _completeInitialization() {
        this.isInitialized = true;
        const initTime = Date.now() - this.startTime;
        
        // 设置全局应用实例
        window.app = this;
        
        console.log(`🎉 应用初始化完成 (${initTime}ms)`);
        console.log('📊 初始化统计:', {
            租户类型: this.tenantConfig.tenantType,
            功能数量: this.routeConfig?.menuConfig.length || 0,
            认证状态: this.isAuthenticated,
            初始化时间: `${initTime}ms`
        });
    }

    /**
     * 显示初始化错误
     * @private
     */
    _showInitializationError(error) {
        document.body.innerHTML = `
            <div class="error-container d-flex align-items-center justify-content-center min-vh-100">
                <div class="error-content text-center">
                    <i class="bi-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                    <h2 class="mt-3">应用初始化失败</h2>
                    <p class="text-muted">${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="bi-arrow-clockwise me-2"></i>重新加载
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 退出登录
     */
    logout() {
        localStorage.removeItem('auth_token');
        location.reload();
    }
}

// 导出到全局作用域
window.AppInitializerOptimized = AppInitializerOptimized;
console.log('✅ 优化版应用初始化器已加载'); 