/**
 * 安全日志管理器
 * 从admin.js第13483-14284行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class SecurityLogManager {
    constructor() {
        this.apiUrl = '/api/admin.php?do=security_logs';
        this.currentPage = 1;
        this.pageSize = 20;
        this.currentFilters = {
        };
        this.refreshInterval = null;
        this.isRealTimeMode = false;
        this.auth = new AuthManager();
    }
    // 初始化
    initialize() {
        this.loadSecurityStats();
        this.loadSecurityLogs();
        this.loadRecentEvents();
        this.initializeEventListeners();
        this.startRealTimeUpdates();
    }
    // 初始化事件监听器
    initializeEventListeners() {
        // 搜索功能
        document.getElementById('logSearchBtn')?.addEventListener('click', () => {
            this.searchLogs();
        });
        document.getElementById('logSearchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchLogs();
            }
        });
        // 筛选器
        ['logEventTypeFilter', 'logRiskLevelFilter', 'logTimeRangeFilter', 'logUserTypeFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });
        // 实时监控开关
        document.getElementById('realTimeToggle')?.addEventListener('change', (e) => {
            this.toggleRealTimeMode(e.target.checked);
        });
        // 导出功能
        document.getElementById('exportLogsBtn')?.addEventListener('click', () => {
            this.exportSecurityLogs();
        });
        // 刷新按钮
        document.getElementById('refreshLogsBtn')?.addEventListener('click', () => {
            this.refreshData();
        });
        // 清除筛选
        document.getElementById('clearFiltersBtn')?.addEventListener('click', () => {
            this.clearFilters();
        });
    }
    // 加载安全统计
    async loadSecurityStats() {
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=stats`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.updateStatsDisplay(result.data);
            }
        } catch (error) {
            console.error('加载安全统计失败:',
            error);
        }
    }
    // 更新统计显示
    updateStatsDisplay(stats) {
        const elements = {
            todayEvents: stats.today_events || 0,
            riskEvents: stats.risk_events || 0,
            suspiciousIPs: stats.suspicious_ips || 0,
            alertsCount: stats.alerts_count || 0,
            riskScore: stats.risk_score || 0
        };
        Object.keys(elements).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (key === 'riskScore') {
                    element.textContent = `${
                        elements[key]
                    }/100`;
                    // 根据风险分数设置颜色
                    element.className = this.getRiskScoreClass(elements[key]);
                } else {
                    element.textContent = this.formatNumber(elements[key]);
                }
            }
        });
        // 更新风险等级分布图表
        if (stats.risk_distribution) {
            this.updateRiskDistributionChart(stats.risk_distribution);
        }
    }
    // 加载安全日志
    async loadSecurityLogs() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                ...this.currentFilters
            });
            const response = await fetch(`${
                this.apiUrl
            }&${
                params
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderSecurityLogsTable(result.data.logs);
                this.renderPagination(result.data.pagination);
                this.updateLogCount(result.data.pagination.total_records);
            } else {
                this.showError('加载安全日志失败: ' + result.message);
            }
        } catch (error) {
            console.error('加载安全日志失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 渲染安全日志表格
    renderSecurityLogsTable(logs) {
        const container = document.getElementById('securityLogsTableContainer');
        if (!container) return;
        if (!logs || logs.length === 0) {
            container.innerHTML = `
            <div class="text-center py-5">
            <i class="bi bi-shield-exclamation display-1 text-muted"></i>
            <div class="mt-3">暂无安全日志数据</div>
            </div>
            `;
            return;
        }
        const tableHtml = `
        <div class="table-responsive">
        <table class="table table-hover align-middle">
        <thead class="table-light">
        <tr>
        <th>时间</th>
        <th>事件类型</th>
        <th>用户</th>
        <th>风险等级</th>
        <th>IP地址</th>
        <th>描述</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        ${
            logs.map(log => `
            <tr class="${
                this.getLogRowClass(log.risk_level)
            }">
            <td>
            <small class="text-nowrap">${
                this.formatDateTime(log.created_at)
            }</small>
            </td>
            <td>
            <span class="badge ${
                this.getEventTypeBadgeClass(log.event_type)
            }">
            ${
                this.getEventTypeText(log.event_type)
            }
            </span>
            </td>
            <td>
            <div class="d-flex align-items-center">
            ${
                log.username ? `
                <div class="avatar-xs bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                ${
                    log.username.charAt(0).toUpperCase()
                }
                </div>
                <div>
                <div class="fw-bold">${
                    log.username
                }</div>
                <small class="text-muted">${
                    log.user_type || '-'
                }</small>
                </div>
                ` : '<span class="text-muted">系统</span>'
            }
            </div>
            </td>
            <td>
            <span class="badge ${
                this.getRiskLevelBadgeClass(log.risk_level)
            }">
            ${
                this.getRiskLevelText(log.risk_level)
            }
            </span>
            </td>
            <td>
            <code class="small">${
                log.ip_address || '-'
            }</code>
            ${
                log.country ? `<br><small class="text-muted">${
                    log.country
                }</small>` : ''
            }
            </td>
            <td>
            <div class="log-description">
            ${
                log.description
            }
            ${
                log.details ? `<br><small class="text-muted">${
                    this.truncateText(log.details, 100)
                }</small>` : ''
            }
            </div>
            </td>
            <td>
            <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary"
            onclick="window.securityLogManager.viewLogDetails(${
                log.id
            })"
            data-bs-toggle="tooltip" title="查看详情">
            <i class="bi bi-eye"></i>
            </button>
            ${
                log.risk_level === 'high' || log.risk_level === 'critical' ? `
                <button class="btn btn-outline-warning"
                onclick="window.securityLogManager.handleSecurityAlert(${
                    log.id
                })"
                data-bs-toggle="tooltip" title="处理告警">
                <i class="bi bi-exclamation-triangle"></i>
                </button>
                ` : ''
            }
            ${
                log.ip_address ? `
                <button class="btn btn-outline-danger"
                onclick="window.securityLogManager.blockIP('${
                    log.ip_address
                }')"
                data-bs-toggle="tooltip" title="封禁IP">
                <i class="bi bi-ban"></i>
                </button>
                ` : ''
            }
            </div>
            </td>
            </tr>
            `).join('')
        }
        </tbody>
        </table>
        </div>
        `;
        container.innerHTML = tableHtml;
        // 初始化tooltip
        const tooltips = container.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(tooltip => {
            new bootstrap.Tooltip(tooltip);
        });
    }
    // 加载最近事件
    async loadRecentEvents() {
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=recent&limit=10`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderRecentEvents(result.data.events);
            }
        } catch (error) {
            console.error('加载最近事件失败:',
            error);
        }
    }
    // 渲染最近事件
    renderRecentEvents(events) {
        const container = document.getElementById('recentEventsContainer');
        if (!container) return;
        if (!events || events.length === 0) {
            container.innerHTML = '<div class="text-center text-muted py-3">暂无最近事件</div>';
            return;
        }
        const eventsHtml = events.map(event => `
        <div class="d-flex align-items-start mb-3 ${
            this.getEventAlertClass(event.risk_level)
        }">
        <div class="flex-shrink-0 me-3">
        <div class="event-icon ${
            this.getRiskLevelBadgeClass(event.risk_level)
        }">
        <i class="${
            this.getEventTypeIcon(event.event_type)
        }"></i>
        </div>
        </div>
        <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start">
        <div>
        <h6 class="mb-1">${
            this.getEventTypeText(event.event_type)
        }</h6>
        <p class="mb-1 small">${
            event.description
        }</p>
        <small class="text-muted">
        <i class="bi bi-clock me-1"></i>${
            this.formatTimeAgo(event.created_at)
        }
        ${
            event.ip_address ? `<i class="bi bi-geo-alt ms-2 me-1"></i>${
                event.ip_address
            }` : ''
        }
        </small>
        </div>
        <span class="badge ${
            this.getRiskLevelBadgeClass(event.risk_level)
        } ms-2">
        ${
            this.getRiskLevelText(event.risk_level)
        }
        </span>
        </div>
        </div>
        </div>
        `).join('');
        container.innerHTML = eventsHtml;
    }
    // 查看日志详情
    async viewLogDetails(logId) {
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=details&id=${
                logId
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showLogDetailsModal(result.data);
            } else {
                this.showError('获取日志详情失败: ' + result.message);
            }
        } catch (error) {
            console.error('获取日志详情失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 显示日志详情模态框
    showLogDetailsModal(log) {
        const modal = new bootstrap.Modal(document.getElementById('logDetailsModal'));
        const content = document.getElementById('logDetailsContent');
        content.innerHTML = `
        <div class="row">
        <div class="col-md-6">
        <h6 class="text-muted">基本信息</h6>
        <table class="table table-sm">
        <tr><td>事件ID:</td><td><code>${
            log.id
        }</code></td></tr>
        <tr><td>事件类型:</td><td><span class="badge ${
            this.getEventTypeBadgeClass(log.event_type)
        }">${
            this.getEventTypeText(log.event_type)
        }</span></td></tr>
        <tr><td>风险等级:</td><td><span class="badge ${
            this.getRiskLevelBadgeClass(log.risk_level)
        }">${
            this.getRiskLevelText(log.risk_level)
        }</span></td></tr>
        <tr><td>发生时间:</td><td>${
            this.formatDateTime(log.created_at)
        }</td></tr>
        </table>
        </div>
        <div class="col-md-6">
        <h6 class="text-muted">用户和网络信息</h6>
        <table class="table table-sm">
        <tr><td>用户:</td><td>${
            log.username || '系统'
        }</td></tr>
        <tr><td>用户类型:</td><td>${
            log.user_type || '-'
        }</td></tr>
        <tr><td>IP地址:</td><td><code>${
            log.ip_address || '-'
        }</code></td></tr>
        <tr><td>地理位置:</td><td>${
            log.country || '-'
        }</td></tr>
        <tr><td>用户代理:</td><td><small>${
            log.user_agent || '-'
        }</small></td></tr>
        </table>
        </div>
        </div>
        <hr>
        <div class="row">
        <div class="col-12">
        <h6 class="text-muted">事件描述</h6>
        <div class="alert alert-light">
        ${
            log.description
        }
        </div>
        ${
            log.details ? `
            <h6 class="text-muted">详细信息</h6>
            <pre class="bg-light p-3 rounded"><code>${
                JSON.stringify(JSON.parse(log.details || '{
                }'),
                null, 2)
            }</code></pre>
            ` : ''
        }
        ${
            log.related_logs && log.related_logs.length > 0 ? `
            <h6 class="text-muted">相关日志</h6>
            <div class="list-group">
            ${
                log.related_logs.map(relatedLog => `
                <div class="list-group-item">
                <div class="d-flex justify-content-between">
                <span>${
                    relatedLog.description
                }</span>
                <small class="text-muted">${
                    this.formatDateTime(relatedLog.created_at)
                }</small>
                </div>
                </div>
                `).join('')
            }
            </div>
            ` : ''
        }
        </div>
        </div>
        `;
        modal.show();
    }
    // 处理安全告警
    async handleSecurityAlert(logId) {
        const modal = new bootstrap.Modal(document.getElementById('securityAlertModal'));
        const content = document.getElementById('alertHandleContent');
        content.innerHTML = `
        <div class="alert alert-warning">
        <h6 class="alert-heading">⚠️ 安全告警处理</h6>
        <p class="mb-0">您正在处理一个安全告警事件，请选择适当的处理措施：</p>
        </div>
        <div class="mb-3">
        <label class="form-label">处理状态</label>
        <select class="form-select" id="alertHandleStatus">
        <option value="investigating">调查中</option>
        <option value="confirmed">已确认</option>
        <option value="false_positive">误报</option>
        <option value="resolved">已解决</option>
        </select>
        </div>
        <div class="mb-3">
        <label class="form-label">处理备注</label>
        <textarea class="form-control" id="alertHandleNote" rows="3" placeholder="请输入处理备注..."></textarea>
        </div>
        <div class="mb-3">
        <div class="form-check">
        <input class="form-check-input" type="checkbox" id="alertBlockIP">
        <label class="form-check-label" for="alertBlockIP">
        同时封禁相关IP地址
        </label>
        </div>
        <div class="form-check">
        <input class="form-check-input" type="checkbox" id="alertNotifyAdmin">
        <label class="form-check-label" for="alertNotifyAdmin">
        通知其他管理员
        </label>
        </div>
        </div>
        `;
        // 设置确认按钮事件
        const confirmBtn = document.getElementById('confirmAlertHandle');
        if (confirmBtn) {
            confirmBtn.onclick = () => this.confirmHandleAlert(logId);
        }
        modal.show();
    }
    // 确认处理告警
    async confirmHandleAlert(logId) {
        const status = document.getElementById('alertHandleStatus')?.value;
        const note = document.getElementById('alertHandleNote')?.value;
        const blockIP = document.getElementById('alertBlockIP')?.checked;
        const notifyAdmin = document.getElementById('alertNotifyAdmin')?.checked;
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=handle_alert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    log_id: logId,
                    status: status,
                    note: note,
                    block_ip: blockIP,
                    notify_admin: notifyAdmin
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('安全告警处理成功');
                bootstrap.Modal.getInstance(document.getElementById('securityAlertModal')).hide();
                this.loadSecurityLogs();
            } else {
                this.showError('处理失败: ' + result.message);
            }
        } catch (error) {
            console.error('处理安全告警失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 封禁IP
    async blockIP(ipAddress) {
        if (!confirm(`确定要封禁IP地址 ${
            ipAddress
        } 吗？`)) return;
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=block_ip`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    ip_address: ipAddress
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess(`IP地址 ${
                    ipAddress
                } 已封禁`);
            } else {
                this.showError('封禁失败: ' + result.message);
            }
        } catch (error) {
            console.error('封禁IP失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 导出安全日志
    async exportSecurityLogs() {
        try {
            const params = new URLSearchParams(this.currentFilters);
            window.open(`${
                this.apiUrl
            }&action=export&${
                params
            }`, '_blank');
        } catch (error) {
            console.error('导出安全日志失败:',
            error);
            this.showError('导出失败，请重试');
        }
    }
    // 搜索日志
    searchLogs() {
        const searchTerm = document.getElementById('logSearchInput')?.value.trim();
        this.currentFilters.search = searchTerm || undefined;
        this.currentPage = 1;
        this.loadSecurityLogs();
    }
    // 应用筛选器
    applyFilters() {
        const eventTypeFilter = document.getElementById('logEventTypeFilter')?.value;
        const riskLevelFilter = document.getElementById('logRiskLevelFilter')?.value;
        const timeRangeFilter = document.getElementById('logTimeRangeFilter')?.value;
        const userTypeFilter = document.getElementById('logUserTypeFilter')?.value;
        this.currentFilters = {
            ...this.currentFilters,
            event_type: eventTypeFilter || undefined,
            risk_level: riskLevelFilter || undefined,
            time_range: timeRangeFilter || undefined,
            user_type: userTypeFilter || undefined
        };
        this.currentPage = 1;
        this.loadSecurityLogs();
    }
    // 清除筛选
    clearFilters() {
        this.currentFilters = {
        };
        this.currentPage = 1;
        // 清除筛选器UI
        ['logEventTypeFilter', 'logRiskLevelFilter', 'logTimeRangeFilter', 'logUserTypeFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });
        document.getElementById('logSearchInput').value = '';
        this.loadSecurityLogs();
    }
    // 开启/关闭实时监控
    toggleRealTimeMode(enabled) {
        this.isRealTimeMode = enabled;
        if (enabled) {
            this.startRealTimeUpdates();
            this.showSuccess('实时监控已开启');
        } else {
            this.stopRealTimeUpdates();
            this.showSuccess('实时监控已关闭');
        }
    }
    // 开始实时更新
    startRealTimeUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        this.refreshInterval = setInterval(() => {
            if (this.isRealTimeMode) {
                this.loadRecentEvents();
                this.loadSecurityStats();
            }
        }, 10000);
        // 每10秒更新一次
    }
    // 停止实时更新
    stopRealTimeUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    // 刷新数据
    refreshData() {
        this.loadSecurityStats();
        this.loadSecurityLogs();
        this.loadRecentEvents();
        this.showSuccess('数据已刷新');
    }
    // 渲染分页
    renderPagination(pagination) {
        const container = document.getElementById('logPagination');
        if (!container || !pagination) return;
        const {
            current_page,
            total_pages
        } = pagination;
        if (total_pages <= 1) {
            container.innerHTML = '';
            return;
        }
        let paginationHtml = '<nav><ul class="pagination">';
        // 上一页
        paginationHtml += `
        <li class="page-item ${
            current_page === 1 ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.securityLogManager.goToPage(${
            current_page - 1
        })">上一页</a>
        </li>
        `;
        // 页码
        const start = Math.max(1,
        current_page - 2);
        const end = Math.min(total_pages,
        current_page + 2);
        for (let i = start;
        i <= end;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === current_page ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.securityLogManager.goToPage(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        // 下一页
        paginationHtml += `
        <li class="page-item ${
            current_page === total_pages ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.securityLogManager.goToPage(${
            current_page + 1
        })">下一页</a>
        </li>
        `;
        paginationHtml += '</ul></nav>';
        container.innerHTML = paginationHtml;
    }
    // 跳转到指定页
    goToPage(page) {
        this.currentPage = page;
        this.loadSecurityLogs();
    }
    // 更新日志数量显示
    updateLogCount(count) {
        const element = document.getElementById('logCount');
        if (element) {
            element.textContent = this.formatNumber(count);
        }
    }
    // 更新风险分布图表
    updateRiskDistributionChart(distribution) {
        // 这里可以集成Chart.js来显示风险等级分布饼图
        const container = document.getElementById('riskDistributionChart');
        if (container) {
            const chartHtml = Object.keys(distribution).map(level => `
            <div class="d-flex justify-content-between align-items-center mb-2">
            <span class="badge ${
                this.getRiskLevelBadgeClass(level)
            }">${
                this.getRiskLevelText(level)
            }</span>
            <span class="fw-bold">${
                distribution[level]
            }</span>
            </div>
            `).join('');
            container.innerHTML = chartHtml;
        }
    }
    // 工具函数
    formatNumber(num) {
        if (num === null || num === undefined) return '0';
        return Number(num).toLocaleString();
    }
    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';
        return new Date(dateTimeStr).toLocaleString('zh-CN');
    }
    formatTimeAgo(dateTimeStr) {
        if (!dateTimeStr) return '-';
        const now = new Date();
        const time = new Date(dateTimeStr);
        const diff = now - time;
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return `${
            Math.floor(diff / 60000)
        }分钟前`;
        if (diff < 86400000) return `${
            Math.floor(diff / 3600000)
        }小时前`;
        return `${
            Math.floor(diff / 86400000)
        }天前`;
    }
    truncateText(text,
    maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0,
        maxLength) + '...';
    }
    getRiskScoreClass(score) {
        if (score >= 80) return 'text-danger fw-bold';
        if (score >= 60) return 'text-warning fw-bold';
        if (score >= 40) return 'text-info fw-bold';
        return 'text-success fw-bold';
    }
    getLogRowClass(riskLevel) {
        const classes = {
            'critical': 'table-danger',
            'high': 'table-warning',
            'medium': '',
            'low': ''
        };
        return classes[riskLevel] || '';
    }
    getEventTypeBadgeClass(eventType) {
        const classes = {
            'login': 'bg-primary',
            'logout': 'bg-secondary',
            'failed_login': 'bg-danger',
            'password_change': 'bg-warning',
            'totp_setup': 'bg-info',
            'totp_verify': 'bg-success',
            'blacklist_add': 'bg-dark',
            'risk_triggered': 'bg-danger',
            'suspicious_activity': 'bg-warning'
        };
        return classes[eventType] || 'bg-secondary';
    }
    getEventTypeText(eventType) {
        const texts = {
            'login': '登录',
            'logout': '登出',
            'failed_login': '登录失败',
            'password_change': '密码修改',
            'totp_setup': 'TOTP设置',
            'totp_verify': 'TOTP验证',
            'blacklist_add': '黑名单添加',
            'risk_triggered': '风控触发',
            'suspicious_activity': '可疑活动'
        };
        return texts[eventType] || eventType;
    }
    getEventTypeIcon(eventType) {
        const icons = {
            'login': 'bi-box-arrow-in-right',
            'logout': 'bi-box-arrow-right',
            'failed_login': 'bi-exclamation-triangle',
            'password_change': 'bi-key',
            'totp_setup': 'bi-shield-plus',
            'totp_verify': 'bi-shield-check',
            'blacklist_add': 'bi-ban',
            'risk_triggered': 'bi-shield-exclamation',
            'suspicious_activity': 'bi-eye'
        };
        return icons[eventType] || 'bi-info-circle';
    }
    getRiskLevelBadgeClass(riskLevel) {
        const classes = {
            'critical': 'bg-danger',
            'high': 'bg-warning',
            'medium': 'bg-info',
            'low': 'bg-success'
        };
        return classes[riskLevel] || 'bg-secondary';
    }
    getRiskLevelText(riskLevel) {
        const texts = {
            'critical': '严重',
            'high': '高',
            'medium': '中',
            'low': '低'
        };
        return texts[riskLevel] || riskLevel;
    }
    getEventAlertClass(riskLevel) {
        if (riskLevel === 'critical' || riskLevel === 'high') {
            return 'border-start border-danger border-3';
        }
        return '';
    }
    showSuccess(message) {
        console.log('Success:',
        message);
        // 这里可以集成toast通知组件
    }
    showError(message) {
        console.error('Error:',
        message);
        // 这里可以集成toast通知组件
    }
    // 销毁
    destroy() {
        this.stopRealTimeUpdates();
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = SecurityLogManager;
} else if (typeof window !== "undefined") {
    window.SecurityLogManager = SecurityLogManager;
}

console.log('📦 SecurityLogManager 模块加载完成');
