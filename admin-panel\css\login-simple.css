/*!
 * 简洁美观的登录界面样式
 */

/* 登录容器 */
#loginContainer {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    padding: 20px;
}

/* 登录卡片 */
.login-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    text-align: center;
}

/* 标题 */
.login-title {
    color: #333;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
}

.login-subtitle {
    color: #666;
    font-size: 16px;
    margin-bottom: 30px;
    font-weight: 400;
}

/* 表单组 */
.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-label {
    color: #333;
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

/* 输入框 */
.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fff;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control::placeholder {
    color: #999;
}

/* 记住我 */
.remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    font-size: 14px;
    color: #666;
}

.remember-me input[type="checkbox"] {
    margin-right: 8px;
}

/* 登录按钮 */
.btn-login {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-login:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-login:active {
    transform: translateY(0);
}

/* 测试信息 */
.test-info {
    margin-top: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 14px;
    color: #666;
    text-align: left;
}

.test-info strong {
    color: #333;
}

/* 错误提示 */
.error-message {
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
}

/* 成功提示 */
.success-message {
    background: #efe;
    border: 1px solid #cfc;
    color: #3c3;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
}

/* 响应式 */
@media (max-width: 480px) {
    .login-card {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .login-title {
        font-size: 24px;
    }
} 