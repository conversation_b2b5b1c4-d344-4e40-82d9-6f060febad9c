<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';

$db = new Database();
$auth = new Auth();

// 路由处理
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'status':
                    handleCheckinStatus();
                    break;
                case 'history':
                    handleCheckinHistory();
                    break;
                case 'overdue_devices':
                    handleOverdueDevices();
                    break;
                case 'stats':
                    handleCheckinStats();
                    break;
                default:
                    throw new Exception('不支持的GET操作');
            }
            break;
            
        case 'POST':
            switch ($action) {
                case 'checkin':
                    handleDeviceCheckin();
                    break;
                case 'batch_remind':
                    handleBatchRemind();
                    break;
                default:
                    throw new Exception('不支持的POST操作');
            }
            break;
            
        default:
            throw new Exception('不支持的HTTP方法');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'error_code' => 400,
        'error_message' => $e->getMessage(),
        'data' => null
    ));
}

// 设备签到
function handleDeviceCheckin() {
    global $db, $auth;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['device_id'])) {
        throw new Exception('缺少设备ID参数');
    }
    
    $deviceId = $input['device_id'];
    $deviceFingerprint = isset($input['device_fingerprint']) ? $input['device_fingerprint'] : '';
    $locationInfo = isset($input['location_info']) ? json_encode($input['location_info']) : '';
    $appVersion = isset($input['app_version']) ? $input['app_version'] : '';
    $systemInfo = isset($input['system_info']) ? json_encode($input['system_info']) : '';
    
    // 验证设备是否存在且为激活状态
    $device = $db->fetch(
        "SELECT d.*, p.company_name as provider_name, g.group_code
         FROM devices d
         LEFT JOIN payment_providers p ON d.provider_id = p.id
         LEFT JOIN provider_groups g ON d.group_id = g.id
         WHERE d.device_id = ? AND d.status = 'active'",
        array($deviceId)
    );
    
    if (!$device) {
        echo json_encode(array(
            'error_code' => 404,
            'error_message' => '设备不存在或未激活',
            'data' => null
        ));
        return;
    }
    
    // 验证设备指纹（如果提供）
    if ($deviceFingerprint && $device['device_fingerprint'] !== $deviceFingerprint) {
        echo json_encode(array(
            'error_code' => 401,
            'error_message' => '设备指纹不匹配',
            'data' => null
        ));
        return;
    }
    
    $currentTime = date('Y-m-d H:i:s');
    $checkinInterval = 7200; // 2小时签到间隔
    $nextCheckinTime = date('Y-m-d H:i:s', time() + $checkinInterval);
    
    $db->beginTransaction();
    try {
        // 更新设备签到信息
        $db->execute(
            "UPDATE devices SET 
                last_checkin_time = ?,
                checkin_status = 'normal',
                last_online = ?,
                app_version = ?,
                system_info = ?
             WHERE device_id = ?",
            array($currentTime, $currentTime, $appVersion, $systemInfo, $deviceId)
        );
        
        // 记录签到日志
        $db->execute(
            "INSERT INTO device_logs (device_id, log_type, log_level, message, extra_data, created_at)
             VALUES (?, 'checkin', 'info', '设备签到成功', ?, NOW())",
            array($device['id'], json_encode(array(
                'location_info' => $locationInfo,
                'app_version' => $appVersion,
                'system_info' => $systemInfo,
                'checkin_time' => $currentTime
            )))
        );
        
        // 如果设备之前是超时状态，记录恢复日志
        if ($device['checkin_status'] === 'overdue') {
            $db->execute(
                "INSERT INTO device_logs (device_id, log_type, log_level, message, created_at)
                 VALUES (?, 'recovery', 'info', '设备签到超时恢复', NOW())",
                array($device['id'])
            );
        }
        
        $db->commit();
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array(
                'device_id' => $deviceId,
                'checkin_time' => $currentTime,
                'next_checkin_time' => $nextCheckinTime,
                'status' => 'normal',
                'message' => '签到成功',
                'checkin_interval' => $checkinInterval
            )
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 查询设备签到状态
function handleCheckinStatus() {
    global $db;
    
    $deviceId = isset($_GET['device_id']) ? $_GET['device_id'] : '';
    
    if ($deviceId) {
        // 查询单个设备状态
        $device = $db->fetch(
            "SELECT d.device_id, d.device_name, d.last_checkin_time, d.checkin_status,
                    d.status, p.company_name as provider_name, g.group_code
             FROM devices d
             LEFT JOIN payment_providers p ON d.provider_id = p.id
             LEFT JOIN provider_groups g ON d.group_id = g.id
             WHERE d.device_id = ?",
            array($deviceId)
        );
        
        if (!$device) {
            throw new Exception('设备不存在');
        }
        
        $checkinInfo = calculateCheckinInfo($device);
        $device['checkin_info'] = $checkinInfo;
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => $device
        ));
    } else {
        // 获取认证用户信息（用于权限控制）
        $token = isset($_GET['token']) ? $_GET['token'] : '';
        $currentUser = null;
        
        if ($token) {
            $authInstance = new Auth();
            $currentUser = $authInstance->getCurrentUser($token);
        }
        
        // 构建查询条件
        $whereClause = "d.status = 'active'";
        $params = array();
        
        if ($currentUser) {
            if ($currentUser['user_type'] === 'provider') {
                $whereClause .= " AND d.provider_id = ?";
                $params[] = $currentUser['profile_id'];
            } elseif ($currentUser['user_type'] === 'employee') {
                $managedGroups = getAllManagedGroups($currentUser['id']);
                if (!empty($managedGroups)) {
                    $placeholders = str_repeat('?,', count($managedGroups) - 1) . '?';
                    $whereClause .= " AND d.group_id IN ($placeholders)";
                    $params = array_merge($params, $managedGroups);
                } else {
                    $whereClause .= " AND 1 = 0";
                }
            }
        }
        
        // 查询所有活跃设备的签到状态
        $devices = $db->fetchAll(
            "SELECT d.device_id, d.device_name, d.last_checkin_time, d.checkin_status,
                    d.status, p.company_name as provider_name, g.group_code
             FROM devices d
             LEFT JOIN payment_providers p ON d.provider_id = p.id
             LEFT JOIN provider_groups g ON d.group_id = g.id
             WHERE $whereClause
             ORDER BY d.last_checkin_time ASC",
            $params
        );
        
        $normalDevices = array();
        $overdueDevices = array();
        $neverCheckinDevices = array();
        
        foreach ($devices as $device) {
            $checkinInfo = calculateCheckinInfo($device);
            $device['checkin_info'] = $checkinInfo;
            
            if ($checkinInfo['status'] === 'never') {
                $neverCheckinDevices[] = $device;
            } elseif ($checkinInfo['is_overdue']) {
                $overdueDevices[] = $device;
            } else {
                $normalDevices[] = $device;
            }
        }
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array(
                'normal_devices' => $normalDevices,
                'overdue_devices' => $overdueDevices,
                'never_checkin_devices' => $neverCheckinDevices,
                'summary' => array(
                    'total_devices' => count($devices),
                    'normal_count' => count($normalDevices),
                    'overdue_count' => count($overdueDevices),
                    'never_checkin_count' => count($neverCheckinDevices)
                )
            )
        ));
    }
}

// 查询签到历史记录
function handleCheckinHistory() {
    global $db;
    
    $deviceId = isset($_GET['device_id']) ? $_GET['device_id'] : '';
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(10, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    if (!$deviceId) {
        throw new Exception('缺少设备ID参数');
    }
    
    // 获取设备信息
    $device = $db->fetch(
        "SELECT id, device_id, device_name FROM devices WHERE device_id = ?",
        array($deviceId)
    );
    
    if (!$device) {
        throw new Exception('设备不存在');
    }
    
    // 查询签到历史记录
    $history = $db->fetchAll(
        "SELECT log_type, log_level, message, extra_data, created_at
         FROM device_logs
         WHERE device_id = ? AND log_type IN ('checkin', 'overdue', 'recovery')
         ORDER BY created_at DESC
         LIMIT $limit OFFSET $offset",
        array($device['id'])
    );
    
    // 查询总数
    $total = $db->fetch(
        "SELECT COUNT(*) as count 
         FROM device_logs 
         WHERE device_id = ? AND log_type IN ('checkin', 'overdue', 'recovery')",
        array($device['id'])
    );
    
    // 处理额外数据
    foreach ($history as &$record) {
        if ($record['extra_data']) {
            $record['extra_data'] = json_decode($record['extra_data'], true);
        }
    }
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'device_info' => $device,
            'history' => $history,
            'pagination' => array(
                'page' => $page,
                'limit' => $limit,
                'total' => intval($total['count']),
                'pages' => ceil($total['count'] / $limit)
            )
        )
    ));
}

// 获取超时设备列表
function handleOverdueDevices() {
    global $db, $auth;
    
    // 获取认证用户信息
    $token = isset($_GET['token']) ? $_GET['token'] : '';
    $currentUser = null;
    
    if ($token) {
        $currentUser = $auth->getCurrentUser($token);
    }
    
    // 构建查询条件
    $whereClause = "d.status = 'active'";
    $params = array();
    
    if ($currentUser) {
        if ($currentUser['user_type'] === 'provider') {
            $whereClause .= " AND d.provider_id = ?";
            $params[] = $currentUser['profile_id'];
        } elseif ($currentUser['user_type'] === 'employee') {
            $managedGroups = getAllManagedGroups($currentUser['id']);
            if (!empty($managedGroups)) {
                $placeholders = str_repeat('?,', count($managedGroups) - 1) . '?';
                $whereClause .= " AND d.group_id IN ($placeholders)";
                $params = array_merge($params, $managedGroups);
            } else {
                $whereClause .= " AND 1 = 0";
            }
        }
    }
    
    // 查询所有设备
    $devices = $db->fetchAll(
        "SELECT d.device_id, d.device_name, d.device_brand, d.device_model,
                d.last_checkin_time, d.checkin_status, d.status,
                p.company_name as provider_name, g.group_code,
                u1.real_name as team_leader_name, u2.real_name as group_manager_name
         FROM devices d
         LEFT JOIN payment_providers p ON d.provider_id = p.id
         LEFT JOIN provider_groups g ON d.group_id = g.id
         LEFT JOIN users u1 ON g.team_leader_id = u1.id
         LEFT JOIN users u2 ON g.group_manager_id = u2.id
         WHERE $whereClause
         ORDER BY d.last_checkin_time ASC",
        $params
    );
    
    $overdueDevices = array();
    $criticalOverdueDevices = array(); // 超时超过4小时的设备
    
    foreach ($devices as $device) {
        $checkinInfo = calculateCheckinInfo($device);
        
        if ($checkinInfo['is_overdue']) {
            $device['checkin_info'] = $checkinInfo;
            $overdueDevices[] = $device;
            
            // 标记严重超时设备（超过4小时）
            if (isset($checkinInfo['overdue_minutes']) && $checkinInfo['overdue_minutes'] > 240) {
                $criticalOverdueDevices[] = $device;
            }
        }
    }
    
    // 更新数据库中的超时状态
    updateOverdueStatus($overdueDevices);
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'overdue_devices' => $overdueDevices,
            'critical_overdue_devices' => $criticalOverdueDevices,
            'summary' => array(
                'total_overdue' => count($overdueDevices),
                'critical_overdue' => count($criticalOverdueDevices)
            )
        )
    ));
}

// 获取签到统计信息
function handleCheckinStats() {
    global $db, $auth;
    
    // 获取认证用户信息
    $token = isset($_GET['token']) ? $_GET['token'] : '';
    $currentUser = null;
    
    if ($token) {
        $currentUser = $auth->getCurrentUser($token);
    }
    
    // 时间范围参数
    $startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-7 days'));
    $endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
    
    // 构建查询条件
    $whereClause = "d.status = 'active'";
    $params = array();
    
    if ($currentUser) {
        if ($currentUser['user_type'] === 'provider') {
            $whereClause .= " AND d.provider_id = ?";
            $params[] = $currentUser['profile_id'];
        } elseif ($currentUser['user_type'] === 'employee') {
            $managedGroups = getAllManagedGroups($currentUser['id']);
            if (!empty($managedGroups)) {
                $placeholders = str_repeat('?,', count($managedGroups) - 1) . '?';
                $whereClause .= " AND d.group_id IN ($placeholders)";
                $params = array_merge($params, $managedGroups);
            } else {
                $whereClause .= " AND 1 = 0";
            }
        }
    }
    
    // 当前状态统计
    $currentStats = $db->fetchAll(
        "SELECT d.checkin_status, COUNT(*) as count
         FROM devices d
         WHERE $whereClause
         GROUP BY d.checkin_status",
        $params
    );
    
    // 按日期统计签到次数
    $dailyStats = $db->fetchAll(
        "SELECT DATE(dl.created_at) as checkin_date, COUNT(*) as checkin_count
         FROM device_logs dl
         JOIN devices d ON dl.device_id = d.id
         WHERE $whereClause AND dl.log_type = 'checkin' 
         AND DATE(dl.created_at) BETWEEN ? AND ?
         GROUP BY DATE(dl.created_at)
         ORDER BY checkin_date ASC",
        array_merge($params, array($startDate, $endDate))
    );
    
    // 按小组统计
    $groupStats = array();
    if ($currentUser && $currentUser['user_type'] === 'provider') {
        $groupStats = $db->fetchAll(
            "SELECT g.group_code, g.id as group_id,
                    COUNT(d.id) as total_devices,
                    COUNT(CASE WHEN d.checkin_status = 'normal' THEN 1 END) as normal_devices,
                    COUNT(CASE WHEN d.checkin_status = 'overdue' THEN 1 END) as overdue_devices,
                    COUNT(CASE WHEN d.last_checkin_time IS NULL THEN 1 END) as never_checkin_devices
             FROM provider_groups g
             LEFT JOIN devices d ON g.id = d.group_id AND d.status = 'active'
             WHERE g.provider_id = ?
             GROUP BY g.id
             ORDER BY overdue_devices DESC, total_devices DESC",
            array($currentUser['profile_id'])
        );
    }
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'current_stats' => $currentStats,
            'daily_stats' => $dailyStats,
            'group_stats' => $groupStats,
            'date_range' => array(
                'start_date' => $startDate,
                'end_date' => $endDate
            )
        )
    ));
}

// 批量提醒超时设备
function handleBatchRemind() {
    global $db, $auth;
    
    // 获取认证用户信息
    $token = $auth->getAuthToken();
    $currentUser = $auth->getCurrentUser($token);
    
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(array(
            'error_code' => 401,
            'error_message' => '未授权访问',
            'data' => null
        ));
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $deviceIds = isset($input['device_ids']) ? $input['device_ids'] : array();
    $remindMessage = isset($input['message']) ? trim($input['message']) : '请及时签到';
    
    if (empty($deviceIds)) {
        throw new Exception('设备ID列表不能为空');
    }
    
    $successCount = 0;
    $failedDevices = array();
    
    foreach ($deviceIds as $deviceId) {
        try {
            // 获取设备信息
            $device = $db->fetch(
                "SELECT id, device_id, device_name FROM devices WHERE device_id = ?",
                array($deviceId)
            );
            
            if ($device) {
                // 记录提醒日志
                $db->execute(
                    "INSERT INTO device_logs (device_id, user_id, log_type, log_level, message, created_at)
                     VALUES (?, ?, 'remind', 'warning', ?, NOW())",
                    array($device['id'], $currentUser['id'], "签到提醒: $remindMessage")
                );
                
                $successCount++;
            } else {
                $failedDevices[] = $deviceId . ' (设备不存在)';
            }
        } catch (Exception $e) {
            $failedDevices[] = $deviceId . ' (' . $e->getMessage() . ')';
        }
    }
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'success_count' => $successCount,
            'total_count' => count($deviceIds),
            'failed_devices' => $failedDevices,
            'message' => "成功提醒 $successCount 台设备"
        )
    ));
}

// 辅助函数：计算签到信息
function calculateCheckinInfo($device) {
    $checkinInterval = 7200; // 2小时 = 7200秒
    $currentTime = time();
    
    if (!$device['last_checkin_time']) {
        return array(
            'status' => 'never',
            'is_overdue' => true,
            'next_checkin_time' => null,
            'overdue_minutes' => null,
            'remaining_minutes' => null,
            'message' => '从未签到'
        );
    }
    
    $lastCheckinTime = strtotime($device['last_checkin_time']);
    $nextCheckinTime = $lastCheckinTime + $checkinInterval;
    
    if ($currentTime > $nextCheckinTime) {
        $overdueSeconds = $currentTime - $nextCheckinTime;
        $overdueMinutes = floor($overdueSeconds / 60);
        $overdueHours = floor($overdueMinutes / 60);
        
        $message = '';
        if ($overdueHours > 0) {
            $message = "已超时 {$overdueHours} 小时 " . ($overdueMinutes % 60) . " 分钟";
        } else {
            $message = "已超时 {$overdueMinutes} 分钟";
        }
        
        return array(
            'status' => 'overdue',
            'is_overdue' => true,
            'next_checkin_time' => date('Y-m-d H:i:s', $nextCheckinTime),
            'overdue_minutes' => $overdueMinutes,
            'remaining_minutes' => null,
            'message' => $message
        );
    } else {
        $remainingSeconds = $nextCheckinTime - $currentTime;
        $remainingMinutes = floor($remainingSeconds / 60);
        $remainingHours = floor($remainingMinutes / 60);
        
        $message = '';
        if ($remainingHours > 0) {
            $message = "还有 {$remainingHours} 小时 " . ($remainingMinutes % 60) . " 分钟需签到";
        } else {
            $message = "还有 {$remainingMinutes} 分钟需签到";
        }
        
        return array(
            'status' => 'normal',
            'is_overdue' => false,
            'next_checkin_time' => date('Y-m-d H:i:s', $nextCheckinTime),
            'overdue_minutes' => null,
            'remaining_minutes' => $remainingMinutes,
            'message' => $message
        );
    }
}

// 辅助函数：更新超时状态
function updateOverdueStatus($overdueDevices) {
    global $db;
    
    if (empty($overdueDevices)) {
        return;
    }
    
    $deviceIds = array();
    foreach ($overdueDevices as $device) {
        $deviceIds[] = $device['device_id'];
    }
    
    // 批量更新超时状态
    $placeholders = str_repeat('?,', count($deviceIds) - 1) . '?';
    $db->execute(
        "UPDATE devices SET checkin_status = 'overdue' WHERE device_id IN ($placeholders) AND checkin_status != 'overdue'",
        $deviceIds
    );
    
    // 记录超时日志
    foreach ($overdueDevices as $device) {
        if ($device['checkin_status'] !== 'overdue') {
            $db->execute(
                "INSERT INTO device_logs (device_id, log_type, log_level, message, created_at)
                 SELECT id, 'overdue', 'warning', '设备签到超时', NOW()
                 FROM devices WHERE device_id = ?",
                array($device['device_id'])
            );
        }
    }
}

// 辅助函数：获取管理的小组ID列表
function getAllManagedGroups($userId) {
    global $db;
    
    $position = getEmployeePosition($userId);
    
    if ($position === 'team_leader') {
        $groups = $db->fetchAll(
            "SELECT id FROM provider_groups WHERE team_leader_id = ?",
            array($userId)
        );
        return array_column($groups, 'id');
    } elseif ($position === 'group_manager') {
        $groups = $db->fetchAll(
            "SELECT id FROM provider_groups WHERE group_manager_id = ?",
            array($userId)
        );
        return array_column($groups, 'id');
    }
    
    return array();
}

// 辅助函数：获取员工职位
function getEmployeePosition($userId) {
    global $db;
    
    $result = $db->fetch(
        "SELECT jp.position_name FROM users u
         LEFT JOIN job_positions jp ON u.job_position_id = jp.id
         WHERE u.id = ?",
        array($userId)
    );
    
    return $result ? $result['position_name'] : null;
}

?> 