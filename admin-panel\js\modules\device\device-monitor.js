/**
 * 设备实时监控模块
 * 提供设备在线状态监控、性能监控、健康检查和故障预警功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class DeviceMonitor {
    constructor() {
        this.apiBase = '/api/device';
        this.refreshInterval = 30000; // 30秒刷新间隔
        this.refreshTimer = null;
        this.isMonitoring = false;
        this.monitoringData = {};
        this.alertThresholds = {
            offlineTimeout: 300, // 5分钟离线超时
            batteryLow: 20, // 电量低于20%
            memoryHigh: 80, // 内存使用率高于80%
            cpuHigh: 85, // CPU使用率高于85%
            temperatureHigh: 70 // 温度高于70°C
        };
        this.alerts = [];
        
        console.log('DeviceMonitor initialized');
    }

    /**
     * 初始化监控系统
     */
    async initialize() {
        try {
            console.log('🔧 初始化设备监控系统...');
            
            // 加载监控界面
            await this.loadMonitorInterface();
            
            // 启动实时监控
            await this.startMonitoring();
            
            // 绑定事件监听
            this.bindEvents();
            
            console.log('✅ 设备监控系统初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 设备监控系统初始化失败:', error);
            return false;
        }
    }

    /**
     * 加载监控界面
     */
    async loadMonitorInterface() {
        const container = document.getElementById('main-content');
        if (!container) return;

        container.innerHTML = this.getMonitorHTML();
    }

    /**
     * 获取监控界面HTML
     */
    getMonitorHTML() {
        return `
            <div class="device-monitor-container">
                <!-- 监控头部 -->
                <div class="monitor-header">
                    <div class="row">
                        <div class="col-md-8">
                            <h2><i class="bi bi-activity"></i> 设备实时监控</h2>
                            <p class="text-muted">实时监控设备状态、性能指标和健康状况</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="monitor-controls">
                                <button class="btn btn-success" id="startMonitoring">
                                    <i class="bi bi-play-circle"></i> 开始监控
                                </button>
                                <button class="btn btn-warning" id="pauseMonitoring" style="display: none;">
                                    <i class="bi bi-pause-circle"></i> 暂停监控
                                </button>
                                <button class="btn btn-info" id="refreshMonitor">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 监控统计卡片 -->
                <div class="monitor-stats-row">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-card online-devices">
                                <div class="stat-icon">
                                    <i class="bi bi-wifi"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="onlineDevicesCount">0</div>
                                    <div class="stat-label">在线设备</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card offline-devices">
                                <div class="stat-icon">
                                    <i class="bi bi-wifi-off"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="offlineDevicesCount">0</div>
                                    <div class="stat-label">离线设备</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card warning-devices">
                                <div class="stat-icon">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="warningDevicesCount">0</div>
                                    <div class="stat-label">告警设备</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card error-devices">
                                <div class="stat-icon">
                                    <i class="bi bi-x-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number" id="errorDevicesCount">0</div>
                                    <div class="stat-label">故障设备</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 告警面板 -->
                <div class="alert-panel" id="alertPanel" style="display: none;">
                    <div class="alert-header">
                        <h5><i class="bi bi-bell"></i> 系统告警</h5>
                        <button class="btn btn-sm btn-outline-secondary" id="clearAlerts">清除所有</button>
                    </div>
                    <div class="alert-list" id="alertList"></div>
                </div>

                <!-- 设备监控列表 -->
                <div class="monitor-content">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-0">设备监控列表</h5>
                                </div>
                                <div class="col-auto">
                                    <div class="monitor-filters">
                                        <select class="form-select form-select-sm" id="statusFilter">
                                            <option value="">全部状态</option>
                                            <option value="online">在线</option>
                                            <option value="offline">离线</option>
                                            <option value="warning">告警</option>
                                            <option value="error">故障</option>
                                        </select>
                                        <select class="form-select form-select-sm" id="groupFilter">
                                            <option value="">全部小组</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>设备ID</th>
                                            <th>设备名称</th>
                                            <th>在线状态</th>
                                            <th>性能指标</th>
                                            <th>健康状态</th>
                                            <th>最后活动</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="monitorTableBody">
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                <div class="loading-spinner">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">加载中...</span>
                                                    </div>
                                                    <div class="mt-2">正在加载监控数据...</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设备详情监控模态框 -->
                <div class="modal fade" id="deviceDetailModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">设备详细监控</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body" id="deviceDetailContent">
                                <!-- 设备详情内容将在此处动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            ${this.getMonitorCSS()}
        `;
    }

    /**
     * 获取监控样式CSS
     */
    getMonitorCSS() {
        return `
            <style>
                .device-monitor-container {
                    padding: 20px;
                }

                .monitor-header {
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 1px solid #e9ecef;
                }

                .monitor-controls .btn {
                    margin-left: 10px;
                }

                .monitor-stats-row {
                    margin-bottom: 30px;
                }

                .stat-card {
                    background: white;
                    border-radius: 10px;
                    padding: 20px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    display: flex;
                    align-items: center;
                    margin-bottom: 20px;
                }

                .stat-icon {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 24px;
                    margin-right: 20px;
                }

                .online-devices .stat-icon {
                    background: #d4edda;
                    color: #155724;
                }

                .offline-devices .stat-icon {
                    background: #f8d7da;
                    color: #721c24;
                }

                .warning-devices .stat-icon {
                    background: #fff3cd;
                    color: #856404;
                }

                .error-devices .stat-icon {
                    background: #f8d7da;
                    color: #721c24;
                }

                .stat-number {
                    font-size: 28px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }

                .stat-label {
                    color: #6c757d;
                    font-size: 14px;
                }

                .alert-panel {
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 8px;
                    padding: 20px;
                    margin-bottom: 30px;
                }

                .alert-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;
                }

                .alert-item {
                    background: white;
                    border-radius: 5px;
                    padding: 15px;
                    margin-bottom: 10px;
                    border-left: 4px solid #dc3545;
                }

                .alert-item.warning {
                    border-left-color: #ffc107;
                }

                .monitor-filters {
                    display: flex;
                    gap: 10px;
                }

                .monitor-filters .form-select {
                    width: auto;
                    min-width: 120px;
                }

                .status-badge {
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .status-online {
                    background: #d4edda;
                    color: #155724;
                }

                .status-offline {
                    background: #f8d7da;
                    color: #721c24;
                }

                .status-warning {
                    background: #fff3cd;
                    color: #856404;
                }

                .status-error {
                    background: #f8d7da;
                    color: #721c24;
                }

                .performance-indicators {
                    display: flex;
                    gap: 10px;
                }

                .performance-indicator {
                    background: #f8f9fa;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 11px;
                    min-width: 50px;
                    text-align: center;
                }

                .performance-indicator.good {
                    background: #d4edda;
                    color: #155724;
                }

                .performance-indicator.warning {
                    background: #fff3cd;
                    color: #856404;
                }

                .performance-indicator.critical {
                    background: #f8d7da;
                    color: #721c24;
                }

                .health-status {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                }

                .health-icon {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                }

                .health-good {
                    background: #28a745;
                }

                .health-warning {
                    background: #ffc107;
                }

                .health-critical {
                    background: #dc3545;
                }

                .loading-spinner {
                    padding: 40px;
                    text-align: center;
                }

                .performance-metric {
                    text-align: center;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    margin-bottom: 15px;
                }

                .metric-value {
                    font-size: 24px;
                    font-weight: bold;
                    margin: 10px 0;
                }

                .chart-placeholder {
                    height: 200px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            </style>
        `;
    }

    /**
     * 绑定事件监听
     */
    bindEvents() {
        // 监控控制按钮
        document.getElementById('startMonitoring')?.addEventListener('click', () => {
            this.startMonitoring();
        });

        document.getElementById('pauseMonitoring')?.addEventListener('click', () => {
            this.pauseMonitoring();
        });

        document.getElementById('refreshMonitor')?.addEventListener('click', () => {
            this.refreshMonitorData();
        });

        // 清除告警
        document.getElementById('clearAlerts')?.addEventListener('click', () => {
            this.clearAllAlerts();
        });

        // 筛选器
        document.getElementById('statusFilter')?.addEventListener('change', (e) => {
            this.filterDevices('status', e.target.value);
        });

        document.getElementById('groupFilter')?.addEventListener('change', (e) => {
            this.filterDevices('group', e.target.value);
        });
    }

    /**
     * 开始监控
     */
    async startMonitoring() {
        if (this.isMonitoring) return;

        try {
            this.isMonitoring = true;
            
            // 更新按钮状态
            document.getElementById('startMonitoring').style.display = 'none';
            document.getElementById('pauseMonitoring').style.display = 'inline-block';
            
            // 立即加载一次数据
            await this.refreshMonitorData();
            
            // 设置定时刷新
            this.refreshTimer = setInterval(() => {
                this.refreshMonitorData();
            }, this.refreshInterval);
            
            this.showSuccess('监控已启动');
            console.log('✅ 设备监控已启动');
        } catch (error) {
            console.error('启动监控失败:', error);
            this.showError('启动监控失败');
        }
    }

    /**
     * 暂停监控
     */
    pauseMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;
        
        // 清除定时器
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
        
        // 更新按钮状态
        document.getElementById('startMonitoring').style.display = 'inline-block';
        document.getElementById('pauseMonitoring').style.display = 'none';
        
        this.showInfo('监控已暂停');
        console.log('⏸️ 设备监控已暂停');
    }

    /**
     * 刷新监控数据
     */
    async refreshMonitorData() {
        try {
            console.log('🔄 刷新监控数据...');
            
            // 加载设备监控数据
            const response = await this.makeApiRequest('/monitor.php?action=realtime_status', 'GET');
            
            if (response.error_code === 0) {
                this.monitoringData = response.data;
                
                // 更新统计数据
                this.updateStatistics();
                
                // 更新设备列表
                this.updateDeviceList();
                
                // 检查告警
                this.checkAlerts();
                
                console.log('✅ 监控数据刷新完成');
            } else {
                throw new Error(response.error_message);
            }
        } catch (error) {
            console.error('刷新监控数据失败:', error);
            this.showError('刷新监控数据失败: ' + error.message);
        }
    }

    /**
     * 更新统计数据
     */
    updateStatistics() {
        const stats = this.monitoringData.statistics || {};
        
        document.getElementById('onlineDevicesCount').textContent = stats.online || 0;
        document.getElementById('offlineDevicesCount').textContent = stats.offline || 0;
        document.getElementById('warningDevicesCount').textContent = stats.warning || 0;
        document.getElementById('errorDevicesCount').textContent = stats.error || 0;
    }

    /**
     * 更新设备列表
     */
    updateDeviceList() {
        const devices = this.monitoringData.devices || [];
        const tbody = document.getElementById('monitorTableBody');
        
        if (!tbody) return;
        
        if (devices.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                        <div>暂无设备监控数据</div>
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = devices.map(device => `
            <tr>
                <td>
                    <span class="device-id">${device.device_id}</span>
                </td>
                <td>
                    <div class="device-name">${device.device_name || '-'}</div>
                    <small class="text-muted">${device.group_name || '未分组'}</small>
                </td>
                <td>
                    <span class="status-badge status-${device.online_status}">${this.getOnlineStatusText(device.online_status)}</span>
                </td>
                <td>
                    <div class="performance-indicators">
                        ${this.renderPerformanceIndicators(device.performance)}
                    </div>
                </td>
                <td>
                    <div class="health-status">
                        <div class="health-icon health-${device.health_status}"></div>
                        <span>${this.getHealthStatusText(device.health_status)}</span>
                    </div>
                </td>
                <td>${this.formatDateTime(device.last_activity)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="deviceMonitor.viewDeviceDetail('${device.device_id}')" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="deviceMonitor.pingDevice('${device.device_id}')" title="Ping测试">
                        <i class="bi bi-wifi"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 渲染性能指标
     */
    renderPerformanceIndicators(performance) {
        if (!performance) return '<span class="text-muted">-</span>';
        
        const indicators = [];
        
        // CPU使用率
        if (performance.cpu !== undefined) {
            const cpuClass = performance.cpu > this.alertThresholds.cpuHigh ? 'critical' : 
                           performance.cpu > 70 ? 'warning' : 'good';
            indicators.push(`<div class="performance-indicator ${cpuClass}">CPU ${performance.cpu}%</div>`);
        }
        
        // 内存使用率
        if (performance.memory !== undefined) {
            const memClass = performance.memory > this.alertThresholds.memoryHigh ? 'critical' : 
                           performance.memory > 60 ? 'warning' : 'good';
            indicators.push(`<div class="performance-indicator ${memClass}">MEM ${performance.memory}%</div>`);
        }
        
        // 电池电量
        if (performance.battery !== undefined) {
            const battClass = performance.battery < this.alertThresholds.batteryLow ? 'critical' : 
                            performance.battery < 40 ? 'warning' : 'good';
            indicators.push(`<div class="performance-indicator ${battClass}">BAT ${performance.battery}%</div>`);
        }
        
        return indicators.join('');
    }

    /**
     * 检查告警
     */
    checkAlerts() {
        const devices = this.monitoringData.devices || [];
        const newAlerts = [];
        
        devices.forEach(device => {
            // 离线告警
            if (device.online_status === 'offline') {
                const offlineTime = this.calculateOfflineTime(device.last_activity);
                if (offlineTime > this.alertThresholds.offlineTimeout) {
                    newAlerts.push({
                        id: `offline_${device.device_id}`,
                        type: 'error',
                        device_id: device.device_id,
                        device_name: device.device_name,
                        message: `设备已离线 ${Math.floor(offlineTime / 60)} 分钟`,
                        timestamp: new Date()
                    });
                }
            }
            
            // 性能告警
            if (device.performance) {
                const perf = device.performance;
                
                if (perf.cpu > this.alertThresholds.cpuHigh) {
                    newAlerts.push({
                        id: `cpu_${device.device_id}`,
                        type: 'warning',
                        device_id: device.device_id,
                        device_name: device.device_name,
                        message: `CPU使用率过高: ${perf.cpu}%`,
                        timestamp: new Date()
                    });
                }
                
                if (perf.memory > this.alertThresholds.memoryHigh) {
                    newAlerts.push({
                        id: `memory_${device.device_id}`,
                        type: 'warning',
                        device_id: device.device_id,
                        device_name: device.device_name,
                        message: `内存使用率过高: ${perf.memory}%`,
                        timestamp: new Date()
                    });
                }
                
                if (perf.battery < this.alertThresholds.batteryLow) {
                    newAlerts.push({
                        id: `battery_${device.device_id}`,
                        type: 'warning',
                        device_id: device.device_id,
                        device_name: device.device_name,
                        message: `电池电量不足: ${perf.battery}%`,
                        timestamp: new Date()
                    });
                }
            }
        });
        
        // 更新告警列表
        this.updateAlerts(newAlerts);
    }

    /**
     * 更新告警列表
     */
    updateAlerts(newAlerts) {
        // 去重和合并告警
        const alertMap = new Map();
        
        // 保留现有告警
        this.alerts.forEach(alert => {
            alertMap.set(alert.id, alert);
        });
        
        // 添加新告警
        newAlerts.forEach(alert => {
            alertMap.set(alert.id, alert);
        });
        
        this.alerts = Array.from(alertMap.values());
        
        // 显示/隐藏告警面板
        const alertPanel = document.getElementById('alertPanel');
        const alertList = document.getElementById('alertList');
        
        if (this.alerts.length > 0) {
            alertPanel.style.display = 'block';
            alertList.innerHTML = this.alerts.map(alert => `
                <div class="alert-item ${alert.type}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <strong>${alert.device_name}</strong> (${alert.device_id})
                            <div class="text-muted">${alert.message}</div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">${this.formatDateTime(alert.timestamp)}</small>
                            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="deviceMonitor.dismissAlert('${alert.id}')">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        } else {
            alertPanel.style.display = 'none';
        }
    }

    /**
     * 查看设备详情
     */
    async viewDeviceDetail(deviceId) {
        try {
            const response = await this.makeApiRequest(`/monitor.php?action=device_detail&device_id=${deviceId}`, 'GET');
            
            if (response.error_code === 0) {
                this.renderDeviceDetail(response.data);
                const modal = new bootstrap.Modal(document.getElementById('deviceDetailModal'));
                modal.show();
            } else {
                this.showError('获取设备详情失败: ' + response.error_message);
            }
        } catch (error) {
            console.error('获取设备详情失败:', error);
            this.showError('获取设备详情失败');
        }
    }

    /**
     * 渲染设备详情
     */
    renderDeviceDetail(deviceData) {
        const container = document.getElementById('deviceDetailContent');
        if (!container) return;
        
        container.innerHTML = `
            <div class="device-detail-tabs">
                <ul class="nav nav-tabs" id="deviceDetailTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">基本信息</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">性能监控</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">活动日志</button>
                    </li>
                </ul>
                <div class="tab-content" id="deviceDetailTabContent">
                    <div class="tab-pane fade show active" id="basic" role="tabpanel">
                        ${this.renderBasicInfo(deviceData)}
                    </div>
                    <div class="tab-pane fade" id="performance" role="tabpanel">
                        ${this.renderPerformanceChart(deviceData)}
                    </div>
                    <div class="tab-pane fade" id="logs" role="tabpanel">
                        ${this.renderActivityLogs(deviceData)}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染基本信息
     */
    renderBasicInfo(deviceData) {
        const device = deviceData.device;
        return `
            <div class="device-basic-info">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>设备ID:</strong></td>
                                <td>${device.device_id}</td>
                            </tr>
                            <tr>
                                <td><strong>设备名称:</strong></td>
                                <td>${device.device_name || '-'}</td>
                            </tr>
                            <tr>
                                <td><strong>设备品牌:</strong></td>
                                <td>${device.device_brand || '-'}</td>
                            </tr>
                            <tr>
                                <td><strong>设备型号:</strong></td>
                                <td>${device.device_model || '-'}</td>
                            </tr>
                            <tr>
                                <td><strong>所属小组:</strong></td>
                                <td>${device.group_name || '未分组'}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>在线状态:</strong></td>
                                <td><span class="status-badge status-${device.online_status}">${this.getOnlineStatusText(device.online_status)}</span></td>
                            </tr>
                            <tr>
                                <td><strong>健康状态:</strong></td>
                                <td>
                                    <div class="health-status">
                                        <div class="health-icon health-${device.health_status}"></div>
                                        <span>${this.getHealthStatusText(device.health_status)}</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>最后活动:</strong></td>
                                <td>${this.formatDateTime(device.last_activity)}</td>
                            </tr>
                            <tr>
                                <td><strong>创建时间:</strong></td>
                                <td>${this.formatDateTime(device.created_at)}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染性能图表
     */
    renderPerformanceChart(deviceData) {
        return `
            <div class="performance-charts">
                <div class="row">
                    <div class="col-md-4">
                        <div class="performance-metric">
                            <h6>CPU使用率</h6>
                            <div class="metric-value">${deviceData.performance?.cpu || 0}%</div>
                            <div class="progress">
                                <div class="progress-bar" style="width: ${deviceData.performance?.cpu || 0}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="performance-metric">
                            <h6>内存使用率</h6>
                            <div class="metric-value">${deviceData.performance?.memory || 0}%</div>
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: ${deviceData.performance?.memory || 0}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="performance-metric">
                            <h6>电池电量</h6>
                            <div class="metric-value">${deviceData.performance?.battery || 0}%</div>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: ${deviceData.performance?.battery || 0}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <h6>性能历史趋势</h6>
                    <div class="chart-placeholder">
                        <p class="text-muted text-center">性能历史图表功能开发中...</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染活动日志
     */
    renderActivityLogs(deviceData) {
        const logs = deviceData.activity_logs || [];
        
        if (logs.length === 0) {
            return `
                <div class="text-center text-muted py-4">
                    <i class="bi bi-journal-x" style="font-size: 2rem;"></i>
                    <div>暂无活动日志</div>
                </div>
            `;
        }
        
        return `
            <div class="activity-logs">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>活动类型</th>
                                <th>描述</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${logs.map(log => `
                                <tr>
                                    <td>${this.formatDateTime(log.timestamp)}</td>
                                    <td>${log.activity_type}</td>
                                    <td>${log.description}</td>
                                    <td><span class="badge ${log.status === 'success' ? 'bg-success' : 'bg-danger'}">${log.status}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * Ping设备测试
     */
    async pingDevice(deviceId) {
        try {
            this.showInfo('正在测试设备连接...');
            
            const response = await this.makeApiRequest('/monitor.php?action=ping_device', 'POST', {
                device_id: deviceId
            });
            
            if (response.error_code === 0) {
                this.showSuccess(`设备连接正常，延迟: ${response.data.latency}ms`);
            } else {
                this.showError('设备连接失败: ' + response.error_message);
            }
        } catch (error) {
            console.error('Ping设备失败:', error);
            this.showError('Ping设备失败');
        }
    }

    /**
     * 筛选设备
     */
    filterDevices(type, value) {
        // 实现设备筛选逻辑
        console.log(`筛选设备: ${type} = ${value}`);
        // 这里可以重新加载数据或在前端筛选
        this.refreshMonitorData();
    }

    /**
     * 清除所有告警
     */
    clearAllAlerts() {
        this.alerts = [];
        document.getElementById('alertPanel').style.display = 'none';
        this.showInfo('所有告警已清除');
    }

    /**
     * 忽略单个告警
     */
    dismissAlert(alertId) {
        this.alerts = this.alerts.filter(alert => alert.id !== alertId);
        this.updateAlerts([]);
    }

    /**
     * 工具方法
     */
    getOnlineStatusText(status) {
        const statusMap = {
            'online': '在线',
            'offline': '离线',
            'warning': '告警',
            'error': '故障'
        };
        return statusMap[status] || '未知';
    }

    getHealthStatusText(status) {
        const statusMap = {
            'good': '良好',
            'warning': '警告',
            'critical': '严重'
        };
        return statusMap[status] || '未知';
    }

    calculateOfflineTime(lastActivity) {
        if (!lastActivity) return 0;
        const now = new Date();
        const last = new Date(lastActivity);
        return (now.getTime() - last.getTime()) / 1000; // 返回秒数
    }

    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    /**
     * API请求方法
     */
    async makeApiRequest(url, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
            }
        };

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(this.apiBase + url, options);
        return await response.json();
    }

    /**
     * 消息提示方法
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showInfo(message) {
        this.showToast(message, 'info');
    }

    showToast(message, type) {
        // 实现Toast提示
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // 可以集成Bootstrap Toast或其他提示组件
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    /**
     * 销毁监控器
     */
    destroy() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
        this.isMonitoring = false;
        console.log('DeviceMonitor destroyed');
    }
}

// 全局实例
window.DeviceMonitor = DeviceMonitor;

// 创建全局实例
if (typeof window !== 'undefined') {
    window.deviceMonitor = new DeviceMonitor();
} 