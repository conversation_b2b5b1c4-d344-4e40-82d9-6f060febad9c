<?php
/**
 * 通知管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

// 检查基本权限
if (!in_array($currentUser['user_type'], array('admin', 'merchant'))) {
    $auth->sendForbidden('权限不足，无法访问通知管理');
}

// 获取请求参数
$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

try {
    // 根据action处理不同的请求
    switch ($action) {
        case 'notifications':
        case 'get_notifications':
            getNotifications($auth);
            break;
            
        case 'get_notification_stats':
            getNotificationStats($auth);
            break;
            
        case 'add_notification':
            addNotification($auth);
            break;
            
        case 'edit_notification':
            editNotification($auth);
            break;
            
        case 'delete_notification':
            deleteNotification($auth);
            break;
            
        case 'update_notification_status':
            updateNotificationStatus($auth);
            break;
            
        case 'send_notification':
            sendNotification($auth);
            break;
            
        default:
            $auth->sendError('无效的操作', 400);
            break;
    }
} catch (Exception $e) {
    error_log("Notifications API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

/**
 * 获取通知任务列表
 */
function getNotifications($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('notification.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        // 获取查询参数
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;
        
        $merchant_id = isset($_GET['merchant_id']) ? trim($_GET['merchant_id']) : '';
        $status = isset($_GET['status']) ? trim($_GET['status']) : '';
        $type = isset($_GET['type']) ? trim($_GET['type']) : '';
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        
        // 构建查询条件
        $where_conditions = array();
        $params = array();
        
        // 权限控制：merchant用户只能查看自己的通知
        if ($currentUser['user_type'] === 'merchant') {
            $where_conditions[] = 'n.merchant_id = ?';
            $params[] = $currentUser['profile_id'];
        } elseif (!empty($merchant_id)) {
            $where_conditions[] = 'n.merchant_id = ?';
            $params[] = $merchant_id;
        }
        
        // 状态筛选
        if (!empty($status)) {
            $where_conditions[] = 'n.status = ?';
            $params[] = $status;
        }
        
        // 类型筛选
        if (!empty($type)) {
            $where_conditions[] = 'n.type = ?';
            $params[] = $type;
        }
        
        // 搜索
        if (!empty($search)) {
            $where_conditions[] = '(n.title LIKE ? OR n.content LIKE ?)';
            $params[] = '%' . $search . '%';
            $params[] = '%' . $search . '%';
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        // 查询总数
        $count_sql = "SELECT COUNT(*) as total FROM notifications n $where_clause";
        $total_result = $db->fetch($count_sql, $params);
        $total = $total_result ? intval($total_result['total']) : 0;
        
        // 查询通知列表
        $sql = "SELECT 
                    n.id,
                    n.merchant_id,
                    n.title,
                    n.content,
                    n.type,
                    n.status,
                    n.target_users,
                    n.scheduled_time,
                    n.sent_time,
                    n.created_at,
                    n.updated_at,
                    m.company_name as merchant_name
                FROM notifications n
                LEFT JOIN merchants m ON n.merchant_id = m.id
                $where_clause
                ORDER BY n.created_at DESC
                LIMIT $limit OFFSET $offset";
        
        $notifications = $db->fetchAll($sql, $params);
        
        // 处理数据
        if ($notifications) {
            foreach ($notifications as &$notification) {
                // 解析目标用户（如果是JSON格式）
                if (!empty($notification['target_users']) && $notification['target_users'] !== 'all') {
                    $target_users = json_decode($notification['target_users'], true);
                    $notification['target_users'] = $target_users ? $target_users : array();
                } else {
                    $notification['target_users'] = 'all';
                }
            }
        }
        
        // 获取统计信息
        $stats = getNotificationStatsData($where_conditions, $params);
        
        // 记录操作日志
        $auth->logUserAction('view_notifications', 'notification', 0, '查看通知列表');
        
        $response = array(
            'notifications' => $notifications ? $notifications : array(),
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        );
        
        $auth->sendSuccess($response, '通知列表获取成功');
        
    } catch (Exception $e) {
        error_log('获取通知列表失败: ' . $e->getMessage());
        $auth->sendError('获取通知列表失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 获取通知统计信息
 */
function getNotificationStats($auth) {
    // 检查权限
    if (!$auth->checkPermission('notification.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $stats = getNotificationStatsData();
    $auth->sendSuccess($stats, '通知统计获取成功');
}

/**
 * 获取通知统计数据（内部函数）
 */
function getNotificationStatsData($where_conditions = array(), $params = array()) {
    global $db, $currentUser;
    
    // 添加用户权限过滤
    if ($currentUser['user_type'] === 'merchant') {
        $where_conditions[] = 'merchant_id = ?';
        $params[] = $currentUser['profile_id'];
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    try {
        $stats = $db->fetch("
            SELECT 
                COUNT(*) as total_notifications,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_notifications,
                COUNT(CASE WHEN status = 'sent' THEN 1 END) as sent_notifications,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_notifications
            FROM notifications $where_clause", $params);
        
        return $stats ? $stats : array(
            'total_notifications' => 0,
            'pending_notifications' => 0,
            'sent_notifications' => 0,
            'failed_notifications' => 0
        );
    } catch (Exception $e) {
        error_log('获取通知统计失败: ' . $e->getMessage());
        return array(
            'total_notifications' => 0,
            'pending_notifications' => 0,
            'sent_notifications' => 0,
            'failed_notifications' => 0
        );
    }
}

/**
 * 添加通知（暂时简化）
 */
function addNotification($auth) {
    // 检查权限
    if (!$auth->checkPermission('notification.create')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

/**
 * 编辑通知（暂时简化）
 */
function editNotification($auth) {
    // 检查权限
    if (!$auth->checkPermission('notification.update')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

/**
 * 删除通知（暂时简化）
 */
function deleteNotification($auth) {
    // 检查权限
    if (!$auth->checkPermission('notification.delete')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

/**
 * 更新通知状态（暂时简化）
 */
function updateNotificationStatus($auth) {
    // 检查权限
    if (!$auth->checkPermission('notification.update')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

/**
 * 发送通知（暂时简化）
 */
function sendNotification($auth) {
    // 检查权限
    if (!$auth->checkPermission('notification.send')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}
?> 