<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多租户设备管理测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            background-color: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .test-result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            max-height: 400px;
            overflow-y: auto;
        }
        .success { 
            background-color: #d4edda; 
            border: 1px solid #c3e6cb; 
            color: #155724; 
        }
        .error { 
            background-color: #f8d7da; 
            border: 1px solid #f5c6cb; 
            color: #721c24; 
        }
        .info { 
            background-color: #d1ecf1; 
            border: 1px solid #bee5eb; 
            color: #0c5460; 
        }
        button { 
            background-color: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px; 
        }
        button:hover { 
            background-color: #0056b3; 
        }
        button:disabled { 
            background-color: #6c757d; 
            cursor: not-allowed; 
        }
        input[type="text"], input[type="password"], select { 
            width: 200px; 
            padding: 8px; 
            margin: 5px; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .device-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .device-table th, .device-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .device-table th {
            background-color: #f2f2f2;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active { background-color: #28a745; color: white; }
        .status-pending { background-color: #ffc107; color: black; }
        .status-offline { background-color: #6c757d; color: white; }
        .status-online { background-color: #17a2b8; color: white; }
        
        .alipay-info {
            font-size: 12px;
            line-height: 1.3;
        }
        .alipay-info div {
            margin-bottom: 2px;
        }
        .alipay-info div:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 多租户设备管理测试 - Day 6 Step 4</h1>
        <div class="info test-result">
            <strong>测试目标:</strong> 验证devices.php多租户设备管理系统改造结果<br>
            <strong>测试域名:</strong> <span id="currentDomain"></span><br>
            <strong>API地址:</strong> <span id="apiUrl"></span><br>
            <strong>权限说明:</strong><br>
            • 设备管理：小组长和大组长可以启用/停用设备（审核=启用）<br>
            • 支付宝账户：设备启用后上传支付宝账户，小组长负责配置限额、权限等<br>
            • 小组管理：大组长和码商管理员可以创建、启用、停用小组<br>
            • 系统管理员/平台管理员/码商：只能查看设备，无管理权限
        </div>
        
        <!-- 登录状态 -->
        <div class="test-section">
            <h2>🔐 登录状态</h2>
            <p>Token: <span id="loginToken">未登录</span></p>
            <p>当前用户: <span id="currentUser">未登录</span></p>
            <button onclick="quickLogin()">快速登录</button>
            <div id="loginResult" class="test-result"></div>
        </div>
        
        <!-- 测试1: 获取设备列表 -->
        <div class="test-section">
            <h2>测试1: 获取租户设备列表</h2>
            <div>
                <input type="text" id="searchKeyword" placeholder="搜索关键词" value="">
                <select id="filterStatus">
                    <option value="">所有状态</option>
                    <option value="active">活跃</option>
                    <option value="pending">待审核</option>
                    <option value="disabled">禁用</option>
                </select>
                <select id="filterProvider">
                    <option value="">所有码商</option>
                </select>
                <button onclick="testGetDevices()" id="getDevicesBtn" disabled>获取设备列表</button>
                <button onclick="testGetProviders()" id="getProvidersBtn" disabled>获取可用码商</button>
            </div>
            <div id="devicesResult" class="test-result"></div>
            <div id="devicesTable"></div>
        </div>
        
        <!-- 测试2: 设备审核 -->
        <div class="test-section">
            <h2>测试2: 设备审核功能</h2>
            <div class="form-group">
                <label>设备ID:</label>
                <input type="number" id="approveDeviceId" placeholder="设备ID" value="">
            </div>
            <p class="info"><strong>业务流程：</strong>设备注册→启用(审核)→上传支付宝账户→配置账户权限<br>
            <strong>设备管理权限：</strong>小组长和大组长可以启用/停用设备<br>
            <strong>支付宝账户权限：</strong>小组长负责配置限额、收款规则、商户权限等</p>
            <button onclick="testEnableDevice()" id="enableDeviceBtn" disabled>启用设备</button>
            <button onclick="testDisableDevice()" id="disableDeviceBtn" disabled>停用设备</button>
            <button onclick="testGetAlipayAccounts()" id="getAlipayBtn" disabled>查看支付宝账户</button>
            

            
            <div id="auditResult" class="test-result"></div>
        </div>
        
        <!-- 测试3: 权限验证 -->
        <div class="test-section">
            <h2>测试3: 权限验证测试</h2>
            <p>测试不同租户类型的设备管理权限限制</p>
            <button onclick="testPermissions()" id="testPermissionsBtn" disabled>测试权限限制</button>
            <div id="permissionsResult" class="test-result"></div>
        </div>
        
        <!-- 测试4: 数据隔离验证 -->
        <div class="test-section">
            <h2>测试4: 数据隔离验证</h2>
            <p>验证不同租户只能看到自己范围内的设备数据</p>
            <button onclick="testDataIsolation()" id="testDataIsolationBtn" disabled>测试数据隔离</button>
            <div id="dataIsolationResult" class="test-result"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentToken = '';
        let currentUserInfo = null;
        let availableProviders = [];
        const API_BASE_AUTH = window.location.protocol + '//' + window.location.hostname + '/api/admin/auth.php';
        const API_BASE_DEVICES = window.location.protocol + '//' + window.location.hostname + '/api/admin/devices.php';
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('currentDomain').textContent = window.location.hostname;
            document.getElementById('apiUrl').textContent = API_BASE_DEVICES;
        });
        
        // 通用API请求函数
        async function apiRequest(apiBase, action, data = {}, useToken = false) {
            const url = apiBase + '?action=' + action;
            
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (useToken && currentToken) {
                headers['Authorization'] = 'Bearer ' + currentToken;
            }
            
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(data)
                });
                
                let result;
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    result = await response.json();
                } else {
                    const text = await response.text();
                    result = { error: 'Non-JSON response', response_text: text };
                }
                
                return { 
                    success: response.ok, 
                    data: result, 
                    status: response.status,
                    statusText: response.statusText
                };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        // 显示结果
        function displayResult(elementId, result, isSuccess = null) {
            const element = document.getElementById(elementId);
            
            if (isSuccess === null) {
                isSuccess = result.success;
            }
            
            element.className = 'test-result ' + (isSuccess ? 'success' : 'error');
            element.textContent = JSON.stringify(result, null, 2);
        }
        
        // 更新按钮状态
        function updateButtonStates() {
            const hasToken = !!currentToken;
            document.getElementById('getDevicesBtn').disabled = !hasToken;
            document.getElementById('getProvidersBtn').disabled = !hasToken;
            document.getElementById('enableDeviceBtn').disabled = !hasToken;
            document.getElementById('disableDeviceBtn').disabled = !hasToken;
            document.getElementById('getAlipayBtn').disabled = !hasToken;
            document.getElementById('testPermissionsBtn').disabled = !hasToken;
            document.getElementById('testDataIsolationBtn').disabled = !hasToken;
        }
        
        // 快速登录
        async function quickLogin() {
            displayResult('loginResult', { message: '正在登录...' }, true);
            
            const loginData = {
                username: 'system_admin',
                password: 'password'
            };
            
            const result = await apiRequest(API_BASE_AUTH, 'login', loginData);
            displayResult('loginResult', result);
            
            if (result.success && result.data && result.data.data) {
                currentToken = result.data.data.token;
                currentUserInfo = result.data.data.user;
                
                document.getElementById('loginToken').textContent = currentToken.substring(0, 50) + '...';
                document.getElementById('currentUser').textContent = currentUserInfo.username + ' (' + currentUserInfo.user_type + ')';
                
                updateButtonStates();
                
                // 自动获取可用码商列表
                setTimeout(() => {
                    testGetProviders();
                }, 1000);
            }
        }
        
        // 测试1: 获取设备列表
        async function testGetDevices() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            const search = document.getElementById('searchKeyword').value;
            const status = document.getElementById('filterStatus').value;
            const providerId = document.getElementById('filterProvider').value;
            
            let queryParams = '';
            if (search) queryParams += '&search=' + encodeURIComponent(search);
            if (status) queryParams += '&status=' + encodeURIComponent(status);
            if (providerId) queryParams += '&provider_id=' + encodeURIComponent(providerId);
            
            displayResult('devicesResult', { message: '正在获取设备列表...' }, true);
            
            try {
                const url = API_BASE_DEVICES + '?action=get_devices' + queryParams;
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + currentToken
                    }
                });
                
                const result = await response.json();
                displayResult('devicesResult', { success: response.ok, data: result, status: response.status });
                
                if (response.ok && result.data && result.data.devices) {
                    displayDevicesTable(result.data.devices, result.data.stats);
                }
            } catch (error) {
                displayResult('devicesResult', { success: false, error: error.message });
            }
        }
        
        // 显示设备表格
        function displayDevicesTable(devices, stats) {
            const tableContainer = document.getElementById('devicesTable');
            
            let html = '<h3>设备列表 (共 ' + devices.length + ' 个设备)</h3>';
            
            if (stats) {
                html += '<p><strong>统计信息:</strong></p>';
                html += '<p>总数: ' + stats.total + ' | 活跃: ' + stats.active + ' | 待审核: ' + stats.pending + ' | 在线: ' + stats.online + ' | 离线: ' + stats.offline + '</p>';
            }
            
            html += '<table class="device-table">';
            html += '<tr><th>ID</th><th>设备ID</th><th>设备名称</th><th>状态</th><th>在线状态</th><th>签到状态</th><th>任务状态</th><th>码商</th><th>平台</th><th>支付宝账户</th><th>最后签到</th></tr>';
            
            devices.forEach(device => {
                html += '<tr>';
                html += '<td>' + device.id + '</td>';
                html += '<td>' + device.device_id + '</td>';
                html += '<td>' + device.device_name + '</td>';
                html += '<td><span class="status-badge status-' + device.status + '">' + device.status + '</span></td>';
                html += '<td><span class="status-badge status-' + (device.is_online ? 'online' : 'offline') + '">' + device.online_status + '</span></td>';
                html += '<td><span class="status-badge">' + (device.checkin_status_text || device.checkin_status || '-') + '</span></td>';
                html += '<td><span class="status-badge">' + (device.task_status_text || device.task_status || '-') + '</span></td>';
                html += '<td>' + (device.provider_name || '-') + '</td>';
                html += '<td>' + device.platform_info + '</td>';
                
                // 支付宝账户信息
                if (device.alipay_info && device.alipay_info.account_name) {
                    html += '<td><div class="alipay-info">';
                    html += '<div><strong>' + device.alipay_info.account_name + '</strong></div>';
                    if (device.alipay_info.real_name) {
                        html += '<div>(' + device.alipay_info.real_name + ')</div>';
                    }
                    if (device.alipay_info.account_number) {
                        html += '<div>' + device.alipay_info.account_number + '</div>';
                    }
                    html += '<div>状态: ' + device.alipay_info.status + '</div>';
                    html += '<div>余额: ¥' + device.alipay_info.balance + '</div>';
                    html += '</div></td>';
                } else {
                    html += '<td>-</td>';
                }
                
                html += '<td>' + (device.last_checkin_formatted || '-') + '</td>';
                html += '</tr>';
            });
            
            html += '</table>';
            tableContainer.innerHTML = html;
        }
        
        // 获取可用码商
        async function testGetProviders() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            displayResult('devicesResult', { message: '正在获取可用码商...' }, true);
            
            const result = await apiRequest(API_BASE_DEVICES, 'get_providers', {}, true);
            displayResult('devicesResult', result);
            
            if (result.success && result.data && result.data.data) {
                availableProviders = result.data.data;
                
                // 更新码商选择框
                const filterSelect = document.getElementById('filterProvider');
                
                filterSelect.innerHTML = '<option value="">所有码商</option>';
                
                availableProviders.forEach(provider => {
                    const option = document.createElement('option');
                    option.value = provider.id;
                    option.textContent = provider.company_name + (provider.platform_name ? ' (' + provider.platform_name + ')' : '');
                    filterSelect.appendChild(option);
                });
            }
        }
        
        // 测试2: 启用设备
        async function testEnableDevice() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            const deviceId = document.getElementById('approveDeviceId').value;
            
            if (!deviceId) {
                alert('请填写设备ID');
                return;
            }
            
            displayResult('auditResult', { message: '正在启用设备...' }, true);
            
            const enableData = {
                device_id: parseInt(deviceId)
            };
            
            const result = await apiRequest(API_BASE_DEVICES, 'enable_device', enableData, true);
            displayResult('auditResult', result);
            
            if (result.success) {
                // 清空表单
                document.getElementById('approveDeviceId').value = '';
                
                // 刷新设备列表
                setTimeout(() => {
                    testGetDevices();
                }, 1000);
            }
        }
        
        // 测试2: 停用设备
        async function testDisableDevice() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            const deviceId = document.getElementById('approveDeviceId').value;
            
            if (!deviceId) {
                alert('请填写设备ID');
                return;
            }
            
            const disableReason = prompt('请输入停用原因（可选）:') || '管理员停用';
            
            displayResult('auditResult', { message: '正在停用设备...' }, true);
            
            const disableData = {
                device_id: parseInt(deviceId),
                disable_reason: disableReason
            };
            
            const result = await apiRequest(API_BASE_DEVICES, 'disable_device', disableData, true);
            displayResult('auditResult', result);
            
            if (result.success) {
                // 清空表单
                document.getElementById('approveDeviceId').value = '';
                
                // 刷新设备列表
                setTimeout(() => {
                    testGetDevices();
                }, 1000);
            }
        }
        
        // 测试2: 查看支付宝账户
        async function testGetAlipayAccounts() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            const deviceId = document.getElementById('approveDeviceId').value;
            
            if (!deviceId) {
                alert('请填写设备ID');
                return;
            }
            
            displayResult('auditResult', { message: '正在获取支付宝账户...' }, true);
            
            const result = await apiRequest(API_BASE_DEVICES, 'get_alipay_accounts', { device_id: parseInt(deviceId) }, false);
            displayResult('auditResult', result);
        }
        
        // 测试3: 权限验证
        async function testPermissions() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            displayResult('permissionsResult', { message: '正在测试权限验证...' }, true);
            
            const permissionTests = [
                { action: 'get_devices', description: '查看设备列表' },
                { action: 'enable_device', description: '启用设备', data: { device_id: 1 } },
                { action: 'disable_device', description: '停用设备', data: { device_id: 1, disable_reason: '测试' } },
                { action: 'get_alipay_accounts', description: '查看支付宝账户', data: { device_id: 1 } },
                { action: 'get_providers', description: '获取码商列表' }
            ];
            
            let results = [];
            
            for (let test of permissionTests) {
                try {
                    const result = await apiRequest(API_BASE_DEVICES, test.action, test.data || {}, true);
                    results.push({
                        action: test.action,
                        description: test.description,
                        success: result.success,
                        code: result.data ? result.data.code : result.status,
                        message: result.data ? result.data.message : result.statusText
                    });
                } catch (error) {
                    results.push({
                        action: test.action,
                        description: test.description,
                        success: false,
                        error: error.message
                    });
                }
            }
            
            displayResult('permissionsResult', { 
                success: true, 
                message: '权限测试完成',
                results: results 
            });
        }
        
        // 测试4: 数据隔离验证
        async function testDataIsolation() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            displayResult('dataIsolationResult', { message: '正在测试数据隔离...' }, true);
            
            // 获取设备列表，检查返回的数据是否符合租户权限
            const result = await apiRequest(API_BASE_DEVICES, 'get_devices', {}, true);
            
            if (result.success && result.data && result.data.data) {
                const devices = result.data.data.devices || [];
                const tenantInfo = result.data.data.tenant_info || {};
                
                let isolationTest = {
                    tenant_type: tenantInfo.type,
                    tenant_id: tenantInfo.id,
                    platform_id: tenantInfo.platform_id,
                    device_count: devices.length,
                    isolation_check: '通过'
                };
                
                // 根据租户类型验证数据隔离
                switch (tenantInfo.type) {
                    case 'system_admin':
                        isolationTest.expected = '可以看到所有设备';
                        break;
                    case 'platform_admin':
                        isolationTest.expected = '只能看到本平台的设备';
                        // 检查所有设备是否都属于当前平台
                        const nonPlatformDevices = devices.filter(d => d.platform_id && d.platform_id != tenantInfo.platform_id);
                        if (nonPlatformDevices.length > 0) {
                            isolationTest.isolation_check = '失败：发现其他平台的设备';
                        }
                        break;
                    case 'provider':
                        isolationTest.expected = '只能看到自己的设备';
                        // 检查所有设备是否都属于当前码商
                        const nonProviderDevices = devices.filter(d => d.provider_id != tenantInfo.id);
                        if (nonProviderDevices.length > 0) {
                            isolationTest.isolation_check = '失败：发现其他码商的设备';
                        }
                        break;
                    case 'merchant':
                        isolationTest.expected = '只能看到关联的设备';
                        break;
                }
                
                displayResult('dataIsolationResult', { 
                    success: true, 
                    message: '数据隔离测试完成',
                    isolation_test: isolationTest,
                    sample_devices: devices.slice(0, 3) // 显示前3个设备作为样本
                });
            } else {
                displayResult('dataIsolationResult', result);
            }
        }
    </script>
</body>
</html> 