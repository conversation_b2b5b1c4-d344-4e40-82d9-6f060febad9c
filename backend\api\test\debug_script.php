<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$debug_info = array();
$debug_info['php_version'] = phpversion();
$debug_info['request_method'] = $_SERVER['REQUEST_METHOD'];
$debug_info['get_params'] = $_GET;

// 测试数据库连接
try {
    $debug_info['database_test'] = 'starting';
    require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
    $debug_info['database_require'] = 'success';
    
    $db = Database::getInstance()->getConnection();
    $debug_info['database_connection'] = 'success';
    
    // 测试查询
    $stmt = $db->prepare("SELECT COUNT(*) FROM script_versions");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    $debug_info['script_versions_count'] = $count;
    
} catch (Exception $e) {
    $debug_info['database_error'] = $e->getMessage();
}

// 测试Auth类
try {
    $debug_info['auth_test'] = 'starting';
    require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';
    $debug_info['auth_require'] = 'success';
    
    $auth = new Auth();
    $debug_info['auth_instance'] = 'success';
    
} catch (Exception $e) {
    $debug_info['auth_error'] = $e->getMessage();
}

// 测试加密类
try {
    $debug_info['crypto_test'] = 'starting';
    
    // 简单的加密测试
    $baseKey = "PayPal_Script_2025" . "UNIFIED_KEY";
    $key = hash('sha256', $baseKey, true);
    $debug_info['crypto_key_generated'] = 'success';
    
    $iv = openssl_random_pseudo_bytes(16);
    $debug_info['crypto_iv_generated'] = 'success';
    
    $test_data = "test encryption";
    $encrypted = openssl_encrypt($test_data, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
    $debug_info['crypto_encrypt'] = $encrypted ? 'success' : 'failed';
    
} catch (Exception $e) {
    $debug_info['crypto_error'] = $e->getMessage();
}

echo json_encode(array(
    'success' => true,
    'message' => '调试信息',
    'debug' => $debug_info
), JSON_UNESCAPED_UNICODE);
?> 