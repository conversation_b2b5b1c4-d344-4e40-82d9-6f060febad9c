/**
 * 交易管理模块
 * 负责交易查询、状态管理、对账功能、退款处理等核心交易业务功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-15
 */

class TransactionManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.currentTransactions = [];
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalTransactions = 0;
        this.searchQuery = '';
        this.filterStatus = 'all';
        this.filterType = 'all';
        this.dateRange = { start: '', end: '' };
        
        console.log('TransactionManager initialized');
    }

    /**
     * 初始化交易管理模块
     */
    async init() {
        try {
            // 注册页面处理器
            if (window.uiManager) {
                window.uiManager.registerPageHandler('transactions', (container) => {
                    this.loadTransactionManagementPage(container);
                });
            }
            
            console.log('✅ 交易管理模块初始化完成');
        } catch (error) {
            console.error('❌ 交易管理模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载交易管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadTransactionManagementPage(container) {
        container.innerHTML = this.generateTransactionManagementHTML();
        this.initializeTransactionManagementEvents();
        this.loadTransactionList();
    }

    /**
     * 生成交易管理页面HTML
     * @returns {string} HTML字符串
     */
    generateTransactionManagementHTML() {
        return `
            <div class="transaction-management">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-credit-card me-2"></i>交易管理</h2>
                            <p class="text-muted mb-0">管理交易记录、状态跟踪和退款处理</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary" id="reconcileBtn">
                                <i class="bi bi-arrow-repeat me-2"></i>对账
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="exportTransactionsBtn">
                                <i class="bi bi-download me-2"></i>导出数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-receipt"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalTransactionsCount">-</div>
                                <div class="stat-label">总交易数</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="successTransactionsCount">-</div>
                                <div class="stat-label">成功交易</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="pendingTransactionsCount">-</div>
                                <div class="stat-label">处理中</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="todayVolumeCount">-</div>
                                <div class="stat-label">今日交易额</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">搜索交易</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="transactionSearchInput" 
                                           placeholder="订单号、商户号...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">交易状态</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="all">全部状态</option>
                                    <option value="success">成功</option>
                                    <option value="pending">处理中</option>
                                    <option value="failed">失败</option>
                                    <option value="refunded">已退款</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">交易类型</label>
                                <select class="form-select" id="typeFilter">
                                    <option value="all">全部类型</option>
                                    <option value="payment">支付</option>
                                    <option value="refund">退款</option>
                                    <option value="transfer">转账</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDateFilter">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDateFilter">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-outline-primary" id="searchBtn">
                                        <i class="bi bi-funnel"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 交易列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">交易列表</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-secondary" id="refreshTransactionsBtn">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                        type="button" data-bs-toggle="dropdown">
                                    批量操作
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" id="batchRefundBtn">
                                        <i class="bi bi-arrow-counterclockwise me-2"></i>批量退款
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" id="batchExportBtn">
                                        <i class="bi bi-download me-2"></i>批量导出
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" class="form-check-input" id="selectAllTransactions">
                                        </th>
                                        <th>交易信息</th>
                                        <th>商户</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>类型</th>
                                        <th>交易时间</th>
                                        <th width="120">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="transactionTableBody">
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <div class="mt-2">正在加载交易数据...</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                显示第 <span id="currentPageInfo">1-20</span> 条，共 <span id="totalTransactionsInfo">0</span> 条记录
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="transactionPagination">
                                    <!-- 分页将动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易详情模态框 -->
            <div class="modal fade" id="transactionDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">交易详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="transactionDetailContent">
                            <!-- 交易详情内容将动态生成 -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-warning" id="refundBtn" style="display: none;">
                                <i class="bi bi-arrow-counterclockwise me-2"></i>申请退款
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 退款模态框 -->
            <div class="modal fade" id="refundModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">申请退款</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="refundForm">
                                <input type="hidden" id="refundTransactionId">
                                <div class="mb-3">
                                    <label class="form-label">退款金额 *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="refundAmount" 
                                               min="0" step="0.01" required>
                                    </div>
                                    <div class="form-text">最大可退款金额：¥<span id="maxRefundAmount">0.00</span></div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">退款原因 *</label>
                                    <select class="form-select" id="refundReason" required>
                                        <option value="">请选择退款原因</option>
                                        <option value="duplicate">重复扣款</option>
                                        <option value="error">交易错误</option>
                                        <option value="cancel">订单取消</option>
                                        <option value="quality">质量问题</option>
                                        <option value="other">其他原因</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">备注说明</label>
                                    <textarea class="form-control" id="refundRemarks" rows="3" 
                                              placeholder="请详细说明退款原因..."></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-warning" id="submitRefundBtn">
                                <i class="bi bi-arrow-counterclockwise me-2"></i>提交退款
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            ${this.generateTransactionManagementStyles()}
        `;
    }

    /**
     * 生成交易管理样式
     * @returns {string} CSS样式
     */
    generateTransactionManagementStyles() {
        return `
            <style>
                .transaction-management .stat-card {
                    background: white;
                    border-radius: 12px;
                    padding: 20px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }

                .transaction-management .stat-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                }

                .transaction-management .stat-icon {
                    width: 50px;
                    height: 50px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .transaction-management .stat-content {
                    flex: 1;
                }

                .transaction-management .stat-number {
                    font-size: 1.8rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin-bottom: 4px;
                }

                .transaction-management .stat-label {
                    color: #6b7280;
                    font-size: 14px;
                }

                .transaction-management .transaction-info h6 {
                    margin: 0;
                    font-weight: 600;
                    color: #1f2937;
                    font-family: 'Courier New', monospace;
                }

                .transaction-management .transaction-info small {
                    color: #6b7280;
                }

                .transaction-management .status-badge {
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .transaction-management .status-success {
                    background: #dcfce7;
                    color: #166534;
                }

                .transaction-management .status-pending {
                    background: #fef3c7;
                    color: #92400e;
                }

                .transaction-management .status-failed {
                    background: #fecaca;
                    color: #dc2626;
                }

                .transaction-management .status-refunded {
                    background: #e0e7ff;
                    color: #3730a3;
                }

                .transaction-management .type-badge {
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .transaction-management .type-payment {
                    background: #dcfce7;
                    color: #166534;
                }

                .transaction-management .type-refund {
                    background: #fef3c7;
                    color: #92400e;
                }

                .transaction-management .type-transfer {
                    background: #dbeafe;
                    color: #1e40af;
                }

                .transaction-management .amount-positive {
                    color: #059669;
                    font-weight: 600;
                }

                .transaction-management .amount-negative {
                    color: #dc2626;
                    font-weight: 600;
                }

                .transaction-management .table th {
                    font-weight: 600;
                    color: #374151;
                    border-bottom: 2px solid #e5e7eb;
                }

                .transaction-management .table td {
                    vertical-align: middle;
                    border-bottom: 1px solid #f3f4f6;
                }

                .transaction-management .btn-action {
                    padding: 4px 8px;
                    font-size: 12px;
                    border-radius: 6px;
                    margin: 0 2px;
                }

                .transaction-management .form-label {
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 6px;
                }

                .transaction-management .form-control:focus,
                .transaction-management .form-select:focus {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }

                .transaction-management .transaction-detail-table {
                    margin-bottom: 0;
                }

                .transaction-management .transaction-detail-table td {
                    border: none;
                    padding: 8px 0;
                }

                .transaction-management .transaction-detail-table td:first-child {
                    font-weight: 600;
                    color: #374151;
                    width: 120px;
                }
            </style>
        `;
    }

    /**
     * 初始化交易管理事件
     */
    initializeTransactionManagementEvents() {
        // 搜索按钮
        document.getElementById('searchBtn')?.addEventListener('click', () => {
            this.handleSearch();
        });

        // 刷新按钮
        document.getElementById('refreshTransactionsBtn')?.addEventListener('click', () => {
            this.loadTransactionList();
        });

        // 对账按钮
        document.getElementById('reconcileBtn')?.addEventListener('click', () => {
            this.handleReconcile();
        });

        // 全选复选框
        document.getElementById('selectAllTransactions')?.addEventListener('change', (e) => {
            this.handleSelectAll(e.target.checked);
        });

        // 批量操作
        document.getElementById('batchRefundBtn')?.addEventListener('click', () => {
            this.handleBatchRefund();
        });

        // 提交退款按钮
        document.getElementById('submitRefundBtn')?.addEventListener('click', () => {
            this.handleSubmitRefund();
        });

        // 搜索输入框回车事件
        document.getElementById('transactionSearchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch();
            }
        });

        // 筛选器变化事件
        ['statusFilter', 'typeFilter', 'startDateFilter', 'endDateFilter'].forEach(id => {
            document.getElementById(id)?.addEventListener('change', () => {
                this.handleSearch();
            });
        });
    }

    /**
     * 加载交易列表
     * @param {number} page 页码
     */
    async loadTransactionList(page = 1) {
        try {
            this.showTableLoading();
            
            const params = {
                page: page,
                pageSize: this.pageSize,
                search: this.searchQuery,
                status: this.filterStatus,
                type: this.filterType,
                start_date: this.dateRange.start,
                end_date: this.dateRange.end
            };

            const response = await this.apiClient.get('/admin/transactions', { params });
            
            if (response.success) {
                this.currentTransactions = response.data.transactions;
                this.totalTransactions = response.data.total;
                this.currentPage = page;
                
                this.renderTransactionTable();
                this.renderPagination();
                this.updateTransactionStats();
            } else {
                throw new Error(response.message || '加载交易列表失败');
            }
        } catch (error) {
            console.error('加载交易列表失败:', error);
            this.showTableError('加载交易列表失败，请重试');
            this.utils.showMessage('加载交易列表失败', 'error');
        }
    }

    /**
     * 渲染交易表格
     */
    renderTransactionTable() {
        const tbody = document.getElementById('transactionTableBody');
        if (!tbody) return;

        if (this.currentTransactions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="bi bi-inbox" style="font-size: 2rem; color: #6b7280;"></i>
                        <div class="mt-2 text-muted">暂无交易数据</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.currentTransactions.map(transaction => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input transaction-checkbox" value="${transaction.id}">
                </td>
                <td>
                    <div class="transaction-info">
                        <h6>${transaction.order_no}</h6>
                        <small>商户订单号: ${transaction.merchant_order_no || '-'}</small>
                    </div>
                </td>
                <td>
                    <div>
                        <div class="fw-bold">${transaction.merchant_name}</div>
                        <small class="text-muted">${transaction.merchant_id}</small>
                    </div>
                </td>
                <td>
                    <div class="${transaction.type === 'refund' ? 'amount-negative' : 'amount-positive'}">
                        ${transaction.type === 'refund' ? '-' : ''}¥${this.utils.formatNumber(transaction.amount)}
                    </div>
                    ${transaction.fee_amount ? `<small class="text-muted">手续费: ¥${this.utils.formatNumber(transaction.fee_amount)}</small>` : ''}
                </td>
                <td>
                    <span class="status-badge status-${transaction.status}">
                        ${this.getStatusText(transaction.status)}
                    </span>
                </td>
                <td>
                    <span class="type-badge type-${transaction.type}">
                        ${this.getTypeText(transaction.type)}
                    </span>
                </td>
                <td>
                    <div>${this.utils.formatDate(transaction.created_at)}</div>
                    ${transaction.completed_at ? `<small class="text-muted">完成: ${this.utils.formatDate(transaction.completed_at)}</small>` : ''}
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-action" 
                                onclick="transactionManager.showTransactionDetail(${transaction.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${transaction.status === 'success' && transaction.type === 'payment' ? `
                        <button class="btn btn-outline-warning btn-action" 
                                onclick="transactionManager.showRefundModal(${transaction.id})" title="申请退款">
                            <i class="bi bi-arrow-counterclockwise"></i>
                        </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 显示表格加载状态
     */
    showTableLoading() {
        const tbody = document.getElementById('transactionTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载交易数据...</div>
                </td>
            </tr>
        `;
    }

    /**
     * 显示表格错误状态
     * @param {string} message 错误信息
     */
    showTableError(message) {
        const tbody = document.getElementById('transactionTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                    <div class="mt-2 text-danger">${message}</div>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="transactionManager.loadTransactionList()">
                        重新加载
                    </button>
                </td>
            </tr>
        `;
    }

    /**
     * 获取状态文本
     * @param {string} status 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'success': '成功',
            'pending': '处理中',
            'failed': '失败',
            'refunded': '已退款'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取类型文本
     * @param {string} type 类型
     * @returns {string} 类型文本
     */
    getTypeText(type) {
        const typeMap = {
            'payment': '支付',
            'refund': '退款',
            'transfer': '转账'
        };
        return typeMap[type] || type;
    }

    /**
     * 更新交易统计数据
     */
    async updateTransactionStats() {
        try {
            const response = await this.apiClient.get('/admin/transactions/stats');
            
            if (response.success) {
                const stats = response.data;
                document.getElementById('totalTransactionsCount').textContent = stats.total || 0;
                document.getElementById('successTransactionsCount').textContent = stats.success || 0;
                document.getElementById('pendingTransactionsCount').textContent = stats.pending || 0;
                document.getElementById('todayVolumeCount').textContent = 
                    '¥' + this.utils.formatNumber(stats.today_volume || 0);
            }
        } catch (error) {
            console.error('加载交易统计失败:', error);
        }
    }

    /**
     * 显示交易详情
     * @param {number} transactionId 交易ID
     */
    async showTransactionDetail(transactionId) {
        try {
            const response = await this.apiClient.get(`/admin/transactions/${transactionId}`);
            
            if (response.success) {
                const transaction = response.data;
                const modal = new bootstrap.Modal(document.getElementById('transactionDetailModal'));
                const content = document.getElementById('transactionDetailContent');
                const refundBtn = document.getElementById('refundBtn');
                
                content.innerHTML = this.generateTransactionDetailHTML(transaction);
                
                // 显示/隐藏退款按钮
                if (transaction.status === 'success' && transaction.type === 'payment') {
                    refundBtn.style.display = 'inline-block';
                    refundBtn.onclick = () => this.showRefundModal(transactionId);
                } else {
                    refundBtn.style.display = 'none';
                }
                
                modal.show();
            } else {
                throw new Error(response.message || '加载交易详情失败');
            }
        } catch (error) {
            console.error('加载交易详情失败:', error);
            this.utils.showMessage('加载交易详情失败', 'error');
        }
    }

    /**
     * 生成交易详情HTML
     * @param {Object} transaction 交易信息
     * @returns {string} HTML字符串
     */
    generateTransactionDetailHTML(transaction) {
        return `
            <div class="row g-4">
                <div class="col-12">
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-info-circle me-2"></i>基本信息
                    </h6>
                    <table class="table transaction-detail-table">
                        <tr>
                            <td>订单号:</td>
                            <td><code>${transaction.order_no}</code></td>
                        </tr>
                        <tr>
                            <td>商户订单号:</td>
                            <td>${transaction.merchant_order_no || '-'}</td>
                        </tr>
                        <tr>
                            <td>商户名称:</td>
                            <td>${transaction.merchant_name}</td>
                        </tr>
                        <tr>
                            <td>交易金额:</td>
                            <td class="${transaction.type === 'refund' ? 'amount-negative' : 'amount-positive'}">
                                ${transaction.type === 'refund' ? '-' : ''}¥${this.utils.formatNumber(transaction.amount)}
                            </td>
                        </tr>
                        <tr>
                            <td>手续费:</td>
                            <td>¥${this.utils.formatNumber(transaction.fee_amount || 0)}</td>
                        </tr>
                        <tr>
                            <td>实际到账:</td>
                            <td class="text-success fw-bold">¥${this.utils.formatNumber((transaction.amount || 0) - (transaction.fee_amount || 0))}</td>
                        </tr>
                    </table>
                </div>
                
                <div class="col-12">
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-clock-history me-2"></i>状态信息
                    </h6>
                    <table class="table transaction-detail-table">
                        <tr>
                            <td>交易状态:</td>
                            <td>
                                <span class="status-badge status-${transaction.status}">
                                    ${this.getStatusText(transaction.status)}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>交易类型:</td>
                            <td>
                                <span class="type-badge type-${transaction.type}">
                                    ${this.getTypeText(transaction.type)}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td>创建时间:</td>
                            <td>${this.utils.formatDate(transaction.created_at)}</td>
                        </tr>
                        <tr>
                            <td>完成时间:</td>
                            <td>${transaction.completed_at ? this.utils.formatDate(transaction.completed_at) : '-'}</td>
                        </tr>
                        <tr>
                            <td>支付方式:</td>
                            <td>${transaction.payment_method || '-'}</td>
                        </tr>
                    </table>
                </div>
                
                ${transaction.remarks ? `
                <div class="col-12">
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-chat-text me-2"></i>备注信息
                    </h6>
                    <div class="bg-light p-3 rounded">
                        ${transaction.remarks}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 显示退款模态框
     * @param {number} transactionId 交易ID
     */
    async showRefundModal(transactionId) {
        try {
            const response = await this.apiClient.get(`/admin/transactions/${transactionId}`);
            
            if (response.success) {
                const transaction = response.data;
                const modal = new bootstrap.Modal(document.getElementById('refundModal'));
                
                document.getElementById('refundTransactionId').value = transactionId;
                document.getElementById('refundAmount').value = transaction.amount;
                document.getElementById('refundAmount').max = transaction.amount;
                document.getElementById('maxRefundAmount').textContent = this.utils.formatNumber(transaction.amount);
                
                // 重置表单
                document.getElementById('refundReason').value = '';
                document.getElementById('refundRemarks').value = '';
                
                modal.show();
            }
        } catch (error) {
            console.error('加载交易信息失败:', error);
            this.utils.showMessage('加载交易信息失败', 'error');
        }
    }

    /**
     * 处理提交退款
     */
    async handleSubmitRefund() {
        const form = document.getElementById('refundForm');
        const transactionId = document.getElementById('refundTransactionId').value;
        const amount = document.getElementById('refundAmount').value;
        const reason = document.getElementById('refundReason').value;
        const remarks = document.getElementById('refundRemarks').value;

        // 表单验证
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        if (!confirm(`确定要申请退款 ¥${amount} 吗？`)) {
            return;
        }

        try {
            const submitBtn = document.getElementById('submitRefundBtn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>提交中...';

            const response = await this.apiClient.post(`/admin/transactions/${transactionId}/refund`, {
                amount: parseFloat(amount),
                reason: reason,
                remarks: remarks
            });

            if (response.success) {
                this.utils.showMessage('退款申请提交成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('refundModal')).hide();
                this.loadTransactionList(this.currentPage);
            } else {
                throw new Error(response.message || '退款申请失败');
            }
        } catch (error) {
            console.error('退款申请失败:', error);
            this.utils.showMessage(error.message || '退款申请失败', 'error');
        } finally {
            const submitBtn = document.getElementById('submitRefundBtn');
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="bi bi-arrow-counterclockwise me-2"></i>提交退款';
        }
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        this.searchQuery = document.getElementById('transactionSearchInput').value.trim();
        this.filterStatus = document.getElementById('statusFilter').value;
        this.filterType = document.getElementById('typeFilter').value;
        this.dateRange.start = document.getElementById('startDateFilter').value;
        this.dateRange.end = document.getElementById('endDateFilter').value;
        
        this.currentPage = 1;
        this.loadTransactionList(1);
    }

    /**
     * 处理全选
     * @param {boolean} checked 是否选中
     */
    handleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.transaction-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
    }

    /**
     * 获取选中的交易ID
     * @returns {Array} 交易ID数组
     */
    getSelectedTransactionIds() {
        const checkboxes = document.querySelectorAll('.transaction-checkbox:checked');
        return Array.from(checkboxes).map(cb => parseInt(cb.value));
    }

    /**
     * 处理批量退款
     */
    async handleBatchRefund() {
        const selectedIds = this.getSelectedTransactionIds();
        
        if (selectedIds.length === 0) {
            this.utils.showMessage('请选择要退款的交易', 'warning');
            return;
        }

        if (!confirm(`确定要对选中的 ${selectedIds.length} 笔交易申请退款吗？`)) {
            return;
        }

        try {
            const response = await this.apiClient.post('/admin/transactions/batch/refund', {
                transaction_ids: selectedIds
            });

            if (response.success) {
                this.utils.showMessage('批量退款申请提交成功', 'success');
                this.loadTransactionList(this.currentPage);
                document.getElementById('selectAllTransactions').checked = false;
            } else {
                throw new Error(response.message || '批量退款失败');
            }
        } catch (error) {
            console.error('批量退款失败:', error);
            this.utils.showMessage(error.message || '批量退款失败', 'error');
        }
    }

    /**
     * 处理对账
     */
    async handleReconcile() {
        if (!confirm('确定要开始对账吗？此操作可能需要较长时间。')) {
            return;
        }

        try {
            const reconcileBtn = document.getElementById('reconcileBtn');
            reconcileBtn.disabled = true;
            reconcileBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>对账中...';

            const response = await this.apiClient.post('/admin/transactions/reconcile');

            if (response.success) {
                this.utils.showMessage('对账完成', 'success');
                this.loadTransactionList(this.currentPage);
            } else {
                throw new Error(response.message || '对账失败');
            }
        } catch (error) {
            console.error('对账失败:', error);
            this.utils.showMessage(error.message || '对账失败', 'error');
        } finally {
            const reconcileBtn = document.getElementById('reconcileBtn');
            reconcileBtn.disabled = false;
            reconcileBtn.innerHTML = '<i class="bi bi-arrow-repeat me-2"></i>对账';
        }
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        const totalPages = Math.ceil(this.totalTransactions / this.pageSize);
        const pagination = document.getElementById('transactionPagination');
        const currentPageInfo = document.getElementById('currentPageInfo');
        const totalTransactionsInfo = document.getElementById('totalTransactionsInfo');

        if (!pagination) return;

        // 更新信息显示
        const startItem = (this.currentPage - 1) * this.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.pageSize, this.totalTransactions);
        currentPageInfo.textContent = `${startItem}-${endItem}`;
        totalTransactionsInfo.textContent = this.totalTransactions;

        // 生成分页HTML（与其他模块类似的逻辑）
        let paginationHTML = '';

        // 上一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="transactionManager.loadTransactionList(${this.currentPage - 1})">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码逻辑
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="transactionManager.loadTransactionList(1)">1</a>
                </li>
            `;
            if (startPage > 2) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="transactionManager.loadTransactionList(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="transactionManager.loadTransactionList(${totalPages})">${totalPages}</a>
                </li>
            `;
        }

        // 下一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="transactionManager.loadTransactionList(${this.currentPage + 1})">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }
}

// 创建全局实例
const transactionManager = new TransactionManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TransactionManager };
} else {
    // 浏览器环境
    window.TransactionManager = TransactionManager;
    window.transactionManager = transactionManager;
}
</rewritten_file>