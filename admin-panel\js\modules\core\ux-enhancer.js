/**
 * 用户体验增强模块
 * 提升交互体验、可用性和用户满意度
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class UXEnhancer {
    constructor() {
        this.animations = new Map();
        this.shortcuts = new Map();
        this.themes = new Map();
        this.accessibility = {
            highContrast: false,
            largeText: false,
            reducedMotion: false
        };
        
        console.log('✨ UXEnhancer initialized');
        this.initialize();
    }

    /**
     * 初始化用户体验增强
     */
    initialize() {
        this.setupAnimations();
        this.setupKeyboardShortcuts();
        this.setupThemeSystem();
        this.setupAccessibility();
        this.setupInteractionFeedback();
        this.setupProgressIndicators();
        this.setupSmartDefaults();
        
        console.log('✨ 用户体验增强已启动');
    }

    /**
     * 设置动画效果
     */
    setupAnimations() {
        // 检查用户是否偏好减少动画
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        
        if (prefersReducedMotion) {
            this.accessibility.reducedMotion = true;
            document.documentElement.style.setProperty('--animation-duration', '0s');
            return;
        }
        
        // 添加页面加载动画
        this.addPageLoadAnimation();
        
        // 添加元素进入动画
        this.setupIntersectionAnimations();
        
        // 添加悬停效果
        this.setupHoverEffects();
        
        // 添加点击反馈
        this.setupClickFeedback();
    }

    /**
     * 页面加载动画
     */
    addPageLoadAnimation() {
        const style = document.createElement('style');
        style.textContent = `
            .fade-in {
                opacity: 0;
                transform: translateY(20px);
                transition: opacity 0.6s ease, transform 0.6s ease;
            }
            
            .fade-in.visible {
                opacity: 1;
                transform: translateY(0);
            }
            
            .slide-in-left {
                opacity: 0;
                transform: translateX(-30px);
                transition: opacity 0.5s ease, transform 0.5s ease;
            }
            
            .slide-in-left.visible {
                opacity: 1;
                transform: translateX(0);
            }
            
            .scale-in {
                opacity: 0;
                transform: scale(0.9);
                transition: opacity 0.4s ease, transform 0.4s ease;
            }
            
            .scale-in.visible {
                opacity: 1;
                transform: scale(1);
            }
            
            .pulse-button {
                transition: transform 0.1s ease;
            }
            
            .pulse-button:active {
                transform: scale(0.95);
            }
            
            .hover-lift {
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }
            
            .hover-lift:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 设置交集动画
     */
    setupIntersectionAnimations() {
        if (!window.IntersectionObserver) return;
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });
        
        // 观察所有需要动画的元素
        document.querySelectorAll('.fade-in, .slide-in-left, .scale-in').forEach(el => {
            observer.observe(el);
        });
        
        // 监听新元素
        const mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        const animElements = node.querySelectorAll ? 
                            node.querySelectorAll('.fade-in, .slide-in-left, .scale-in') : [];
                        animElements.forEach(el => observer.observe(el));
                    }
                });
            });
        });
        
        mutationObserver.observe(document.body, { childList: true, subtree: true });
    }

    /**
     * 设置悬停效果
     */
    setupHoverEffects() {
        document.addEventListener('mouseover', (e) => {
            if (e.target.classList.contains('btn') || e.target.classList.contains('card')) {
                e.target.classList.add('hover-lift');
            }
        });
    }

    /**
     * 设置点击反馈
     */
    setupClickFeedback() {
        document.addEventListener('click', (e) => {
            if (e.target.tagName === 'BUTTON' || e.target.classList.contains('btn')) {
                e.target.classList.add('pulse-button');
                
                // 添加涟漪效果
                this.createRippleEffect(e);
            }
        });
    }

    /**
     * 创建涟漪效果
     */
    createRippleEffect(event) {
        const button = event.target;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple 0.6s linear;
            left: ${x}px;
            top: ${y}px;
            width: ${size}px;
            height: ${size}px;
            pointer-events: none;
        `;
        
        // 添加动画样式
        if (!document.querySelector('#ripple-style')) {
            const style = document.createElement('style');
            style.id = 'ripple-style';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    }

    /**
     * 设置键盘快捷键
     */
    setupKeyboardShortcuts() {
        const shortcuts = {
            'Ctrl+/': () => this.showShortcutHelp(),
            'Ctrl+Shift+D': () => this.toggleDarkMode(),
            'Ctrl+Shift+R': () => this.refreshCurrentModule(),
            'Escape': () => this.closeTopModal(),
            'Ctrl+S': (e) => { e.preventDefault(); this.saveCurrentForm(); },
            'Ctrl+Z': (e) => { e.preventDefault(); this.undoLastAction(); }
        };
        
        document.addEventListener('keydown', (e) => {
            const key = this.getKeyString(e);
            const handler = shortcuts[key];
            
            if (handler) {
                handler(e);
            }
        });
        
        // 存储快捷键信息
        this.shortcuts = new Map(Object.entries(shortcuts));
    }

    /**
     * 获取按键字符串
     */
    getKeyString(event) {
        const parts = [];
        
        if (event.ctrlKey) parts.push('Ctrl');
        if (event.shiftKey) parts.push('Shift');
        if (event.altKey) parts.push('Alt');
        
        if (event.key !== 'Control' && event.key !== 'Shift' && event.key !== 'Alt') {
            parts.push(event.key);
        }
        
        return parts.join('+');
    }

    /**
     * 显示快捷键帮助
     */
    showShortcutHelp() {
        const shortcuts = [
            { key: 'Ctrl + /', desc: '显示快捷键帮助' },
            { key: 'Ctrl + Shift + D', desc: '切换深色模式' },
            { key: 'Ctrl + Shift + R', desc: '刷新当前模块' },
            { key: 'Escape', desc: '关闭顶层模态框' },
            { key: 'Ctrl + S', desc: '保存当前表单' },
            { key: 'Ctrl + Z', desc: '撤销上一步操作' }
        ];
        
        const content = `
            <div class="shortcut-help">
                <h5>键盘快捷键</h5>
                <div class="shortcut-list">
                    ${shortcuts.map(s => `
                        <div class="shortcut-item d-flex justify-content-between">
                            <span class="shortcut-key">${s.key}</span>
                            <span class="shortcut-desc">${s.desc}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        if (window.UI) {
            const modal = window.UI.create('modal', {
                title: '快捷键帮助',
                content: content,
                size: 'medium'
            });
            modal.show();
        }
    }

    /**
     * 设置主题系统
     */
    setupThemeSystem() {
        // 检测系统主题偏好
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        // 从localStorage加载主题设置
        const savedTheme = localStorage.getItem('theme') || (prefersDark ? 'dark' : 'light');
        this.setTheme(savedTheme);
        
        // 监听系统主题变化
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                this.setTheme(e.matches ? 'dark' : 'light');
            }
        });
    }

    /**
     * 设置主题
     */
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        // 更新状态管理器
        if (window.GlobalState) {
            window.GlobalState.setState('ui.theme', theme);
        }
        
        // 触发主题变更事件
        if (window.eventBus) {
            window.eventBus.emit('theme:changed', theme);
        }
        
        console.log(`🎨 主题已切换为: ${theme}`);
    }

    /**
     * 切换深色模式
     */
    toggleDarkMode() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
        
        // 显示切换提示
        if (window.UI) {
            window.UI.showToast(`已切换到${newTheme === 'dark' ? '深色' : '浅色'}模式`, 'info', 2000);
        }
    }

    /**
     * 设置无障碍功能
     */
    setupAccessibility() {
        // 检测高对比度偏好
        const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
        if (prefersHighContrast) {
            this.enableHighContrast();
        }
        
        // 检测大文本偏好
        const prefersLargeText = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        if (prefersLargeText) {
            this.enableLargeText();
        }
        
        // 添加焦点管理
        this.setupFocusManagement();
        
        // 添加屏幕阅读器支持
        this.setupScreenReaderSupport();
    }

    /**
     * 启用高对比度
     */
    enableHighContrast() {
        document.documentElement.classList.add('high-contrast');
        this.accessibility.highContrast = true;
    }

    /**
     * 启用大文本
     */
    enableLargeText() {
        document.documentElement.classList.add('large-text');
        this.accessibility.largeText = true;
    }

    /**
     * 设置焦点管理
     */
    setupFocusManagement() {
        // 添加焦点指示器
        const style = document.createElement('style');
        style.textContent = `
            .focus-visible {
                outline: 2px solid #007bff;
                outline-offset: 2px;
            }
            
            .skip-link {
                position: absolute;
                top: -40px;
                left: 6px;
                background: #000;
                color: #fff;
                padding: 8px;
                text-decoration: none;
                z-index: 9999;
            }
            
            .skip-link:focus {
                top: 6px;
            }
        `;
        document.head.appendChild(style);
        
        // 添加跳转链接
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.className = 'skip-link';
        skipLink.textContent = '跳转到主要内容';
        document.body.insertBefore(skipLink, document.body.firstChild);
    }

    /**
     * 设置屏幕阅读器支持
     */
    setupScreenReaderSupport() {
        // 添加ARIA标签
        document.querySelectorAll('button, a, input, select, textarea').forEach(el => {
            if (!el.getAttribute('aria-label') && !el.textContent.trim()) {
                const placeholder = el.getAttribute('placeholder');
                const title = el.getAttribute('title');
                if (placeholder) el.setAttribute('aria-label', placeholder);
                else if (title) el.setAttribute('aria-label', title);
            }
        });
        
        // 添加live region用于动态内容通知
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        liveRegion.id = 'live-region';
        document.body.appendChild(liveRegion);
    }

    /**
     * 设置交互反馈
     */
    setupInteractionFeedback() {
        // 表单验证反馈
        this.setupFormFeedback();
        
        // 加载状态反馈
        this.setupLoadingFeedback();
        
        // 操作成功反馈
        this.setupSuccessFeedback();
    }

    /**
     * 设置表单反馈
     */
    setupFormFeedback() {
        document.addEventListener('invalid', (e) => {
            e.target.classList.add('is-invalid');
            
            // 添加错误提示
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = e.target.validationMessage;
            e.target.parentNode.appendChild(feedback);
        });
        
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('is-invalid') && e.target.checkValidity()) {
                e.target.classList.remove('is-invalid');
                const feedback = e.target.parentNode.querySelector('.invalid-feedback');
                if (feedback) feedback.remove();
            }
        });
    }

    /**
     * 设置加载反馈
     */
    setupLoadingFeedback() {
        // 拦截fetch请求添加加载状态
        if (window.fetch) {
            const originalFetch = window.fetch;
            
            window.fetch = function(...args) {
                const loadingId = 'loading-' + Date.now();
                
                // 显示加载状态
                if (window.UI) {
                    const loading = window.UI.create('loading', {
                        text: '正在加载...',
                        size: 'small'
                    });
                    loading.show();
                    
                    return originalFetch.apply(this, args).finally(() => {
                        loading.hide();
                    });
                }
                
                return originalFetch.apply(this, args);
            };
        }
    }

    /**
     * 设置成功反馈
     */
    setupSuccessFeedback() {
        // 监听成功操作事件
        if (window.eventBus) {
            window.eventBus.on('operation:success', (data) => {
                if (window.UI) {
                    window.UI.showToast(data.message || '操作成功', 'success');
                }
            });
        }
    }

    /**
     * 设置进度指示器
     */
    setupProgressIndicators() {
        // 页面加载进度
        let loadProgress = 0;
        const progressBar = document.createElement('div');
        progressBar.className = 'page-progress';
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: #007bff;
            z-index: 9999;
            transition: width 0.3s ease;
        `;
        document.body.appendChild(progressBar);
        
        // 监听资源加载
        const updateProgress = () => {
            loadProgress += 10;
            progressBar.style.width = Math.min(loadProgress, 100) + '%';
            
            if (loadProgress >= 100) {
                setTimeout(() => {
                    progressBar.style.opacity = '0';
                    setTimeout(() => progressBar.remove(), 300);
                }, 500);
            }
        };
        
        // 模拟进度更新
        const interval = setInterval(updateProgress, 100);
        
        window.addEventListener('load', () => {
            clearInterval(interval);
            loadProgress = 100;
            updateProgress();
        });
    }

    /**
     * 设置智能默认值
     */
    setupSmartDefaults() {
        // 记住用户偏好
        this.rememberUserPreferences();
        
        // 智能表单填充
        this.setupSmartFormFilling();
        
        // 自动保存
        this.setupAutoSave();
    }

    /**
     * 记住用户偏好
     */
    rememberUserPreferences() {
        // 记住表格每页显示数量
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('page-size-select')) {
                localStorage.setItem('preferred-page-size', e.target.value);
            }
        });
        
        // 应用保存的偏好
        setTimeout(() => {
            const savedPageSize = localStorage.getItem('preferred-page-size');
            if (savedPageSize) {
                document.querySelectorAll('.page-size-select').forEach(select => {
                    if (select.querySelector(`option[value="${savedPageSize}"]`)) {
                        select.value = savedPageSize;
                    }
                });
            }
        }, 1000);
    }

    /**
     * 设置智能表单填充
     */
    setupSmartFormFilling() {
        document.addEventListener('focus', (e) => {
            if (e.target.tagName === 'INPUT' && e.target.type === 'text') {
                const savedValue = localStorage.getItem(`form-${e.target.name}`);
                if (savedValue && !e.target.value) {
                    e.target.value = savedValue;
                    e.target.style.backgroundColor = '#f8f9fa';
                }
            }
        });
        
        document.addEventListener('blur', (e) => {
            if (e.target.tagName === 'INPUT' && e.target.type === 'text' && e.target.value) {
                localStorage.setItem(`form-${e.target.name}`, e.target.value);
            }
        });
    }

    /**
     * 设置自动保存
     */
    setupAutoSave() {
        let autoSaveTimer;
        
        document.addEventListener('input', (e) => {
            if (e.target.tagName === 'TEXTAREA' || 
                (e.target.tagName === 'INPUT' && e.target.type === 'text')) {
                
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(() => {
                    this.autoSaveForm(e.target.form);
                }, 2000); // 2秒后自动保存
            }
        });
    }

    /**
     * 自动保存表单
     */
    autoSaveForm(form) {
        if (!form) return;
        
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        localStorage.setItem(`autosave-${form.id || 'form'}`, JSON.stringify(data));
        
        // 显示自动保存提示
        const indicator = document.createElement('div');
        indicator.textContent = '已自动保存';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 9999;
        `;
        
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => indicator.remove(), 300);
        }, 2000);
    }

    /**
     * 刷新当前模块
     */
    refreshCurrentModule() {
        if (window.GlobalState) {
            const currentModule = window.GlobalState.getState('currentModule');
            if (currentModule && window.smartModuleLoader) {
                window.smartModuleLoader.loadModule(currentModule, { forceReload: true });
                
                if (window.UI) {
                    window.UI.showToast('模块已刷新', 'info');
                }
            }
        }
    }

    /**
     * 关闭顶层模态框
     */
    closeTopModal() {
        const modals = document.querySelectorAll('.modal.show');
        if (modals.length > 0) {
            const topModal = modals[modals.length - 1];
            const bsModal = bootstrap.Modal.getInstance(topModal);
            if (bsModal) bsModal.hide();
        }
    }

    /**
     * 保存当前表单
     */
    saveCurrentForm() {
        const activeForm = document.activeElement.closest('form');
        if (activeForm) {
            const submitBtn = activeForm.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn) {
                submitBtn.click();
            }
        }
    }

    /**
     * 撤销上一步操作
     */
    undoLastAction() {
        if (window.eventBus) {
            window.eventBus.emit('action:undo');
        }
        
        if (window.UI) {
            window.UI.showToast('撤销功能开发中...', 'info');
        }
    }

    /**
     * 获取用户体验统计
     */
    getUXStats() {
        return {
            theme: document.documentElement.getAttribute('data-theme'),
            accessibility: this.accessibility,
            shortcutsCount: this.shortcuts.size,
            animationsEnabled: !this.accessibility.reducedMotion
        };
    }
}

// 创建全局用户体验增强器实例
if (typeof window !== 'undefined') {
    window.UXEnhancer = UXEnhancer;
    window.uxEnhancer = new UXEnhancer();
    
    console.log('🎯 用户体验增强器已就绪');
} 