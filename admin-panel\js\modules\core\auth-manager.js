/**
 * 认证管理器
 * 从admin.js第9-82行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class AuthManager {
    constructor() {
        this.token = localStorage.getItem(CONFIG.TOKEN_KEY);
        this.user = JSON.parse(localStorage.getItem(CONFIG.USER_KEY) || 'null');
        // 如果token是字符串"null"或无效token，将其设置为真正的null
        if (this.token === 'null' || this.token === null || !this.token || this.token.length < 10) {
            this.token = null;
            // 同时清除localStorage中的无效token
            localStorage.removeItem(CONFIG.TOKEN_KEY);
            localStorage.removeItem(CONFIG.USER_KEY);
        }
    }
    // 检查是否已登录
    isAuthenticated() {
        return this.token && this.user;
    }
    // 登录
    async login(username,
    password) {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username,
                    password
                })
            });
            const data = await response.json();
            // 适配后端返回格式：{
                code: 200,
                message: '登录成功',
                data: {
                    token: ...,
                    user: ...
                }
            }
            if (data.code === 200 && data.data) {
                this.token = data.data.token;
                this.user = data.data.user;
                localStorage.setItem(CONFIG.TOKEN_KEY,
                this.token);
                localStorage.setItem(CONFIG.USER_KEY,
                JSON.stringify(this.user));
                return {
                    success: true,
                    user: this.user
                };
            } else {
                return {
                    success: false,
                    message: data.message || '登录失败'
                };
            }
        } catch (error) {
            console.error('Login error:',
            error);
            return {
                success: false,
                message: '网络错误，请重试'
            };
        }
    }
    // 退出登录
    logout() {
        this.token = null;
        this.user = null;
        localStorage.removeItem(CONFIG.TOKEN_KEY);
        localStorage.removeItem(CONFIG.USER_KEY);
        location.reload();
    }
    // 获取当前用户信息
    getUser() {
        return this.user;
    }
    // 获取token
    getToken() {
        return this.token;
    }
    // 获取用户类型
    getUserType() {
        return this.user ? this.user.user_type : null;
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = AuthManager;
} else if (typeof window !== "undefined") {
    window.AuthManager = AuthManager;
}

console.log('📦 AuthManager 模块加载完成');
