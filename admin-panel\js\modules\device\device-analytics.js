/**
 * 设备数据分析模块
 * 提供设备使用分析、收益分析、智能推荐和效率优化建议功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class DeviceAnalytics {
    constructor() {
        this.apiBase = '/api/device';
        this.analyticsData = {};
        this.charts = {};
        this.timeRange = '7d'; // 默认7天
        this.isInitialized = false;
        
        console.log('DeviceAnalytics initialized');
    }

    /**
     * 初始化分析模块
     */
    async initialize() {
        try {
            console.log('🔧 初始化设备数据分析模块...');
            
            // 加载分析界面
            await this.loadAnalyticsInterface();
            
            // 加载分析数据
            await this.loadAnalyticsData();
            
            // 绑定事件监听
            this.bindEvents();
            
            this.isInitialized = true;
            console.log('✅ 设备数据分析模块初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 设备数据分析模块初始化失败:', error);
            return false;
        }
    }

    /**
     * 加载分析界面
     */
    async loadAnalyticsInterface() {
        const container = document.getElementById('main-content');
        if (!container) return;

        container.innerHTML = `
            <div class="device-analytics-container">
                <!-- 分析头部 -->
                <div class="analytics-header">
                    <div class="row">
                        <div class="col-md-8">
                            <h2><i class="bi bi-graph-up"></i> 设备数据分析</h2>
                            <p class="text-muted">设备使用情况分析、收益统计和智能优化建议</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="analytics-controls">
                                <select class="form-select d-inline-block w-auto" id="timeRange">
                                    <option value="1d">今天</option>
                                    <option value="7d" selected>最近7天</option>
                                    <option value="30d">最近30天</option>
                                    <option value="90d">最近90天</option>
                                </select>
                                <button class="btn btn-primary ms-2" id="exportReport">
                                    <i class="bi bi-download"></i> 导出报告
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 核心指标卡片 -->
                <div class="metrics-row">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-icon">
                                    <i class="bi bi-phone"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="totalDevices">0</div>
                                    <div class="metric-label">总设备数</div>
                                    <div class="metric-change" id="devicesChange">+0%</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-icon">
                                    <i class="bi bi-currency-dollar"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="totalRevenue">¥0</div>
                                    <div class="metric-label">总收益</div>
                                    <div class="metric-change" id="revenueChange">+0%</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-icon">
                                    <i class="bi bi-activity"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="avgEfficiency">0%</div>
                                    <div class="metric-label">平均效率</div>
                                    <div class="metric-change" id="efficiencyChange">+0%</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-icon">
                                    <i class="bi bi-receipt"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="totalOrders">0</div>
                                    <div class="metric-label">处理订单</div>
                                    <div class="metric-change" id="ordersChange">+0%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分析图表区域 -->
                <div class="charts-section">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">收益趋势分析</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="revenueChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">设备状态分布</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="statusChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设备效率分析 -->
                <div class="efficiency-section mt-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">设备效率排行</h5>
                                </div>
                                <div class="card-body">
                                    <div class="efficiency-ranking" id="efficiencyRanking">
                                        <!-- 效率排行列表 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">收益排行</h5>
                                </div>
                                <div class="card-body">
                                    <div class="revenue-ranking" id="revenueRanking">
                                        <!-- 收益排行列表 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 智能推荐 -->
                <div class="recommendations-section mt-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-lightbulb"></i> 智能优化建议</h5>
                        </div>
                        <div class="card-body">
                            <div class="recommendations-list" id="recommendationsList">
                                <!-- 推荐建议列表 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细分析表格 -->
                <div class="detailed-analysis-section mt-4">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-0">设备详细分析</h5>
                                </div>
                                <div class="col-auto">
                                    <div class="analysis-filters">
                                        <select class="form-select form-select-sm" id="sortBy">
                                            <option value="revenue">按收益排序</option>
                                            <option value="efficiency">按效率排序</option>
                                            <option value="orders">按订单量排序</option>
                                            <option value="usage">按使用率排序</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>设备ID</th>
                                            <th>设备名称</th>
                                            <th>收益</th>
                                            <th>订单数</th>
                                            <th>效率</th>
                                            <th>使用率</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="analyticsTable">
                                        <!-- 分析数据表格 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .device-analytics-container {
                    padding: 20px;
                }

                .analytics-header {
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 1px solid #e9ecef;
                }

                .analytics-controls {
                    display: flex;
                    align-items: center;
                }

                .metrics-row {
                    margin-bottom: 30px;
                }

                .metric-card {
                    background: white;
                    border-radius: 10px;
                    padding: 25px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    display: flex;
                    align-items: center;
                    margin-bottom: 20px;
                    transition: transform 0.2s;
                }

                .metric-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                }

                .metric-icon {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 24px;
                    margin-right: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .metric-value {
                    font-size: 28px;
                    font-weight: bold;
                    margin-bottom: 5px;
                    color: #2c3e50;
                }

                .metric-label {
                    color: #6c757d;
                    font-size: 14px;
                    margin-bottom: 5px;
                }

                .metric-change {
                    font-size: 12px;
                    font-weight: 500;
                }

                .metric-change.positive {
                    color: #28a745;
                }

                .metric-change.negative {
                    color: #dc3545;
                }

                .charts-section, .efficiency-section, .recommendations-section, .detailed-analysis-section {
                    margin-bottom: 30px;
                }

                .efficiency-ranking, .revenue-ranking {
                    max-height: 400px;
                    overflow-y: auto;
                }

                .ranking-item {
                    display: flex;
                    align-items: center;
                    padding: 12px;
                    border-bottom: 1px solid #e9ecef;
                    transition: background 0.2s;
                }

                .ranking-item:hover {
                    background: #f8f9fa;
                }

                .ranking-number {
                    width: 30px;
                    height: 30px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                    margin-right: 15px;
                    font-size: 14px;
                }

                .ranking-number.top1 {
                    background: #ffd700;
                    color: #fff;
                }

                .ranking-number.top2 {
                    background: #c0c0c0;
                    color: #fff;
                }

                .ranking-number.top3 {
                    background: #cd7f32;
                    color: #fff;
                }

                .ranking-number.other {
                    background: #f8f9fa;
                    color: #6c757d;
                }

                .ranking-info {
                    flex: 1;
                }

                .ranking-name {
                    font-weight: 500;
                    margin-bottom: 2px;
                }

                .ranking-value {
                    color: #6c757d;
                    font-size: 12px;
                }

                .ranking-metric {
                    font-weight: bold;
                    color: #007bff;
                }

                .recommendations-list {
                    max-height: 400px;
                    overflow-y: auto;
                }

                .recommendation-item {
                    padding: 15px;
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    margin-bottom: 15px;
                    background: #f8f9fa;
                }

                .recommendation-type {
                    font-weight: 500;
                    color: #007bff;
                    margin-bottom: 5px;
                }

                .recommendation-content {
                    margin-bottom: 10px;
                }

                .recommendation-impact {
                    font-size: 12px;
                    color: #28a745;
                }

                .analysis-filters {
                    display: flex;
                    gap: 10px;
                }

                .efficiency-badge {
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .efficiency-high {
                    background: #d4edda;
                    color: #155724;
                }

                .efficiency-medium {
                    background: #fff3cd;
                    color: #856404;
                }

                .efficiency-low {
                    background: #f8d7da;
                    color: #721c24;
                }

                .usage-bar {
                    width: 100px;
                    height: 8px;
                    background: #e9ecef;
                    border-radius: 4px;
                    overflow: hidden;
                }

                .usage-fill {
                    height: 100%;
                    background: linear-gradient(90deg, #28a745, #20c997);
                    transition: width 0.3s;
                }
            </style>
        `;
    }

    /**
     * 绑定事件监听
     */
    bindEvents() {
        // 时间范围选择
        document.getElementById('timeRange')?.addEventListener('change', (e) => {
            this.timeRange = e.target.value;
            this.loadAnalyticsData();
        });

        // 导出报告按钮
        document.getElementById('exportReport')?.addEventListener('click', () => {
            this.exportAnalyticsReport();
        });

        // 排序选择
        document.getElementById('sortBy')?.addEventListener('change', (e) => {
            this.sortAnalyticsTable(e.target.value);
        });
    }

    /**
     * 加载分析数据
     */
    async loadAnalyticsData() {
        try {
            console.log('🔄 加载设备分析数据...');
            
            const response = await this.makeApiRequest(`/analytics.php?action=device_analytics&timeRange=${this.timeRange}`, 'GET');
            
            if (response.error_code === 0) {
                this.analyticsData = response.data;
                
                // 更新核心指标
                this.updateMetrics();
                
                // 更新图表
                this.updateCharts();
                
                // 更新排行榜
                this.updateRankings();
                
                // 更新推荐建议
                this.updateRecommendations();
                
                // 更新详细分析表格
                this.updateAnalyticsTable();
                
                console.log('✅ 设备分析数据加载完成');
            } else {
                throw new Error(response.error_message);
            }
        } catch (error) {
            console.error('加载分析数据失败:', error);
            this.showError('加载分析数据失败: ' + error.message);
        }
    }

    /**
     * 更新核心指标
     */
    updateMetrics() {
        const metrics = this.analyticsData.metrics || {};
        
        // 总设备数
        document.getElementById('totalDevices').textContent = metrics.total_devices || 0;
        document.getElementById('devicesChange').textContent = this.formatChange(metrics.devices_change);
        document.getElementById('devicesChange').className = `metric-change ${metrics.devices_change >= 0 ? 'positive' : 'negative'}`;
        
        // 总收益
        document.getElementById('totalRevenue').textContent = this.formatCurrency(metrics.total_revenue || 0);
        document.getElementById('revenueChange').textContent = this.formatChange(metrics.revenue_change);
        document.getElementById('revenueChange').className = `metric-change ${metrics.revenue_change >= 0 ? 'positive' : 'negative'}`;
        
        // 平均效率
        document.getElementById('avgEfficiency').textContent = `${Math.round(metrics.avg_efficiency || 0)}%`;
        document.getElementById('efficiencyChange').textContent = this.formatChange(metrics.efficiency_change);
        document.getElementById('efficiencyChange').className = `metric-change ${metrics.efficiency_change >= 0 ? 'positive' : 'negative'}`;
        
        // 总订单数
        document.getElementById('totalOrders').textContent = metrics.total_orders || 0;
        document.getElementById('ordersChange').textContent = this.formatChange(metrics.orders_change);
        document.getElementById('ordersChange').className = `metric-change ${metrics.orders_change >= 0 ? 'positive' : 'negative'}`;
    }

    /**
     * 更新图表
     */
    updateCharts() {
        // 这里可以集成Chart.js或其他图表库
        console.log('更新分析图表...');
        
        // 收益趋势图表
        this.renderRevenueChart();
        
        // 设备状态分布图表
        this.renderStatusChart();
    }

    /**
     * 渲染收益趋势图表
     */
    renderRevenueChart() {
        const canvas = document.getElementById('revenueChart');
        if (!canvas) return;
        
        // 这里应该使用Chart.js等图表库
        // 暂时显示占位符
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#6c757d';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('收益趋势图表 (需要集成Chart.js)', canvas.width/2, canvas.height/2);
    }

    /**
     * 渲染设备状态分布图表
     */
    renderStatusChart() {
        const canvas = document.getElementById('statusChart');
        if (!canvas) return;
        
        // 这里应该使用Chart.js等图表库
        // 暂时显示占位符
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#6c757d';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('设备状态分布图 (需要集成Chart.js)', canvas.width/2, canvas.height/2);
    }

    /**
     * 更新排行榜
     */
    updateRankings() {
        const rankings = this.analyticsData.rankings || {};
        
        // 效率排行
        this.renderRanking('efficiencyRanking', rankings.efficiency || [], 'efficiency');
        
        // 收益排行
        this.renderRanking('revenueRanking', rankings.revenue || [], 'revenue');
    }

    /**
     * 渲染排行榜
     */
    renderRanking(containerId, data, type) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        if (data.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">暂无数据</p>';
            return;
        }
        
        container.innerHTML = data.map((item, index) => {
            const rankClass = index === 0 ? 'top1' : index === 1 ? 'top2' : index === 2 ? 'top3' : 'other';
            const value = type === 'efficiency' ? `${Math.round(item.efficiency)}%` : this.formatCurrency(item.revenue);
            
            return `
                <div class="ranking-item">
                    <div class="ranking-number ${rankClass}">${index + 1}</div>
                    <div class="ranking-info">
                        <div class="ranking-name">${item.device_name || item.device_id}</div>
                        <div class="ranking-value">${item.device_id}</div>
                    </div>
                    <div class="ranking-metric">${value}</div>
                </div>
            `;
        }).join('');
    }

    /**
     * 更新推荐建议
     */
    updateRecommendations() {
        const recommendations = this.analyticsData.recommendations || [];
        const container = document.getElementById('recommendationsList');
        
        if (!container) return;
        
        if (recommendations.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">暂无优化建议</p>';
            return;
        }
        
        container.innerHTML = recommendations.map(rec => `
            <div class="recommendation-item">
                <div class="recommendation-type">${rec.type}</div>
                <div class="recommendation-content">${rec.content}</div>
                <div class="recommendation-impact">预计影响: ${rec.impact}</div>
            </div>
        `).join('');
    }

    /**
     * 更新详细分析表格
     */
    updateAnalyticsTable() {
        const devices = this.analyticsData.devices || [];
        const tbody = document.getElementById('analyticsTable');
        
        if (!tbody) return;
        
        if (devices.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted">
                        <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                        <div>暂无分析数据</div>
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = devices.map(device => `
            <tr>
                <td>${device.device_id}</td>
                <td>${device.device_name || '-'}</td>
                <td>${this.formatCurrency(device.revenue || 0)}</td>
                <td>${device.orders || 0}</td>
                <td>
                    <span class="efficiency-badge efficiency-${this.getEfficiencyLevel(device.efficiency)}">
                        ${Math.round(device.efficiency || 0)}%
                    </span>
                </td>
                <td>
                    <div class="usage-bar">
                        <div class="usage-fill" style="width: ${device.usage_rate || 0}%"></div>
                    </div>
                    <small class="text-muted">${Math.round(device.usage_rate || 0)}%</small>
                </td>
                <td>
                    <span class="status-badge status-${device.status}">${this.getStatusText(device.status)}</span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="deviceAnalytics.viewDeviceDetail('${device.device_id}')" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 导出分析报告
     */
    async exportAnalyticsReport() {
        try {
            this.showInfo('正在生成分析报告...');
            
            const response = await this.makeApiRequest(`/analytics.php?action=export_report&timeRange=${this.timeRange}`, 'GET');
            
            if (response.error_code === 0) {
                // 创建下载链接
                const blob = new Blob([response.data.report], { type: 'application/vnd.ms-excel' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `设备分析报告_${this.timeRange}_${new Date().toISOString().split('T')[0]}.xlsx`;
                a.click();
                window.URL.revokeObjectURL(url);
                
                this.showSuccess('分析报告导出成功');
            } else {
                this.showError('导出分析报告失败: ' + response.error_message);
            }
        } catch (error) {
            console.error('导出分析报告失败:', error);
            this.showError('导出分析报告失败');
        }
    }

    /**
     * 工具方法
     */
    formatCurrency(amount) {
        return `¥${parseFloat(amount || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
    }

    formatChange(change) {
        const value = parseFloat(change || 0);
        const sign = value >= 0 ? '+' : '';
        return `${sign}${value.toFixed(1)}%`;
    }

    getEfficiencyLevel(efficiency) {
        const eff = parseFloat(efficiency || 0);
        if (eff >= 80) return 'high';
        if (eff >= 60) return 'medium';
        return 'low';
    }

    getStatusText(status) {
        const statusMap = {
            'online': '在线',
            'offline': '离线',
            'active': '活跃',
            'inactive': '非活跃'
        };
        return statusMap[status] || '未知';
    }

    /**
     * API请求方法
     */
    async makeApiRequest(url, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
            }
        };

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(this.apiBase + url, options);
        return await response.json();
    }

    showSuccess(message) {
        console.log('SUCCESS:', message);
    }

    showError(message) {
        console.log('ERROR:', message);
    }

    showInfo(message) {
        console.log('INFO:', message);
    }
}

// 全局实例
window.DeviceAnalytics = DeviceAnalytics;

if (typeof window !== 'undefined') {
    window.deviceAnalytics = new DeviceAnalytics();
} 