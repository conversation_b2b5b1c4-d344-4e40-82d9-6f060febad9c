<?php
/**
 * 对外API通用工具函数
 */

/**
 * 获取产品信息
 */
function getProductInfo($product_id, $merchant_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM products 
            WHERE id = ? AND merchant_id = ? AND status = 'active'
        ");
        $stmt->execute([$product_id, $merchant_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("获取产品信息失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 检查订单号是否已存在
 */
function checkOrderExists($order_no, $merchant_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT id FROM payment_requests 
            WHERE order_no = ? AND merchant_id = ?
        ");
        $stmt->execute([$order_no, $merchant_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC) !== false;
        
    } catch (Exception $e) {
        error_log("检查订单号失败: " . $e->getMessage());
        return true; // 出错时返回true，避免重复订单
    }
}

/**
 * 创建支付订单
 */
function createPaymentOrder($data) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO payment_requests (
                merchant_id, product_id, order_no, amount, 
                payable_amount, actual_amount, amount_diff, alipay_account_id,
                status, notification_url, created_at, fee_rate,
                product_name, client_ip, user_agent,
                payable_generated_at, account_occupied_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $data['merchant_id'],
            $data['product_id'], 
            $data['order_no'],
            $data['amount'],
            isset($data['payable_amount']) ? $data['payable_amount'] : $data['amount'],
            isset($data['actual_amount']) ? $data['actual_amount'] : $data['amount'],
            isset($data['amount_diff']) ? $data['amount_diff'] : 0.00,
            isset($data['alipay_account_id']) ? $data['alipay_account_id'] : null,
            isset($data['notification_url']) ? $data['notification_url'] : '',
            isset($data['fee_rate']) ? $data['fee_rate'] : 0,
            isset($data['product_name']) ? $data['product_name'] : '',
            isset($data['client_ip']) ? $data['client_ip'] : '',
            isset($data['user_agent']) ? $data['user_agent'] : ''
        ]);
        
        return $pdo->lastInsertId();
        
    } catch (Exception $e) {
        error_log("创建支付订单失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取可用的支付宝账户
 */
function getAvailableAlipayAccount($amount, $product_id) {
    global $pdo;
    
    try {
        // 查找可用的支付宝账户
        $stmt = $pdo->prepare("
            SELECT * FROM alipay_accounts 
            WHERE status = 'active' 
            AND min_amount <= ? 
            AND max_amount >= ?
            AND daily_limit > daily_used
            AND product_ids LIKE ?
            ORDER BY priority ASC, daily_used ASC
            LIMIT 1
        ");
        
        $product_pattern = '%,' . $product_id . ',%';
        $stmt->execute([$amount, $amount, $product_pattern]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("获取支付宝账户失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 生成支付页面URL
 */
function generatePaymentUrl($transaction_id, $alipay_account_id, $payable_amount = null) {
    // 生成支付页面的key
    $key = $alipay_account_id . '_' . time() . '_' . $transaction_id;
    
    // 构建URL参数
    $url_params = ['key' => $key];
    
    // 如果有用户应付金额，添加到URL参数中
    if ($payable_amount !== null) {
        $url_params['amount'] = $payable_amount;
    }
    
    // 这里应该根据实际的支付页面域名来生成
    $base_url = "https://pay.top670.com/";
    return $base_url . '?' . http_build_query($url_params);
}

/**
 * 验证金额格式
 */
function validateAmount($amount) {
    if (!is_numeric($amount)) {
        return false;
    }
    
    $amount = floatval($amount);
    return $amount > 0 && $amount <= 999999;
}

/**
 * 验证订单号格式
 */
function validateOrderNo($order_no) {
    if (empty($order_no) || strlen($order_no) > 50) {
        return false;
    }
    
    // 只允许数字和字母
    return preg_match('/^[a-zA-Z0-9]+$/', $order_no);
}

/**
 * 记录API调用日志
 */
function logApiCall($merchant_id, $api_name, $request_data, $response_data, $status = 'success') {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO api_logs (
                merchant_id, api_name, request_data, response_data, 
                status, client_ip, create_time
            ) VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $merchant_id,
            $api_name,
            json_encode($request_data, JSON_UNESCAPED_UNICODE),
            json_encode($response_data, JSON_UNESCAPED_UNICODE),
            $status,
            getClientIP()
        ]);
        
    } catch (Exception $e) {
        error_log("记录API日志失败: " . $e->getMessage());
    }
}

/**
 * 获取客户端IP（从api.php复制）
 */
function getClientIP() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
}
?> 