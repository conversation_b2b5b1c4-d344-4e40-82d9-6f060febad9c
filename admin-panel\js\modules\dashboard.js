// 仪表板模块
// import CoreModule from './core.js'; // 注释掉ES6导入，使用全局变量

class DashboardManager {
    constructor() {
        this.widgets = new Map();
        this.updateInterval = null;
        this.charts = new Map();
    }

    // 渲染仪表板
    async render() {
        const startTime = performance.now();
        
        const html = `
            <div class="dashboard-container">
                <div class="dashboard-header">
                    <h1 class="dashboard-title">
                        <i class="fas fa-tachometer-alt me-3"></i>
                        系统仪表板
                    </h1>
                    <div class="dashboard-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="window.dashboardManager.refresh()">
                            <i class="fas fa-sync-alt me-2"></i>刷新数据
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="window.dashboardManager.exportReport()">
                            <i class="fas fa-download me-2"></i>导出报告
                        </button>
                    </div>
                </div>
                
                <div class="dashboard-content">
                    <!-- 关键指标卡片 -->
                    <div class="metrics-grid">
                        <div class="metric-card" id="totalUsers">
                            <div class="metric-icon bg-primary">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">--</div>
                                <div class="metric-label">总用户数</div>
                                <div class="metric-change">--</div>
                            </div>
                        </div>
                        
                        <div class="metric-card" id="totalTransactions">
                            <div class="metric-icon bg-success">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">--</div>
                                <div class="metric-label">今日交易</div>
                                <div class="metric-change">--</div>
                            </div>
                        </div>
                        
                        <div class="metric-card" id="totalRevenue">
                            <div class="metric-icon bg-warning">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">--</div>
                                <div class="metric-label">今日收入</div>
                                <div class="metric-change">--</div>
                            </div>
                        </div>
                        
                        <div class="metric-card" id="systemHealth">
                            <div class="metric-icon bg-info">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">--</div>
                                <div class="metric-label">系统健康度</div>
                                <div class="metric-change">--</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 图表区域 -->
                    <div class="charts-grid">
                        <div class="chart-container">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-chart-line me-2"></i>
                                        交易趋势
                                    </h5>
                                    <div class="card-actions">
                                        <select class="form-select form-select-sm" id="transactionPeriod">
                                            <option value="7">近7天</option>
                                            <option value="30">近30天</option>
                                            <option value="90">近90天</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <canvas id="transactionChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <div class="chart-container">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-chart-pie me-2"></i>
                                        支付方式分布
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="paymentMethodChart" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 活动列表 -->
                    <div class="activity-section">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-clock me-2"></i>
                                    最近活动
                                </h5>
                                <button class="btn btn-outline-primary btn-sm" onclick="window.dashboardManager.loadMoreActivities()">
                                    <i class="fas fa-plus me-2"></i>加载更多
                                </button>
                            </div>
                            <div class="card-body p-0">
                                <div class="activity-list" id="activityList">
                                    <div class="activity-loading text-center p-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <style>
                .dashboard-container {
                    max-width: 1400px;
                    margin: 0 auto;
                }
                
                .dashboard-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 1px solid #e5e7eb;
                }
                
                .dashboard-title {
                    margin: 0;
                    color: #1f2937;
                    font-weight: 600;
                    font-size: 2rem;
                }
                
                .dashboard-actions {
                    display: flex;
                    gap: 10px;
                }
                
                .metrics-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin-bottom: 30px;
                }
                
                .metric-card {
                    background: white;
                    border-radius: 12px;
                    padding: 24px;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                    display: flex;
                    align-items: center;
                    transition: all 0.3s ease;
                }
                
                .metric-card:hover {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    transform: translateY(-2px);
                }
                
                .metric-icon {
                    width: 60px;
                    height: 60px;
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                    margin-right: 20px;
                }
                
                .metric-icon.bg-primary { background: #3b82f6; }
                .metric-icon.bg-success { background: #10b981; }
                .metric-icon.bg-warning { background: #f59e0b; }
                .metric-icon.bg-info { background: #06b6d4; }
                
                .metric-content {
                    flex: 1;
                }
                
                .metric-value {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1f2937;
                    line-height: 1;
                    margin-bottom: 4px;
                }
                
                .metric-label {
                    font-size: 0.9rem;
                    color: #6b7280;
                    margin-bottom: 8px;
                }
                
                .metric-change {
                    font-size: 0.8rem;
                    font-weight: 500;
                    padding: 2px 8px;
                    border-radius: 4px;
                    display: inline-block;
                }
                
                .metric-change.positive {
                    background: #ecfdf5;
                    color: #065f46;
                }
                
                .metric-change.negative {
                    background: #fef2f2;
                    color: #991b1b;
                }
                
                .charts-grid {
                    display: grid;
                    grid-template-columns: 2fr 1fr;
                    gap: 20px;
                    margin-bottom: 30px;
                }
                
                .chart-container {
                    min-height: 400px;
                }
                
                .card-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .card-actions {
                    display: flex;
                    gap: 10px;
                }
                
                .activity-section {
                    margin-bottom: 30px;
                }
                
                .activity-list {
                    max-height: 400px;
                    overflow-y: auto;
                }
                
                .activity-item {
                    display: flex;
                    align-items: center;
                    padding: 16px 24px;
                    border-bottom: 1px solid #f3f4f6;
                    transition: background-color 0.2s ease;
                }
                
                .activity-item:hover {
                    background: #f8fafc;
                }
                
                .activity-item:last-child {
                    border-bottom: none;
                }
                
                .activity-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1rem;
                    margin-right: 16px;
                }
                
                .activity-content {
                    flex: 1;
                }
                
                .activity-title {
                    font-weight: 500;
                    color: #1f2937;
                    margin-bottom: 4px;
                }
                
                .activity-description {
                    font-size: 0.85rem;
                    color: #6b7280;
                    margin-bottom: 4px;
                }
                
                .activity-time {
                    font-size: 0.8rem;
                    color: #9ca3af;
                }
                
                @media (max-width: 768px) {
                    .dashboard-header {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 15px;
                    }
                    
                    .metrics-grid {
                        grid-template-columns: 1fr;
                    }
                    
                    .charts-grid {
                        grid-template-columns: 1fr;
                    }
                    
                    .metric-card {
                        padding: 20px;
                    }
                    
                    .metric-icon {
                        width: 50px;
                        height: 50px;
                        font-size: 1.25rem;
                        margin-right: 15px;
                    }
                    
                    .metric-value {
                        font-size: 1.5rem;
                    }
                }
            </style>
        `;
        
        // 记录渲染时间
        PerformanceMonitor.recordMetric('dashboardRender', performance.now() - startTime);
        
        return html;
    }

    // 初始化仪表板
    async init() {
        try {
            console.log('🎯 初始化仪表板...');
            
            // 设置全局引用
            window.dashboardManager = this;
            
            // 加载初始数据
            await this.loadInitialData();
            
            // 初始化图表
            await this.initializeCharts();
            
            // 设置自动刷新
            this.setupAutoRefresh();
            
            // 绑定事件
            this.bindEvents();
            
            console.log('✅ 仪表板初始化完成');
            
        } catch (error) {
            console.error('❌ 仪表板初始化失败:', error);
            throw error;
        }
    }

    // 加载初始数据
    async loadInitialData() {
        const startTime = performance.now();
        
        try {
            // 并行加载多个数据源
            const [metricsData, activitiesData] = await Promise.all([
                this.loadMetrics(),
                this.loadActivities()
            ]);
            
            // 更新指标卡片
            this.updateMetricCards(metricsData);
            
            // 更新活动列表
            this.updateActivityList(activitiesData);
            
            PerformanceMonitor.recordMetric('dashboardDataLoad', performance.now() - startTime);
            
        } catch (error) {
            console.error('数据加载失败:', error);
            this.showErrorState();
        }
    }

    // 加载指标数据
    async loadMetrics() {
        const response = await AdminUtils.apiRequest('/admin.php?action=dashboard_metrics');
        
        if (response.success) {
            return response.data;
        }
        
        // 返回模拟数据
        return {
            totalUsers: { value: 1234, change: 12.5, trend: 'up' },
            totalTransactions: { value: 567, change: -3.2, trend: 'down' },
            totalRevenue: { value: 89012, change: 8.7, trend: 'up' },
            systemHealth: { value: 94, change: 2.1, trend: 'up' }
        };
    }

    // 加载活动数据
    async loadActivities() {
        const response = await AdminUtils.apiRequest('/admin.php?action=dashboard_activities');
        
        if (response.success) {
            return response.data;
        }
        
        // 返回模拟数据
        return [
            {
                id: 1,
                type: 'transaction',
                title: '新交易',
                description: '用户 user123 完成了一笔 $150.00 的交易',
                time: '2分钟前',
                icon: 'fas fa-exchange-alt',
                iconColor: '#10b981'
            },
            {
                id: 2,
                type: 'user',
                title: '新用户注册',
                description: '新用户 newuser456 已注册',
                time: '5分钟前',
                icon: 'fas fa-user-plus',
                iconColor: '#3b82f6'
            },
            {
                id: 3,
                type: 'system',
                title: '系统维护',
                description: '数据库优化任务已完成',
                time: '15分钟前',
                icon: 'fas fa-cog',
                iconColor: '#6b7280'
            }
        ];
    }

    // 更新指标卡片
    updateMetricCards(data) {
        Object.entries(data).forEach(([key, metric]) => {
            const card = document.getElementById(key);
            if (!card) return;
            
            const valueEl = card.querySelector('.metric-value');
            const changeEl = card.querySelector('.metric-change');
            
            if (valueEl) {
                // 格式化数值
                let formattedValue = metric.value;
                if (key === 'totalRevenue') {
                    formattedValue = `$${metric.value.toLocaleString()}`;
                } else if (key === 'systemHealth') {
                    formattedValue = `${metric.value}%`;
                } else {
                    formattedValue = metric.value.toLocaleString();
                }
                
                // 动画更新数值
                this.animateValue(valueEl, 0, metric.value, 1000, formattedValue);
            }
            
            if (changeEl) {
                const changeText = metric.change > 0 ? `+${metric.change}%` : `${metric.change}%`;
                changeEl.textContent = changeText;
                changeEl.className = `metric-change ${metric.trend === 'up' ? 'positive' : 'negative'}`;
            }
        });
    }

    // 数值动画
    animateValue(element, start, end, duration, formatter) {
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = start + (end - start) * this.easeOutQuart(progress);
            
            if (typeof formatter === 'function') {
                element.textContent = formatter(Math.round(current));
            } else if (typeof formatter === 'string' && formatter.includes('$')) {
                element.textContent = formatter.replace(/\d+/, Math.round(current).toLocaleString());
            } else if (typeof formatter === 'string' && formatter.includes('%')) {
                element.textContent = `${Math.round(current)}%`;
            } else {
                element.textContent = Math.round(current).toLocaleString();
            }
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }

    // 缓动函数
    easeOutQuart(t) {
        return 1 - Math.pow(1 - t, 4);
    }

    // 更新活动列表
    updateActivityList(activities) {
        const listEl = document.getElementById('activityList');
        if (!listEl) return;
        
        const html = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon" style="background: ${activity.iconColor}">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-description">${activity.description}</div>
                    <div class="activity-time">${activity.time}</div>
                </div>
            </div>
        `).join('');
        
        listEl.innerHTML = html;
    }

    // 初始化图表
    async initializeCharts() {
        try {
            // 懒加载Chart.js
            if (!window.Chart) {
                await this.loadChartJS();
            }
            
            // 初始化交易趋势图表
            await this.initTransactionChart();
            
            // 初始化支付方式分布图表
            await this.initPaymentMethodChart();
            
        } catch (error) {
            console.error('图表初始化失败:', error);
        }
    }

    // 加载Chart.js
    async loadChartJS() {
        // 使用Chart.js加载器
        if (window.ChartLoader) {
            return window.ChartLoader.load();
        }

        // 备用加载方式
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'js/libs/chart.min.js';
            script.onload = () => resolve(window.Chart);
            script.onerror = () => {
                // 本地加载失败，尝试CDN
                const cdnScript = document.createElement('script');
                cdnScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.1/chart.min.js';
                cdnScript.onload = () => resolve(window.Chart);
                cdnScript.onerror = reject;
                document.head.appendChild(cdnScript);
            };
            document.head.appendChild(script);
        });
    }

    // 初始化交易趋势图表
    async initTransactionChart() {
        const ctx = document.getElementById('transactionChart');
        if (!ctx) return;
        
        const data = await this.getTransactionChartData();
        
        this.charts.set('transaction', new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f3f4f6'
                        }
                    },
                    x: {
                        grid: {
                            color: '#f3f4f6'
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.4
                    }
                }
            }
        }));
    }

    // 初始化支付方式分布图表
    async initPaymentMethodChart() {
        const ctx = document.getElementById('paymentMethodChart');
        if (!ctx) return;
        
        const data = await this.getPaymentMethodChartData();
        
        this.charts.set('paymentMethod', new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        }));
    }

    // 获取交易趋势图表数据
    async getTransactionChartData() {
        // 这里应该从API获取真实数据
        return {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            datasets: [{
                label: '交易金额',
                data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                fill: true
            }, {
                label: '交易笔数',
                data: [65, 59, 80, 81, 56, 55, 40],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                fill: true
            }]
        };
    }

    // 获取支付方式分布图表数据
    async getPaymentMethodChartData() {
        return {
            labels: ['PayPal', '信用卡', '银行转账', '数字钱包', '其他'],
            datasets: [{
                data: [45, 25, 15, 10, 5],
                backgroundColor: [
                    '#3b82f6',
                    '#10b981',
                    '#f59e0b',
                    '#ef4444',
                    '#6b7280'
                ]
            }]
        };
    }

    // 设置自动刷新
    setupAutoRefresh() {
        // 每30秒刷新一次数据
        this.updateInterval = setInterval(() => {
            this.refresh();
        }, 30000);
    }

    // 绑定事件
    bindEvents() {
        // 交易周期选择
        const periodSelect = document.getElementById('transactionPeriod');
        if (periodSelect) {
            periodSelect.addEventListener('change', (e) => {
                this.updateTransactionChart(e.target.value);
            });
        }
    }

    // 刷新数据
    async refresh() {
        try {
            await this.loadInitialData();
            
            // 更新图表
            if (this.charts.has('transaction')) {
                const data = await this.getTransactionChartData();
                this.charts.get('transaction').data = data;
                this.charts.get('transaction').update();
            }
            
            if (this.charts.has('paymentMethod')) {
                const data = await this.getPaymentMethodChartData();
                this.charts.get('paymentMethod').data = data;
                this.charts.get('paymentMethod').update();
            }
            
            console.log('✅ 仪表板数据已刷新');
            
        } catch (error) {
            console.error('❌ 数据刷新失败:', error);
        }
    }

    // 更新交易图表
    async updateTransactionChart(period) {
        const chart = this.charts.get('transaction');
        if (!chart) return;
        
        // 根据周期获取数据
        const data = await this.getTransactionChartData(period);
        chart.data = data;
        chart.update();
    }

    // 加载更多活动
    async loadMoreActivities() {
        try {
            const activities = await this.loadActivities();
            this.updateActivityList(activities);
        } catch (error) {
            console.error('加载活动失败:', error);
        }
    }

    // 导出报告
    async exportReport() {
        try {
            if (!window.exportManager) {
                // const { DataExportManager } = await import('./data-manager.js'); // 注释掉ES6导入
            const DataExportManager = window.DataExportManager || class {
                async exportToCSV(data, filename) {
                    console.log('导出CSV功能暂未实现:', filename, data);
                }
            };
                window.exportManager = new DataExportManager();
            }
            
            const data = await this.generateReportData();
            await window.exportManager.exportToCSV(data, `dashboard-report-${new Date().toISOString().split('T')[0]}.csv`);
            
            if (window.uiManager) {
                window.uiManager.showToast('报告导出成功', 'success');
            }
            
        } catch (error) {
            console.error('报告导出失败:', error);
            if (window.uiManager) {
                window.uiManager.showToast('报告导出失败', 'danger');
            }
        }
    }

    // 生成报告数据
    async generateReportData() {
        const metrics = await this.loadMetrics();
        const activities = await this.loadActivities();
        
        return {
            metrics: Object.entries(metrics).map(([key, value]) => ({
                指标: key,
                数值: value.value,
                变化: `${value.change}%`,
                趋势: value.trend
            })),
            activities: activities.map(activity => ({
                类型: activity.type,
                标题: activity.title,
                描述: activity.description,
                时间: activity.time
            }))
        };
    }

    // 显示错误状态
    showErrorState() {
        const container = document.querySelector('.dashboard-content');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <h5>数据加载失败</h5>
                    <p>无法加载仪表板数据，请检查网络连接或稍后重试。</p>
                    <button class="btn btn-primary" onclick="window.dashboardManager.refresh()">
                        <i class="fas fa-redo me-2"></i>重新加载
                    </button>
                </div>
            `;
        }
    }

    // 销毁仪表板
    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.charts.forEach(chart => {
            chart.destroy();
        });
        
        this.charts.clear();
        this.widgets.clear();
    }
}

// 全局导出
window.DashboardManager = DashboardManager;

// 创建全局实例
window.dashboardManager = window.dashboardManager || new DashboardManager();

// 模块对象导出
window.DashboardModule = {
    DashboardManager,
    instance: window.dashboardManager,
    
    // 便捷方法
    render: async () => {
        const manager = new DashboardManager();
        return await manager.render();
    },
    
    init: async () => {
        if (window.dashboardManager) {
            await window.dashboardManager.init();
        } else {
            const manager = new DashboardManager();
            await manager.init();
        }
    }
};

// ES6模块导出兼容
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DashboardManager };
}

console.log('✅ 仪表板模块加载完成'); 