/**
 * 商户产品管理器
 * 从admin.js第18680-19372行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class MerchantProductManager {
    constructor() {
        this.auth = new AuthManager();
        this.products = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.totalPages = 1;
        this.filters = {
            status: '',
            search: '',
            category: ''
        };
        this.sortBy = 'created_at';
        this.sortOrder = 'desc';
    }
    async initialize() {
        try {
            await this.loadProducts();
            this.renderProductManagement();
        } catch (error) {
            console.error('初始化产品管理失败:',
            error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }
    async loadProducts(page = 1) {
        this.currentPage = page;
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.itemsPerPage,
                sort_by: this.sortBy,
                sort_order: this.sortOrder,
                ...this.filters
            });
            const response = await fetch(`/api/merchant/index.php?module=tools&action=get_products&${
                params
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.products = data.data?.products || data.products || [];
                this.totalPages = data.data?.total_pages || data.total_pages || 1;
                return true;
            } else {
                this.showError('加载产品列表失败: ' + (data.message || '未知错误'));
                return false;
            }
        } catch (error) {
            console.error('加载产品列表失败:',
            error);
            this.showError('网络错误，请重试');
            return false;
        }
    }
    renderProductManagement() {
        const container = document.getElementById('merchantProductsContainer');
        if (!container) return;
        container.innerHTML = `
        <!-- 产品管理头部 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4 class="mb-1">产品管理</h4>
        <small class="text-muted">管理您的支付产品和配额设置</small>
        </div>
        <div class="btn-group">
        <button class="btn btn-primary" onclick="window.merchantProductManager.showAddProductModal()">
        <i class="bi bi-plus-circle me-2"></i>添加产品
        </button>
        <button class="btn btn-outline-primary" onclick="window.merchantProductManager.exportProducts()">
        <i class="bi bi-download me-2"></i>导出
        </button>
        <button class="btn btn-outline-secondary" onclick="window.merchantProductManager.refreshProducts()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        <!-- 筛选和搜索 -->
        <div class="card mb-4">
        <div class="card-body">
        <div class="row">
        <div class="col-md-3">
        <label class="form-label">产品状态</label>
        <select class="form-select" id="productStatusFilter" onchange="window.merchantProductManager.applyFilters()">
        <option value="">全部状态</option>
        <option value="active" ${
            this.filters.status === 'active' ? 'selected' : ''
        }>启用</option>
        <option value="inactive" ${
            this.filters.status === 'inactive' ? 'selected' : ''
        }>禁用</option>
        <option value="quota_full" ${
            this.filters.status === 'quota_full' ? 'selected' : ''
        }>额度已满</option>
        </select>
        </div>
        <div class="col-md-3">
        <label class="form-label">产品类别</label>
        <select class="form-select" id="productCategoryFilter" onchange="window.merchantProductManager.applyFilters()">
        <option value="">全部类别</option>
        <option value="alipay" ${
            this.filters.category === 'alipay' ? 'selected' : ''
        }>支付宝</option>
        <option value="wechat" ${
            this.filters.category === 'wechat' ? 'selected' : ''
        }>微信支付</option>
        <option value="qq" ${
            this.filters.category === 'qq' ? 'selected' : ''
        }>QQ钱包</option>
        </select>
        </div>
        <div class="col-md-4">
        <label class="form-label">搜索产品</label>
        <div class="input-group">
        <input type="text" class="form-control" id="productSearchInput"
        placeholder="输入产品名称或编号..."
        value="${
            this.filters.search
        }"
        onkeypress="if(event.key==='Enter') window.merchantProductManager.applyFilters()">
        <button class="btn btn-outline-secondary" onclick="window.merchantProductManager.applyFilters()">
        <i class="bi bi-search"></i>
        </button>
        </div>
        </div>
        <div class="col-md-2">
        <label class="form-label">排序方式</label>
        <select class="form-select" id="productSortSelect" onchange="window.merchantProductManager.applySorting()">
        <option value="created_at,
        desc" ${
            this.sortBy === 'created_at' && this.sortOrder === 'desc' ? 'selected' : ''
        }>创建时间↓</option>
        <option value="created_at,
        asc" ${
            this.sortBy === 'created_at' && this.sortOrder === 'asc' ? 'selected' : ''
        }>创建时间↑</option>
        <option value="name,
        asc" ${
            this.sortBy === 'name' && this.sortOrder === 'asc' ? 'selected' : ''
        }>产品名称↑</option>
        <option value="name,
        desc" ${
            this.sortBy === 'name' && this.sortOrder === 'desc' ? 'selected' : ''
        }>产品名称↓</option>
        <option value="quota_used,
        desc" ${
            this.sortBy === 'quota_used' && this.sortOrder === 'desc' ? 'selected' : ''
        }>使用量↓</option>
        <option value="quota_used,
        asc" ${
            this.sortBy === 'quota_used' && this.sortOrder === 'asc' ? 'selected' : ''
        }>使用量↑</option>
        </select>
        </div>
        </div>
        </div>
        </div>
        <!-- 产品列表 -->
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-box me-2"></i>产品列表
        <span class="badge bg-secondary ms-2">${
            this.products.length
        } 个产品</span>
        </h6>
        </div>
        <div class="card-body p-0">
        ${
            this.renderProductList()
        }
        </div>
        <div class="card-footer">
        ${
            this.renderPagination()
        }
        </div>
        </div>
        `;
    }
    renderProductList() {
        if (this.products.length === 0) {
            return `
            <div class="text-center py-5">
            <i class="bi bi-box" style="font-size: 3rem;
            color: #6c757d;
            "></i>
            <h5 class="text-muted mt-3">暂无产品</h5>
            <p class="text-muted">点击"添加产品"按钮创建您的第一个产品</p>
            <button class="btn btn-primary" onclick="window.merchantProductManager.showAddProductModal()">
            <i class="bi bi-plus-circle me-2"></i>添加产品
            </button>
            </div>
            `;
        }
        return `
        <div class="table-responsive">
        <table class="table table-hover mb-0">
        <thead class="table-light">
        <tr>
        <th>产品信息</th>
        <th>类别</th>
        <th>状态</th>
        <th>配额使用</th>
        <th>费率</th>
        <th>创建时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        ${
            this.products.map(product => this.renderProductRow(product)).join('')
        }
        </tbody>
        </table>
        </div>
        `;
    }
    renderProductRow(product) {
        const statusConfig = {
            'active': {
                class: 'success',
                text: '启用',
                icon: 'check-circle'
            },
            'inactive': {
                class: 'secondary',
                text: '禁用',
                icon: 'x-circle'
            },
            'quota_full': {
                class: 'warning',
                text: '额度已满',
                icon: 'exclamation-triangle'
            }
        };
        const categoryConfig = {
            'alipay': {
                class: 'primary',
                text: '支付宝',
                icon: 'alipay'
            },
            'wechat': {
                class: 'success',
                text: '微信支付',
                icon: 'wechat'
            },
            'qq': {
                class: 'info',
                text: 'QQ钱包',
                icon: 'qq'
            }
        };
        const status = statusConfig[product.status] || {
            class: 'secondary',
            text: '未知',
            icon: 'question'
        };
        const category = categoryConfig[product.category] || {
            class: 'secondary',
            text: '其他',
            icon: 'credit-card'
        };
        const quotaPercentage = product.quota_total > 0 ? ((product.quota_used / product.quota_total) * 100).toFixed(1) : 0;
        const quotaClass = quotaPercentage >= 90 ? 'danger' : quotaPercentage >= 70 ? 'warning' : 'success';
        return `
        <tr>
        <td>
        <div class="d-flex align-items-center">
        <div class="product-avatar me-3">
        <div class="bg-light rounded p-2">
        <i class="bi bi-${
            category.icon
        } text-${
            category.class
        }"></i>
        </div>
        </div>
        <div>
        <div class="fw-bold">${
            product.name
        }</div>
        <small class="text-muted">ID: ${
            product.id
        }</small>
        ${
            product.description ? `<br><small class="text-muted">${
                product.description
            }</small>` : ''
        }
        </div>
        </div>
        </td>
        <td>
        <span class="badge bg-${
            category.class
        }">
        <i class="bi bi-${
            category.icon
        } me-1"></i>${
            category.text
        }
        </span>
        </td>
        <td>
        <span class="badge bg-${
            status.class
        }">
        <i class="bi bi-${
            status.icon
        } me-1"></i>${
            status.text
        }
        </span>
        </td>
        <td>
        <div class="quota-info">
        <div class="d-flex justify-content-between align-items-center mb-1">
        <small class="text-muted">已用: ${
            product.quota_used || 0
        }</small>
        <small class="text-muted">总计: ${
            product.quota_total || 0
        }</small>
        </div>
        <div class="progress" style="height: 6px;
        ">
        <div class="progress-bar bg-${
            quotaClass
        }" style="width: ${
            quotaPercentage
        }%"></div>
        </div>
        <small class="text-${
            quotaClass
        }">${
            quotaPercentage
        }% 已使用</small>
        </div>
        </td>
        <td>
        <strong class="text-primary">${
            product.rate || 0
        }%</strong>
        </td>
        <td>
        <div>${
            this.formatDateTime(product.created_at)
        }</div>
        <small class="text-muted">${
            this.getRelativeTime(product.created_at)
        }</small>
        </td>
        <td>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-primary" onclick="window.merchantProductManager.viewProduct('${
            product.id
        }')" title="查看详情">
        <i class="bi bi-eye"></i>
        </button>
        <button class="btn btn-outline-secondary" onclick="window.merchantProductManager.editProduct('${
            product.id
        }')" title="编辑">
        <i class="bi bi-pencil"></i>
        </button>
        <button class="btn btn-outline-info" onclick="window.merchantProductManager.manageQuota('${
            product.id
        }')" title="配额管理">
        <i class="bi bi-speedometer2"></i>
        </button>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" title="更多操作">
        <i class="bi bi-three-dots"></i>
        </button>
        <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#" onclick="window.merchantProductManager.toggleProductStatus('${
            product.id
        }')">
        <i class="bi bi-${
            product.status === 'active' ? 'pause' : 'play'
        } me-2"></i>
        ${
            product.status === 'active' ? '禁用' : '启用'
        }产品
        </a></li>
        <li><a class="dropdown-item" href="#" onclick="window.merchantProductManager.duplicateProduct('${
            product.id
        }')">
        <i class="bi bi-files me-2"></i>复制产品
        </a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item text-danger" href="#" onclick="window.merchantProductManager.deleteProduct('${
            product.id
        }')">
        <i class="bi bi-trash me-2"></i>删除产品
        </a></li>
        </ul>
        </div>
        </div>
        </td>
        </tr>
        `;
    }
    renderPagination() {
        if (this.totalPages <= 1) return '';
        const pages = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1,
        this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(this.totalPages,
        startPage + maxVisiblePages - 1);
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1,
            endPage - maxVisiblePages + 1);
        }
        for (let i = startPage;
        i <= endPage;
        i++) {
            pages.push(i);
        }
        return `
        <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
        显示第 ${
            (this.currentPage - 1) * this.itemsPerPage + 1
        } - ${
            Math.min(this.currentPage * this.itemsPerPage,
            this.products.length)
        } 条，共 ${
            this.products.length
        } 条记录
        </div>
        <nav>
        <ul class="pagination pagination-sm mb-0">
        <li class="page-item ${
            this.currentPage === 1 ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.merchantProductManager.goToPage(${
            this.currentPage - 1
        })">
        <i class="bi bi-chevron-left"></i>
        </a>
        </li>
        ${
            pages.map(page => `
            <li class="page-item ${
                page === this.currentPage ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.merchantProductManager.goToPage(${
                page
            })">${
                page
            }</a>
            </li>
            `).join('')
        }
        <li class="page-item ${
            this.currentPage === this.totalPages ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.merchantProductManager.goToPage(${
            this.currentPage + 1
        })">
        <i class="bi bi-chevron-right"></i>
        </a>
        </li>
        </ul>
        </nav>
        </div>
        `;
    }
    // 工具函数
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    getRelativeTime(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${
            diffMins
        }分钟前`;
        if (diffHours < 24) return `${
            diffHours
        }小时前`;
        if (diffDays < 30) return `${
            diffDays
        }天前`;
        return '';
    }
    // 交互功能
    async applyFilters() {
        this.filters.status = document.getElementById('productStatusFilter').value;
        this.filters.category = document.getElementById('productCategoryFilter').value;
        this.filters.search = document.getElementById('productSearchInput').value.trim();
        await this.loadProducts(1);
        this.renderProductManagement();
    }
    async applySorting() {
        const sortValue = document.getElementById('productSortSelect').value;
        const [sortBy,
        sortOrder] = sortValue.split(',');
        this.sortBy = sortBy;
        this.sortOrder = sortOrder;
        await this.loadProducts(1);
        this.renderProductManagement();
    }
    async goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) return;
        await this.loadProducts(page);
        this.renderProductManagement();
    }
    async refreshProducts() {
        const container = document.getElementById('merchantProductsContainer');
        if (container) {
            container.innerHTML = `
            <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">刷新中...</span>
            </div>
            <div class="mt-3">正在刷新产品列表...</div>
            </div>
            `;
        }
        await this.loadProducts(this.currentPage);
        this.renderProductManagement();
        this.showSuccess('产品列表已刷新');
    }
    // 产品操作功能
    showAddProductModal() {
        // 创建模态框HTML
        const modalHtml = `
        <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">
        <i class="bi bi-plus-circle me-2"></i>添加新产品
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="addProductForm">
        <div class="row">
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">产品名称 *</label>
        <input type="text" class="form-control" id="productName" required>
        </div>
        </div>
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">产品类别 *</label>
        <select class="form-select" id="productCategory" required>
        <option value="">请选择类别</option>
        <option value="alipay">支付宝</option>
        <option value="wechat">微信支付</option>
        <option value="qq">QQ钱包</option>
        </select>
        </div>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">产品描述</label>
        <textarea class="form-control" id="productDescription" rows="3" placeholder="请输入产品描述..."></textarea>
        </div>
        <div class="row">
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">费率 (%) *</label>
        <div class="input-group">
        <input type="number" class="form-control" id="productRate" step="0.01" min="0" max="100" required>
        <span class="input-group-text">%</span>
        </div>
        </div>
        </div>
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">初始配额</label>
        <input type="number" class="form-control" id="productQuota" min="0" placeholder="0 表示无限制">
        </div>
        </div>
        </div>
        <div class="mb-3">
        <div class="form-check">
        <input class="form-check-input" type="checkbox" id="productActive" checked>
        <label class="form-check-label" for="productActive">
        立即启用此产品
        </label>
        </div>
        </div>
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.merchantProductManager.saveProduct()">
        <i class="bi bi-check me-2"></i>保存产品
        </button>
        </div>
        </div>
        </div>
        </div>
        `;
        // 添加到页面并显示
        document.body.insertAdjacentHTML('beforeend',
        modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
        modal.show();
        // 模态框关闭时移除DOM
        document.getElementById('addProductModal').addEventListener('hidden.bs.modal',
        function() {
            this.remove();
        });
    }
    async saveProduct() {
        const form = document.getElementById('addProductForm');
        const formData = new FormData(form);
        const productData = {
            name: document.getElementById('productName').value.trim(),
            category: document.getElementById('productCategory').value,
            description: document.getElementById('productDescription').value.trim(),
            rate: parseFloat(document.getElementById('productRate').value) || 0,
            quota_total: parseInt(document.getElementById('productQuota').value) || 0,
            status: document.getElementById('productActive').checked ? 'active' : 'inactive'
        };
        // 表单验证
        if (!productData.name) {
            this.showError('请输入产品名称');
            return;
        }
        if (!productData.category) {
            this.showError('请选择产品类别');
            return;
        }
        if (productData.rate < 0 || productData.rate > 100) {
            this.showError('费率必须在0-100%之间');
            return;
        }
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=create_product', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify(productData)
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('产品创建成功');
                bootstrap.Modal.getInstance(document.getElementById('addProductModal')).hide();
                await this.refreshProducts();
            } else {
                this.showError('创建产品失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('创建产品失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async viewProduct(productId) {
        // 查看产品详情
        this.showInfo('产品详情功能开发中...');
    }
    async editProduct(productId) {
        // 编辑产品
        this.showInfo('编辑产品功能开发中...');
    }
    async manageQuota(productId) {
        // 配额管理
        this.showInfo('配额管理功能开发中...');
    }
    async toggleProductStatus(productId) {
        if (!confirm('确定要切换产品状态吗？')) return;
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=toggle_product_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    product_id: productId
                })
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('产品状态已更新');
                await this.refreshProducts();
            } else {
                this.showError('更新产品状态失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('更新产品状态失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async duplicateProduct(productId) {
        if (!confirm('确定要复制这个产品吗？')) return;
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=duplicate_product', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    product_id: productId
                })
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('产品复制成功');
                await this.refreshProducts();
            } else {
                this.showError('复制产品失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('复制产品失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async deleteProduct(productId) {
        if (!confirm('确定要删除这个产品吗？此操作不可恢复！')) return;
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=delete_product', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    product_id: productId
                })
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('产品删除成功');
                await this.refreshProducts();
            } else {
                this.showError('删除产品失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('删除产品失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async exportProducts() {
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=export_products', {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `products-${
                    new Date().toISOString().split('T')[0]
                }.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                this.showSuccess('产品列表已导出');
            } else {
                this.showError('导出产品列表失败');
            }
        } catch (error) {
            console.error('导出产品列表失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    showSuccess(message) {
        console.log('Success:',
        message);
        // 这里可以集成toast通知组件
    }
    showError(message) {
        console.error('Error:',
        message);
        // 这里可以集成toast通知组件
        const container = document.getElementById('merchantProductsContainer');
        if (container) {
            container.innerHTML = `
            <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle me-2"></i>
            ${
                message
            }
            <button class="btn btn-outline-danger btn-sm ms-3" onclick="window.merchantProductManager.refreshProducts()">
            重试
            </button>
            </div>
            `;
        }
    }
    showInfo(message) {
        console.log('Info:',
        message);
        // 这里可以集成toast通知组件
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = MerchantProductManager;
} else if (typeof window !== "undefined") {
    window.MerchantProductManager = MerchantProductManager;
}

console.log('📦 MerchantProductManager 模块加载完成');
