/**
 * 系统模块 - 整合脚本、通知、面板管理
 * 实现界面共用+数据差异化的设计模式
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-01-20
 */

class SystemModule {
    constructor() {

        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        this.currentRole = null;
        
        console.log('✅ SystemModule initialized');
    }

    /**
     * 统一渲染方法 - 方案5配置驱动的入口
     */
    async render(container, params = {}) {
        try {
            this.currentRole = params.role || this.getCurrentUserRole();
            const moduleType = params.type || 'script'; // script, notification, panel
            
            console.log(`🎯 系统模块渲染 - 角色: ${this.currentRole}, 类型: ${moduleType}`);
            
            container.innerHTML = this.generateHTML(moduleType);
            this.initializeEvents(moduleType);
            await this.loadData(moduleType);
            
        } catch (error) {
            console.error('❌ 系统模块渲染失败:', error);
            this.showError(container, '系统模块加载失败');
        }
    }

    getCurrentUserRole() {
        if (this.tenantInfo?.tenant_type) {
            return this.tenantInfo.tenant_type;
        }
        const user = this.authManager?.getUser();
        return user?.user_type || 'system_admin';
    }

    generateHTML(moduleType) {
        const config = this.getModuleConfig(moduleType, this.currentRole);
        
        return `
            <div class="system-module" data-role="${this.currentRole}" data-type="${moduleType}">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="${config.icon} me-2"></i>${config.title}</h2>
                            <p class="text-muted mb-0">${config.description}</p>
                        </div>
                        <div>
                            ${this.generateHeaderButtons(moduleType)}
                        </div>
                    </div>
                </div>

                ${this.generateModuleContent(moduleType)}
            </div>

            <style>
                .system-module { padding: 0; }
                .stat-card { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .notification-item { padding: 15px; border-bottom: 1px solid #eee; }
                .script-item { margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; }
            </style>
        `;
    }

    getModuleConfig(moduleType, role) {
        const configs = {
            script: {
                title: '脚本管理',
                description: '系统脚本和自动化任务管理',
                icon: 'bi bi-file-code'
            },
            notification: {
                title: '通知管理',
                description: '系统通知和消息管理',
                icon: 'bi bi-bell'
            },
            panel: {
                title: '面板管理',
                description: '系统面板和仪表板管理',
                icon: 'bi bi-grid-3x3'
            }
        };
        return configs[moduleType] || configs.script;
    }

    generateHeaderButtons(moduleType) {
        switch (moduleType) {
            case 'script':
                return `
                    <button class="btn btn-primary" id="addScriptBtn">
                        <i class="bi bi-plus-circle me-2"></i>添加脚本
                    </button>
                    <button class="btn btn-success ms-2" id="runScriptBtn">
                        <i class="bi bi-play-circle me-2"></i>执行脚本
                    </button>
                `;
            case 'notification':
                return `
                    <button class="btn btn-primary" id="sendNotificationBtn">
                        <i class="bi bi-send me-2"></i>发送通知
                    </button>
                    <button class="btn btn-outline-secondary ms-2" id="markAllReadBtn">
                        <i class="bi bi-check-all me-2"></i>全部已读
                    </button>
                `;
            case 'panel':
                return `
                    <button class="btn btn-primary" id="addPanelBtn">
                        <i class="bi bi-plus-square me-2"></i>添加面板
                    </button>
                    <button class="btn btn-outline-secondary ms-2" id="refreshPanelBtn">
                        <i class="bi bi-arrow-clockwise me-2"></i>刷新
                    </button>
                `;
            default:
                return '';
        }
    }

    generateModuleContent(moduleType) {
        switch (moduleType) {
            case 'script':
                return this.generateScriptContent();
            case 'notification':
                return this.generateNotificationContent();
            case 'panel':
                return this.generatePanelContent();
            default:
                return '<div class="text-center py-4">模块内容加载中...</div>';
        }
    }

    generateScriptContent() {
        return `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-primary" id="totalScripts">-</div>
                        <div class="text-muted">总脚本数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-success" id="runningScripts">-</div>
                        <div class="text-muted">运行中</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-info" id="scheduledScripts">-</div>
                        <div class="text-muted">定时任务</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-warning" id="failedScripts">-</div>
                        <div class="text-muted">执行失败</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">脚本列表</h6>
                </div>
                <div class="card-body">
                    <div id="scriptsList">
                        <div class="text-center py-4">加载中...</div>
                    </div>
                </div>
            </div>
        `;
    }

    generateNotificationContent() {
        return `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-primary" id="totalNotifications">-</div>
                        <div class="text-muted">总通知数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-warning" id="unreadNotifications">-</div>
                        <div class="text-muted">未读通知</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-success" id="todayNotifications">-</div>
                        <div class="text-muted">今日通知</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-info" id="systemNotifications">-</div>
                        <div class="text-muted">系统通知</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">通知列表</h6>
                </div>
                <div class="card-body">
                    <div id="notificationsList">
                        <div class="text-center py-4">加载中...</div>
                    </div>
                </div>
            </div>
        `;
    }

    generatePanelContent() {
        return `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-primary" id="totalPanels">-</div>
                        <div class="text-muted">总面板数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-success" id="activePanels">-</div>
                        <div class="text-muted">活跃面板</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-info" id="customPanels">-</div>
                        <div class="text-muted">自定义面板</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <div class="h4 text-warning" id="systemPanels">-</div>
                        <div class="text-muted">系统面板</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">面板列表</h6>
                </div>
                <div class="card-body">
                    <div id="panelsList">
                        <div class="text-center py-4">加载中...</div>
                    </div>
                </div>
            </div>
        `;
    }

    initializeEvents(moduleType) {
        // 基础事件绑定
        console.log(`🔗 初始化${moduleType}事件`);
    }

    async loadData(moduleType) {
        try {
            console.log(`📊 加载${moduleType}数据...`);
            
            // 模拟数据加载
            const mockStats = {
                script: { totalScripts: '15', runningScripts: '3', scheduledScripts: '8', failedScripts: '1' },
                notification: { totalNotifications: '156', unreadNotifications: '12', todayNotifications: '23', systemNotifications: '89' },
                panel: { totalPanels: '8', activePanels: '6', customPanels: '3', systemPanels: '5' }
            };
            
            this.updateStats(mockStats[moduleType] || {});
            
        } catch (error) {
            console.error(`❌ 加载${moduleType}数据失败:`, error);
        }
    }

    updateStats(stats) {
        Object.keys(stats).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = stats[key];
            }
        });
    }

    showError(container, message) {
        container.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
}

// 全局注册
window.SystemModule = SystemModule;
window.systemModule = new SystemModule();

console.log('✅ SystemModule 已注册到全局'); 