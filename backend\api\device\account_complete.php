<?php
/**
 * 账户信息完善API
 * 处理用户补充的完整账户信息（实名、完整联系方式、密码等）
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';

try {
    $db = new Database();
    
    // 只处理POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(array(
            'success' => false,
            'message' => '只支持POST请求'
        ));
        exit;
    }
    
    // 获取输入数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(array(
            'success' => false,
            'message' => '无效的JSON数据'
        ));
        exit;
    }
    
    // 验证必需参数
    $deviceIdString = isset($input['device_id']) ? trim($input['device_id']) : '';
    $action = isset($input['action']) ? trim($input['action']) : '';
    
    if (empty($deviceIdString)) {
        http_response_code(400);
        echo json_encode(array(
            'success' => false,
            'message' => '缺少设备ID参数'
        ));
        exit;
    }
    
    // 根据action处理不同操作
    switch ($action) {
        case 'update_complete_info':
            handleUpdateCompleteInfo($db, $input);
            break;
            
        case 'check_completeness':
            handleCheckCompleteness($db, $input);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(array(
                'success' => false,
                'message' => '不支持的操作类型'
            ));
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(array(
        'success' => false,
        'message' => '服务器错误: ' . $e->getMessage()
    ));
}

/**
 * 处理完整信息更新
 */
function handleUpdateCompleteInfo($db, $input) {
    $deviceIdString = trim($input['device_id']);
    
    // 1. 验证设备是否存在并获取相关信息
    $deviceInfo = $db->fetch(
        "SELECT id, provider_id, device_name FROM devices WHERE device_id = ? AND status = 'active'",
        array($deviceIdString)
    );
    
    if (!$deviceInfo) {
        http_response_code(404);
        echo json_encode(array(
            'success' => false,
            'message' => '设备不存在或未激活'
        ));
        return;
    }
    
    $deviceTableId = $deviceInfo['id'];
    $providerId = $deviceInfo['provider_id'];
    
    // 2. 查找现有账户记录
    $existingAccount = $db->fetch(
        "SELECT id, account_name, account_number FROM alipay_accounts WHERE device_id = ? ORDER BY created_at DESC LIMIT 1",
        array($deviceTableId)
    );
    
    if (!$existingAccount) {
        http_response_code(404);
        echo json_encode(array(
            'success' => false,
            'message' => '未找到对应的账户记录，请先上传基础账户信息'
        ));
        return;
    }
    
    $accountId = $existingAccount['id'];
    
    // 3. 准备更新数据
    $updateFields = array();
    $updateValues = array();
    
    // 实名信息
    if (isset($input['real_name']) && !empty(trim($input['real_name']))) {
        $updateFields[] = "real_name = ?";
        $updateValues[] = trim($input['real_name']);
    }
    
    // 完整手机号
    if (isset($input['phone']) && !empty(trim($input['phone']))) {
        $phone = trim($input['phone']);
        // 验证手机号格式
        if (preg_match('/^1[3-9]\d{9}$/', $phone)) {
            $updateFields[] = "phone = ?";
            $updateValues[] = $phone;
        } else {
            http_response_code(400);
            echo json_encode(array(
                'success' => false,
                'message' => '手机号格式不正确'
            ));
            return;
        }
    }
    
    // 邮箱
    if (isset($input['email']) && !empty(trim($input['email']))) {
        $email = trim($input['email']);
        // 验证邮箱格式
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $updateFields[] = "email = ?";
            $updateValues[] = $email;
        } else {
            http_response_code(400);
            echo json_encode(array(
                'success' => false,
                'message' => '邮箱格式不正确'
            ));
            return;
        }
    }
    
    // 登录密码（加密存储）
    if (isset($input['login_password']) && !empty(trim($input['login_password']))) {
        $loginPassword = trim($input['login_password']);
        if (strlen($loginPassword) >= 6) {
            $updateFields[] = "login_password = ?";
            $updateValues[] = password_hash($loginPassword, PASSWORD_DEFAULT);
        } else {
            http_response_code(400);
            echo json_encode(array(
                'success' => false,
                'message' => '登录密码至少6位'
            ));
            return;
        }
    }
    
    // 支付密码（加密存储）
    if (isset($input['payment_password']) && !empty(trim($input['payment_password']))) {
        $paymentPassword = trim($input['payment_password']);
        if (strlen($paymentPassword) >= 6) {
            $updateFields[] = "payment_password = ?";
            $updateValues[] = password_hash($paymentPassword, PASSWORD_DEFAULT);
        } else {
            http_response_code(400);
            echo json_encode(array(
                'success' => false,
                'message' => '支付密码至少6位'
            ));
            return;
        }
    }
    
    if (empty($updateFields)) {
        http_response_code(400);
        echo json_encode(array(
            'success' => false,
            'message' => '没有需要更新的信息'
        ));
        return;
    }
    
    // 4. 执行更新
    $updateFields[] = "updated_at = NOW()";
    $updateValues[] = $accountId;
    
    $updateSql = "UPDATE alipay_accounts SET " . implode(", ", $updateFields) . " WHERE id = ?";
    
    $db->execute($updateSql, $updateValues);
    
    // 5. 检查更新后的完整性
    $updatedAccount = $db->fetch(
        "SELECT 
            id, account_name, account_number, real_name, phone, email,
            CASE WHEN login_password IS NOT NULL THEN 1 ELSE 0 END as has_login_password,
            CASE WHEN payment_password IS NOT NULL THEN 1 ELSE 0 END as has_payment_password
        FROM alipay_accounts WHERE id = ?",
        array($accountId)
    );
    
    // 计算完整性
    $completeness = calculateCompleteness($updatedAccount);
    
    echo json_encode(array(
        'success' => true,
        'message' => '账户信息更新成功',
        'data' => array(
            'account_id' => $accountId,
            'completeness' => $completeness,
            'updated_fields' => count($updateFields) - 1, // 减去updated_at字段
            'is_complete' => $completeness['percentage'] >= 80
        )
    ));
}

/**
 * 检查账户信息完整性
 */
function handleCheckCompleteness($db, $input) {
    $deviceIdString = trim($input['device_id']);
    
    // 1. 验证设备并获取账户信息
    $deviceInfo = $db->fetch(
        "SELECT id FROM devices WHERE device_id = ? AND status = 'active'",
        array($deviceIdString)
    );
    
    if (!$deviceInfo) {
        http_response_code(404);
        echo json_encode(array(
            'success' => false,
            'message' => '设备不存在或未激活'
        ));
        return;
    }
    
    $account = $db->fetch(
        "SELECT 
            id, account_name, account_number, real_name, phone, email,
            CASE WHEN login_password IS NOT NULL THEN 1 ELSE 0 END as has_login_password,
            CASE WHEN payment_password IS NOT NULL THEN 1 ELSE 0 END as has_payment_password,
            status, created_at, updated_at
        FROM alipay_accounts WHERE device_id = ? ORDER BY created_at DESC LIMIT 1",
        array($deviceInfo['id'])
    );
    
    if (!$account) {
        echo json_encode(array(
            'success' => true,
            'data' => array(
                'has_account' => false,
                'completeness' => array(
                    'percentage' => 0,
                    'missing_fields' => array('account_name', 'account_number', 'real_name', 'phone', 'login_password', 'payment_password'),
                    'completed_fields' => array()
                ),
                'is_complete' => false
            )
        ));
        return;
    }
    
    // 计算完整性
    $completeness = calculateCompleteness($account);
    
    echo json_encode(array(
        'success' => true,
        'data' => array(
            'has_account' => true,
            'account_info' => array(
                'id' => $account['id'],
                'account_name' => $account['account_name'],
                'account_number' => $account['account_number'],
                'status' => $account['status']
            ),
            'completeness' => $completeness,
            'is_complete' => $completeness['percentage'] >= 80
        )
    ));
}

/**
 * 计算账户信息完整性
 */
function calculateCompleteness($account) {
    $requiredFields = array(
        'account_name' => '账户名称',
        'account_number' => '账户号码',
        'real_name' => '实名信息',
        'phone' => '手机号码',
        'has_login_password' => '登录密码',
        'has_payment_password' => '支付密码'
    );
    
    $completedFields = array();
    $missingFields = array();
    
    foreach ($requiredFields as $field => $fieldName) {
        if ($field === 'has_login_password' || $field === 'has_payment_password') {
            // 密码字段特殊处理
            if (!empty($account[$field]) && $account[$field] == 1) {
                $completedFields[] = $fieldName;
            } else {
                $missingFields[] = $fieldName;
            }
        } else {
            // 普通字段
            if (!empty($account[$field])) {
                $completedFields[] = $fieldName;
            } else {
                $missingFields[] = $fieldName;
            }
        }
    }
    
    $totalFields = count($requiredFields);
    $completedCount = count($completedFields);
    $percentage = round(($completedCount / $totalFields) * 100, 1);
    
    return array(
        'percentage' => $percentage,
        'completed_count' => $completedCount,
        'total_count' => $totalFields,
        'completed_fields' => $completedFields,
        'missing_fields' => $missingFields
    );
}
?> 