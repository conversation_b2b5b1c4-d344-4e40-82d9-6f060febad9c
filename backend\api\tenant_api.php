<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理 OPTIONS 请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once dirname(__FILE__) . '/../utils/TenantAuth.php';
require_once dirname(__FILE__) . '/../core/TenantManager.php';

/**
 * 多租户API路由器
 * 根据域名识别租户并路由到相应的API处理器
 */
class TenantAPIRouter {
    private $tenantAuth;
    private $tenantManager;
    private $currentTenant;
    
    public function __construct() {
        $this->tenantAuth = new TenantAuth();
        $this->tenantManager = new TenantManager();
        $this->currentTenant = $this->tenantManager->getCurrentTenant();
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            // 记录访问日志
            $startTime = microtime(true);
            
            // 验证租户
            if (!$this->currentTenant) {
                throw new Exception('无效的访问域名', 404);
            }
            
            // 获取请求参数
            $action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');
            $method = $_SERVER['REQUEST_METHOD'];
            
            if (!$action) {
                throw new Exception('缺少action参数', 400);
            }
            
            // 验证认证（除了登录接口）
            $user = null;
            if ($action !== 'login' && $action !== 'provider_login' && $action !== 'merchant_login' && $action !== 'get_tenant_info') {
                $user = $this->authenticateRequest();
                if (!$user) {
                    throw new Exception('认证失败', 401);
                }
            }
            
            // 路由到具体的处理方法
            $result = $this->routeToHandler($action, $method, $user);
            
            // 记录成功访问
            $responseTime = (microtime(true) - $startTime) * 1000;
            $this->tenantManager->logDomainAccess(200, $responseTime);
            
            echo json_encode($result);
            
        } catch (Exception $e) {
            $responseTime = (microtime(true) - $startTime) * 1000;
            $this->tenantManager->logDomainAccess($e->getCode() ?: 500, $responseTime);
            
            http_response_code($e->getCode() ?: 500);
            echo json_encode(array(
                'error' => true,
                'message' => $e->getMessage(),
                'code' => $e->getCode() ?: 500
            ));
        }
    }
    
    /**
     * 认证请求
     */
    private function authenticateRequest() {
        try {
            error_log("TenantAPIRouter::authenticateRequest - 开始认证");
            
            $token = $this->tenantAuth->getAuthToken();
            error_log("TenantAPIRouter::authenticateRequest - getAuthToken结果: " . ($token ? substr($token, 0, 30) . "..." : 'null'));
            
            if (!$token) {
                error_log("TenantAPIRouter::authenticateRequest - 没有token，返回null");
                return null;
            }
            
            $user = $this->tenantAuth->getCurrentTenantUser($token);
            error_log("TenantAPIRouter::authenticateRequest - getCurrentTenantUser结果: " . ($user ? 'success' : 'failed'));
            
            if ($user) {
                error_log("TenantAPIRouter::authenticateRequest - 认证成功，用户ID: " . $user['id']);
            }
            
            return $user;
            
        } catch (Exception $e) {
            error_log("TenantAPIRouter::authenticateRequest - 异常: " . $e->getMessage());
            error_log("TenantAPIRouter::authenticateRequest - 异常堆栈: " . $e->getTraceAsString());
            return null;
        } catch (Error $e) {
            error_log("TenantAPIRouter::authenticateRequest - PHP错误: " . $e->getMessage());
            error_log("TenantAPIRouter::authenticateRequest - 错误堆栈: " . $e->getTraceAsString());
            return null;
        }
    }
    
    /**
     * 路由到处理器
     */
    private function routeToHandler($action, $method, $user = null) {
        switch ($action) {
            case 'login':
            case 'provider_login':
            case 'merchant_login':
                return $this->handleLogin();
                
            case 'logout':
                return $this->handleLogout();
                
            case 'get_tenant_info':
                return $this->getTenantInfo();
                
            case 'get_user_info':
                return $this->getUserInfo();
                
            case 'get_dashboard_data':
                return $this->getDashboardData();
                
            case 'manage_domains':
                return $this->manageDomains($method);
                
            case 'set_custom_domain':
                return $this->setCustomDomain();
                
            case 'verify_custom_domain':
                return $this->verifyCustomDomain();
                
            case 'remove_custom_domain':
                return $this->removeCustomDomain();
                
            case 'update_tenant_config':
                return $this->updateTenantConfig();
                
            // 根据租户类型路由到具体的业务处理
            default:
                return $this->routeToBusinessHandler($action, $method, $user);
        }
    }
    
    /**
     * 处理登录
     */
    private function handleLogin() {
        // 尝试从JSON格式获取参数
        $input = json_decode(file_get_contents('php://input'), true);
        
        // 如果JSON解析失败，尝试从POST参数获取
        if (!$input) {
            $input = $_POST;
        }
        
        // 验证必要参数
        if (!$input || !isset($input['username']) || !isset($input['password'])) {
            throw new Exception('用户名和密码不能为空', 400);
        }
        
        $result = $this->tenantAuth->tenantLogin($input['username'], $input['password']);
        
        if (!$result['success']) {
            throw new Exception($result['message'], 401);
        }
        
        return array(
            'code' => 200,
            'message' => '登录成功',
            'data' => array(
                'token' => $result['token'],
                'user' => $result['user'],
                'tenant' => $result['tenant']
            )
        );
    }
    
    /**
     * 处理注销
     */
    private function handleLogout() {
        $token = $this->tenantAuth->getAuthToken();
        $this->tenantAuth->tenantLogout($token);
        
        return array(
            'code' => 200,
            'message' => '注销成功'
        );
    }
    
    /**
     * 获取租户信息
     */
    private function getTenantInfo() {
        if (!$this->currentTenant) {
            throw new Exception('无效的租户', 404);
        }
        
        return array(
            'code' => 200,
            'data' => array(
                'tenant' => array(
                    'type' => $this->currentTenant['tenant_type'],
                    'id' => $this->currentTenant['tenant_id'],
                    'domain' => $this->currentTenant['domain_name'],
                    'brand_name' => $this->currentTenant['brand_name'],
                    'theme_config' => json_decode($this->currentTenant['theme_config'], true),
                    'logo_url' => $this->currentTenant['logo_url']
                )
            )
        );
    }
    
    /**
     * 获取用户信息
     */
    private function getUserInfo() {
        $user = $this->authenticateRequest();
        
        if (!$user) {
            throw new Exception('用户未认证', 401);
        }
        
        return array(
            'code' => 200,
            'data' => array(
                'user' => array(
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'real_name' => $user['real_name'],
                    'user_type' => $user['user_type'],
                    'profile_id' => $user['profile_id'],
                    'display_name' => $user['display_name'],
                    'tenant_info' => $user['tenant_info']
                )
            )
        );
    }
    
    /**
     * 获取仪表板数据
     */
    private function getDashboardData() {
        $user = $this->authenticateRequest();
        
        if (!$user) {
            throw new Exception('用户未认证', 401);
        }
        
        switch ($this->currentTenant['tenant_type']) {
            case 'provider':
                return $this->getProviderDashboard($user);
            case 'merchant':
                return $this->getMerchantDashboard($user);
            case 'admin':
                return $this->getAdminDashboard($user);
            default:
                throw new Exception('不支持的租户类型', 400);
        }
    }
    
    /**
     * 域名管理
     */
    private function manageDomains($method) {
        $user = $this->authenticateRequest();
        
        if (!$user || !$this->tenantAuth->hasTenantPermission($user, 'manage_domains')) {
            throw new Exception('权限不足', 403);
        }
        
        switch ($method) {
            case 'GET':
                return $this->getDomains($user);
            case 'POST':
                return $this->createDomain($user);
            case 'PUT':
                return $this->updateDomain($user);
            case 'DELETE':
                return $this->deleteDomain($user);
            default:
                throw new Exception('不支持的请求方法', 405);
        }
    }
    
    /**
     * 路由到业务处理器
     */
    private function routeToBusinessHandler($action, $method, $user) {
        // 用户信息已经从上级传递过来，不需要重复认证
        if (!$user) {
            throw new Exception('用户未认证', 401);
        }
        
        // 根据租户类型包含相应的业务处理文件
        switch ($this->currentTenant['tenant_type']) {
            case 'provider':
                require_once dirname(__FILE__) . '/tenant/provider_api.php';
                $handler = new ProviderAPI($this->tenantAuth, $this->tenantManager);
                break;
                
            case 'merchant':
                require_once dirname(__FILE__) . '/tenant/merchant_api.php';
                $handler = new MerchantAPI($this->tenantAuth, $this->tenantManager);
                break;
                
            case 'admin':
                require_once dirname(__FILE__) . '/admin/admin.php';
                $handler = new AdminAPI($this->tenantAuth, $this->tenantManager);
                break;
                
            default:
                throw new Exception('不支持的租户类型', 400);
        }
        
        return $handler->handleAction($action, $method, $user);
    }
    
    /**
     * 获取码商仪表板数据
     */
    private function getProviderDashboard($user) {
        $providerId = $user['profile_id'];
        
        // 获取码商统计数据
        $stats = $this->tenantManager->db->fetch(
            "SELECT 
                COUNT(DISTINCT d.id) as device_count,
                COUNT(DISTINCT aa.id) as account_count,
                COUNT(DISTINCT t.id) as transaction_count,
                COALESCE(SUM(CASE WHEN t.status = 'success' THEN t.amount END), 0) as total_amount,
                COALESCE(SUM(CASE WHEN t.status = 'success' THEN t.fee END), 0) as total_fee
             FROM payment_providers pp
             LEFT JOIN devices d ON pp.id = d.provider_id
             LEFT JOIN alipay_accounts aa ON pp.id = aa.provider_id
             LEFT JOIN transactions t ON pp.id = t.provider_id
             WHERE pp.id = ?",
            array($providerId)
        );
        
        return array(
            'code' => 200,
            'data' => array(
                'stats' => $stats,
                'tenant_type' => 'provider'
            )
        );
    }
    
    /**
     * 获取商户仪表板数据
     */
    private function getMerchantDashboard($user) {
        $merchantId = $user['profile_id'];
        
        // 获取商户统计数据
        $stats = $this->tenantManager->db->fetch(
            "SELECT 
                COUNT(DISTINCT t.id) as transaction_count,
                COALESCE(SUM(CASE WHEN t.status = 'success' THEN t.amount END), 0) as total_amount,
                COALESCE(SUM(CASE WHEN t.status = 'success' THEN t.actual_amount END), 0) as total_received,
                COUNT(DISTINCT p.id) as product_count
             FROM merchants m
             LEFT JOIN transactions t ON m.id = t.merchant_id
             LEFT JOIN products p ON m.id = p.merchant_id
             WHERE m.id = ?",
            array($merchantId)
        );
        
        return array(
            'code' => 200,
            'data' => array(
                'stats' => $stats,
                'tenant_type' => 'merchant'
            )
        );
    }
    
    /**
     * 获取管理员仪表板数据
     */
    private function getAdminDashboard($user) {
        // 系统总体统计
        $stats = $this->tenantManager->db->fetch(
            "SELECT 
                COUNT(DISTINCT pp.id) as provider_count,
                COUNT(DISTINCT m.id) as merchant_count,
                COUNT(DISTINCT t.id) as transaction_count,
                COALESCE(SUM(CASE WHEN t.status = 'success' THEN t.amount END), 0) as total_amount
             FROM users u
             LEFT JOIN payment_providers pp ON u.id = pp.user_id
             LEFT JOIN merchants m ON u.id = m.user_id
             LEFT JOIN transactions t ON t.id IS NOT NULL"
        );
        
        return array(
            'code' => 200,
            'data' => array(
                'stats' => $stats,
                'tenant_type' => 'admin'
            )
        );
    }
    
    /**
     * 获取域名信息
     */
    private function getDomains($user) {
        $domainInfo = $this->tenantManager->getTenantDomains(
            $this->currentTenant['tenant_type'],
            $this->currentTenant['tenant_id']
        );
        
        return array(
            'code' => 200,
            'data' => array('domain_info' => $domainInfo)
        );
    }
    
    /**
     * 设置自定义域名
     */
    private function setCustomDomain() {
        $user = $this->authenticateRequest();
        
        if (!$user || ($this->currentTenant['tenant_type'] !== 'merchant' && $this->currentTenant['tenant_type'] !== 'provider')) {
            throw new Exception('只有商户和码商可以设置自定义域名', 403);
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['custom_domain']) || empty($input['custom_domain'])) {
            throw new Exception('自定义域名不能为空', 400);
        }
        
        try {
            $result = $this->tenantManager->setCustomDomain(
                $this->currentTenant['tenant_type'],
                $this->currentTenant['tenant_id'],
                $input['custom_domain']
            );
            
            return array(
                'code' => 200,
                'message' => '自定义域名设置成功',
                'data' => $result
            );
            
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), 400);
        }
    }
    
    /**
     * 验证自定义域名DNS配置
     */
    private function verifyCustomDomain() {
        $user = $this->authenticateRequest();
        
        if (!$user) {
            throw new Exception('用户未认证', 401);
        }
        
        $result = $this->tenantManager->verifyCustomDomainDNS(
            $this->currentTenant['tenant_type'],
            $this->currentTenant['tenant_id']
        );
        
        return array(
            'code' => 200,
            'message' => $result['verified'] ? 'DNS配置验证成功' : 'DNS配置验证失败',
            'data' => $result
        );
    }
    
    /**
     * 移除自定义域名
     */
    private function removeCustomDomain() {
        $user = $this->authenticateRequest();
        
        if (!$user || ($this->currentTenant['tenant_type'] !== 'merchant' && $this->currentTenant['tenant_type'] !== 'provider')) {
            throw new Exception('权限不足', 403);
        }
        
        try {
            $result = $this->tenantManager->removeCustomDomain(
                $this->currentTenant['tenant_type'],
                $this->currentTenant['tenant_id']
            );
            
            return array(
                'code' => 200,
                'message' => '自定义域名已移除',
                'data' => $result
            );
            
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), 400);
        }
    }
    
    /**
     * 创建域名
     */
    private function createDomain($user) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['domain_name'])) {
            throw new Exception('域名不能为空', 400);
        }
        
        $domainId = $this->tenantManager->createTenantDomain(
            $this->currentTenant['tenant_type'],
            $this->currentTenant['tenant_id'],
            $input['domain_name'],
            isset($input['domain_type']) ? $input['domain_type'] : 'custom',
            $input
        );
        
        return array(
            'code' => 200,
            'message' => '域名创建成功',
            'data' => array('domain_id' => $domainId)
        );
    }
    
    /**
     * 更新租户配置
     */
    private function updateTenantConfig() {
        $user = $this->authenticateRequest();
        
        if (!$user || !$this->tenantAuth->hasTenantPermission($user, 'manage_config')) {
            throw new Exception('权限不足', 403);
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        // 更新租户域名配置
        $this->tenantManager->updateTenantDomain(
            $this->currentTenant['id'],
            $input
        );
        
        return array(
            'code' => 200,
            'message' => '配置更新成功'
        );
    }
}

// 启动API路由器
$router = new TenantAPIRouter();
$router->handleRequest();
?> 