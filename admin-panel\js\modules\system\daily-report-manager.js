/**
 * 日报管理器
 * 从admin.js第15089-15787行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class DailyReportManager {
    constructor() {
        this.apiUrl = '/api/admin.php?do=daily_reports';
        this.currentPage = 1;
        this.pageSize = 20;
        this.currentFilters = {
        };
        this.reportChart = null;
        this.auth = new AuthManager();
    }
    // 初始化
    initialize() {
        this.loadReports();
        this.loadTodayStats();
        this.initializeEventListeners();
        this.initializeChart();
    }
    // 初始化事件监听器
    initializeEventListeners() {
        // 搜索和筛选
        document.getElementById('reportSearchBtn')?.addEventListener('click', () => {
            this.searchReports();
        });
        document.getElementById('reportSearchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchReports();
            }
        });
        // 筛选器改变
        ['reportDateFilter', 'reportMerchantFilter', 'reportStatusFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });
        // 生成报告按钮
        document.getElementById('generateReportBtn')?.addEventListener('click', () => {
            this.showGenerateReportModal();
        });
    }
    // 加载今日统计数据
    async loadTodayStats() {
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=today_stats`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.updateStatsDisplay(result.data);
            }
        } catch (error) {
            console.error('加载今日统计失败:',
            error);
        }
    }
    // 更新统计显示
    updateStatsDisplay(stats) {
        const elements = {
            todayRevenue: stats.today_revenue || 0,
            todayOrders: stats.today_orders || 0,
            successRate: stats.success_rate || 0,
            comparedYesterday: stats.compared_yesterday || 0
        };
        Object.keys(elements).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (key === 'todayRevenue') {
                    element.textContent = `¥${
                        this.formatNumber(elements[key])
                    }`;
                } else if (key === 'successRate') {
                    element.textContent = `${
                        elements[key]
                    }%`;
                } else if (key === 'comparedYesterday') {
                    const isPositive = elements[key] >= 0;
                    element.innerHTML = `
                    <span class="badge ${
                        isPositive ? 'bg-success' : 'bg-danger'
                    }">
                    <i class="bi bi-arrow-${
                        isPositive ? 'up' : 'down'
                    }"></i>
                    ${
                        Math.abs(elements[key])
                    }%
                    </span>
                    `;
                } else {
                    element.textContent = this.formatNumber(elements[key]);
                }
            }
        });
    }
    // 初始化图表
    initializeChart() {
        const ctx = document.getElementById('reportChart');
        if (ctx) {
            this.reportChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '日收入',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }, {
                        label: '订单量',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '日期'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '收入(¥)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '订单量'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
            this.loadChartData();
        }
    }
    // 加载图表数据
    async loadChartData() {
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=chart_data&days=7`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200 && this.reportChart) {
                const data = result.data;
                this.reportChart.data.labels = data.labels;
                this.reportChart.data.datasets[0].data = data.revenue;
                this.reportChart.data.datasets[1].data = data.orders;
                this.reportChart.update();
            }
        } catch (error) {
            console.error('加载图表数据失败:',
            error);
        }
    }
    // 加载日报列表
    async loadReports() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                ...this.currentFilters
            });
            const response = await fetch(`${
                this.apiUrl
            }&${
                params
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderReportsTable(result.data.reports);
                this.renderPagination(result.data.pagination);
                this.updateReportCount(result.data.pagination.total_records);
            } else {
                this.showError('加载日报失败: ' + result.message);
            }
        } catch (error) {
            console.error('加载日报失败:',
            error);
            this.showError('加载日报失败: 未授权访问，请先登录');
        }
    }
    // 渲染日报表格
    renderReportsTable(reports) {
        const container = document.getElementById('reportsTableContainer');
        if (!container) return;
        if (!reports || reports.length === 0) {
            container.innerHTML = `
            <div class="text-center py-5">
            <i class="bi bi-inbox display-1 text-muted"></i>
            <div class="mt-3">暂无日报数据</div>
            <button class="btn btn-primary mt-3" onclick="window.dailyReportManager.showGenerateReportModal()">
            <i class="bi bi-plus-circle me-2"></i>生成日报
            </button>
            </div>
            `;
            return;
        }
        const tableHtml = `
        <div class="table-responsive">
        <table class="table table-hover align-middle">
        <thead class="table-light">
        <tr>
        <th>日期</th>
        <th>商户</th>
        <th>总收入</th>
        <th>订单量</th>
        <th>成功率</th>
        <th>风控拦截</th>
        <th>状态</th>
        <th>生成时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        ${
            reports.map(report => `
            <tr>
            <td>
            <strong>${
                report.report_date
            }</strong>
            </td>
            <td>
            ${
                report.merchant_name || '系 统'
            }
            ${
                report.merchant_id ? `<small class="text-muted d-block">ID: ${
                    report.merchant_id
                }</small>` : ''
            }
            </td>
            <td>
            <span class="text-success fw-bold">¥${
                this.formatNumber(report.total_revenue)
            }</span>
            </td>
            <td>
            <span class="badge bg-primary">${
                this.formatNumber(report.total_orders)
            }</span>
            </td>
            <td>
            <div class="d-flex align-items-center">
            <div class="progress me-2" style="width: 60px;
            height: 6px;
            ">
            <div class="progress-bar ${
                this.getSuccessRateColor(report.success_rate)
            }"
            style="width: ${
                report.success_rate
            }%"></div>
            </div>
            <small>${
                report.success_rate
            }%</small>
            </div>
            </td>
            <td>
            <span class="badge bg-warning">${
                report.risk_blocked || 0
            }</span>
            </td>
            <td>
            <span class="badge ${
                this.getStatusBadgeClass(report.status)
            }">
            ${
                this.getStatusText(report.status)
            }
            </span>
            </td>
            <td>
            <small class="text-muted">${
                this.formatDateTime(report.created_at)
            }</small>
            </td>
            <td>
            <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary"
            onclick="window.dailyReportManager.viewReport(${
                report.id
            })"
            data-bs-toggle="tooltip" title="查看详情">
            <i class="bi bi-eye"></i>
            </button>
            <button class="btn btn-outline-success"
            onclick="window.dailyReportManager.downloadReport(${
                report.id
            })"
            data-bs-toggle="tooltip" title="下载报告">
            <i class="bi bi-download"></i>
            </button>
            <button class="btn btn-outline-info"
            onclick="window.dailyReportManager.sendReport(${
                report.id
            })"
            data-bs-toggle="tooltip" title="发送邮件">
            <i class="bi bi-envelope"></i>
            </button>
            <button class="btn btn-outline-warning"
            onclick="window.dailyReportManager.regenerateReport(${
                report.id
            })"
            data-bs-toggle="tooltip" title="重新生成">
            <i class="bi bi-arrow-clockwise"></i>
            </button>
            <button class="btn btn-outline-danger"
            onclick="window.dailyReportManager.deleteReport(${
                report.id
            })"
            data-bs-toggle="tooltip" title="删除">
            <i class="bi bi-trash"></i>
            </button>
            </div>
            </td>
            </tr>
            `).join('')
        }
        </tbody>
        </table>
        </div>
        `;
        container.innerHTML = tableHtml;
        // 初始化tooltip
        const tooltips = container.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(tooltip => {
            new bootstrap.Tooltip(tooltip);
        });
    }
    // 查看报告详情
    async viewReport(reportId) {
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=view&id=${
                reportId
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showReportDetailModal(result.data);
            } else {
                this.showError('获取报告详情失败: ' + result.message);
            }
        } catch (error) {
            console.error('获取报告详情失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 显示报告详情模态框
    showReportDetailModal(report) {
        const modal = new bootstrap.Modal(document.getElementById('reportDetailModal'));
        const content = document.getElementById('reportDetailContent');
        content.innerHTML = `
        <div class="row">
        <div class="col-md-6">
        <h6 class="text-muted">基本信息</h6>
        <table class="table table-sm">
        <tr><td>报告日期:</td><td><strong>${
            report.report_date
        }</strong></td></tr>
        <tr><td>商户:</td><td>${
            report.merchant_name || '系统报告'
        }</td></tr>
        <tr><td>生成时间:</td><td>${
            this.formatDateTime(report.created_at)
        }</td></tr>
        <tr><td>状态:</td><td><span class="badge ${
            this.getStatusBadgeClass(report.status)
        }">${
            this.getStatusText(report.status)
        }</span></td></tr>
        </table>
        </div>
        <div class="col-md-6">
        <h6 class="text-muted">数据摘要</h6>
        <table class="table table-sm">
        <tr><td>总收入:</td><td class="text-success fw-bold">¥${
            this.formatNumber(report.total_revenue)
        }</td></tr>
        <tr><td>总订单:</td><td><strong>${
            this.formatNumber(report.total_orders)
        }</strong></td></tr>
        <tr><td>成功率:</td><td><strong>${
            report.success_rate
        }%</strong></td></tr>
        <tr><td>风控拦截:</td><td class="text-warning"><strong>${
            report.risk_blocked || 0
        }</strong></td></tr>
        </table>
        </div>
        </div>
        <hr>
        <div class="row">
        <div class="col-12">
        <h6 class="text-muted">详细数据</h6>
        <div class="table-responsive">
        <table class="table table-striped table-sm">
        <thead>
        <tr>
        <th>支付方式</th>
        <th>订单数</th>
        <th>成功数</th>
        <th>成功率</th>
        <th>总金额</th>
        <th>成功金额</th>
        </tr>
        </thead>
        <tbody>
        ${
            report.payment_methods ? report.payment_methods.map(method => `
            <tr>
            <td>${
                method.payment_method
            }</td>
            <td>${
                method.total_orders
            }</td>
            <td>${
                method.success_orders
            }</td>
            <td>${
                method.success_rate
            }%</td>
            <td>¥${
                this.formatNumber(method.total_amount)
            }</td>
            <td class="text-success">¥${
                this.formatNumber(method.success_amount)
            }</td>
            </tr>
            `).join('') : '<tr><td colspan="6" class="text-center">暂无详细数据</td></tr>'
        }
        </tbody>
        </table>
        </div>
        </div>
        </div>
        `;
        modal.show();
    }
    // 下载报告
    async downloadReport(reportId) {
        try {
            window.open(`${
                this.apiUrl
            }&action=download&id=${
                reportId
            }`, '_blank');
        } catch (error) {
            console.error('下载报告失败:',
            error);
            this.showError('下载失败，请重试');
        }
    }
    // 发送报告邮件
    async sendReport(reportId) {
        if (!confirm('确定要发送此报告到邮箱吗？')) return;
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=send_email`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    id: reportId
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('报告已发送到邮箱');
            } else {
                this.showError('发送失败: ' + result.message);
            }
        } catch (error) {
            console.error('发送报告失败:',
            error);
            this.showError('发送失败，请重试');
        }
    }
    // 重新生成报告
    async regenerateReport(reportId) {
        if (!confirm('确定要重新生成此报告吗？这将覆盖现有数据。')) return;
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=regenerate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    id: reportId
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('报告重新生成成功');
                this.loadReports();
                this.loadTodayStats();
            } else {
                this.showError('重新生成失败: ' + result.message);
            }
        } catch (error) {
            console.error('重新生成报告失败:',
            error);
            this.showError('重新生成失败，请重试');
        }
    }
    // 删除报告
    async deleteReport(reportId) {
        if (!confirm('确定要删除此报告吗？此操作不可恢复。')) return;
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=delete`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    id: reportId
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('报告删除成功');
                this.loadReports();
            } else {
                this.showError('删除失败: ' + result.message);
            }
        } catch (error) {
            console.error('删除报告失败:',
            error);
            this.showError('删除失败，请重试');
        }
    }
    // 显示生成报告模态框
    showGenerateReportModal() {
        const modal = new bootstrap.Modal(document.getElementById('generateReportModal'));
        modal.show();
    }
    // 生成新报告
    async generateReport() {
        const form = document.getElementById('generateReportForm');
        const formData = new FormData(form);
        const data = {
            report_date: formData.get('report_date'),
            merchant_id: formData.get('merchant_id') || null
        };
        if (!data.report_date) {
            this.showError('请选择报告日期');
            return;
        }
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify(data)
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('报告生成成功');
                bootstrap.Modal.getInstance(document.getElementById('generateReportModal')).hide();
                this.loadReports();
                this.loadTodayStats();
            } else {
                this.showError('生成失败: ' + result.message);
            }
        } catch (error) {
            console.error('生成报告失败:',
            error);
            this.showError('生成失败，请重试');
        }
    }
    // 搜索日报
    searchReports() {
        const searchTerm = document.getElementById('reportSearchInput')?.value.trim();
        this.currentFilters.search = searchTerm || undefined;
        this.currentPage = 1;
        this.loadReports();
    }
    // 应用筛选器
    applyFilters() {
        const dateFilter = document.getElementById('reportDateFilter')?.value;
        const merchantFilter = document.getElementById('reportMerchantFilter')?.value;
        const statusFilter = document.getElementById('reportStatusFilter')?.value;
        this.currentFilters = {
            ...this.currentFilters,
            date_range: dateFilter || undefined,
            merchant_id: merchantFilter || undefined,
            status: statusFilter || undefined
        };
        this.currentPage = 1;
        this.loadReports();
    }
    // 渲染分页
    renderPagination(pagination) {
        const container = document.getElementById('reportPagination');
        if (!container || !pagination) return;
        const {
            current_page,
            total_pages,
            total_records
        } = pagination;
        if (total_pages <= 1) {
            container.innerHTML = '';
            return;
        }
        let paginationHtml = '<nav><ul class="pagination">';
        // 上一页
        paginationHtml += `
        <li class="page-item ${
            current_page === 1 ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.dailyReportManager.goToPage(${
            current_page - 1
        })">上一页</a>
        </li>
        `;
        // 页码
        const start = Math.max(1,
        current_page - 2);
        const end = Math.min(total_pages,
        current_page + 2);
        if (start > 1) {
            paginationHtml += '<li class="page-item"><a class="page-link" href="#" onclick="window.dailyReportManager.goToPage(1)">1</a></li>';
            if (start > 2) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        for (let i = start;
        i <= end;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === current_page ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.dailyReportManager.goToPage(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        if (end < total_pages) {
            if (end < total_pages - 1) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="window.dailyReportManager.goToPage(${
                total_pages
            })">${
                total_pages
            }</a></li>`;
        }
        // 下一页
        paginationHtml += `
        <li class="page-item ${
            current_page === total_pages ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.dailyReportManager.goToPage(${
            current_page + 1
        })">下一页</a>
        </li>
        `;
        paginationHtml += '</ul></nav>';
        container.innerHTML = paginationHtml;
    }
    // 跳转到指定页
    goToPage(page) {
        this.currentPage = page;
        this.loadReports();
    }
    // 更新记录数显示
    updateReportCount(count) {
        const element = document.getElementById('reportCount');
        if (element) {
            element.textContent = this.formatNumber(count);
        }
    }
    // 工具函数
    formatNumber(num) {
        if (num === null || num === undefined) return '0';
        return Number(num).toLocaleString();
    }
    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';
        return new Date(dateTimeStr).toLocaleString('zh-CN');
    }
    getStatusBadgeClass(status) {
        const statusClasses = {
            'completed': 'bg-success',
            'processing': 'bg-primary',
            'failed': 'bg-danger',
            'pending': 'bg-warning'
        };
        return statusClasses[status] || 'bg-secondary';
    }
    getStatusText(status) {
        const statusTexts = {
            'completed': '已完成',
            'processing': '生成中',
            'failed': '失败',
            'pending': '待生成'
        };
        return statusTexts[status] || status;
    }
    getSuccessRateColor(rate) {
        if (rate >= 90) return 'bg-success';
        if (rate >= 70) return 'bg-warning';
        return 'bg-danger';
    }
    showSuccess(message) {
        // 显示成功消息
        console.log('Success:',
        message);
        // 这里可以集成toast通知组件
    }
    showError(message) {
        // 显示错误消息
        console.error('Error:',
        message);
        // 这里可以集成toast通知组件
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = DailyReportManager;
} else if (typeof window !== "undefined") {
    window.DailyReportManager = DailyReportManager;
}

console.log('📦 DailyReportManager 模块加载完成');
