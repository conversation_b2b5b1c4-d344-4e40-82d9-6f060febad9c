/**
 * 职位管理器
 * 从admin.js第10285-11231行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class JobPositionManager {
    constructor() {
        this.positions = [];
        this.permissions = [];
        this.currentPage = 1;
        this.totalPages = 1;
        this.filters = {
            search: '',
            status: '',
            type: ''
        };
        // 兼容旧属性名
        this.searchTerm = '';
        this.statusFilter = '';
        this.typeFilter = '';
    }
    
    /**
     * 路由渲染方法 - 兼容路由管理器
     */
    render(options = {}) {
        console.log('📄 渲染职位管理页面');
        
        // 返回HTML字符串给router-manager
        const htmlContent = this.getHTMLTemplate();
        
        // 延迟初始化，等待DOM插入后执行
        setTimeout(() => this.initialize(), 100);
        
        return htmlContent;
    }
    
    /**
     * 获取职位管理页面的HTML模板
     */
    getHTMLTemplate() {
        // 设置页面标题（延迟执行）
        setTimeout(() => {
            const pageTitle = document.getElementById('pageTitle');
            if (pageTitle) {
                pageTitle.textContent = '职位管理';
            }
        }, 50);

        // 返回HTML字符串
        return `
        <div class="management-page">
            <div class="page-header">
                <h2 class="mb-1">职位管理</h2>
                <p class="text-muted mb-0">管理和配置员工职位类型</p>
            </div>
        <!-- 统计卡片 -->
        <div class="row g-3 mb-4" id="positionStats">
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="avatar avatar-md bg-primary bg-opacity-10 text-primary rounded-circle">
        <i class="bi bi-diagram-3"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="fw-semibold text-dark">总职位数</div>
        <div class="h5 mb-0" id="totalPositions">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="avatar avatar-md bg-success bg-opacity-10 text-success rounded-circle">
        <i class="bi bi-check-circle"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="fw-semibold text-dark">启用职位</div>
        <div class="h5 mb-0" id="activePositions">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="avatar avatar-md bg-info bg-opacity-10 text-info rounded-circle">
        <i class="bi bi-person-plus"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="fw-semibold text-dark">自定义职位</div>
        <div class="h5 mb-0" id="customPositions">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="avatar avatar-md bg-warning bg-opacity-10 text-warning rounded-circle">
        <i class="bi bi-people"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="fw-semibold text-dark">在职员工</div>
        <div class="h5 mb-0" id="employeesInPositions">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 控制面板 -->
        <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
        <div class="row g-3 align-items-center">
        <div class="col-md-4">
        <div class="input-group">
        <span class="input-group-text">
        <i class="bi bi-search"></i>
        </span>
        <input type="text" class="form-control" id="positionSearch" placeholder="搜索职位名称或描述">
        </div>
        </div>
        <div class="col-md-2">
        <select class="form-select" id="positionStatusFilter">
        <option value="">所有状态</option>
        <option value="active">启用</option>
        <option value="inactive">禁用</option>
        </select>
        </div>
        <div class="col-md-2">
        <select class="form-select" id="positionTypeFilter">
        <option value="">所有类型</option>
        <option value="system">系统预设</option>
        <option value="custom">自定义</option>
        </select>
        </div>
        <div class="col-md-4 text-end">
        <button type="button" class="btn btn-outline-secondary me-2" id="refreshPositions">
        <i class="bi bi-arrow-clockwise me-1"></i>刷新
        </button>
        <button type="button" class="btn btn-primary" id="createPositionBtn">
        <i class="bi bi-plus-lg me-1"></i>添加职位
        </button>
        </div>
        </div>
        </div>
        </div>
        <!-- 职位列表 -->
        <div class="card border-0 shadow-sm">
        <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
        <h6 class="mb-0">职位列表</h6>
        <small class="text-muted">共 <span id="positionCount">0</span> 个职位</small>
        </div>
        </div>
        <div class="card-body p-0">
        <div class="table-responsive">
        <table class="table table-hover mb-0">
        <thead class="table-light">
        <tr>
        <th>职位信息</th>
        <th>状态</th>
        <th>权限数量</th>
        <th>员工数量</th>
        <th>创建时间</th>
        <th width="180">操作</th>
        </tr>
        </thead>
        <tbody id="positionTableBody">
        <tr>
        <td colspan="6" class="text-center py-4">
        <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-2 text-muted">正在加载职位数据...</div>
        </td>
        </tr>
        </tbody>
        </table>
        </div>
        </div>
        <div class="card-footer bg-white">
        <nav id="positionPagination"></nav>
        </div>
        </div>
        </div>
        `;
    }
    
    initialize() {
        // 等待DOM元素准备好，但限制重试次数
        if (!this.retryCount) this.retryCount = 0;
        
        if (!document.getElementById('positionSearch')) {
            this.retryCount++;
            if (this.retryCount > 50) { // 最多重试50次（5秒）
                console.error('📦 职位管理DOM初始化失败，已达到最大重试次数');
                console.error('📦 当前容器内容:', document.querySelector('.main-content')?.innerHTML?.substring(0, 200));
                return;
            }
            console.warn(`📦 职位管理DOM未准备好，延迟100ms重试 (${this.retryCount}/50)`);
            setTimeout(() => this.initialize(), 100);
            return;
        }
        
        console.log('✅ 职位管理DOM准备完成，开始绑定事件和加载数据');
        this.bindEvents();
        this.loadPositions();
    }
    bindEvents() {
        // 搜索
        const positionSearch = document.getElementById('positionSearch');
        if (positionSearch) {
            positionSearch.addEventListener('input', () => {
                this.searchPositions();
            });
        }
        
        // 筛选
        const positionStatusFilter = document.getElementById('positionStatusFilter');
        if (positionStatusFilter) {
            positionStatusFilter.addEventListener('change', () => {
                this.applyFilters();
            });
        }
        
        const positionTypeFilter = document.getElementById('positionTypeFilter');
        if (positionTypeFilter) {
            positionTypeFilter.addEventListener('change', () => {
                this.applyFilters();
            });
        }
        
        // 刷新按钮
        const refreshPositions = document.getElementById('refreshPositions');
        if (refreshPositions) {
            refreshPositions.addEventListener('click', () => {
                this.refreshPositions();
            });
        }
        
        // 创建职位按钮
        const createPositionBtn = document.getElementById('createPositionBtn');
        if (createPositionBtn) {
            createPositionBtn.addEventListener('click', () => {
                this.showCreateModal();
            });
        }
    }
    async loadPositions(page = 1) {
        try {
            this.currentPage = page;
            const params = new URLSearchParams({
                page: page,
                limit: 20,
                search: this.filters.search || this.searchTerm || '',
                status: this.filters.status || this.statusFilter || '',
                type: this.filters.type || this.typeFilter || ''
            });
            
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=job_positions&${params}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem(CONFIG.TOKEN_KEY)}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                this.positions = result.data.positions || [];
                this.permissions = result.data.permissions || {};
                this.pagination = result.data.pagination || {};
                this.updateStatsCards();
                this.renderPositionsTable(this.positions);
                this.renderPagination(this.pagination);
            } else {
                this.showErrorState(result.message || '获取职位列表失败');
            }
        } catch (error) {
            console.error('加载职位失败:', error);
            this.showErrorState('加载职位数据失败: ' + error.message);
        }
    }
    updateStatsCards() {
        const totalPositions = this.positions.length;
        const activePositions = this.positions.filter(p => p.status === 'active').length;
        const customPositions = this.positions.filter(p => p.is_system == 0).length;
        const employeesInPositions = this.positions.reduce((sum,
        p) => sum + parseInt(p.employee_count || 0), 0);
        document.getElementById('totalPositions').textContent = totalPositions;
        document.getElementById('activePositions').textContent = activePositions;
        document.getElementById('customPositions').textContent = customPositions;
        document.getElementById('employeesInPositions').textContent = employeesInPositions;
    }
    renderPositionsTable(positions) {
        const tbody = document.getElementById('positionTableBody');
        if (!positions || positions.length === 0) {
            tbody.innerHTML = `
            <tr>
            <td colspan="6" class="text-center py-4">
            <div class="text-muted">
            <i class="bi bi-inbox" style="font-size: 3rem;
            "></i>
            <p class="mt-2">暂无职位数据</p>
            </div>
            </td>
            </tr>
            `;
            return;
        }
        tbody.innerHTML = positions.map(position => this.renderPositionRow(position)).join('');
    }
    renderPositionRow(position) {
        // 状态显示：1=启用，0=禁用
        const statusBadge = position.status == 1 ? 'bg-success' : 'bg-secondary';
        const statusText = position.status == 1 ? '启用' : '禁用';
        return `
        <tr>
        <td>
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="avatar avatar-sm bg-light rounded-circle">
        <i class="bi bi-diagram-3 text-primary"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="fw-semibold">${
            position.position_name
        }</div>
        ${
            position.description ? `<div class="text-muted small">${
                position.description
            }</div>` : ''
        }
        </div>
        </div>
        </td>
        <td>
        <span class="badge ${
            statusBadge
        }">${
            statusText
        }</span>
        </td>
        <td>
        <span class="fw-semibold text-info">${
            position.permission_count || 0
        }</span> 项
        </td>
        <td>
        <span class="fw-semibold text-primary">${
            position.employee_count || 0
        }</span> 人
        </td>
        <td class="text-muted">
        ${
            this.formatDateTime(position.created_at)
        }
        </td>
        <td>
        ${
            this.renderActionButtons(position)
        }
        </td>
        </tr>
        `;
    }
    renderActionButtons(position) {
        const canEdit = true; // 所有职位都可以编辑（因为都是用户创建的）
        const canConfigPermissions = true; // 所有职位都可以配置权限
        const canDelete = (position.employee_count == 0); // 只能删除没有员工的职位
        let buttons = '';
        // 权限配置按钮
        if (canConfigPermissions) {
            buttons += `
            <button class="btn btn-sm btn-outline-info me-1"
            onclick="window.jobPositionManager.showPermissionModal(${
                position.id
            })"
            title="权限配置">
            <i class="bi bi-shield-check"></i>
            </button>
            `;
        }
        if (canEdit) {
            buttons += `
            <button class="btn btn-sm btn-outline-primary me-1"
            onclick="window.jobPositionManager.showEditModal(${
                position.id
            })"
            title="编辑职位">
            <i class="bi bi-pencil"></i>
            </button>
            `;
        }
        if (canDelete) {
            buttons += `
            <button class="btn btn-sm btn-outline-danger"
            onclick="window.jobPositionManager.deletePosition(${
                position.id
            })"
            title="删除职位">
            <i class="bi bi-trash"></i>
            </button>
            `;
        }
        if (!canEdit && !canDelete && !canConfigPermissions) {
            buttons = `<span class="text-muted small">无操作权限</span>`;
        }
        return buttons;
    }
    renderPagination(pagination) {
        const container = document.getElementById('positionPagination');
        if (pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }
        let html = `
        <nav aria-label="职位列表分页">
        <ul class="pagination pagination-sm justify-content-center mb-0">
        `;
        // 上一页
        if (pagination.current_page > 1) {
            html += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.jobPositionManager.loadPositions(${
                pagination.current_page - 1
            })">
            <i class="bi bi-chevron-left"></i>
            </a>
            </li>
            `;
        }
        // 页码
        const startPage = Math.max(1,
        pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages,
        pagination.current_page + 2);
        for (let i = startPage;
        i <= endPage;
        i++) {
            html += `
            <li class="page-item ${
                i === pagination.current_page ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.jobPositionManager.loadPositions(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        // 下一页
        if (pagination.current_page < pagination.total_pages) {
            html += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.jobPositionManager.loadPositions(${
                pagination.current_page + 1
            })">
            <i class="bi bi-chevron-right"></i>
            </a>
            </li>
            `;
        }
        html += `
        </ul>
        </nav>
        `;
        container.innerHTML = html;
    }
    updatePositionCount(count) {
        document.getElementById('positionCount').textContent = count;
    }
    searchPositions() {
        this.searchTerm = document.getElementById('positionSearch').value.trim();
        this.loadPositions(1);
    }
    applyFilters() {
        this.statusFilter = document.getElementById('positionStatusFilter').value;
        this.typeFilter = document.getElementById('positionTypeFilter').value;
        this.loadPositions(1);
    }
    refreshPositions() {
        this.loadPositions(this.currentPage);
    }
    showCreateModal() {
        // 创建模态框HTML
        const modalHtml = `
        <div class="modal fade" id="positionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">
        <i class="bi bi-person-plus me-2"></i>添加职位
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <!-- 导航标签 -->
        <ul class="nav nav-tabs mb-3" id="positionTabs" role="tablist">
        <li class="nav-item" role="presentation">
        <button class="nav-link active" id="basic-tab" data-bs-toggle="tab"
        data-bs-target="#basic" type="button" role="tab">
        <i class="bi bi-info-circle me-1"></i>基本信息
        </button>
        </li>
        <li class="nav-item" role="presentation">
        <button class="nav-link" id="permissions-tab" data-bs-toggle="tab"
        data-bs-target="#permissions" type="button" role="tab">
        <i class="bi bi-shield-check me-1"></i>权限配置
        </button>
        </li>
        </ul>
        <!-- 标签内容 -->
        <div class="tab-content" id="positionTabContent">
        <!-- 基本信息标签 -->
        <div class="tab-pane fade show active" id="basic" role="tabpanel">
                                <form id="positionForm">
                        <input type="hidden" id="positionId" value="">
                        <div class="mb-3">
                        <label for="positionName" class="form-label">职位名称 *</label>
                        <input type="text" class="form-control" id="positionName" required
                        placeholder="请输入职位名称">
                        </div>
        <div class="mb-3">
        <label for="positionDescription" class="form-label">职位描述</label>
        <textarea class="form-control" id="positionDescription" rows="3"
        placeholder="请输入职位描述（可选）"></textarea>
        </div>
        <div class="mb-3">
        <label for="positionStatus" class="form-label">状态</label>
        <select class="form-select" id="positionStatus">
        <option value="active" selected>启用</option>
        <option value="inactive">禁用</option>
        </select>
        </div>
        </form>
        </div>
        <!-- 权限配置标签 -->
        <div class="tab-pane fade" id="permissions" role="tabpanel">
        <div class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i>
        为此职位配置权限，员工将根据所属职位自动获得相应权限。
        </div>
        <div id="modalPermissionsList">
        <div class="text-center py-4">
        <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-2 text-muted">正在加载权限数据...</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.jobPositionManager.savePositionWithPermissions()">
        <span class="spinner-border spinner-border-sm d-none" id="positionSaveSpinner"></span>
        <span id="positionSaveText">保存职位</span>
        </button>
        </div>
        </div>
        </div>
        </div>
        `;
        // 移除已存在的模态框
        const existingModal = document.getElementById('positionModal');
        if (existingModal) {
            existingModal.remove();
        }
        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend',
        modalHtml);
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('positionModal'));
        modal.show();
        // 加载权限数据
        this.loadPermissionsForModal();
    }
    async loadPermissionsForModal(positionId = null) {
        try {
            document.getElementById('modalPermissionsList').innerHTML = `
            <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2 text-muted">正在加载权限数据...</div>
            </div>
            `;
            
            // 使用POST方法调用get_permissions，与后端API格式匹配
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=job_positions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem(CONFIG.TOKEN_KEY)}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'get_permissions',
                    position_id: positionId || 0 // 0表示获取所有权限但不获取职位已有权限
                })
            });
            
            const result = await response.json();
            
            // 适应TenantAuth系统的响应格式
            if (result.code === 200) {
                this.renderModalPermissions(result.data.permissions, result.data.position_permissions || []);
            } else {
                throw new Error(result.message || '加载权限失败');
            }
        } catch (error) {
            console.error('加载权限失败:', error);
            document.getElementById('modalPermissionsList').innerHTML = `
            <div class="alert alert-danger">
            加载权限失败: ${error.message}
            </div>
            `;
        }
    }
    renderModalPermissions(permissions,
    positionPermissions) {
        const container = document.getElementById('modalPermissionsList');
        let html = '';
        Object.keys(permissions).forEach(module => {
            html += `
            <div class="card mb-3">
            <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0 text-primary">
            <i class="bi bi-folder me-2"></i>${
                module
            }
            </h6>
            <div>
            <button type="button" class="btn btn-sm btn-outline-secondary"
            onclick="window.jobPositionManager.selectAllInModalModule('${
                module
            }',
            true)">
            全选
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary ms-1"
            onclick="window.jobPositionManager.selectAllInModalModule('${
                module
            }',
            false)">
            全不选
            </button>
            </div>
            </div>
            </div>
            <div class="card-body">
            <div class="row">
            `;
            permissions[module].forEach(permission => {
                const isChecked = positionPermissions.includes(permission.id);
                html += `
                <div class="col-md-6 mb-2">
                <div class="form-check">
                <input class="form-check-input" type="checkbox"
                id="modal_perm_${
                    permission.id
                }"
                value="${
                    permission.id
                }"
                data-module="${
                    module
                }"
                ${
                    isChecked ? 'checked' : ''
                }>
                <label class="form-check-label" for="modal_perm_${
                    permission.id
                }">
                <div class="fw-semibold">${
                    permission.permission_name
                }</div>
                ${
                    permission.description ? `<small class="text-muted">${
                        permission.description
                    }</small>` : ''
                }
                </label>
                </div>
                </div>
                `;
            });
            html += `
            </div>
            </div>
            </div>
            `;
        });
        if (!html) {
            html = '<div class="text-muted text-center py-4">暂无可用权限</div>';
        }
        container.innerHTML = html;
    }
    selectAllInModalModule(module,
    select) {
        const checkboxes = document.querySelectorAll(`#modalPermissionsList input[data-module="${
            module
        }"]`);
        checkboxes.forEach(checkbox => {
            checkbox.checked = select;
        });
    }
    async savePositionWithPermissions() {
        const saveBtn = document.getElementById('positionSaveText');
        const saveSpinner = document.getElementById('positionSaveSpinner');
        const positionId = document.getElementById('positionId').value;
        try {
            saveBtn.textContent = '保存中...';
            saveSpinner.classList.remove('d-none');
            // 验证基本信息
            const formData = {
                action: positionId ? 'update' : 'create',
                position_name: document.getElementById('positionName').value.trim(),
                description: document.getElementById('positionDescription').value.trim(),
                status: document.getElementById('positionStatus').value
            };
            if (positionId) {
                formData.id = parseInt(positionId);
            }
            // 验证必填字段
            if (!formData.position_name) {
                throw new Error('职位名称不能为空');
            }
            // 收集选中的权限
            const permissions = [];
            const permissionCheckboxes = document.querySelectorAll('#modalPermissionsList input[type="checkbox"]:checked');
            permissionCheckboxes.forEach(checkbox => {
                permissions.push(parseInt(checkbox.value));
            });
            // 保存职位和权限
            const requestData = {
                ...formData,
                permissions: permissions,
                save_with_permissions: true // 标识同时保存权限
            };
            console.log('发送请求数据 (savePositionWithPermissions):',
            requestData);
            console.log('状态值:', document.getElementById('positionStatus').value);
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=job_positions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem(CONFIG.TOKEN_KEY)}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            // 检查响应是否为JSON格式
            const responseText = await response.text();
            console.log('服务器响应:', responseText);
            console.log('响应状态:', response.status);
            
            let result;
            try {
                result = JSON.parse(responseText);
            } catch (e) {
                console.error('JSON解析错误:', e);
                console.error('原始响应:', responseText);
                throw new Error('服务器返回了无效的JSON格式数据。请检查服务器日志。');
            }
            
            // 适应TenantAuth系统的响应格式
            if (result.code === 200) {
                this.showSuccess(result.message || '职位保存成功');
                bootstrap.Modal.getInstance(document.getElementById('positionModal')).hide();
                this.refreshPositions();
            } else {
                throw new Error(result.message || '保存失败');
            }
        } catch (error) {
            console.error('保存职位失败:',
            error);
            this.showError('保存失败: ' + error.message);
        } finally {
            saveBtn.textContent = '保存职位';
            saveSpinner.classList.add('d-none');
        }
    }
    showEditModal(positionId) {
        const position = this.positions.find(p => p.id == positionId);
        if (!position) {
            this.showError('找不到职位信息');
            return;
        }
        console.log('编辑职位数据:', position);
        console.log('职位状态值:', position.status, '类型:', typeof position.status);
        this.showCreateModal();
        // 填充表单数据
        setTimeout(() => {
            document.getElementById('positionId').value = position.id;
            document.getElementById('positionName').value = position.position_name;
            document.getElementById('positionDescription').value = position.description || '';
            // 状态转换：数字 1 -> "active", 0 -> "inactive"
            const statusValue = position.status == 1 ? 'active' : 'inactive';
            console.log('设置状态值:', statusValue);
            document.getElementById('positionStatus').value = statusValue;
            console.log('实际设置后的值:', document.getElementById('positionStatus').value);
            document.querySelector('#positionModal .modal-title').innerHTML = `
            <i class="bi bi-pencil me-2"></i>编辑职位
            `;
            // 加载该职位的权限数据
            this.loadPermissionsForModal(positionId);
        }, 200);
    }
    async savePosition() {
        const saveBtn = document.getElementById('positionSaveText');
        const saveSpinner = document.getElementById('positionSaveSpinner');
        const positionId = document.getElementById('positionId').value;
        try {
            saveBtn.textContent = '保存中...';
            saveSpinner.classList.remove('d-none');
            const formData = {
                action: positionId ? 'update' : 'create',
                position_name: document.getElementById('positionName').value.trim(),
                description: document.getElementById('positionDescription').value.trim(),
                status: document.getElementById('positionStatus').value
            };
            if (positionId) {
                formData.id = parseInt(positionId);
            }
            // 验证必填字段
            if (!formData.position_name) {
                throw new Error('职位名称不能为空');
            }
            console.log('发送请求数据 (savePosition):', formData);
            console.log('状态值:', document.getElementById('positionStatus').value);
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=job_positions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess(result.message || '职位保存成功');
                bootstrap.Modal.getInstance(document.getElementById('positionModal')).hide();
                this.refreshPositions();
            } else {
                throw new Error(result.message || '保存失败');
            }
        } catch (error) {
            console.error('保存职位失败:',
            error);
            this.showError('保存失败: ' + error.message);
        } finally {
            saveBtn.textContent = '保存职位';
            saveSpinner.classList.add('d-none');
        }
    }
    async deletePosition(positionId) {
        const position = this.positions.find(p => p.id == positionId);
        if (!position) {
            this.showError('找不到职位信息');
            return;
        }
        if (!confirm(`确定要删除职位 "${
            position.position_name
        }" 吗？\n\n注意：删除后无法恢复，请谨慎操作！`)) {
            return;
        }
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=job_positions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'delete',
                    id: positionId
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('职位删除成功');
                this.refreshPositions();
            } else {
                throw new Error(result.message || '删除失败');
            }
        } catch (error) {
            console.error('删除职位失败:',
            error);
            this.showError('删除失败: ' + error.message);
        }
    }
    showPermissionModal(positionId) {
        const position = this.positions.find(p => p.id == positionId);
        if (!position) {
            this.showError('找不到职位信息');
            return;
        }
        // 创建权限配置模态框
        const modalHtml = `
        <div class="modal fade" id="permissionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">
        <i class="bi bi-shield-check me-2"></i>
        权限配置 - ${
            position.position_name
        }
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <input type="hidden" id="permissionPositionId" value="${
            positionId
        }">
        <div class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i>
        为此职位配置权限，员工将根据所属职位自动获得相应权限。
        </div>
        <div id="permissionsList">
        <div class="text-center py-4">
        <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-2 text-muted">正在加载权限数据...</div>
        </div>
        </div>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.jobPositionManager.savePermissions()">
        <span class="spinner-border spinner-border-sm d-none" id="permissionSaveSpinner"></span>
        <span id="permissionSaveText">保存权限</span>
        </button>
        </div>
        </div>
        </div>
        </div>
        `;
        // 移除已存在的模态框
        const existingModal = document.getElementById('permissionModal');
        if (existingModal) {
            existingModal.remove();
        }
        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend',
        modalHtml);
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
        modal.show();
        // 加载权限数据
        this.loadPositionPermissions(positionId);
    }
    async loadPositionPermissions(positionId) {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=job_positions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'get_permissions',
                    position_id: positionId
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderPermissions(result.data.permissions,
                result.data.position_permissions);
            } else {
                throw new Error(result.message || '加载权限失败');
            }
        } catch (error) {
            console.error('加载权限失败:',
            error);
            document.getElementById('permissionsList').innerHTML = `
            <div class="alert alert-danger">
            加载权限失败: ${
                error.message
            }
            </div>
            `;
        }
    }
    renderPermissions(permissions,
    positionPermissions) {
        const container = document.getElementById('permissionsList');
        let html = '';
        Object.keys(permissions).forEach(module => {
            html += `
            <div class="card mb-3">
            <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0 text-primary">
            <i class="bi bi-folder me-2"></i>${
                module
            }
            </h6>
            <div>
            <button type="button" class="btn btn-sm btn-outline-secondary"
            onclick="window.jobPositionManager.selectAllInModule('${
                module
            }',
            true)">
            全选
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary ms-1"
            onclick="window.jobPositionManager.selectAllInModule('${
                module
            }',
            false)">
            全不选
            </button>
            </div>
            </div>
            </div>
            <div class="card-body">
            <div class="row">
            `;
            permissions[module].forEach(permission => {
                const isChecked = positionPermissions.includes(permission.id);
                html += `
                <div class="col-md-6 mb-2">
                <div class="form-check">
                <input class="form-check-input" type="checkbox"
                id="perm_${
                    permission.id
                }"
                value="${
                    permission.id
                }"
                data-module="${
                    module
                }"
                ${
                    isChecked ? 'checked' : ''
                }>
                <label class="form-check-label" for="perm_${
                    permission.id
                }">
                <div class="fw-semibold">${
                    permission.permission_name
                }</div>
                ${
                    permission.description ? `<small class="text-muted">${
                        permission.description
                    }</small>` : ''
                }
                </label>
                </div>
                </div>
                `;
            });
            html += `
            </div>
            </div>
            </div>
            `;
        });
        if (!html) {
            html = '<div class="text-muted text-center py-4">暂无可用权限</div>';
        }
        container.innerHTML = html;
    }
    selectAllInModule(module,
    select) {
        const checkboxes = document.querySelectorAll(`#permissionsList input[data-module="${
            module
        }"]`);
        checkboxes.forEach(checkbox => {
            checkbox.checked = select;
        });
    }
    async savePermissions() {
        const saveBtn = document.getElementById('permissionSaveText');
        const saveSpinner = document.getElementById('permissionSaveSpinner');
        const positionId = document.getElementById('permissionPositionId').value;
        try {
            saveBtn.textContent = '保存中...';
            saveSpinner.classList.remove('d-none');
            // 收集选中的权限
            const permissions = [];
            const permissionCheckboxes = document.querySelectorAll('#permissionsList input[type="checkbox"]:checked');
            permissionCheckboxes.forEach(checkbox => {
                permissions.push(parseInt(checkbox.value));
            });
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=job_positions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'update_permissions',
                    position_id: parseInt(positionId),
                    permissions: permissions
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess(result.message || '权限配置成功');
                bootstrap.Modal.getInstance(document.getElementById('permissionModal')).hide();
                this.refreshPositions();
                // 刷新列表以更新权限数量
            } else {
                throw new Error(result.message || '保存失败');
            }
        } catch (error) {
            console.error('保存权限失败:',
            error);
            this.showError('保存失败: ' + error.message);
        } finally {
            saveBtn.textContent = '保存权限';
            saveSpinner.classList.add('d-none');
        }
    }
    showErrorState(message) {
        const tbody = document.getElementById('positionTableBody');
        tbody.innerHTML = `
        <tr>
        <td colspan="7" class="text-center py-4">
        <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;
        "></i>
        <p class="text-danger mt-2">${
            message
        }</p>
        <button class="btn btn-primary" onclick="window.jobPositionManager.refreshPositions()">
        <i class="bi bi-arrow-clockwise me-2"></i>重试
        </button>
        </td>
        </tr>
        `;
    }
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    showSuccess(message) {
        alert('成功：' + message);
    }
    showError(message) {
        alert('错误：' + message);
    }
}

// 创建实例并导出
const jobPositionManager = new JobPositionManager();

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = jobPositionManager;
} else if (typeof window !== "undefined") {
    window.JobPositionManager = jobPositionManager;
    window.jobPositionManager = jobPositionManager; // 兼容性别名
}

console.log('📦 JobPositionManager 模块加载完成');
