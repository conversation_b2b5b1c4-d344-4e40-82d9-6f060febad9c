<?php
/**
 * 完整的支付流程管理系统
 * 
 * 功能包括：
 * 1. 支付请求生命周期管理
 * 2. 支付状态流转控制
 * 3. 支付通知和回调处理
 * 4. 支付异常处理和重试机制
 * 5. 支付数据统计和分析
 * 
 * <AUTHOR> System
 * @version 1.0
 */

require_once dirname(__FILE__) . '/Database.php';
require_once dirname(__FILE__) . '/Logger.php';

class PaymentFlowManager {
    
    private $db;
    private $logger;
    
    // 支付状态定义
    const STATUS_PENDING = 'pending';      // 待支付
    const STATUS_PAID = 'paid';           // 已支付
    const STATUS_EXPIRED = 'expired';     // 已过期
    const STATUS_CANCELLED = 'cancelled'; // 已取消
    
    // 支付类型定义
    const PAYMENT_TYPE_ALIPAY = 'alipay';
    const PAYMENT_TYPE_WECHAT = 'wechat';
    const PAYMENT_TYPE_UNIONPAY = 'unionpay';
    
    // 通知状态
    const NOTIFY_STATUS_PENDING = 'pending';
    const NOTIFY_STATUS_SUCCESS = 'success';
    const NOTIFY_STATUS_FAILED = 'failed';
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->logger = new Logger('PaymentFlowManager');
    }
    
    /**
     * 创建支付请求
     * 
     * @param array $params 支付请求参数
     * @return array 创建结果
     */
    public function createPaymentRequest($params) {
        try {
            // 参数验证
            $this->validateCreateParams($params);
            
            // 生成唯一请求ID
            $requestId = $this->generateRequestId();
            
            // 计算过期时间（默认30分钟）
            $expiredAt = date('Y-m-d H:i:s', time() + (isset($params['expire_minutes']) ? $params['expire_minutes'] : 30) * 60);
            
            // 计算手续费
            $feeInfo = $this->calculateFee($params['merchant_id'], $params['amount']);
            
            // 选择最优支付账户
            $paymentAccount = $this->selectOptimalPaymentAccount($params);
            
            // 开始事务
            $this->db->beginTransaction();
            
            // 插入支付请求记录
            $requestData = array(
                'request_id' => $requestId,
                'merchant_id' => $params['merchant_id'],
                'product_id' => isset($params['product_id']) ? $params['product_id'] : null,
                'alipay_account_id' => isset($paymentAccount['id']) ? $paymentAccount['id'] : null,
                'amount' => $params['amount'],
                'order_no' => $params['merchant_order_no'],
                'subject' => isset($params['subject']) ? $params['subject'] : '',
                'body' => isset($params['body']) ? $params['body'] : '',
                'fee' => $feeInfo['fee'],
                'fee_rate' => $feeInfo['rate'],
                'notification_url' => isset($params['notify_url']) ? $params['notify_url'] : '',
                'return_url' => isset($params['return_url']) ? $params['return_url'] : '',
                'client_ip' => isset($params['client_ip']) ? $params['client_ip'] : $_SERVER['REMOTE_ADDR'],
                'user_agent' => isset($params['user_agent']) ? $params['user_agent'] : $_SERVER['HTTP_USER_AGENT'],
                'extra_data' => json_encode(isset($params['extra_data']) ? $params['extra_data'] : array()),
                'expired_at' => $expiredAt,
                'status' => self::STATUS_PENDING
            );
            
            $requestDbId = $this->db->insert('payment_requests', $requestData);
            
            // 生成支付二维码或链接
            $paymentInfo = $this->generatePaymentInfo($requestDbId, $requestData, $paymentAccount);
            
            // 更新二维码信息
            $this->db->update('payment_requests', 
                array('qr_code_url' => $paymentInfo['qr_code_url']),
                array('id' => $requestDbId)
            );
            
            // 记录操作日志
            $this->logPaymentAction($requestDbId, 'create', '创建支付请求', $params);
            
            // 如果有支付账户，标记为占用状态
            if (!empty($paymentAccount)) {
                $this->markAccountAsOccupied($paymentAccount['id'], $requestDbId);
            }
            
            $this->db->commit();
            
            return array(
                'success' => true,
                'data' => array(
                    'request_id' => $requestId,
                    'order_no' => $params['merchant_order_no'],
                    'amount' => $params['amount'],
                    'fee' => $feeInfo['fee'],
                    'actual_amount' => $params['amount'] - $feeInfo['fee'],
                    'qr_code_url' => $paymentInfo['qr_code_url'],
                    'payment_url' => isset($paymentInfo['payment_url']) ? $paymentInfo['payment_url'] : '',
                    'expired_at' => $expiredAt,
                    'status' => self::STATUS_PENDING
                )
            );
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->logger->error('创建支付请求失败: ' . $e->getMessage(), $params);
            
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'CREATE_PAYMENT_FAILED'
            );
        }
    }
    
    /**
     * 查询支付请求状态
     * 
     * @param string $requestId 请求ID
     * @param int $merchantId 商户ID（用于权限验证）
     * @return array 查询结果
     */
    public function queryPaymentRequest($requestId, $merchantId = null) {
        try {
            $whereConditions = array('request_id = ?');
            $params = array($requestId);
            
            // 如果指定了商户ID，添加权限验证
            if ($merchantId !== null) {
                $whereConditions[] = 'merchant_id = ?';
                $params[] = $merchantId;
            }
            
            $request = $this->db->fetch(
                "SELECT pr.*, m.company_name as merchant_name, aa.account_name, aa.account_number
                 FROM payment_requests pr
                 LEFT JOIN merchants m ON pr.merchant_id = m.id
                 LEFT JOIN alipay_accounts aa ON pr.alipay_account_id = aa.id
                 WHERE " . implode(' AND ', $whereConditions),
                $params
            );
            
            if (!$request) {
                throw new Exception('支付请求不存在');
            }
            
            // 检查是否过期
            if ($request['status'] === self::STATUS_PENDING && 
                strtotime($request['expired_at']) < time()) {
                $this->expirePaymentRequest($request['id']);
                $request['status'] = self::STATUS_EXPIRED;
            }
            
            return array(
                'success' => true,
                'data' => $this->formatPaymentRequestData($request)
            );
            
        } catch (Exception $e) {
            $this->logger->error('查询支付请求失败: ' . $e->getMessage(), array(
                'request_id' => $requestId,
                'merchant_id' => $merchantId
            ));
            
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'QUERY_PAYMENT_FAILED'
            );
        }
    }
    
    /**
     * 处理支付通知
     * 
     * @param array $notifyData 通知数据
     * @return array 处理结果
     */
    public function handlePaymentNotification($notifyData) {
        try {
            // 验证通知数据
            $this->validateNotificationData($notifyData);
            
            // 查找对应的支付请求
            $request = $this->findRequestByNotification($notifyData);
            
            if (!$request) {
                throw new Exception('找不到对应的支付请求');
            }
            
            // 防重复处理
            if ($request['status'] !== self::STATUS_PENDING) {
                $this->logger->warning('重复的支付通知', array(
                    'request_id' => $request['request_id'],
                    'current_status' => $request['status'],
                    'notify_data' => $notifyData
                ));
                
                return array(
                    'success' => true,
                    'message' => '订单已处理',
                    'status' => $request['status']
                );
            }
            
            // 开始事务处理
            $this->db->beginTransaction();
            
            // 更新支付请求状态
            $updateData = array(
                'status' => self::STATUS_PAID,
                'actual_amount' => $notifyData['amount'],
                'paid_at' => date('Y-m-d H:i:s'),
                'payer_name' => isset($notifyData['payer_name']) ? $notifyData['payer_name'] : '',
                'transaction_id' => isset($notifyData['transaction_id']) ? $notifyData['transaction_id'] : '');
            
            $this->db->update('payment_requests', $updateData, array('id' => $request['id']));
            
            // 创建交易记录
            $this->createTransactionRecord($request, $notifyData);
            
            // 释放支付账户占用状态
            if ($request['alipay_account_id']) {
                $this->releaseAccountOccupation($request['alipay_account_id']);
            }
            
            // 更新商户财务记录
            $this->updateMerchantFinancialRecord($request, $notifyData);
            
            // 记录操作日志
            $this->logPaymentAction($request['id'], 'paid', '支付成功', $notifyData);
            
            $this->db->commit();
            
            // 异步发送商户通知
            $this->sendMerchantNotification($request, $notifyData);
            
            return array(
                'success' => true,
                'message' => '支付处理成功',
                'data' => array(
                    'request_id' => $request['request_id'],
                    'status' => self::STATUS_PAID,
                    'amount' => $notifyData['amount'],
                    'paid_at' => $updateData['paid_at']
                )
            );
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->logger->error('处理支付通知失败: ' . $e->getMessage(), $notifyData);
            
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'HANDLE_NOTIFICATION_FAILED'
            );
        }
    }
    
    /**
     * 取消支付请求
     * 
     * @param string $requestId 请求ID
     * @param int $merchantId 商户ID
     * @param string $reason 取消原因
     * @return array 取消结果
     */
    public function cancelPaymentRequest($requestId, $merchantId, $reason = '') {
        try {
            $request = $this->db->fetch(
                "SELECT * FROM payment_requests WHERE request_id = ? AND merchant_id = ?",
                array($requestId, $merchantId)
            );
            
            if (!$request) {
                throw new Exception('支付请求不存在');
            }
            
            if ($request['status'] !== self::STATUS_PENDING) {
                throw new Exception('只能取消待支付状态的请求');
            }
            
            // 开始事务
            $this->db->beginTransaction();
            
            // 更新状态
            $this->db->update('payment_requests', 
                array(
                    'status' => self::STATUS_CANCELLED,
                    'updated_at' => date('Y-m-d H:i:s')
                ),
                array('id' => $request['id'])
            );
            
            // 释放支付账户
            if ($request['alipay_account_id']) {
                $this->releaseAccountOccupation($request['alipay_account_id']);
            }
            
            // 记录操作日志
            $this->logPaymentAction($request['id'], 'cancel', '取消支付请求: ' . $reason);
            
            $this->db->commit();
            
            return array(
                'success' => true,
                'message' => '支付请求已取消'
            );
            
        } catch (Exception $e) {
            $this->db->rollback();
            $this->logger->error('取消支付请求失败: ' . $e->getMessage(), array(
                'request_id' => $requestId,
                'merchant_id' => $merchantId,
                'reason' => $reason
            ));
            
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'CANCEL_PAYMENT_FAILED'
            );
        }
    }
    
    /**
     * 获取支付流程统计数据
     * 
     * @param int $merchantId 商户ID（可选）
     * @param array $filters 筛选条件
     * @return array 统计结果
     */
    public function getPaymentFlowStatistics($merchantId = null, $filters = array()) {
        try {
            $whereConditions = array();
            $params = array();
            
            // 商户权限控制
            if ($merchantId !== null) {
                $whereConditions[] = 'merchant_id = ?';
                $params[] = $merchantId;
            }
            
            // 时间范围筛选
            if (!empty($filters['start_date'])) {
                $whereConditions[] = 'created_at >= ?';
                $params[] = $filters['start_date'] . ' 00:00:00';
            }
            
            if (!empty($filters['end_date'])) {
                $whereConditions[] = 'created_at <= ?';
                $params[] = $filters['end_date'] . ' 23:59:59';
            }
            
            $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);
            
            // 基础统计
            $basicStats = $this->db->fetch(
                "SELECT 
                    COUNT(*) as total_requests,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
                    COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_requests,
                    COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_requests,
                    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_requests,
                    SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as total_paid_amount,
                    SUM(CASE WHEN status = 'paid' THEN fee ELSE 0 END) as total_fee_amount,
                    AVG(CASE WHEN status = 'paid' THEN amount ELSE NULL END) as avg_paid_amount
                 FROM payment_requests $whereClause",
                $params
            );
            
            // 成功率统计
            $successRate = $basicStats['total_requests'] > 0 ? 
                round(($basicStats['paid_requests'] / $basicStats['total_requests']) * 100, 2) : 0;
            
            // 按日期统计
            $dailyStats = $this->db->fetchAll(
                "SELECT 
                    DATE(created_at) as stat_date,
                    COUNT(*) as requests_count,
                    COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count,
                    SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as paid_amount
                 FROM payment_requests $whereClause
                 GROUP BY DATE(created_at)
                 ORDER BY stat_date DESC
                 LIMIT 30",
                $params
            );
            
            // 按状态统计
            $statusStats = $this->db->fetchAll(
                "SELECT 
                    status,
                    COUNT(*) as count,
                    SUM(amount) as total_amount
                 FROM payment_requests $whereClause
                 GROUP BY status",
                $params
            );
            
            return array(
                'success' => true,
                'data' => array(
                    'basic_stats' => $basicStats,
                    'success_rate' => $successRate,
                    'daily_stats' => $dailyStats,
                    'status_stats' => $statusStats,
                    'generated_at' => date('Y-m-d H:i:s')
                )
            );
            
        } catch (Exception $e) {
            $this->logger->error('获取支付流程统计失败: ' . $e->getMessage(), array(
                'merchant_id' => $merchantId,
                'filters' => $filters
            ));
            
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'GET_STATISTICS_FAILED'
            );
        }
    }
    
    /**
     * 清理过期的支付请求
     * 
     * @return array 清理结果
     */
    public function cleanExpiredRequests() {
        try {
            $this->logger->info('开始清理过期支付请求');
            
            // 查找过期的待支付请求
            $expiredRequests = $this->db->fetchAll(
                "SELECT id, request_id, alipay_account_id 
                 FROM payment_requests 
                 WHERE status = ? AND expired_at < NOW()",
                array(self::STATUS_PENDING)
            );
            
            $cleanedCount = 0;
            
            foreach ($expiredRequests as $request) {
                try {
                    $this->db->beginTransaction();
                    
                    // 更新状态为过期
                    $this->db->update('payment_requests',
                        array('status' => self::STATUS_EXPIRED),
                        array('id' => $request['id'])
                    );
                    
                    // 释放支付账户占用
                    if ($request['alipay_account_id']) {
                        $this->releaseAccountOccupation($request['alipay_account_id']);
                    }
                    
                    // 记录日志
                    $this->logPaymentAction($request['id'], 'expire', '自动过期清理');
                    
                    $this->db->commit();
                    $cleanedCount++;
                    
                } catch (Exception $e) {
                    $this->db->rollback();
                    $this->logger->error('清理过期请求失败: ' . $e->getMessage(), $request);
                }
            }
            
            $this->logger->info("过期支付请求清理完成，共清理 {$cleanedCount} 条记录");
            
            return array(
                'success' => true,
                'cleaned_count' => $cleanedCount,
                'total_expired' => count($expiredRequests)
            );
            
        } catch (Exception $e) {
            $this->logger->error('清理过期支付请求失败: ' . $e->getMessage());
            
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'error_code' => 'CLEAN_EXPIRED_FAILED'
            );
        }
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 验证创建支付请求的参数
     */
    private function validateCreateParams($params) {
        $required = array('merchant_id', 'amount', 'merchant_order_no');
        
        foreach ($required as $field) {
            if (!isset($params[$field]) || $params[$field] === '') {
                throw new Exception("缺少必需参数: {$field}");
            }
        }
        
        if (!is_numeric($params['amount']) || $params['amount'] <= 0) {
            throw new Exception('支付金额必须大于0');
        }
        
        if (strlen($params['merchant_order_no']) > 64) {
            throw new Exception('商户订单号长度不能超过64个字符');
        }
        
        // 检查订单号是否重复
        $existing = $this->db->fetch(
            "SELECT id FROM payment_requests WHERE merchant_id = ? AND order_no = ?",
            array($params['merchant_id'], $params['merchant_order_no'])
        );
        
        if ($existing) {
            throw new Exception('商户订单号已存在');
        }
    }
    
    /**
     * 生成唯一请求ID
     */
    private function generateRequestId() {
        return 'PR' . date('YmdHis') . mt_rand(100000, 999999);
    }
    
    /**
     * 计算手续费
     */
    private function calculateFee($merchantId, $amount) {
        // 获取商户费率配置
        $merchant = $this->db->fetch(
            "SELECT service_rate FROM merchants WHERE id = ?",
            array($merchantId)
        );
        
        $rate = $merchant ? floatval($merchant['service_rate']) : 0.5; // 默认0.5%
        $fee = round($amount * $rate / 100, 2);
        
        return array(
            'rate' => $rate,
            'fee' => $fee
        );
    }
    
    /**
     * 选择最优支付账户
     */
    private function selectOptimalPaymentAccount($params) {
        // 根据金额范围和可用状态选择最优账户
        $account = $this->db->fetch(
            "SELECT * FROM alipay_accounts 
             WHERE status = 'approved' 
             AND use_status = 0 
             AND (fixed_amount IS NULL OR fixed_amount = ?) 
             AND (amount_range_min IS NULL OR amount_range_min <= ?) 
             AND (amount_range_max IS NULL OR amount_range_max >= ?)
             ORDER BY today_call_count ASC, RAND()
             LIMIT 1",
            array($params['amount'], $params['amount'], $params['amount'])
        );
        
        return $account;
    }
    
    /**
     * 生成支付信息（二维码等）
     */
    private function generatePaymentInfo($requestId, $requestData, $paymentAccount) {
        // 这里应该调用具体的支付渠道API生成二维码
        // 暂时返回模拟数据
        $qrCodeUrl = "https://api.payment.com/qrcode/{$requestData['request_id']}";
        $paymentUrl = "https://payment.com/pay/{$requestData['request_id']}";
        
        return array(
            'qr_code_url' => $qrCodeUrl,
            'payment_url' => $paymentUrl
        );
    }
    
    /**
     * 标记账户为占用状态
     */
    private function markAccountAsOccupied($accountId, $requestId) {
        $this->db->update('alipay_accounts',
            array(
                'use_status' => 1,
                'last_use_time' => date('Y-m-d H:i:s')
            ),
            array('id' => $accountId)
        );
    }
    
    /**
     * 释放账户占用状态
     */
    private function releaseAccountOccupation($accountId) {
        $this->db->update('alipay_accounts',
            array('use_status' => 0),
            array('id' => $accountId)
        );
    }
    
    /**
     * 记录支付操作日志
     */
    private function logPaymentAction($requestId, $action, $description, $extraData = array()) {
        $this->db->insert('system_logs', array(
            'user_id' => null,
            'action' => "payment_{$action}",
            'target_type' => 'payment_request',
            'target_id' => $requestId,
            'description' => $description,
            'ip_address' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '',
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'extra_data' => json_encode($extraData)
        ));
    }
    
    /**
     * 格式化支付请求数据
     */
    private function formatPaymentRequestData($request) {
        return array(
            'request_id' => $request['request_id'],
            'merchant_id' => $request['merchant_id'],
            'merchant_name' => isset($request['merchant_name']) ? $request['merchant_name'] : '',
            'order_no' => $request['order_no'],
            'amount' => floatval($request['amount']),
            'actual_amount' => floatval(isset($request['actual_amount']) ? $request['actual_amount'] : 0),
            'fee' => floatval(isset($request['fee']) ? $request['fee'] : 0),
            'fee_rate' => floatval(isset($request['fee_rate']) ? $request['fee_rate'] : 0),
            'status' => $request['status'],
            'qr_code_url' => isset($request['qr_code_url']) ? $request['qr_code_url'] : '',
            'subject' => isset($request['subject']) ? $request['subject'] : '',
            'body' => isset($request['body']) ? $request['body'] : '',
            'client_ip' => isset($request['client_ip']) ? $request['client_ip'] : '',
            'created_at' => $request['created_at'],
            'paid_at' => $request['paid_at'],
            'expired_at' => $request['expired_at'],
            'payment_account' => array(
                'account_name' => isset($request['account_name']) ? $request['account_name'] : '',
                'account_number' => isset($request['account_number']) ? $request['account_number'] : '')
        );
    }
    
    /**
     * 验证支付通知数据
     */
    private function validateNotificationData($notifyData) {
        $required = array('amount', 'transaction_id');
        
        foreach ($required as $field) {
            if (!isset($notifyData[$field]) || $notifyData[$field] === '') {
                throw new Exception("通知数据缺少必需字段: {$field}");
            }
        }
    }
    
    /**
     * 根据通知数据查找支付请求
     */
    private function findRequestByNotification($notifyData) {
        // 可以根据不同字段查找，优先级：request_id > order_no > transaction_id
        if (!empty($notifyData['request_id'])) {
            return $this->db->fetch(
                "SELECT * FROM payment_requests WHERE request_id = ?",
                array($notifyData['request_id'])
            );
        }
        
        if (!empty($notifyData['order_no'])) {
            return $this->db->fetch(
                "SELECT * FROM payment_requests WHERE order_no = ?",
                array($notifyData['order_no'])
            );
        }
        
        return null;
    }
    
    /**
     * 创建交易记录
     */
    private function createTransactionRecord($request, $notifyData) {
        $transactionData = array(
            'order_id' => $request['order_no'],
            'merchant_id' => $request['merchant_id'],
            'provider_id' => 1, // 默认码商ID，实际应该从支付账户获取
            'alipay_account_id' => $request['alipay_account_id'],
            'amount' => $request['amount'],
            'rate' => $request['fee_rate'],
            'fee' => $request['fee'],
            'actual_amount' => $request['amount'] - $request['fee'],
            'status' => 'success',
            'request_id' => $request['id'],
            'transaction_id' => $notifyData['transaction_id'],
            'payer_name' => isset($notifyData['payer_name']) ? $notifyData['payer_name'] : '',
            'payment_type' => self::PAYMENT_TYPE_ALIPAY,
            'paid_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->insert('transactions', $transactionData);
    }
    
    /**
     * 更新商户财务记录
     */
    private function updateMerchantFinancialRecord($request, $notifyData) {
        // 这里应该更新商户的财务统计数据
        // 暂时记录到财务日志表
        $this->db->insert('merchant_financial_logs', array(
            'merchant_id' => $request['merchant_id'],
            'type' => 'income',
            'amount' => $request['amount'] - $request['fee'],
            'fee' => $request['fee'],
            'description' => "支付收款 - 订单号: {$request['order_no']}",
            'reference_type' => 'payment_request',
            'reference_id' => $request['id'],
            'created_at' => date('Y-m-d H:i:s')
        ));
    }
    
    /**
     * 发送商户通知
     */
    private function sendMerchantNotification($request, $notifyData) {
        if (empty($request['notification_url'])) {
            return;
        }
        
        // 构建通知数据
        $notificationData = array(
            'request_id' => $request['request_id'],
            'merchant_order_no' => $request['order_no'],
            'amount' => $request['amount'],
            'actual_amount' => $request['amount'] - $request['fee'],
            'fee' => $request['fee'],
            'status' => 'paid',
            'paid_at' => date('Y-m-d H:i:s'),
            'transaction_id' => $notifyData['transaction_id'],
            'payer_name' => isset($notifyData['payer_name']) ? $notifyData['payer_name'] : '',
            'timestamp' => time()
        );
        
        // 生成签名
        $notificationData['sign'] = $this->generateNotificationSign($notificationData, $request['merchant_id']);
        
        // 异步发送通知
        $this->sendAsyncNotification($request['notification_url'], $notificationData, $request['id']);
    }
    
    /**
     * 生成通知签名
     */
    private function generateNotificationSign($data, $merchantId) {
        // 获取商户密钥
        $apiKey = $this->db->fetch(
            "SELECT secret_key FROM api_keys WHERE merchant_id = ? AND status = 'active' LIMIT 1",
            array($merchantId)
        );
        
        if (!$apiKey) {
            return '';
        }
        
        // 按字典序排序参数
        ksort($data);
        $signString = '';
        foreach ($data as $key => $value) {
            if ($key !== 'sign' && $value !== '') {
                $signString .= $key . '=' . $value . '&';
            }
        }
        $signString .= 'key=' . $apiKey['secret_key'];
        
        return strtoupper(hash('sha256', $signString));
    }
    
    /**
     * 异步发送通知
     */
    private function sendAsyncNotification($url, $data, $requestId) {
        // 记录通知任务
        $this->db->insert('merchant_notification_tasks', array(
            'merchant_id' => isset($data['merchant_id']) ? $data['merchant_id'] : 0,
            'request_id' => $requestId,
            'notification_url' => $url,
            'notification_data' => json_encode($data),
            'status' => self::NOTIFY_STATUS_PENDING,
            'retry_count' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ));
    }
    
    /**
     * 使支付请求过期
     */
    private function expirePaymentRequest($requestId) {
        $this->db->update('payment_requests',
            array('status' => self::STATUS_EXPIRED),
            array('id' => $requestId)
        );
        
        $this->logPaymentAction($requestId, 'expire', '支付请求已过期');
    }
}