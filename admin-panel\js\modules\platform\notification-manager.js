/**
 * 通知管理模块
 * 从admin.js第12962行开始提取
 * 负责系统通知的发送、管理、状态跟踪等功能
 */
// 通知任务管理类
class NotificationManager {
    constructor() {
        this.auth = new AuthManager();
        this.currentPage = 1;
        this.pageSize = 20;
        this.notifications = [];
        this.filters = {
            merchant_id: '',
            status: '',
            type: '',
            date_range: 'today',
            search: ''
        };
        this.selectedNotificationId = null;
    }

    async initialize() {
        this.bindEvents();
        await this.loadMerchantOptions();
        await this.loadNotifications();
    }

    bindEvents() {
        // 筛选器变化事件
        const merchantFilter = document.getElementById('notificationMerchantFilter');
        if (merchantFilter) {
            merchantFilter.addEventListener('change', (e) => {
                this.filters.merchant_id = e.target.value;
                this.applyFilters();
            });
        }

        const statusFilter = document.getElementById('notificationStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }

        const typeFilter = document.getElementById('notificationTypeFilter');
        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => {
                this.filters.type = e.target.value;
                this.applyFilters();
            });
        }

        // 搜索框回车事件
        const searchInput = document.getElementById('notificationSearchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchNotifications();
                }
            });
        }
    }

    async loadNotifications(page = 1) {
        try {
            this.currentPage = page;
            this.showLoadingState();

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                merchant_id: this.filters.merchant_id,
                status: this.filters.status,
                type: this.filters.type,
                search: this.filters.search
            });

            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=notifications&${params}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`,
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.code === 200) {
                this.notifications = result.data.notifications || [];
                this.updateStatsCards(result.data.stats || {});
                this.renderNotificationsList(this.notifications);
                this.renderPagination(result.data.pagination || {});
            } else {
                throw new Error(result.message || '加载通知任务数据失败');
            }
        } catch (error) {
            console.error('加载通知任务数据失败:', error);
            this.showErrorState('加载通知任务数据失败: ' + error.message);
        }
    }

    updateStatsCards(stats) {
        const totalEl = document.getElementById('totalNotifications');
        const pendingEl = document.getElementById('pendingNotifications');
        const successEl = document.getElementById('successNotifications');
        const failedEl = document.getElementById('failedNotifications');
        
        if (totalEl) totalEl.textContent = stats.total_notifications || 0;
        if (pendingEl) pendingEl.textContent = stats.pending_notifications || 0;
        if (successEl) successEl.textContent = stats.success_notifications || 0;
        if (failedEl) failedEl.textContent = stats.failed_notifications || 0;
    }

    renderNotificationsList(notifications) {
        const container = document.getElementById('notificationTableContainer');
        const countEl = document.getElementById('notificationCount');
        
        if (countEl) countEl.textContent = notifications.length;

        if (notifications.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="no-data-icon">🔔</div>
                    <h5>暂无通知任务数据</h5>
                    <p class="text-muted">还没有符合条件的通知任务，或者当前筛选条件下没有找到匹配的数据。</p>
                </div>
            `;
            return;
        }

        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>任务ID</th>
                            <th>商户信息</th>
                            <th>通知类型</th>
                            <th>标题</th>
                            <th>状态</th>
                            <th>重试次数</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${notifications.map(notification => this.renderNotificationRow(notification)).join('')}
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = tableHtml;
    }

    renderNotificationRow(notification) {
        const statusBadges = {
            'pending': 'badge bg-warning',
            'success': 'badge bg-success',
            'failed': 'badge bg-danger',
            'processing': 'badge bg-info'
        };

        const statusTexts = {
            'pending': '待发送',
            'success': '已成功',
            'failed': '失败',
            'processing': '发送中'
        };

        const typeTexts = {
            'payment': '支付通知',
            'system': '系统通知',
            'promotion': '营销通知',
            'alert': '警报通知'
        };

        return `
            <tr>
                <td>
                    <div class="fw-bold">#${notification.id}</div>
                    <div class="text-muted small">请求ID: ${notification.request_id || 'N/A'}</div>
                </td>
                <td>
                    <div class="fw-bold">${notification.merchant_name || '未知商户'}</div>
                    <div class="text-muted small">ID: ${notification.merchant_id}</div>
                </td>
                <td>
                    <span class="badge bg-light text-dark">
                        ${typeTexts[notification.notification_type] || notification.notification_type}
                    </span>
                </td>
                <td>
                    <div class="fw-bold">${notification.title}</div>
                    ${notification.content ? `<div class="text-muted small text-truncate" style="max-width: 200px;">${notification.content}</div>` : ''}
                </td>
                <td>
                    <span class="${statusBadges[notification.status] || 'badge bg-secondary'}">
                        ${statusTexts[notification.status] || notification.status}
                    </span>
                </td>
                <td>
                    <div class="text-center">
                        <span class="badge ${notification.retry_count > 3 ? 'bg-danger' : 'bg-secondary'}">
                            ${notification.retry_count || 0}/5
                        </span>
                    </div>
                </td>
                <td>
                    <div class="small">${this.formatDateTime(notification.created_at)}</div>
                    ${notification.completed_at ? `<div class="text-success small">完成: ${this.formatDateTime(notification.completed_at)}</div>` : ''}
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="window.notificationManager.viewNotificationDetail(${notification.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${notification.status === 'failed' ? `
                            <button class="btn btn-outline-success btn-sm" onclick="window.notificationManager.retryNotification(${notification.id})" title="重试">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        ` : ''}
                        ${notification.status === 'pending' ? `
                            <button class="btn btn-outline-warning btn-sm" onclick="window.notificationManager.cancelNotification(${notification.id})" title="取消">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }

    async loadMerchantOptions() {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=merchants&simple=1`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const result = await response.json();
            
            if (result.code === 200) {
                const merchantFilter = document.getElementById('notificationMerchantFilter');
                if (merchantFilter && result.data.merchants) {
                    let optionsHtml = '<option value="">所有商户</option>';
                    result.data.merchants.forEach(merchant => {
                        optionsHtml += `<option value="${merchant.id}">${merchant.name || merchant.username}</option>`;
                    });
                    merchantFilter.innerHTML = optionsHtml;
                }
            }
        } catch (error) {
            console.error('加载商户选项失败:', error);
        }
    }

    applyFilters() {
        this.loadNotifications(1);
    }

    searchNotifications() {
        const searchInput = document.getElementById('notificationSearchInput');
        if (searchInput) {
            this.filters.search = searchInput.value.trim();
            this.loadNotifications(1);
        }
    }

    refreshNotifications() {
        this.loadNotifications(this.currentPage);
    }

    async viewNotificationDetail(notificationId) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=notifications&id=${notificationId}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const result = await response.json();

            if (result.code === 200) {
                this.showNotificationDetailModal(result.data);
            } else {
                throw new Error(result.message || '获取通知详情失败');
            }
        } catch (error) {
            console.error('获取通知详情失败:', error);
            this.showError('获取通知详情失败: ' + error.message);
        }
    }

    showNotificationDetailModal(notification) {
        const modalContent = document.getElementById('notificationDetailContent');
        if (!modalContent) return;

        modalContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>任务ID:</strong></td>
                            <td>#${notification.id}</td>
                        </tr>
                        <tr>
                            <td><strong>商户:</strong></td>
                            <td>${notification.merchant_name || '未知商户'}</td>
                        </tr>
                        <tr>
                            <td><strong>通知类型:</strong></td>
                            <td>${notification.notification_type}</td>
                        </tr>
                        <tr>
                            <td><strong>标题:</strong></td>
                            <td>${notification.title}</td>
                        </tr>
                        <tr>
                            <td><strong>状态:</strong></td>
                            <td>${notification.status}</td>
                        </tr>
                        <tr>
                            <td><strong>重试次数:</strong></td>
                            <td>${notification.retry_count || 0}/5</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>时间信息</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>创建时间:</strong></td>
                            <td>${this.formatDateTime(notification.created_at)}</td>
                        </tr>
                        ${notification.scheduled_at ? `
                        <tr>
                            <td><strong>计划发送:</strong></td>
                            <td>${this.formatDateTime(notification.scheduled_at)}</td>
                        </tr>
                        ` : ''}
                        ${notification.completed_at ? `
                        <tr>
                            <td><strong>完成时间:</strong></td>
                            <td>${this.formatDateTime(notification.completed_at)}</td>
                        </tr>
                        ` : ''}
                        ${notification.last_retry_at ? `
                        <tr>
                            <td><strong>最后重试:</strong></td>
                            <td>${this.formatDateTime(notification.last_retry_at)}</td>
                        </tr>
                        ` : ''}
                    </table>
                </div>
            </div>
            
            <div class="mt-3">
                <h6>通知内容</h6>
                <div class="alert alert-info">
                    ${notification.content}
                </div>
            </div>
            
            ${notification.notification_url ? `
            <div class="mt-3">
                <h6>通知地址</h6>
                <div class="text-break">
                    <code>${notification.notification_url}</code>
                </div>
            </div>
            ` : ''}
            
            ${notification.error_message ? `
            <div class="mt-3">
                <h6>错误信息</h6>
                <div class="alert alert-danger">
                    ${notification.error_message}
                </div>
            </div>
            ` : ''}
            
            ${notification.response_data ? `
            <div class="mt-3">
                <h6>响应数据</h6>
                <pre class="bg-light p-3 rounded"><code>${JSON.stringify(JSON.parse(notification.response_data), null, 2)}</code></pre>
            </div>
            ` : ''}
        `;

        const modal = new bootstrap.Modal(document.getElementById('notificationDetailModal'));
        modal.show();
    }

    async retryNotification(notificationId) {
        if (!confirm('确定要重试此通知任务吗？')) return;

        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=notifications&retry=1`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    notification_id: notificationId
                })
            });

            const result = await response.json();

            if (result.code === 200) {
                this.showSuccess('通知任务重试成功');
                this.refreshNotifications();
            } else {
                throw new Error(result.message || '重试通知任务失败');
            }
        } catch (error) {
            console.error('重试通知任务失败:', error);
            this.showError('重试通知任务失败: ' + error.message);
        }
    }

    showLoadingState() {
        const container = document.getElementById('notificationTableContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-3">正在加载通知任务数据...</div>
                </div>
            `;
        }
    }

    showErrorState(message) {
        const container = document.getElementById('notificationTableContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="text-danger">
                        <i class="bi bi-exclamation-triangle fa-3x mb-3"></i>
                        <h5>加载失败</h5>
                        <p>${message}</p>
                        <button class="btn btn-primary" onclick="window.notificationManager.refreshNotifications()">重试</button>
                    </div>
                </div>
            `;
        }
    }

    renderPagination(pagination) {
        const container = document.getElementById('notificationPagination');
        if (!container || !pagination.total_pages || pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }

        const currentPage = pagination.current_page || 1;
        const totalPages = pagination.total_pages;
        const maxVisiblePages = 5;

        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage < maxVisiblePages - 1) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        let paginationHtml = `
            <nav aria-label="通知任务分页">
                <ul class="pagination pagination-sm">
        `;

        // 上一页
        if (currentPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.notificationManager.loadNotifications(${currentPage - 1}); return false;">&laquo;</a>
                </li>
            `;
        }

        // 页码
        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="window.notificationManager.loadNotifications(${i}); return false;">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.notificationManager.loadNotifications(${currentPage + 1}); return false;">&raquo;</a>
                </li>
            `;
        }

        paginationHtml += `
                </ul>
            </nav>
        `;

        container.innerHTML = paginationHtml;
    }

    showSuccess(message) {
        console.log('Success:', message);
        // TODO: 实现更好的提示组件
    }

    showError(message) {
        console.error('Error:', message);
        // TODO: 实现更好的提示组件
    }

    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
}


// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = NotificationManager;
} else if (typeof window !== "undefined") {
    window.NotificationManager = NotificationManager;
} 