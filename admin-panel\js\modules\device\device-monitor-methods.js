/**
 * 设备监控模块 - 方法实现部分
 * 扩展DeviceMonitor类的功能方法
 */

// 扩展DeviceMonitor类的方法
Object.assign(DeviceMonitor.prototype, {
    /**
     * 绑定事件监听
     */
    bindEvents() {
        // 监控控制按钮
        document.getElementById('startMonitoring')?.addEventListener('click', () => {
            this.startMonitoring();
        });

        document.getElementById('pauseMonitoring')?.addEventListener('click', () => {
            this.pauseMonitoring();
        });

        document.getElementById('refreshMonitor')?.addEventListener('click', () => {
            this.refreshMonitorData();
        });

        // 清除告警
        document.getElementById('clearAlerts')?.addEventListener('click', () => {
            this.clearAllAlerts();
        });

        // 筛选器
        document.getElementById('statusFilter')?.addEventListener('change', (e) => {
            this.filterDevices('status', e.target.value);
        });

        document.getElementById('groupFilter')?.addEventListener('change', (e) => {
            this.filterDevices('group', e.target.value);
        });
    },

    /**
     * 开始监控
     */
    async startMonitoring() {
        if (this.isMonitoring) return;

        try {
            this.isMonitoring = true;
            
            // 更新按钮状态
            document.getElementById('startMonitoring').style.display = 'none';
            document.getElementById('pauseMonitoring').style.display = 'inline-block';
            
            // 立即加载一次数据
            await this.refreshMonitorData();
            
            // 设置定时刷新
            this.refreshTimer = setInterval(() => {
                this.refreshMonitorData();
            }, this.refreshInterval);
            
            this.showSuccess('监控已启动');
            console.log('✅ 设备监控已启动');
        } catch (error) {
            console.error('启动监控失败:', error);
            this.showError('启动监控失败');
        }
    },

    /**
     * 暂停监控
     */
    pauseMonitoring() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;
        
        // 清除定时器
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
        
        // 更新按钮状态
        document.getElementById('startMonitoring').style.display = 'inline-block';
        document.getElementById('pauseMonitoring').style.display = 'none';
        
        this.showInfo('监控已暂停');
        console.log('⏸️ 设备监控已暂停');
    },

    /**
     * 刷新监控数据
     */
    async refreshMonitorData() {
        try {
            console.log('🔄 刷新监控数据...');
            
            // 加载设备监控数据
            const response = await this.makeApiRequest('/monitor.php?action=realtime_status', 'GET');
            
            if (response.error_code === 0) {
                this.monitoringData = response.data;
                
                // 更新统计数据
                this.updateStatistics();
                
                // 更新设备列表
                this.updateDeviceList();
                
                // 检查告警
                this.checkAlerts();
                
                console.log('✅ 监控数据刷新完成');
            } else {
                throw new Error(response.error_message);
            }
        } catch (error) {
            console.error('刷新监控数据失败:', error);
            this.showError('刷新监控数据失败: ' + error.message);
        }
    },

    /**
     * 更新统计数据
     */
    updateStatistics() {
        const stats = this.monitoringData.statistics || {};
        
        document.getElementById('onlineDevicesCount').textContent = stats.online || 0;
        document.getElementById('offlineDevicesCount').textContent = stats.offline || 0;
        document.getElementById('warningDevicesCount').textContent = stats.warning || 0;
        document.getElementById('errorDevicesCount').textContent = stats.error || 0;
    },

    /**
     * 更新设备列表
     */
    updateDeviceList() {
        const devices = this.monitoringData.devices || [];
        const tbody = document.getElementById('monitorTableBody');
        
        if (!tbody) return;
        
        if (devices.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center text-muted">
                        <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                        <div>暂无设备监控数据</div>
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = devices.map(device => `
            <tr>
                <td>
                    <span class="device-id">${device.device_id}</span>
                </td>
                <td>
                    <div class="device-name">${device.device_name || '-'}</div>
                    <small class="text-muted">${device.group_name || '未分组'}</small>
                </td>
                <td>
                    <span class="status-badge status-${device.online_status}">${this.getOnlineStatusText(device.online_status)}</span>
                </td>
                <td>
                    <div class="performance-indicators">
                        ${this.renderPerformanceIndicators(device.performance)}
                    </div>
                </td>
                <td>
                    <div class="health-status">
                        <div class="health-icon health-${device.health_status}"></div>
                        <span>${this.getHealthStatusText(device.health_status)}</span>
                    </div>
                </td>
                <td>${this.formatDateTime(device.last_activity)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="deviceMonitor.viewDeviceDetail('${device.device_id}')" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="deviceMonitor.pingDevice('${device.device_id}')" title="Ping测试">
                        <i class="bi bi-wifi"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    },

    /**
     * 渲染性能指标
     */
    renderPerformanceIndicators(performance) {
        if (!performance) return '<span class="text-muted">-</span>';
        
        const indicators = [];
        
        // CPU使用率
        if (performance.cpu !== undefined) {
            const cpuClass = performance.cpu > this.alertThresholds.cpuHigh ? 'critical' : 
                           performance.cpu > 70 ? 'warning' : 'good';
            indicators.push(`<div class="performance-indicator ${cpuClass}">CPU ${performance.cpu}%</div>`);
        }
        
        // 内存使用率
        if (performance.memory !== undefined) {
            const memClass = performance.memory > this.alertThresholds.memoryHigh ? 'critical' : 
                           performance.memory > 60 ? 'warning' : 'good';
            indicators.push(`<div class="performance-indicator ${memClass}">MEM ${performance.memory}%</div>`);
        }
        
        // 电池电量
        if (performance.battery !== undefined) {
            const battClass = performance.battery < this.alertThresholds.batteryLow ? 'critical' : 
                            performance.battery < 40 ? 'warning' : 'good';
            indicators.push(`<div class="performance-indicator ${battClass}">BAT ${performance.battery}%</div>`);
        }
        
        return indicators.join('');
    },

    /**
     * 检查告警
     */
    checkAlerts() {
        const devices = this.monitoringData.devices || [];
        const newAlerts = [];
        
        devices.forEach(device => {
            // 离线告警
            if (device.online_status === 'offline') {
                const offlineTime = this.calculateOfflineTime(device.last_activity);
                if (offlineTime > this.alertThresholds.offlineTimeout) {
                    newAlerts.push({
                        id: `offline_${device.device_id}`,
                        type: 'error',
                        device_id: device.device_id,
                        device_name: device.device_name,
                        message: `设备已离线 ${Math.floor(offlineTime / 60)} 分钟`,
                        timestamp: new Date()
                    });
                }
            }
            
            // 性能告警
            if (device.performance) {
                const perf = device.performance;
                
                if (perf.cpu > this.alertThresholds.cpuHigh) {
                    newAlerts.push({
                        id: `cpu_${device.device_id}`,
                        type: 'warning',
                        device_id: device.device_id,
                        device_name: device.device_name,
                        message: `CPU使用率过高: ${perf.cpu}%`,
                        timestamp: new Date()
                    });
                }
                
                if (perf.memory > this.alertThresholds.memoryHigh) {
                    newAlerts.push({
                        id: `memory_${device.device_id}`,
                        type: 'warning',
                        device_id: device.device_id,
                        device_name: device.device_name,
                        message: `内存使用率过高: ${perf.memory}%`,
                        timestamp: new Date()
                    });
                }
                
                if (perf.battery < this.alertThresholds.batteryLow) {
                    newAlerts.push({
                        id: `battery_${device.device_id}`,
                        type: 'warning',
                        device_id: device.device_id,
                        device_name: device.device_name,
                        message: `电池电量不足: ${perf.battery}%`,
                        timestamp: new Date()
                    });
                }
            }
        });
        
        // 更新告警列表
        this.updateAlerts(newAlerts);
    },

    /**
     * 更新告警列表
     */
    updateAlerts(newAlerts) {
        // 去重和合并告警
        const alertMap = new Map();
        
        // 保留现有告警
        this.alerts.forEach(alert => {
            alertMap.set(alert.id, alert);
        });
        
        // 添加新告警
        newAlerts.forEach(alert => {
            alertMap.set(alert.id, alert);
        });
        
        this.alerts = Array.from(alertMap.values());
        
        // 显示/隐藏告警面板
        const alertPanel = document.getElementById('alertPanel');
        const alertList = document.getElementById('alertList');
        
        if (this.alerts.length > 0) {
            alertPanel.style.display = 'block';
            alertList.innerHTML = this.alerts.map(alert => `
                <div class="alert-item ${alert.type}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <strong>${alert.device_name}</strong> (${alert.device_id})
                            <div class="text-muted">${alert.message}</div>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">${this.formatDateTime(alert.timestamp)}</small>
                            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="deviceMonitor.dismissAlert('${alert.id}')">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        } else {
            alertPanel.style.display = 'none';
        }
    },

    /**
     * 查看设备详情
     */
    async viewDeviceDetail(deviceId) {
        try {
            const response = await this.makeApiRequest(`/monitor.php?action=device_detail&device_id=${deviceId}`, 'GET');
            
            if (response.error_code === 0) {
                this.renderDeviceDetail(response.data);
                const modal = new bootstrap.Modal(document.getElementById('deviceDetailModal'));
                modal.show();
            } else {
                this.showError('获取设备详情失败: ' + response.error_message);
            }
        } catch (error) {
            console.error('获取设备详情失败:', error);
            this.showError('获取设备详情失败');
        }
    },

    /**
     * 渲染设备详情
     */
    renderDeviceDetail(deviceData) {
        const container = document.getElementById('deviceDetailContent');
        if (!container) return;
        
        container.innerHTML = `
            <div class="device-detail-tabs">
                <ul class="nav nav-tabs" id="deviceDetailTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">基本信息</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">性能监控</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">活动日志</button>
                    </li>
                </ul>
                <div class="tab-content" id="deviceDetailTabContent">
                    <div class="tab-pane fade show active" id="basic" role="tabpanel">
                        ${this.renderBasicInfo(deviceData)}
                    </div>
                    <div class="tab-pane fade" id="performance" role="tabpanel">
                        ${this.renderPerformanceChart(deviceData)}
                    </div>
                    <div class="tab-pane fade" id="logs" role="tabpanel">
                        ${this.renderActivityLogs(deviceData)}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * 工具方法
     */
    getOnlineStatusText(status) {
        const statusMap = {
            'online': '在线',
            'offline': '离线',
            'warning': '告警',
            'error': '故障'
        };
        return statusMap[status] || '未知';
    },

    getHealthStatusText(status) {
        const statusMap = {
            'good': '良好',
            'warning': '警告',
            'critical': '严重'
        };
        return statusMap[status] || '未知';
    },

    calculateOfflineTime(lastActivity) {
        if (!lastActivity) return 0;
        const now = new Date();
        const last = new Date(lastActivity);
        return (now.getTime() - last.getTime()) / 1000; // 返回秒数
    },

    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    },

    /**
     * API请求方法
     */
    async makeApiRequest(url, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
            }
        };

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(this.apiBase + url, options);
        return await response.json();
    },

    /**
     * 消息提示方法
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    },

    showError(message) {
        this.showToast(message, 'error');
    },

    showInfo(message) {
        this.showToast(message, 'info');
    },

    showToast(message, type) {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }
});

// 全局实例
window.DeviceMonitor = DeviceMonitor;

if (typeof window !== 'undefined') {
    window.deviceMonitor = new DeviceMonitor();
} 