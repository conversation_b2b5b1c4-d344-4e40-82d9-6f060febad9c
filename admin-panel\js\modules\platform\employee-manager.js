/**
 * 员工管理器
 * 从admin.js第9525-10282行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class EmployeeManager {
    constructor() {
        this.employees = [];
        this.jobPositions = [];
        this.currentPage = 1;
        this.totalPages = 1;
        this.filters = {
            search: '',
            job_position_id: '',
            status: ''
        };
        this.initRetryCount = 0;
    }
    
    /**
     * 路由渲染方法 - 兼容路由管理器
     */
    render(container) {
        console.log('📄 渲染员工管理页面');
        
        // 确保container是DOM元素而不是字符串
        if (typeof container === 'string' || !container || !container.innerHTML) {
            console.log('📦 容器参数无效，自动获取主内容区域');
            container = document.querySelector('.main-content') || 
                       document.getElementById('main-content') || 
                       document.querySelector('#content-area') ||
                       document.querySelector('.content-wrapper') ||
                       document.body;
        }
        
        console.log('📦 使用容器:', container);
        
        // 首先创建HTML模板
        this.createHTMLTemplate(container);
        
        // 等待DOM更新后再初始化
        setTimeout(() => this.initialize(), 50);
        return this;
    }
    
    /**
     * 创建员工管理页面的HTML模板
     */
    createHTMLTemplate(container) {
        // 设置页面标题
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            pageTitle.textContent = '员工管理';
        }
        
        container.innerHTML = `
        <div class="employee-management">
        <!-- 页面头部 -->
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h2 class="mb-1">员工管理</h2>
        <p class="text-muted mb-0">管理我的员工账户、职位和权限</p>
        </div>
        <div>
        <button class="btn btn-primary" onclick="window.employeeManager.showCreateModal()">
        <i class="bi bi-person-plus me-2"></i>添加员工
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.employeeManager.refreshEmployees()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 统计卡片 -->
        <div class="stats-section" id="employeeStats">
        <div class="row">
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number" id="totalEmployees">-</div>
        <div class="stat-label">总员工数</div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number text-success" id="activeEmployees">-</div>
        <div class="stat-label">活跃员工</div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number text-warning" id="inactiveEmployees">-</div>
        <div class="stat-label">非活跃员工</div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number text-danger" id="suspendedEmployees">-</div>
        <div class="stat-label">已暂停</div>
        </div>
        </div>
        </div>
        </div>
        <!-- 筛选控制栏 -->
        <div class="controls-section">
        <div class="card">
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-2">
        <label class="form-label">职位筛选</label>
        <select class="form-select" id="employeeJobFilter">
        <option value="">全部职位</option>
        </select>
        </div>
        <div class="col-md-2">
        <label class="form-label">状态筛选</label>
        <select class="form-select" id="employeeStatusFilter">
        <option value="">全部状态</option>
        <option value="active">活跃</option>
        <option value="inactive">非活跃</option>
        <option value="suspended">已暂停</option>
        </select>
        </div>
        <div class="col-md-4">
        <label class="form-label">搜索</label>
        <div class="input-group">
        <input type="text" class="form-control" id="employeeSearchInput" placeholder="搜索员工姓名、用户名、邮箱...">
        <button class="btn btn-outline-secondary" onclick="window.employeeManager.searchEmployees()">
        <i class="bi bi-search"></i>
        </button>
        </div>
        </div>
        <div class="col-md-2">
        <label class="form-label">&nbsp;</label>
        <div class="d-grid">
        <button class="btn btn-primary" onclick="window.employeeManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>筛选
        </button>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 员工列表 -->
        <div class="employees-section">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-person-badge me-2"></i>员工列表
        <span class="badge bg-secondary ms-2" id="employeeCount">0</span>
        </h5>
        </div>
        <div class="card-body">
        <div id="employeesTableContainer">
        <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载员工数据...</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 分页 -->
        <div class="pagination-section" id="employeePagination">
        <!-- 分页控件将在这里动态生成 -->
        </div>
        </div>
        
        <!-- 员工详情/编辑模态框 -->
        <div class="modal fade" id="employeeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title" id="employeeModalTitle">员工详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="employeeForm">
        <input type="hidden" id="employeeId">
        <!-- 基本信息 -->
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">用户名 *</label>
        <input type="text" class="form-control" id="employeeUsername" required autocomplete="username">
        </div>
        </div>
        
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">登录密码</label>
        <input type="password" class="form-control" id="employeePassword" placeholder="留空表示不修改" autocomplete="new-password">
        <div class="form-text">创建时必填，编辑时留空表示不修改</div>
        </div>
        <div class="col-md-6">
        <label class="form-label">职位</label>
        <select class="form-select" id="employeeJobPosition">
        <option value="">请选择职位</option>
        </select>
        </div>
        </div>
        
        <!-- 职位和状态 -->
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">登录状态</label>
        <select class="form-select" id="employeeUserStatus">
        <option value="active">正常</option>
        <option value="disabled">禁用</option>
        </select>
        </div>
        <div class="col-md-6">
        <label class="form-label">员工状态</label>
        <select class="form-select" id="employeeStatus">
        <option value="active">在职</option>
        <option value="inactive">离职</option>
        <option value="resigned">辞职</option>
        </select>
        </div>
        </div>
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" id="employeeSaveBtn" onclick="window.employeeManager.saveEmployee()">保存</button>
        </div>
        </div>
        </div>
        </div>
        
        <!-- 权限管理模态框 -->
        <div class="modal fade" id="permissionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">权限管理</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <input type="hidden" id="permissionEmployeeId">
        <div id="permissionsList">
        <div class="text-center py-3">
        <div class="spinner-border" role="status">
        <span class="visually-hidden">加载权限...</span>
        </div>
        </div>
        </div>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.employeeManager.savePermissions()">
        <span id="permissionSaveText">保存权限</span>
        <span class="spinner-border spinner-border-sm ms-2 d-none" id="permissionSaveSpinner"></span>
        </button>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    
    initialize() {
        // 添加重试计数器，避免无限循环
        if (!this.initRetryCount) {
            this.initRetryCount = 0;
        }
        
        this.initRetryCount++;
        
        // 最大重试20次（2秒），避免无限循环
        if (this.initRetryCount > 20) {
            console.error('📦 员工管理初始化失败：达到最大重试次数，DOM元素始终未准备好');
            console.error('📦 当前页面DOM结构:', document.body.innerHTML.substring(0, 500));
            this.showErrorState('页面初始化失败，请刷新页面重试');
            return;
        }
        
        // 等待DOM元素准备好
        const employeesContainer = document.getElementById('employeesTableContainer');
        if (!employeesContainer) {
            console.warn(`📦 员工管理DOM未准备好，延迟100ms重试 (${this.initRetryCount}/20)`);
            console.log('📦 查找的元素ID: employeesTableContainer');
            console.log('📦 当前DOM中的主要容器:', {
                'contentArea': !!document.getElementById('contentArea'),
                'main-content': !!document.querySelector('.main-content'),
                'content-area': !!document.querySelector('.content-area'),
                'employee-management': !!document.querySelector('.employee-management')
            });
            setTimeout(() => this.initialize(), 100);
            return;
        }
        
        console.log('✅ 员工管理DOM准备完成，开始绑定事件和加载数据');
        this.initRetryCount = 0; // 重置计数器
        this.bindEvents();
        this.loadEmployees();
    }
    bindEvents() {
        // 搜索事件
        const searchInput = document.getElementById('employeeSearchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchEmployees();
                }
            });
        }
        // 筛选事件
        const statusFilter = document.getElementById('employeeStatusFilter');
        const jobFilter = document.getElementById('employeeJobFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.filters.status = statusFilter.value;
                this.applyFilters();
            });
        }
        if (jobFilter) {
            jobFilter.addEventListener('change', () => {
                this.filters.job_position_id = jobFilter.value;
                this.applyFilters();
            });
        }
    }
    async loadEmployees(page = 1) {
        try {
            this.currentPage = page;
            // 构建查询参数
            const params = new URLSearchParams({
                page: page,
                limit: 20,
                ...this.filters
            });
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=employees&${params}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem(CONFIG.TOKEN_KEY)}`,
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();
            
            if (result.code === 200 && result.data) {
                this.employees = result.data.employees || [];
                this.jobPositions = result.data.job_positions || [];
                
                // 安全地访问分页信息
                if (result.data.pagination) {
                    this.totalPages = result.data.pagination.total_pages || 1;
                    this.renderPagination(result.data.pagination);
                    this.updateEmployeeCount(result.data.pagination.total || 0);
                } else {
                    this.totalPages = 1;
                    this.updateEmployeeCount(this.employees.length);
                }
                
                this.updateStatsCards();
                this.renderEmployeesTable(this.employees);
                this.updateJobPositionFilter();
            } else {
                throw new Error(result.message || '获取员工列表失败');
            }
        } catch (error) {
            console.error('加载员工失败:', error);
            this.showErrorState('加载员工数据失败: ' + error.message);
        }
    }
    updateStatsCards() {
        const total = this.employees.length;
        const active = this.employees.filter(e => e.employee_status === 'active').length;
        const inactive = this.employees.filter(e => e.employee_status === 'inactive').length;
        const resigned = this.employees.filter(e => e.employee_status === 'resigned').length;
        
        document.getElementById('totalEmployees').textContent = total;
        document.getElementById('activeEmployees').textContent = active;
        document.getElementById('inactiveEmployees').textContent = inactive;
        document.getElementById('suspendedEmployees').textContent = resigned;
    }
    renderEmployeesTable(employees) {
        const container = document.getElementById('employeesTableContainer');
        if (employees.length === 0) {
            container.innerHTML = `
            <div class="text-center py-4">
            <i class="bi bi-person-x text-muted" style="font-size: 3rem;
            "></i>
            <p class="text-muted mt-2">暂无员工数据</p>
            <button class="btn btn-primary" onclick="window.employeeManager.showCreateModal()">
            <i class="bi bi-person-plus me-2"></i>添加第一个员工
            </button>
            </div>
            `;
            return;
        }
        let tableHtml = `
        <div class="table-responsive">
        <table class="table table-hover">
        <thead class="table-light">
        <tr>
        <th>员工信息</th>
        <th>职位</th>
        <th>登录状态</th>
        <th>员工状态</th>
        <th>最后登录</th>
        <th>创建时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        `;
        employees.forEach(employee => {
            tableHtml += this.renderEmployeeRow(employee);
        });
        tableHtml += `
        </tbody>
        </table>
        </div>
        `;
        container.innerHTML = tableHtml;
    }
    renderEmployeeRow(employee) {
        const statusBadges = {
            'active': 'bg-success',
            'inactive': 'bg-warning',
            'disabled': 'bg-danger',
            'resigned': 'bg-secondary'
        };
        
        const statusTexts = {
            'active': '正常',
            'inactive': '离职', 
            'disabled': '禁用',
            'resigned': '辞职'
        };
        
        const userStatusBadges = {
            'active': 'bg-success',
            'disabled': 'bg-danger'
        };
        
        const userStatusTexts = {
            'active': '正常',
            'disabled': '禁用'
        };
        
        const lastLogin = employee.last_login ? 
            this.formatDateTime(employee.last_login) : 
            '<span class="text-muted">从未登录</span>';
            
        return `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                            ${employee.username.charAt(0).toUpperCase()}
                        </div>
                        <div>
                            <div class="fw-medium">@${employee.username}</div>
                        </div>
                    </div>
                </td>
                <td>
                    ${employee.job_position_name ? 
                        `<span class="badge bg-info">${employee.job_position_name}</span>` : 
                        '<span class="text-muted">未分配</span>'
                    }
                </td>
                <td>
                    <span class="badge ${userStatusBadges[employee.user_status] || 'bg-secondary'}">
                        ${userStatusTexts[employee.user_status] || employee.user_status}
                    </span>
                </td>
                <td>
                    <span class="badge ${statusBadges[employee.employee_status] || 'bg-secondary'}">
                        ${statusTexts[employee.employee_status] || employee.employee_status}
                    </span>
                </td>
                <td>
                    <small>${lastLogin}</small>
                </td>
                <td>
                    <small>${this.formatDateTime(employee.user_created_at)}</small>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" 
                                onclick="window.employeeManager.showEditModal(${employee.user_id})"
                                title="编辑员工">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" 
                                onclick="window.employeeManager.showPermissionModal(${employee.user_id})"
                                title="管理权限">
                            <i class="bi bi-shield-check"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" 
                                onclick="window.employeeManager.deleteEmployee(${employee.user_id})"
                                title="删除员工">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }
    renderPagination(pagination) {
        const container = document.getElementById('employeePagination');
        const {
            current_page,
            total_pages,
            total
        } = pagination;
        if (total_pages <= 1) {
            container.innerHTML = '';
            return;
        }
        let paginationHtml = `
        <div class="d-flex justify-content-between align-items-center">
        <div class="text-muted">
        显示第 ${
            ((current_page - 1) * 20) + 1
        } - ${
            Math.min(current_page * 20,
            total)
        } 条，共 ${
            total
        } 条记录
        </div>
        <nav>
        <ul class="pagination mb-0">
        `;
        // 上一页
        paginationHtml += `
        <li class="page-item ${
            current_page === 1 ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.employeeManager.loadEmployees(${
            current_page - 1
        })">
        上一页
        </a>
        </li>
        `;
        // 页码
        const startPage = Math.max(1,
        current_page - 2);
        const endPage = Math.min(total_pages,
        current_page + 2);
        if (startPage > 1) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.employeeManager.loadEmployees(1)">1</a>
            </li>
            `;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        for (let i = startPage;
        i <= endPage;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === current_page ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.employeeManager.loadEmployees(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        if (endPage < total_pages) {
            if (endPage < total_pages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.employeeManager.loadEmployees(${
                total_pages
            })">${
                total_pages
            }</a>
            </li>
            `;
        }
        // 下一页
        paginationHtml += `
        <li class="page-item ${
            current_page === total_pages ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.employeeManager.loadEmployees(${
            current_page + 1
        })">
        下一页
        </a>
        </li>
        `;
        paginationHtml += `
        </ul>
        </nav>
        </div>
        `;
        container.innerHTML = paginationHtml;
    }
    updateJobPositionFilter() {
        const select = document.getElementById('employeeJobFilter');
        if (!select) return;
        // 清空现有选项（保留"全部职位"）
        select.innerHTML = '<option value="">全部职位</option>';
        // 添加职位选项
        this.jobPositions.forEach(position => {
            const option = document.createElement('option');
            option.value = position.id;
            option.textContent = position.position_name;
            select.appendChild(option);
        });
    }
    updateEmployeeCount(count) {
        const countElement = document.getElementById('employeeCount');
        if (countElement) {
            countElement.textContent = count;
        }
    }
    searchEmployees() {
        const searchInput = document.getElementById('employeeSearchInput');
        this.filters.search = searchInput.value.trim();
        this.applyFilters();
    }
    applyFilters() {
        this.currentPage = 1;
        this.loadEmployees();
    }
    refreshEmployees() {
        this.loadEmployees(this.currentPage);
    }
    showCreateModal() {
        // 重置表单
        document.getElementById('employeeForm').reset();
        document.getElementById('employeeId').value = '';
        
        // 设置模态框标题
        document.getElementById('employeeModalTitle').textContent = '添加员工';
        
        // 创建时密码字段必填
        document.getElementById('employeePassword').setAttribute('required', 'required');
        
        // 填充职位选项
        this.fillJobPositionOptions();
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
        modal.show();
    }
    showEditModal(userId) {
        const employee = this.employees.find(e => e.user_id == userId);
        if (!employee) {
            this.showError('找不到员工信息');
            return;
        }
        
        // 填充表单数据
        document.getElementById('employeeModalTitle').textContent = '编辑员工';
        document.getElementById('employeeId').value = employee.user_id;
        document.getElementById('employeeUsername').value = employee.username;
        document.getElementById('employeeJobPosition').value = employee.job_position_id || '';
        document.getElementById('employeeUserStatus').value = employee.user_status;
        document.getElementById('employeeStatus').value = employee.employee_status;
        
        // 编辑时密码字段为可选
        document.getElementById('employeePassword').removeAttribute('required');
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
        modal.show();
    }
    fillJobPositionOptions() {
        const select = document.getElementById('employeeJobPosition');
        if (!select) return;
        // 清空现有选项
        select.innerHTML = '<option value="">请选择职位</option>';
        // 添加职位选项
        this.jobPositions.forEach(position => {
            const option = document.createElement('option');
            option.value = position.id;
            option.textContent = position.position_name;
            if (position.description) {
                option.title = position.description;
            }
            select.appendChild(option);
        });
    }
    async loadEmployeePermissions(employeeId) {
        const container = document.getElementById('employeePermissions');
        try {
            container.innerHTML = `
            <div class="text-center py-3">
            <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">加载权限...</span>
            </div>
            </div>
            `;
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=permissions&employee_id=${
                employeeId
            }`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`,
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderPermissions(result.data.permissions,
                result.data.employee_permissions || []);
            } else {
                throw new Error(result.message || '加载权限失败');
            }
        } catch (error) {
            console.error('加载权限失败:',
            error);
            container.innerHTML = `
            <div class="alert alert-danger">
            加载权限失败: ${
                error.message
            }
            </div>
            `;
        }
    }
    renderPermissions(permissions,
    employeePermissions) {
        const container = document.getElementById('employeePermissions');
        let html = '';
        Object.keys(permissions).forEach(module => {
            html += `
            <div class="mb-3">
            <h6 class="text-primary">${
                module
            }</h6>
            <div class="row">
            `;
            permissions[module].forEach(permission => {
                const isChecked = employeePermissions.includes(permission.id);
                html += `
                <div class="col-md-6 mb-2">
                <div class="form-check">
                <input class="form-check-input" type="checkbox"
                id="perm_${
                    permission.id
                }"
                value="${
                    permission.id
                }"
                ${
                    isChecked ? 'checked' : ''
                }>
                <label class="form-check-label" for="perm_${
                    permission.id
                }">
                ${
                    permission.permission_name
                }
                ${
                    permission.description ? `<small class="text-muted d-block">${
                        permission.description
                    }</small>` : ''
                }
                </label>
                </div>
                </div>
                `;
            });
            html += `
            </div>
            </div>
            `;
        });
        if (!html) {
            html = '<div class="text-muted">暂无可用权限</div>';
        }
        container.innerHTML = html;
    }
    async saveEmployee() {
        const saveBtn = document.getElementById('employeeSaveBtn');
        const originalText = saveBtn.textContent;
        
        try {
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
            
            const userId = document.getElementById('employeeId').value;
            const isEdit = userId && userId !== '';
            
            const formData = {
                username: document.getElementById('employeeUsername').value,
                job_position_id: parseInt(document.getElementById('employeeJobPosition').value) || null,
                user_status: document.getElementById('employeeUserStatus').value,
                employee_status: document.getElementById('employeeStatus').value
            };
            
            // 密码处理
            const password = document.getElementById('employeePassword').value;
            if (password) {
                formData.password = password;
            } else if (!isEdit) {
                // 创建时密码必填
                this.showError('创建员工时密码不能为空');
                return;
            }
            
            if (isEdit) {
                formData.user_id = parseInt(userId);
            }
            
            const url = `${CONFIG.API_BASE_URL}/admin.php?action=employees`;
            const method = isEdit ? 'PUT' : 'POST';
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem(CONFIG.TOKEN_KEY)}`
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                this.showSuccess(result.message || '员工保存成功');
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('employeeModal'));
                modal.hide();
                
                // 重新加载员工列表
                await this.loadEmployees(this.currentPage);
                
                // 清空表单
                document.getElementById('employeeForm').reset();
                document.getElementById('employeeId').value = '';
                
            } else {
                this.showError(result.message || '保存失败');
            }
            
        } catch (error) {
            console.error('保存员工失败:', error);
            this.showError('保存失败: ' + error.message);
        } finally {
            saveBtn.disabled = false;
            saveBtn.textContent = originalText;
        }
    }
    showPermissionModal(employeeId) {
        const employee = this.employees.find(e => e.id == employeeId);
        if (!employee) {
            this.showError('找不到员工信息');
            return;
        }
        document.getElementById('permissionEmployeeId').value = employeeId;
        // 加载权限列表
        this.loadPermissionsForModal(employeeId);
        const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
        modal.show();
    }
    async loadPermissionsForModal(employeeId) {
        const container = document.getElementById('permissionsList');
        try {
            container.innerHTML = `
            <div class="text-center py-3">
            <div class="spinner-border" role="status">
            <span class="visually-hidden">加载权限...</span>
            </div>
            </div>
            `;
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=permissions&employee_id=${
                employeeId
            }`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`,
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderPermissionsForModal(result.data.permissions,
                result.data.employee_permissions || []);
            } else {
                throw new Error(result.message || '加载权限失败');
            }
        } catch (error) {
            console.error('加载权限失败:',
            error);
            container.innerHTML = `
            <div class="alert alert-danger">
            加载权限失败: ${
                error.message
            }
            </div>
            `;
        }
    }
    renderPermissionsForModal(permissions,
    employeePermissions) {
        const container = document.getElementById('permissionsList');
        let html = '';
        Object.keys(permissions).forEach(module => {
            html += `
            <div class="card mb-3">
            <div class="card-header">
            <h6 class="mb-0 text-primary">${
                module
            }</h6>
            </div>
            <div class="card-body">
            <div class="row">
            `;
            permissions[module].forEach(permission => {
                const isChecked = employeePermissions.includes(permission.id);
                html += `
                <div class="col-md-6 mb-2">
                <div class="form-check">
                <input class="form-check-input" type="checkbox"
                id="modal_perm_${
                    permission.id
                }"
                value="${
                    permission.id
                }"
                ${
                    isChecked ? 'checked' : ''
                }>
                <label class="form-check-label" for="modal_perm_${
                    permission.id
                }">
                ${
                    permission.permission_name
                }
                ${
                    permission.description ? `<small class="text-muted d-block">${
                        permission.description
                    }</small>` : ''
                }
                </label>
                </div>
                </div>
                `;
            });
            html += `
            </div>
            </div>
            </div>
            `;
        });
        if (!html) {
            html = '<div class="text-muted">暂无可用权限</div>';
        }
        container.innerHTML = html;
    }
    async savePermissions() {
        const saveBtn = document.getElementById('permissionSaveText');
        const saveSpinner = document.getElementById('permissionSaveSpinner');
        const employeeId = document.getElementById('permissionEmployeeId').value;
        try {
            saveBtn.textContent = '保存中...';
            saveSpinner.classList.remove('d-none');
            // 收集权限
            const permissions = [];
            const permissionCheckboxes = document.querySelectorAll('#permissionsList input[type="checkbox"]:checked');
            permissionCheckboxes.forEach(checkbox => {
                permissions.push(parseInt(checkbox.value));
            });
            const formData = {
                action: 'update_permissions',
                employee_id: parseInt(employeeId),
                permissions: permissions
            };
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=employees`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess(result.message || '权限更新成功');
                bootstrap.Modal.getInstance(document.getElementById('permissionModal')).hide();
            } else {
                throw new Error(result.message || '权限更新失败');
            }
        } catch (error) {
            console.error('保存权限失败:',
            error);
            this.showError('保存权限失败: ' + error.message);
        } finally {
            saveBtn.textContent = '保存权限';
            saveSpinner.classList.add('d-none');
        }
    }
    async deleteEmployee(userId) {
        const employee = this.employees.find(e => e.user_id == userId);
        if (!employee) {
            this.showError('找不到员工信息');
            return;
        }
        
        if (!confirm(`确定要删除员工 "${employee.real_name || employee.username}" 吗？\n\n注意：删除后无法恢复，请谨慎操作！`)) {
            return;
        }
        
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=employees`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem(CONFIG.TOKEN_KEY)}`
                },
                body: JSON.stringify({ user_id: userId })
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                this.showSuccess('员工删除成功');
                await this.loadEmployees(this.currentPage);
            } else {
                this.showError(result.message || '删除失败');
            }
            
        } catch (error) {
            console.error('删除员工失败:', error);
            this.showError('删除失败: ' + error.message);
        }
    }
    showErrorState(message) {
        const container = document.getElementById('employeesTableContainer');
        if (!container) {
            console.error('📦 员工表格容器未找到，无法显示错误状态');
            return;
        }
        
        container.innerHTML = `
        <div class="text-center py-4">
        <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
        <p class="text-danger mt-2">${message}</p>
        <button class="btn btn-primary" onclick="window.employeeManager.refreshEmployees()">
        <i class="bi bi-arrow-clockwise me-2"></i>重试
        </button>
        </div>
        `;
    }
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    showSuccess(message) {
        // 这里可以使用toast或其他通知组件
        alert('成功：' + message);
    }
    showError(message) {
        // 这里可以使用toast或其他通知组件
        alert('错误：' + message);
    }
}

// 创建实例并导出
const employeeManager = new EmployeeManager();

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = employeeManager;
} else if (typeof window !== "undefined") {
    window.EmployeeManager = employeeManager;
    window.employeeManager = employeeManager; // 兼容性别名
}

console.log('📦 EmployeeManager 模块加载完成');
