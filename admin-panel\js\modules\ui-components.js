/**
 * ⚠️ 此文件已过期 - DEPRECATED
 * 
 * 原因：功能已迁移到 core/ui-manager.js
 * 当前架构：AppInitializer → UIManager (core/ui-manager.js)
 * 
 * 此文件保留仅为防止引用错误，所有功能已在新架构中实现
 * 
 * @deprecated 请使用 core/ui-manager.js 替代
 * @since 2024-01-20
 */

console.warn('⚠️ ui-components.js 已过期，请使用 core/ui-manager.js');

// 空的兼容性导出，防止引用错误
window.UIComponentsModule = {
    deprecated: true,
    message: '此模块已过期，请使用 core/ui-manager.js'
};

// 兼容性UIManager类（空实现）
class UIManager {
    constructor() {
        console.warn('⚠️ ui-components.js中的UIManager已过期，请使用core/ui-manager.js');
    }
    
    renderMainInterface() {
        throw new Error('此方法已过期，请使用core/ui-manager.js中的UIManager');
    }
}

window.UIManager = window.UIManager || UIManager;