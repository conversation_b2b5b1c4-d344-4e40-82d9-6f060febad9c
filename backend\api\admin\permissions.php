<?php
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

$auth = TenantAuth::init();
if (!$auth->requireAdmin()) exit;
$db = $auth->getDatabase();

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'permissions':
            handleGetPermissions();
            break;
        case 'permission_groups':
            handleGetPermissionGroups();
            break;
        default:
            $auth->sendError('接口不存在', 404);
    }
} catch (Exception $e) {
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 获取所有权限列表
function handleGetPermissions() {
    global $auth;
    
    if (!$auth->checkPermission('permission_management')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    // 定义系统权限结构
    $permissions = array(
        'user_management' => array(
            'name' => '用户管理',
            'description' => '管理系统用户',
            'children' => array(
                'user_create' => array('name' => '创建用户', 'description' => '创建新用户'),
                'user_update' => array('name' => '更新用户', 'description' => '修改用户信息'),
                'user_delete' => array('name' => '删除用户', 'description' => '删除用户'),
                'user_view' => array('name' => '查看用户', 'description' => '查看用户列表和详情')
            )
        ),
        'merchant_management' => array(
            'name' => '商户管理',
            'description' => '管理商户信息',
            'children' => array(
                'merchant_create' => array('name' => '创建商户', 'description' => '创建新商户'),
                'merchant_update' => array('name' => '更新商户', 'description' => '修改商户信息'),
                'merchant_delete' => array('name' => '删除商户', 'description' => '删除商户'),
                'merchant_view' => array('name' => '查看商户', 'description' => '查看商户列表和详情'),
                'merchant_api_management' => array('name' => 'API密钥管理', 'description' => '管理商户API密钥')
            )
        ),
        'device_management' => array(
            'name' => '设备管理',
            'description' => '管理支付设备',
            'children' => array(
                'device_create' => array('name' => '创建设备', 'description' => '添加新设备'),
                'device_update' => array('name' => '更新设备', 'description' => '修改设备信息'),
                'device_delete' => array('name' => '删除设备', 'description' => '删除设备'),
                'device_view' => array('name' => '查看设备', 'description' => '查看设备列表和状态')
            )
        ),
        'payment_management' => array(
            'name' => '支付管理',
            'description' => '管理支付订单',
            'children' => array(
                'payment_view' => array('name' => '查看支付', 'description' => '查看支付订单'),
                'payment_update' => array('name' => '更新支付', 'description' => '修改支付状态'),
                'payment_retry' => array('name' => '重试支付', 'description' => '重新处理支付'),
                'payment_refund' => array('name' => '退款处理', 'description' => '处理退款申请')
            )
        ),
        'financial_management' => array(
            'name' => '财务管理',
            'description' => '管理财务数据',
            'children' => array(
                'financial_view' => array('name' => '查看财务', 'description' => '查看财务报表'),
                'financial_export' => array('name' => '导出财务', 'description' => '导出财务数据'),
                'settlement_management' => array('name' => '结算管理', 'description' => '管理商户结算')
            )
        ),
        'risk_management' => array(
            'name' => '风控管理',
            'description' => '管理风险控制',
            'children' => array(
                'risk_config' => array('name' => '风控配置', 'description' => '配置风控规则'),
                'blacklist_management' => array('name' => '黑名单管理', 'description' => '管理黑名单'),
                'risk_monitoring' => array('name' => '风险监控', 'description' => '监控风险事件')
            )
        ),
        'system_management' => array(
            'name' => '系统管理',
            'description' => '系统设置和监控',
            'children' => array(
                'permission_management' => array('name' => '权限管理', 'description' => '管理系统权限'),
                'security_logs' => array('name' => '安全日志', 'description' => '查看安全日志'),
                'performance_monitor' => array('name' => '性能监控', 'description' => '监控系统性能'),
                'system_config' => array('name' => '系统配置', 'description' => '修改系统配置')
            )
        )
    );
    
    $auth->sendSuccess($permissions, '获取成功');
}

// 获取权限分组
function handleGetPermissionGroups() {
    global $auth;
    
    if (!$auth->checkPermission('permission_management')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    // 定义权限分组模板
    $permissionGroups = array(
        'super_admin' => array(
            'name' => '超级管理员',
            'description' => '拥有所有权限',
            'permissions' => array('*') // 通配符表示所有权限
        ),
        'admin' => array(
            'name' => '管理员',
            'description' => '拥有大部分管理权限',
            'permissions' => array(
                'user_management', 'user_create', 'user_update', 'user_view',
                'merchant_management', 'merchant_create', 'merchant_update', 'merchant_view', 'merchant_api_management',
                'device_management', 'device_create', 'device_update', 'device_view',
                'payment_management', 'payment_view', 'payment_update', 'payment_retry',
                'financial_management', 'financial_view', 'financial_export',
                'risk_management', 'risk_config', 'blacklist_management', 'risk_monitoring'
            )
        ),
        'operator' => array(
            'name' => '操作员',
            'description' => '日常操作权限',
            'permissions' => array(
                'user_view',
                'merchant_view',
                'device_view', 'device_update',
                'payment_view', 'payment_update',
                'financial_view',
                'risk_monitoring'
            )
        ),
        'viewer' => array(
            'name' => '查看员',
            'description' => '只读权限',
            'permissions' => array(
                'user_view',
                'merchant_view',
                'device_view',
                'payment_view',
                'financial_view'
            )
        ),
        'financial' => array(
            'name' => '财务员',
            'description' => '财务相关权限',
            'permissions' => array(
                'merchant_view',
                'payment_view',
                'financial_management', 'financial_view', 'financial_export', 'settlement_management'
            )
        ),
        'risk_control' => array(
            'name' => '风控员',
            'description' => '风控相关权限',
            'permissions' => array(
                'merchant_view',
                'payment_view',
                'risk_management', 'risk_config', 'blacklist_management', 'risk_monitoring',
                'security_logs'
            )
        )
    );
    
    $auth->sendSuccess($permissionGroups, '获取成功');
}

// 验证权限数组
function validatePermissions($permissions) {
    // 获取所有可用权限
    $allPermissions = getAllPermissionKeys();
    
    foreach ($permissions as $permission) {
        if ($permission === '*') {
            continue; // 通配符权限
        }
        
        if (!in_array($permission, $allPermissions)) {
            return "无效的权限: {$permission}";
        }
    }
    
    return null;
}

// 获取所有权限键名
function getAllPermissionKeys() {
    $permissions = array();
    
    // 从权限定义中提取所有权限键名
    $permissionStructure = array(
        'user_management', 'user_create', 'user_update', 'user_delete', 'user_view',
        'merchant_management', 'merchant_create', 'merchant_update', 'merchant_delete', 'merchant_view', 'merchant_api_management',
        'device_management', 'device_create', 'device_update', 'device_delete', 'device_view',
        'payment_management', 'payment_view', 'payment_update', 'payment_retry', 'payment_refund',
        'financial_management', 'financial_view', 'financial_export', 'settlement_management',
        'risk_management', 'risk_config', 'blacklist_management', 'risk_monitoring',
        'system_management', 'permission_management', 'security_logs', 'performance_monitor', 'system_config'
    );
    
    return $permissionStructure;
}
?> 