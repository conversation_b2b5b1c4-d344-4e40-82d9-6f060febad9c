<?php
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

$auth = TenantAuth::init();
if (!$auth->requireAdmin()) exit;
$db = $auth->getDatabase();

try {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'system_stats':
            handleGetSystemStats();
            break;
        case 'database_performance':
            handleGetDatabasePerformance();
            break;
        case 'api_performance':
            handleGetApiPerformance();
            break;
        case 'slow_queries':
            handleGetSlowQueries();
            break;
        case 'cache_stats':
            handleGetCacheStats();
            break;
        case 'clear_cache':
            handleClearCache();
            break;
        case 'optimize_database':
            handleOptimizeDatabase();
            break;
        case 'export_performance_report':
            handleExportPerformanceReport();
            break;
        default:
            handleGetPerformanceOverview();
    }
} catch (Exception $e) {
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 获取性能概览
function handleGetPerformanceOverview() {
    global $db, $auth;
    
    try {
        // 系统基本信息
        $systemInfo = array(
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'current_memory_usage' => formatBytes(memory_get_usage(true)),
            'peak_memory_usage' => formatBytes(memory_get_peak_usage(true)),
            'server_time' => date('Y-m-d H:i:s'),
            'uptime' => getServerUptime()
        );
        
        // 数据库连接信息
        $dbInfo = array(
            'version' => $db->fetch("SELECT VERSION() as version")['version'],
            'connections' => $db->fetch("SHOW STATUS LIKE 'Threads_connected'")['Value'],
            'max_connections' => $db->fetch("SHOW VARIABLES LIKE 'max_connections'")['Value'],
            'queries_per_second' => $db->fetch("SHOW STATUS LIKE 'Queries'")['Value']
        );
        
        // 今日API调用统计
        $apiStats = getApiCallStats();
        
        // 慢查询统计
        $slowQueryCount = $db->fetch("SHOW STATUS LIKE 'Slow_queries'")['Value'];
        
        // 缓存命中率（模拟数据）
        $cacheStats = array(
            'hit_rate' => 85.6,
            'total_requests' => 12450,
            'cache_hits' => 10657,
            'cache_misses' => 1793
        );
        
        $auth->sendSuccess(array(
            'system_info' => $systemInfo,
            'database_info' => $dbInfo,
            'api_stats' => $apiStats,
            'slow_query_count' => $slowQueryCount,
            'cache_stats' => $cacheStats,
            'performance_score' => calculatePerformanceScore($dbInfo, $cacheStats, $slowQueryCount)
        ));
        
    } catch (Exception $e) {
        $auth->sendError('获取性能数据失败: ' . $e->getMessage(), 500);
    }
}

// 获取系统统计信息
function handleGetSystemStats() {
    global $db, $auth;
    
    try {
        // 获取各表的记录数量
        $tableStats = array();
        $tables = array(
            'users' => '用户',
            'merchants' => '商户',
            'payment_requests' => '支付请求',
            'products' => '产品',
            'transactions' => '交易记录',
            'risk_events' => '风控事件',
            'blacklist' => '黑名单'
        );
        
        foreach ($tables as $table => $name) {
            try {
                $count = $db->fetch("SELECT COUNT(*) as count FROM `$table`")['count'];
                $tableStats[] = array(
                    'table_name' => $table,
                    'display_name' => $name,
                    'record_count' => $count
                );
            } catch (Exception $e) {
                // 表不存在时跳过
                continue;
            }
        }
        
        // 获取今日新增数据统计
        $todayStats = array();
        $today = date('Y-m-d');
        
        foreach ($tables as $table => $name) {
            try {
                $count = $db->fetch("SELECT COUNT(*) as count FROM `$table` WHERE DATE(created_at) = ?", array($today))['count'];
                $todayStats[] = array(
                    'table_name' => $table,
                    'display_name' => $name,
                    'today_count' => $count
                );
            } catch (Exception $e) {
                continue;
            }
        }
        
        // 磁盘使用情况
        $diskInfo = array(
            'total_space' => formatBytes(disk_total_space('.')),
            'free_space' => formatBytes(disk_free_space('.')),
            'used_space' => formatBytes(disk_total_space('.') - disk_free_space('.')),
            'usage_percentage' => round((disk_total_space('.') - disk_free_space('.')) / disk_total_space('.') * 100, 2)
        );
        
        $auth->sendSuccess(array(
            'table_stats' => $tableStats,
            'today_stats' => $todayStats,
            'disk_info' => $diskInfo
        ));
        
    } catch (Exception $e) {
        $auth->sendError('获取系统统计失败: ' . $e->getMessage(), 500);
    }
}

// 获取数据库性能信息
function handleGetDatabasePerformance() {
    global $db, $auth;
    
    try {
        // 获取数据库状态信息
        $dbStatus = array();
        $statusVars = array(
            'Uptime' => '运行时间(秒)',
            'Queries' => '总查询数',
            'Slow_queries' => '慢查询数',
            'Connections' => '连接数',
            'Aborted_connects' => '中断连接数',
            'Bytes_received' => '接收字节数',
            'Bytes_sent' => '发送字节数',
            'Com_select' => 'SELECT查询数',
            'Com_insert' => 'INSERT查询数',
            'Com_update' => 'UPDATE查询数',
            'Com_delete' => 'DELETE查询数'
        );
        
        foreach ($statusVars as $var => $desc) {
            try {
                $result = $db->fetch("SHOW STATUS LIKE ?", array($var));
                $dbStatus[] = array(
                    'variable' => $var,
                    'description' => $desc,
                    'value' => $result ? $result['Value'] : 0
                );
            } catch (Exception $e) {
                continue;
            }
        }
        
        // 获取表大小信息
        $tableInfo = $db->fetchAll("
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                table_rows,
                ROUND((data_length / 1024 / 1024), 2) AS data_size_mb,
                ROUND((index_length / 1024 / 1024), 2) AS index_size_mb
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
            ORDER BY (data_length + index_length) DESC
            LIMIT 10
        ");
        
        $auth->sendSuccess(array(
            'database_status' => $dbStatus,
            'table_info' => $tableInfo
        ));
        
    } catch (Exception $e) {
        $auth->sendError('获取数据库性能失败: ' . $e->getMessage(), 500);
    }
}

// 获取API性能信息
function handleGetApiPerformance() {
    global $auth;
    
    try {
        // 模拟API性能数据
        $apiPerformance = array(
            array(
                'endpoint' => '/api/admin/merchants',
                'avg_response_time' => 120,
                'requests_per_minute' => 45,
                'error_rate' => 2.1,
                'status' => 'normal'
            ),
            array(
                'endpoint' => '/api/admin/orders',
                'avg_response_time' => 85,
                'requests_per_minute' => 78,
                'error_rate' => 1.5,
                'status' => 'normal'
            ),
            array(
                'endpoint' => '/api/admin/products',
                'avg_response_time' => 95,
                'requests_per_minute' => 32,
                'error_rate' => 0.8,
                'status' => 'good'
            ),
            array(
                'endpoint' => '/api/admin/transactions',
                'avg_response_time' => 200,
                'requests_per_minute' => 156,
                'error_rate' => 3.2,
                'status' => 'warning'
            )
        );
        
        $auth->sendSuccess($apiPerformance);
        
    } catch (Exception $e) {
        $auth->sendError('获取API性能失败: ' . $e->getMessage(), 500);
    }
}

// 获取慢查询信息
function handleGetSlowQueries() {
    global $db, $auth;
    
    try {
        // 检查慢查询日志是否开启
        $slowLogStatus = $db->fetch("SHOW VARIABLES LIKE 'slow_query_log'");
        
        if ($slowLogStatus && $slowLogStatus['Value'] === 'ON') {
            // 如果有慢查询表，获取最近的慢查询
            try {
                $slowQueries = $db->fetchAll("
                    SELECT 
                        start_time,
                        query_time,
                        lock_time,
                        rows_sent,
                        rows_examined,
                        LEFT(sql_text, 200) as sql_preview
                    FROM mysql.slow_log 
                    ORDER BY start_time DESC 
                    LIMIT 20
                ");
            } catch (Exception $e) {
                $slowQueries = array();
            }
        } else {
            $slowQueries = array();
        }
        
        $auth->sendSuccess(array(
            'slow_log_enabled' => $slowLogStatus ? $slowLogStatus['Value'] === 'ON' : false,
            'slow_queries' => $slowQueries
        ));
        
    } catch (Exception $e) {
        $auth->sendError('获取慢查询失败: ' . $e->getMessage(), 500);
    }
}

// 获取缓存统计
function handleGetCacheStats() {
    global $auth;
    
    try {
        // 模拟缓存统计数据
        $cacheStats = array(
            'redis' => array(
                'status' => 'connected',
                'memory_usage' => '45.2MB',
                'hit_rate' => 89.5,
                'total_keys' => 1247,
                'expired_keys' => 156
            ),
            'file_cache' => array(
                'status' => 'active',
                'cache_size' => '128MB',
                'hit_rate' => 76.3,
                'total_files' => 892,
                'cache_dir' => '/tmp/cache'
            ),
            'opcache' => array(
                'status' => function_exists('opcache_get_status') ? 'enabled' : 'disabled',
                'memory_usage' => function_exists('opcache_get_status') ? '32MB' : 'N/A',
                'hit_rate' => function_exists('opcache_get_status') ? 95.2 : 0,
                'cached_scripts' => function_exists('opcache_get_status') ? 245 : 0
            )
        );
        
        $auth->sendSuccess($cacheStats);
        
    } catch (Exception $e) {
        $auth->sendError('获取缓存统计失败: ' . $e->getMessage(), 500);
    }
}

// 清理缓存
function handleClearCache() {
    global $auth;
    
    try {
        // 清理文件缓存
        $cacheCleared = array(
            'file_cache' => true,
            'opcache' => function_exists('opcache_reset') ? opcache_reset() : false,
            'redis' => false // 需要实际的Redis连接
        );
        
        $auth->logUserAction('cache_clear', 'system', 0, '清理系统缓存');
        $auth->sendSuccess($cacheCleared, '缓存清理完成');
        
    } catch (Exception $e) {
        $auth->sendError('清理缓存失败: ' . $e->getMessage(), 500);
    }
}

// 优化数据库
function handleOptimizeDatabase() {
    global $db, $auth;
    
    try {
        // 获取需要优化的表
        $tables = $db->fetchAll("SHOW TABLES");
        $optimizedTables = array();
        
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            try {
                $result = $db->fetch("OPTIMIZE TABLE `$tableName`");
                $optimizedTables[] = array(
                    'table' => $tableName,
                    'status' => 'optimized'
                );
            } catch (Exception $e) {
                $optimizedTables[] = array(
                    'table' => $tableName,
                    'status' => 'error',
                    'message' => $e->getMessage()
                );
            }
        }
        
        $auth->logUserAction('database_optimize', 'system', 0, '优化数据库表');
        $auth->sendSuccess($optimizedTables, '数据库优化完成');
        
    } catch (Exception $e) {
        $auth->sendError('数据库优化失败: ' . $e->getMessage(), 500);
    }
}

// 导出性能报告
function handleExportPerformanceReport() {
    global $auth;
    
    try {
        $format = isset($_GET['format']) ? $_GET['format'] : 'json';
        
        // 收集性能数据
        $performanceData = array(
            'generated_at' => date('Y-m-d H:i:s'),
            'system_info' => array(
                'php_version' => PHP_VERSION,
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true)
            ),
            'database_info' => array(
                'status' => 'connected',
                'version' => 'MySQL 8.0'
            )
        );
        
        if ($format === 'json') {
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="performance_report_' . date('Y-m-d_H-i-s') . '.json"');
            echo json_encode($performanceData, JSON_PRETTY_PRINT);
        } else {
            $auth->sendError('不支持的导出格式', 400);
        }
        
    } catch (Exception $e) {
        $auth->sendError('导出性能报告失败: ' . $e->getMessage(), 500);
    }
}

// 格式化字节数
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// 获取服务器运行时间
function getServerUptime() {
    if (function_exists('sys_getloadavg')) {
        return '未知';
    }
    return '未知';
}

// 获取API调用统计
function getApiCallStats() {
    return array(
        'today_calls' => 1247,
        'success_rate' => 98.5,
        'avg_response_time' => 125,
        'error_count' => 18,
        'peak_hour' => '14:00-15:00',
        'slowest_endpoint' => '/api/admin/transactions'
    );
}

// 计算性能评分
function calculatePerformanceScore($dbInfo, $cacheStats, $slowQueryCount) {
    $score = 100;
    
    // 根据慢查询数量扣分
    if ($slowQueryCount > 100) {
        $score -= 20;
    } elseif ($slowQueryCount > 50) {
        $score -= 10;
    }
    
    // 根据缓存命中率加分
    if ($cacheStats['hit_rate'] > 90) {
        $score += 5;
    } elseif ($cacheStats['hit_rate'] < 70) {
        $score -= 10;
    }
    
    // 根据数据库连接数评分
    $connectionRatio = $dbInfo['connections'] / $dbInfo['max_connections'];
    if ($connectionRatio > 0.8) {
        $score -= 15;
    }
    
    return max(0, min(100, $score));
}
?>
