/*!
 * PayPal管理系统 - 完整CSS样式
 * 包含Bootstrap核心组件和自定义样式
 */

/* CSS Reset */
*, *::before, *::after {
    box-sizing: border-box;
}

/* 基础样式 */
html {
    font-size: 16px;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
}

body {
    margin: 0 !important;
    padding: 0 !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529 !important;
    background-color: #f8f9fa !important;
    text-align: left;
}

/* 强制文字颜色 */
* {
    color: inherit;
}

h1, h2, h3, h4, h5, h6 {
    color: #212529 !important;
    font-weight: 600;
}

p, span, div, td, th, li, a {
    color: #212529 !important;
}

/* 登录模式背景 */
body.login-mode {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* 链接样式 */
a {
    color: #0d6efd;
    text-decoration: none;
}

a:hover {
    color: #0a58ca;
    text-decoration: underline;
}

/* 容器系统 */
.container, .container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

.container {
    max-width: 1140px;
}

.container-fluid {
    max-width: none;
}

/* 应用容器 */
#appContainer {
    display: none;
    min-height: 100vh;
    background-color: #f8f9fa !important;
}

#loginContainer {
    display: none;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* 网格系统 */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col, .col-12, .col-md-6, .col-lg-4, .col-lg-3, .col-lg-8, .col-lg-9 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

.col { flex-basis: 0; flex-grow: 1; max-width: 100%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-md-6 { flex: 0 0 50%; max-width: 50%; }
.col-lg-3 { flex: 0 0 25%; max-width: 25%; }
.col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-lg-9 { flex: 0 0 75%; max-width: 75%; }

/* Buttons */
.btn {
    display: inline-block;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-success {
    color: #fff;
    background-color: #198754;
    border-color: #198754;
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Forms */
.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: #212529;
    background-color: #fff;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group {
    margin-bottom: 1rem;
}

/* Cards */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.375rem;
}

.card-body {
    flex: 1 1 auto;
    padding: 1rem;
}

.card-header {
    padding: 0.5rem 1rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-title {
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
    font-weight: 500;
}

/* Alerts */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alert-success {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
}

.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

.alert-warning {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
}

.alert-info {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
}

/* Utilities */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.py-5 { padding-top: 3rem !important; padding-bottom: 3rem !important; }

/* 按钮样式 */
.btn {
    display: inline-block;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* 警告框样式 */
.alert {
    position: relative;
    padding: 1rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alert-info {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
}

.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

/* 文本颜色 */
.text-muted {
    color: #6c757d !important;
}

.text-warning {
    color: #ffc107 !important;
}

/* 导航栏样式 */
.navbar {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    background-color: #fff !important;
    border-bottom: 1px solid #dee2e6;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.navbar-brand {
    display: inline-block;
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
    line-height: inherit;
    white-space: nowrap;
    color: #212529;
    text-decoration: none;
}

.navbar-brand:hover {
    color: #212529;
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    flex-direction: row;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

.navbar-nav .nav-link {
    display: block;
    padding: 0.5rem 1rem;
    color: #495057;
    text-decoration: none;
    transition: color 0.15s ease-in-out;
}

.navbar-nav .nav-link:hover {
    color: #0d6efd;
}

.navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
}

.ms-auto {
    margin-left: auto !important;
}

/* 下拉菜单样式 */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    white-space: nowrap;
}

.dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.375rem 1rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

.dropdown-item:hover {
    color: #1e2125;
    background-color: #e9ecef;
}

.dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #dee2e6;
}

/* 侧边栏样式 */
.sidebar {
    position: relative;
    background-color: #f8f9fa !important;
    border-right: 1px solid #dee2e6 !important;
    min-height: calc(100vh - 56px);
    width: 250px;
    flex-shrink: 0;
}

.sidebar .nav {
    flex-direction: column;
}

.sidebar .nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #212529 !important;
    text-decoration: none;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
    transition: all 0.15s ease-in-out;
}

.sidebar .nav-link:hover {
    background-color: #e9ecef;
    color: #212529 !important;
}

.sidebar .nav-link.active {
    background-color: #0d6efd;
    color: white !important;
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    width: 1rem;
    text-align: center;
    font-size: 1rem;
}

/* 主内容区域 */
.flex-grow-1 {
    flex: 1 1 auto;
}

/* 布局修复 */
.d-flex {
    display: flex !important;
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.bg-white {
    background-color: #fff !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* 响应式 */
@media (max-width: 767.98px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .sidebar {
        width: 100% !important;
        min-height: auto;
    }

    .d-flex {
        flex-direction: column;
    }
}

@media (max-width: 991.98px) {
    .col-lg-4 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

/* 主内容区域样式 */
.main-content {
    flex: 1;
    padding: 1.5rem;
    background-color: #fff !important;
    color: #212529 !important;
    min-height: calc(100vh - 56px);
    overflow-y: auto;
}

.main-content * {
    color: #212529 !important;
}

.main-content .card {
    background-color: #fff !important;
    color: #212529 !important;
}

.main-content .card * {
    color: #212529 !important;
}

.content-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.content-header h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: #212529;
}

/* 应用布局 */
.app-layout {
    display: flex !important;
    min-height: 100vh;
    background-color: #f8f9fa;
}

.app-layout .sidebar {
    flex-shrink: 0;
    width: 250px;
}

.app-layout .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 表格样式 */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529 !important;
    vertical-align: top;
    border-color: #dee2e6;
}

.table * {
    color: #212529 !important;
}

.table th {
    color: #212529 !important;
    font-weight: 600;
    background-color: #f8f9fa !important;
}

.table td {
    color: #212529 !important;
    background-color: #fff !important;
}

.table > :not(caption) > * > * {
    padding: 0.5rem 0.5rem;
    background-color: var(--bs-table-bg);
    border-bottom-width: 1px;
    box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
}

.table > tbody {
    vertical-align: inherit;
}

.table > thead {
    vertical-align: bottom;
}

.table-striped > tbody > tr:nth-of-type(odd) > td,
.table-striped > tbody > tr:nth-of-type(odd) > th {
    --bs-table-accent-bg: rgba(0, 0, 0, 0.05);
}

.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th {
    --bs-table-accent-bg: rgba(0, 0, 0, 0.075);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    font-weight: 400;
    line-height: 1.5;
    color: #212529 !important;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn:hover {
    color: #212529;
}

.btn-primary {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-success {
    color: #fff;
    background-color: #198754;
    border-color: #198754;
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-warning {
    color: #000;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-info {
    color: #000;
    background-color: #0dcaf0;
    border-color: #0dcaf0;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

.btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.25rem;
    border-radius: 0.5rem;
}

/* 表单样式 */
.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529 !important;
    background-color: #fff !important;
    background-image: none;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: #212529;
    background-color: #fff;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-select {
    display: block;
    width: 100%;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group {
    margin-bottom: 1rem;
}

/* 卡片样式 */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.card-body {
    flex: 1 1 auto;
    padding: 1rem 1rem;
}

.card-header {
    padding: 0.5rem 1rem;
    margin-bottom: 0;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
}

.card-footer {
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: calc(0.375rem - 1px);
    border-bottom-left-radius: calc(0.375rem - 1px);
}

.card-title {
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
    font-weight: 500;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1055;
    display: none;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    outline: 0;
}

.modal-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.5rem - 1px);
    border-top-right-radius: calc(0.5rem - 1px);
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
}

.modal-footer {
    display: flex;
    flex-wrap: wrap;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: calc(0.5rem - 1px);
    border-bottom-left-radius: calc(0.5rem - 1px);
}

.modal-title {
    margin-bottom: 0;
    line-height: 1.5;
}

/* 工具类 */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-muted { color: #6c757d !important; }
.text-primary { color: #0d6efd !important; }
.text-success { color: #198754 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

.float-left { float: left !important; }
.float-right { float: right !important; }

.clearfix::after {
    display: block;
    clear: both;
    content: "";
}

/* 特殊修复 */
.navbar-toggler {
    padding: 0.25rem 0.75rem;
    font-size: 1.25rem;
    line-height: 1;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: 0.5;
}

.close:hover {
    color: #000;
    text-decoration: none;
    opacity: 0.75;
}

/* 确保布局正确 */
#appContainer {
    display: none;
    width: 100%;
    height: 100vh;
    background-color: #f8f9fa !important;
}

#appContainer.show {
    display: block !important;
}

#loginContainer {
    display: none;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

#loginContainer.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* 强制覆盖任何冲突样式 */
body:not(.login-mode) {
    background-color: #f8f9fa !important;
    background-image: none !important;
}

body:not(.login-mode) #appContainer {
    background-color: #f8f9fa !important;
    background-image: none !important;
}

/* 确保导航栏在顶部 */
.navbar {
    z-index: 1030;
    position: relative;
}

/* 确保侧边栏和主内容正确布局 */
.app-layout {
    width: 100% !important;
    height: 100vh !important;
    overflow: hidden;
}

.app-layout .sidebar {
    height: calc(100vh - 56px);
    overflow-y: auto;
}

.app-layout .main-content {
    height: calc(100vh - 56px);
    overflow-y: auto;
}

/* 全局文字颜色强制修复 */
#appContainer, #appContainer * {
    color: #212529 !important;
}

#mainContent, #mainContent * {
    color: #212529 !important;
}

.sidebar, .sidebar * {
    color: #212529 !important;
}

.navbar, .navbar * {
    color: #212529 !important;
}

/* 特殊元素颜色修复 */
.alert {
    color: #212529 !important;
}

.badge {
    color: #fff !important;
}

.text-muted {
    color: #6c757d !important;
}

.text-primary {
    color: #0d6efd !important;
}

.text-success {
    color: #198754 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}