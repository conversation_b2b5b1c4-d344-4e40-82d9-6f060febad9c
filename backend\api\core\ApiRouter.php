<?php
/**
 * API路由配置管理系统
 * 实现三层路由架构中的API路由层
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

class ApiRouter {
    
    private static $routes = [];
    private static $initialized = false;
    
    /**
     * 初始化API路由配置
     */
    public static function init() {
        if (self::$initialized) {
            return;
        }
        
        self::$initialized = true;
        self::initializeRoutes();
    }
    
    /**
     * 配置所有API路由
     */
    private static function initializeRoutes() {
        
        // ================================
        // 认证相关API
        // ================================
        self::addRoute('auth.login', [
            'method' => 'POST',
            'path' => '/admin/auth.php',
            'permissions' => ['public'],
            'description' => '用户登录',
            'rate_limit' => 60 // 每分钟最多60次
        ]);
        
        self::addRoute('auth.logout', [
            'method' => 'POST',
            'path' => '/admin/auth.php',
            'permissions' => ['authenticated'],
            'description' => '用户登出'
        ]);
        
        self::addRoute('auth.check', [
            'method' => 'GET',
            'path' => '/admin/auth.php',
            'permissions' => ['authenticated'],
            'description' => '验证登录状态'
        ]);
        
        // ================================
        // 仪表板API
        // ================================
        self::addRoute('dashboard.stats', [
            'method' => 'GET',
            'path' => '/admin/dashboard.php',
            'permissions' => ['authenticated'],
            'description' => '获取仪表板统计数据'
        ]);
        
        // ================================
        // 用户管理API（已整合到商户和码商管理）
        // ================================
        
        // ================================
        // 平台管理API（系统管理员专用）
        // ================================
        self::addRoute('platform.list', [
            'method' => 'GET',
            'path' => '/admin/platforms.php',
            'permissions' => ['system_admin'],
            'description' => '获取平台列表'
        ]);
        
        self::addRoute('platform.create', [
            'method' => 'POST',
            'path' => '/admin/platforms.php',
            'permissions' => ['system_admin'],
            'description' => '创建平台'
        ]);
        
        self::addRoute('platform.update', [
            'method' => 'PUT',
            'path' => '/admin/platforms.php',
            'permissions' => ['system_admin'],
            'description' => '更新平台信息'
        ]);
        
        self::addRoute('platform.delete', [
            'method' => 'DELETE',
            'path' => '/admin/platforms.php',
            'permissions' => ['system_admin'],
            'description' => '删除平台'
        ]);
        
        // ================================
        // 域名管理API（系统管理员专用）
        // ================================
        self::addRoute('domain.list', [
            'method' => 'GET',
            'path' => '/admin/domains.php',
            'permissions' => ['system_admin'],
            'description' => '获取域名列表'
        ]);
        
        self::addRoute('domain.create', [
            'method' => 'POST',
            'path' => '/admin/domains.php',
            'permissions' => ['system_admin'],
            'description' => '添加域名配置'
        ]);
        
        self::addRoute('domain.update', [
            'method' => 'PUT',
            'path' => '/admin/domains.php',
            'permissions' => ['system_admin'],
            'description' => '更新域名配置'
        ]);
        
        self::addRoute('domain.delete', [
            'method' => 'DELETE',
            'path' => '/admin/domains.php',
            'permissions' => ['system_admin'],
            'description' => '删除域名配置'
        ]);
        
        // ================================
        // 商户管理API
        // ================================
        self::addRoute('merchant.list', [
            'method' => 'GET',
            'path' => '/admin/merchants.php',
            'permissions' => ['platform_admin', 'system_admin'],
            'description' => '获取商户列表'
        ]);
        
        self::addRoute('merchant.detail', [
            'method' => 'GET',
            'path' => '/admin/merchants.php',
            'permissions' => ['platform_admin', 'system_admin', 'merchant'],
            'description' => '获取商户详情',
            'tenant_filter' => true
        ]);
        
        self::addRoute('merchant.create', [
            'method' => 'POST',
            'path' => '/admin/merchants.php',
            'permissions' => ['platform_admin', 'system_admin'],
            'description' => '创建商户'
        ]);
        
        self::addRoute('merchant.update', [
            'method' => 'PUT',
            'path' => '/admin/merchants.php',
            'permissions' => ['platform_admin', 'system_admin', 'merchant'],
            'description' => '更新商户信息',
            'tenant_filter' => true
        ]);
        
        self::addRoute('merchant.approve', [
            'method' => 'POST',
            'path' => '/admin/merchants.php',
            'permissions' => ['platform_admin', 'system_admin'],
            'description' => '审核商户'
        ]);
        
        self::addRoute('merchant.reject', [
            'method' => 'POST',
            'path' => '/admin/merchants.php',
            'permissions' => ['platform_admin', 'system_admin'],
            'description' => '拒绝商户'
        ]);
        
        self::addRoute('merchant.reset_password', [
            'method' => 'POST',
            'path' => '/admin/merchants.php',
            'permissions' => ['platform_admin', 'system_admin'],
            'description' => '重置商户密码'
        ]);
        
        // ================================
        // 码商管理API
        // ================================
        self::addRoute('provider.list', [
            'method' => 'GET',
            'path' => '/admin/providers.php',
            'permissions' => ['platform_admin', 'system_admin'],
            'description' => '获取码商列表'
        ]);
        
        self::addRoute('provider.detail', [
            'method' => 'GET',
            'path' => '/admin/providers.php',
            'permissions' => ['platform_admin', 'system_admin', 'provider'],
            'description' => '获取码商详情',
            'tenant_filter' => true
        ]);
        
        self::addRoute('provider.create', [
            'method' => 'POST',
            'path' => '/admin/providers.php',
            'permissions' => ['platform_admin', 'system_admin'],
            'description' => '创建码商'
        ]);
        
        self::addRoute('provider.update', [
            'method' => 'PUT',
            'path' => '/admin/providers.php',
            'permissions' => ['platform_admin', 'system_admin', 'provider'],
            'description' => '更新码商信息',
            'tenant_filter' => true
        ]);
        
        self::addRoute('provider.reset_password', [
            'method' => 'POST',
            'path' => '/admin/providers.php',
            'permissions' => ['platform_admin', 'system_admin'],
            'description' => '重置码商密码'
        ]);
        
        // ================================
        // 设备管理API
        // ================================
        self::addRoute('device.list', [
            'method' => 'GET',
            'path' => '/admin/devices.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '获取设备列表',
            'tenant_filter' => true
        ]);
        
        self::addRoute('device.create', [
            'method' => 'POST',
            'path' => '/admin/devices.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '添加设备'
        ]);
        
        self::addRoute('device.update', [
            'method' => 'PUT',
            'path' => '/admin/devices.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '更新设备信息',
            'tenant_filter' => true
        ]);
        
        self::addRoute('device.delete', [
            'method' => 'DELETE',
            'path' => '/admin/devices.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '删除设备',
            'tenant_filter' => true
        ]);
        
        // ================================
        // 订单管理API
        // ================================
        self::addRoute('order.list', [
            'method' => 'GET',
            'path' => '/admin/orders.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '获取订单列表',
            'tenant_filter' => true
        ]);
        
        self::addRoute('order.detail', [
            'method' => 'GET',
            'path' => '/admin/orders.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '获取订单详情',
            'tenant_filter' => true
        ]);
        
        self::addRoute('order.stats', [
            'method' => 'GET',
            'path' => '/admin/orders.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '获取订单统计',
            'tenant_filter' => true
        ]);
        
        // ================================
        // 财务管理API
        // ================================
        self::addRoute('finance.flow', [
            'method' => 'GET',
            'path' => '/admin/financial.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '获取资金流水',
            'tenant_filter' => true
        ]);
        
        self::addRoute('finance.settlement', [
            'method' => 'GET',
            'path' => '/admin/financial.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '获取结算记录',
            'tenant_filter' => true
        ]);
        
        self::addRoute('finance.reports', [
            'method' => 'GET',
            'path' => '/admin/financial.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '获取财务报表',
            'tenant_filter' => true
        ]);
        
        // ================================
        // 支付宝账单管理API（码商专用）
        // ================================
        self::addRoute('alipay_bills.list', [
            'method' => 'GET',
            'path' => '/alipay_bills.php',
            'permissions' => ['provider'],
            'description' => '获取支付宝账单列表',
            'tenant_filter' => true
        ]);
        
        self::addRoute('alipay_bills.stats', [
            'method' => 'GET',
            'path' => '/alipay_bills.php',
            'permissions' => ['provider'],
            'description' => '获取支付宝账单统计',
            'tenant_filter' => true
        ]);
        
        self::addRoute('alipay_bills.detail', [
            'method' => 'GET',
            'path' => '/alipay_bills.php',
            'permissions' => ['provider'],
            'description' => '获取支付宝账单详情',
            'tenant_filter' => true
        ]);
        
        self::addRoute('alipay_bills.manual_match', [
            'method' => 'POST',
            'path' => '/alipay_bills.php',
            'permissions' => ['provider'],
            'description' => '手动匹配账单',
            'tenant_filter' => true
        ]);
        
        self::addRoute('alipay_bills.ignore', [
            'method' => 'POST',
            'path' => '/alipay_bills.php',
            'permissions' => ['provider'],
            'description' => '忽略账单',
            'tenant_filter' => true
        ]);
        
        // ================================
        // 支付宝账户管理API
        // ================================
        self::addRoute('alipay_account.list', [
            'method' => 'GET',
            'path' => '/admin/payments.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '获取支付宝账户列表',
            'tenant_filter' => true
        ]);
        
        self::addRoute('alipay_account.create', [
            'method' => 'POST',
            'path' => '/admin/payments.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '添加支付宝账户'
        ]);
        
        self::addRoute('alipay_account.update', [
            'method' => 'PUT',
            'path' => '/admin/payments.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '更新支付宝账户',
            'tenant_filter' => true
        ]);
        
        self::addRoute('alipay_account.delete', [
            'method' => 'DELETE',
            'path' => '/admin/payments.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '删除支付宝账户',
            'tenant_filter' => true
        ]);
        
        // ================================
        // 产品管理API
        // ================================
        self::addRoute('product.list', [
            'method' => 'GET',
            'path' => '/admin/products.php',
            'permissions' => ['merchant', 'platform_admin', 'system_admin'],
            'description' => '获取产品列表',
            'tenant_filter' => true
        ]);
        
        self::addRoute('product.create', [
            'method' => 'POST',
            'path' => '/admin/products.php',
            'permissions' => ['merchant', 'platform_admin', 'system_admin'],
            'description' => '创建产品'
        ]);
        
        self::addRoute('product.update', [
            'method' => 'PUT',
            'path' => '/admin/products.php',
            'permissions' => ['merchant', 'platform_admin', 'system_admin'],
            'description' => '更新产品信息',
            'tenant_filter' => true
        ]);
        
        self::addRoute('product.delete', [
            'method' => 'DELETE',
            'path' => '/admin/products.php',
            'permissions' => ['merchant', 'platform_admin', 'system_admin'],
            'description' => '删除产品',
            'tenant_filter' => true
        ]);
        
        // ================================
        // API管理相关
        // ================================
        self::addRoute('api.key_info', [
            'method' => 'GET',
            'path' => '/admin/api_management.php',
            'permissions' => ['merchant'],
            'description' => '获取API密钥信息',
            'tenant_filter' => true
        ]);
        
        self::addRoute('api.regenerate_key', [
            'method' => 'POST',
            'path' => '/admin/api_management.php',
            'permissions' => ['merchant'],
            'description' => '重新生成API密钥',
            'tenant_filter' => true
        ]);
        
        self::addRoute('api.update_callback', [
            'method' => 'PUT',
            'path' => '/admin/api_management.php',
            'permissions' => ['merchant'],
            'description' => '更新回调URL',
            'tenant_filter' => true
        ]);
        
        self::addRoute('api.ip_whitelist', [
            'method' => 'GET',
            'path' => '/admin/api_management.php',
            'permissions' => ['merchant'],
            'description' => '获取IP白名单',
            'tenant_filter' => true
        ]);
        
        self::addRoute('api.update_ip_whitelist', [
            'method' => 'PUT',
            'path' => '/admin/api_management.php',
            'permissions' => ['merchant'],
            'description' => '更新IP白名单',
            'tenant_filter' => true
        ]);
        
        // ================================
        // 员工管理API
        // ================================
        self::addRoute('employee.list', [
            'method' => 'GET',
            'path' => '/admin/employees.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '获取员工列表',
            'tenant_filter' => true
        ]);
        
        self::addRoute('employee.create', [
            'method' => 'POST',
            'path' => '/admin/employees.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '添加员工'
        ]);
        
        self::addRoute('employee.update', [
            'method' => 'PUT',
            'path' => '/admin/employees.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '更新员工信息',
            'tenant_filter' => true
        ]);
        
        self::addRoute('employee.delete', [
            'method' => 'DELETE',
            'path' => '/admin/employees.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '删除员工',
            'tenant_filter' => true
        ]);
        
        // ================================
        // 职位管理API
        // ================================
        self::addRoute('job_position.list', [
            'method' => 'GET',
            'path' => '/admin/job_positions.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '获取职位列表',
            'tenant_filter' => true
        ]);
        
        self::addRoute('job_position.create', [
            'method' => 'POST',
            'path' => '/admin/job_positions.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '创建职位'
        ]);
        
        self::addRoute('job_position.update', [
            'method' => 'PUT',
            'path' => '/admin/job_positions.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '更新职位信息',
            'tenant_filter' => true
        ]);
        
        self::addRoute('job_position.delete', [
            'method' => 'DELETE',
            'path' => '/admin/job_positions.php',
            'permissions' => ['merchant', 'provider', 'platform_admin', 'system_admin'],
            'description' => '删除职位',
            'tenant_filter' => true
        ]);
        
        // ================================
        // 风控管理API
        // ================================
        self::addRoute('risk.blacklist', [
            'method' => 'GET',
            'path' => '/admin/risk_control.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '获取黑名单',
            'tenant_filter' => true
        ]);
        
        self::addRoute('risk.monitor', [
            'method' => 'GET',
            'path' => '/admin/risk_control.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '风险监控',
            'tenant_filter' => true
        ]);
        
        self::addRoute('risk.reports', [
            'method' => 'GET',
            'path' => '/admin/risk_control.php',
            'permissions' => ['provider', 'platform_admin', 'system_admin'],
            'description' => '风控报表',
            'tenant_filter' => true
        ]);
        
        // ================================
        // 系统管理API
        // ================================
        self::addRoute('system.monitoring', [
            'method' => 'GET',
            'path' => '/admin/performance_monitor.php',
            'permissions' => ['system_admin'],
            'description' => '系统监控'
        ]);
        
        self::addRoute('system.security_logs', [
            'method' => 'GET',
            'path' => '/admin/security_logs.php',
            'permissions' => ['system_admin'],
            'description' => '安全日志'
        ]);
        
        self::addRoute('system.scripts', [
            'method' => 'GET',
            'path' => '/admin/devices.php',
            'permissions' => ['system_admin'],
            'description' => '系统脚本管理'
        ]);
        
        self::addRoute('system.totp', [
            'method' => 'GET',
            'path' => '/admin/totp.php',
            'permissions' => ['system_admin'],
            'description' => 'TOTP管理'
        ]);
        
        // ================================
        // SQL路由管理API（系统管理员专用）
        // ================================
        self::addRoute('sql_router_get_queries', [
            'method' => 'GET',
            'path' => '/admin/sql_router.php',
            'permissions' => ['system_admin'],
            'description' => '获取SQL查询配置'
        ]);
        
        self::addRoute('sql_router_get_query_detail', [
            'method' => 'GET',
            'path' => '/admin/sql_router.php',
            'permissions' => ['system_admin'],
            'description' => '获取SQL查询详情'
        ]);
        
        self::addRoute('sql_router_get_query_stats', [
            'method' => 'GET',
            'path' => '/admin/sql_router.php',
            'permissions' => ['system_admin'],
            'description' => '获取SQL查询统计'
        ]);
        
        self::addRoute('sql_router_get_performance_report', [
            'method' => 'GET',
            'path' => '/admin/sql_router.php',
            'permissions' => ['system_admin'],
            'description' => '获取SQL性能报告'
        ]);
        
        self::addRoute('sql_router_clear_cache', [
            'method' => 'POST',
            'path' => '/admin/sql_router.php',
            'permissions' => ['system_admin'],
            'description' => '清空SQL缓存'
        ]);
        
        self::addRoute('sql_router_export_queries', [
            'method' => 'GET',
            'path' => '/admin/sql_router.php',
            'permissions' => ['system_admin'],
            'description' => '导出SQL查询配置'
        ]);
        
        self::addRoute('sql_router_get_sql_logs', [
            'method' => 'GET',
            'path' => '/admin/sql_router.php',
            'permissions' => ['system_admin'],
            'description' => '获取SQL查询日志'
        ]);
        
        // ================================
        // 平台配置API
        // ================================
        self::addRoute('config.platform', [
            'method' => 'GET',
            'path' => '/admin/config.php',
            'permissions' => ['platform_admin', 'system_admin'],
            'description' => '平台配置',
            'tenant_filter' => true
        ]);
        
        self::addRoute('config.system', [
            'method' => 'GET',
            'path' => '/admin/config.php',
            'permissions' => ['merchant', 'provider'],
            'description' => '系统配置',
            'tenant_filter' => true
        ]);
        
        error_log("API路由配置完成，共配置 " . count(self::$routes) . " 个路由");
    }
    
    /**
     * 添加路由配置
     */
    private static function addRoute($name, $config) {
        self::$routes[$name] = array_merge([
            'method' => 'GET',
            'path' => '',
            'permissions' => [],
            'description' => '',
            'tenant_filter' => false,
            'rate_limit' => null,
            'cache_ttl' => null
        ], $config);
    }
    
    /**
     * 获取路由配置
     */
    public static function getRoute($name) {
        self::init();
        return isset(self::$routes[$name]) ? self::$routes[$name] : null;
    }
    
    /**
     * 获取所有路由
     */
    public static function getAllRoutes() {
        self::init();
        return self::$routes;
    }
    
    /**
     * 根据权限获取可用路由
     */
    public static function getRoutesByPermission($userType) {
        self::init();
        $availableRoutes = [];
        
        foreach (self::$routes as $name => $config) {
            if (in_array($userType, $config['permissions']) || 
                in_array('authenticated', $config['permissions']) ||
                in_array('public', $config['permissions'])) {
                $availableRoutes[$name] = $config;
            }
        }
        
        return $availableRoutes;
    }
    
    /**
     * 验证路由权限
     */
    public static function checkPermission($routeName, $userType) {
        self::init();
        
        if (!isset(self::$routes[$routeName])) {
            return false;
        }
        
        $route = self::$routes[$routeName];
        
        return in_array($userType, $route['permissions']) || 
               in_array('authenticated', $route['permissions']) ||
               in_array('public', $route['permissions']);
    }
    
    /**
     * 获取路由统计信息
     */
    public static function getRouteStats() {
        self::init();
        
        $stats = [
            'total' => count(self::$routes),
            'by_permission' => [],
            'by_method' => [],
            'tenant_filtered' => 0
        ];
        
        foreach (self::$routes as $route) {
            // 按权限统计
            foreach ($route['permissions'] as $permission) {
                if (!isset($stats['by_permission'][$permission])) {
                    $stats['by_permission'][$permission] = 0;
                }
                $stats['by_permission'][$permission]++;
            }
            
            // 按方法统计
            if (!isset($stats['by_method'][$route['method']])) {
                $stats['by_method'][$route['method']] = 0;
            }
            $stats['by_method'][$route['method']]++;
            
            // 租户过滤统计
            if ($route['tenant_filter']) {
                $stats['tenant_filtered']++;
            }
        }
        
        return $stats;
    }
    
    /**
     * 路由解析器 - 将action转换为路由名称
     */
    public static function parseAction($action) {
        // 映射传统action到新路由名称
        $actionMap = [
            // 认证
            'login' => 'auth.login',
            'logout' => 'auth.logout',
            'check_auth' => 'auth.check',
            
            // 仪表板
            'dashboard' => 'dashboard.stats',
            'get_stats' => 'dashboard.stats',
            
            // 商户管理
            'get_merchants' => 'merchant.list',
            'merchants' => 'merchant.list',
            'get_merchant_user_detail' => 'merchant.detail',
            'create_merchant' => 'merchant.create',
            'update_merchant' => 'merchant.update',
            'approve_merchant' => 'merchant.approve',
            'reject_merchant' => 'merchant.reject',
            'reset_merchant_password' => 'merchant.reset_password',
            
            // 码商管理
            'get_providers' => 'provider.list',
            'providers' => 'provider.list',
            'get_provider_user_detail' => 'provider.detail',
            'add_provider' => 'provider.create',
            'update_provider' => 'provider.update',
            'reset_provider_password' => 'provider.reset_password',
            
            // 设备管理
            'get_devices' => 'device.list',
            'devices' => 'device.list',
            'add_device' => 'device.create',
            'edit_device' => 'device.update',
            'delete_device' => 'device.delete',
            
            // 订单管理
            'get_orders' => 'order.list',
            'orders' => 'order.list',
            'get_order_detail' => 'order.detail',
            
            // 财务管理
            'get_financial_data' => 'finance.flow',
            'financial' => 'finance.flow',
            
            // 产品管理
            'get_products' => 'product.list',
            'products' => 'product.list',
            
            // API管理
            'get_key_info' => 'api.key_info',
            'regenerate_api_key' => 'api.regenerate_key',
            'update_callback_url' => 'api.update_callback',
            'get_ip_whitelist' => 'api.ip_whitelist',
            'update_ip_whitelist' => 'api.update_ip_whitelist',
            
            // 员工管理
            'get_employees' => 'employee.list',
            'employees' => 'employee.list',
            
            // 职位管理
            'get_job_positions' => 'job_position.list',
            'job_positions' => 'job_position.list',
            
            // 支付宝账户
            'alipay_accounts' => 'alipay_account.list',
            'get_accounts' => 'alipay_account.list',
            
            // 平台管理
            'get_platforms_list' => 'platform.list',
            'add_platform' => 'platform.create',
            'edit_platform' => 'platform.update',
            'delete_platform' => 'platform.delete',
            
            // 域名管理
            'get_domains_list' => 'domain.list',
            'add_domain' => 'domain.create',
            'edit_domain' => 'domain.update',
            'delete_domain' => 'domain.delete'
        ];
        
        return isset($actionMap[$action]) ? $actionMap[$action] : null;
    }
}

// 初始化路由配置
ApiRouter::init();

error_log("ApiRouter 类加载完成");
?> 