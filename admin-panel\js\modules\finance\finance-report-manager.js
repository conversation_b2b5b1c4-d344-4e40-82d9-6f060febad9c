/**
 * 财务报表管理器 - 四层架构财务系统核心模块
 * 负责财务数据的报表生成、统计分析和可视化展示
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class FinanceReportManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        
        this.currentReport = null;
        this.reportTemplates = [];
        this.charts = {};
        
        console.log('📊 FinanceReportManager initialized for tenant:', this.tenantInfo?.tenant_type);
    }

    /**
     * 初始化财务报表管理器
     */
    async init(container) {
        try {
            this.container = container;
            await this.renderReportPage();
            await this.loadReportTemplates();
            this.bindEvents();
            
            console.log('✅ 财务报表管理器初始化完成');
        } catch (error) {
            console.error('❌ 财务报表管理器初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    /**
     * 渲染财务报表页面
     */
    async renderReportPage() {
        this.container.innerHTML = `
            <div class="finance-report-manager">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="bi bi-file-earmark-bar-graph me-2"></i>财务报表</h2>
                            <p class="text-muted mb-0">生成和管理各类财务报表，支持多维度数据分析</p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="financeReportManager.showCreateReportModal()">
                                <i class="bi bi-plus me-2"></i>创建报表
                            </button>
                            <button class="btn btn-success" onclick="financeReportManager.showTemplateModal()">
                                <i class="bi bi-file-earmark-text me-2"></i>报表模板
                            </button>
                            <button class="btn btn-outline-info" onclick="financeReportManager.showScheduleModal()">
                                <i class="bi bi-clock me-2"></i>定时报表
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 快速报表生成 -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-lightning me-2"></i>快速报表生成
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-2">
                                        <label class="form-label">报表类型</label>
                                        <select class="form-select" id="quickReportType">
                                            <option value="daily">日报</option>
                                            <option value="weekly">周报</option>
                                            <option value="monthly">月报</option>
                                            <option value="custom">自定义</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">数据范围</label>
                                        <select class="form-select" id="quickDataScope">
                                            <option value="all">全部数据</option>
                                            <option value="provider">码商数据</option>
                                            <option value="merchant">商户数据</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">开始日期</label>
                                        <input type="date" class="form-control" id="quickStartDate">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">结束日期</label>
                                        <input type="date" class="form-control" id="quickEndDate">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">格式</label>
                                        <select class="form-select" id="quickFormat">
                                            <option value="html">在线查看</option>
                                            <option value="excel">Excel</option>
                                            <option value="pdf">PDF</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">操作</label>
                                        <button class="btn btn-primary w-100" onclick="financeReportManager.generateQuickReport()">
                                            <i class="bi bi-play me-2"></i>生成
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 报表内容区域 -->
                <div class="row">
                    <div class="col-md-3">
                        <!-- 报表导航 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">报表导航</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush" id="reportNavigation">
                                    <a href="#" class="list-group-item list-group-item-action active" data-report="overview">
                                        <i class="bi bi-speedometer2 me-2"></i>财务概览
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" data-report="revenue">
                                        <i class="bi bi-graph-up me-2"></i>收入分析
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" data-report="transaction">
                                        <i class="bi bi-receipt me-2"></i>交易统计
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" data-report="settlement">
                                        <i class="bi bi-bank me-2"></i>结算报表
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" data-report="fee">
                                        <i class="bi bi-percent me-2"></i>手续费分析
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" data-report="provider">
                                        <i class="bi bi-building me-2"></i>码商报表
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" data-report="merchant">
                                        <i class="bi bi-shop me-2"></i>商户报表
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- 报表筛选 -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">数据筛选</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">时间范围</label>
                                    <select class="form-select" id="timeRange">
                                        <option value="today">今天</option>
                                        <option value="yesterday">昨天</option>
                                        <option value="week">本周</option>
                                        <option value="month">本月</option>
                                        <option value="quarter">本季度</option>
                                        <option value="year">本年</option>
                                        <option value="custom">自定义</option>
                                    </select>
                                </div>
                                <div class="mb-3" id="customDateRange" style="display: none;">
                                    <label class="form-label">开始日期</label>
                                    <input type="date" class="form-control mb-2" id="startDate">
                                    <label class="form-label">结束日期</label>
                                    <input type="date" class="form-control" id="endDate">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">数据维度</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeRevenue" checked>
                                        <label class="form-check-label" for="includeRevenue">收入数据</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeTransaction" checked>
                                        <label class="form-check-label" for="includeTransaction">交易数据</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeFee" checked>
                                        <label class="form-check-label" for="includeFee">手续费数据</label>
                                    </div>
                                </div>
                                <button class="btn btn-outline-primary w-100" onclick="financeReportManager.applyFilters()">
                                    <i class="bi bi-funnel me-2"></i>应用筛选
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-9">
                        <!-- 报表内容 -->
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0" id="reportTitle">财务概览报表</h6>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-secondary" onclick="financeReportManager.refreshReport()" title="刷新">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="financeReportManager.exportReport('excel')" title="导出Excel">
                                            <i class="bi bi-file-earmark-excel"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="financeReportManager.exportReport('pdf')" title="导出PDF">
                                            <i class="bi bi-file-earmark-pdf"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="financeReportManager.printReport()" title="打印">
                                            <i class="bi bi-printer"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body" id="reportContent">
                                <div class="text-center py-5">
                                    <div class="spinner-border spinner-border-lg mb-3" role="status"></div>
                                    <div>正在生成报表...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 创建报表模态框 -->
            <div class="modal fade" id="createReportModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-plus me-2"></i>创建自定义报表
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createReportForm">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">报表名称 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="reportName" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">报表类型 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="reportType" required>
                                            <option value="">请选择报表类型</option>
                                            <option value="financial_overview">财务概览</option>
                                            <option value="revenue_analysis">收入分析</option>
                                            <option value="transaction_report">交易报表</option>
                                            <option value="settlement_report">结算报表</option>
                                            <option value="fee_analysis">手续费分析</option>
                                            <option value="custom">自定义报表</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">数据源</label>
                                        <select class="form-select" id="dataSource">
                                            <option value="all">全部数据</option>
                                            <option value="platform">平台数据</option>
                                            <option value="provider">码商数据</option>
                                            <option value="merchant">商户数据</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">报表周期</label>
                                        <select class="form-select" id="reportPeriod">
                                            <option value="daily">日报</option>
                                            <option value="weekly">周报</option>
                                            <option value="monthly">月报</option>
                                            <option value="quarterly">季报</option>
                                            <option value="yearly">年报</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">包含指标</label>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="metric_revenue" checked>
                                                    <label class="form-check-label" for="metric_revenue">收入金额</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="metric_transaction_count" checked>
                                                    <label class="form-check-label" for="metric_transaction_count">交易笔数</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="metric_success_rate" checked>
                                                    <label class="form-check-label" for="metric_success_rate">成功率</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="metric_fee_amount" checked>
                                                    <label class="form-check-label" for="metric_fee_amount">手续费</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="metric_settlement_amount">
                                                    <label class="form-check-label" for="metric_settlement_amount">结算金额</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="metric_user_count">
                                                    <label class="form-check-label" for="metric_user_count">用户数量</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="metric_avg_amount">
                                                    <label class="form-check-label" for="metric_avg_amount">平均金额</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="metric_growth_rate">
                                                    <label class="form-check-label" for="metric_growth_rate">增长率</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="metric_profit_margin">
                                                    <label class="form-check-label" for="metric_profit_margin">利润率</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">报表描述</label>
                                        <textarea class="form-control" id="reportDescription" rows="3" placeholder="报表用途和说明..."></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="financeReportManager.createCustomReport()">
                                <i class="bi bi-check me-2"></i>创建报表
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 报表导航切换
        document.querySelectorAll('#reportNavigation .list-group-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                document.querySelectorAll('#reportNavigation .list-group-item').forEach(i => i.classList.remove('active'));
                item.classList.add('active');
                this.switchReport(item.dataset.report);
            });
        });

        // 时间范围变化
        document.getElementById('timeRange').addEventListener('change', (e) => {
            const customRange = document.getElementById('customDateRange');
            customRange.style.display = e.target.value === 'custom' ? 'block' : 'none';
        });

        // 快速报表类型变化
        document.getElementById('quickReportType').addEventListener('change', (e) => {
            this.updateQuickReportDates(e.target.value);
        });
    }

    /**
     * 切换报表类型
     */
    async switchReport(reportType) {
        try {
            document.getElementById('reportTitle').textContent = this.getReportTitle(reportType);
            document.getElementById('reportContent').innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border spinner-border-lg mb-3" role="status"></div>
                    <div>正在生成${this.getReportTitle(reportType)}...</div>
                </div>
            `;

            await this.loadReportData(reportType);
        } catch (error) {
            console.error('切换报表失败:', error);
            this.showError('报表加载失败: ' + error.message);
        }
    }

    /**
     * 加载报表数据
     */
    async loadReportData(reportType) {
        try {
            const params = this.getReportParams();
            const response = await this.apiClient.get(`/admin/finance/reports/${reportType}`, { params });

            if (response.success) {
                this.currentReport = {
                    type: reportType,
                    data: response.data,
                    params: params
                };
                this.renderReport(reportType, response.data);
            } else {
                throw new Error(response.message || '加载报表数据失败');
            }

        } catch (error) {
            console.error('加载报表数据失败:', error);
            this.showReportError(error.message);
        }
    }

    /**
     * 渲染报表
     */
    renderReport(reportType, data) {
        const contentEl = document.getElementById('reportContent');
        
        switch (reportType) {
            case 'overview':
                contentEl.innerHTML = this.renderOverviewReport(data);
                break;
            case 'revenue':
                contentEl.innerHTML = this.renderRevenueReport(data);
                break;
            case 'transaction':
                contentEl.innerHTML = this.renderTransactionReport(data);
                break;
            case 'settlement':
                contentEl.innerHTML = this.renderSettlementReport(data);
                break;
            case 'fee':
                contentEl.innerHTML = this.renderFeeReport(data);
                break;
            case 'provider':
                contentEl.innerHTML = this.renderProviderReport(data);
                break;
            case 'merchant':
                contentEl.innerHTML = this.renderMerchantReport(data);
                break;
            default:
                contentEl.innerHTML = this.renderDefaultReport(data);
        }

        // 初始化图表
        this.initializeReportCharts(reportType, data);
    }

    /**
     * 渲染财务概览报表
     */
    renderOverviewReport(data) {
        return `
            <div class="report-overview">
                <!-- 核心指标 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value text-primary">¥${this.formatAmount(data.total_revenue)}</div>
                            <div class="metric-label">总收入</div>
                            <div class="metric-change ${data.revenue_change >= 0 ? 'text-success' : 'text-danger'}">
                                ${data.revenue_change >= 0 ? '+' : ''}${data.revenue_change}%
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value text-success">${this.formatNumber(data.total_transactions)}</div>
                            <div class="metric-label">总交易笔数</div>
                            <div class="metric-change ${data.transaction_change >= 0 ? 'text-success' : 'text-danger'}">
                                ${data.transaction_change >= 0 ? '+' : ''}${data.transaction_change}%
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value text-info">${data.success_rate}%</div>
                            <div class="metric-label">成功率</div>
                            <div class="metric-change ${data.success_rate_change >= 0 ? 'text-success' : 'text-danger'}">
                                ${data.success_rate_change >= 0 ? '+' : ''}${data.success_rate_change}%
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-value text-warning">¥${this.formatAmount(data.total_fees)}</div>
                            <div class="metric-label">总手续费</div>
                            <div class="metric-change ${data.fee_change >= 0 ? 'text-success' : 'text-danger'}">
                                ${data.fee_change >= 0 ? '+' : ''}${data.fee_change}%
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 趋势图表 -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="chart-container">
                            <h6>收入趋势</h6>
                            <canvas id="revenueTrendChart" height="300"></canvas>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="chart-container">
                            <h6>收入来源分布</h6>
                            <canvas id="revenueSourceChart" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 详细数据表格 -->
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>收入金额</th>
                                <th>交易笔数</th>
                                <th>成功率</th>
                                <th>手续费</th>
                                <th>净收入</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${data.daily_data.map(day => `
                                <tr>
                                    <td>${this.formatDate(day.date)}</td>
                                    <td class="text-primary">¥${this.formatAmount(day.revenue)}</td>
                                    <td>${this.formatNumber(day.transactions)}</td>
                                    <td>
                                        <span class="badge bg-${day.success_rate >= 95 ? 'success' : day.success_rate >= 90 ? 'warning' : 'danger'}">
                                            ${day.success_rate}%
                                        </span>
                                    </td>
                                    <td class="text-warning">¥${this.formatAmount(day.fees)}</td>
                                    <td class="text-success">¥${this.formatAmount(day.net_revenue)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * 生成快速报表
     */
    async generateQuickReport() {
        try {
            const reportType = document.getElementById('quickReportType').value;
            const dataScope = document.getElementById('quickDataScope').value;
            const startDate = document.getElementById('quickStartDate').value;
            const endDate = document.getElementById('quickEndDate').value;
            const format = document.getElementById('quickFormat').value;

            if (!startDate || !endDate) {
                this.showError('请选择日期范围');
                return;
            }

            const params = {
                type: reportType,
                scope: dataScope,
                start_date: startDate,
                end_date: endDate,
                format: format
            };

            if (format === 'html') {
                // 在线查看
                await this.loadReportData('quick');
            } else {
                // 下载文件
                const response = await this.apiClient.post('/admin/finance/reports/export', params, {
                    responseType: 'blob'
                });
                
                this.downloadFile(response.data, `财务报表_${reportType}_${startDate}_${endDate}.${format}`);
                this.showSuccess('报表导出成功');
            }

        } catch (error) {
            console.error('生成快速报表失败:', error);
            this.showError('生成报表失败: ' + error.message);
        }
    }

    /**
     * 获取报表参数
     */
    getReportParams() {
        const timeRange = document.getElementById('timeRange').value;
        let startDate, endDate;

        if (timeRange === 'custom') {
            startDate = document.getElementById('startDate').value;
            endDate = document.getElementById('endDate').value;
        } else {
            const dates = this.getDateRange(timeRange);
            startDate = dates.start;
            endDate = dates.end;
        }

        return {
            time_range: timeRange,
            start_date: startDate,
            end_date: endDate,
            include_revenue: document.getElementById('includeRevenue').checked,
            include_transaction: document.getElementById('includeTransaction').checked,
            include_fee: document.getElementById('includeFee').checked
        };
    }

    /**
     * 获取日期范围
     */
    getDateRange(timeRange) {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        
        switch (timeRange) {
            case 'today':
                return {
                    start: today.toISOString().split('T')[0],
                    end: today.toISOString().split('T')[0]
                };
            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                return {
                    start: yesterday.toISOString().split('T')[0],
                    end: yesterday.toISOString().split('T')[0]
                };
            case 'week':
                const weekStart = new Date(today);
                weekStart.setDate(weekStart.getDate() - weekStart.getDay());
                return {
                    start: weekStart.toISOString().split('T')[0],
                    end: today.toISOString().split('T')[0]
                };
            case 'month':
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                return {
                    start: monthStart.toISOString().split('T')[0],
                    end: today.toISOString().split('T')[0]
                };
            default:
                return {
                    start: today.toISOString().split('T')[0],
                    end: today.toISOString().split('T')[0]
                };
        }
    }

    /**
     * 获取报表标题
     */
    getReportTitle(reportType) {
        const titleMap = {
            'overview': '财务概览报表',
            'revenue': '收入分析报表',
            'transaction': '交易统计报表',
            'settlement': '结算报表',
            'fee': '手续费分析报表',
            'provider': '码商报表',
            'merchant': '商户报表'
        };
        return titleMap[reportType] || '财务报表';
    }

    /**
     * 格式化金额
     */
    formatAmount(amount) {
        return parseFloat(amount || 0).toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    /**
     * 格式化数字
     */
    formatNumber(number) {
        return parseInt(number || 0).toLocaleString('zh-CN');
    }

    /**
     * 格式化日期
     */
    formatDate(date) {
        return new Date(date).toLocaleDateString('zh-CN');
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        console.log('✅', message);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error('❌', message);
    }

    /**
     * 显示报表错误
     */
    showReportError(message) {
        document.getElementById('reportContent').innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                <h5 class="mt-3">报表加载失败</h5>
                <p class="text-muted">${message}</p>
                <button class="btn btn-primary" onclick="financeReportManager.refreshReport()">
                    <i class="bi bi-arrow-clockwise me-2"></i>重新加载
                </button>
            </div>
        `;
    }
}

// 导出模块
window.FinanceReportManager = FinanceReportManager;

// 创建全局实例
if (typeof window.financeReportManager === 'undefined') {
    window.financeReportManager = new FinanceReportManager();
}

console.log('📊 FinanceReportManager 模块加载完成'); 