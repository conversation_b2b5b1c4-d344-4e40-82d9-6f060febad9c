/**
 * 商户管理器
 * 从admin.js第6708-7255行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class MerchantManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.currentFilters = {
        };
        this.isEditing = false;
        this.currentMerchantId = null;
    }
    async initialize() {
        this.initializeEventListeners();
        await this.loadMerchants();
    }
    initializeEventListeners() {
        // 状态筛选
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.applyFilters());
        }
        // 搜索框回车事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchMerchants();
                }
            });
        }
    }
    async loadMerchants(page = 1) {
        try {
            this.showLoadingState();
            const params = new URLSearchParams({
                page: page,
                limit: this.pageSize,
                ...this.currentFilters
            });
            const response = await fetch(`../api/admin.php?action=merchants&${
                params
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`
                }
            });
            // 检查HTTP状态码
            if (!response.ok) {
                let errorMessage = `HTTP ${
                    response.status
                }: ${
                    response.statusText
                }`;
                try {
                    const errorData = await response.json();
                    console.log('错误详情:',
                    errorData);
                    if (errorData.debug) {
                        errorMessage += ` - Debug: ${
                            JSON.stringify(errorData.debug)
                        }`;
                    }
                    errorMessage += ` - ${
                        errorData.message || '未知错误'
                    }`;
                } catch (parseError) {
                    console.log('无法解析错误响应');
                }
                throw new Error(errorMessage);
            }
            const result = await response.json();
            if (result.code === 200) {
                this.renderMerchantList(result.data.merchants);
                this.renderStats(result.data.stats);
                this.renderPagination(result.data.pagination);
                this.updateMerchantCount(result.data.pagination.total_records);
                this.currentPage = page;
            } else {
                throw new Error(result.message || '加载商户数据失败');
            }
        } catch (error) {
            console.error('加载商户数据失败:',
            error);
            this.showErrorState(error.message);
        }
    }
    renderMerchantList(merchants) {
        const container = document.getElementById('merchantsTableContainer');
        if (!merchants || merchants.length === 0) {
            container.innerHTML = `
            <div class="text-center py-4">
            <i class="bi bi-shop text-muted" style="font-size: 3rem;
            "></i>
            <h5 class="mt-3 text-muted">暂无商户数据</h5>
            <p class="text-muted">点击"添加商户"按钮创建第一个商户</p>
            </div>
            `;
            return;
        }
        const tableHtml = `
        <div class="table-responsive">
        <table class="table table-hover">
        <thead class="table-light">
        <tr>
        <th>商户信息</th>
        <th>联系方式</th>
        <th>业务信息</th>
        <th>状态</th>
        <th>创建时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        ${
            merchants.map(merchant => this.renderMerchantRow(merchant)).join('')
        }
        </tbody>
        </table>
        </div>
        `;
        container.innerHTML = tableHtml;
    }
    renderMerchantRow(merchant) {
        return `
        <tr>
        <td>
        <div class="d-flex align-items-center">
        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
        ${
            merchant.real_name ? merchant.real_name.charAt(0).toUpperCase() : 'M'
        }
        </div>
        <div>
        <div class="fw-bold">${
            merchant.real_name || '-'
        }</div>
        <small class="text-muted">@${
            merchant.username
        }</small>
        ${
            merchant.company_name ? `<br><small class="text-info">${
                merchant.company_name
            }</small>` : ''
        }
        </div>
        </div>
        </td>
        <td>
        <div>
        <i class="bi bi-envelope me-1"></i>${
            merchant.email || '-'
        }
        </div>
        ${
            merchant.phone ? `<div><i class="bi bi-telephone me-1"></i>${
                merchant.phone
            }</div>` : ''
        }
        </td>
        <td>
        <div>费率: <span class="badge bg-info">${
            (merchant.service_rate * 100).toFixed(2)
        }%</span></div>
        <div>产品: <span class="badge bg-secondary">${
            merchant.product_count || 0
        }</span></div>
        <div>交易: <span class="badge bg-success">${
            merchant.transaction_count || 0
        }</span></div>
        ${
            merchant.total_amount ? `<div>总额: ¥${
                parseFloat(merchant.total_amount).toFixed(2)
            }</div>` : ''
        }
        </td>
        <td>
        ${
            this.renderStatusBadge(merchant.status)
        }
        </td>
        <td>
        <small>${
            this.formatDateTime(merchant.created_at)
        }</small>
        </td>
        <td>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-primary" onclick="window.merchantManager.editMerchant(${
            merchant.id
        })" title="编辑">
        <i class="bi bi-pencil"></i>
        </button>
        <button class="btn btn-outline-info" onclick="window.merchantManager.viewMerchantDetail(${
            merchant.id
        })" title="详情">
        <i class="bi bi-eye"></i>
        </button>
        <button class="btn btn-outline-danger" onclick="window.merchantManager.deleteMerchant(${
            merchant.id
        })" title="删除">
        <i class="bi bi-trash"></i>
        </button>
        </div>
        </td>
        </tr>
        `;
    }
    renderStatusBadge(status) {
        const statusConfig = {
            'pending': {
                class: 'bg-warning',
                text: '待审核'
            },
            'active': {
                class: 'bg-success',
                text: '已激活'
            },
            'suspended': {
                class: 'bg-danger',
                text: '已暂停'
            },
            'rejected': {
                class: 'bg-secondary',
                text: '已拒绝'
            }
        };
        const config = statusConfig[status] || {
            class: 'bg-secondary',
            text: status
        };
        return `<span class="badge ${
            config.class
        }">${
            config.text
        }</span>`;
    }
    renderStats(stats) {
        if (!stats) return;
        document.getElementById('totalMerchants').textContent = stats.total_merchants || 0;
        document.getElementById('pendingMerchants').textContent = stats.pending_merchants || 0;
        document.getElementById('activeMerchants').textContent = stats.active_merchants || 0;
        document.getElementById('suspendedMerchants').textContent = stats.suspended_merchants || 0;
        document.getElementById('rejectedMerchants').textContent = stats.rejected_merchants || 0;
    }
    renderPagination(pagination) {
        const container = document.getElementById('merchantPagination');
        if (!container || !pagination) return;
        const {
            current_page,
            total_pages,
            total_records
        } = pagination;
        if (total_pages <= 1) {
            container.innerHTML = '';
            return;
        }
        let paginationHtml = `
        <nav aria-label="商户分页">
        <ul class="pagination justify-content-center">
        `;
        // 上一页
        if (current_page > 1) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.merchantManager.loadMerchants(${
                current_page - 1
            })">上一页</a>
            </li>
            `;
        }
        // 页码
        const startPage = Math.max(1,
        current_page - 2);
        const endPage = Math.min(total_pages,
        current_page + 2);
        if (startPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="window.merchantManager.loadMerchants(1)">1</a></li>`;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        for (let i = startPage;
        i <= endPage;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === current_page ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.merchantManager.loadMerchants(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        if (endPage < total_pages) {
            if (endPage < total_pages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="window.merchantManager.loadMerchants(${
                total_pages
            })">${
                total_pages
            }</a></li>`;
        }
        // 下一页
        if (current_page < total_pages) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.merchantManager.loadMerchants(${
                current_page + 1
            })">下一页</a>
            </li>
            `;
        }
        paginationHtml += `
        </ul>
        </nav>
        <div class="text-center mt-2">
        <small class="text-muted">共 ${
            total_records
        } 条记录，第 ${
            current_page
        } / ${
            total_pages
        } 页</small>
        </div>
        `;
        container.innerHTML = paginationHtml;
    }
    updateMerchantCount(count) {
        const countElement = document.getElementById('merchantCount');
        if (countElement) {
            countElement.textContent = count || 0;
        }
    }
    applyFilters() {
        const status = document.getElementById('statusFilter')?.value || '';
        const search = document.getElementById('searchInput')?.value.trim() || '';
        this.currentFilters = {
        };
        if (status) this.currentFilters.status = status;
        if (search) this.currentFilters.search = search;
        this.currentPage = 1;
        this.loadMerchants(1);
    }
    searchMerchants() {
        this.applyFilters();
    }
    refreshMerchants() {
        this.loadMerchants(this.currentPage);
    }
    showCreateModal() {
        this.isEditing = false;
        this.currentMerchantId = null;
        document.getElementById('merchantModalTitle').textContent = '添加商户';
        document.getElementById('merchantForm').reset();
        document.getElementById('merchantId').value = '';
        document.getElementById('passwordRow').style.display = 'block';
        document.getElementById('password').required = true;
        document.getElementById('apiInfoSection').style.display = 'none';
        const modal = new bootstrap.Modal(document.getElementById('merchantModal'));
        modal.show();
    }
    async editMerchant(merchantId) {
        try {
            this.isEditing = true;
            this.currentMerchantId = merchantId;
            const response = await fetch(`../api/admin.php?action=merchants&id=${
                merchantId
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                const merchant = result.data;
                this.fillEditForm(merchant);
                document.getElementById('merchantModalTitle').textContent = '编辑商户';
                document.getElementById('passwordRow').style.display = 'none';
                document.getElementById('password').required = false;
                document.getElementById('apiInfoSection').style.display = 'block';
                const modal = new bootstrap.Modal(document.getElementById('merchantModal'));
                modal.show();
            } else {
                throw new Error(result.message || '获取商户信息失败');
            }
        } catch (error) {
            console.error('编辑商户失败:',
            error);
            this.showError(error.message);
        }
    }
    fillEditForm(merchant) {
        document.getElementById('merchantId').value = merchant.id;
        document.getElementById('username').value = merchant.username || '';
        document.getElementById('realName').value = merchant.real_name || '';
        document.getElementById('email').value = merchant.email || '';
        document.getElementById('phone').value = merchant.phone || '';
        document.getElementById('companyName').value = merchant.company_name || '';
        document.getElementById('businessLicense').value = merchant.business_license || '';
        document.getElementById('serviceRate').value = (merchant.service_rate * 100).toFixed(2);
        document.getElementById('status').value = merchant.status || 'pending';
        document.getElementById('ipWhitelist').value = merchant.ip_whitelist || '';
        document.getElementById('apiKey').value = merchant.api_key || '';
    }
    async saveMerchant() {
        try {
            const form = document.getElementById('merchantForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }
            this.setSaveLoading(true);
            const formData = {
                username: document.getElementById('username').value.trim(),
                real_name: document.getElementById('realName').value.trim(),
                email: document.getElementById('email').value.trim(),
                phone: document.getElementById('phone').value.trim(),
                company_name: document.getElementById('companyName').value.trim(),
                business_license: document.getElementById('businessLicense').value.trim(),
                service_rate: parseFloat(document.getElementById('serviceRate').value) / 100,
                status: document.getElementById('status').value,
                ip_whitelist: document.getElementById('ipWhitelist').value.trim()
            };
            if (!this.isEditing) {
                formData.password = document.getElementById('password').value;
            } else {
                formData.id = this.currentMerchantId;
            }
            const url = '../api/admin.php?action=merchants';
            const method = this.isEditing ? 'PUT' : 'POST';
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`
                },
                body: JSON.stringify(formData)
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess(this.isEditing ? '商户更新成功' : '商户创建成功');
                bootstrap.Modal.getInstance(document.getElementById('merchantModal')).hide();
                this.refreshMerchants();
            } else {
                throw new Error(result.message || '保存失败');
            }
        } catch (error) {
            console.error('保存商户失败:',
            error);
            this.showError(error.message);
        } finally {
            this.setSaveLoading(false);
        }
    }
    async regenerateApiKey() {
        if (!this.currentMerchantId) return;
        try {
            const response = await fetch('../api/admin.php?action=merchant_api_keys', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`
                },
                body: JSON.stringify({
                    merchant_id: this.currentMerchantId
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                document.getElementById('apiKey').value = result.data.api_key;
                this.showSuccess('API密钥重新生成成功');
            } else {
                throw new Error(result.message || 'API密钥生成失败');
            }
        } catch (error) {
            console.error('重新生成API密钥失败:',
            error);
            this.showError(error.message);
        }
    }
    async deleteMerchant(merchantId) {
        if (!confirm('确定要删除这个商户吗？此操作不可恢复！')) {
            return;
        }
        try {
            const response = await fetch(`../api/admin.php?action=merchants&id=${
                merchantId
            }`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('商户删除成功');
                this.refreshMerchants();
            } else {
                throw new Error(result.message || '删除失败');
            }
        } catch (error) {
            console.error('删除商户失败:',
            error);
            this.showError(error.message);
        }
    }
    setSaveLoading(loading) {
        const btn = document.getElementById('saveMerchantBtn');
        const spinner = btn.querySelector('.spinner-border');
        if (loading) {
            btn.disabled = true;
            spinner.style.display = 'inline-block';
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
        } else {
            btn.disabled = false;
            spinner.style.display = 'none';
            btn.innerHTML = '保存';
        }
    }
    showLoadingState() {
        const container = document.getElementById('merchantsTableContainer');
        container.innerHTML = `
        <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载商户数据...</p>
        </div>
        `;
    }
    showErrorState(message) {
        const container = document.getElementById('merchantsTableContainer');
        container.innerHTML = `
        <div class="text-center py-4">
        <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;
        "></i>
        <h5 class="mt-3 text-danger">加载失败</h5>
        <p class="text-muted">${
            message
        }</p>
        <button class="btn btn-primary" onclick="window.merchantManager.refreshMerchants()">重试</button>
        </div>
        `;
    }
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    showSuccess(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        ';
        alertDiv.innerHTML = `
        <i class="bi bi-check-circle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
    showError(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        ';
        alertDiv.innerHTML = `
        <i class="bi bi-exclamation-triangle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = MerchantManager;
} else if (typeof window !== "undefined") {
    window.MerchantManager = MerchantManager;
}

console.log('📦 MerchantManager 模块加载完成');
