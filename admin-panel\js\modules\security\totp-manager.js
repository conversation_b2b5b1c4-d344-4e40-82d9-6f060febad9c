/**
 * TOTP双因子认证管理器
 * 从admin.js第14288-15085行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class TOTPManager {
    constructor() {
        this.apiUrl = '/api/admin.php?do=totp';
        this.currentPage = 1;
        this.pageSize = 20;
        this.currentFilters = {
        };
        this.pollInterval = null;
        this.auth = new AuthManager();
    }
    // 初始化
    initialize() {
        this.loadTOTPStatus();
        this.loadUserTOTPList();
        this.initializeEventListeners();
    }
    // 初始化事件监听器
    initializeEventListeners() {
        // TOTP开关
        document.getElementById('totpToggle')?.addEventListener('change', (e) => {
            if (e.target.checked) {
                this.setupTOTP();
            } else {
                this.disableTOTP();
            }
        });
        // 验证按钮
        document.getElementById('verifyTOTPBtn')?.addEventListener('click', () => {
            this.verifyTOTP();
        });
        // 重新生成密钥
        document.getElementById('regenerateSecretBtn')?.addEventListener('click', () => {
            this.regenerateSecret();
        });
        // 搜索功能
        document.getElementById('totpSearchBtn')?.addEventListener('click', () => {
            this.searchUsers();
        });
        document.getElementById('totpSearchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchUsers();
            }
        });
        // 筛选器
        ['totpStatusFilter', 'totpUserTypeFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });
    }
    // 加载TOTP状态
    async loadTOTPStatus() {
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=status`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.updateTOTPStatusDisplay(result.data);
            }
        } catch (error) {
            console.error('加载TOTP状态失败:',
            error);
        }
    }
    // 更新TOTP状态显示
    updateTOTPStatusDisplay(data) {
        const elements = {
            totpEnabled: data.enabled,
            totpSetupTime: data.setup_at,
            totpLastUsed: data.last_used,
            totpStatus: data.enabled ? 'enabled' : 'disabled'
        };
        // 更新开关状态
        const toggleElement = document.getElementById('totpToggle');
        if (toggleElement) {
            toggleElement.checked = elements.totpEnabled;
        }
        // 更新状态显示
        const statusElement = document.getElementById('totpStatusText');
        if (statusElement) {
            statusElement.innerHTML = elements.totpEnabled
            ? '<span class="badge bg-success">已启用</span>'
            : '<span class="badge bg-secondary">未启用</span>';
        }
        // 更新设置时间
        const setupTimeElement = document.getElementById('totpSetupTime');
        if (setupTimeElement) {
            setupTimeElement.textContent = elements.totpSetupTime
            ? this.formatDateTime(elements.totpSetupTime)
            : '未设置';
        }
        // 更新最后使用时间
        const lastUsedElement = document.getElementById('totpLastUsed');
        if (lastUsedElement) {
            lastUsedElement.textContent = elements.totpLastUsed
            ? this.formatDateTime(elements.totpLastUsed)
            : '从未使用';
        }
        // 显示/隐藏相关面板
        const setupPanel = document.getElementById('totpSetupPanel');
        const managementPanel = document.getElementById('totpManagementPanel');
        if (elements.totpEnabled) {
            setupPanel?.classList.add('d-none');
            managementPanel?.classList.remove('d-none');
        } else {
            setupPanel?.classList.remove('d-none');
            managementPanel?.classList.add('d-none');
        }
    }
    // 设置TOTP
    async setupTOTP() {
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=setup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showTOTPSetupModal(result.data);
            } else {
                this.showError('设置TOTP失败: ' + result.message);
                // 恢复开关状态
                const toggleElement = document.getElementById('totpToggle');
                if (toggleElement) toggleElement.checked = false;
            }
        } catch (error) {
            console.error('设置TOTP失败:',
            error);
            this.showError('网络请求失败，请重试');
            // 恢复开关状态
            const toggleElement = document.getElementById('totpToggle');
            if (toggleElement) toggleElement.checked = false;
        }
    }
    // 显示TOTP设置模态框
    showTOTPSetupModal(data) {
        const modal = new bootstrap.Modal(document.getElementById('totpSetupModal'));
        const content = document.getElementById('totpSetupContent');
        content.innerHTML = `
        <div class="text-center">
        <h5 class="mb-3">扫描二维码设置双因子认证</h5>
        <div class="qr-code-container mb-4">
        <div id="qrCodeDisplay"></div>
        </div>
        <div class="alert alert-info text-start">
        <h6 class="alert-heading">设置步骤：</h6>
        <ol class="mb-0">
        <li>在手机上安装Google Authenticator、Microsoft Authenticator或其他TOTP应用</li>
        <li>使用应用扫描上方二维码</li>
        <li>输入应用显示的6位验证码进行验证</li>
        <li>验证成功后双因子认证即可启用</li>
        </ol>
        </div>
        <div class="row">
        <div class="col-md-8">
        <label class="form-label">验证码</label>
        <input type="text" class="form-control text-center" id="setupVerificationCode"
        placeholder="输入6位验证码" maxlength="6" pattern="\\d{
            6
        }">
        </div>
        <div class="col-md-4 d-flex align-items-end">
        <button class="btn btn-success w-100" onclick="window.totpManager.completeSetup()">
        <i class="bi bi-check-circle me-2"></i>验证并启用
        </button>
        </div>
        </div>
        <div class="mt-3">
        <p class="text-muted small">密钥: <code id="totpSecret">${
            data.secret
        }</code></p>
        <p class="text-muted small">如果无法扫描二维码，可以手动输入上述密钥</p>
        </div>
        </div>
        `;
        // 生成二维码
        this.generateQRCode(data.qr_code_url);
        modal.show();
    }
    // 生成二维码
    generateQRCode(url) {
        const container = document.getElementById('qrCodeDisplay');
        if (container && window.QRCode) {
            container.innerHTML = '';
            new QRCode(container, {
                text: url,
                width: 200,
                height: 200,
                colorDark: '#000000',
                colorLight: '#ffffff'
            });
        }
    }
    // 完成TOTP设置
    async completeSetup() {
        const code = document.getElementById('setupVerificationCode')?.value.trim();
        if (!code || code.length !== 6) {
            this.showError('请输入6位验证码');
            return;
        }
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=verify_setup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    code: code
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('双因子认证设置成功！');
                bootstrap.Modal.getInstance(document.getElementById('totpSetupModal')).hide();
                this.loadTOTPStatus();
            } else {
                this.showError('验证失败: ' + result.message);
            }
        } catch (error) {
            console.error('验证TOTP失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 禁用TOTP
    async disableTOTP() {
        if (!confirm('确定要禁用双因子认证吗？这将降低账户安全性。')) {
            // 恢复开关状态
            const toggleElement = document.getElementById('totpToggle');
            if (toggleElement) toggleElement.checked = true;
            return;
        }
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=disable`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('双因子认证已禁用');
                this.loadTOTPStatus();
            } else {
                this.showError('禁用失败: ' + result.message);
                // 恢复开关状态
                const toggleElement = document.getElementById('totpToggle');
                if (toggleElement) toggleElement.checked = true;
            }
        } catch (error) {
            console.error('禁用TOTP失败:',
            error);
            this.showError('网络请求失败，请重试');
            // 恢复开关状态
            const toggleElement = document.getElementById('totpToggle');
            if (toggleElement) toggleElement.checked = true;
        }
    }
    // 验证TOTP
    async verifyTOTP() {
        const code = document.getElementById('verificationCodeInput')?.value.trim();
        if (!code || code.length !== 6) {
            this.showError('请输入6位验证码');
            return;
        }
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=verify`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    code: code
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('验证码正确！');
                document.getElementById('verificationCodeInput').value = '';
                this.loadTOTPStatus();
                // 更新最后使用时间
            } else {
                this.showError('验证码错误');
            }
        } catch (error) {
            console.error('验证TOTP失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 重新生成密钥
    async regenerateSecret() {
        if (!confirm('重新生成密钥将使现有的认证器应用失效，需要重新扫描二维码。确定继续吗？')) {
            return;
        }
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=regenerate`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('密钥已重新生成，请重新设置认证器应用');
                this.showTOTPSetupModal(result.data);
            } else {
                this.showError('重新生成失败: ' + result.message);
            }
        } catch (error) {
            console.error('重新生成密钥失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 加载用户TOTP列表（管理员功能）
    async loadUserTOTPList() {
        const user = window.authManager?.getUser();
        if (!user || user.user_type !== 'admin') return;
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                ...this.currentFilters
            });
            const response = await fetch(`${
                this.apiUrl
            }&action=list&${
                params
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderUserTOTPTable(result.data.users);
                this.renderPagination(result.data.pagination);
                this.updateUserCount(result.data.pagination.total_records);
                this.updateTOTPStats(result.data.stats);
            }
        } catch (error) {
            console.error('加载用户TOTP列表失败:',
            error);
        }
    }
    // 渲染用户TOTP表格
    renderUserTOTPTable(users) {
        const container = document.getElementById('userTOTPTableContainer');
        if (!container) return;
        if (!users || users.length === 0) {
            container.innerHTML = `
            <div class="text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <div class="mt-3">暂无用户数据</div>
            </div>
            `;
            return;
        }
        const tableHtml = `
        <div class="table-responsive">
        <table class="table table-hover align-middle">
        <thead class="table-light">
        <tr>
        <th>用户</th>
        <th>用户类型</th>
        <th>TOTP状态</th>
        <th>设置时间</th>
        <th>最后使用</th>
        <th>使用次数</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        ${
            users.map(user => `
            <tr>
            <td>
            <div class="d-flex align-items-center">
            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
            ${
                user.username.charAt(0).toUpperCase()
            }
            </div>
            <div>
            <div class="fw-bold">${
                user.username
            }</div>
            <small class="text-muted">ID: ${
                user.id
            }</small>
            </div>
            </div>
            </td>
            <td>
            <span class="badge ${
                this.getUserTypeBadgeClass(user.user_type)
            }">
            ${
                this.getUserTypeText(user.user_type)
            }
            </span>
            </td>
            <td>
            <span class="badge ${
                user.totp_enabled ? 'bg-success' : 'bg-secondary'
            }">
            ${
                user.totp_enabled ? '已启用' : '未启用'
            }
            </span>
            </td>
            <td>
            <small class="text-muted">
            ${
                user.totp_setup_at ? this.formatDateTime(user.totp_setup_at) : '-'
            }
            </small>
            </td>
            <td>
            <small class="text-muted">
            ${
                user.totp_last_used ? this.formatDateTime(user.totp_last_used) : '-'
            }
            </small>
            </td>
            <td>
            <span class="badge bg-info">${
                user.totp_usage_count || 0
            }</span>
            </td>
            <td>
            <div class="btn-group btn-group-sm">
            ${
                user.totp_enabled ? `
                <button class="btn btn-outline-warning"
                onclick="window.totpManager.resetUserTOTP(${
                    user.id
                })"
                data-bs-toggle="tooltip" title="重置TOTP">
                <i class="bi bi-arrow-clockwise"></i>
                </button>
                <button class="btn btn-outline-danger"
                onclick="window.totpManager.disableUserTOTP(${
                    user.id
                })"
                data-bs-toggle="tooltip" title="禁用TOTP">
                <i class="bi bi-toggle-off"></i>
                </button>
                ` : `
                <button class="btn btn-outline-success"
                onclick="window.totpManager.enableUserTOTP(${
                    user.id
                })"
                data-bs-toggle="tooltip" title="启用TOTP">
                <i class="bi bi-toggle-on"></i>
                </button>
                `
            }
            <button class="btn btn-outline-info"
            onclick="window.totpManager.viewUserTOTPStats(${
                user.id
            })"
            data-bs-toggle="tooltip" title="查看统计">
            <i class="bi bi-bar-chart"></i>
            </button>
            </div>
            </td>
            </tr>
            `).join('')
        }
        </tbody>
        </table>
        </div>
        `;
        container.innerHTML = tableHtml;
        // 初始化tooltip
        const tooltips = container.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(tooltip => {
            new bootstrap.Tooltip(tooltip);
        });
    }
    // 更新TOTP统计
    updateTOTPStats(stats) {
        if (!stats) return;
        const elements = {
            totalUsers: stats.total_users || 0,
            enabledUsers: stats.enabled_users || 0,
            todayUsage: stats.today_usage || 0,
            enabledRate: stats.enabled_rate || 0
        };
        Object.keys(elements).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (key === 'enabledRate') {
                    element.textContent = `${
                        elements[key]
                    }%`;
                } else {
                    element.textContent = this.formatNumber(elements[key]);
                }
            }
        });
    }
    // 重置用户TOTP
    async resetUserTOTP(userId) {
        if (!confirm('确定要重置该用户的TOTP设置吗？用户需要重新设置认证器应用。')) return;
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=reset_user`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    user_id: userId
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('用户TOTP已重置');
                this.loadUserTOTPList();
            } else {
                this.showError('重置失败: ' + result.message);
            }
        } catch (error) {
            console.error('重置用户TOTP失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 禁用用户TOTP
    async disableUserTOTP(userId) {
        if (!confirm('确定要禁用该用户的TOTP吗？')) return;
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=disable_user`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    user_id: userId
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('用户TOTP已禁用');
                this.loadUserTOTPList();
            } else {
                this.showError('禁用失败: ' + result.message);
            }
        } catch (error) {
            console.error('禁用用户TOTP失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 启用用户TOTP
    async enableUserTOTP(userId) {
        if (!confirm('确定要为该用户启用TOTP吗？用户需要设置认证器应用。')) return;
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=enable_user`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    user_id: userId
                })
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('用户TOTP已启用');
                this.loadUserTOTPList();
            } else {
                this.showError('启用失败: ' + result.message);
            }
        } catch (error) {
            console.error('启用用户TOTP失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 查看用户TOTP统计
    async viewUserTOTPStats(userId) {
        try {
            const response = await fetch(`${
                this.apiUrl
            }&action=user_stats&user_id=${
                userId
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showUserStatsModal(result.data);
            } else {
                this.showError('获取统计失败: ' + result.message);
            }
        } catch (error) {
            console.error('获取用户TOTP统计失败:',
            error);
            this.showError('网络请求失败，请重试');
        }
    }
    // 显示用户统计模态框
    showUserStatsModal(data) {
        const modal = new bootstrap.Modal(document.getElementById('userStatsModal'));
        const content = document.getElementById('userStatsContent');
        content.innerHTML = `
        <div class="row">
        <div class="col-md-6">
        <h6 class="text-muted">基本信息</h6>
        <table class="table table-sm">
        <tr><td>用户名:</td><td><strong>${
            data.username
        }</strong></td></tr>
        <tr><td>用户类型:</td><td><span class="badge ${
            this.getUserTypeBadgeClass(data.user_type)
        }">${
            this.getUserTypeText(data.user_type)
        }</span></td></tr>
        <tr><td>TOTP状态:</td><td><span class="badge ${
            data.totp_enabled ? 'bg-success' : 'bg-secondary'
        }">${
            data.totp_enabled ? '已启用' : '未启用'
        }</span></td></tr>
        <tr><td>设置时间:</td><td>${
            data.setup_at ? this.formatDateTime(data.setup_at) : '-'
        }</td></tr>
        </table>
        </div>
        <div class="col-md-6">
        <h6 class="text-muted">使用统计</h6>
        <table class="table table-sm">
        <tr><td>总使用次数:</td><td class="text-primary fw-bold">${
            data.total_usage || 0
        }</td></tr>
        <tr><td>最后使用:</td><td>${
            data.last_used ? this.formatDateTime(data.last_used) : '-'
        }</td></tr>
        <tr><td>今日使用:</td><td class="text-success fw-bold">${
            data.today_usage || 0
        }</td></tr>
        <tr><td>本周使用:</td><td class="text-info fw-bold">${
            data.week_usage || 0
        }</td></tr>
        </table>
        </div>
        </div>
        ${
            data.recent_usage ? `
            <hr>
            <h6 class="text-muted">最近使用记录</h6>
            <div class="table-responsive">
            <table class="table table-striped table-sm">
            <thead>
            <tr>
            <th>使用时间</th>
            <th>IP地址</th>
            <th>用户代理</th>
            <th>结果</th>
            </tr>
            </thead>
            <tbody>
            ${
                data.recent_usage.map(usage => `
                <tr>
                <td>${
                    this.formatDateTime(usage.used_at)
                }</td>
                <td><code>${
                    usage.ip_address
                }</code></td>
                <td><small>${
                    usage.user_agent || '-'
                }</small></td>
                <td><span class="badge ${
                    usage.success ? 'bg-success' : 'bg-danger'
                }">${
                    usage.success ? '成功' : '失败'
                }</span></td>
                </tr>
                `).join('')
            }
            </tbody>
            </table>
            </div>
            ` : ''
        }
        `;
        modal.show();
    }
    // 搜索用户
    searchUsers() {
        const searchTerm = document.getElementById('totpSearchInput')?.value.trim();
        this.currentFilters.search = searchTerm || undefined;
        this.currentPage = 1;
        this.loadUserTOTPList();
    }
    // 应用筛选器
    applyFilters() {
        const statusFilter = document.getElementById('totpStatusFilter')?.value;
        const userTypeFilter = document.getElementById('totpUserTypeFilter')?.value;
        this.currentFilters = {
            ...this.currentFilters,
            status: statusFilter || undefined,
            user_type: userTypeFilter || undefined
        };
        this.currentPage = 1;
        this.loadUserTOTPList();
    }
    // 渲染分页
    renderPagination(pagination) {
        const container = document.getElementById('totpPagination');
        if (!container || !pagination) return;
        const {
            current_page,
            total_pages
        } = pagination;
        if (total_pages <= 1) {
            container.innerHTML = '';
            return;
        }
        let paginationHtml = '<nav><ul class="pagination">';
        // 上一页
        paginationHtml += `
        <li class="page-item ${
            current_page === 1 ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.totpManager.goToPage(${
            current_page - 1
        })">上一页</a>
        </li>
        `;
        // 页码
        const start = Math.max(1,
        current_page - 2);
        const end = Math.min(total_pages,
        current_page + 2);
        for (let i = start;
        i <= end;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === current_page ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.totpManager.goToPage(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        // 下一页
        paginationHtml += `
        <li class="page-item ${
            current_page === total_pages ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.totpManager.goToPage(${
            current_page + 1
        })">下一页</a>
        </li>
        `;
        paginationHtml += '</ul></nav>';
        container.innerHTML = paginationHtml;
    }
    // 跳转到指定页
    goToPage(page) {
        this.currentPage = page;
        this.loadUserTOTPList();
    }
    // 更新用户数量显示
    updateUserCount(count) {
        const element = document.getElementById('userCount');
        if (element) {
            element.textContent = this.formatNumber(count);
        }
    }
    // 工具函数
    formatNumber(num) {
        if (num === null || num === undefined) return '0';
        return Number(num).toLocaleString();
    }
    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';
        return new Date(dateTimeStr).toLocaleString('zh-CN');
    }
    getUserTypeBadgeClass(userType) {
        const classes = {
            'admin': 'bg-danger',
            'provider': 'bg-warning',
            'merchant': 'bg-info'
        };
        return classes[userType] || 'bg-secondary';
    }
    getUserTypeText(userType) {
        const texts = {
            'admin': '管理员',
            'provider': '支付码商',
            'merchant': '商户'
        };
        return texts[userType] || userType;
    }
    showSuccess(message) {
        console.log('Success:',
        message);
        // 这里可以集成toast通知组件
    }
    showError(message) {
        console.error('Error:',
        message);
        // 这里可以集成toast通知组件
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = TOTPManager;
} else if (typeof window !== "undefined") {
    window.TOTPManager = TOTPManager;
}

console.log('📦 TOTPManager 模块加载完成');
