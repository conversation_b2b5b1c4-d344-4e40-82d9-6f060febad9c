/**
 * API测试器
 * 从ApiModule.js提取的API测试相关功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class ApiTester {
    constructor() {
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.testHistory = [];
        
        console.log('✅ ApiTester initialized');
    }

    /**
     * 统一渲染方法 - 路由系统调用
     */
    async render(container, params = {}) {
        try {
            console.log('🎯 ApiTester渲染 - 参数:', params);
            
            // 显示加载状态
            container.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><div class="mt-2">正在加载API测试工具...</div></div>';
            
            // 生成HTML内容
            container.innerHTML = this.generateTestingContent();
            
            // 设置全局引用以便HTML中的onclick调用
            window.apiModule = this;
            
        } catch (error) {
            console.error('❌ ApiTester渲染失败:', error);
            container.innerHTML = `<div class="alert alert-danger">API测试工具加载失败: ${error.message}</div>`;
        }
    }

    /**
     * 生成API测试相关的HTML内容
     */
    generateTestingContent() {
        return `
            <div class="row">
                <!-- 接口测试工具 -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-play-circle me-2"></i>API接口测试
                            </h6>
                        </div>
                        <div class="card-body">
                            <form id="apiTestForm">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label class="form-label">接口类型</label>
                                        <select class="form-select" id="apiType" onchange="apiModule.loadTestTemplate()">
                                            <option value="">选择接口类型</option>
                                            <option value="payment">支付接口</option>
                                            <option value="refund">退款接口</option>
                                            <option value="query">查询接口</option>
                                            <option value="callback">回调测试</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">请求方法</label>
                                        <select class="form-select" id="requestMethod">
                                            <option value="POST">POST</option>
                                            <option value="GET">GET</option>
                                            <option value="PUT">PUT</option>
                                            <option value="DELETE">DELETE</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">环境</label>
                                        <select class="form-select" id="environment">
                                            <option value="sandbox">测试环境</option>
                                            <option value="production">生产环境</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">请求URL</label>
                                    <input type="text" class="form-control" id="requestUrl" 
                                           placeholder="https://api.example.com/v1/payment">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">请求头 (Headers)</label>
                                    <textarea class="form-control" id="requestHeaders" rows="3" 
                                              placeholder='{"Content-Type": "application/json", "Authorization": "Bearer your-token"}'></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">请求参数 (Body)</label>
                                    <textarea class="form-control" id="requestBody" rows="6" 
                                              placeholder='{"amount": 100.00, "currency": "USD", "description": "测试支付"}'></textarea>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-primary" onclick="apiModule.sendApiTest()">
                                        <i class="bi bi-send me-2"></i>发送请求
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="apiModule.saveTestCase()">
                                        <i class="bi bi-bookmark me-2"></i>保存测试用例
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="apiModule.loadTestTemplate()">
                                        <i class="bi bi-file-text me-2"></i>加载模板
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 快速测试 -->
                <div class="col-lg-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-lightning me-2"></i>快速测试
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" onclick="apiModule.quickTest('payment')">
                                    <i class="bi bi-credit-card me-2"></i>测试支付
                                </button>
                                <button class="btn btn-outline-success" onclick="apiModule.quickTest('query')">
                                    <i class="bi bi-search me-2"></i>测试查询
                                </button>
                                <button class="btn btn-outline-warning" onclick="apiModule.quickTest('refund')">
                                    <i class="bi bi-arrow-counterclockwise me-2"></i>测试退款
                                </button>
                                <button class="btn btn-outline-info" onclick="apiModule.quickTest('callback')">
                                    <i class="bi bi-arrow-left-right me-2"></i>测试回调
                                </button>
                            </div>
                            
                            <hr class="my-3">
                            
                            <h6 class="text-muted">测试用例</h6>
                            <div class="list-group list-group-flush">
                                <a href="#" class="list-group-item list-group-item-action" onclick="apiModule.loadTestCase('basic_payment')">
                                    <i class="bi bi-play-circle me-2"></i>基础支付测试
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" onclick="apiModule.loadTestCase('batch_payment')">
                                    <i class="bi bi-layers me-2"></i>批量支付测试
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" onclick="apiModule.loadTestCase('error_handling')">
                                    <i class="bi bi-exclamation-triangle me-2"></i>错误处理测试
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 响应结果 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-code-square me-2"></i>响应结果
                    </h6>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary" onclick="apiModule.copyResponse()">
                            <i class="bi bi-clipboard me-1"></i>复制
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="apiModule.clearResponse()">
                            <i class="bi bi-trash me-1"></i>清空
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="responseContainer">
                        <div class="text-center py-4 text-muted">
                            <i class="bi bi-code-slash" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">等待API响应</h5>
                            <p>发送请求后，响应结果将在这里显示</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试历史 -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-clock-history me-2"></i>测试历史
                    </h6>
                    <button class="btn btn-sm btn-outline-secondary" onclick="apiModule.clearTestHistory()">
                        <i class="bi bi-trash me-1"></i>清空历史
                    </button>
                </div>
                <div class="card-body">
                    <div id="testHistoryContainer">
                        <div class="text-center py-4 text-muted">
                            <i class="bi bi-clock" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">暂无测试历史</h5>
                            <p>执行API测试后，历史记录将在这里显示</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 发送API测试请求
     */
    async sendApiTest() {
        const apiType = document.getElementById('apiType')?.value;
        const method = document.getElementById('requestMethod')?.value || 'POST';
        const url = document.getElementById('requestUrl')?.value?.trim();
        const headers = document.getElementById('requestHeaders')?.value?.trim();
        const body = document.getElementById('requestBody')?.value?.trim();

        if (!url) {
            this.showError('请输入请求URL');
            return;
        }

        try {
            // 解析请求头
            let parsedHeaders = {};
            if (headers) {
                try {
                    parsedHeaders = JSON.parse(headers);
                } catch (e) {
                    this.showError('请求头格式错误，请使用有效的JSON格式');
                    return;
                }
            }

            // 解析请求体
            let parsedBody = null;
            if (body && (method === 'POST' || method === 'PUT')) {
                try {
                    parsedBody = JSON.parse(body);
                } catch (e) {
                    this.showError('请求体格式错误，请使用有效的JSON格式');
                    return;
                }
            }

            console.log('🔄 发送API测试请求:', { method, url, headers: parsedHeaders, body: parsedBody });
            
            // 显示加载状态
            this.showResponseLoading();
            
            const startTime = Date.now();
            
            // 发送请求
            const response = await this.makeRequest(method, url, parsedHeaders, parsedBody);
            
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            console.log('✅ API测试响应:', response);
            
            // 显示响应结果
            this.showResponse(response, responseTime);
            
            // 添加到测试历史
            this.addTestHistory(apiType, method, response.status || 200, responseTime);
            
        } catch (error) {
            console.error('❌ API测试失败:', error);
            this.showError('API测试失败: ' + error.message);
            this.showResponse({ error: error.message }, 0);
        }
    }

    /**
     * 发送HTTP请求
     */
    async makeRequest(method, url, headers, body) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        };

        if (body && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch(url, options);
        
        const responseData = {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            data: null
        };

        try {
            responseData.data = await response.json();
        } catch (e) {
            responseData.data = await response.text();
        }

        return responseData;
    }

    /**
     * 显示响应加载状态
     */
    showResponseLoading() {
        const container = document.getElementById('responseContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">请求中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在发送请求...</p>
                </div>
            `;
        }
    }

    /**
     * 显示响应结果
     */
    showResponse(response, responseTime) {
        const container = document.getElementById('responseContainer');
        if (!container) return;

        const statusClass = response.status >= 200 && response.status < 300 ? 'success' : 'danger';
        const statusIcon = response.status >= 200 && response.status < 300 ? 'check-circle' : 'x-circle';

        container.innerHTML = `
            <div class="response-header mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge bg-${statusClass}">
                            <i class="bi bi-${statusIcon} me-1"></i>
                            ${response.status || 'Error'} ${response.statusText || ''}
                        </span>
                        <small class="text-muted ms-2">响应时间: ${responseTime}ms</small>
                    </div>
                    <small class="text-muted">${new Date().toLocaleString()}</small>
                </div>
            </div>
            
            <div class="response-tabs">
                <ul class="nav nav-tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#response-body">
                            响应体
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#response-headers">
                            响应头
                        </button>
                    </li>
                </ul>
                
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="response-body">
                        <pre class="bg-light p-3 mt-2"><code>${JSON.stringify(response.data, null, 2)}</code></pre>
                    </div>
                    <div class="tab-pane fade" id="response-headers">
                        <pre class="bg-light p-3 mt-2"><code>${JSON.stringify(response.headers, null, 2)}</code></pre>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 添加测试历史记录
     */
    addTestHistory(apiType, method, statusCode, responseTime) {
        const historyItem = {
            id: Date.now(),
            timestamp: new Date(),
            apiType: apiType || 'Unknown',
            method: method,
            statusCode: statusCode,
            responseTime: responseTime,
            success: statusCode >= 200 && statusCode < 300
        };

        this.testHistory.unshift(historyItem);
        
        // 限制历史记录数量
        if (this.testHistory.length > 50) {
            this.testHistory = this.testHistory.slice(0, 50);
        }

        this.updateTestHistoryDisplay();
    }

    /**
     * 更新测试历史显示
     */
    updateTestHistoryDisplay() {
        const container = document.getElementById('testHistoryContainer');
        if (!container) return;

        if (this.testHistory.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="bi bi-clock" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">暂无测试历史</h5>
                    <p>执行API测试后，历史记录将在这里显示</p>
                </div>
            `;
            return;
        }

        const historyHtml = this.testHistory.map(item => {
            const statusClass = item.success ? 'success' : 'danger';
            const statusIcon = item.success ? 'check-circle' : 'x-circle';
            
            return `
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-${statusClass} me-2">
                                <i class="bi bi-${statusIcon}"></i>
                            </span>
                            <strong>${item.method}</strong>
                            <span class="text-muted ms-2">${item.apiType}</span>
                        </div>
                        <small class="text-muted">
                            ${item.timestamp.toLocaleString()} • ${item.responseTime}ms
                        </small>
                    </div>
                    <div class="text-end">
                        <button class="btn btn-sm btn-outline-primary" 
                                onclick="apiModule.replayTest('${item.apiType}', '${item.method}')">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = `
            <div class="test-history-list">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">测试记录 (${this.testHistory.length})</h6>
                    <small class="text-muted">最近50条记录</small>
                </div>
                ${historyHtml}
            </div>
        `;
    }

    /**
     * 加载测试模板
     */
    loadTestTemplate() {
        const apiType = document.getElementById('apiType')?.value;
        if (!apiType) return;

        const templates = {
            payment: {
                url: '/api/payment',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': '{{api_key}}'
                },
                body: {
                    amount: 100.00,
                    currency: 'USD',
                    description: '测试支付',
                    merchant_id: '{{merchant_id}}'
                }
            },
            refund: {
                url: '/api/refund',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': '{{api_key}}'
                },
                body: {
                    transaction_id: 'TXN123456',
                    amount: 50.00,
                    reason: '客户要求退款'
                }
            },
            query: {
                url: '/api/query',
                method: 'GET',
                headers: {
                    'X-API-Key': '{{api_key}}'
                },
                body: null
            },
            callback: {
                url: '/api/callback/test',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: {
                    event: 'payment.completed',
                    data: {
                        transaction_id: 'TXN123456',
                        status: 'completed'
                    }
                }
            }
        };

        const template = templates[apiType];
        if (template) {
            // 填充表单
            document.getElementById('requestUrl').value = template.url;
            document.getElementById('requestMethod').value = template.method;
            document.getElementById('requestHeaders').value = JSON.stringify(template.headers, null, 2);
            document.getElementById('requestBody').value = template.body ? JSON.stringify(template.body, null, 2) : '';
            
            this.showSuccess('模板已加载');
        }
    }

    /**
     * 保存测试用例
     */
    saveTestCase() {
        const testCase = {
            name: prompt('请输入测试用例名称:'),
            apiType: document.getElementById('apiType')?.value,
            method: document.getElementById('requestMethod')?.value,
            url: document.getElementById('requestUrl')?.value,
            headers: document.getElementById('requestHeaders')?.value,
            body: document.getElementById('requestBody')?.value,
            timestamp: new Date()
        };

        if (!testCase.name) return;

        // 保存到localStorage
        const savedCases = JSON.parse(localStorage.getItem('api_test_cases') || '[]');
        savedCases.push(testCase);
        localStorage.setItem('api_test_cases', JSON.stringify(savedCases));
        
        this.showSuccess('测试用例已保存');
    }

    /**
     * 复制响应结果
     */
    copyResponse() {
        const responseContainer = document.getElementById('responseContainer');
        if (responseContainer) {
            const responseText = responseContainer.innerText;
            navigator.clipboard.writeText(responseText).then(() => {
                this.showSuccess('响应结果已复制到剪贴板');
            }).catch(() => {
                this.showError('复制失败');
            });
        }
    }

    /**
     * 清空响应结果
     */
    clearResponse() {
        const container = document.getElementById('responseContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="bi bi-code-slash" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">等待API响应</h5>
                    <p>发送请求后，响应结果将在这里显示</p>
                </div>
            `;
        }
    }

    /**
     * 清空测试历史
     */
    clearTestHistory() {
        if (confirm('确定要清空所有测试历史吗？')) {
            this.testHistory = [];
            this.updateTestHistoryDisplay();
            this.showSuccess('测试历史已清空');
        }
    }

    /**
     * 重放测试
     */
    replayTest(apiType, method) {
        // 这里可以根据历史记录重新填充表单
        document.getElementById('apiType').value = apiType;
        document.getElementById('requestMethod').value = method;
        this.loadTestTemplate();
        this.showSuccess('测试参数已重新加载');
    }

    /**
     * 快速测试
     */
    quickTest(apiType) {
        document.getElementById('apiType').value = apiType;
        this.loadTestTemplate();
        // 自动发送请求
        setTimeout(() => {
            this.sendApiTest();
        }, 500);
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        if (window.apiModule && window.apiModule.showSuccess && window.apiModule.showSuccess !== this.showSuccess) {
            window.apiModule.showSuccess(message);
        } else {
            console.log('✅ 成功:', message);
            // 简单的成功提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-check-circle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        if (window.apiModule && window.apiModule.showError && window.apiModule.showError !== this.showError) {
            window.apiModule.showError(message);
        } else {
            console.error('❌ 错误:', message);
            // 简单的错误提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }
    }
}

// 导出类
window.ApiTester = ApiTester;