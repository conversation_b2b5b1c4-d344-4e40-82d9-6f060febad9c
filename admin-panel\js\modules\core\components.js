/**
 * 公共UI组件库
 * 提供可复用的界面组件，统一UI风格和交互
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class Components {
    constructor() {
        this.componentRegistry = new Map();
        this.templates = new Map();
        this.styles = new Map();
        
        console.log('🧩 Components library initialized');
        this.initializeDefaultComponents();
    }

    /**
     * 初始化默认组件
     */
    initializeDefaultComponents() {
        // 注册基础组件
        this.registerComponent('loading', this.createLoadingComponent);
        this.registerComponent('modal', this.createModalComponent);
        this.registerComponent('toast', this.createToastComponent);
        this.registerComponent('table', this.createTableComponent);
        this.registerComponent('form', this.createFormComponent);
        this.registerComponent('pagination', this.createPaginationComponent);
        this.registerComponent('search', this.createSearchComponent);
        this.registerComponent('card', this.createCardComponent);
        this.registerComponent('tabs', this.createTabsComponent);
        this.registerComponent('dropdown', this.createDropdownComponent);
    }

    /**
     * 注册组件
     */
    registerComponent(name, factory) {
        this.componentRegistry.set(name, factory);
    }

    /**
     * 创建组件实例
     */
    create(componentName, options = {}) {
        const factory = this.componentRegistry.get(componentName);
        if (!factory) {
            throw new Error(`组件 ${componentName} 未注册`);
        }
        
        return factory.call(this, options);
    }

    /**
     * 加载组件 - Loading Spinner
     */
    createLoadingComponent(options = {}) {
        const {
            size = 'medium',
            text = '加载中...',
            overlay = false,
            container = null
        } = options;
        
        const sizeClass = {
            small: 'loading-sm',
            medium: 'loading-md',
            large: 'loading-lg'
        }[size] || 'loading-md';
        
        const html = `
            <div class="loading-component ${overlay ? 'loading-overlay' : ''}" data-component="loading">
                <div class="loading-spinner ${sizeClass}">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">${text}</span>
                    </div>
                </div>
                ${text ? `<div class="loading-text">${text}</div>` : ''}
            </div>
        `;
        
        const element = this.createElement(html);
        
        return {
            element,
            show() {
                element.style.display = 'flex';
                if (container) {
                    container.appendChild(element);
                }
            },
            hide() {
                element.style.display = 'none';
            },
            destroy() {
                element.remove();
            }
        };
    }

    /**
     * 模态框组件
     */
    createModalComponent(options = {}) {
        const {
            title = '',
            content = '',
            size = 'medium',
            closable = true,
            backdrop = true,
            keyboard = true
        } = options;
        
        const sizeClass = {
            small: 'modal-sm',
            medium: '',
            large: 'modal-lg',
            xl: 'modal-xl'
        }[size] || '';
        
        const html = `
            <div class="modal fade" tabindex="-1" data-component="modal">
                <div class="modal-dialog ${sizeClass}">
                    <div class="modal-content">
                        ${title ? `
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                ${closable ? '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' : ''}
                            </div>
                        ` : ''}
                        <div class="modal-body">
                            ${content}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        const element = this.createElement(html);
        document.body.appendChild(element);
        
        // 使用Bootstrap Modal
        const modal = new bootstrap.Modal(element, {
            backdrop: backdrop,
            keyboard: keyboard
        });
        
        return {
            element,
            modal,
            show() {
                modal.show();
            },
            hide() {
                modal.hide();
            },
            setContent(newContent) {
                const body = element.querySelector('.modal-body');
                if (body) body.innerHTML = newContent;
            },
            setTitle(newTitle) {
                const titleEl = element.querySelector('.modal-title');
                if (titleEl) titleEl.textContent = newTitle;
            },
            destroy() {
                modal.dispose();
                element.remove();
            }
        };
    }

    /**
     * Toast 通知组件
     */
    createToastComponent(options = {}) {
        const {
            message = '',
            type = 'info',
            duration = 3000,
            position = 'top-right'
        } = options;
        
        const typeClass = {
            success: 'toast-success',
            error: 'toast-error',
            warning: 'toast-warning',
            info: 'toast-info'
        }[type] || 'toast-info';
        
        const html = `
            <div class="toast ${typeClass}" role="alert" data-component="toast">
                <div class="toast-header">
                    <strong class="me-auto">${this.getToastTitle(type)}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;
        
        const element = this.createElement(html);
        
        // 确保toast容器存在
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = `toast-container position-fixed ${position}`;
            document.body.appendChild(container);
        }
        
        container.appendChild(element);
        
        const toast = new bootstrap.Toast(element, {
            autohide: duration > 0,
            delay: duration
        });
        
        return {
            element,
            toast,
            show() {
                toast.show();
            },
            hide() {
                toast.hide();
            },
            destroy() {
                toast.dispose();
                element.remove();
            }
        };
    }

    /**
     * 表格组件
     */
    createTableComponent(options = {}) {
        const {
            columns = [],
            data = [],
            pagination = false,
            search = false,
            actions = []
        } = options;
        
        const tableId = `table-${Date.now()}`;
        
        let html = `
            <div class="table-component" data-component="table">
                ${search ? `
                    <div class="table-search mb-3">
                        <input type="text" class="form-control" placeholder="搜索...">
                    </div>
                ` : ''}
                <div class="table-responsive">
                    <table class="table table-striped" id="${tableId}">
                        <thead>
                            <tr>
                                ${columns.map(col => `<th>${col.title || col.key}</th>`).join('')}
                                ${actions.length > 0 ? '<th>操作</th>' : ''}
                            </tr>
                        </thead>
                        <tbody>
                            ${this.renderTableRows(data, columns, actions)}
                        </tbody>
                    </table>
                </div>
                ${pagination ? '<div class="table-pagination"></div>' : ''}
            </div>
        `;
        
        const element = this.createElement(html);
        
        return {
            element,
            setData(newData) {
                const tbody = element.querySelector('tbody');
                if (tbody) {
                    tbody.innerHTML = this.renderTableRows(newData, columns, actions);
                }
            },
            refresh() {
                // 触发数据刷新事件
                if (window.eventBus) {
                    window.eventBus.emit('table:refresh', { tableId });
                }
            },
            destroy() {
                element.remove();
            }
        };
    }

    /**
     * 渲染表格行
     */
    renderTableRows(data, columns, actions) {
        return data.map(row => {
            const cells = columns.map(col => {
                const value = row[col.key] || '';
                return `<td>${col.render ? col.render(value, row) : value}</td>`;
            }).join('');
            
            const actionCells = actions.length > 0 ? `
                <td>
                    ${actions.map(action => `
                        <button class="btn btn-sm btn-${action.type || 'primary'}" 
                                onclick="${action.handler}('${row.id || row.key}')">
                            ${action.text}
                        </button>
                    `).join(' ')}
                </td>
            ` : '';
            
            return `<tr>${cells}${actionCells}</tr>`;
        }).join('');
    }

    /**
     * 分页组件
     */
    createPaginationComponent(options = {}) {
        const {
            total = 0,
            pageSize = 20,
            current = 1,
            showSizeChanger = true,
            showQuickJumper = true
        } = options;
        
        const totalPages = Math.ceil(total / pageSize);
        
        const html = `
            <div class="pagination-component d-flex justify-content-between align-items-center" data-component="pagination">
                <div class="pagination-info">
                    共 ${total} 条记录，第 ${current} / ${totalPages} 页
                </div>
                <nav>
                    <ul class="pagination mb-0">
                        <li class="page-item ${current <= 1 ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${current - 1}">上一页</a>
                        </li>
                        ${this.renderPaginationPages(current, totalPages)}
                        <li class="page-item ${current >= totalPages ? 'disabled' : ''}">
                            <a class="page-link" href="#" data-page="${current + 1}">下一页</a>
                        </li>
                    </ul>
                </nav>
                ${showSizeChanger ? `
                    <select class="form-select form-select-sm" style="width: auto;">
                        <option value="10" ${pageSize === 10 ? 'selected' : ''}>10条/页</option>
                        <option value="20" ${pageSize === 20 ? 'selected' : ''}>20条/页</option>
                        <option value="50" ${pageSize === 50 ? 'selected' : ''}>50条/页</option>
                        <option value="100" ${pageSize === 100 ? 'selected' : ''}>100条/页</option>
                    </select>
                ` : ''}
            </div>
        `;
        
        const element = this.createElement(html);
        
        // 绑定分页点击事件
        element.addEventListener('click', (e) => {
            if (e.target.classList.contains('page-link') && !e.target.closest('.disabled')) {
                e.preventDefault();
                const page = parseInt(e.target.dataset.page);
                if (page && page !== current) {
                    if (window.eventBus) {
                        window.eventBus.emit('pagination:change', { page, pageSize });
                    }
                }
            }
        });
        
        return {
            element,
            updatePagination(newOptions) {
                // 重新渲染分页组件
                const newComponent = this.createPaginationComponent(newOptions);
                element.replaceWith(newComponent.element);
                return newComponent;
            },
            destroy() {
                element.remove();
            }
        };
    }

    /**
     * 渲染分页页码
     */
    renderPaginationPages(current, total) {
        const pages = [];
        const maxVisible = 5;
        
        let start = Math.max(1, current - Math.floor(maxVisible / 2));
        let end = Math.min(total, start + maxVisible - 1);
        
        if (end - start + 1 < maxVisible) {
            start = Math.max(1, end - maxVisible + 1);
        }
        
        for (let i = start; i <= end; i++) {
            pages.push(`
                <li class="page-item ${i === current ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}">${i}</a>
                </li>
            `);
        }
        
        return pages.join('');
    }

    /**
     * 搜索组件
     */
    createSearchComponent(options = {}) {
        const {
            placeholder = '请输入搜索关键词',
            filters = [],
            onSearch = null
        } = options;
        
        const html = `
            <div class="search-component" data-component="search">
                <div class="row g-3">
                    <div class="col-md-6">
                        <input type="text" class="form-control search-input" placeholder="${placeholder}">
                    </div>
                    ${filters.map(filter => `
                        <div class="col-md-3">
                            <select class="form-select" data-filter="${filter.key}">
                                <option value="">${filter.placeholder || '全部'}</option>
                                ${filter.options.map(opt => `
                                    <option value="${opt.value}">${opt.label}</option>
                                `).join('')}
                            </select>
                        </div>
                    `).join('')}
                    <div class="col-md-3">
                        <button type="button" class="btn btn-primary search-btn">搜索</button>
                        <button type="button" class="btn btn-outline-secondary reset-btn ms-2">重置</button>
                    </div>
                </div>
            </div>
        `;
        
        const element = this.createElement(html);
        
        // 绑定搜索事件
        const searchBtn = element.querySelector('.search-btn');
        const resetBtn = element.querySelector('.reset-btn');
        const searchInput = element.querySelector('.search-input');
        
        searchBtn.addEventListener('click', () => {
            const searchData = this.getSearchData(element);
            if (onSearch) onSearch(searchData);
            if (window.eventBus) {
                window.eventBus.emit('search:submit', searchData);
            }
        });
        
        resetBtn.addEventListener('click', () => {
            this.resetSearch(element);
            if (onSearch) onSearch({});
            if (window.eventBus) {
                window.eventBus.emit('search:reset');
            }
        });
        
        // 回车搜索
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                searchBtn.click();
            }
        });
        
        return {
            element,
            getSearchData: () => this.getSearchData(element),
            reset: () => this.resetSearch(element),
            destroy() {
                element.remove();
            }
        };
    }

    /**
     * 获取搜索数据
     */
    getSearchData(searchElement) {
        const data = {};
        
        const searchInput = searchElement.querySelector('.search-input');
        if (searchInput && searchInput.value.trim()) {
            data.keyword = searchInput.value.trim();
        }
        
        const selects = searchElement.querySelectorAll('select[data-filter]');
        selects.forEach(select => {
            if (select.value) {
                data[select.dataset.filter] = select.value;
            }
        });
        
        return data;
    }

    /**
     * 重置搜索
     */
    resetSearch(searchElement) {
        const searchInput = searchElement.querySelector('.search-input');
        if (searchInput) searchInput.value = '';
        
        const selects = searchElement.querySelectorAll('select[data-filter]');
        selects.forEach(select => {
            select.selectedIndex = 0;
        });
    }

    /**
     * 工具方法
     */
    createElement(html) {
        const template = document.createElement('template');
        template.innerHTML = html.trim();
        return template.content.firstChild;
    }

    getToastTitle(type) {
        const titles = {
            success: '成功',
            error: '错误',
            warning: '警告',
            info: '提示'
        };
        return titles[type] || '提示';
    }

    /**
     * 显示Toast通知
     */
    showToast(message, type = 'info', duration = 3000) {
        const toast = this.create('toast', { message, type, duration });
        toast.show();
        return toast;
    }

    /**
     * 显示确认对话框
     */
    showConfirm(title, content, onConfirm, onCancel) {
        const confirmContent = `
            ${content}
            <div class="mt-3 text-end">
                <button type="button" class="btn btn-secondary me-2" data-action="cancel">取消</button>
                <button type="button" class="btn btn-primary" data-action="confirm">确认</button>
            </div>
        `;
        
        const modal = this.create('modal', {
            title,
            content: confirmContent,
            closable: true
        });
        
        modal.element.addEventListener('click', (e) => {
            if (e.target.dataset.action === 'confirm') {
                if (onConfirm) onConfirm();
                modal.hide();
            } else if (e.target.dataset.action === 'cancel') {
                if (onCancel) onCancel();
                modal.hide();
            }
        });
        
        modal.show();
        return modal;
    }
}

// 创建全局组件库实例
if (typeof window !== 'undefined') {
    window.Components = Components;
    window.UI = new Components();
    
    console.log('🎯 公共组件库已就绪');
} 