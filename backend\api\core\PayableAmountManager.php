<?php
/**
 * 用户应付金额管理器
 * 
 * 功能：
 * 1. 检测并发冲突
 * 2. 生成用户应付金额
 * 3. 管理金额差额
 * 4. 确保唯一性
 * 
 * <AUTHOR> System
 * @version 1.0
 */

require_once dirname(dirname(__FILE__)) . '/config/database.php';

class PayableAmountManager {
    
    private $db;
    
    // 用户应付金额范围配置
    const MIN_DIFF = 0.01;  // 最小差额 0.01元
    const MAX_DIFF = 0.50;  // 最大差额 0.50元
    
    // 并发检测时间窗口（秒）
    const CONFLICT_WINDOW = 60;  // 1分钟内检测冲突
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * 为订单生成用户应付金额
     * 
     * @param float $orderAmount 订单金额
     * @param int $alipayAccountId 支付宝账户ID
     * @param int $merchantId 商户ID
     * @return array 生成结果
     */
    public function generatePayableAmount($orderAmount, $alipayAccountId, $merchantId) {
        try {
            // 1. 检测并发冲突
            $hasConflict = $this->detectConcurrencyConflict($orderAmount, $alipayAccountId);
            
            if (!$hasConflict) {
                // 无冲突，用户应付金额等于订单金额
                return [
                    'success' => true,
                    'payable_amount' => $orderAmount,
                    'amount_diff' => 0.00,
                    'conflict_detected' => false,
                    'generation_strategy' => 'normal'
                ];
            }
            
            // 2. 有冲突，生成差异化用户应付金额
            $payableAmount = $this->generateUniquePayableAmount($orderAmount, $alipayAccountId);
            
            if (!$payableAmount) {
                throw new Exception('无法生成唯一的用户应付金额');
            }
            
            $amountDiff = $orderAmount - $payableAmount;
            
            return [
                'success' => true,
                'payable_amount' => $payableAmount,
                'amount_diff' => $amountDiff,
                'conflict_detected' => true,
                'generation_strategy' => 'differentiated',
                'payable_range_min' => $orderAmount - self::MAX_DIFF,
                'payable_range_max' => $orderAmount - self::MIN_DIFF
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'payable_amount' => $orderAmount,
                'amount_diff' => 0.00,
                'conflict_detected' => false
            ];
        }
    }
    
    /**
     * 检测并发冲突
     * 
     * @param float $amount 订单金额
     * @param int $alipayAccountId 支付宝账户ID
     * @return bool 是否存在冲突
     */
    private function detectConcurrencyConflict($amount, $alipayAccountId) {
        $timeWindow = date('Y-m-d H:i:s', time() - self::CONFLICT_WINDOW);
        
        // 查询时间窗口内相同金额的待支付订单
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as conflict_count
            FROM payment_requests 
            WHERE amount = ? 
            AND alipay_account_id = ? 
            AND status = 'pending'
            AND created_at >= ?
        ");
        
        $stmt->execute([$amount, $alipayAccountId, $timeWindow]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['conflict_count'] > 0;
    }
    
    /**
     * 生成唯一的用户应付金额
     * 
     * @param float $orderAmount 订单金额
     * @param int $alipayAccountId 支付宝账户ID
     * @return float|false 生成的用户应付金额，失败返回false
     */
    private function generateUniquePayableAmount($orderAmount, $alipayAccountId) {
        $maxAttempts = 50; // 最多尝试50次
        $timeWindow = date('Y-m-d H:i:s', time() - self::CONFLICT_WINDOW);
        
        for ($i = 0; $i < $maxAttempts; $i++) {
            // 生成随机差额
            $diff = mt_rand(self::MIN_DIFF * 100, self::MAX_DIFF * 100) / 100;
            $payableAmount = round($orderAmount - $diff, 2);
            
            // 检查该金额是否已被使用
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count
                FROM payment_requests 
                WHERE payable_amount = ? 
                AND alipay_account_id = ? 
                AND status = 'pending'
                AND payable_generated_at >= ?
            ");
            
            $stmt->execute([$payableAmount, $alipayAccountId, $timeWindow]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['count'] == 0) {
                return $payableAmount;
            }
        }
        
        return false; // 无法生成唯一金额
    }
    
    /**
     * 验证用户应付金额的有效性
     * 
     * @param float $payableAmount 用户应付金额
     * @param float $orderAmount 订单金额
     * @return array 验证结果
     */
    public function validatePayableAmount($payableAmount, $orderAmount) {
        $diff = $orderAmount - $payableAmount;
        
        // 检查金额差额是否在允许范围内
        if ($diff < 0 || $diff > self::MAX_DIFF) {
            return [
                'valid' => false,
                'error' => '用户应付金额超出允许范围'
            ];
        }
        
        // 检查金额精度（保留2位小数）
        if (round($payableAmount, 2) != $payableAmount) {
            return [
                'valid' => false,
                'error' => '用户应付金额精度错误'
            ];
        }
        
        return [
            'valid' => true,
            'amount_diff' => $diff
        ];
    }
    
    /**
     * 获取用户应付金额统计信息
     * 
     * @param int $alipayAccountId 支付宝账户ID（可选）
     * @param string $dateRange 日期范围（可选）
     * @return array 统计结果
     */
    public function getPayableAmountStats($alipayAccountId = null, $dateRange = '1 day') {
        $whereClause = "WHERE payable_amount IS NOT NULL";
        $params = [];
        
        if ($alipayAccountId) {
            $whereClause .= " AND alipay_account_id = ?";
            $params[] = $alipayAccountId;
        }
        
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$dateRange}"));
        $whereClause .= " AND created_at >= ?";
        $params[] = $timeLimit;
        
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN amount_diff > 0 THEN 1 END) as differentiated_orders,
                AVG(amount_diff) as avg_diff,
                SUM(amount_diff) as total_diff,
                MIN(amount_diff) as min_diff,
                MAX(amount_diff) as max_diff
            FROM payment_requests 
            {$whereClause}
        ");
        
        $stmt->execute($params);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 计算差异化比例
        $stats['differentiation_rate'] = $stats['total_orders'] > 0 
            ? round($stats['differentiated_orders'] / $stats['total_orders'] * 100, 2) 
            : 0;
            
        return $stats;
    }
    
    /**
     * 清理过期的用户应付金额占用记录
     * 
     * @param int $expireMinutes 过期时间（分钟）
     * @return int 清理的记录数
     */
    public function cleanupExpiredPayableAmounts($expireMinutes = 30) {
        $expireTime = date('Y-m-d H:i:s', time() - ($expireMinutes * 60));
        
        $stmt = $this->db->prepare("
            UPDATE payment_requests 
            SET status = 'expired',
                account_released_at = NOW()
            WHERE status = 'pending' 
            AND payable_generated_at < ?
        ");
        
        $stmt->execute([$expireTime]);
        
        return $stmt->rowCount();
    }
}
?> 