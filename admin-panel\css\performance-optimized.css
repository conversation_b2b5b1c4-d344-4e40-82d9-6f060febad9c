/* 性能优化的CSS样式 */

/* 基础重置和优化 */
* {
    box-sizing: border-box;
}

/* 减少重绘的优化 */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* 主容器布局 */
.main-container {
    display: flex;
    min-height: 100vh;
    background: #f8fafc;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
    color: white;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 1000;
    box-shadow: 4px 0 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255,255,255,0.3) transparent;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar.mobile-hidden {
    transform: translateX(-100%);
}

/* 侧边栏头部 */
.sidebar-header {
    padding: 30px 25px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 20px;
}

.logo i {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.logo h4 {
    margin: 0;
    font-weight: 300;
    letter-spacing: 1px;
}

.user-info {
    display: flex;
    align-items: center;
    padding: 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    margin-top: 20px;
}

.user-avatar {
    font-size: 2rem;
    margin-right: 12px;
    opacity: 0.9;
}

.user-details {
    flex: 1;
    text-align: left;
}

.user-name {
    font-weight: 500;
    font-size: 0.95rem;
    margin-bottom: 2px;
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.7;
    color: #94a3b8;
}

/* 侧边栏菜单 */
.sidebar-menu {
    padding: 20px 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    border-left-color: #3b82f6;
}

.menu-item.active {
    background: rgba(59, 130, 246, 0.2);
    color: white;
    border-left-color: #3b82f6;
}

.menu-item i {
    width: 20px;
    margin-right: 15px;
    font-size: 1.1rem;
}

.menu-item span {
    flex: 1;
    font-weight: 400;
}

.loading-indicator {
    margin-left: auto;
}

.loading-indicator .spinner-border {
    width: 16px;
    height: 16px;
    border-width: 2px;
}

/* 侧边栏底部 */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px 25px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.performance-indicator {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
}

.indicator-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.indicator-item:last-child {
    margin-bottom: 0;
}

.indicator-label {
    font-size: 0.8rem;
    opacity: 0.7;
}

.indicator-value {
    font-size: 0.9rem;
    font-weight: 500;
}

.indicator-value.text-success {
    color: #10b981;
}

.indicator-value.text-warning {
    color: #f59e0b;
}

.indicator-value.text-danger {
    color: #ef4444;
}

/* 主内容区 */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

.sidebar.collapsed + .main-content {
    margin-left: 0;
}

/* 头部样式 */
.header {
    background: white;
    padding: 20px 30px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.header-left {
    display: flex;
    align-items: center;
}

.sidebar-toggle {
    display: none;
    margin-right: 15px;
}

.breadcrumb-container {
    margin-left: 15px;
}

.breadcrumb {
    margin: 0;
    background: none;
    padding: 0;
}

.breadcrumb-item {
    font-size: 0.9rem;
}

.breadcrumb-item a {
    color: #6b7280;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #3b82f6;
}

.breadcrumb-item.active {
    color: #374151;
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 2px;
}

.stat-value {
    display: block;
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 30px;
    position: relative;
    overflow-y: auto;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* 虚拟滚动表格 */
.virtual-table-container {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.virtual-table-header {
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.virtual-table-header-row {
    min-height: 50px;
}

.virtual-table-header-cell {
    padding: 12px 16px;
    font-weight: 600;
    color: #374151;
    border-right: 1px solid #e5e7eb;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.virtual-table-header-cell:hover {
    background: #f3f4f6;
}

.virtual-table-header-cell:last-child {
    border-right: none;
}

.virtual-table-body {
    height: 400px;
    overflow-y: auto;
}

.virtual-table-row {
    min-height: 50px;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s ease;
}

.virtual-table-row:hover {
    background: #f8fafc;
}

.virtual-table-row:last-child {
    border-bottom: none;
}

.virtual-table-cell {
    padding: 12px 16px;
    color: #374151;
    border-right: 1px solid #f3f4f6;
    font-size: 0.9rem;
}

.virtual-table-cell:last-child {
    border-right: none;
}

/* 模态框优化 */
.modal {
    backdrop-filter: blur(4px);
}

.modal-dialog {
    margin: 1.75rem auto;
    max-width: 90vw;
}

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px 24px;
    border-top: 1px solid #e5e7eb;
}

/* 按钮优化 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
    border-color: #2563eb;
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: #3b82f6;
    border-color: #3b82f6;
}

.btn-outline-primary:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-1px);
}

/* 表单优化 */
.form-control {
    border-radius: 6px;
    border: 1px solid #d1d5db;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control.is-valid {
    border-color: #10b981;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2310b981' d='m2.3 6.73.94-.94 1.44 1.44L7.88 4.05l.94.94-2.88 2.88z'/%3e%3c/svg%3e");
}

.form-control.is-invalid {
    border-color: #ef4444;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ef4444'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.5 5.5 1 1m0 0 1 1m-1-1 1-1m-1 1-1 1'/%3e%3c/svg%3e");
}

/* 卡片优化 */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;
}

.card-body {
    padding: 24px;
}

/* 警告框优化 */
.alert {
    border: none;
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: #ecfdf5;
    color: #065f46;
    border-left: 4px solid #10b981;
}

.alert-warning {
    background: #fffbeb;
    color: #92400e;
    border-left: 4px solid #f59e0b;
}

.alert-danger {
    background: #fef2f2;
    color: #991b1b;
    border-left: 4px solid #ef4444;
}

.alert-info {
    background: #eff6ff;
    color: #1e40af;
    border-left: 4px solid #3b82f6;
}

/* Toast通知优化 */
.toast {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(8px);
}

/* 加载动画优化 */
.spinner-border {
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .header-stats {
        display: none;
    }
    
    .content-area {
        padding: 20px 15px;
    }
    
    .modal-dialog {
        margin: 1rem;
    }
}

@media (max-width: 576px) {
    .header {
        padding: 15px 20px;
    }
    
    .header-actions {
        gap: 5px;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .virtual-table-header-cell,
    .virtual-table-cell {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}

/* 性能优化类 */
.will-change-transform {
    will-change: transform;
}

.will-change-scroll {
    will-change: scroll-position;
}

.contain-layout {
    contain: layout;
}

.contain-paint {
    contain: paint;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .main-container {
        background: #111827;
        color: #f9fafb;
    }
    
    .header {
        background: #1f2937;
        border-bottom-color: #374151;
    }
    
    .card {
        background: #1f2937;
        color: #f9fafb;
    }
    
    .card-header {
        background: #374151;
        border-bottom-color: #4b5563;
    }
    
    .form-control {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
    
    .form-control:focus {
        background: #374151;
        border-color: #3b82f6;
    }
    
    .virtual-table-container {
        background: #1f2937;
        border-color: #374151;
    }
    
    .virtual-table-header {
        background: #374151;
        border-bottom-color: #4b5563;
    }
    
    .virtual-table-header-cell {
        color: #f9fafb;
        border-right-color: #4b5563;
    }
    
    .virtual-table-row {
        border-bottom-color: #374151;
    }
    
    .virtual-table-row:hover {
        background: #374151;
    }
    
    .virtual-table-cell {
        color: #e5e7eb;
        border-right-color: #374151;
    }
}

/* 打印样式优化 */
@media print {
    .sidebar,
    .header-actions,
    .loading-overlay {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .content-area {
        padding: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
    
    .btn {
        display: none !important;
    }
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .btn {
        border-width: 2px;
    }
    
    .form-control {
        border-width: 2px;
    }
    
    .card {
        border: 2px solid #000;
    }
} 