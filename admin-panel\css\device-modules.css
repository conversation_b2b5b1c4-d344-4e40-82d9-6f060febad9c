/**
 * 设备管理模块样式文件
 * 为设备管理、小组管理、签到监控等模块提供统一样式
 */

/* ==================== 基础容器样式 ==================== */
.device-module-container {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
}

.device-module-nav {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1.5rem;
}

.module-content {
    min-height: 600px;
}

/* ==================== 统计卡片样式 ==================== */
.stat-card {
    border: 1px solid;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin: 0;
}

/* ==================== 表格样式增强 ==================== */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.table thead th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.table-responsive {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* ==================== 设备状态徽章 ==================== */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 4px;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-warning {
    background-color: #fd7e14 !important;
    color: #fff;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
}

/* ==================== 进度条样式 ==================== */
.progress {
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* ==================== 按钮组样式 ==================== */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 4px;
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover,
.btn-outline-danger:hover,
.btn-outline-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

/* ==================== 卡片样式增强 ==================== */
.card {
    border: 1px solid #e3e6f0;
    border-radius: 8px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.05);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.1);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
    font-weight: 600;
}

.card-header h6 {
    margin: 0;
    color: #5a5c69;
}

/* ==================== 表单控件样式 ==================== */
.form-control,
.form-select {
    border: 1px solid #d1d3e2;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

/* ==================== 模态框样式 ==================== */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
    padding: 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: #5a5c69;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
}

/* ==================== 加载状态样式 ==================== */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

#loadingIndicator,
#groupLoadingIndicator,
#checkinLoadingIndicator {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* ==================== 空数据状态样式 ==================== */
#noDataIndicator,
#groupNoDataIndicator,
#checkinNoDataIndicator {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

/* ==================== 分页样式 ==================== */
.pagination {
    margin: 0;
}

.page-link {
    color: #5a5c69;
    border: 1px solid #d1d3e2;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    margin: 0 2px;
}

.page-link:hover {
    color: #224abe;
    background-color: #eaecf4;
    border-color: #d1d3e2;
}

.page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* ==================== 警告和提示样式 ==================== */
.alert {
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

/* ==================== 超时设备特殊样式 ==================== */
.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.table-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

.table-warning:hover {
    background-color: rgba(255, 193, 7, 0.2);
}

.table-danger:hover {
    background-color: rgba(220, 53, 69, 0.2);
}

/* ==================== 签到状态特殊样式 ==================== */
.checkin-status-normal {
    color: #198754;
    font-weight: 600;
}

.checkin-status-overdue {
    color: #fd7e14;
    font-weight: 600;
}

.checkin-status-critical {
    color: #dc3545;
    font-weight: 700;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

/* ==================== 设备指纹显示 ==================== */
.device-fingerprint {
    font-family: Monaco, Consolas, 'Courier New', monospace;
    font-size: 0.8rem;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}

/* ==================== DNS记录显示 ==================== */
.dns-record {
    font-family: Monaco, Consolas, 'Courier New', monospace;
    font-size: 0.8rem;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 0.5rem;
    word-break: break-all;
    overflow-x: auto;
}

/* ==================== 小组识别码样式 ==================== */
.group-code {
    font-family: Monaco, Consolas, 'Courier New', monospace;
    font-weight: 700;
    color: #495057;
    background-color: #e9ecef;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
}

/* ==================== 操作日志样式 ==================== */
.log-entry {
    border-left: 3px solid #dee2e6;
    padding-left: 1rem;
    margin-bottom: 0.5rem;
}

.log-entry.log-info {
    border-left-color: #0dcaf0;
}

.log-entry.log-warning {
    border-left-color: #ffc107;
}

.log-entry.log-error {
    border-left-color: #dc3545;
}

.log-entry.log-success {
    border-left-color: #198754;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .stat-value {
        font-size: 1.5rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .device-module-nav .nav-link {
        font-size: 0.85rem;
        padding: 0.5rem 0.75rem;
    }
    
    .stat-card {
        padding: 0.75rem;
    }
    
    .stat-value {
        font-size: 1.3rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
}

/* ==================== 打印样式 ==================== */
@media print {
    .btn,
    .btn-group,
    .pagination,
    .modal,
    .device-module-nav {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .table {
        border-collapse: collapse;
    }
    
    .table th,
    .table td {
        border: 1px solid #000;
    }
}

/* ==================== 深色模式支持 ==================== */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .card-header {
        background-color: #1a202c;
        border-bottom-color: #4a5568;
        color: #e2e8f0;
    }
    
    .table {
        color: #e2e8f0;
    }
    
    .table-light {
        background-color: #4a5568;
        color: #e2e8f0;
    }
    
    .form-control,
    .form-select {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .form-control:focus,
    .form-select:focus {
        background-color: #2d3748;
        border-color: #63b3ed;
        color: #e2e8f0;
    }
}

/* ==================== 自定义滚动条 ==================== */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ==================== 动画效果 ==================== */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ==================== 工具提示增强 ==================== */
[title] {
    cursor: help;
}

.tooltip-inner {
    max-width: 300px;
    text-align: left;
    font-size: 0.8rem;
}

/* ==================== 状态指示器 ==================== */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.status-active {
    background-color: #198754;
    box-shadow: 0 0 0 2px rgba(25, 135, 84, 0.3);
}

.status-indicator.status-inactive {
    background-color: #6c757d;
}

.status-indicator.status-warning {
    background-color: #fd7e14;
    animation: pulse 2s infinite;
}

.status-indicator.status-danger {
    background-color: #dc3545;
    animation: pulse 1.5s infinite;
} 