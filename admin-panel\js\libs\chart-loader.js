/**
 * Chart.js 加载器
 * 优先加载本地文件，失败时回退到CDN
 */
window.ChartLoader = {
    loaded: false,
    loading: false,
    callbacks: [],

    // 加载Chart.js
    load() {
        return new Promise((resolve, reject) => {
            // 如果已经加载完成
            if (this.loaded && window.Chart) {
                resolve(window.Chart);
                return;
            }

            // 如果正在加载中，添加到回调队列
            if (this.loading) {
                this.callbacks.push({ resolve, reject });
                return;
            }

            this.loading = true;
            this.callbacks.push({ resolve, reject });

            // 尝试加载本地文件
            this.loadLocal()
                .then(() => {
                    this.onLoadSuccess();
                })
                .catch(() => {
                    console.warn('本地Chart.js加载失败，尝试CDN...');
                    // 本地加载失败，尝试CDN
                    this.loadCDN()
                        .then(() => {
                            this.onLoadSuccess();
                        })
                        .catch((error) => {
                            this.onLoadError(error);
                        });
                });
        });
    },

    // 加载本地文件
    loadLocal() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.onload = resolve;
            script.onerror = reject;
            script.src = 'js/libs/chart.min.js';
            document.head.appendChild(script);
        });
    },

    // 加载CDN文件
    loadCDN() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.onload = resolve;
            script.onerror = reject;
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.1/chart.min.js';
            document.head.appendChild(script);
        });
    },

    // 加载成功回调
    onLoadSuccess() {
        this.loaded = true;
        this.loading = false;
        
        // 执行所有等待的回调
        this.callbacks.forEach(({ resolve }) => {
            resolve(window.Chart);
        });
        this.callbacks = [];
    },

    // 加载失败回调
    onLoadError(error) {
        this.loading = false;
        
        // 执行所有等待的回调
        this.callbacks.forEach(({ reject }) => {
            reject(error);
        });
        this.callbacks = [];
    }
};

// 预加载Chart.js（如果页面需要图表功能）
if (document.querySelector('[data-chart]') || 
    window.location.pathname.includes('dashboard') || 
    window.location.pathname.includes('stats')) {
    
    // 延迟加载，避免阻塞页面渲染
    setTimeout(() => {
        window.ChartLoader.load().catch(error => {
            console.error('Chart.js预加载失败:', error);
        });
    }, 1000);
} 