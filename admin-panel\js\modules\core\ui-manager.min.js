class UIManager{constructor(e,t=null){this.auth=e,this.tenantInfo=t,this.currentPage="dashboard",this.pageLoadHandlers=new Map,this.menuItems=[],this.isInitialized=!1,this.utils={showLoading:(e="加载中...")=>{this.showLoading(!0,e)},hideLoading:()=>{this.showLoading(!1)},showMessage:(e,t="info")=>{this.showMessage(e,t)}},document.addEventListener("mainInterfaceCreate",e=>{console.log("🎯 UI管理器接收到主界面创建事件:",e.detail),this.createMainInterface(e.detail.container,e.detail.user)})}createMainInterface(e,t){if(e&&t){console.log("🎨 正在创建主界面...",{user:t});try{console.log("📝 生成主界面HTML..."),e.innerHTML=this.generateMainInterfaceHTML(t),window.authManager=this.auth,console.log("⚙️ 初始化界面功能..."),this.initializeInterface(),this.isInitialized=!0,console.log("✅ 主界面创建完成")}catch(e){console.error("❌ 主界面创建失败:",e)}}else console.error("❌ 无法创建主界面：缺少必要参数",{container:e,user:t})}generateMainInterfaceHTML(e){return`
            ${this.generateMainStyles()}
            
            <div class="main-container">
                <!-- 侧边栏 -->
                <div class="sidebar">
                    <div class="sidebar-header">
                        <h4 class="mb-0">
                            <i class="bi bi-speedometer2"></i> ${this.getTenantDisplayName()}
                        </h4>
                        <small style="opacity: 0.7">
                            ${e.display_name||e.real_name||e.username}
                        </small>
                        <div style="font-size: 10px; opacity: 0.5; margin-top: 5px;">
                            ${this.getTenantTypeText()}
                        </div>
                    </div>
                    <nav class="sidebar-menu">
                        ${this.generateMenuItems(e.user_type)}
                        <div class="mt-auto">
                            <a href="#" class="menu-item logout-btn">
                                <i class="bi bi-box-arrow-right me-3"></i>退出登录
                            </a>
                        </div>
                    </nav>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <!-- 顶部栏 -->
                    <div class="top-bar">
                        <div>
                            <h3 class="mb-0" id="pageTitle">仪表板</h3>
                            <small class="text-muted">欢迎使用支付设备认证管理系统</small>
                        </div>
                        <div class="top-bar-actions">
                            <button class="btn btn-outline-primary refresh-btn">
                                <i class="bi bi-arrow-clockwise me-2"></i>刷新
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-person-circle me-2"></i>${e.username}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" data-action="profile">
                                        <i class="bi bi-person me-2"></i>个人资料
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" data-action="settings">
                                        <i class="bi bi-gear me-2"></i>系统设置
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item logout-btn" href="#">
                                        <i class="bi bi-box-arrow-right me-2"></i>退出登录
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 内容区域 -->
                    <div class="content-wrapper" id="contentArea">
                        ${this.generateDashboardContent(e)}
                    </div>
                </div>
            </div>
        `}generateMainStyles(){return`
            <style>
                .main-container {
                    display: flex;
                    min-height: 100vh;
                    background: #f8fafc;
                    width: 100%;
                }

                .sidebar {
                    width: 280px;
                    min-width: 280px;
                    background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
                    color: white;
                    position: sticky;
                    top: 0;
                    height: 100vh;
                    z-index: 1000;
                    box-shadow: 4px 0 15px rgba(0,0,0,0.1);
                    overflow-y: auto;
                    flex-shrink: 0;
                }

                .sidebar-header {
                    padding: 30px 25px;
                    border-bottom: 1px solid rgba(255,255,255,0.1);
                    text-align: center;
                }

                .sidebar-menu {
                    padding: 20px 0;
                    display: flex;
                    flex-direction: column;
                    min-height: calc(100vh - 120px);
                }

                .menu-item {
                    display: flex;
                    align-items: center;
                    padding: 15px 25px;
                    color: rgba(255,255,255,0.7);
                    text-decoration: none;
                    transition: all 0.3s ease;
                    border-left: 3px solid transparent;
                    cursor: pointer;
                }

                .menu-item:hover,
                .menu-item.active {
                    background: rgba(255,255,255,0.1);
                    color: white;
                    border-left-color: #3b82f6;
                    text-decoration: none;
                }

                .menu-header {
                    padding: 20px 25px 10px;
                    font-size: 12px;
                    font-weight: 600;
                    color: rgba(255,255,255,0.5);
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }

                .main-content {
                    flex: 1;
                    padding: 30px;
                    min-width: 0;
                    min-height: 100vh;
                }

                .top-bar {
                    background: #ffffff;
                    padding: 20px 30px;
                    border-radius: 15px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    margin-bottom: 30px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .top-bar-actions {
                    display: flex;
                    gap: 10px;
                    align-items: center;
                }

                .content-wrapper {
                    background: #ffffff;
                    border-radius: 15px;
                    padding: 30px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    min-height: 600px;
                }

                .welcome-card {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-radius: 15px;
                    padding: 30px;
                    margin-bottom: 30px;
                    text-align: center;
                }

                .stats-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin-bottom: 30px;
                }

                .stat-card {
                    background: #ffffff;
                    border: 1px solid #e5e7eb;
                    border-radius: 15px;
                    padding: 25px;
                    text-align: center;
                    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
                    transition: all 0.3s ease;
                }

                .stat-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                }

                .stat-number {
                    font-size: 2.5rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin: 10px 0;
                }

                .stat-label {
                    color: #6b7280;
                    font-size: 14px;
                    font-weight: 500;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .sidebar {
                        width: 100%;
                        transform: translateX(-100%);
                        transition: transform 0.3s ease;
                    }
                    
                    .sidebar.show {
                        transform: translateX(0);
                    }
                    
                    .main-content {
                        margin-left: 0;
                        width: 100%;
                        padding: 15px;
                    }
                    
                    .top-bar {
                        padding: 15px 20px;
                        flex-direction: column;
                        gap: 15px;
                    }
                    
                    .stats-grid {
                        grid-template-columns: 1fr;
                    }
                }

                /* 加载状态 */
                .loading-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                }

                .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid #f3f4f6;
                    border-top: 4px solid #3b82f6;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `}generateMenuItems(e){var t=this.auth.getUser(),t=t&&1==t.is_staff,a=this.tenantInfo?this.tenantInfo.tenant_type:"provider";console.log("🎯 生成菜单项:",{userType:e,tenantType:a,isStaff:t});let i=[];switch(a){case"system_admin":i=this.getSystemAdminMenuItems();break;case"platform_admin":i=this.getPlatformAdminMenuItems();break;case"provider":i=this.getProviderMenuItems();break;case"merchant":i=this.getMerchantMenuItems();break;default:console.warn("⚠️ 未知的租户类型，使用默认菜单:",a),i=this.getProviderMenuItems()}return t&&(i=i.filter(e=>!["employees","job-positions","users","user-management","platform-users"].includes(e.id))),(this.menuItems=i).map(e=>"header"===e.type?`<div class="menu-header">${e.text}</div>`:`
                <a href="#" class="menu-item" data-page="${e.id}">
                    <i class="${e.icon} me-3"></i>${e.text}
                </a>
            `).join("")}getSystemAdminMenuItems(){return[{id:"dashboard",icon:"bi-speedometer2",text:"系统总览"},{type:"header",text:"平台管理"},{id:"platforms",icon:"bi-building-gear",text:"平台管理"},{id:"domain-management",icon:"bi-globe",text:"域名管理"},{id:"users",icon:"bi-people",text:"系统用户"},{type:"header",text:"App版本管理"},{id:"app-versions",icon:"bi-phone-vibrate",text:"版本发布管理"},{id:"provider-apps",icon:"bi-app-indicator",text:"码商App管理"},{id:"update-push",icon:"bi-broadcast",text:"更新推送管理"},{type:"header",text:"脚本管理"},{id:"system-scripts",icon:"bi-terminal",text:"系统脚本"},{id:"business-scripts",icon:"bi-code-square",text:"业务脚本"},{id:"script-execution",icon:"bi-play-circle",text:"脚本执行"},{type:"header",text:"高级监控"},{id:"system-monitoring",icon:"bi-activity",text:"系统监控"},{id:"server-monitor",icon:"bi-cpu",text:"服务器监控"},{id:"database-monitor",icon:"bi-database",text:"数据库监控"},{id:"application-monitor",icon:"bi-speedometer",text:"应用监控"},{id:"performance-monitor",icon:"bi-speedometer2",text:"性能监控"},{id:"security-logs",icon:"bi-shield-exclamation",text:"安全日志"},{type:"header",text:"全系统财务"},{id:"global-finance",icon:"bi-cash-stack",text:"全系统财务概览"},{type:"header",text:"系统设置"},{id:"system-settings",icon:"bi-gear",text:"系统设置"},{id:"backup-restore",icon:"bi-archive",text:"备份恢复"}]}getPlatformAdminMenuItems(){return[{id:"dashboard",icon:"bi-speedometer2",text:"平台总览"},{type:"header",text:"用户管理"},{id:"users",icon:"bi-people",text:"用户管理"},{id:"employees",icon:"bi-person-badge",text:"员工管理"},{id:"job-positions",icon:"bi-diagram-3",text:"职位管理"},{type:"header",text:"码商管理"},{id:"providers",icon:"bi-building",text:"码商列表"},{id:"provider-audit",icon:"bi-clipboard-check",text:"码商审核"},{id:"provider-monitor",icon:"bi-graph-up-arrow",text:"码商监控"},{type:"header",text:"商户管理"},{id:"merchants",icon:"bi-shop",text:"商户列表"},{id:"merchant-audit",icon:"bi-clipboard-check",text:"商户审核"},{id:"merchant-service",icon:"bi-headset",text:"商户服务"},{type:"header",text:"业务管理"},{id:"products",icon:"bi-box-seam",text:"产品管理"},{id:"devices",icon:"bi-phone",text:"设备管理"},{type:"header",text:"财务系统"},{id:"financial",icon:"bi-graph-up",text:"财务管理"},{id:"realtime-data",icon:"bi-speedometer2",text:"实时数据"},{id:"fee-config",icon:"bi-gear",text:"手续费配置"},{id:"settlement-mgmt",icon:"bi-bank",text:"结算管理"},{id:"finance-reports",icon:"bi-file-earmark-bar-graph",text:"财务报表"},{id:"transactions",icon:"bi-receipt",text:"交易记录"},{id:"alipay",icon:"bi-credit-card",text:"支付宝账户"},{type:"header",text:"系统管理"},{id:"risk-control",icon:"bi-shield-check",text:"风控管理"},{id:"notifications",icon:"bi-bell",text:"通知管理"},{id:"platform-config",icon:"bi-sliders",text:"平台配置"},{id:"blacklist",icon:"bi-shield-exclamation",text:"黑名单管理"},{id:"scripts",icon:"bi-code-square",text:"脚本管理"},{id:"security-logs",icon:"bi-shield-exclamation",text:"安全日志"},{id:"performance-monitor",icon:"bi-speedometer2",text:"性能监控"}]}getProviderMenuItems(){return[{id:"dashboard",icon:"bi-speedometer2",text:"仪表板"},{type:"header",text:"团队管理"},{id:"employees",icon:"bi-person-badge",text:"员工管理"},{id:"job-positions",icon:"bi-diagram-3",text:"职位管理"},{type:"header",text:"设备管理"},{id:"devices",icon:"bi-phone",text:"设备列表"},{id:"device-groups",icon:"bi-collection",text:"设备小组"},{id:"device-monitor",icon:"bi-activity",text:"设备监控"},{id:"alipay-accounts",icon:"bi-credit-card",text:"支付宝账户"},{id:"totp",icon:"bi-shield-lock",text:"TOTP管理"},{type:"header",text:"订单管理"},{id:"realtime-orders",icon:"bi-receipt",text:"实时订单"},{id:"order-process",icon:"bi-gear",text:"订单处理"},{id:"order-stats",icon:"bi-bar-chart",text:"订单统计"},{id:"order-export",icon:"bi-download",text:"订单导出"},{type:"header",text:"风控管理"},{id:"risk-rules",icon:"bi-shield-check",text:"风控规则"},{id:"blacklist-mgmt",icon:"bi-shield-exclamation",text:"黑名单管理"},{id:"risk-monitor",icon:"bi-eye",text:"风险监控"},{id:"risk-reports",icon:"bi-file-text",text:"风控报表"},{type:"header",text:"财务管理"},{id:"flow-records",icon:"bi-list-ul",text:"流水记录"},{id:"finance-settlement",icon:"bi-bank2",text:"财务结算"},{id:"realtime-balance",icon:"bi-wallet2",text:"实时余额"},{id:"finance-reconcile",icon:"bi-check2-square",text:"财务对账"},{id:"transactions",icon:"bi-receipt",text:"交易记录"}]}getMerchantMenuItems(){return[{id:"merchant-dashboard",icon:"bi-speedometer2",text:"仪表板"},{type:"header",text:"业务管理"},{id:"merchant-orders",icon:"bi-receipt",text:"我的订单"},{id:"order-records",icon:"bi-receipt",text:"订单记录"},{id:"business-analysis",icon:"bi-graph-up",text:"业务分析"},{id:"merchant-stats",icon:"bi-graph-up",text:"数据统计"},{id:"merchant-products",icon:"bi-box-seam",text:"产品管理"},{type:"header",text:"开发者工具"},{id:"api-keys",icon:"bi-key",text:"API密钥"},{id:"api-settings",icon:"bi-gear",text:"API设置"},{id:"api-test",icon:"bi-play-circle",text:"接口测试"},{id:"code-generator",icon:"bi-code-square",text:"代码生成器"},{id:"signature-tool",icon:"bi-shield-check",text:"签名工具"},{id:"api-docs",icon:"bi-book",text:"接口文档"},{id:"dev-support",icon:"bi-question-circle",text:"开发支持"},{type:"header",text:"财务管理"},{id:"realtime-flow",icon:"bi-speedometer2",text:"实时流水"},{id:"finance-reconcile",icon:"bi-check2-all",text:"财务对账"},{id:"settlement-records",icon:"bi-bank",text:"结算记录"},{id:"realtime-balance",icon:"bi-wallet",text:"实时余额"},{id:"finance-reports",icon:"bi-file-earmark-spreadsheet",text:"财务报表"},{type:"header",text:"系统设置"},{id:"merchant-config",icon:"bi-gear",text:"系统配置"},{id:"merchant-profile",icon:"bi-person-circle",text:"商户资料"},{id:"employees",icon:"bi-person-badge",text:"员工管理"},{id:"job-positions",icon:"bi-diagram-3",text:"职位管理"}]}generateDashboardContent(e){return`
            <!-- 欢迎信息 -->
            <div class="welcome-card">
                <h2>欢迎回来，${e.display_name||e.real_name||e.username}！</h2>
                <p>您的身份：${this.getUserTypeText(e.user_type)}</p>
                <small>上次登录：${(new Date).toLocaleString()}</small>
            </div>

            <!-- 统计数据 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="bi bi-people" style="font-size: 2rem; color: #3b82f6;"></i>
                    <div class="stat-number" id="stat-users">-</div>
                    <div class="stat-label">总用户数</div>
                </div>
                <div class="stat-card">
                    <i class="bi bi-phone" style="font-size: 2rem; color: #10b981;"></i>
                    <div class="stat-number" id="stat-devices">-</div>
                    <div class="stat-label">活跃设备</div>
                </div>
                <div class="stat-card">
                    <i class="bi bi-building" style="font-size: 2rem; color: #f59e0b;"></i>
                    <div class="stat-number" id="stat-providers">-</div>
                    <div class="stat-label">码商数量</div>
                </div>
                <div class="stat-card">
                    <i class="bi bi-graph-up" style="font-size: 2rem; color: #ef4444;"></i>
                    <div class="stat-number" id="stat-transactions">-</div>
                    <div class="stat-label">今日交易额</div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-lightning-charge me-2"></i>快速操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" data-quick-action="add-user">
                                    <i class="bi bi-person-plus me-2"></i>添加用户
                                </button>
                                <button class="btn btn-success" data-quick-action="add-device">
                                    <i class="bi bi-phone-plus me-2"></i>添加设备
                                </button>
                                <button class="btn btn-info" data-quick-action="view-transactions">
                                    <i class="bi bi-receipt me-2"></i>查看交易
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-clock-history me-2"></i>最近活动</h5>
                        </div>
                        <div class="card-body">
                            <div id="recent-activities">
                                <div class="text-center text-muted">
                                    <i class="bi bi-clock"></i>
                                    <p>正在加载最近活动...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `}initializeInterface(){this.initializeMenuEvents(),this.initializeTopBarEvents(),this.initializeQuickActions(),this.initializeRouterManager(),this.loadDashboardStats()}initializeRouterManager(){try{window.routerManager?(window.routerManager.setManagers({moduleLoader:window.AdminModuleLoader,uiManager:this,authManager:this.auth}),console.log("✅ 路由管理器依赖设置完成")):console.warn("⚠️ 路由管理器未找到，使用传统导航方式")}catch(e){console.error("❌ 路由管理器初始化失败:",e)}}initializeMenuEvents(){document.querySelectorAll(".menu-item[data-page]").forEach(t=>{t.addEventListener("click",e=>{e.preventDefault();e=t.getAttribute("data-page");this.navigateToPage(e,t)})});var e=document.querySelector('.menu-item[data-page="dashboard"]');e&&e.classList.add("active")}initializeTopBarEvents(){var e=document.querySelector(".refresh-btn");e&&e.addEventListener("click",()=>{this.refreshCurrentPage()}),document.querySelectorAll(".logout-btn").forEach(e=>{e.addEventListener("click",e=>{e.preventDefault(),this.handleLogout()})}),document.querySelectorAll(".dropdown-item[data-action]").forEach(t=>{t.addEventListener("click",e=>{e.preventDefault();e=t.getAttribute("data-action");this.handleTopBarAction(e)})})}initializeQuickActions(){document.querySelectorAll("[data-quick-action]").forEach(a=>{a.addEventListener("click",e=>{var t=a.getAttribute("data-quick-action");this.handleQuickAction(t)})})}navigateToPage(e,t){this.currentPage!==e&&(window.routerManager?window.routerManager.navigateByMenuId(e):this.legacyNavigateToPage(e,t),this.currentPage=e,console.log("导航到页面: "+e))}legacyNavigateToPage(e,t){document.querySelectorAll(".menu-item").forEach(e=>{e.classList.remove("active")}),t&&t.classList.add("active");var a=document.getElementById("pageTitle");a&&t&&(a.textContent=t.textContent.trim()),this.loadPageContent(e).catch(e=>{console.error("页面加载失败:",e)})}async loadPageContent(t){var a=document.querySelector(".content-wrapper");if(a)if(this.canAccessPage(t)){this.showLoading(!0);try{if(this.pageLoadHandlers.has(t)){var e=this.pageLoadHandlers.get(t);try{await e(a)}catch(e){console.error(`页面加载处理器错误 (${t}):`,e),this.showErrorPage(a,"加载页面失败: "+e.message)}}else await this.loadDefaultPageContent(t,a)}finally{this.showLoading(!1)}}else this.showAccessDeniedPage(a,t)}canAccessPage(e){var t,a;return this.tenantInfo?((a=({system_admin:["dashboard","platforms","domain-management","users","system-users","app-versions","provider-apps","update-push","system-scripts","business-scripts","script-execution","scripts","system-monitoring","server-monitor","database-monitor","application-monitor","performance-monitor","security-logs","global-finance","system-settings","backup-restore"],platform_admin:["dashboard","users","employees","job-positions","products","devices","providers","provider-audit","provider-monitor","merchants","merchant-audit","merchant-service","financial","realtime-data","fee-config","settlement-mgmt","finance-reports","transactions","alipay","risk-control","notifications","platform-config","blacklist","scripts","security-logs","performance-monitor"],provider:["dashboard","employees","job-positions","totp","devices","device-groups","device-monitor","alipay-accounts","realtime-orders","order-process","order-stats","order-export","risk-rules","blacklist-mgmt","risk-monitor","risk-reports","flow-records","finance-settlement","realtime-balance","finance-reconcile","transactions","alipay"],merchant:["merchant-dashboard","employees","job-positions","merchant-orders","order-records","business-analysis","merchant-stats","merchant-products","api-keys","api-settings","api-test","code-generator","signature-tool","api-docs","dev-support","realtime-flow","finance-reconcile","settlement-records","realtime-balance","finance-reports","merchant-config"]}[t=this.tenantInfo.tenant_type]||[]).includes(e))||console.warn(`⚠️ 租户类型 ${t} 无权访问页面: `+e),a):(console.warn("⚠️ 租户信息未初始化，拒绝访问页面:",e),!1)}showAccessDeniedPage(e,t){e.innerHTML=`
            <div class="text-center py-5">
                <i class="bi bi-shield-exclamation text-warning" style="font-size: 4rem;"></i>
                <h3 class="mt-3">访问被拒绝</h3>
                <p class="text-muted">您没有权限访问此页面</p>
                <div class="mt-3">
                    <p class="text-muted"><strong>页面ID：</strong>${t}</p>
                    <p class="text-muted"><strong>当前租户类型：</strong>${this.getTenantTypeText()}</p>
                    <p class="text-muted"><strong>当前域名：</strong>${window.location.hostname}</p>
                </div>
                <div class="mt-4">
                    <button class="btn btn-primary me-2" onclick="window.uiManager.navigateToPage('dashboard')">
                        <i class="bi bi-house me-2"></i>返回首页
                    </button>
                    <button class="btn btn-outline-secondary" onclick="history.back()">
                        <i class="bi bi-arrow-left me-2"></i>返回上一页
                    </button>
                </div>
            </div>
        `}async loadDefaultPageContent(e,t){var a={dashboard:()=>this.loadDashboard(t),platforms:()=>this.loadPlatformsPage(t),"domain-management":()=>this.loadDomainManagementPage(t),users:()=>this.loadUsersPage(t),employees:()=>this.loadEmployeesPage(t),"job-positions":()=>this.loadJobPositionsPage(t),providers:()=>this.loadProvidersPage(t),merchants:()=>this.loadMerchantsPage(t),products:()=>this.loadProductsPage(t),devices:()=>this.loadDevicesPage(t),financial:()=>this.loadFinancialPage(t),transactions:()=>this.loadTransactionsPage(t),alipay:()=>this.loadAlipayPage(t),"risk-control":()=>this.loadRiskControlPage(t),notifications:()=>this.loadNotificationsPage(t),blacklist:()=>this.loadBlacklistPage(t),scripts:()=>this.loadScriptsPage(t),"security-logs":()=>this.loadSecurityLogsPage(t),"performance-monitor":()=>this.loadPerformanceMonitorPage(t),totp:()=>this.loadTOTPPage(t),"app-versions":()=>this.loadAppVersionsPage(t),"provider-apps":()=>this.loadProviderAppsPage(t),"update-push":()=>this.loadUpdatePushPage(t),"system-scripts":()=>this.loadSystemScriptsPage(t),"business-scripts":()=>this.loadBusinessScriptsPage(t),"script-execution":()=>this.loadScriptExecutionPage(t),"system-monitoring":()=>this.loadSystemMonitoringPage(t),"server-monitor":()=>this.loadServerMonitorPage(t),"database-monitor":()=>this.loadDatabaseMonitorPage(t),"application-monitor":()=>this.loadApplicationMonitorPage(t),"global-finance":()=>this.loadGlobalFinancePage(t),"system-settings":()=>this.loadSystemSettingsPage(t),"backup-restore":()=>this.loadBackupRestorePage(t),"provider-audit":()=>this.loadProviderAuditPage(t),"provider-monitor":()=>this.loadProviderMonitorPage(t),"merchant-audit":()=>this.loadMerchantAuditPage(t),"merchant-service":()=>this.loadMerchantServicePage(t),"realtime-data":()=>this.loadRealtimeDataPage(t),"fee-config":()=>this.loadFeeConfigPage(t),"settlement-mgmt":()=>this.loadSettlementMgmtPage(t),"finance-reports":()=>this.loadFinanceReportsPage(t),"platform-config":()=>this.loadPlatformConfigPage(t),"device-groups":()=>this.loadDeviceGroupsPage(t),"device-monitor":()=>this.loadDeviceMonitorPage(t),"alipay-accounts":()=>this.loadAlipayAccountsPage(t),"realtime-orders":()=>this.loadRealtimeOrdersPage(t),"order-process":()=>this.loadOrderProcessPage(t),"order-stats":()=>this.loadOrderStatsPage(t),"order-export":()=>this.loadOrderExportPage(t),"risk-rules":()=>this.loadRiskRulesPage(t),"blacklist-mgmt":()=>this.loadBlacklistMgmtPage(t),"risk-monitor":()=>this.loadRiskMonitorPage(t),"risk-reports":()=>this.loadRiskReportsPage(t),"flow-records":()=>this.loadFlowRecordsPage(t),"finance-settlement":()=>this.loadFinanceSettlementPage(t),"realtime-balance":()=>this.loadRealtimeBalancePage(t),"finance-reconcile":()=>this.loadFinanceReconcilePage(t),"merchant-dashboard":()=>this.loadMerchantDashboard(t),"merchant-orders":()=>this.loadMerchantOrdersPage(t),"order-records":()=>this.loadOrderRecordsPage(t),"business-analysis":()=>this.loadBusinessAnalysisPage(t),"merchant-stats":()=>this.loadMerchantStatsPage(t),"merchant-products":()=>this.loadMerchantProductsPage(t),"api-keys":()=>this.loadApiKeysPage(t),"api-settings":()=>this.loadApiSettingsPage(t),"api-test":()=>this.loadApiTestPage(t),"code-generator":()=>this.loadCodeGeneratorPage(t),"signature-tool":()=>this.loadSignatureToolPage(t),"api-docs":()=>this.loadApiDocsPage(t),"dev-support":()=>this.loadDevSupportPage(t),"realtime-flow":()=>this.loadRealtimeFlowPage(t),"settlement-records":()=>this.loadSettlementRecordsPage(t),"merchant-config":()=>this.loadMerchantConfigPage(t)}[e];a?await a():this.showDevelopmentPage(t,e)}registerPageHandler(e,t){this.pageLoadHandlers.set(e,t)}getTenantDisplayName(){if(!this.tenantInfo)return"控制面板";switch(this.tenantInfo.tenant_type){case"system_admin":return"系统管理后台";case"platform_admin":return`${this.tenantInfo.platform_name||"平台"} 管理后台`;case"provider":return`${this.tenantInfo.brand_name||"码商"} 管理系统`;case"merchant":return`${this.tenantInfo.brand_name||"商户"} 管理系统`;default:return"控制面板"}}getTenantTypeText(){return this.tenantInfo&&{system_admin:"系统管理员",platform_admin:"平台管理员",provider:"码商",merchant:"商户"}[this.tenantInfo.tenant_type]||""}getUserTypeText(e){return{admin:"系统管理员",provider:"支付码商",merchant:"商户用户"}[e]||"未知用户"}showLoading(e,t="加载中..."){let a=document.querySelector(".loading-overlay");e?(a||((a=document.createElement("div")).className="loading-overlay",document.body.appendChild(a)),a.innerHTML=`
                <div class="text-center">
                    <div class="loading-spinner mb-3"></div>
                    <p class="text-muted">${t}</p>
                </div>
            `,a.style.display="flex"):a&&(a.style.display="none")}showMessage(e,t="info"){var a=document.querySelector(".toast-message");a&&a.remove();let i=document.createElement("div");i.className=`toast-message alert alert-${this.getAlertClass(t)} alert-dismissible fade show`,i.style.cssText=`
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `,i.innerHTML=`
            <div class="d-flex align-items-center">
                <i class="bi ${this.getIconClass(t)} me-2"></i>
                <span>${e}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `,document.body.appendChild(i),setTimeout(()=>{i&&i.parentNode&&i.remove()},5e3)}getAlertClass(e){return{success:"success",error:"danger",warning:"warning",info:"info"}[e]||"info"}getIconClass(e){return{success:"bi-check-circle",error:"bi-exclamation-circle",warning:"bi-exclamation-triangle",info:"bi-info-circle"}[e]||"bi-info-circle"}refreshCurrentPage(){this.loadPageContent(this.currentPage).catch(e=>{console.error("页面刷新失败:",e)})}handleLogout(){confirm("确定要退出登录吗？")&&this.auth.logout()}handleTopBarAction(e){switch(e){case"profile":this.navigateToPage("profile");break;case"settings":this.navigateToPage("settings");break;default:console.log("未处理的顶部栏操作: "+e)}}handleQuickAction(e){switch(e){case"add-user":this.navigateToPage("users");break;case"add-device":this.navigateToPage("devices");break;case"view-transactions":this.navigateToPage("transactions");break;default:console.log("未处理的快速操作: "+e)}}loadDashboard(e){console.log("加载仪表板页面...");var t=this.auth.getUser();e.innerHTML=this.generateDashboardContent(t),this.loadDashboardStats(),this.initializeQuickActions()}async loadDashboardStats(){try{setTimeout(()=>{document.getElementById("stat-users").textContent="156",document.getElementById("stat-devices").textContent="42",document.getElementById("stat-providers").textContent="8",document.getElementById("stat-transactions").textContent="¥12,345"},500)}catch(e){console.error("加载统计数据失败:",e)}}loadUsersPage(e){console.log("加载用户管理页面..."),this.loadBusinessModule(e,"user-management","用户管理")}loadDevicesPage(t){if(console.log("🔄 加载设备管理页面..."),window.DeviceModuleManagerModule)try{console.log("✅ 使用设备模块管理器"),(new window.DeviceModuleManagerModule.DeviceModuleManager).init(t)}catch(e){console.error("❌ 设备模块管理器加载失败:",e),this.showErrorPage(t,"设备模块管理器加载失败")}else if(window.DeviceManagementModule)try{console.log("✅ 使用设备管理模块"),(new window.DeviceManagementModule.DeviceManager).init(t)}catch(e){console.error("❌ 设备管理模块加载失败:",e),this.showErrorPage(t,"设备管理模块加载失败")}else console.log("⏳ 动态加载设备管理模块..."),this.loadDeviceManagementModule(t)}async loadDeviceManagementModule(t){try{if(t.innerHTML=`
                <div class="text-center p-5">
                    <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <h5 class="text-muted">正在加载设备管理模块...</h5>
                    <p class="text-muted small">首次加载可能需要几秒钟时间</p>
                </div>
            `,!window.appInitializer||!window.appInitializer.loadBusinessModules)throw new Error("应用初始化器不可用或不支持动态模块加载");console.log("📦 开始加载设备管理业务模块..."),await window.appInitializer.loadBusinessModules(["device-management","group-management","checkin-monitor","device-module-manager"]),console.log("✅ 设备管理模块加载完成，重新渲染页面"),this.loadDevicesPage(t)}catch(e){console.error("❌ 设备管理模块动态加载失败:",e),this.showErrorPage(t,"设备管理模块加载失败: "+e.message)}}loadTransactionsPage(t){if(console.log("加载交易管理页面..."),window.TransactionManagementModule)try{(new window.TransactionManagementModule.TransactionManager).render(t)}catch(e){console.error("交易管理模块加载失败:",e),this.showErrorPage(t,"交易管理模块加载失败")}else t.innerHTML=`
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-credit-card me-2"></i>交易管理
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" placeholder="搜索订单号...">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select">
                                            <option>全部状态</option>
                                            <option>待支付</option>
                                            <option>已支付</option>
                                            <option>已取消</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="date" class="form-control">
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-primary">搜索</button>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>订单号</th>
                                                <th>金额</th>
                                                <th>状态</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>TXN001</td>
                                                <td>¥100.00</td>
                                                <td><span class="badge bg-success">已支付</span></td>
                                                <td>2024-01-15 10:30</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary">详情</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>TXN002</td>
                                                <td>¥50.00</td>
                                                <td><span class="badge bg-warning">待支付</span></td>
                                                <td>2024-01-15 10:25</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary">详情</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `}showDevelopmentPage(e,t){e.innerHTML=`
            <div class="text-center py-5">
                <i class="bi bi-tools" style="font-size: 4rem; color: #6b7280;"></i>
                <h3 class="mt-3">页面开发中</h3>
                <p class="text-muted">页面 "${t}" 正在开发中，敬请期待...</p>
                <button class="btn btn-primary" onclick="history.back()">返回上一页</button>
            </div>
        `}loadEmployeesPage(e){console.log("加载员工管理页面..."),this.loadBusinessModule(e,"user-management","员工管理")}loadJobPositionsPage(e){console.log("加载职位管理页面..."),this.loadBusinessModule(e,"user-management","职位管理")}async loadProvidersPage(e){await this.loadBusinessModule(e,"provider-management","码商管理")}async loadMerchantsPage(e){await this.loadBusinessModule(e,"merchant-management","商户管理")}async loadProductsPage(e){await this.loadBusinessModule(e,"product-management","产品管理")}async loadFinancialPage(e){await this.loadBusinessModule(e,"finance-management","财务管理")}async loadAlipayPage(e){await this.loadBusinessModule(e,"payment-management","支付宝账户")}loadRiskControlPage(e){this.loadSecurityModule(e,"security-management","风控管理","RiskControlManager")}loadNotificationsPage(e){this.loadSystemModule(e,"system-monitoring","通知管理","NotificationManager")}loadBlacklistPage(e){this.loadSecurityModule(e,"security-management","黑名单管理","BlacklistManager")}loadScriptsPage(e){this.loadSystemModule(e,"system-monitoring","脚本管理")}loadSecurityLogsPage(e){this.loadSecurityModule(e,"security-management","安全日志","SecurityLogManager")}loadPerformanceMonitorPage(e){this.loadSystemModule(e,"system-monitoring","性能监控","PerformanceManager")}loadTOTPPage(e){this.loadSecurityModule(e,"security-management","TOTP管理","TOTPManager")}loadAppVersionsPage(e){this.loadSystemModule(e,"app-management","App版本管理")}loadProviderAppsPage(e){this.loadSystemModule(e,"app-management","码商App管理")}loadUpdatePushPage(e){this.loadSystemModule(e,"app-management","更新推送管理")}loadSystemScriptsPage(e){this.loadSystemModule(e,"script-management","系统脚本","ScriptManager")}loadBusinessScriptsPage(e){this.loadSystemModule(e,"script-management","业务脚本","ScriptManager")}loadScriptExecutionPage(e){this.loadSystemModule(e,"script-management","脚本执行","ScriptManager")}loadSystemMonitoringPage(e){this.loadSystemModule(e,"system-monitoring","系统监控","PerformanceManager")}loadServerMonitorPage(e){this.loadSystemModule(e,"system-monitoring","服务器监控","PerformanceManager")}loadDatabaseMonitorPage(e){this.loadSystemModule(e,"system-monitoring","数据库监控","PerformanceManager")}loadApplicationMonitorPage(e){this.loadSystemModule(e,"system-monitoring","应用监控","PerformanceManager")}loadGlobalFinancePage(e){this.loadSystemModule(e,"global-finance","全系统财务概览","FinancialManager")}loadSystemSettingsPage(e){this.loadSystemModule(e,"system-settings","系统设置")}loadBackupRestorePage(e){this.loadSystemModule(e,"system-settings","备份恢复")}loadProviderAuditPage(e){this.loadBusinessModule(e,"provider-management","码商审核")}loadProviderMonitorPage(e){this.loadBusinessModule(e,"provider-management","码商监控")}loadMerchantAuditPage(e){this.loadBusinessModule(e,"merchant-management","商户审核")}loadMerchantServicePage(e){this.loadBusinessModule(e,"merchant-management","商户服务")}loadRealtimeDataPage(e){this.loadFinanceModule(e,"realtime-data","实时数据","RealtimeDataManager")}loadFeeConfigPage(e){this.loadFinanceModule(e,"fee-config","手续费配置","FeeConfigManager")}loadSettlementMgmtPage(e){this.loadFinanceModule(e,"settlement","结算管理","SettlementManager")}loadFinanceReportsPage(e){this.loadFinanceModule(e,"finance-reports","财务报表","FinanceReportManager")}loadPlatformConfigPage(e){this.loadSystemModule(e,"platform-config","平台配置")}loadDeviceGroupsPage(e){this.loadBusinessModule(e,"device-management","设备小组")}loadDeviceMonitorPage(e){this.loadBusinessModule(e,"device-management","设备监控")}loadAlipayAccountsPage(e){this.loadBusinessModule(e,"account-management","支付宝账户")}loadRealtimeOrdersPage(e){this.loadBusinessModule(e,"transaction-management","实时订单")}loadOrderProcessPage(e){this.loadBusinessModule(e,"transaction-management","订单处理")}loadOrderStatsPage(e){this.loadBusinessModule(e,"transaction-management","订单统计")}loadOrderExportPage(e){this.loadBusinessModule(e,"transaction-management","订单导出")}loadRiskRulesPage(e){this.loadSecurityModule(e,"risk-management","风控规则","RiskControlManager")}loadBlacklistMgmtPage(e){this.loadSecurityModule(e,"risk-management","黑名单管理","BlacklistManager")}loadRiskMonitorPage(e){this.loadSecurityModule(e,"risk-management","风险监控","RiskControlManager")}loadRiskReportsPage(e){this.loadSecurityModule(e,"risk-management","风控报表","RiskControlManager")}loadFlowRecordsPage(e){this.loadBusinessModule(e,"finance-management","流水记录")}loadFinanceSettlementPage(e){this.loadFinanceModule(e,"settlement","财务结算","SettlementManager")}loadRealtimeBalancePage(e){this.loadFinanceModule(e,"realtime-data","实时余额","RealtimeDataManager")}loadFinanceReconcilePage(e){this.loadFinanceModule(e,"reconciliation","财务对账","ReconciliationManager")}loadOrderRecordsPage(e){this.loadMerchantModule(e,"order-records","订单记录")}loadBusinessAnalysisPage(e){this.loadMerchantModule(e,"business-analysis","业务分析")}loadApiSettingsPage(e){this.loadMerchantModule(e,"api-settings","API设置")}loadApiTestPage(e){this.loadMerchantModule(e,"api-test","接口测试")}loadDevSupportPage(e){this.loadMerchantModule(e,"dev-support","开发支持")}loadRealtimeFlowPage(e){this.loadMerchantModule(e,"realtime-flow","实时流水")}loadSettlementRecordsPage(e){this.loadMerchantModule(e,"settlement-records","结算记录")}loadMerchantDashboard(e){this.loadMerchantModule(e,"dashboard","商户仪表板")}loadMerchantOrdersPage(e){this.loadMerchantModule(e,"orders","商户订单")}loadMerchantStatsPage(e){this.loadMerchantModule(e,"statistics","商户统计")}loadMerchantProductsPage(e){this.loadMerchantModule(e,"products","商户产品")}loadApiKeysPage(e){this.loadMerchantModule(e,"api-keys","API密钥")}loadCodeGeneratorPage(e){this.loadMerchantModule(e,"code-generator","代码生成器")}loadSignatureToolPage(e){this.loadMerchantModule(e,"signature-tool","签名工具")}loadApiDocsPage(e){this.loadMerchantModule(e,"api-docs","API文档")}loadMerchantConfigPage(e){this.loadMerchantModule(e,"configuration","商户配置")}async loadFinanceModule(t,e,a,i=null){console.log("加载财务模块: "+e);try{t.innerHTML=`
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-3 text-muted">正在加载${a}...</p>
                </div>
            `;var s,o=i||{"realtime-data":"RealtimeDataManager","fee-config":"FeeConfigManager",settlement:"SettlementManager","finance-reports":"FinanceReportManager",reconciliation:"ReconciliationManager","risk-control":"RiskControlManager"}[e];o&&window[o]?"function"==typeof(s=new window[o]).initialize?(await s.initialize(),s.render(t)):t.innerHTML=`
                        <div class="alert alert-info">
                            <h4>${a}</h4>
                            <p>财务模块 ${o} 已加载，但缺少初始化方法。</p>
                        </div>
                    `:this.showErrorPage(t,a+`模块加载失败: 找不到${o}类`)}catch(e){console.error("财务模块加载失败:",e),this.showErrorPage(t,a+"模块加载失败: "+e.message)}}async loadBusinessModule(a,i,t){console.log("加载业务模块: "+i);var s={"user-management":"UserManager","provider-management":"ProviderManager","merchant-management":"MerchantManager","product-management":"ProductManager","finance-management":"FinanceManager","payment-management":"PaymentManager","transaction-management":"TransactionManager","api-management":"APIManager","merchant-dashboard":"MerchantToolsManager","merchant-order-management":"MerchantToolsManager","merchant-statistics":"MerchantToolsManager","merchant-product-management":"MerchantToolsManager","api-key-management":"MerchantToolsManager","code-generator":"MerchantToolsManager","signature-tool":"MerchantToolsManager","api-documentation":"MerchantToolsManager","merchant-configuration":"MerchantToolsManager"}[i];if(!window[s])try{this.showLoading(!0,`正在加载${t}模块...`),window.AdminModuleLoader?(console.log("动态加载模块: "+i),await window.AdminModuleLoader.loadModule(i)):(console.warn("模块加载器未找到，尝试直接加载脚本"),await this.loadModuleScript(i)),await new Promise(e=>setTimeout(e,100))}catch(e){return console.error(`动态加载模块 ${i} 失败:`,e),this.showLoading(!1),void this.showErrorPage(a,t+"模块加载失败: "+e.message)}finally{this.showLoading(!1)}if(window[s])try{let t=window[s];"function"==typeof t&&(console.log("实例化模块类: "+s),t=new t);var o,e={apiClient:window.AdminAPI||window.apiClient,utils:window.AdminUtils||this.utils,authManager:this.auth,tenantInfo:this.tenantInfo};if(t.init&&"function"==typeof t.init&&(console.log(`初始化模块 ${i}，传递依赖项:`,e),t.init(e)),t.render)t.render(a);else if(t.loadUserManagementPage&&"user-management"===i)t.loadUserManagementPage(a);else if(t.loadProviderManagementPage&&"provider-management"===i)t.loadProviderManagementPage(a);else if(t.loadMerchantManagementPage&&"merchant-management"===i)t.loadMerchantManagementPage(a);else if(t.loadPage)t.loadPage(a);else{let e=!1;for(o of["show","display","load","initialize"])if(t[o]&&"function"==typeof t[o]){console.log(`使用方法 ${o} 加载模块 `+i),t[o](a),e=!0;break}if(!e)throw new Error(`模块 ${s} 缺少可用的加载方法 (render, load, show, display, initialize)`)}}catch(e){console.error(`业务模块 ${i} 加载失败:`,e),this.showErrorPage(a,t+"模块加载失败: "+e.message)}else console.error(`模块 ${i} 加载后仍然找不到实例 `+s),console.error("当前全局对象中可用的管理器:",Object.keys(window).filter(e=>e.includes("Manager"))),a.innerHTML=`
                <div class="alert alert-warning">
                    <h4><i class="bi bi-exclamation-triangle"></i> 模块加载调试信息</h4>
                    <p><strong>模块名：</strong>${i}</p>
                    <p><strong>期望的全局变量：</strong>window.${s}</p>
                    <p><strong>当前可用的管理器：</strong></p>
                    <ul>
                        ${Object.keys(window).filter(e=>e.includes("Manager")).map(e=>`<li>${e}</li>`).join("")}
                    </ul>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载页面</button>
                    <button class="btn btn-secondary" onclick="window.uiManager.navigateToPage('dashboard')">返回首页</button>
                </div>
            `}async loadModuleScript(e){let i={"user-management":"/js/modules/business/user-management.js","provider-management":"/js/modules/business/provider-management.js","merchant-management":"/js/modules/business/merchant-management.js","product-management":"/js/modules/business/product-management.js","finance-management":"/js/modules/business/finance-management.js","payment-management":"/js/modules/business/payment-management.js","transaction-management":"/js/modules/business/transaction-management.js","api-management":"/js/modules/business/api-management.js"}[e];if(i)return new Promise((e,t)=>{var a=document.createElement("script");a.src=i+"?v="+Date.now(),a.onload=e,a.onerror=()=>t(new Error(`加载脚本 ${i} 失败`)),document.head.appendChild(a)});throw new Error(`未找到模块 ${e} 的脚本路径`)}loadSecurityModule(t,a,i,e=null){console.log("加载安全模块: "+a);var s={"security-management":"SecurityManagementModule"}[a];if(window[s])try{if(e&&window[e]){var o=new window[e];if(o.initialize)o.initialize();else{if(!o.init)throw new Error(`管理器 ${e} 缺少 initialize 或 init 方法`);o.init(t)}}else{var n=window[s];if(!n.init)throw new Error(`模块 ${s} 缺少 init 方法`);n.init(t)}}catch(e){console.error(`安全模块 ${a} 加载失败:`,e),this.showErrorPage(t,i+"模块加载失败: "+e.message)}else this.showDevelopmentPage(t,i)}loadSystemModule(t,a,i,e=null){console.log("加载系统模块: "+a);var s={"system-monitoring":"SystemMonitoringModule"}[a];if(window[s])try{if(e&&window[e]){var o=new window[e];if(o.initialize)o.initialize();else{if(!o.init)throw new Error(`管理器 ${e} 缺少 initialize 或 init 方法`);o.init(t)}}else{var n=window[s];if(!n.init)throw new Error(`模块 ${s} 缺少 init 方法`);n.init(t)}}catch(e){console.error(`系统模块 ${a} 加载失败:`,e),this.showErrorPage(t,i+"模块加载失败: "+e.message)}else this.showDevelopmentPage(t,i)}showErrorPage(e,t){e.innerHTML=`
            <div class="text-center py-5">
                <i class="bi bi-exclamation-triangle" style="font-size: 4rem; color: #ef4444;"></i>
                <h3 class="mt-3">页面加载失败</h3>
                <p class="text-muted">${t}</p>
                <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
            </div>
        `}getCurrentPage(){return this.currentPage}isInterfaceInitialized(){return this.isInitialized}loadMerchantModule(t,a,i){if(console.log("加载商户模块功能: "+a),window.MerchantToolsManager)try{var e=new window.MerchantToolsManager;switch(a){case"dashboard":e.loadMerchantDashboard(t);break;case"orders":e.loadMerchantOrders(t);break;case"statistics":e.loadMerchantStatistics(t);break;case"products":e.loadMerchantProducts(t);break;case"api-keys":e.loadAPIKeys(t);break;case"code-generator":e.loadCodeGenerator(t);break;case"signature-tool":e.loadSignatureTool(t);break;case"api-docs":e.loadAPIDocumentation(t);break;case"configuration":e.loadMerchantConfiguration(t);break;default:e.loadMerchantToolsPage(t)}}catch(e){console.error(`商户模块 ${a} 加载失败:`,e),this.showErrorPage(t,i+"模块加载失败: "+e.message)}else this.showDevelopmentPage(t,i)}async loadPlatformsPage(e){e.innerHTML=`
            <div class="platform-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4><i class="bi bi-building me-2"></i>平台管理</h4>
                        <p class="text-muted mb-0">管理所有平台方及其配置</p>
                    </div>
                    <button class="btn btn-primary" onclick="window.uiManager.showAddPlatformModal()">
                        <i class="bi bi-plus"></i> 添加平台
                    </button>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                                <h5 class="mt-2">总平台数</h5>
                                <h3 class="text-primary" id="totalPlatforms">-</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-shop text-success" style="font-size: 2rem;"></i>
                                <h5 class="mt-2">总码商数</h5>
                                <h3 class="text-success" id="totalProviders">-</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-storefront text-info" style="font-size: 2rem;"></i>
                                <h5 class="mt-2">总商户数</h5>
                                <h3 class="text-info" id="totalMerchants">-</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-globe text-warning" style="font-size: 2rem;"></i>
                                <h5 class="mt-2">总域名数</h5>
                                <h3 class="text-warning" id="totalDomains">-</h3>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">平台列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="platformsTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>平台名称</th>
                                        <th>平台代码</th>
                                        <th>联系人</th>
                                        <th>域名</th>
                                        <th>码商数</th>
                                        <th>商户数</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="platformsTableBody">
                                    <tr><td colspan="10" class="text-center">加载中...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `,await this.loadPlatformsData()}async loadDomainManagementPage(e){e.innerHTML=`
            <div class="domain-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4><i class="bi bi-globe me-2"></i>域名管理</h4>
                        <p class="text-muted mb-0">管理所有租户的域名配置</p>
                    </div>
                    <button class="btn btn-primary" onclick="window.uiManager.showAddDomainModal()">
                        <i class="bi bi-plus"></i> 添加域名
                    </button>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">域名配置列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="domainsTable">
                                <thead>
                                    <tr>
                                        <th>域名</th>
                                        <th>租户类型</th>
                                        <th>租户名称</th>
                                        <th>所属平台</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="domainsTableBody">
                                    <tr><td colspan="7" class="text-center">加载中...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `,await this.loadDomainsData()}async loadPlatformsData(){try{var e=await(await fetch("/api/admin.php",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer "+this.auth.getToken()},body:JSON.stringify({action:"get_platforms_list"})})).json();0===e.error_code?(this.renderPlatformsTable(e.data.platforms),this.updatePlatformStats(e.data.stats)):this.showMessage("加载平台数据失败: "+e.error_message,"error")}catch(e){console.error("加载平台数据异常:",e),this.showMessage("网络请求失败","error")}}renderPlatformsTable(e){var t=document.getElementById("platformsTableBody");t&&(e&&0!==e.length?t.innerHTML=e.map(e=>`
            <tr>
                <td>${e.id}</td>
                <td>${e.platform_name}</td>
                <td><code>${e.platform_code}</code></td>
                <td>${e.contact_name||"-"}</td>
                <td><span class="badge bg-info">${e.domain_count||0}个</span></td>
                <td><span class="badge bg-success">${e.provider_count||0}个</span></td>
                <td><span class="badge bg-primary">${e.merchant_count||0}个</span></td>
                <td>
                    <span class="badge bg-${"active"===e.status?"success":"secondary"}">
                        ${"active"===e.status?"正常":"停用"}
                    </span>
                </td>
                <td>${new Date(e.created_at).toLocaleDateString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="window.uiManager.editPlatform(${e.id})">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="window.uiManager.viewPlatformDetails(${e.id})">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="window.uiManager.deletePlatform(${e.id})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join(""):t.innerHTML='<tr><td colspan="10" class="text-center text-muted">暂无平台数据</td></tr>')}updatePlatformStats(e){e&&(e={totalPlatforms:e.total_platforms||0,totalProviders:e.total_providers||0,totalMerchants:e.total_merchants||0,totalDomains:e.total_domains||0},Object.entries(e).forEach(([e,t])=>{e=document.getElementById(e);e&&(e.textContent=t)}))}showAddPlatformModal(){this.showMessage("添加平台功能开发中...","info")}showAddDomainModal(){this.showMessage("添加域名功能开发中...","info")}editPlatform(e){this.showMessage(`编辑平台 ${e} 功能开发中...`,"info")}viewPlatformDetails(e){this.showMessage(`查看平台 ${e} 详情功能开发中...`,"info")}deletePlatform(e){confirm("确定要删除这个平台吗？此操作不可恢复。")&&this.showMessage(`删除平台 ${e} 功能开发中...`,"info")}async loadDomainsData(){try{var e=await(await fetch("/api/admin.php",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer "+this.auth.getToken()},body:JSON.stringify({action:"get_domains_list"})})).json();0===e.error_code?this.renderDomainsTable(e.data):this.showMessage("加载域名数据失败: "+e.error_message,"error")}catch(e){console.error("加载域名数据异常:",e),this.showMessage("网络请求失败","error")}}renderDomainsTable(e){var t=document.getElementById("domainsTableBody");t&&(e&&0!==e.length?t.innerHTML=e.map(e=>`
            <tr>
                <td><code>${e.domain}</code></td>
                <td>
                    <span class="badge bg-${this.getTenantTypeBadgeColor(e.tenant_type)}">
                        ${this.getTenantTypeText(e.tenant_type)}
                    </span>
                </td>
                <td>${e.tenant_name||"-"}</td>
                <td>${e.platform_name||"-"}</td>
                <td>
                    <span class="badge bg-${"active"===e.status?"success":"secondary"}">
                        ${"active"===e.status?"正常":"停用"}
                    </span>
                </td>
                <td>${new Date(e.created_at).toLocaleDateString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="window.uiManager.editDomain('${e.domain}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="window.uiManager.deleteDomain('${e.domain}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join(""):t.innerHTML='<tr><td colspan="7" class="text-center text-muted">暂无域名数据</td></tr>')}getTenantTypeBadgeColor(e){return{system_admin:"danger",platform_admin:"warning",provider:"success",merchant:"info"}[e]||"secondary"}getTenantTypeText(e){return{system_admin:"系统管理员",platform_admin:"平台管理员",provider:"码商",merchant:"商户"}[e]||"未知"}editDomain(e){this.showMessage(`编辑域名 ${e} 功能开发中...`,"info")}deleteDomain(e){confirm(`确定要删除域名 ${e} 吗？此操作不可恢复。`)&&this.showMessage(`删除域名 ${e} 功能开发中...`,"info")}}"undefined"!=typeof module&&module.exports?module.exports={UIManager:UIManager}:(window.UIManager=UIManager,window.UiManagerModule={UIManager:UIManager,version:"1.0.0",initialized:!0},console.log("✅ UI管理器模块已导出到全局作用域"));