/**
 * 财务管理模块 - 四层架构版本
 * 支持系统方→平台方→码商→商户的财务数据管理
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-12-17
 */

class FinancialManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        
        console.log('FinancialManager initialized for tenant:', this.tenantInfo?.tenant_type);
    }

    /**
     * 初始化财务管理模块
     */
    async init() {
        try {
            // 注册页面处理器
            if (window.uiManager) {
                window.uiManager.registerPageHandler('financial', (container) => {
                    this.loadFinancialPage(container);
                });
                window.uiManager.registerPageHandler('transactions', (container) => {
                    this.loadTransactionsPage(container);
                });
                window.uiManager.registerPageHandler('platform-finance', (container) => {
                    this.loadPlatformFinancePage(container);
                });
                window.uiManager.registerPageHandler('provider-finance', (container) => {
                    this.loadProviderFinancePage(container);
                });
                window.uiManager.registerPageHandler('merchant-finance', (container) => {
                    this.loadMerchantFinancePage(container);
                });
            }
            
            console.log('✅ 财务管理模块初始化完成');
        } catch (error) {
            console.error('❌ 财务管理模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载财务管理主页面
     */
    loadFinancialPage(container) {
        const tenantType = this.tenantInfo?.tenant_type;
        
        switch (tenantType) {
            case 'system_admin':
                this.loadSystemFinancePage(container);
                break;
            case 'platform_admin':
                this.loadPlatformFinancePage(container);
                break;
            case 'provider':
                this.loadProviderFinancePage(container);
                break;
            case 'merchant':
                this.loadMerchantFinancePage(container);
                break;
            default:
                this.showAccessDenied(container);
        }
    }

    /**
     * 系统管理员财务页面
     */
    loadSystemFinancePage(container) {
        container.innerHTML = `
            <div class="financial-system">
                <div class="page-header">
                    <h2><i class="bi bi-graph-up me-2"></i>系统财务总览</h2>
                    <p class="text-muted">全系统财务数据监控与管理</p>
                </div>

                <!-- 全系统统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalPlatformsRevenue">¥0</div>
                                <div class="stat-label">全平台总收入</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-cash-stack"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalSystemFee">¥0</div>
                                <div class="stat-label">系统手续费收入</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-graph-up-arrow"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="todayTransactions">0</div>
                                <div class="stat-label">今日交易笔数</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="pendingSettlements">0</div>
                                <div class="stat-label">待处理结算</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 各平台财务状况 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>各平台财务状况</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>平台名称</th>
                                        <th>码商数量</th>
                                        <th>商户数量</th>
                                        <th>总收入</th>
                                        <th>手续费收入</th>
                                        <th>今日交易</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="platformFinanceTableBody">
                                    <tr><td colspan="7" class="text-center">加载中...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.loadSystemFinanceData();
    }

    /**
     * 平台管理员财务页面
     */
    loadPlatformFinancePage(container) {
        container.innerHTML = `
            <div class="financial-platform">
                <div class="page-header">
                    <h2><i class="bi bi-building me-2"></i>平台财务管理</h2>
                    <p class="text-muted">管理本平台的财务数据和结算</p>
                </div>

                <!-- 平台财务概览 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-cash"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="platformTotalRevenue">¥0</div>
                                <div class="stat-label">平台总收入</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-arrow-down-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="platformFeeIncome">¥0</div>
                                <div class="stat-label">手续费收入</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-arrow-up-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="platformFeeExpense">¥0</div>
                                <div class="stat-label">手续费支出</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-wallet2"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="platformBalance">¥0</div>
                                <div class="stat-label">平台余额</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#providers-finance">
                                    <i class="bi bi-shop me-2"></i>码商财务
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#merchants-finance">
                                    <i class="bi bi-storefront me-2"></i>商户财务
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#fee-management">
                                    <i class="bi bi-percent me-2"></i>手续费管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#settlement-management">
                                    <i class="bi bi-arrow-left-right me-2"></i>结算管理
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <!-- 码商财务 -->
                            <div class="tab-pane fade show active" id="providers-finance">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>码商名称</th>
                                                <th>收款总额</th>
                                                <th>结算给商户</th>
                                                <th>应付手续费</th>
                                                <th>当前余额</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="providersFinanceTableBody">
                                            <tr><td colspan="6" class="text-center">加载中...</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 商户财务 -->
                            <div class="tab-pane fade" id="merchants-finance">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>商户名称</th>
                                                <th>支付总额</th>
                                                <th>已收结算</th>
                                                <th>应付手续费</th>
                                                <th>当前余额</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="merchantsFinanceTableBody">
                                            <tr><td colspan="6" class="text-center">加载中...</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 手续费管理 -->
                            <div class="tab-pane fade" id="fee-management">
                                ${this.generateFeeManagementHTML()}
                            </div>

                            <!-- 结算管理 -->
                            <div class="tab-pane fade" id="settlement-management">
                                ${this.generateSettlementManagementHTML()}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.loadPlatformFinanceData();
        this.initializePlatformFinanceEvents();
    }

    /**
     * 码商财务页面
     */
    loadProviderFinancePage(container) {
        container.innerHTML = `
            <div class="financial-provider">
                <div class="page-header">
                    <h2><i class="bi bi-shop me-2"></i>码商财务管理</h2>
                    <p class="text-muted">管理收款、结算和财务对账</p>
                </div>

                <!-- 码商财务概览 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-arrow-down-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="providerTotalIncome">¥0</div>
                                <div class="stat-label">总收款</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-arrow-up-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="providerTotalSettlement">¥0</div>
                                <div class="stat-label">已结算给商户</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-percent"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="providerTotalFee">¥0</div>
                                <div class="stat-label">手续费支出</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-wallet2"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="providerCurrentBalance">¥0</div>
                                <div class="stat-label">当前余额</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能模块 -->
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="bi bi-list-ul me-2"></i>收款流水</h6>
                            </div>
                            <div class="card-body">
                                <div id="incomeTransactionsContainer">
                                    <!-- 收款流水将动态生成 -->
                                </div>
                                <button class="btn btn-outline-primary btn-sm" onclick="this.viewAllIncomeTransactions()">
                                    查看全部
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="bi bi-arrow-left-right me-2"></i>结算记录</h6>
                            </div>
                            <div class="card-body">
                                <div id="settlementRecordsContainer">
                                    <!-- 结算记录将动态生成 -->
                                </div>
                                <button class="btn btn-outline-success btn-sm" onclick="this.viewAllSettlements()">
                                    查看全部
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结算功能 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6><i class="bi bi-plus-circle me-2"></i>创建结算</h6>
                    </div>
                    <div class="card-body">
                        ${this.generateProviderSettlementForm()}
                    </div>
                </div>
            </div>
        `;

        this.loadProviderFinanceData();
        this.initializeProviderFinanceEvents();
    }

    /**
     * 商户财务页面
     */
    loadMerchantFinancePage(container) {
        container.innerHTML = `
            <div class="financial-merchant">
                <div class="page-header">
                    <h2><i class="bi bi-storefront me-2"></i>商户财务管理</h2>
                    <p class="text-muted">查看收入、对账和财务报表</p>
                </div>

                <!-- 商户财务概览 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-cash"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="merchantTotalIncome">¥0</div>
                                <div class="stat-label">总收入</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="merchantTotalReceived">¥0</div>
                                <div class="stat-label">已到账</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-hourglass-split"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="merchantPendingAmount">¥0</div>
                                <div class="stat-label">待结算</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-percent"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="merchantTotalFee">¥0</div>
                                <div class="stat-label">手续费</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 财务功能模块 -->
                <div class="row g-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="bi bi-receipt me-2"></i>收入流水</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>订单号</th>
                                                <th>金额</th>
                                                <th>手续费</th>
                                                <th>实收</th>
                                                <th>状态</th>
                                            </tr>
                                        </thead>
                                        <tbody id="merchantTransactionsTableBody">
                                            <tr><td colspan="6" class="text-center">加载中...</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="bi bi-graph-up me-2"></i>收入趋势</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="merchantIncomeChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 对账工具 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6><i class="bi bi-file-earmark-check me-2"></i>财务对账</h6>
                    </div>
                    <div class="card-body">
                        ${this.generateReconciliationTools()}
                    </div>
                </div>
            </div>
        `;

        this.loadMerchantFinanceData();
        this.initializeMerchantFinanceEvents();
    }

    /**
     * 生成手续费管理HTML
     */
    generateFeeManagementHTML() {
        return `
            <div class="fee-management">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6>手续费配置</h6>
                    <button class="btn btn-primary btn-sm" onclick="this.showAddFeeConfigModal()">
                        <i class="bi bi-plus"></i> 添加配置
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>对象类型</th>
                                <th>对象名称</th>
                                <th>手续费率</th>
                                <th>最小费用</th>
                                <th>最大费用</th>
                                <th>生效时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="feeConfigTableBody">
                            <tr><td colspan="7" class="text-center">加载中...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * 生成结算管理HTML
     */
    generateSettlementManagementHTML() {
        return `
            <div class="settlement-management">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6>结算记录</h6>
                    <div>
                        <select class="form-select form-select-sm d-inline-block" style="width: auto;">
                            <option value="">全部状态</option>
                            <option value="pending">待处理</option>
                            <option value="processing">处理中</option>
                            <option value="completed">已完成</option>
                            <option value="failed">失败</option>
                        </select>
                        <button class="btn btn-success btn-sm ms-2" onclick="this.showCreateSettlementModal()">
                            <i class="bi bi-plus"></i> 创建结算
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>结算ID</th>
                                <th>类型</th>
                                <th>商户</th>
                                <th>金额</th>
                                <th>手续费</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="settlementTableBody">
                            <tr><td colspan="8" class="text-center">加载中...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * 加载系统财务数据
     */
    async loadSystemFinanceData() {
        try {
            const response = await this.apiClient.request('/api/admin.php', {
                action: 'get_system_finance_overview'
            });

            if (response.error_code === 0) {
                this.updateSystemFinanceStats(response.data.stats);
                this.renderPlatformFinanceTable(response.data.platforms);
            }
        } catch (error) {
            console.error('加载系统财务数据失败:', error);
        }
    }

    /**
     * 更新系统财务统计
     */
    updateSystemFinanceStats(stats) {
        document.getElementById('totalPlatformsRevenue').textContent = `¥${stats.total_revenue || 0}`;
        document.getElementById('totalSystemFee').textContent = `¥${stats.total_system_fee || 0}`;
        document.getElementById('todayTransactions').textContent = stats.today_transactions || 0;
        document.getElementById('pendingSettlements').textContent = stats.pending_settlements || 0;
    }

    /**
     * 显示访问拒绝页面
     */
    showAccessDenied(container) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-shield-exclamation text-warning" style="font-size: 4rem;"></i>
                <h3 class="mt-3">访问被拒绝</h3>
                <p class="text-muted">您没有权限访问财务管理模块</p>
                <button class="btn btn-primary" onclick="window.uiManager.navigateToPage('dashboard')">
                    返回首页
                </button>
            </div>
        `;
    }
}

// 全局暴露
window.FinancialManager = FinancialManager; 