/**
 * 商户资料管理器
 * 从admin.js第19375-19654行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class MerchantProfileManager {
    constructor() {
        this.profileData = null;
    }
    // 初始化
    async initialize() {
        try {
            await this.loadProfile();
        } catch (error) {
            console.error('初始化商户个人资料管理器失败:',
            error);
        }
    }
    // 加载个人资料
    async loadProfile() {
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=get_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`
                }
            });
            const result = await response.json();
            if (result.error_code === 0) {
                this.profileData = result.data;
                this.renderProfile();
            } else {
                throw new Error(result.error_message || '加载个人资料失败');
            }
        } catch (error) {
            console.error('加载个人资料失败:',
            error);
            this.showError('加载个人资料失败: ' + error.message);
        }
    }
    // 渲染个人资料界面
    renderProfile() {
        const content = `
        <div class="container-fluid">
        <div class="row">
        <div class="col-12">
        <div class="card">
        <div class="card-header">
        <h5 class="card-title mb-0">
        <i class="fas fa-user-circle me-2"></i>个人资料
        </h5>
        </div>
        <div class="card-body">
        ${
            this.renderProfileForm()
        }
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
        document.getElementById('main-content').innerHTML = content;
    }
    // 渲染个人资料表单
    renderProfileForm() {
        if (!this.profileData) {
            return '<div class="text-center">暂无数据</div>';
        }
        const merchantInfo = this.profileData.merchant_info || {
        };
        const apiConfig = this.profileData.api_config || {
        };
        const accountInfo = this.profileData.account_info || {
        };
        return `
        <form id="profileForm">
        <div class="row">
        <div class="col-md-6">
        <h6 class="text-primary mb-3">
        <i class="fas fa-building me-2"></i>商户信息
        </h6>
        <div class="mb-3">
        <label class="form-label">商户名称</label>
        <input type="text" class="form-control" value="${
            merchantInfo.name || ''
        }" readonly>
        </div>
        <div class="mb-3">
        <label class="form-label">公司名称</label>
        <input type="text" class="form-control" value="${
            merchantInfo.company_name || ''
        }" readonly>
        </div>
        <div class="mb-3">
        <label class="form-label">联系人</label>
        <input type="text" class="form-control" id="contactPerson" value="${
            merchantInfo.contact_person || ''
        }">
        </div>
        <div class="mb-3">
        <label class="form-label">联系电话</label>
        <input type="text" class="form-control" id="contactPhone" value="${
            merchantInfo.contact_phone || ''
        }">
        </div>
        <div class="mb-3">
        <label class="form-label">联系邮箱</label>
        <input type="email" class="form-control" id="contactEmail" value="${
            merchantInfo.contact_email || ''
        }">
        </div>
        <div class="mb-3">
        <label class="form-label">商户描述</label>
        <textarea class="form-control" id="description" rows="3">${
            merchantInfo.description || ''
        }</textarea>
        </div>
        </div>
        <div class="col-md-6">
        <h6 class="text-success mb-3">
        <i class="fas fa-key me-2"></i>API配置
        </h6>
        <div class="mb-3">
        <label class="form-label">开发者ID</label>
        <div class="input-group">
        <input type="text" class="form-control" value="${
            apiConfig.developer_id || ''
        }" readonly>
        <button class="btn btn-outline-secondary" type="button" onclick="window.merchantProfileManager.copyToClipboard('${
            apiConfig.developer_id || ''
        }')">
        <i class="fas fa-copy"></i>
        </button>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">API密钥</label>
        <div class="input-group">
        <input type="password" class="form-control" value="${
            apiConfig.api_key || ''
        }" readonly id="apiKeyInput">
        <button class="btn btn-outline-secondary" type="button" onclick="window.merchantProfileManager.toggleApiKeyVisibility()">
        <i class="fas fa-eye" id="toggleApiKeyIcon"></i>
        </button>
        <button class="btn btn-outline-secondary" type="button" onclick="window.merchantProfileManager.copyToClipboard('${
            apiConfig.api_key || ''
        }')">
        <i class="fas fa-copy"></i>
        </button>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">回调地址</label>
        <input type="url" class="form-control" id="callbackUrl" value="${
            apiConfig.callback_url || ''
        }" placeholder="https://your-domain.com/callback">
        </div>
        <h6 class="text-info mb-3 mt-4">
        <i class="fas fa-chart-line me-2"></i>账户信息
        </h6>
        <div class="mb-3">
        <label class="form-label">账户余额</label>
        <input type="text" class="form-control" value="¥${
            accountInfo.balance || '0.00'
        }" readonly>
        </div>
        <div class="mb-3">
        <label class="form-label">服务费率</label>
        <input type="text" class="form-control" value="${
            accountInfo.service_rate || '0'
        }%" readonly>
        </div>
        <div class="mb-3">
        <label class="form-label">账户状态</label>
        <span class="badge bg-${
            this.getStatusColor(merchantInfo.status)
        } ms-2">${
            this.getStatusText(merchantInfo.status)
        }</span>
        </div>
        <div class="mb-3">
        <label class="form-label">创建时间</label>
        <input type="text" class="form-control" value="${
            accountInfo.created_at || ''
        }" readonly>
        </div>
        </div>
        </div>
        <div class="row mt-4">
        <div class="col-12">
        <div class="d-flex justify-content-end">
        <button type="button" class="btn btn-outline-secondary me-2" onclick="window.merchantProfileManager.loadProfile()">
        <i class="fas fa-sync-alt me-1"></i>刷新
        </button>
        <button type="button" class="btn btn-primary" onclick="window.merchantProfileManager.saveProfile()">
        <i class="fas fa-save me-1"></i>保存修改
        </button>
        </div>
        </div>
        </div>
        </form>
        `;
    }
    // 保存个人资料
    async saveProfile() {
        try {
            const formData = {
                contact_person: document.getElementById('contactPerson').value,
                contact_phone: document.getElementById('contactPhone').value,
                contact_email: document.getElementById('contactEmail').value,
                description: document.getElementById('description').value,
                callback_url: document.getElementById('callbackUrl').value
            };
            const response = await fetch('/api/merchant/index.php?module=tools&action=update_profile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`
                },
                body: JSON.stringify(formData)
            });
            const result = await response.json();
            if (result.error_code === 0) {
                this.showSuccess('个人资料保存成功');
                await this.loadProfile();
                // 重新加载数据
            } else {
                throw new Error(result.error_message || '保存失败');
            }
        } catch (error) {
            console.error('保存个人资料失败:',
            error);
            this.showError('保存失败: ' + error.message);
        }
    }
    // 切换API密钥显示/隐藏
    toggleApiKeyVisibility() {
        const input = document.getElementById('apiKeyInput');
        const icon = document.getElementById('toggleApiKeyIcon');
        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }
    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showSuccess('已复制到剪贴板');
        } catch (error) {
            console.error('复制失败:',
            error);
            this.showError('复制失败');
        }
    }
    // 获取状态颜色
    getStatusColor(status) {
        const colors = {
            'active': 'success',
            'approved': 'success',
            'pending': 'warning',
            'disabled': 'danger',
            'rejected': 'danger'
        };
        return colors[status] || 'secondary';
    }
    // 获取状态文本
    getStatusText(status) {
        const texts = {
            'active': '正常',
            'approved': '已批准',
            'pending': '待审核',
            'disabled': '已禁用',
            'rejected': '已拒绝'
        };
        return texts[status] || '未知';
    }
    // 显示成功消息
    showSuccess(message) {
        // 这里可以集成现有的通知系统
        alert('成功: ' + message);
    }
    // 显示错误消息
    showError(message) {
        // 这里可以集成现有的通知系统
        alert('错误: ' + message);
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = MerchantProfileManager;
} else if (typeof window !== "undefined") {
    window.MerchantProfileManager = MerchantProfileManager;
}

console.log('📦 MerchantProfileManager 模块加载完成');
