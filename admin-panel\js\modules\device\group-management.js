/**
 * 小组管理模块
 * 对接小组管理API，提供完整的小组管理功能
 */
class GroupManager {
    constructor() {
        this.apiBase = '/api/device';
        this.currentPage = 1;
        this.pageSize = 20;
        this.groups = [];
        this.employees = [];
        this.isInitialized = false;
        
        console.log('GroupManager initialized');
    }

    /**
     * 初始化小组管理模块
     */
    async init(container) {
        try {
            this.renderGroupManagement(container);
            await this.loadGroups();
            await this.loadEmployees();
            this.bindEvents();
        } catch (error) {
            console.error('小组管理模块初始化失败:', error);
            this.showError('小组管理模块初始化失败');
        }
    }

    /**
     * 渲染小组管理界面
     */
    renderGroupManagement(container) {
        container.innerHTML = `
            <div class="group-management-module">
                <div class="row">
                    <!-- 左侧小组列表 -->
                    <div class="col-md-8">
                        <!-- 页面头部 -->
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4><i class="bi bi-people me-2"></i>小组管理</h4>
                            <div class="btn-group">
                                <button class="btn btn-primary" id="createGroupBtn">
                                    <i class="bi bi-plus"></i> 创建小组
                                </button>
                                <button class="btn btn-outline-secondary" id="refreshGroups">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>

                        <!-- 小组列表 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">小组列表</h6>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>小组识别码</th>
                                                <th>小组长</th>
                                                <th>大组长</th>
                                                <th>设备数量</th>
                                                <th>状态</th>
                                                <th>创建时间</th>
                                                <th width="150">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="groupTableBody">
                                            <!-- 小组列表将在这里动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div id="groupLoadingIndicator" class="text-center p-4" style="display: none;">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2 text-muted">正在加载小组数据...</p>
                                </div>
                                <div id="groupNoDataIndicator" class="text-center p-4" style="display: none;">
                                    <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                                    <p class="mt-2 text-muted">暂无小组数据</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧操作面板 -->
                    <div class="col-md-4">
                        <!-- 快速统计 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-bar-chart me-2"></i>小组统计</h6>
                            </div>
                            <div class="card-body">
                                <div id="groupStats">
                                    <!-- 统计信息将在这里动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- DNS管理 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-globe me-2"></i>DNS管理</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">DNS域名</label>
                                    <input type="text" class="form-control" id="dnsDomainInput" 
                                           placeholder="api.paypal-app.com" readonly>
                                </div>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary btn-sm" id="refreshDnsBtn">
                                        <i class="bi bi-arrow-clockwise"></i> 更新DNS记录
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" id="viewDnsRecordsBtn">
                                        <i class="bi bi-list"></i> 查看所有记录
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 快速操作 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-lightning me-2"></i>快速操作</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-success btn-sm" id="createMultipleGroupsBtn">
                                        <i class="bi bi-plus-circle"></i> 批量创建小组
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" id="assignLeadersBtn">
                                        <i class="bi bi-person-badge"></i> 批量分配组长
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" id="exportGroupsBtn">
                                        <i class="bi bi-download"></i> 导出小组信息
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 创建/编辑小组模态框 -->
            <div class="modal fade" id="groupFormModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="groupFormModalTitle">创建小组</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="groupForm">
                                <input type="hidden" id="groupIdInput">
                                <div class="mb-3">
                                    <label class="form-label">小组识别码</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="groupCodeInput" 
                                               placeholder="6位数字+字母组合" maxlength="6" required>
                                        <button class="btn btn-outline-secondary" type="button" id="generateGroupCodeBtn">
                                            <i class="bi bi-dice-5"></i> 随机生成
                                        </button>
                                    </div>
                                    <div class="form-text">识别码用于设备验证，格式：A3B7F2</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">小组长</label>
                                    <select class="form-select" id="teamLeaderSelect">
                                        <option value="">请选择小组长</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">大组长</label>
                                    <select class="form-select" id="groupManagerSelect">
                                        <option value="">请选择大组长</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">设备配额</label>
                                    <input type="number" class="form-control" id="maxDevicesInput" 
                                           min="1" max="100" value="10" required>
                                    <div class="form-text">该小组最多可容纳的设备数量</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <select class="form-select" id="groupStatusSelect">
                                        <option value="active">启用</option>
                                        <option value="inactive">停用</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">备注</label>
                                    <textarea class="form-control" id="groupRemarksInput" rows="3" 
                                              placeholder="可选的备注信息..."></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveGroupBtn">保存</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 小组详情模态框 -->
            <div class="modal fade" id="groupDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">小组详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="groupDetailContent">
                            <!-- 小组详情内容 -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- DNS记录查看模态框 -->
            <div class="modal fade" id="dnsRecordsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">DNS记录管理</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6>当前DNS记录</h6>
                                    <button class="btn btn-outline-primary btn-sm" id="refreshDnsRecordsBtn">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新记录
                                    </button>
                                </div>
                            </div>
                            <div id="dnsRecordsContent">
                                <!-- DNS记录内容 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量创建小组模态框 -->
            <div class="modal fade" id="batchCreateModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">批量创建小组</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="batchCreateForm">
                                <div class="mb-3">
                                    <label class="form-label">创建数量</label>
                                    <input type="number" class="form-control" id="batchCreateCount" 
                                           min="1" max="20" value="5" required>
                                    <div class="form-text">一次最多创建20个小组</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">识别码前缀</label>
                                    <input type="text" class="form-control" id="batchCreatePrefix" 
                                           placeholder="如：GRP" maxlength="3">
                                    <div class="form-text">可选，识别码前缀，后面会自动添加数字</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">默认大组长</label>
                                    <select class="form-select" id="batchDefaultManagerSelect">
                                        <option value="">请选择默认大组长</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">默认设备配额</label>
                                    <input type="number" class="form-control" id="batchMaxDevices" 
                                           min="1" max="100" value="10" required>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmBatchCreateBtn">开始创建</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 创建小组按钮
        document.getElementById('createGroupBtn').addEventListener('click', () => {
            this.showGroupForm();
        });

        // 刷新按钮
        document.getElementById('refreshGroups').addEventListener('click', () => {
            this.loadGroups();
        });

        // 保存小组按钮
        document.getElementById('saveGroupBtn').addEventListener('click', () => {
            this.saveGroup();
        });

        // 生成识别码按钮
        document.getElementById('generateGroupCodeBtn').addEventListener('click', () => {
            this.generateGroupCode();
        });

        // DNS管理按钮
        document.getElementById('refreshDnsBtn').addEventListener('click', () => {
            this.refreshDnsRecords();
        });

        document.getElementById('viewDnsRecordsBtn').addEventListener('click', () => {
            this.showDnsRecords();
        });

        // 批量操作按钮
        document.getElementById('createMultipleGroupsBtn').addEventListener('click', () => {
            this.showBatchCreateModal();
        });

        document.getElementById('confirmBatchCreateBtn').addEventListener('click', () => {
            this.batchCreateGroups();
        });

        // 导出按钮
        document.getElementById('exportGroupsBtn').addEventListener('click', () => {
            this.exportGroups();
        });
    }

    /**
     * 加载小组列表
     */
    async loadGroups() {
        try {
            this.showGroupLoading(true);
            
            const response = await this.makeApiRequest('/group_management.php?action=list', 'GET');
            
            if (response.error_code === 0) {
                this.groups = response.data.groups;
                this.renderGroupList();
                this.renderGroupStats(response.data.stats);
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('加载小组列表失败:', error);
            this.showError('加载小组列表失败');
        } finally {
            this.showGroupLoading(false);
        }
    }

    /**
     * 加载员工列表
     */
    async loadEmployees() {
        try {
            const response = await this.makeApiRequest('/group_management.php?action=employees', 'GET');
            if (response.error_code === 0) {
                this.employees = response.data.employees;
                this.updateEmployeeSelects();
            }
        } catch (error) {
            console.error('加载员工列表失败:', error);
        }
    }

    /**
     * 更新员工选择框
     */
    updateEmployeeSelects() {
        const teamLeaderSelect = document.getElementById('teamLeaderSelect');
        const groupManagerSelect = document.getElementById('groupManagerSelect');
        const batchDefaultManagerSelect = document.getElementById('batchDefaultManagerSelect');
        
        // 清空现有选项
        teamLeaderSelect.innerHTML = '<option value="">请选择小组长</option>';
        groupManagerSelect.innerHTML = '<option value="">请选择大组长</option>';
        batchDefaultManagerSelect.innerHTML = '<option value="">请选择默认大组长</option>';
        
        this.employees.forEach(employee => {
            const option = `<option value="${employee.id}">${employee.username} (${employee.job_position})</option>`;
            
            if (employee.job_position === 'team_leader') {
                teamLeaderSelect.innerHTML += option;
            } else if (employee.job_position === 'group_manager') {
                groupManagerSelect.innerHTML += option;
                batchDefaultManagerSelect.innerHTML += option;
            }
        });
    }

    /**
     * 渲染小组列表
     */
    renderGroupList() {
        const tbody = document.getElementById('groupTableBody');
        
        if (this.groups.length === 0) {
            tbody.innerHTML = '';
            document.getElementById('groupNoDataIndicator').style.display = 'block';
            return;
        }
        
        document.getElementById('groupNoDataIndicator').style.display = 'none';
        
        tbody.innerHTML = this.groups.map(group => `
            <tr>
                <td>
                    <div class="fw-bold">${group.group_code}</div>
                    <small class="text-muted">ID: ${group.id}</small>
                </td>
                <td>
                    ${group.team_leader_name ? 
                      `<span class="badge bg-primary">${group.team_leader_name}</span>` : 
                      '<span class="text-muted">未分配</span>'}
                </td>
                <td>
                    ${group.group_manager_name ? 
                      `<span class="badge bg-info">${group.group_manager_name}</span>` : 
                      '<span class="text-muted">未分配</span>'}
                </td>
                <td>
                    <div>${group.current_devices}/${group.max_devices}</div>
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar ${this.getProgressBarClass(group.current_devices, group.max_devices)}" 
                             style="width: ${(group.current_devices / group.max_devices) * 100}%"></div>
                    </div>
                </td>
                <td>${this.renderGroupStatusBadge(group.status)}</td>
                <td>
                    <small>${new Date(group.created_at).toLocaleString()}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="groupManager.showGroupDetail(${group.id})" 
                                title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="groupManager.editGroup(${group.id})" 
                                title="编辑小组">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" onclick="groupManager.manageDevices(${group.id})">
                                    <i class="bi bi-phone"></i> 管理设备
                                </a></li>
                                <li><a class="dropdown-item" onclick="groupManager.regenerateKey(${group.id})">
                                    <i class="bi bi-key"></i> 重新生成密钥
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item ${group.status === 'active' ? 'text-warning' : 'text-success'}" 
                                       onclick="groupManager.toggleGroupStatus(${group.id})">
                                    <i class="bi bi-${group.status === 'active' ? 'pause' : 'play'}"></i> 
                                    ${group.status === 'active' ? '停用' : '启用'}
                                </a></li>
                                <li><a class="dropdown-item text-danger" onclick="groupManager.deleteGroup(${group.id})">
                                    <i class="bi bi-trash"></i> 删除
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 渲染小组统计
     */
    renderGroupStats(stats) {
        const container = document.getElementById('groupStats');
        
        container.innerHTML = `
            <div class="row g-2">
                <div class="col-6">
                    <div class="stat-card bg-primary bg-opacity-10 border-primary">
                        <div class="stat-value">${stats.total_groups}</div>
                        <div class="stat-label">总小组数</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stat-card bg-success bg-opacity-10 border-success">
                        <div class="stat-value">${stats.active_groups}</div>
                        <div class="stat-label">活跃小组</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stat-card bg-info bg-opacity-10 border-info">
                        <div class="stat-value">${stats.total_devices}</div>
                        <div class="stat-label">总设备数</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stat-card bg-warning bg-opacity-10 border-warning">
                        <div class="stat-value">${stats.unassigned_leaders}</div>
                        <div class="stat-label">未分配组长</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 显示小组表单
     */
    showGroupForm(groupId = null) {
        const modal = document.getElementById('groupFormModal');
        const title = document.getElementById('groupFormModalTitle');
        const form = document.getElementById('groupForm');
        
        form.reset();
        
        if (groupId) {
            title.textContent = '编辑小组';
            document.getElementById('groupIdInput').value = groupId;
            this.loadGroupData(groupId);
        } else {
            title.textContent = '创建小组';
            document.getElementById('groupIdInput').value = '';
            this.generateGroupCode();
        }
        
        new bootstrap.Modal(modal).show();
    }

    /**
     * 生成小组识别码
     */
    generateGroupCode() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        document.getElementById('groupCodeInput').value = result;
    }

    /**
     * 保存小组
     */
    async saveGroup() {
        try {
            const form = document.getElementById('groupForm');
            const formData = new FormData(form);
            
            const data = {
                group_code: document.getElementById('groupCodeInput').value,
                team_leader_id: document.getElementById('teamLeaderSelect').value || null,
                group_manager_id: document.getElementById('groupManagerSelect').value || null,
                max_devices: parseInt(document.getElementById('maxDevicesInput').value),
                status: document.getElementById('groupStatusSelect').value,
                remarks: document.getElementById('groupRemarksInput').value
            };
            
            const groupId = document.getElementById('groupIdInput').value;
            const endpoint = groupId ? 
                `/group_management.php?action=update&id=${groupId}` : 
                '/group_management.php?action=create';
            
            const response = await this.makeApiRequest(endpoint, 'POST', data);
            
            if (response.error_code === 0) {
                bootstrap.Modal.getInstance(document.getElementById('groupFormModal')).hide();
                this.loadGroups();
                this.showSuccess(groupId ? '小组更新成功' : '小组创建成功');
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('保存小组失败:', error);
            this.showError('保存小组失败');
        }
    }

    /**
     * 显示小组详情
     */
    async showGroupDetail(groupId) {
        try {
            const response = await this.makeApiRequest(`/group_management.php?action=detail&id=${groupId}`, 'GET');
            if (response.error_code === 0) {
                this.renderGroupDetail(response.data);
                new bootstrap.Modal(document.getElementById('groupDetailModal')).show();
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('获取小组详情失败:', error);
            this.showError('获取小组详情失败');
        }
    }

    /**
     * 渲染小组详情
     */
    renderGroupDetail(group) {
        const content = document.getElementById('groupDetailContent');
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>小组识别码</td><td><code>${group.group_code}</code></td></tr>
                        <tr><td>解密密钥</td><td><code class="text-muted">${group.decrypt_key?.substring(0, 16)}...</code></td></tr>
                        <tr><td>设备配额</td><td>${group.current_devices}/${group.max_devices}</td></tr>
                        <tr><td>状态</td><td>${this.renderGroupStatusBadge(group.status)}</td></tr>
                        <tr><td>创建时间</td><td>${new Date(group.created_at).toLocaleString()}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>管理人员</h6>
                    <table class="table table-sm">
                        <tr><td>小组长</td><td>${group.team_leader_name || '未分配'}</td></tr>
                        <tr><td>大组长</td><td>${group.group_manager_name || '未分配'}</td></tr>
                        <tr><td>创建者</td><td>${group.created_by_name || '未知'}</td></tr>
                    </table>
                    
                    <h6 class="mt-3">DNS记录</h6>
                    <div class="border rounded p-2 bg-light">
                        <small class="font-monospace">${group.dns_record || '无DNS记录'}</small>
                    </div>
                </div>
            </div>
            ${group.devices && group.devices.length > 0 ? `
                <div class="mt-4">
                    <h6>设备列表</h6>
                    <div class="table-responsive" style="max-height: 300px;">
                        <table class="table table-sm">
                            <thead>
                                <tr><th>设备ID</th><th>状态</th><th>最后签到</th><th>创建时间</th></tr>
                            </thead>
                            <tbody>
                                ${group.devices.map(device => `
                                    <tr>
                                        <td>${device.device_id}</td>
                                        <td>${this.renderStatusBadge(device.status)}</td>
                                        <td><small>${device.last_checkin_time || '从未签到'}</small></td>
                                        <td><small>${new Date(device.created_at).toLocaleString()}</small></td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            ` : ''}
        `;
    }

    /**
     * 编辑小组
     */
    editGroup(groupId) {
        this.showGroupForm(groupId);
    }

    /**
     * 加载小组数据到表单
     */
    async loadGroupData(groupId) {
        try {
            const response = await this.makeApiRequest(`/group_management.php?action=detail&id=${groupId}`, 'GET');
            if (response.error_code === 0) {
                const group = response.data;
                document.getElementById('groupCodeInput').value = group.group_code;
                document.getElementById('teamLeaderSelect').value = group.team_leader_id || '';
                document.getElementById('groupManagerSelect').value = group.group_manager_id || '';
                document.getElementById('maxDevicesInput').value = group.max_devices;
                document.getElementById('groupStatusSelect').value = group.status;
                document.getElementById('groupRemarksInput').value = group.remarks || '';
            }
        } catch (error) {
            console.error('加载小组数据失败:', error);
        }
    }

    /**
     * 删除小组
     */
    async deleteGroup(groupId) {
        if (!confirm('确认删除该小组？删除后该小组下的所有设备将被取消分配。')) {
            return;
        }
        
        try {
            const response = await this.makeApiRequest(`/group_management.php?action=delete&id=${groupId}`, 'DELETE');
            if (response.error_code === 0) {
                this.loadGroups();
                this.showSuccess('小组删除成功');
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('删除小组失败:', error);
            this.showError('删除小组失败');
        }
    }

    /**
     * 切换小组状态
     */
    async toggleGroupStatus(groupId) {
        try {
            const group = this.groups.find(g => g.id === groupId);
            const newStatus = group.status === 'active' ? 'inactive' : 'active';
            
            if (!confirm(`确认${newStatus === 'active' ? '启用' : '停用'}该小组？`)) {
                return;
            }
            
            const response = await this.makeApiRequest(
                `/group_management.php?action=update_status&id=${groupId}`, 
                'POST', 
                { status: newStatus }
            );
            
            if (response.error_code === 0) {
                this.loadGroups();
                this.showSuccess(`小组${newStatus === 'active' ? '启用' : '停用'}成功`);
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('切换小组状态失败:', error);
            this.showError('切换小组状态失败');
        }
    }

    /**
     * 重新生成密钥
     */
    async regenerateKey(groupId) {
        if (!confirm('确认重新生成该小组的解密密钥？重新生成后现有设备需要重新配置。')) {
            return;
        }
        
        try {
            const response = await this.makeApiRequest(
                `/group_management.php?action=regenerate_key&id=${groupId}`, 
                'POST'
            );
            
            if (response.error_code === 0) {
                this.loadGroups();
                this.showSuccess('解密密钥重新生成成功');
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('重新生成密钥失败:', error);
            this.showError('重新生成密钥失败');
        }
    }

    /**
     * 显示批量创建模态框
     */
    showBatchCreateModal() {
        new bootstrap.Modal(document.getElementById('batchCreateModal')).show();
    }

    /**
     * 批量创建小组
     */
    async batchCreateGroups() {
        try {
            const count = parseInt(document.getElementById('batchCreateCount').value);
            const prefix = document.getElementById('batchCreatePrefix').value;
            const defaultManagerId = document.getElementById('batchDefaultManagerSelect').value;
            const maxDevices = parseInt(document.getElementById('batchMaxDevices').value);
            
            if (count < 1 || count > 20) {
                this.showError('创建数量必须在1-20之间');
                return;
            }
            
            const data = {
                count: count,
                prefix: prefix,
                default_manager_id: defaultManagerId || null,
                max_devices: maxDevices
            };
            
            const response = await this.makeApiRequest('/group_management.php?action=batch_create', 'POST', data);
            if (response.error_code === 0) {
                bootstrap.Modal.getInstance(document.getElementById('batchCreateModal')).hide();
                this.loadGroups();
                this.showSuccess(`批量创建成功，共创建 ${response.data.created_count} 个小组`);
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('批量创建小组失败:', error);
            this.showError('批量创建小组失败');
        }
    }

    /**
     * 刷新DNS记录
     */
    async refreshDnsRecords() {
        try {
            const response = await this.makeApiRequest('/group_management.php?action=refresh_dns', 'POST');
            if (response.error_code === 0) {
                this.showSuccess('DNS记录更新成功');
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('刷新DNS记录失败:', error);
            this.showError('刷新DNS记录失败');
        }
    }

    /**
     * 显示DNS记录
     */
    async showDnsRecords() {
        try {
            const response = await this.makeApiRequest('/group_management.php?action=dns_records', 'GET');
            if (response.error_code === 0) {
                this.renderDnsRecords(response.data.records);
                new bootstrap.Modal(document.getElementById('dnsRecordsModal')).show();
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('获取DNS记录失败:', error);
            this.showError('获取DNS记录失败');
        }
    }

    /**
     * 渲染DNS记录
     */
    renderDnsRecords(records) {
        const content = document.getElementById('dnsRecordsContent');
        
        if (records.length === 0) {
            content.innerHTML = '<div class="text-center text-muted">暂无DNS记录</div>';
            return;
        }
        
        content.innerHTML = `
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>小组识别码</th>
                            <th>DNS记录</th>
                            <th>状态</th>
                            <th>更新时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${records.map(record => `
                            <tr>
                                <td><code>${record.group_code}</code></td>
                                <td><small class="font-monospace">${record.dns_record}</small></td>
                                <td>${record.is_active ? 
                                    '<span class="badge bg-success">活跃</span>' : 
                                    '<span class="badge bg-secondary">停用</span>'}</td>
                                <td><small>${new Date(record.updated_at).toLocaleString()}</small></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * 导出小组信息
     */
    exportGroups() {
        const data = this.groups.map(group => ({
            '小组识别码': group.group_code,
            '小组长': group.team_leader_name || '未分配',
            '大组长': group.group_manager_name || '未分配',
            '设备数量': `${group.current_devices}/${group.max_devices}`,
            '状态': group.status === 'active' ? '启用' : '停用',
            '创建时间': new Date(group.created_at).toLocaleString()
        }));
        
        this.downloadCSV(data, '小组列表.csv');
    }

    /**
     * 下载CSV文件
     */
    downloadCSV(data, filename) {
        if (data.length === 0) return;
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
        ].join('\n');
        
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    }

    /**
     * 工具方法
     */
    renderGroupStatusBadge(status) {
        const config = {
            active: { class: 'success', text: '启用' },
            inactive: { class: 'secondary', text: '停用' }
        };
        
        const statusConfig = config[status] || { class: 'secondary', text: status };
        return `<span class="badge bg-${statusConfig.class}">${statusConfig.text}</span>`;
    }

    renderStatusBadge(status) {
        const statusConfig = {
            pending: { class: 'warning', text: '待审核' },
            active: { class: 'success', text: '已激活' },
            inactive: { class: 'secondary', text: '已停用' },
            maintenance: { class: 'info', text: '维护中' },
            banned: { class: 'danger', text: '已禁用' },
            rejected: { class: 'danger', text: '已拒绝' }
        };
        
        const config = statusConfig[status] || { class: 'secondary', text: status };
        return `<span class="badge bg-${config.class}">${config.text}</span>`;
    }

    getProgressBarClass(current, max) {
        const ratio = current / max;
        if (ratio >= 0.9) return 'bg-danger';
        if (ratio >= 0.7) return 'bg-warning';
        return 'bg-success';
    }

    async makeApiRequest(endpoint, method = 'GET', data = null) {
        const url = `${this.apiBase}${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.getAuthToken()}`
            }
        };
        
        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        return await response.json();
    }

    getAuthToken() {
        return localStorage.getItem('admin_token') || sessionStorage.getItem('admin_token') || '';
    }

    showGroupLoading(show) {
        document.getElementById('groupLoadingIndicator').style.display = show ? 'block' : 'none';
    }

    showError(message) {
        alert(message);
    }

    showSuccess(message) {
        alert(message);
    }
}

// 创建全局实例
window.groupManager = new GroupManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GroupManager };
} else {
    // 浏览器环境 - 导出类到全局作用域
    window.GroupManager = GroupManager;
    
    // 为模块加载器导出整个模块
    window.GroupManagementModule = {
        GroupManager,
        version: '1.0.0',
        initialized: true
    };
    
    console.log('✅ 小组管理模块已导出到全局作用域');
}

// export default GroupManager; // 注释掉ES6导出，使用全局变量
window.GroupManager = GroupManager; 