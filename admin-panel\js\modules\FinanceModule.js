/**
 * 财务模块 - 统一财务管理
 * 整合finance-management.js、financial-management.js、financial-manager.js
 * 实现界面共用+数据差异化的设计模式
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-01-20
 */

class FinanceModule {
    constructor() {

        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        this.currentPage = 1;
        this.pageSize = 20;
        this.dateRange = { start: '', end: '' };
        this.currentRole = null;
        
        console.log('✅ FinanceModule initialized');
    }

    /**
     * 统一渲染方法 - 方案5配置驱动的入口
     * @param {HTMLElement} container 容器元素
     * @param {Object} params 参数对象 {role: 'merchant'|'provider'|'platform_admin'|'system_admin'}
     */
    async render(container, params = {}) {
        try {
            // 确定用户角色和页面类型
            this.currentRole = params.role || this.getCurrentUserRole();
            this.currentPage = params.activeTab || this.getPageFromURL();
            
            console.log(`🎯 财务模块渲染 - 角色: ${this.currentRole}, 活跃选项卡: ${this.currentPage}`);
            
            // 显示加载状态
            container.innerHTML = UIComponents.generateLoadingState('正在加载财务数据...');
            
            // 根据页面类型决定渲染方式
            if (this.currentPage && this.currentPage !== 'overview') {
                // 渲染特定页面内容
                container.innerHTML = this.generateSpecificPageHTML(this.currentPage);
                this.initializePageEvents(this.currentPage);
                await this.loadPageData(this.currentPage);
            } else {
                // 渲染选项卡界面（默认）
                const config = RoleDataAdapter.getFinanceConfig(this.currentRole);
                container.innerHTML = this.generateHTML(config);
                this.initializeEvents();
                await this.loadData();
            }
            
        } catch (error) {
            console.error('❌ 财务模块渲染失败:', error);
            container.innerHTML = UIComponents.generateEmptyState({
                icon: 'bi-exclamation-triangle',
                title: '财务模块加载失败',
                description: error.message
            });
        }
    }

    /**
     * 获取当前用户角色
     */
    getCurrentUserRole() {
        if (this.tenantInfo?.tenant_type) {
            return this.tenantInfo.tenant_type;
        }
        
        const user = this.authManager?.getUser();
        return user?.user_type || 'merchant';
    }

    /**
     * 从URL获取页面类型
     */
    getPageFromURL() {
        const path = window.location.pathname;
        const pageMap = {
            '/finance-reconcile': 'reconcile',
            '/settlement-records': 'settlement',
            '/realtime-balance': 'balance',
            '/finance-reports': 'reports',
            '/realtime-flow': 'flow'
        };
        return pageMap[path] || null;
    }

    /**
     * 生成特定页面的HTML
     */
    generateSpecificPageHTML(pageType) {
        const pageConfig = this.getPageConfig(pageType);
        
        return `
            <div class="finance-module finance-${pageType}" data-role="${this.currentRole}" data-page="${pageType}">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi ${pageConfig.icon} me-2"></i>${pageConfig.title}</h2>
                            <p class="text-muted mb-0">${pageConfig.description}</p>
                        </div>
                        <div>
                            ${this.generatePageHeaderButtons(pageConfig)}
                        </div>
                    </div>
                </div>

                <!-- 页面内容 -->
                <div id="page-content">
                    ${this.generatePageContent(pageType)}
                </div>
            </div>

            <style>
                ${this.generateStyles()}
            </style>
        `;
    }

    /**
     * 获取页面配置
     */
    getPageConfig(pageType) {
        const configs = {
            'reconcile': {
                title: '财务对账',
                description: '核对交易记录和财务数据',
                icon: 'bi-check2-square',
                color: 'primary'
            },
            'settlement': {
                title: '结算记录',
                description: '查看历史结算记录',
                icon: 'bi-bank',
                color: 'success'
            },
            'balance': {
                title: '实时余额',
                description: '查看账户实时余额信息',
                icon: 'bi-wallet2',
                color: 'info'
            },
            'reports': {
                title: '财务报表',
                description: '财务数据统计分析报表',
                icon: 'bi-file-earmark-bar-graph',
                color: 'warning'
            }
        };
        return configs[pageType] || configs['reconcile'];
    }

    /**
     * 生成页面头部按钮
     */
    generatePageHeaderButtons(pageConfig) {
        return `
            <button class="btn btn-primary" id="refreshPageBtn">
                <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
            </button>
            <button class="btn btn-outline-secondary ms-2" id="exportPageBtn">
                <i class="bi bi-download me-2"></i>导出数据
            </button>
        `;
    }

    /**
     * 生成页面内容 - 映射到现有已开发功能
     */
    generatePageContent(pageType) {
        switch (pageType) {
            case 'reconcile':
                // 财务对账 → 使用概览功能
                return this.generateOverviewContent();
            case 'settlement':
                // 结算记录 → 使用结算管理功能  
                return this.generateSettlementsContent();
            case 'balance':
                // 实时余额 → 使用交易流水功能（显示余额变动）
                return this.generateTransactionsContent();
            case 'reports':
                // 财务报表 → 使用报表功能
                return this.generateReportsContent();
            case 'flow':
                // 实时流水 → 使用交易流水功能
                return this.generateTransactionsContent();
            default:
                return '<div class="text-center py-5">页面内容加载中...</div>';
        }
    }

    /**
     * 初始化特定页面事件
     */
    initializePageEvents(pageType) {
        // 绑定页面特定的事件处理器
        const refreshBtn = document.getElementById('refreshPageBtn');
        const exportBtn = document.getElementById('exportPageBtn');

        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                console.log(`刷新${pageType}页面数据`);
                this.loadPageData(pageType);
            });
        }

        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                console.log(`导出${pageType}页面数据`);
            });
        }
    }

    /**
     * 加载特定页面数据
     */
    async loadPageData(pageType) {
        try {
            console.log(`📊 加载${pageType}页面数据...`);
            // 根据页面类型加载对应的数据
            // 这里可以调用不同的API获取数据
        } catch (error) {
            console.error(`❌ 加载${pageType}页面数据失败:`, error);
        }
    }

    /**
     * 生成统一的HTML界面
     */
    generateHTML(config) {
        // 准备统计数据（模拟）
        const mockStats = this.getMockStatsData(this.currentRole);
        config.stats.forEach((stat, index) => {
            stat.value = mockStats[index] || '0';
        });
        
        return `
            <div class="finance-module" data-role="${this.currentRole}">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-currency-dollar me-2"></i>${config.title}</h2>
                            <p class="text-muted mb-0">财务数据管理与分析</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="generateReportBtn">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>生成报表
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="exportFinanceBtn">
                                <i class="bi bi-download me-2"></i>导出数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片区域 -->
                ${UIComponents.generateStatsCards(config.stats, this.currentRole)}

                <!-- 功能选项卡 -->
                ${UIComponents.generateTabs(config.tabs, config.tabs[0].id, this.currentRole)}
            </div>

            <style>
                ${this.generateStyles()}
            </style>
        `;
    }

    /**
     * 根据角色获取配置
     */
    getRoleConfig(role) {
        const configs = {
            'system_admin': {
                title: '系统财务总览',
                description: '全系统财务数据监控与管理',
                icon: 'bi bi-globe',
                color: 'primary'
            },
            'platform_admin': {
                title: '平台财务管理',
                description: '管理本平台的财务数据和结算',
                icon: 'bi bi-building',
                color: 'success'
            },
            'provider': {
                title: '设备收益管理',
                description: '设备收益、提现和结算管理',
                icon: 'bi bi-phone',
                color: 'info'
            },
            'merchant': {
                title: '交易财务管理',
                description: '交易流水、手续费和结算记录',
                icon: 'bi bi-shop',
                color: 'warning'
            }
        };
        
        return configs[role] || configs['merchant'];
    }

    /**
     * 生成统计卡片
     */
    generateStatsCards() {
        const statsConfig = this.getStatsConfig(this.currentRole);
        
        return statsConfig.map(stat => `
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-${stat.color}">
                        <i class="${stat.icon}"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="${stat.id}">-</div>
                        <div class="stat-label">${stat.label}</div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 根据角色获取统计配置
     */
    getStatsConfig(role) {
        const configs = {
            'system_admin': [
                { id: 'totalPlatformsRevenue', label: '全平台总收入', icon: 'bi bi-building', color: 'primary' },
                { id: 'totalSystemFee', label: '系统手续费收入', icon: 'bi bi-cash-stack', color: 'success' },
                { id: 'todayTransactions', label: '今日交易笔数', icon: 'bi bi-graph-up-arrow', color: 'info' },
                { id: 'pendingSettlements', label: '待处理结算', icon: 'bi bi-exclamation-triangle', color: 'warning' }
            ],
            'platform_admin': [
                { id: 'platformTotalRevenue', label: '平台总收入', icon: 'bi bi-cash', color: 'primary' },
                { id: 'platformFeeIncome', label: '手续费收入', icon: 'bi bi-arrow-down-circle', color: 'success' },
                { id: 'providerCount', label: '码商数量', icon: 'bi bi-people', color: 'info' },
                { id: 'merchantCount', label: '商户数量', icon: 'bi bi-shop', color: 'warning' }
            ],
            'provider': [
                { id: 'deviceRevenue', label: '设备总收益', icon: 'bi bi-phone', color: 'primary' },
                { id: 'availableBalance', label: '可提现余额', icon: 'bi bi-wallet2', color: 'success' },
                { id: 'todayOrders', label: '今日订单', icon: 'bi bi-list-check', color: 'info' },
                { id: 'pendingWithdraw', label: '待处理提现', icon: 'bi bi-hourglass-split', color: 'warning' }
            ],
            'merchant': [
                { id: 'totalRevenue', label: '总收入', icon: 'bi bi-cash-stack', color: 'success' },
                { id: 'availableBalance', label: '可用余额', icon: 'bi bi-wallet2', color: 'primary' },
                { id: 'pendingSettlement', label: '待结算', icon: 'bi bi-hourglass-split', color: 'warning' },
                { id: 'feeIncome', label: '手续费', icon: 'bi bi-percent', color: 'info' }
            ]
        };
        
        return configs[role] || configs['merchant'];
    }

    /**
     * 生成选项卡标题
     */
    generateTabHeaders() {
        const tabs = this.getTabsConfig(this.currentRole);
        
        return tabs.map((tab, index) => `
            <li class="nav-item">
                <a class="nav-link ${index === 0 ? 'active' : ''}" 
                   id="${tab.id}-tab" 
                   data-bs-toggle="tab" 
                   href="#${tab.id}">
                    <i class="${tab.icon} me-2"></i>${tab.title}
                </a>
            </li>
        `).join('');
    }

    /**
     * 生成选项卡内容
     */
    generateTabContent() {
        const tabs = this.getTabsConfig(this.currentRole);
        
        return tabs.map((tab, index) => `
            <div class="tab-pane fade ${index === 0 ? 'show active' : ''}" id="${tab.id}">
                ${this.generateTabPaneContent(tab)}
            </div>
        `).join('');
    }

    /**
     * 根据角色获取选项卡配置
     */
    getTabsConfig(role) {
        const configs = {
            'system_admin': [
                { id: 'overview', title: '系统概览', icon: 'bi bi-graph-up', type: 'overview' },
                { id: 'platforms', title: '平台财务', icon: 'bi bi-building', type: 'platforms' },
                { id: 'settlements', title: '结算管理', icon: 'bi bi-calendar-check', type: 'settlements' },
                { id: 'reports', title: '财务报表', icon: 'bi bi-file-earmark-text', type: 'reports' }
            ],
            'platform_admin': [
                { id: 'overview', title: '平台概览', icon: 'bi bi-graph-up', type: 'overview' },
                { id: 'transactions', title: '交易流水', icon: 'bi bi-list-ul', type: 'transactions' },
                { id: 'settlements', title: '结算管理', icon: 'bi bi-calendar-check', type: 'settlements' },
                { id: 'reports', title: '财务报表', icon: 'bi bi-file-earmark-text', type: 'reports' }
            ],
            'provider': [
                { id: 'overview', title: '收益概览', icon: 'bi bi-graph-up', type: 'overview' },
                { id: 'orders', title: '订单收益', icon: 'bi bi-list-ul', type: 'orders' },
                { id: 'withdraw', title: '提现管理', icon: 'bi bi-cash-coin', type: 'withdraw' },
                { id: 'reports', title: '收益报表', icon: 'bi bi-file-earmark-text', type: 'reports' }
            ],
            'merchant': [
                { id: 'overview', title: '财务概览', icon: 'bi bi-graph-up', type: 'overview' },
                { id: 'transactions', title: '资金流水', icon: 'bi bi-list-ul', type: 'transactions' },
                { id: 'settlement', title: '结算管理', icon: 'bi bi-calendar-check', type: 'settlement' },
                { id: 'reports', title: '财务报表', icon: 'bi bi-file-earmark-text', type: 'reports' }
            ]
        };
        
        return configs[role] || configs['merchant'];
    }

    /**
     * 生成选项卡面板内容
     */
    generateTabPaneContent(tab) {
        switch (tab.type) {
            case 'overview':
                return this.generateOverviewContent();
            case 'transactions':
            case 'orders':
                return this.generateTransactionsContent();
            case 'settlements':
            case 'settlement':
            case 'withdraw':
                return this.generateSettlementsContent();
            case 'platforms':
                return this.generatePlatformsContent();
            case 'reports':
                return this.generateReportsContent();
            default:
                return '<div class="text-center py-4">内容加载中...</div>';
        }
    }

    /**
     * 生成概览内容
     */
    generateOverviewContent() {
        return `
            <div class="row g-4">
                <div class="col-md-8">
                    <div class="chart-container">
                        <h6 class="mb-3">收入趋势图</h6>
                        <canvas id="revenueChart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-summary">
                        <h6 class="mb-3">本月统计</h6>
                        <div id="monthlyStats">
                            <div class="stat-item">
                                <span class="label">交易笔数:</span>
                                <span class="value" id="monthlyTransactions">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">交易金额:</span>
                                <span class="value text-success" id="monthlyVolume">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">手续费:</span>
                                <span class="value text-primary" id="monthlyFees">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">净收入:</span>
                                <span class="value text-info" id="monthlyProfit">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成交易流水内容
     */
    generateTransactionsContent() {
        return `
            <div class="transactions-section">
                <!-- 筛选器 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="flowStartDate" placeholder="开始日期">
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="flowEndDate" placeholder="结束日期">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="flowTypeFilter">
                            <option value="all">全部类型</option>
                            <option value="income">收入</option>
                            <option value="expense">支出</option>
                            <option value="settlement">结算</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary" id="applyFiltersBtn">
                            <i class="bi bi-funnel me-2"></i>应用筛选
                        </button>
                    </div>
                </div>
                
                <!-- 数据表格 -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr id="transactionTableHeader">
                                <!-- 表头将根据角色动态生成 -->
                            </tr>
                        </thead>
                        <tbody id="transactionTableBody">
                            <tr><td colspan="8" class="text-center">加载中...</td></tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div id="transactionPagination" class="d-flex justify-content-center mt-3">
                    <!-- 分页将动态生成 -->
                </div>
            </div>
        `;
    }

    /**
     * 生成结算管理内容
     */
    generateSettlementsContent() {
        return `
            <div class="settlements-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6>结算记录</h6>
                    <button class="btn btn-primary btn-sm" id="createSettlementBtn">
                        <i class="bi bi-plus-circle me-2"></i>创建结算
                    </button>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr id="settlementTableHeader">
                                <!-- 表头将根据角色动态生成 -->
                            </tr>
                        </thead>
                        <tbody id="settlementTableBody">
                            <tr><td colspan="7" class="text-center">加载中...</td></tr>
                        </tbody>
                    </table>
                </div>
                
                <div id="settlementPagination" class="d-flex justify-content-center mt-3">
                    <!-- 分页将动态生成 -->
                </div>
            </div>
        `;
    }

    /**
     * 生成平台内容（系统管理员专用）
     */
    generatePlatformsContent() {
        return `
            <div class="platforms-section">
                <h6 class="mb-3">各平台财务状况</h6>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>平台名称</th>
                                <th>码商数量</th>
                                <th>商户数量</th>
                                <th>总收入</th>
                                <th>手续费收入</th>
                                <th>今日交易</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="platformFinanceTableBody">
                            <tr><td colspan="7" class="text-center">加载中...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * 生成报表内容
     */
    generateReportsContent() {
        return `
            <div class="reports-section">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">快速报表</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="financeModule.generateReport('daily')">
                                        <i class="bi bi-calendar-day me-2"></i>日报表
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="financeModule.generateReport('weekly')">
                                        <i class="bi bi-calendar-week me-2"></i>周报表
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="financeModule.generateReport('monthly')">
                                        <i class="bi bi-calendar-month me-2"></i>月报表
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">自定义报表</h6>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-primary" id="customReportBtn">
                                    <i class="bi bi-gear me-2"></i>自定义报表设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成样式
     */
    generateStyles() {
        return `
            .finance-module {
                padding: 0;
            }
            
            .stat-card {
                background: white;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                display: flex;
                align-items: center;
                gap: 15px;
                transition: transform 0.2s;
            }
            
            .stat-card:hover {
                transform: translateY(-2px);
            }
            
            .stat-icon {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 20px;
            }
            
            .stat-content {
                flex: 1;
            }
            
            .stat-number {
                font-size: 24px;
                font-weight: bold;
                color: #333;
            }
            
            .stat-label {
                color: #666;
                font-size: 14px;
            }
            
            .chart-container {
                background: white;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            
            .stats-summary {
                background: white;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            
            .stat-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
            }
            
            .stat-item:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }
            
            .transactions-section,
            .settlements-section,
            .platforms-section,
            .reports-section {
                background: white;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
        `;
    }

    /**
     * 初始化事件监听
     */
    initializeEvents() {
        // 报表生成按钮
        const generateReportBtn = document.getElementById('generateReportBtn');
        if (generateReportBtn) {
            generateReportBtn.addEventListener('click', () => this.showReportModal());
        }

        // 导出数据按钮
        const exportFinanceBtn = document.getElementById('exportFinanceBtn');
        if (exportFinanceBtn) {
            exportFinanceBtn.addEventListener('click', () => this.exportData());
        }

        // 筛选按钮
        const applyFiltersBtn = document.getElementById('applyFiltersBtn');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => this.applyFilters());
        }

        // 创建结算按钮
        const createSettlementBtn = document.getElementById('createSettlementBtn');
        if (createSettlementBtn) {
            createSettlementBtn.addEventListener('click', () => this.createSettlement());
        }

        // 自定义报表按钮
        const customReportBtn = document.getElementById('customReportBtn');
        if (customReportBtn) {
            customReportBtn.addEventListener('click', () => this.showCustomReportModal());
        }

        // 选项卡切换事件
        const tabLinks = document.querySelectorAll('#financeTabs .nav-link');
        tabLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const target = e.target.getAttribute('href');
                this.handleTabChange(target);
            });
        });
    }

    /**
     * 根据角色加载数据
     */
    async loadData() {
        try {
            // 模拟数据加载
            console.log(`📊 加载${this.currentRole}的财务数据...`);
            
            // 模拟统计数据
            const mockStats = this.getMockStatsData(this.currentRole);
            this.updateStatsCards(mockStats);
            
        } catch (error) {
            console.error('❌ 加载财务数据失败:', error);
        }
    }

    /**
     * 获取模拟统计数据
     */
    getMockStatsData(role) {
        const mockData = {
            'system_admin': {
                'totalPlatformsRevenue': '¥12,345,678',
                'totalSystemFee': '¥234,567',
                'todayTransactions': '1,234',
                'pendingSettlements': '56'
            },
            'platform_admin': {
                'platformTotalRevenue': '¥2,345,678',
                'platformFeeIncome': '¥34,567',
                'providerCount': '123',
                'merchantCount': '456'
            },
            'provider': {
                'deviceRevenue': '¥345,678',
                'availableBalance': '¥45,678',
                'todayOrders': '234',
                'pendingWithdraw': '12'
            },
            'merchant': {
                'totalRevenue': '¥123,456',
                'availableBalance': '¥23,456',
                'pendingSettlement': '¥3,456',
                'feeIncome': '¥1,234'
            }
        };
        
        return mockData[role] || mockData['merchant'];
    }

    /**
     * 更新统计卡片
     */
    updateStatsCards(data) {
        Object.keys(data).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = data[key];
            }
        });
    }

    /**
     * 处理选项卡切换
     */
    handleTabChange(target) {
        console.log(`🔄 切换到选项卡: ${target}`);
    }

    /**
     * 应用筛选
     */
    applyFilters() {
        console.log('🔍 应用筛选条件');
    }

    /**
     * 生成报表
     */
    async generateReport(type) {
        console.log(`📊 生成${type}报表`);
    }

    /**
     * 导出数据
     */
    exportData() {
        console.log('📤 导出财务数据');
    }

    /**
     * 创建结算
     */
    createSettlement() {
        console.log('💰 创建新结算');
    }

    /**
     * 显示报表模态框
     */
    showReportModal() {
        console.log('📋 显示报表模态框');
    }

    /**
     * 显示错误信息
     */
    showError(container, message) {
        container.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
}

// 全局注册
window.FinanceModule = FinanceModule;

// 创建全局实例
window.financeModule = new FinanceModule();

console.log('✅ FinanceModule 已注册到全局');
