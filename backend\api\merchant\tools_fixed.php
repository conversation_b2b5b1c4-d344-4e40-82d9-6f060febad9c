<?php
// 使用TenantAuth多租户认证系统
require_once dirname(__FILE__) . '/../../utils/TenantAuth.php';

// 初始化多租户认证
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();

// 验证是否为商户用户
if ($currentUser['user_type'] !== 'merchant') {
    $auth->sendForbidden('只有商户用户可以访问此接口');
}

// 获取商户信息
$currentMerchant = isset($currentUser['merchant_info']) ? $currentUser['merchant_info'] : null;
if (!$currentMerchant) {
    $auth->sendError('商户信息不存在', 403);
}

// 获取数据库连接
$db = $auth->getDatabase();

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'get_dashboard':
            handleGetDashboard($auth);
            break;
        case 'get_orders':
            handleGetOrders($auth);
            break;
        case 'get_statistics':
            handleGetStatistics($auth);
            break;
        case 'get_products':
            handleGetProducts($auth);
            break;
        case 'get_key_info':
            handleGetKeyInfo($auth);
            break;
        case 'get_call_stats':
            handleGetCallStats($auth);
            break;
        case 'get_config':
            handleGetConfig($auth);
            break;
        case 'update_profile':
            handleUpdateProfile($auth);
            break;
        case 'update_callback_url':
            handleUpdateCallbackUrl($auth);
            break;
        case 'update_ip_whitelist':
            handleUpdateIpWhitelist($auth);
            break;
        case 'regenerate_api_key':
            handleRegenerateApiKey($auth);
            break;
        default:
            $auth->sendError('接口不存在', 404);
    }
} catch (Exception $e) {
    error_log("Merchant tools error: " . $e->getMessage());
    $auth->sendError('系统错误: ' . $e->getMessage(), 500);
}

// 获取API密钥信息
function handleGetKeyInfo($auth) {
    global $currentMerchant, $db;
    
    // 记录操作日志
    $auth->logUserAction('view_api_key', 'merchant', $currentMerchant['id'], '查看API密钥信息');
    
    // 从数据库获取完整的商户信息，包括API密钥
    try {
        $stmt = $db->query("
            SELECT id, api_key, callback_url, ip_whitelist, status, created_at, 
                   company_name, contact_person, contact_phone, contact_email, balance, service_rate
            FROM merchants 
            WHERE id = ?
        ", array($currentMerchant['id']));
        
        $merchantData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchantData) {
            $auth->sendError('商户信息不存在', 404);
            return;
        }
        
        $keyInfo = array(
            'developer_id' => $merchantData['id'],
            'api_key' => isset($merchantData['api_key']) ? $merchantData['api_key'] : '',
            'callback_url' => isset($merchantData['callback_url']) ? $merchantData['callback_url'] : '',
            'ip_whitelist' => isset($merchantData['ip_whitelist']) ? $merchantData['ip_whitelist'] : '',
            'status' => isset($merchantData['status']) ? $merchantData['status'] : 'inactive',
            'created_at' => isset($merchantData['created_at']) ? $merchantData['created_at'] : '',
            'company_name' => isset($merchantData['company_name']) ? $merchantData['company_name'] : '',
            'contact_person' => isset($merchantData['contact_person']) ? $merchantData['contact_person'] : '',
            'contact_phone' => isset($merchantData['contact_phone']) ? $merchantData['contact_phone'] : '',
            'contact_email' => isset($merchantData['contact_email']) ? $merchantData['contact_email'] : '',
            'balance' => isset($merchantData['balance']) ? $merchantData['balance'] : '0.00',
            'service_rate' => isset($merchantData['service_rate']) ? $merchantData['service_rate'] : '0.0050');
        
        $auth->sendSuccess($keyInfo, 'API密钥信息获取成功');
        
    } catch (Exception $e) {
        error_log("获取API密钥信息失败: " . $e->getMessage());
        $auth->sendError('获取API密钥信息失败: ' . $e->getMessage(), 500);
    }
}

// 重新生成API密钥
function handleRegenerateApiKey($auth) {
    global $currentMerchant, $db;
    
    try {
        $newApiKey = generateApiKey();
        
        // 只更新api_key字段，不更新不存在的secret_key
        $db->query("UPDATE merchants SET api_key = ?, updated_at = NOW() WHERE id = ?", 
                  array($newApiKey, $currentMerchant['id']));
        
        // 记录操作日志
        $auth->logUserAction('regenerate_api_key', 'merchant', $currentMerchant['id'], '重新生成API密钥');
        
        $auth->sendSuccess(array(
            'api_key' => $newApiKey
        ), 'API密钥重新生成成功');
        
    } catch (Exception $e) {
        $auth->sendError('API密钥重新生成失败: ' . $e->getMessage(), 500);
    }
}

// 生成API密钥
function generateApiKey() {
    return hash('sha256', uniqid(mt_rand(), true));
}

// 其他函数保持不变...
?> 