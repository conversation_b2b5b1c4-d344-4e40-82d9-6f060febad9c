/**
 * API管理模块
 * 负责API接口管理、文档生成、测试工具、权限控制等核心API业务功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-15
 */

class APIManager {
    constructor() {

        this.authManager = window.authManager;
        this.utils = window.utils;
        this.currentAPIs = [];
        this.currentDevelopers = [];
        this.currentLogs = [];
        
        console.log('APIManager initialized');
    }

    /**
     * 初始化API管理模块
     */
    async init() {
        try {
            // 注册页面处理器
            if (window.uiManager) {
                window.uiManager.registerPageHandler('apis', (container) => {
                    this.loadAPIManagementPage(container);
                });
            }
            
            console.log('✅ API管理模块初始化完成');
        } catch (error) {
            console.error('❌ API管理模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载API管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadAPIManagementPage(container) {
        container.innerHTML = this.generateAPIManagementHTML();
        this.initializeAPIManagementEvents();
        this.loadAPIData();
    }

    /**
     * 生成API管理页面HTML
     * @returns {string} HTML字符串
     */
    generateAPIManagementHTML() {
        return `
            <div class="api-management">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-code-slash me-2"></i>API管理</h2>
                            <p class="text-muted mb-0">管理API接口、开发者权限和调用统计</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="addAPIBtn">
                                <i class="bi bi-plus-circle me-2"></i>添加API
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="apiDocsBtn">
                                <i class="bi bi-book me-2"></i>API文档
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-code-slash"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalAPIsCount">-</div>
                                <div class="stat-label">API接口</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalDevelopersCount">-</div>
                                <div class="stat-label">开发者</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="todayCallsCount">-</div>
                                <div class="stat-label">今日调用</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="successRateCount">-</div>
                                <div class="stat-label">成功率</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="apiTabs">
                            <li class="nav-item">
                                <a class="nav-link active" id="interfaces-tab" data-bs-toggle="tab" href="#interfaces">
                                    <i class="bi bi-code-slash me-2"></i>API接口
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="developers-tab" data-bs-toggle="tab" href="#developers">
                                    <i class="bi bi-people me-2"></i>开发者管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="logs-tab" data-bs-toggle="tab" href="#logs">
                                    <i class="bi bi-list-ul me-2"></i>调用日志
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="testing-tab" data-bs-toggle="tab" href="#testing">
                                    <i class="bi bi-play-circle me-2"></i>API测试
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="apiTabContent">
                            <!-- API接口 -->
                            <div class="tab-pane fade show active" id="interfaces">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>API接口列表</h6>
                                    <div>
                                        <button class="btn btn-outline-primary btn-sm" id="refreshAPIsBtn">
                                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm ms-2" id="exportAPIsBtn">
                                            <i class="bi bi-download me-1"></i>导出
                                        </button>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>接口名称</th>
                                                <th>路径</th>
                                                <th>方法</th>
                                                <th>版本</th>
                                                <th>状态</th>
                                                <th>今日调用</th>
                                                <th>成功率</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="apisTableBody">
                                            <tr>
                                                <td colspan="8" class="text-center py-4">
                                                    <div class="spinner-border text-primary" role="status"></div>
                                                    <div class="mt-2">正在加载API接口...</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 开发者管理 -->
                            <div class="tab-pane fade" id="developers">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>开发者列表</h6>
                                    <button class="btn btn-primary btn-sm" id="addDeveloperBtn">
                                        <i class="bi bi-plus-circle me-1"></i>添加开发者
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>开发者ID</th>
                                                <th>名称</th>
                                                <th>邮箱</th>
                                                <th>状态</th>
                                                <th>权限等级</th>
                                                <th>今日调用</th>
                                                <th>注册时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="developersTableBody">
                                            <tr>
                                                <td colspan="8" class="text-center py-4">
                                                    <div class="spinner-border text-primary" role="status"></div>
                                                    <div class="mt-2">正在加载开发者...</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 调用日志 -->
                            <div class="tab-pane fade" id="logs">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>API调用日志</h6>
                                    <div>
                                        <select class="form-select form-select-sm" id="logFilterAPI" style="width: 200px; display: inline-block;">
                                            <option value="">全部接口</option>
                                        </select>
                                        <button class="btn btn-outline-secondary btn-sm ms-2" id="refreshLogsBtn">
                                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                        </button>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover table-sm">
                                        <thead class="table-light">
                                            <tr>
                                                <th>时间</th>
                                                <th>接口</th>
                                                <th>开发者</th>
                                                <th>IP地址</th>
                                                <th>状态码</th>
                                                <th>响应时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="logsTableBody">
                                            <tr>
                                                <td colspan="7" class="text-center py-4">
                                                    <div class="spinner-border text-primary" role="status"></div>
                                                    <div class="mt-2">正在加载调用日志...</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- API测试 -->
                            <div class="tab-pane fade" id="testing">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">选择API接口</h6>
                                            </div>
                                            <div class="card-body">
                                                <select class="form-select mb-3" id="testAPISelect">
                                                    <option value="">请选择API接口</option>
                                                </select>
                                                <div class="mb-3">
                                                    <label class="form-label">请求方法</label>
                                                    <select class="form-select" id="testMethod">
                                                        <option value="GET">GET</option>
                                                        <option value="POST">POST</option>
                                                        <option value="PUT">PUT</option>
                                                        <option value="DELETE">DELETE</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">请求URL</label>
                                                    <input type="text" class="form-control" id="testURL" placeholder="/api/...">
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">开发者密钥</label>
                                                    <input type="text" class="form-control" id="testDeveloperKey" placeholder="developer_key">
                                                </div>
                                                <button class="btn btn-primary w-100" id="testAPIBtn">
                                                    <i class="bi bi-play-circle me-2"></i>发送测试
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">请求参数</h6>
                                            </div>
                                            <div class="card-body">
                                                <textarea class="form-control mb-3" id="testParams" rows="8" 
                                                          placeholder='{"key": "value"}'></textarea>
                                            </div>
                                        </div>
                                        <div class="card mt-3">
                                            <div class="card-header">
                                                <h6 class="mb-0">响应结果</h6>
                                            </div>
                                            <div class="card-body">
                                                <pre id="testResponse" class="bg-light p-3 rounded" style="min-height: 200px; max-height: 400px; overflow-y: auto;">
等待测试结果...
                                                </pre>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 添加/编辑API模态框 -->
            <div class="modal fade" id="apiModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="apiModalTitle">添加API接口</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="apiForm">
                                <input type="hidden" id="apiId">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">接口名称 *</label>
                                        <input type="text" class="form-control" id="apiName" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">接口路径 *</label>
                                        <input type="text" class="form-control" id="apiPath" required placeholder="/api/...">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">请求方法 *</label>
                                        <select class="form-select" id="apiMethod" required>
                                            <option value="">请选择</option>
                                            <option value="GET">GET</option>
                                            <option value="POST">POST</option>
                                            <option value="PUT">PUT</option>
                                            <option value="DELETE">DELETE</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">版本</label>
                                        <input type="text" class="form-control" id="apiVersion" value="v1">
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">状态</label>
                                        <select class="form-select" id="apiStatus">
                                            <option value="active">启用</option>
                                            <option value="inactive">禁用</option>
                                            <option value="deprecated">已废弃</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">接口描述</label>
                                        <textarea class="form-control" id="apiDescription" rows="3"></textarea>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">请求参数 (JSON格式)</label>
                                        <textarea class="form-control" id="apiParams" rows="4" 
                                                  placeholder='{"param1": "string", "param2": "number"}'></textarea>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">响应示例 (JSON格式)</label>
                                        <textarea class="form-control" id="apiResponse" rows="4" 
                                                  placeholder='{"error_code": 0, "error_message": "", "data": {}}'></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveAPIBtn">保存</button>
                        </div>
                    </div>
                </div>
            </div>

            ${this.generateAPIManagementStyles()}
        `;
    }

    /**
     * 生成API管理样式
     * @returns {string} CSS样式
     */
    generateAPIManagementStyles() {
        return `
            <style>
                .api-management .stat-card {
                    background: white;
                    border-radius: 12px;
                    padding: 20px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }

                .api-management .stat-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                }

                .api-management .stat-icon {
                    width: 50px;
                    height: 50px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .api-management .stat-content {
                    flex: 1;
                }

                .api-management .stat-number {
                    font-size: 1.8rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin-bottom: 4px;
                }

                .api-management .stat-label {
                    color: #6b7280;
                    font-size: 14px;
                }

                .api-management .status-badge {
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .api-management .status-active {
                    background: #dcfce7;
                    color: #166534;
                }

                .api-management .status-inactive {
                    background: #fef3c7;
                    color: #92400e;
                }

                .api-management .status-deprecated {
                    background: #fee2e2;
                    color: #991b1b;
                }

                .api-management .method-badge {
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 11px;
                    font-weight: 600;
                    font-family: 'Courier New', monospace;
                }

                .api-management .method-GET {
                    background: #dcfce7;
                    color: #166534;
                }

                .api-management .method-POST {
                    background: #dbeafe;
                    color: #1e40af;
                }

                .api-management .method-PUT {
                    background: #fef3c7;
                    color: #92400e;
                }

                .api-management .method-DELETE {
                    background: #fee2e2;
                    color: #991b1b;
                }

                .api-management .table th {
                    font-weight: 600;
                    color: #374151;
                    border-bottom: 2px solid #e5e7eb;
                }

                .api-management .table td {
                    vertical-align: middle;
                    border-bottom: 1px solid #f3f4f6;
                }

                .api-management .nav-tabs .nav-link {
                    color: #6b7280;
                    border: none;
                    padding: 12px 20px;
                }

                .api-management .nav-tabs .nav-link.active {
                    color: #3b82f6;
                    background: white;
                    border-bottom: 2px solid #3b82f6;
                }

                .api-management .form-label {
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 6px;
                }

                .api-management .form-control:focus,
                .api-management .form-select:focus {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }

                .api-management #testResponse {
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    color: #374151;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                }
            </style>
        `;
    }

    /**
     * 初始化API管理事件
     */
    initializeAPIManagementEvents() {
        // 添加API按钮
        document.getElementById('addAPIBtn')?.addEventListener('click', () => {
            this.showAPIModal();
        });

        // API文档按钮
        document.getElementById('apiDocsBtn')?.addEventListener('click', () => {
            this.showAPIDocs();
        });

        // 刷新按钮
        document.getElementById('refreshAPIsBtn')?.addEventListener('click', () => {
            this.loadAPIList();
        });

        // 导出按钮
        document.getElementById('exportAPIsBtn')?.addEventListener('click', () => {
            this.exportAPIList();
        });

        // 保存API按钮
        document.getElementById('saveAPIBtn')?.addEventListener('click', () => {
            this.handleSaveAPI();
        });

        // API测试按钮
        document.getElementById('testAPIBtn')?.addEventListener('click', () => {
            this.handleAPITest();
        });

        // 选项卡切换事件
        document.querySelectorAll('#apiTabs .nav-link').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const target = e.target.getAttribute('href').substring(1);
                this.handleTabChange(target);
            });
        });
    }

    /**
     * 加载API数据
     */
    async loadAPIData() {
        try {
            await this.updateAPIStats();
            await this.loadAPIList();
        } catch (error) {
            console.error('加载API数据失败:', error);
            this.utils.showMessage('加载API数据失败', 'error');
        }
    }

    /**
     * 更新API统计数据
     */
    async updateAPIStats() {
        try {
            // 模拟统计数据
            document.getElementById('totalAPIsCount').textContent = '24';
            document.getElementById('totalDevelopersCount').textContent = '12';
            document.getElementById('todayCallsCount').textContent = '8,456';
            document.getElementById('successRateCount').textContent = '99.2%';
        } catch (error) {
            console.error('加载API统计失败:', error);
        }
    }

    /**
     * 加载API列表
     */
    async loadAPIList() {
        try {
            // 模拟API数据
            this.currentAPIs = [
                {
                    id: 1,
                    name: '获取收款二维码',
                    path: '/api/pay/qrcode',
                    method: 'POST',
                    version: 'v1',
                    status: 'active',
                    today_calls: 1234,
                    success_rate: 99.5
                },
                {
                    id: 2,
                    name: '查询订单状态',
                    path: '/api/pay/order/query',
                    method: 'POST',
                    version: 'v1',
                    status: 'active',
                    today_calls: 856,
                    success_rate: 98.8
                },
                {
                    id: 3,
                    name: '查询产品额度',
                    path: '/api/pay/quota',
                    method: 'POST',
                    version: 'v1',
                    status: 'active',
                    today_calls: 432,
                    success_rate: 99.1
                }
            ];
            
            this.renderAPIList();
        } catch (error) {
            console.error('加载API列表失败:', error);
            this.showAPIListError('加载API列表失败');
        }
    }

    /**
     * 渲染API列表
     */
    renderAPIList() {
        const tbody = document.getElementById('apisTableBody');
        if (!tbody) return;

        if (this.currentAPIs.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="bi bi-code-slash" style="font-size: 3rem; color: #6b7280;"></i>
                        <h5 class="mt-3 text-muted">暂无API接口</h5>
                        <p class="text-muted">点击上方"添加API"按钮开始添加接口</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.currentAPIs.map(api => `
            <tr>
                <td>
                    <div class="fw-semibold">${api.name}</div>
                    <small class="text-muted">${api.path}</small>
                </td>
                <td>
                    <code class="text-muted">${api.path}</code>
                </td>
                <td>
                    <span class="method-badge method-${api.method}">${api.method}</span>
                </td>
                <td>
                    <span class="badge bg-secondary">${api.version}</span>
                </td>
                <td>
                    <span class="status-badge status-${api.status}">
                        ${this.getStatusText(api.status)}
                    </span>
                </td>
                <td class="fw-semibold">${this.utils.formatNumber(api.today_calls)}</td>
                <td>
                    <span class="text-success fw-semibold">${api.success_rate}%</span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" 
                                onclick="apiManager.showAPIModal(${api.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" 
                                onclick="apiManager.showAPIDetails(${api.id})" title="详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" 
                                onclick="apiManager.testAPI(${api.id})" title="测试">
                            <i class="bi bi-play-circle"></i>
                        </button>
                        <button class="btn btn-outline-danger" 
                                onclick="apiManager.deleteAPI(${api.id})" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 显示API列表错误
     * @param {string} message 错误信息
     */
    showAPIListError(message) {
        const tbody = document.getElementById('apisTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-danger">${message}</h5>
                    <button class="btn btn-outline-primary mt-2" onclick="apiManager.loadAPIList()">
                        重新加载
                    </button>
                </td>
            </tr>
        `;
    }

    /**
     * 显示API模态框
     * @param {number} apiId API ID（编辑时）
     */
    showAPIModal(apiId = null) {
        const modal = new bootstrap.Modal(document.getElementById('apiModal'));
        const title = document.getElementById('apiModalTitle');
        const form = document.getElementById('apiForm');

        // 重置表单
        form.reset();

        if (apiId) {
            // 编辑模式
            title.textContent = '编辑API接口';
            const api = this.currentAPIs.find(a => a.id === apiId);
            if (api) {
                document.getElementById('apiId').value = api.id;
                document.getElementById('apiName').value = api.name;
                document.getElementById('apiPath').value = api.path;
                document.getElementById('apiMethod').value = api.method;
                document.getElementById('apiVersion').value = api.version;
                document.getElementById('apiStatus').value = api.status;
            }
        } else {
            // 添加模式
            title.textContent = '添加API接口';
        }

        modal.show();
    }

    /**
     * 处理保存API
     */
    handleSaveAPI() {
        const form = document.getElementById('apiForm');
        const apiId = document.getElementById('apiId').value;

        // 表单验证
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const apiData = {
            name: document.getElementById('apiName').value,
            path: document.getElementById('apiPath').value,
            method: document.getElementById('apiMethod').value,
            version: document.getElementById('apiVersion').value,
            status: document.getElementById('apiStatus').value,
            description: document.getElementById('apiDescription').value,
            params: document.getElementById('apiParams').value,
            response: document.getElementById('apiResponse').value
        };

        // 模拟保存
        this.utils.showMessage(apiId ? 'API更新成功' : 'API添加成功', 'success');
        bootstrap.Modal.getInstance(document.getElementById('apiModal')).hide();
        this.loadAPIList();
    }

    /**
     * 处理选项卡切换
     * @param {string} target 目标选项卡
     */
    handleTabChange(target) {
        switch (target) {
            case 'interfaces':
                this.loadAPIList();
                break;
            case 'developers':
                this.loadDeveloperList();
                break;
            case 'logs':
                this.loadAPILogs();
                break;
            case 'testing':
                this.loadAPITesting();
                break;
        }
    }

    /**
     * 加载开发者列表
     */
    loadDeveloperList() {
        const tbody = document.getElementById('developersTableBody');
        if (!tbody) return;

        // 模拟开发者数据
        const developers = [
            {
                id: 'dev_001',
                name: '开发者A',
                email: '<EMAIL>',
                status: 'active',
                level: 'premium',
                today_calls: 1234,
                created_at: '2024-01-01'
            },
            {
                id: 'dev_002',
                name: '开发者B',
                email: '<EMAIL>',
                status: 'active',
                level: 'basic',
                today_calls: 456,
                created_at: '2024-01-15'
            }
        ];

        tbody.innerHTML = developers.map(dev => `
            <tr>
                <td><code>${dev.id}</code></td>
                <td>${dev.name}</td>
                <td>${dev.email}</td>
                <td>
                    <span class="status-badge status-${dev.status}">
                        ${dev.status === 'active' ? '活跃' : '禁用'}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${dev.level === 'premium' ? 'warning' : 'secondary'}">
                        ${dev.level === 'premium' ? '高级' : '基础'}
                    </span>
                </td>
                <td class="fw-semibold">${this.utils.formatNumber(dev.today_calls)}</td>
                <td>${dev.created_at}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" title="密钥">
                            <i class="bi bi-key"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 加载API日志
     */
    loadAPILogs() {
        const tbody = document.getElementById('logsTableBody');
        if (!tbody) return;

        // 模拟日志数据
        const logs = [
            {
                time: '2024-01-15 14:30:25',
                api: '/api/pay/qrcode',
                developer: 'dev_001',
                ip: '*************',
                status: 200,
                response_time: 125
            },
            {
                time: '2024-01-15 14:29:18',
                api: '/api/pay/order/query',
                developer: 'dev_002',
                ip: '*************',
                status: 200,
                response_time: 89
            },
            {
                time: '2024-01-15 14:28:45',
                api: '/api/pay/quota',
                developer: 'dev_001',
                ip: '*************',
                status: 400,
                response_time: 45
            }
        ];

        tbody.innerHTML = logs.map(log => `
            <tr>
                <td><small>${log.time}</small></td>
                <td><code class="text-muted">${log.api}</code></td>
                <td><code>${log.developer}</code></td>
                <td>${log.ip}</td>
                <td>
                    <span class="badge bg-${log.status === 200 ? 'success' : 'danger'}">
                        ${log.status}
                    </span>
                </td>
                <td>${log.response_time}ms</td>
                <td>
                    <button class="btn btn-outline-info btn-sm" title="详情">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 加载API测试
     */
    loadAPITesting() {
        const select = document.getElementById('testAPISelect');
        if (!select) return;

        // 填充API选项
        select.innerHTML = '<option value="">请选择API接口</option>' +
            this.currentAPIs.map(api => 
                `<option value="${api.id}">${api.name} (${api.method} ${api.path})</option>`
            ).join('');
    }

    /**
     * 处理API测试
     */
    handleAPITest() {
        const apiId = document.getElementById('testAPISelect').value;
        const method = document.getElementById('testMethod').value;
        const url = document.getElementById('testURL').value;
        const developerKey = document.getElementById('testDeveloperKey').value;
        const params = document.getElementById('testParams').value;

        if (!apiId || !url || !developerKey) {
            this.utils.showMessage('请填写完整的测试信息', 'warning');
            return;
        }

        // 模拟测试结果
        const response = {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'X-Response-Time': '125ms'
            },
            data: {
                error_code: 0,
                error_message: '',
                data: {
                    test: 'success',
                    timestamp: new Date().toISOString()
                }
            }
        };

        document.getElementById('testResponse').textContent = JSON.stringify(response, null, 2);
        this.utils.showMessage('API测试完成', 'success');
    }

    /**
     * 显示API详情
     * @param {number} apiId API ID
     */
    showAPIDetails(apiId) {
        this.utils.showMessage('API详情功能开发中', 'info');
    }

    /**
     * 测试API
     * @param {number} apiId API ID
     */
    testAPI(apiId) {
        // 切换到测试选项卡
        const testingTab = document.getElementById('testing-tab');
        if (testingTab) {
            testingTab.click();
            
            setTimeout(() => {
                const select = document.getElementById('testAPISelect');
                if (select) {
                    select.value = apiId;
                }
            }, 100);
        }
    }

    /**
     * 删除API
     * @param {number} apiId API ID
     */
    deleteAPI(apiId) {
        if (!confirm('确定要删除这个API接口吗？此操作不可恢复！')) {
            return;
        }

        this.utils.showMessage('API删除成功', 'success');
        this.loadAPIList();
    }

    /**
     * 导出API列表
     */
    exportAPIList() {
        this.utils.showMessage('导出功能开发中', 'info');
    }

    /**
     * 显示API文档
     */
    showAPIDocs() {
        this.utils.showMessage('API文档功能开发中', 'info');
    }

    /**
     * 获取状态文本
     * @param {string} status 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'active': '启用',
            'inactive': '禁用',
            'deprecated': '已废弃'
        };
        return statusMap[status] || status;
    }
}

// 创建全局实例
const apiManager = new APIManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APIManager };
} else {
    // 浏览器环境
    window.APIManager = APIManager;
    window.apiManager = apiManager;
} 