/**
 * 动态资源加载器
 * 实现按需并行加载，显示加载进度
 * 
 * <AUTHOR>
 * @version 2.0.0 - 架构优化版本
 */

class ResourceLoader {
    constructor() {
        this.loadedScripts = new Set();
        this.loadingPromises = new Map();
        this.loadQueue = [];
        this.totalResources = 0;
        this.loadedResources = 0;
        this.startTime = Date.now();
    }

    /**
     * 根据租户配置加载所需资源
     * @param {Object} tenantConfig 租户配置信息
     * @returns {Promise<void>}
     */
    async loadTenantResources(tenantConfig) {
        try {
            console.log('📦 开始加载租户资源:', tenantConfig.tenantType);
            
            // 定义加载资源列表
            const resources = [
                // 配置文件（最高优先级）
                { name: 'config', path: 'js/config.js', priority: 0 },
                
                // 核心模块（必需 - 精简版）
                { name: 'utils', path: 'js/modules/core/utils-slim.js', priority: 1 },
                { name: 'auth', path: 'js/modules/core/auth-slim.js', priority: 1 },
                
                // 租户专用资源（按需）
                { name: 'routes', path: `js/routes/${tenantConfig.routes}`, priority: 2 },
                { name: 'ui', path: `js/ui/${tenantConfig.ui}`, priority: 2 },
                
                // 应用初始化（最后 - 精简版）
                { name: 'app-init', path: 'js/core/app-initializer-slim.js', priority: 3 }
            ];

            this.totalResources = resources.length;
            this._showLoadingProgress();

            // 按优先级分组并行加载
            const priorityGroups = this._groupByPriority(resources);
            
            for (const [priority, group] of priorityGroups) {
                console.log(`📥 加载优先级${priority}资源:`, group.map(r => r.name));
                
                // 同优先级资源并行加载
                await Promise.all(group.map(resource => this._loadScript(resource)));
                
                console.log(`✅ 优先级${priority}资源加载完成`);
            }

            this._hideLoadingProgress();
            
            const totalTime = Date.now() - this.startTime;
            console.log(`🎉 所有资源加载完成 (${totalTime}ms)`);
            
            // 设置全局租户配置
            window.TENANT_CONFIG = tenantConfig;
            
            return true;
            
        } catch (error) {
            console.error('❌ 资源加载失败:', error);
            this._showLoadingError(error.message);
            throw error;
        }
    }

    /**
     * 加载单个脚本文件
     * @private
     */
    async _loadScript(resource) {
        const { name, path } = resource;
        
        // 避免重复加载
        if (this.loadedScripts.has(path)) {
            console.log(`⏭️ 跳过已加载的脚本: ${name}`);
            this._updateProgress();
            return;
        }

        // 如果正在加载中，返回现有Promise
        if (this.loadingPromises.has(path)) {
            return this.loadingPromises.get(path);
        }

        const loadPromise = new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = path + '?v=' + Date.now(); // 添加时间戳避免缓存
            script.async = true;
            
            script.onload = () => {
                this.loadedScripts.add(path);
                this.loadingPromises.delete(path);
                this._updateProgress();
                console.log(`✅ 脚本加载成功: ${name} (${path})`);
                resolve();
            };
            
            script.onerror = () => {
                this.loadingPromises.delete(path);
                const error = new Error(`脚本加载失败: ${name} (${path})`);
                console.error('❌', error.message);
                reject(error);
            };
            
            document.head.appendChild(script);
        });

        this.loadingPromises.set(path, loadPromise);
        return loadPromise;
    }

    /**
     * 按优先级分组资源
     * @private
     */
    _groupByPriority(resources) {
        const groups = new Map();
        
        resources.forEach(resource => {
            const priority = resource.priority || 999;
            if (!groups.has(priority)) {
                groups.set(priority, []);
            }
            groups.get(priority).push(resource);
        });
        
        // 按优先级排序
        return new Map([...groups.entries()].sort((a, b) => a[0] - b[0]));
    }

    /**
     * 显示加载进度
     * @private
     */
    _showLoadingProgress() {
        const progressHtml = `
            <div id="resourceLoadingProgress" style="
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 8px 24px rgba(0,0,0,0.2);
                z-index: 10001;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                text-align: center;
                min-width: 300px;
            ">
                <div style="color: #1890ff; font-size: 20px; font-weight: 600; margin-bottom: 20px;">
                    🚀 正在加载系统资源
                </div>
                <div style="
                    background: #f0f0f0;
                    height: 8px;
                    border-radius: 4px;
                    overflow: hidden;
                    margin-bottom: 15px;
                ">
                    <div id="progressBar" style="
                        background: linear-gradient(90deg, #1890ff, #40a9ff);
                        height: 100%;
                        width: 0%;
                        transition: width 0.3s ease;
                        border-radius: 4px;
                    "></div>
                </div>
                <div id="progressText" style="
                    color: #666;
                    font-size: 14px;
                ">正在初始化... (0/${this.totalResources})</div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', progressHtml);
    }

    /**
     * 更新加载进度
     * @private
     */
    _updateProgress() {
        this.loadedResources++;
        
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        
        if (progressBar && progressText) {
            const percentage = Math.round((this.loadedResources / this.totalResources) * 100);
            
            progressBar.style.width = percentage + '%';
            progressText.textContent = `加载中... (${this.loadedResources}/${this.totalResources}) - ${percentage}%`;
            
            // 添加完成动画
            if (this.loadedResources === this.totalResources) {
                setTimeout(() => {
                    progressText.innerHTML = `
                        <div style="color: #52c41a; font-weight: 600;">
                            ✅ 加载完成！正在启动系统...
                        </div>
                    `;
                }, 200);
            }
        }
    }

    /**
     * 隐藏加载进度
     * @private
     */
    _hideLoadingProgress() {
        setTimeout(() => {
            const progressEl = document.getElementById('resourceLoadingProgress');
            if (progressEl) {
                progressEl.style.opacity = '0';
                progressEl.style.transform = 'translate(-50%, -50%) scale(0.95)';
                progressEl.style.transition = 'all 0.3s ease';
                
                setTimeout(() => {
                    progressEl.remove();
                }, 300);
            }
        }, 1000);
    }

    /**
     * 显示加载错误
     * @private
     */
    _showLoadingError(message) {
        const progressEl = document.getElementById('resourceLoadingProgress');
        if (progressEl) {
            progressEl.innerHTML = `
                <div style="text-align: center;">
                    <div style="color: #ff4d4f; font-size: 48px; margin-bottom: 20px;">⚠️</div>
                    <h3 style="color: #1f2937; margin-bottom: 15px;">资源加载失败</h3>
                    <p style="color: #6b7280; margin-bottom: 20px; line-height: 1.6;">
                        ${message}
                    </p>
                    <button onclick="location.reload()" style="
                        background: #1890ff;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 16px;
                        font-weight: 600;
                        cursor: pointer;
                    ">重新加载</button>
                </div>
            `;
        }
    }

    /**
     * 预加载指定资源
     * @param {Array} resources 资源列表
     */
    async preloadResources(resources) {
        const preloadPromises = resources.map(resource => {
            return new Promise((resolve) => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'script';
                link.href = resource;
                link.onload = resolve;
                link.onerror = resolve; // 即使失败也继续
                document.head.appendChild(link);
            });
        });
        
        await Promise.all(preloadPromises);
        console.log('🔄 资源预加载完成');
    }

    /**
     * 获取加载统计信息
     */
    getLoadStats() {
        return {
            totalResources: this.totalResources,
            loadedResources: this.loadedResources,
            loadTime: Date.now() - this.startTime,
            loadedScripts: Array.from(this.loadedScripts)
        };
    }
}

// 导出到全局作用域
window.ResourceLoader = ResourceLoader;

console.log('✅ ResourceLoader 动态资源加载器已加载'); 