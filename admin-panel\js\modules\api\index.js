/**
 * API模块加载器
 * 统一管理所有API相关的子模块
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class ApiModuleLoader {
    constructor() {
        this.modules = {};
        this.loadedModules = new Set();
        this.loadPromises = {};
        
        console.log('✅ ApiModuleLoader initialized');
    }

    /**
     * 加载所有API模块
     */
    async loadAllModules() {
        const moduleFiles = [
            'ApiKeyManager.js',
            'IpWhitelistManager.js', 
            'ApiTester.js',
            'ApiStatsManager.js',
            'ApiCodeGenerator.js',
            'ApiSignatureTool.js'
        ];

        try {
            console.log('🔄 开始加载API模块...');
            
            // 并行加载所有模块
            const loadPromises = moduleFiles.map(file => this.loadModule(file));
            await Promise.all(loadPromises);
            
            console.log('✅ 所有API模块加载完成');
            return true;
            
        } catch (error) {
            console.error('❌ 加载API模块失败:', error);
            throw error;
        }
    }

    /**
     * 加载单个模块
     */
    async loadModule(filename) {
        if (this.loadedModules.has(filename)) {
            return this.loadPromises[filename];
        }

        console.log(`🔄 加载模块: ${filename}`);
        
        this.loadPromises[filename] = new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = `/admin-panel/js/modules/api/${filename}`;
            script.onload = () => {
                this.loadedModules.add(filename);
                console.log(`✅ 模块加载成功: ${filename}`);
                resolve();
            };
            script.onerror = () => {
                console.error(`❌ 模块加载失败: ${filename}`);
                reject(new Error(`Failed to load module: ${filename}`));
            };
            document.head.appendChild(script);
        });

        return this.loadPromises[filename];
    }

    /**
     * 初始化所有模块实例
     */
    initializeModules(apiClient, authManager, utils) {
        try {
            console.log('🔄 初始化API模块实例...');
            
            // 初始化各个模块（不再传递apiClient，因为都使用fetch）
            this.modules.apiKeyManager = new window.ApiKeyManager();
            this.modules.ipWhitelistManager = new window.IpWhitelistManager();
            this.modules.apiTester = new window.ApiTester();
            this.modules.apiStatsManager = new window.ApiStatsManager();
            this.modules.apiCodeGenerator = new window.ApiCodeGenerator();
            this.modules.apiSignatureTool = new window.ApiSignatureTool();
            
            console.log('✅ API模块实例初始化完成');
            return this.modules;
            
        } catch (error) {
            console.error('❌ 初始化API模块实例失败:', error);
            throw error;
        }
    }

    /**
     * 获取模块实例
     */
    getModule(moduleName) {
        return this.modules[moduleName];
    }

    /**
     * 获取所有模块
     */
    getAllModules() {
        return this.modules;
    }

    /**
     * 检查模块是否已加载
     */
    isModuleLoaded(filename) {
        return this.loadedModules.has(filename);
    }

    /**
     * 检查所有模块是否已加载
     */
    areAllModulesLoaded() {
        const requiredModules = [
            'ApiKeyManager.js',
            'IpWhitelistManager.js', 
            'ApiTester.js',
            'ApiStatsManager.js',
            'ApiCodeGenerator.js',
            'ApiSignatureTool.js'
        ];
        
        return requiredModules.every(module => this.loadedModules.has(module));
    }

    /**
     * 销毁所有模块
     */
    destroyModules() {
        try {
            console.log('🔄 销毁API模块...');
            
            // 销毁各个模块
            Object.values(this.modules).forEach(module => {
                if (module && typeof module.destroy === 'function') {
                    module.destroy();
                }
            });
            
            this.modules = {};
            this.loadedModules.clear();
            this.loadPromises = {};
            
            console.log('✅ API模块销毁完成');
            
        } catch (error) {
            console.error('❌ 销毁API模块失败:', error);
        }
    }
}

// 创建全局实例
window.apiModuleLoader = new ApiModuleLoader();

// 导出类
window.ApiModuleLoader = ApiModuleLoader; 