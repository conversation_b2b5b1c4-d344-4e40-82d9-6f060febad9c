<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 简单的响应，不使用数据库
$action = isset($_GET['action']) ? $_GET['action'] : '';

switch ($action) {
    case 'list':
        echo json_encode(array(
            'success' => true,
            'message' => '获取成功',
            'data' => array(
                'scripts' => array(),
                'pagination' => array(
                    'page' => 1,
                    'limit' => 20,
                    'total' => 0,
                    'pages' => 0
                )
            )
        ));
        break;
        
    default:
        echo json_encode(array(
            'success' => false,
            'message' => '无效的操作'
        ));
        break;
}
?> 