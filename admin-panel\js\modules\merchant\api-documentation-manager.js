/**
 * 商户API文档管理器
 * 从admin.js第17200-17957行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class MerchantApiDocumentationManager {
    constructor() {
        this.auth = new AuthManager();
        this.apiDocs = null;
        this.currentSection = 'overview';
    }
    async initialize() {
        try {
            await this.loadApiDocumentation();
            this.renderApiDocumentation();
        } catch (error) {
            console.error('初始化API文档失败:',
            error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }
    async loadApiDocumentation() {
        try {
            const response = await fetch('/api/merchant/index.php?module=api_docs&action=get_docs', {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.apiDocs = data.data || data;
                return true;
            } else {
                this.showError('加载API文档失败: ' + (data.message || '未知错误'));
                return false;
            }
        } catch (error) {
            console.error('加载API文档失败:',
            error);
            this.showError('网络错误，请重试');
            return false;
        }
    }
    renderApiDocumentation() {
        const container = document.getElementById('merchantApiDocsContainer');
        if (!container) return;
        container.innerHTML = `
        <div class="row">
        <!-- 左侧：文档导航 -->
        <div class="col-md-3">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-list me-2"></i>文档导航</h6>
        </div>
        <div class="card-body p-0">
        <div class="list-group list-group-flush">
        <a href="#" class="list-group-item list-group-item-action ${
            this.currentSection === 'overview' ? 'active' : ''
        }"
        onclick="window.merchantApiDocManager.switchSection('overview')">
        <i class="bi bi-info-circle me-2"></i>概述
        </a>
        <a href="#" class="list-group-item list-group-item-action ${
            this.currentSection === 'authentication' ? 'active' : ''
        }"
        onclick="window.merchantApiDocManager.switchSection('authentication')">
        <i class="bi bi-shield-lock me-2"></i>认证方式
        </a>
        <a href="#" class="list-group-item list-group-item-action ${
            this.currentSection === 'apis' ? 'active' : ''
        }"
        onclick="window.merchantApiDocManager.switchSection('apis')">
        <i class="bi bi-code-square me-2"></i>API接口
        </a>
        <a href="#" class="list-group-item list-group-item-action ${
            this.currentSection === 'callback' ? 'active' : ''
        }"
        onclick="window.merchantApiDocManager.switchSection('callback')">
        <i class="bi bi-arrow-left-right me-2"></i>回调通知
        </a>
        <a href="#" class="list-group-item list-group-item-action ${
            this.currentSection === 'errors' ? 'active' : ''
        }"
        onclick="window.merchantApiDocManager.switchSection('errors')">
        <i class="bi bi-exclamation-triangle me-2"></i>错误码
        </a>
        <a href="#" class="list-group-item list-group-item-action ${
            this.currentSection === 'examples' ? 'active' : ''
        }"
        onclick="window.merchantApiDocManager.switchSection('examples')">
        <i class="bi bi-file-earmark-code me-2"></i>示例代码
        </a>
        </div>
        </div>
        </div>
        <!-- 快速链接 -->
        <div class="card mt-3">
        <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-lightning me-2"></i>快速链接</h6>
        </div>
        <div class="card-body">
        <div class="d-grid gap-2">
        <button class="btn btn-outline-primary btn-sm" onclick="window.merchantApiDocManager.goToSection('apis')">
        <i class="bi bi-code-square me-1"></i>查看API
        </button>
        <button class="btn btn-outline-secondary btn-sm" onclick="window.merchantApiDocManager.downloadDocs()">
        <i class="bi bi-download me-1"></i>下载文档
        </button>
        <button class="btn btn-outline-info btn-sm" onclick="window.merchantApiDocManager.openTester()">
        <i class="bi bi-play me-1"></i>在线测试
        </button>
        </div>
        </div>
        </div>
        </div>
        <!-- 右侧：文档内容 -->
        <div class="col-md-9">
        <div id="docContent" class="card">
        <div class="card-body">
        <!-- 文档内容将在这里动态加载 -->
        </div>
        </div>
        </div>
        </div>
        `;
        this.renderCurrentSection();
    }
    switchSection(section) {
        this.currentSection = section;
        // 更新左侧导航状态
        document.querySelectorAll('.list-group-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[onclick*="'${
            section
        }'"]`).classList.add('active');
        this.renderCurrentSection();
    }
    renderCurrentSection() {
        const docContent = document.getElementById('docContent');
        if (!docContent) return;
        switch (this.currentSection) {
            case 'overview':
            this.renderOverview(docContent);
            break;
            case 'authentication':
            this.renderAuthentication(docContent);
            break;
            case 'apis':
            this.renderApis(docContent);
            break;
            case 'callback':
            this.renderCallback(docContent);
            break;
            case 'errors':
            this.renderErrors(docContent);
            break;
            case 'examples':
            this.renderExamples(docContent);
            break;
        }
    }
    renderOverview(container) {
        const overview = this.apiDocs?.overview || {
        };
        container.innerHTML = `
        <div class="card-body">
        <h4><i class="bi bi-info-circle me-2"></i>API概述</h4>
        <hr>
        <div class="row">
        <div class="col-md-8">
        <h5>系统介绍</h5>
        <p class="text-muted">${
            overview.description || 'PayPal支付系统为商户提供完整的支付解决方案，支持多种支付方式和灵活的接口调用。'
        }</p>
        <h5 class="mt-4">主要特性</h5>
        <ul class="list-unstyled">
        <li><i class="bi bi-check-circle text-success me-2"></i>支持支付宝、微信等主流支付方式</li>
        <li><i class="bi bi-check-circle text-success me-2"></i>实时订单状态查询</li>
        <li><i class="bi bi-check-circle text-success me-2"></i>安全的签名验证机制</li>
        <li><i class="bi bi-check-circle text-success me-2"></i>灵活的回调通知</li>
        <li><i class="bi bi-check-circle text-success me-2"></i>完整的错误处理</li>
        </ul>
        <h5 class="mt-4">技术规范</h5>
        <div class="table-responsive">
        <table class="table table-bordered table-sm">
        <tr>
        <td><strong>协议</strong></td>
        <td>HTTPS</td>
        </tr>
        <tr>
        <td><strong>请求方式</strong></td>
        <td>POST</td>
        </tr>
        <tr>
        <td><strong>数据格式</strong></td>
        <td>JSON / Form Data</td>
        </tr>
        <tr>
        <td><strong>字符编码</strong></td>
        <td>UTF-8</td>
        </tr>
        <tr>
        <td><strong>签名算法</strong></td>
        <td>SHA-256</td>
        </tr>
        </table>
        </div>
        </div>
        <div class="col-md-4">
        <div class="card bg-light">
        <div class="card-header">
        <h6 class="mb-0">接口统计</h6>
        </div>
        <div class="card-body">
        <div class="d-flex justify-content-between">
        <span>主要接口</span>
        <span class="badge bg-primary">3个</span>
        </div>
        <div class="d-flex justify-content-between mt-2">
        <span>支持语言</span>
        <span class="badge bg-success">6种</span>
        </div>
        <div class="d-flex justify-content-between mt-2">
        <span>错误码</span>
        <span class="badge bg-warning">15个</span>
        </div>
        </div>
        </div>
        <div class="alert alert-info mt-3">
        <h6><i class="bi bi-lightbulb me-2"></i>开发建议</h6>
        <small>
        建议先阅读认证方式和API接口文档，然后使用在线测试工具验证接口调用。
        </small>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    renderAuthentication(container) {
        const auth = this.apiDocs?.authentication || {
        };
        container.innerHTML = `
        <div class="card-body">
        <h4><i class="bi bi-shield-lock me-2"></i>认证方式</h4>
        <hr>
        <h5>签名算法</h5>
        <p class="text-muted">所有API请求都需要使用SHA-256算法生成签名，确保数据传输安全。</p>
        <div class="alert alert-warning">
        <h6><i class="bi bi-exclamation-triangle me-2"></i>重要提醒</h6>
        <p class="mb-0">请妥善保管您的API密钥，不要在客户端代码中暴露密钥信息。</p>
        </div>
        <h5 class="mt-4">签名生成步骤</h5>
        <ol>
        <li><strong>参数排序</strong>：将所有业务参数按键名进行ASCII码升序排列</li>
        <li><strong>添加系统参数</strong>：加入developer_id、developer_key、timestamp</li>
        <li><strong>构造签名字符串</strong>：按照key=value&key=value的格式拼接</li>
        <li><strong>计算签名</strong>：使用SHA-256算法计算签名值</li>
        <li><strong>转换大写</strong>：将签名结果转换为大写字母</li>
        </ol>
        <h5 class="mt-4">签名示例</h5>
        <div class="code-block">
        <strong>原始参数：</strong><br>
        amount=100<br>
        type=2<br>
        product_id=1<br>
        order_no=ORDER123<br>
        developer_id=your_developer_id<br>
        developer_key=your_api_key<br>
        timestamp=1703001234<br>
        <br>
        <strong>排序后：</strong><br>
        amount=100&developer_id=your_developer_id&developer_key=your_api_key&order_no=ORDER123&product_id=1&timestamp=1703001234&type=2<br>
        <br>
        <strong>签名结果：</strong><br>
        <code>A1B2C3D4E5F6789...</code>
        </div>
        <h5 class="mt-4">请求格式</h5>
        <div class="code-block">
        <strong>POST请求示例：</strong><br>
        <pre>
        POST /api.php?do=pay_get_qrcode HTTP/1.1
        Host: your-domain.com
        Content-Type: application/x-www-form-urlencoded
        amount=100&type=2&product_id=1&order_no=ORDER123&developer_id=your_developer_id&timestamp=1703001234&sign=A1B2C3D4E5F6789...
        </pre>
        </div>
        <div class="mt-4">
        <button class="btn btn-primary" onclick="window.merchantApiDocManager.openSignatureTool()">
        <i class="bi bi-tools me-2"></i>打开签名工具
        </button>
        </div>
        </div>
        `;
    }
    renderApis(container) {
        const apis = this.apiDocs?.apis || {
        };
        container.innerHTML = `
        <div class="card-body">
        <h4><i class="bi bi-code-square me-2"></i>API接口</h4>
        <hr>
        ${
            this.renderApiSection('pay_get_qrcode', '获取收款页面',
            apis.pay_get_qrcode)
        }
        ${
            this.renderApiSection('pay_get_orders_inquiry', '查询订单信息',
            apis.pay_get_orders_inquiry)
        }
        ${
            this.renderApiSection('pay_get_payment_quota', '查询产品可用额度',
            apis.pay_get_payment_quota)
        }
        </div>
        `;
    }
    renderApiSection(apiKey,
    apiName,
    apiData) {
        if (!apiData) {
            return `
            <div class="api-section mb-5">
            <h5>${
                apiName
            }</h5>
            <p class="text-muted">接口文档加载中...</p>
            </div>
            `;
        }
        const paramsHtml = this.renderParameterTable(apiData.parameters);
        const responseHtml = this.renderResponseTable(apiData.response);
        const exampleHtml = this.renderExampleCode(apiData.example);
        return `
        <div class="api-section mb-5">
        <div class="d-flex justify-content-between align-items-start">
        <div>
        <h5>${
            apiName
        }</h5>
        <p class="text-muted">${
            apiData.description || ''
        }</p>
        <div class="mb-3">
        <span class="badge bg-success me-2">POST</span>
        <code>${
            apiData.url || '/api.php?do=' + apiKey
        }</code>
        </div>
        </div>
        <button class="btn btn-outline-primary btn-sm" onclick="window.merchantApiDocManager.testApi('${
            apiKey
        }')">
        <i class="bi bi-play me-1"></i>测试
        </button>
        </div>
        ${
            paramsHtml
        }
        ${
            responseHtml
        }
        ${
            exampleHtml
        }
        </div>
        `;
    }
    renderParameterTable(parameters) {
        if (!parameters || !Array.isArray(parameters)) return '';
        const rows = parameters.map(param => `
        <tr>
        <td><code>${
            param.name
        }</code></td>
        <td>${
            param.type
        }</td>
        <td>${
            param.required ? '<span class="badge bg-danger">必填</span>' : '<span class="badge bg-secondary">可选</span>'
        }</td>
        <td>${
            param.description
        }</td>
        <td><code>${
            param.example || ''
        }</code></td>
        </tr>
        `).join('');
        return `
        <h6 class="mt-4">请求参数</h6>
        <div class="table-responsive">
        <table class="table table-bordered table-sm">
        <thead class="table-light">
        <tr>
        <th>参数名</th>
        <th>类型</th>
        <th>必填</th>
        <th>说明</th>
        <th>示例</th>
        </tr>
        </thead>
        <tbody>
        ${
            rows
        }
        </tbody>
        </table>
        </div>
        `;
    }
    renderResponseTable(response) {
        if (!response || !Array.isArray(response)) return '';
        const rows = response.map(field => `
        <tr>
        <td><code>${
            field.name
        }</code></td>
        <td>${
            field.type
        }</td>
        <td>${
            field.description
        }</td>
        <td><code>${
            field.example || ''
        }</code></td>
        </tr>
        `).join('');
        return `
        <h6 class="mt-4">响应参数</h6>
        <div class="table-responsive">
        <table class="table table-bordered table-sm">
        <thead class="table-light">
        <tr>
        <th>字段名</th>
        <th>类型</th>
        <th>说明</th>
        <th>示例</th>
        </tr>
        </thead>
        <tbody>
        ${
            rows
        }
        </tbody>
        </table>
        </div>
        `;
    }
    renderExampleCode(example) {
        if (!example) return '';
        return `
        <h6 class="mt-4">请求示例</h6>
        <div class="code-block">
        <pre>${
            JSON.stringify(example.request || {
            },
            null, 2)
        }</pre>
        </div>
        <h6 class="mt-3">响应示例</h6>
        <div class="code-block">
        <pre>${
            JSON.stringify(example.response || {
            },
            null, 2)
        }</pre>
        </div>
        `;
    }
    renderCallback(container) {
        const callback = this.apiDocs?.callback || {
        };
        container.innerHTML = `
        <div class="card-body">
        <h4><i class="bi bi-arrow-left-right me-2"></i>回调通知</h4>
        <hr>
        <h5>回调说明</h5>
        <p class="text-muted">${
            callback.description || '当订单状态发生变化时，系统会向商户指定的回调地址发送通知。'
        }</p>
        <div class="alert alert-info">
        <h6><i class="bi bi-info-circle me-2"></i>重要提醒</h6>
        <ul class="mb-0">
        <li>回调通知采用POST方式发送</li>
        <li>回调数据包含完整的订单信息</li>
        <li>商户需要验证回调签名确保数据安全</li>
        <li>收到回调后请返回"OK"表示处理成功</li>
        </ul>
        </div>
        <h5 class="mt-4">回调参数</h5>
        <div class="table-responsive">
        <table class="table table-bordered table-sm">
        <thead class="table-light">
        <tr>
        <th>参数名</th>
        <th>类型</th>
        <th>说明</th>
        </tr>
        </thead>
        <tbody>
        <tr><td><code>order_no</code></td><td>string</td><td>商户订单号</td></tr>
        <tr><td><code>platform_order_no</code></td><td>string</td><td>平台订单号</td></tr>
        <tr><td><code>amount</code></td><td>decimal</td><td>订单金额</td></tr>
        <tr><td><code>status</code></td><td>int</td><td>订单状态</td></tr>
        <tr><td><code>pay_time</code></td><td>int</td><td>支付时间戳</td></tr>
        <tr><td><code>developer_id</code></td><td>string</td><td>开发者ID</td></tr>
        <tr><td><code>timestamp</code></td><td>int</td><td>时间戳</td></tr>
        <tr><td><code>sign</code></td><td>string</td><td>签名</td></tr>
        </tbody>
        </table>
        </div>
        <h5 class="mt-4">签名验证</h5>
        <div class="code-block">
        <strong>PHP验证示例：</strong><br>
        <pre>
        // 获取回调参数
        $params = $_POST;
        $receivedSign = $params['sign'];
        unset($params['sign']);
        // 参数排序
        ksort($params);
        // 添加密钥
        $params['developer_key'] = 'your_api_key';
        // 构造签名字符串
        $signString = http_build_query($params);
        // 计算签名
        $calculatedSign = strtoupper(hash('sha256',
        $signString));
        // 验证签名
        if ($calculatedSign === $receivedSign) {
            // 签名验证成功，处理业务逻辑
            echo "OK";
        } else {
            // 签名验证失败
            echo "FAIL";
        }
        </pre>
        </div>
        </div>
        `;
    }
    renderErrors(container) {
        const errors = this.apiDocs?.error_codes || {
        };
        container.innerHTML = `
        <div class="card-body">
        <h4><i class="bi bi-exclamation-triangle me-2"></i>错误码说明</h4>
        <hr>
        <div class="table-responsive">
        <table class="table table-bordered">
        <thead class="table-light">
        <tr>
        <th>错误码</th>
        <th>说明</th>
        <th>解决方案</th>
        </tr>
        </thead>
        <tbody>
        ${
            Object.keys(errors).map(code => {
                const error = errors[code];
                const badgeClass = code === '0' ? 'bg-success' : 'bg-danger';
                return `
                <tr>
                <td><span class="badge ${
                    badgeClass
                }">${
                    code
                }</span></td>
                <td>${
                    error.message || error
                }</td>
                <td class="text-muted small">${
                    error.solution || '请检查请求参数'
                }</td>
                </tr>
                `;
            }).join('')
        }
        </tbody>
        </table>
        </div>
        <div class="alert alert-warning mt-4">
        <h6><i class="bi bi-lightbulb me-2"></i>调试建议</h6>
        <p class="mb-0">遇到错误时，请先检查签名是否正确，然后验证必填参数是否完整。可以使用签名工具进行调试。</p>
        </div>
        </div>
        `;
    }
    renderExamples(container) {
        container.innerHTML = `
        <div class="card-body">
        <h4><i class="bi bi-file-earmark-code me-2"></i>示例代码</h4>
        <hr>
        <div class="alert alert-info">
        <h6><i class="bi bi-info-circle me-2"></i>代码示例</h6>
        <p class="mb-0">这里展示各种编程语言的调用示例，您也可以使用代码生成器自动生成。</p>
        </div>
        <div class="row">
        <div class="col-md-6">
        <h5>PHP示例</h5>
        <div class="code-block">
        <pre>
        &lt;
        ?php
        // PayPal支付接口调用示例
        $developer_id = 'your_developer_id';
        $developer_key = 'your_api_key';
        $timestamp = time();
        $params = [
        'amount' => 100,
        'type' => 2,
        'product_id' => 1,
        'order_no' => 'ORDER' . time(),
        'developer_id' => $developer_id,
        'developer_key' => $developer_key,
        'timestamp' => $timestamp
        ];
        ksort($params);
        $sign = strtoupper(hash('sha256',
        http_build_query($params)));
        unset($params['developer_key']);
        $params['sign'] = $sign;
        $ch = curl_init();
        curl_setopt($ch,
        CURLOPT_URL, 'https://your-domain.com/api.php?do=pay_get_qrcode');
        curl_setopt($ch,
        CURLOPT_POST,
        true);
        curl_setopt($ch,
        CURLOPT_POSTFIELDS,
        http_build_query($params));
        curl_setopt($ch,
        CURLOPT_RETURNTRANSFER,
        true);
        $response = curl_exec($ch);
        curl_close($ch);
        echo $response;
        ?&gt;
        </pre>
        </div>
        </div>
        <div class="col-md-6">
        <h5>Python示例</h5>
        <div class="code-block">
        <pre>
        import hashlib
        import requests
        import time
        from urllib.parse import urlencode
        # PayPal支付接口调用示例
        developer_id = 'your_developer_id'
        developer_key = 'your_api_key'
        timestamp = int(time.time())
        params = {
            'amount': 100,
            'type': 2,
            'product_id': 1,
            'order_no': f'ORDER{
                timestamp
            }',
            'developer_id': developer_id,
            'developer_key': developer_key,
            'timestamp': timestamp
        }
        # 生成签名
        sorted_params = sorted(params.items())
        sign_string = urlencode(sorted_params)
        sign = hashlib.sha256(sign_string.encode()).hexdigest().upper()
        # 移除密钥并添加签名
        del params['developer_key']
        params['sign'] = sign
        # 发送请求
        response = requests.post(
        'https://your-domain.com/api.php?do=pay_get_qrcode',
        data=params
        )
        print(response.text)
        </pre>
        </div>
        </div>
        </div>
        <div class="mt-4">
        <button class="btn btn-primary" onclick="window.merchantApiDocManager.openCodeGenerator()">
        <i class="bi bi-code-square me-2"></i>打开代码生成器
        </button>
        </div>
        </div>
        `;
    }
    // 导航和工具函数
    goToSection(section) {
        this.switchSection(section);
    }
    openSignatureTool() {
        // 切换到签名工具页面
        const signatureMenuItem = document.querySelector('[data-page="signature-tool"]');
        if (signatureMenuItem) {
            signatureMenuItem.click();
        }
    }
    openCodeGenerator() {
        // 切换到代码生成器页面
        const codeGenMenuItem = document.querySelector('[data-page="code-generator"]');
        if (codeGenMenuItem) {
            codeGenMenuItem.click();
        }
    }
    openTester() {
        this.openSignatureTool();
    }
    testApi(apiType) {
        // 切换到签名工具的API测试器
        this.openSignatureTool();
        // 延迟执行以确保页面已加载
        setTimeout(() => {
            if (window.signatureToolManager) {
                window.signatureToolManager.switchTool('tester');
                // 设置API类型
                const apiSelect = document.getElementById('apiTestType');
                if (apiSelect) {
                    apiSelect.value = apiType;
                }
            }
        }, 500);
    }
    async refreshDocs() {
        const container = document.getElementById('merchantApiDocsContainer');
        if (container) {
            container.innerHTML = `
            <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">刷新中...</span>
            </div>
            <div class="mt-3">正在刷新API文档...</div>
            </div>
            `;
        }
        await this.loadApiDocumentation();
        this.renderApiDocumentation();
        this.showSuccess('API文档已刷新');
    }
    downloadDocs() {
        if (!this.apiDocs) {
            this.showError('没有可下载的文档');
            return;
        }
        const docContent = JSON.stringify(this.apiDocs,
        null, 2);
        const blob = new Blob([docContent], {
            type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'paypal-merchant-api-docs.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        this.showSuccess('API文档已下载');
    }
    showSuccess(message) {
        console.log('Success:',
        message);
        // 这里可以集成toast通知组件
    }
    showError(message) {
        console.error('Error:',
        message);
        // 这里可以集成toast通知组件
        const container = document.getElementById('merchantApiDocsContainer');
        if (container) {
            container.innerHTML = `
            <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle me-2"></i>
            ${
                message
            }
            <button class="btn btn-outline-danger btn-sm ms-3" onclick="window.merchantApiDocManager.refreshDocs()">
            重试
            </button>
            </div>
            `;
        }
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = MerchantApiDocumentationManager;
} else if (typeof window !== "undefined") {
    window.MerchantApiDocumentationManager = MerchantApiDocumentationManager;
}

console.log('📦 MerchantApiDocumentationManager 模块加载完成');
