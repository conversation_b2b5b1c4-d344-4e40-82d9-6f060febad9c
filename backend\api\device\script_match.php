<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';

/**
 * 响应JSON数据
 */
function jsonResponse($code, $message, $data = null) {
    echo json_encode([
        'code' => $code,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 验证设备权限
 */
function validateDevicePermission($device_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT status FROM devices WHERE device_id = ?");
    $stmt->execute([$device_id]);
    $device = $stmt->fetch();
    
    return $device && $device['status'] === 'active';
}

/**
 * AES加密脚本内容（PHP版本，对应Android解密）
 */
function encryptScript($content) {
    $salt = "PayPal_Script_2025";
    $keySource = $salt . "UNIFIED_KEY";
    $key = hash('sha256', $keySource, true);
    
    // 生成随机IV
    $iv = openssl_random_pseudo_bytes(16);
    
    // AES-256-CBC加密
    $encrypted = openssl_encrypt($content, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
    
    if ($encrypted === false) {
        return null;
    }
    
    // 组合IV + 密文并Base64编码
    return base64_encode($iv . $encrypted);
}

/**
 * 解密脚本内容（用于调试）
 */
function decryptScript($encryptedContent) {
    $salt = "PayPal_Script_2025";
    $keySource = $salt . "UNIFIED_KEY";
    $key = hash('sha256', $keySource, true);
    
    // Base64解码
    $combined = base64_decode($encryptedContent);
    if ($combined === false || strlen($combined) <= 16) {
        return null;
    }
    
    // 分离IV和密文
    $iv = substr($combined, 0, 16);
    $encrypted = substr($combined, 16);
    
    // AES-256-CBC解密
    $decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
    
    return $decrypted;
}

// 获取请求参数
$action = isset($_GET['action']) ? $_GET['action'] : '';
$device_id = isset($_GET['device_id']) ? $_GET['device_id'] : (isset($_POST['device_id']) ? $_POST['device_id'] : '');

// 验证设备ID
if (empty($device_id)) {
    jsonResponse(400, '缺少设备ID参数');
}

if (!validateDevicePermission($device_id)) {
    jsonResponse(403, '设备未授权或状态异常');
}

switch ($action) {
    case 'match':
        handleScriptMatch($device_id);
        break;
        
    case 'upload':
        handleScriptUpload($device_id);
        break;
        
    default:
        jsonResponse(400, '无效的操作类型');
}

/**
 * 处理脚本匹配
 */
function handleScriptMatch($device_id) {
    global $pdo;
    
    $app_type = isset($_GET['app_type']) ? $_GET['app_type'] : 'alipay';
    $environment = isset($_GET['environment']) ? $_GET['environment'] : 'release';
    
    // 获取设备信息
    $stmt = $pdo->prepare("SELECT * FROM devices WHERE device_id = ?");
    $stmt->execute([$device_id]);
    $device = $stmt->fetch();
    
    if (!$device) {
        jsonResponse(404, '设备不存在');
    }
    
    // 检查设备品牌是否已设置且支持
    if (empty($device['device_brand'])) {
        jsonResponse(400, '设备品牌未设置，请先选择设备品牌');
    }
    
    $stmt = $pdo->prepare("SELECT * FROM supported_brands WHERE brand_code = ? AND is_active = 1");
    $stmt->execute([$device['device_brand']]);
    $supportedBrand = $stmt->fetch();
    
    if (!$supportedBrand) {
        jsonResponse(400, '设备品牌不受支持，请联系管理员或重新选择品牌');
    }
    
    // 获取应用版本
    $app_version = $app_type === 'alipay' ? $device['alipay_version'] : $device['wechat_version'];
    if (empty($app_version) || $app_version === '未安装') {
        jsonResponse(404, $app_type === 'alipay' ? '支付宝未安装' : '微信未安装');
    }
    
    // 脚本匹配逻辑
    $script = findBestMatchScript($app_type, $app_version, $device['device_brand'], $environment);
    
    if (!$script) {
        jsonResponse(404, '未找到匹配的脚本');
    }
    
    // 读取已加密的脚本文件内容
    $scriptPath = dirname(__FILE__) . '/../../scripts/' . $script['file_path'];
    if (!file_exists($scriptPath)) {
        jsonResponse(500, '脚本文件不存在');
    }
    
    $encryptedContent = file_get_contents($scriptPath);
    if ($encryptedContent === false) {
        jsonResponse(500, '读取脚本文件失败');
    }
    
    jsonResponse(200, '脚本匹配成功', [
        'script_name' => $script['script_name'],
        'script_content' => $encryptedContent,
        'version' => $script['version'],
        'description' => $script['description'],
        'match_type' => $script['match_type'],
        'app_version' => $app_version,
        'device_brand' => $device['device_brand'],
        'environment' => $environment
    ]);
}

/**
 * 查找最佳匹配脚本
 */
function findBestMatchScript($app_type, $app_version, $device_brand, $environment) {
    global $pdo;
    
    // 1. 精确匹配：应用类型 + 应用版本 + 设备品牌 + 环境
    $stmt = $pdo->prepare("
        SELECT *, 'exact' as match_type 
        FROM script_versions 
        WHERE app_type = ? AND app_version = ? AND device_brand = ? AND environment = ? AND is_active = 1
        ORDER BY updated_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$app_type, $app_version, $device_brand, $environment]);
    $script = $stmt->fetch();
    
    if ($script) {
        return $script;
    }
    
    // 2. 通用版本匹配：使用*版本（适配所有版本）
    $stmt = $pdo->prepare("
        SELECT *, 'universal' as match_type 
        FROM script_versions 
        WHERE app_type = ? AND app_version = '*' AND device_brand = ? AND environment = ? AND is_active = 1
        ORDER BY updated_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$app_type, $device_brand, $environment]);
    $script = $stmt->fetch();
    
    if ($script) {
        return $script;
    }
    
    // 3. 版本降级匹配：找相同品牌的较低版本
    $stmt = $pdo->prepare("
        SELECT *, 'fallback' as match_type 
        FROM script_versions 
        WHERE app_type = ? AND device_brand = ? AND environment = ? AND is_active = 1
        AND app_version != '*' AND app_version <= ? 
        ORDER BY app_version DESC, updated_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$app_type, $device_brand, $environment, $app_version]);
    $script = $stmt->fetch();
    
    if ($script) {
        return $script;
    }
    
    // 4. 通用品牌匹配：使用generic品牌
    $stmt = $pdo->prepare("
        SELECT *, 'generic' as match_type 
        FROM script_versions 
        WHERE app_type = ? AND device_brand = 'generic' AND environment = ? AND is_active = 1
        ORDER BY updated_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$app_type, $environment]);
    $script = $stmt->fetch();
    
    return $script;
}

/**
 * 处理脚本上传
 */
function handleScriptUpload($device_id) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        jsonResponse(405, '仅支持POST请求');
    }
    
    $script_name = isset($_POST['script_name']) ? $_POST['script_name'] : '';
    $app_type = isset($_POST['app_type']) ? $_POST['app_type'] : '';
    $app_version = isset($_POST['app_version']) ? $_POST['app_version'] : '';
    $device_brand = isset($_POST['device_brand']) ? $_POST['device_brand'] : '';
    $environment = isset($_POST['environment']) ? $_POST['environment'] : 'release';
    $script_content = isset($_POST['script_content']) ? $_POST['script_content'] : '';
    $description = isset($_POST['description']) ? $_POST['description'] : '';
    $version = isset($_POST['version']) ? $_POST['version'] : '1.0.0';
    
    if (empty($script_name) || empty($app_type) || empty($script_content)) {
        jsonResponse(400, '脚本名称、应用类型和脚本内容不能为空');
    }
    
    // 加密脚本内容
    $encrypted_content = encryptScript($script_content);
    if ($encrypted_content === null) {
        jsonResponse(500, '脚本加密失败');
    }
    
    // 保存到数据库
    global $pdo;
    $stmt = $pdo->prepare("
        INSERT INTO script_versions 
        (script_name, app_type, app_version, device_brand, environment, script_content, version, description)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        script_content = VALUES(script_content),
        version = VALUES(version),
        description = VALUES(description),
        updated_at = NOW()
    ");
    
    $success = $stmt->execute([
        $script_name, $app_type, $app_version, $device_brand, 
        $environment, $encrypted_content, $version, $description
    ]);
    
    if ($success) {
        jsonResponse(200, '脚本上传成功', [
            'script_name' => $script_name,
            'encrypted_size' => strlen($encrypted_content),
            'upload_time' => date('Y-m-d H:i:s')
        ]);
    } else {
        jsonResponse(500, '脚本保存失败');
    }
}

?> 