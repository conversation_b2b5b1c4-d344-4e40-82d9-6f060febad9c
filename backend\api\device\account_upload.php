<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';

$db = new Database();

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(array(
        'success' => false,
        'message' => '只支持POST请求'
    ));
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(array(
        'success' => false,
        'message' => '无效的JSON数据'
    ));
    exit;
}

// 验证必需参数
$deviceIdString = isset($input['device_id']) ? trim($input['device_id']) : '';
$accountName = isset($input['account_name']) ? trim($input['account_name']) : '';
$accountNumber = isset($input['account_number']) ? trim($input['account_number']) : '';



if (empty($deviceIdString) || empty($accountName) || empty($accountNumber)) {
    http_response_code(400);
    echo json_encode(array(
        'success' => false,
        'message' => '缺少必需参数: device_id, account_name, account_number'
    ));
    exit;
}

try {
    // 1. 验证设备是否已登录且激活，同时获取devices表的id
    $device = $db->fetch(
        "SELECT id, provider_id, status FROM devices WHERE device_id = ?",
        array($deviceIdString)
    );
    
    if (!$device) {
        http_response_code(401);
        echo json_encode(array(
            'success' => false,
            'message' => '设备未注册'
        ));
        exit;
    }
    
    if ($device['status'] !== 'active') {
        http_response_code(401);
        echo json_encode(array(
            'success' => false,
            'message' => '设备未激活，无法上传账户信息'
        ));
        exit;
    }
    
    $deviceTableId = $device['id'];  // devices表的主键id
    $providerId = $device['provider_id'];
    if (!$providerId) {
        http_response_code(401);
        echo json_encode(array(
            'success' => false,
            'message' => '设备未分配码商，无法上传账户信息'
        ));
        exit;
    }
    
    // 2. 检查账户是否已存在（基于设备ID和账号）
    $existingAccount = $db->fetch(
        "SELECT id FROM alipay_accounts WHERE account_number = ? AND device_id = ?",
        array($accountNumber, $deviceTableId)
    );
    
    if ($existingAccount) {
        // 更新现有账户信息
        $db->execute(
            "UPDATE alipay_accounts SET 
                account_name = ?,
                updated_at = NOW()
            WHERE id = ?",
            array($accountName, $existingAccount['id'])
        );
        
        $message = '账户信息更新成功';
        $accountId = $existingAccount['id'];
    } else {
        // 插入新账户信息
        $db->execute(
            "INSERT INTO alipay_accounts (
                device_id,
                provider_id, 
                account_name, 
                account_number,
                status,
                created_at
            ) VALUES (?, ?, ?, ?, 'pending', NOW())",
            array($deviceTableId, $providerId, $accountName, $accountNumber)
        );
        
        $accountId = $db->lastInsertId();
        $message = '账户信息上传成功';
    }
    
    // 3. 记录上传日志
    error_log("Account uploaded: Device $deviceIdString, DeviceTableId $deviceTableId, Provider $providerId, Account $accountNumber");
    
    // 4. 返回成功响应
    echo json_encode(array(
        'success' => true,
        'message' => $message,
        'data' => array(
            'account_id' => $accountId,
            'device_table_id' => $deviceTableId,
            'provider_id' => $providerId,
            'device_id_string' => $deviceIdString,
            'account_name' => $accountName,
            'account_number' => $accountNumber,
            'upload_time' => date('Y-m-d H:i:s')
        )
    ));
    
} catch (Exception $e) {
    error_log("Account upload error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode(array(
        'success' => false,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ));
}
?> 