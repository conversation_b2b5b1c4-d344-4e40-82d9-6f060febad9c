<?php
/**
 * API文档生成器
 * 自动解析代码注释和函数签名，生成完整的API文档
 * 
 * <AUTHOR> System Team
 * @version 1.0.0
 * @since Day 14
 */

class ApiDocGenerator {
    private $config;
    private $endpoints;
    private $schemas;
    private $examples;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->config = $this->loadConfig();
        $this->endpoints = array();
        $this->schemas = array();
        $this->examples = array();
    }
    
    /**
     * 加载配置
     */
    private function loadConfig() {
        return array(
            'title' => 'PayPal管理系统 API文档',
            'version' => '1.0.0',
            'description' => '完整的PayPal管理系统API接口文档',
            'base_url' => '/api',
            'contact' => array(
                'name' => 'PayPal System Team',
                'email' => '<EMAIL>'
            ),
            'license' => array(
                'name' => 'MIT License',
                'url' => 'https://opensource.org/licenses/MIT'
            )
        );
    }
    
    /**
     * 扫描API文件
     */
    public function scanApiFiles($files) {
        foreach ($files as $file) {
            if (file_exists($file)) {
                $this->parseApiFile($file);
            }
        }
    }
    
    /**
     * 解析API文件
     */
    private function parseApiFile($file) {
        $content = file_get_contents($file);
        $tokens = token_get_all($content);
        
        $currentClass = null;
        $currentFunction = null;
        $docComment = null;
        
        for ($i = 0; $i < count($tokens); $i++) {
            $token = $tokens[$i];
            
            if (is_array($token)) {
                switch ($token[0]) {
                    case T_DOC_COMMENT:
                        $docComment = $token[1];
                        break;
                        
                    case T_CLASS:
                        $className = $this->getNextIdentifier($tokens, $i);
                        $currentClass = $className;
                        break;
                        
                    case T_FUNCTION:
                        $functionName = $this->getNextIdentifier($tokens, $i);
                        $currentFunction = $functionName;
                        
                        // 解析函数
                        if ($docComment && $this->isApiFunction($functionName)) {
                            $endpoint = $this->parseEndpoint($functionName, $docComment, $tokens, $i);
                            if ($endpoint) {
                                $this->endpoints[] = $endpoint;
                            }
                        }
                        
                        $docComment = null;
                        break;
                }
            }
        }
    }
    
    /**
     * 获取下一个标识符
     */
    private function getNextIdentifier($tokens, $start) {
        for ($i = $start + 1; $i < count($tokens); $i++) {
            $token = $tokens[$i];
            if (is_array($token) && $token[0] === T_STRING) {
                return $token[1];
            }
        }
        return null;
    }
    
    /**
     * 检查是否为API函数
     */
    private function isApiFunction($functionName) {
        return strpos($functionName, 'handle') === 0 || 
               strpos($functionName, 'get') === 0 || 
               strpos($functionName, 'create') === 0 || 
               strpos($functionName, 'update') === 0 || 
               strpos($functionName, 'delete') === 0;
    }
    
    /**
     * 解析端点信息
     */
    private function parseEndpoint($functionName, $docComment, $tokens, $position) {
        $doc = $this->parseDocComment($docComment);
        
        // 获取函数参数
        $parameters = $this->getFunctionParameters($tokens, $position);
        
        // 推断HTTP方法
        $method = $this->inferHttpMethod($functionName);
        
        // 推断端点路径
        $path = $this->inferEndpointPath($functionName);
        
        $endpoint = array(
            'path' => $path,
            'method' => $method,
            'function' => $functionName,
            'summary' => $doc['summary'] ?: $this->generateSummary($functionName),
            'description' => $doc['description'] ?: $this->generateDescription($functionName),
            'parameters' => $this->parseParameters($doc['params'], $parameters),
            'responses' => $this->parseResponses($doc['return']),
            'tags' => $this->inferTags($functionName),
            'security' => $this->inferSecurity($functionName),
            'examples' => $this->generateExamples($functionName, $method)
        );
        
        return $endpoint;
    }
    
    /**
     * 解析文档注释
     */
    private function parseDocComment($comment) {
        $lines = explode("\n", $comment);
        $doc = array(
            'summary' => '',
            'description' => '',
            'params' => array(),
            'return' => '',
            'throws' => array(),
            'tags' => array()
        );
        
        $currentSection = 'description';
        $descriptionLines = array();
        
        foreach ($lines as $line) {
            $line = trim($line, " \t\n\r\0\x0B/*");
            
            if (empty($line)) continue;
            
            if (preg_match('/@(\w+)\s*(.*)/', $line, $matches)) {
                $tag = $matches[1];
                $content = $matches[2];
                
                switch ($tag) {
                    case 'param':
                        if (preg_match('/(\S+)\s+\$(\w+)\s*(.*)/', $content, $paramMatches)) {
                            $doc['params'][] = array(
                                'type' => $paramMatches[1],
                                'name' => $paramMatches[2],
                                'description' => $paramMatches[3]
                            );
                        }
                        break;
                        
                    case 'return':
                        $doc['return'] = $content;
                        break;
                        
                    case 'throws':
                        $doc['throws'][] = $content;
                        break;
                        
                    default:
                        $doc['tags'][$tag] = $content;
                        break;
                }
                $currentSection = $tag;
            } else {
                if ($currentSection === 'description') {
                    $descriptionLines[] = $line;
                }
            }
        }
        
        // 处理描述
        if (!empty($descriptionLines)) {
            $doc['summary'] = $descriptionLines[0];
            $doc['description'] = implode("\n", array_slice($descriptionLines, 1));
        }
        
        return $doc;
    }
    
    /**
     * 获取函数参数
     */
    private function getFunctionParameters($tokens, $start) {
        $parameters = array();
        $inParams = false;
        $paramDepth = 0;
        $currentParam = '';
        
        for ($i = $start; $i < count($tokens); $i++) {
            $token = $tokens[$i];
            
            if (is_string($token)) {
                if ($token === '(') {
                    $inParams = true;
                    $paramDepth++;
                } elseif ($token === ')') {
                    $paramDepth--;
                    if ($paramDepth === 0) {
                        if (!empty($currentParam)) {
                            $parameters[] = trim($currentParam);
                        }
                        break;
                    }
                } elseif ($token === ',' && $paramDepth === 1) {
                    if (!empty($currentParam)) {
                        $parameters[] = trim($currentParam);
                        $currentParam = '';
                    }
                } elseif ($inParams) {
                    $currentParam .= $token;
                }
            } elseif (is_array($token) && $inParams) {
                $currentParam .= $token[1];
            }
        }
        
        return $parameters;
    }
    
    /**
     * 推断HTTP方法
     */
    private function inferHttpMethod($functionName) {
        if (strpos($functionName, 'create') !== false || strpos($functionName, 'add') !== false) {
            return 'POST';
        } elseif (strpos($functionName, 'update') !== false || strpos($functionName, 'edit') !== false) {
            return 'PUT';
        } elseif (strpos($functionName, 'delete') !== false || strpos($functionName, 'remove') !== false) {
            return 'DELETE';
        } else {
            return 'GET';
        }
    }
    
    /**
     * 推断端点路径
     */
    private function inferEndpointPath($functionName) {
        // 移除handle前缀
        $path = preg_replace('/^handle/', '', $functionName);
        
        // 转换为小写并添加下划线
        $path = strtolower(preg_replace('/([A-Z])/', '_$1', $path));
        $path = ltrim($path, '_');
        
        return '/admin.php?action=' . $path;
    }
    
    /**
     * 解析参数
     */
    private function parseParameters($docParams, $functionParams) {
        $parameters = array();
        
        // 添加通用参数
        $parameters[] = array(
            'name' => 'action',
            'in' => 'query',
            'required' => true,
            'type' => 'string',
            'description' => 'API动作名称'
        );
        
        // 从文档注释解析参数
        foreach ($docParams as $param) {
            $parameters[] = array(
                'name' => $param['name'],
                'in' => 'body',
                'required' => true,
                'type' => $this->mapPhpTypeToJsonType($param['type']),
                'description' => $param['description']
            );
        }
        
        return $parameters;
    }
    
    /**
     * 解析响应
     */
    private function parseResponses($returnDoc) {
        $responses = array();
        
        // 成功响应
        $responses['200'] = array(
            'description' => '请求成功',
            'schema' => array(
                'type' => 'object',
                'properties' => array(
                    'success' => array('type' => 'boolean'),
                    'data' => array('type' => 'object'),
                    'message' => array('type' => 'string')
                )
            )
        );
        
        // 错误响应
        $responses['400'] = array(
            'description' => '请求参数错误',
            'schema' => array(
                'type' => 'object',
                'properties' => array(
                    'success' => array('type' => 'boolean'),
                    'error' => array(
                        'type' => 'object',
                        'properties' => array(
                            'code' => array('type' => 'integer'),
                            'message' => array('type' => 'string')
                        )
                    )
                )
            )
        );
        
        $responses['401'] = array(
            'description' => '未授权访问'
        );
        
        $responses['500'] = array(
            'description' => '服务器内部错误'
        );
        
        return $responses;
    }
    
    /**
     * 推断标签
     */
    private function inferTags($functionName) {
        $tags = array();
        
        if (strpos($functionName, 'User') !== false) {
            $tags[] = '用户管理';
        } elseif (strpos($functionName, 'Employee') !== false) {
            $tags[] = '员工管理';
        } elseif (strpos($functionName, 'Provider') !== false) {
            $tags[] = '码商管理';
        } elseif (strpos($functionName, 'Merchant') !== false) {
            $tags[] = '商户管理';
        } elseif (strpos($functionName, 'Product') !== false) {
            $tags[] = '产品管理';
        } elseif (strpos($functionName, 'Payment') !== false) {
            $tags[] = '支付管理';
        } elseif (strpos($functionName, 'Financial') !== false) {
            $tags[] = '财务管理';
        } elseif (strpos($functionName, 'Risk') !== false) {
            $tags[] = '风控管理';
        } elseif (strpos($functionName, 'Performance') !== false) {
            $tags[] = '性能监控';
        } else {
            $tags[] = '系统管理';
        }
        
        return $tags;
    }
    
    /**
     * 推断安全要求
     */
    private function inferSecurity($functionName) {
        // 登录接口不需要认证
        if ($functionName === 'handleLogin') {
            return array();
        }
        
        return array(
            array('bearerAuth' => array())
        );
    }
    
    /**
     * 生成示例
     */
    private function generateExamples($functionName, $method) {
        $examples = array();
        
        // 请求示例
        $examples['request'] = $this->generateRequestExample($functionName, $method);
        
        // 响应示例
        $examples['response'] = $this->generateResponseExample($functionName);
        
        return $examples;
    }
    
    /**
     * 生成请求示例
     */
    private function generateRequestExample($functionName, $method) {
        $example = array(
            'method' => $method,
            'url' => $this->config['base_url'] . $this->inferEndpointPath($functionName),
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer your_token_here'
            )
        );
        
        if ($method !== 'GET') {
            $example['body'] = $this->generateRequestBody($functionName);
        }
        
        return $example;
    }
    
    /**
     * 生成请求体示例
     */
    private function generateRequestBody($functionName) {
        $body = array();
        
        if (strpos($functionName, 'create') !== false || strpos($functionName, 'update') !== false) {
            if (strpos($functionName, 'User') !== false) {
                $body = array(
                    'username' => 'example_user',
                    'email' => '<EMAIL>',
                    'password' => 'secure_password',
                    'role' => 'user'
                );
            } elseif (strpos($functionName, 'Product') !== false) {
                $body = array(
                    'name' => '示例产品',
                    'description' => '产品描述',
                    'price' => 99.99,
                    'status' => 'active'
                );
            }
        }
        
        return $body;
    }
    
    /**
     * 生成响应示例
     */
    private function generateResponseExample($functionName) {
        $example = array(
            'success' => true,
            'message' => '操作成功',
            'timestamp' => time()
        );
        
        if (strpos($functionName, 'get') !== false || strpos($functionName, 'Get') !== false) {
            $example['data'] = $this->generateDataExample($functionName);
            $example['pagination'] = array(
                'current_page' => 1,
                'total_pages' => 10,
                'total_items' => 100,
                'page_size' => 10
            );
        } elseif (strpos($functionName, 'create') !== false) {
            $example['data'] = array(
                'id' => 123,
                'created_at' => date('Y-m-d H:i:s')
            );
        }
        
        return $example;
    }
    
    /**
     * 生成数据示例
     */
    private function generateDataExample($functionName) {
        if (strpos($functionName, 'User') !== false) {
            return array(
                array(
                    'id' => 1,
                    'username' => 'admin',
                    'email' => '<EMAIL>',
                    'role' => 'admin',
                    'status' => 'active',
                    'created_at' => '2024-01-01 00:00:00'
                )
            );
        } elseif (strpos($functionName, 'Product') !== false) {
            return array(
                array(
                    'id' => 1,
                    'name' => '示例产品',
                    'price' => 99.99,
                    'status' => 'active',
                    'created_at' => '2024-01-01 00:00:00'
                )
            );
        }
        
        return array();
    }
    
    /**
     * 映射PHP类型到JSON类型
     */
    private function mapPhpTypeToJsonType($phpType) {
        $typeMap = array(
            'string' => 'string',
            'int' => 'integer',
            'integer' => 'integer',
            'float' => 'number',
            'double' => 'number',
            'bool' => 'boolean',
            'boolean' => 'boolean',
            'array' => 'array',
            'object' => 'object',
            'mixed' => 'object'
        );
        
        return isset($typeMap[$phpType]) ? $typeMap[$phpType] : 'string';
    }
    
    /**
     * 生成摘要
     */
    private function generateSummary($functionName) {
        $summaries = array(
            'handleLogin' => '用户登录',
            'handleUsers' => '用户管理',
            'handleGetUsers' => '获取用户列表',
            'handleCreateUser' => '创建用户',
            'handleUpdateUser' => '更新用户',
            'handleDeleteUser' => '删除用户',
            'handleProducts' => '产品管理',
            'handleGetProducts' => '获取产品列表',
            'handleCreateProduct' => '创建产品',
            'handleUpdateProduct' => '更新产品',
            'handleDeleteProduct' => '删除产品'
        );
        
        return isset($summaries[$functionName]) ? $summaries[$functionName] : $functionName;
    }
    
    /**
     * 生成描述
     */
    private function generateDescription($functionName) {
        return $this->generateSummary($functionName) . '的详细操作接口';
    }
    
    /**
     * 生成OpenAPI文档
     */
    public function generateOpenApiDoc() {
        $doc = array(
            'openapi' => '3.0.0',
            'info' => array(
                'title' => $this->config['title'],
                'version' => $this->config['version'],
                'description' => $this->config['description'],
                'contact' => $this->config['contact'],
                'license' => $this->config['license']
            ),
            'servers' => array(
                array(
                    'url' => $this->config['base_url'],
                    'description' => '生产环境'
                )
            ),
            'paths' => array(),
            'components' => array(
                'securitySchemes' => array(
                    'bearerAuth' => array(
                        'type' => 'http',
                        'scheme' => 'bearer',
                        'bearerFormat' => 'JWT'
                    )
                ),
                'schemas' => $this->schemas
            ),
            'tags' => $this->generateTags()
        );
        
        // 添加路径
        foreach ($this->endpoints as $endpoint) {
            $path = $endpoint['path'];
            $method = strtolower($endpoint['method']);
            
            if (!isset($doc['paths'][$path])) {
                $doc['paths'][$path] = array();
            }
            
            $doc['paths'][$path][$method] = array(
                'summary' => $endpoint['summary'],
                'description' => $endpoint['description'],
                'tags' => $endpoint['tags'],
                'parameters' => $endpoint['parameters'],
                'responses' => $endpoint['responses'],
                'security' => $endpoint['security']
            );
            
            if (!empty($endpoint['examples'])) {
                $doc['paths'][$path][$method]['examples'] = $endpoint['examples'];
            }
        }
        
        return $doc;
    }
    
    /**
     * 生成标签
     */
    private function generateTags() {
        $allTags = array();
        
        foreach ($this->endpoints as $endpoint) {
            foreach ($endpoint['tags'] as $tag) {
                if (!in_array($tag, $allTags)) {
                    $allTags[] = $tag;
                }
            }
        }
        
        $tags = array();
        foreach ($allTags as $tag) {
            $tags[] = array(
                'name' => $tag,
                'description' => $tag . '相关接口'
            );
        }
        
        return $tags;
    }
    
    /**
     * 生成HTML文档
     */
    public function generateHtmlDoc() {
        $openApiDoc = $this->generateOpenApiDoc();
        
        $html = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . $this->config['title'] . '</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        body { margin: 0; padding: 0; }
        .swagger-ui .topbar { display: none; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script>
        SwaggerUIBundle({
            url: "api-doc.json",
            dom_id: "#swagger-ui",
            deepLinking: true,
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIBundle.presets.standalone
            ],
            plugins: [
                SwaggerUIBundle.plugins.DownloadUrl
            ],
            layout: "StandaloneLayout",
            spec: ' . json_encode($openApiDoc, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . '
        });
    </script>
</body>
</html>';
        
        return $html;
    }
    
    /**
     * 保存文档
     */
    public function saveDocumentation($outputDir) {
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }
        
        // 保存OpenAPI JSON
        $openApiDoc = $this->generateOpenApiDoc();
        file_put_contents($outputDir . '/api-doc.json', json_encode($openApiDoc, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        
        // 保存HTML文档
        $htmlDoc = $this->generateHtmlDoc();
        file_put_contents($outputDir . '/index.html', $htmlDoc);
        
        // 保存Markdown文档
        $markdownDoc = $this->generateMarkdownDoc();
        file_put_contents($outputDir . '/api-doc.md', $markdownDoc);
        
        return array(
            'json' => $outputDir . '/api-doc.json',
            'html' => $outputDir . '/index.html',
            'markdown' => $outputDir . '/api-doc.md'
        );
    }
    
    /**
     * 生成Markdown文档
     */
    private function generateMarkdownDoc() {
        $md = "# " . $this->config['title'] . "\n\n";
        $md .= $this->config['description'] . "\n\n";
        $md .= "**版本**: " . $this->config['version'] . "\n";
        $md .= "**基础URL**: " . $this->config['base_url'] . "\n\n";
        
        // 按标签分组
        $endpointsByTag = array();
        foreach ($this->endpoints as $endpoint) {
            $tag = isset($endpoint['tags'][0]) ? $endpoint['tags'][0] : '其他';
            if (!isset($endpointsByTag[$tag])) {
                $endpointsByTag[$tag] = array();
            }
            $endpointsByTag[$tag][] = $endpoint;
        }
        
        foreach ($endpointsByTag as $tag => $endpoints) {
            $md .= "## " . $tag . "\n\n";
            
            foreach ($endpoints as $endpoint) {
                $md .= "### " . $endpoint['summary'] . "\n\n";
                $md .= "**方法**: `" . $endpoint['method'] . "`\n";
                $md .= "**路径**: `" . $endpoint['path'] . "`\n\n";
                $md .= $endpoint['description'] . "\n\n";
                
                // 参数
                if (!empty($endpoint['parameters'])) {
                    $md .= "#### 参数\n\n";
                    $md .= "| 名称 | 位置 | 类型 | 必需 | 描述 |\n";
                    $md .= "|------|------|------|------|------|\n";
                    
                    foreach ($endpoint['parameters'] as $param) {
                        $md .= "| " . $param['name'] . " | " . $param['in'] . " | " . $param['type'] . " | " . ($param['required'] ? '是' : '否') . " | " . $param['description'] . " |\n";
                    }
                    $md .= "\n";
                }
                
                // 响应
                $md .= "#### 响应\n\n";
                foreach ($endpoint['responses'] as $code => $response) {
                    $md .= "**" . $code . "**: " . $response['description'] . "\n\n";
                }
                
                // 示例
                if (!empty($endpoint['examples'])) {
                    $md .= "#### 示例\n\n";
                    $md .= "```json\n";
                    $md .= json_encode($endpoint['examples']['response'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                    $md .= "\n```\n\n";
                }
                
                $md .= "---\n\n";
            }
        }
        
        return $md;
    }
    
    /**
     * 获取端点统计
     */
    public function getStats() {
        $stats = array(
            'total_endpoints' => count($this->endpoints),
            'methods' => array(),
            'tags' => array()
        );
        
        foreach ($this->endpoints as $endpoint) {
            // 统计方法
            $method = $endpoint['method'];
            if (!isset($stats['methods'][$method])) {
                $stats['methods'][$method] = 0;
            }
            $stats['methods'][$method]++;
            
            // 统计标签
            foreach ($endpoint['tags'] as $tag) {
                if (!isset($stats['tags'][$tag])) {
                    $stats['tags'][$tag] = 0;
                }
                $stats['tags'][$tag]++;
            }
        }
        
        return $stats;
    }
}
?> 