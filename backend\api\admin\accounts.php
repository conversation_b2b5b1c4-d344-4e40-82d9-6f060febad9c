<?php
/**
 * 码商管理员 - 支付宝账户管理API
 * 支持查询、更新、审核账户信息
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';

try {
    $db = new Database();
    $auth = new Auth();
    
    // 验证用户认证
    $token = $auth->getAuthToken();
    $currentUser = $auth->getCurrentUser($token);
    
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(array(
            'success' => false,
            'message' => '用户未登录或token已过期'
        ));
        exit;
    }
    
    // 验证用户权限 - 只有码商和管理员可以管理支付宝账户
    if (!in_array($currentUser['user_type'], ['admin', 'provider'])) {
        http_response_code(403);
        echo json_encode(array(
            'success' => false,
            'message' => '无权限访问支付宝账户管理'
        ));
        exit;
    }
    
    // 获取请求方法和输入数据
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 确定操作的码商ID
    $providerId = null;
    if ($currentUser['user_type'] === 'admin') {
        // 管理员可以指定码商ID，如果没指定则报错
        $providerId = isset($_GET['provider_id']) ? intval($_GET['provider_id']) : 
                     (isset($input['provider_id']) ? intval($input['provider_id']) : 0);
        
        if ($providerId <= 0) {
            http_response_code(400);
            echo json_encode(array(
                'success' => false,
                'message' => '管理员操作需要指定码商ID参数'
            ));
            exit;
        }
        
        // 验证码商存在
        $provider = $db->fetch(
            "SELECT id FROM payment_providers WHERE id = ?",
            array($providerId)
        );
        
        if (!$provider) {
            http_response_code(404);
            echo json_encode(array(
                'success' => false,
                'message' => '指定的码商不存在'
            ));
            exit;
        }
        
    } else if ($currentUser['user_type'] === 'provider') {
        // 码商只能操作自己的数据
        $providerId = $currentUser['profile_id'];
        
        if (!$providerId) {
            http_response_code(500);
            echo json_encode(array(
                'success' => false,
                'message' => '用户数据异常，无法获取码商信息'
            ));
            exit;
        }
    }
    
    switch ($method) {
        case 'GET':
            handleGetAccounts($db, $providerId, $currentUser);
            break;
            
        case 'POST':
            handleCreateOrUpdateAccount($db, $input, $providerId, $currentUser);
            break;
            
        case 'PUT':
            handleUpdateAccount($db, $input, $providerId, $currentUser);
            break;
            
        case 'DELETE':
            handleDeleteAccount($db, $input, $providerId, $currentUser);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(array(
                'success' => false,
                'message' => '不支持的请求方法'
            ));
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(array(
        'success' => false,
        'message' => '服务器错误: ' . $e->getMessage()
    ));
}

/**
 * 获取账户列表 - 添加用户权限验证
 */
function handleGetAccounts($db, $providerId, $currentUser) {
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(10, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    // 构建查询条件 - 强制按码商ID过滤
    $whereConditions = array("a.provider_id = ?");
    $params = array($providerId);
    
    if (!empty($status) && in_array($status, array('pending', 'approved', 'rejected', 'disabled'))) {
        $whereConditions[] = "a.status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(a.account_name LIKE ? OR a.account_number LIKE ? OR a.phone LIKE ? OR a.email LIKE ?)";
        $searchParam = "%$search%";
        $params[] = $searchParam;
        $params[] = $searchParam;
        $params[] = $searchParam;
        $params[] = $searchParam;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as total FROM alipay_accounts a WHERE $whereClause",
        $params
    );
    $total = $totalResult['total'];
    
    // 获取账户列表
    $accounts = $db->fetchAll(
        "SELECT 
            a.id,
            a.account_name,
            a.account_number,
            a.real_name,
            a.phone,
            a.email,
            a.status,
            a.daily_limit,
            a.created_at,
            a.updated_at,
            a.approved_at,
            d.device_name,
            d.device_id as device_string_id,
            p.company_name as provider_name
        FROM alipay_accounts a
        LEFT JOIN devices d ON a.device_id = d.id
        LEFT JOIN payment_providers p ON a.provider_id = p.id
        WHERE $whereClause
        ORDER BY a.created_at DESC
        LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 记录访问日志
    if (function_exists('logUserAction')) {
        logUserAction($currentUser['id'], 'view_alipay_accounts', 'alipay_accounts', $providerId, 
                    "查看码商 {$providerId} 的支付宝账户列表");
    }
    
    echo json_encode(array(
        'code' => 200,
        'message' => '账户列表获取成功',
        'data' => array(
            'accounts' => $accounts,
            'pagination' => array(
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ),
            'user_info' => array(
                'user_type' => $currentUser['user_type'],
                'provider_id' => $providerId
            )
        )
    ));
}

/**
 * 创建或更新账户信息 - 添加权限控制和服务器时间戳
 */
function handleCreateOrUpdateAccount($db, $input, $providerId, $currentUser) {
    // 验证必需参数
    $accountId = isset($input['id']) ? intval($input['id']) : 0;
    $accountName = isset($input['account_name']) ? trim($input['account_name']) : '';
    $accountNumber = isset($input['account_number']) ? trim($input['account_number']) : '';
    
    if (empty($accountName) || empty($accountNumber)) {
        http_response_code(400);
        echo json_encode(array(
            'success' => false,
            'message' => '账户名称和账号不能为空'
        ));
        return;
    }
    
    // 可选参数
    $realName = isset($input['real_name']) ? trim($input['real_name']) : null;
    $phone = isset($input['phone']) ? trim($input['phone']) : null;
    $email = isset($input['email']) ? trim($input['email']) : null;
    $loginPassword = isset($input['login_password']) ? trim($input['login_password']) : null;
    $paymentPassword = isset($input['payment_password']) ? trim($input['payment_password']) : null;
    $dailyLimit = isset($input['daily_limit']) ? floatval($input['daily_limit']) : 50000.00;
    $status = isset($input['status']) ? $input['status'] : 'pending';
    
    // 验证状态值
    if (!in_array($status, array('pending', 'approved', 'rejected', 'disabled'))) {
        $status = 'pending';
    }
    
    // 密码加密处理
    $encryptedLoginPassword = null;
    $encryptedPaymentPassword = null;
    
    if (!empty($loginPassword)) {
        $encryptedLoginPassword = password_hash($loginPassword, PASSWORD_DEFAULT);
    }
    
    if (!empty($paymentPassword)) {
        $encryptedPaymentPassword = password_hash($paymentPassword, PASSWORD_DEFAULT);
    }
    
    if ($accountId > 0) {
        // 更新现有账户 - 强制验证账户归属
        $existingAccount = $db->fetch(
            "SELECT id, status FROM alipay_accounts WHERE id = ? AND provider_id = ?",
            array($accountId, $providerId)
        );
        
        if (!$existingAccount) {
            http_response_code(404);
            echo json_encode(array(
                'success' => false,
                'message' => '账户不存在或无权限访问'
            ));
            return;
        }
        
        // 构建更新字段
        $updateFields = array();
        $updateValues = array();
        
        $updateFields[] = "account_name = ?";
        $updateValues[] = $accountName;
        
        $updateFields[] = "account_number = ?";
        $updateValues[] = $accountNumber;
        
        if ($realName !== null) {
            $updateFields[] = "real_name = ?";
            $updateValues[] = $realName;
        }
        
        if ($phone !== null) {
            $updateFields[] = "phone = ?";
            $updateValues[] = $phone;
        }
        
        if ($email !== null) {
            $updateFields[] = "email = ?";
            $updateValues[] = $email;
        }
        
        if ($encryptedLoginPassword !== null) {
            $updateFields[] = "login_password = ?";
            $updateValues[] = $encryptedLoginPassword;
        }
        
        if ($encryptedPaymentPassword !== null) {
            $updateFields[] = "payment_password = ?";
            $updateValues[] = $encryptedPaymentPassword;
        }
        
        $updateFields[] = "daily_limit = ?";
        $updateValues[] = $dailyLimit;
        
        $updateFields[] = "status = ?";
        $updateValues[] = $status;
        
        // 使用服务器时间戳
        $updateFields[] = "updated_at = NOW()";
        
        if ($status === 'approved') {
            $updateFields[] = "approved_by = ?";
            $updateValues[] = $currentUser['id']; // 使用当前用户ID
            
            $updateFields[] = "approved_at = NOW()"; // 使用服务器时间
        }
        
        $updateValues[] = $accountId;
        $updateValues[] = $providerId; // 双重验证
        
        $db->execute(
            "UPDATE alipay_accounts SET " . implode(', ', $updateFields) . " WHERE id = ? AND provider_id = ?",
            $updateValues
        );
        
        // 记录操作日志
        if (function_exists('logUserAction')) {
            logUserAction($currentUser['id'], 'update_alipay_account', 'alipay_accounts', $accountId, 
                        "更新支付宝账户: {$accountName} ({$accountNumber})");
        }
        
        echo json_encode(array(
            'code' => 200,
            'message' => '账户信息更新成功',
            'data' => array('id' => $accountId)
        ));
        
    } else {
        // 创建新账户 - 强制设置provider_id
        
        // 检查账号是否已存在
        $existingAccount = $db->fetch(
            "SELECT id FROM alipay_accounts WHERE account_number = ? AND provider_id = ?",
            array($accountNumber, $providerId)
        );
        
        if ($existingAccount) {
            http_response_code(409);
            echo json_encode(array(
                'success' => false,
                'message' => '该支付宝账号已存在'
            ));
            return;
        }
        
        $insertFields = array(
            'provider_id', 'account_name', 'account_number', 'daily_limit', 'status', 'created_at'
        );
        $insertValues = array($providerId, $accountName, $accountNumber, $dailyLimit, $status, 'NOW()');
        $insertPlaceholders = array('?', '?', '?', '?', '?', 'NOW()');
        
        if ($realName !== null) {
            $insertFields[] = 'real_name';
            $insertValues[] = $realName;
            $insertPlaceholders[] = '?';
        }
        
        if ($phone !== null) {
            $insertFields[] = 'phone';
            $insertValues[] = $phone;
            $insertPlaceholders[] = '?';
        }
        
        if ($email !== null) {
            $insertFields[] = 'email';
            $insertValues[] = $email;
            $insertPlaceholders[] = '?';
        }
        
        if ($encryptedLoginPassword !== null) {
            $insertFields[] = 'login_password';
            $insertValues[] = $encryptedLoginPassword;
            $insertPlaceholders[] = '?';
        }
        
        if ($encryptedPaymentPassword !== null) {
            $insertFields[] = 'payment_password';
            $insertValues[] = $encryptedPaymentPassword;
            $insertPlaceholders[] = '?';
        }
        
        if ($status === 'approved') {
            $insertFields[] = 'approved_by';
            $insertFields[] = 'approved_at';
            $insertValues[] = $currentUser['id'];
            $insertPlaceholders[] = '?';
            $insertPlaceholders[] = 'NOW()';
        }
        
        // 准备SQL语句，将NOW()函数单独处理
        $finalValues = array();
        $finalPlaceholders = array();
        
        for ($i = 0; $i < count($insertPlaceholders); $i++) {
            if ($insertPlaceholders[$i] === 'NOW()') {
                $finalPlaceholders[] = 'NOW()';
            } else {
                $finalPlaceholders[] = '?';
                $finalValues[] = $insertValues[$i];
            }
        }
        
        $sql = "INSERT INTO alipay_accounts (" . implode(', ', $insertFields) . ") VALUES (" . implode(', ', $finalPlaceholders) . ")";
        
        $db->execute($sql, $finalValues);
        $newAccountId = $db->lastInsertId();
        
        // 记录操作日志
        if (function_exists('logUserAction')) {
            logUserAction($currentUser['id'], 'create_alipay_account', 'alipay_accounts', $newAccountId, 
                        "创建支付宝账户: {$accountName} ({$accountNumber})");
        }
        
        echo json_encode(array(
            'code' => 200,
            'message' => '账户创建成功',
            'data' => array('id' => $newAccountId)
        ));
    }
}

/**
 * 更新账户状态（审核）
 */
function handleUpdateAccount($db, $input, $providerId, $currentUser) {
    $accountId = isset($input['id']) ? intval($input['id']) : 0;
    $action = isset($input['action']) ? $input['action'] : '';
    
    if ($accountId <= 0) {
        http_response_code(400);
        echo json_encode(array(
            'success' => false,
            'message' => '缺少账户ID'
        ));
        return;
    }
    
    // 验证账户是否存在且属于该码商
    $account = $db->fetch(
        "SELECT id, status FROM alipay_accounts WHERE id = ? AND provider_id = ?",
        array($accountId, $providerId)
    );
    
    if (!$account) {
        http_response_code(404);
        echo json_encode(array(
            'success' => false,
            'message' => '账户不存在或无权限访问'
        ));
        return;
    }
    
    switch ($action) {
        case 'approve':
            $db->execute(
                "UPDATE alipay_accounts SET 
                    status = 'approved',
                    approved_by = ?,
                    approved_at = NOW(),
                    updated_at = NOW()
                WHERE id = ?",
                array($currentUser['id'], $accountId)
            );
            $message = '账户审核通过';
            break;
            
        case 'reject':
            $db->execute(
                "UPDATE alipay_accounts SET 
                    status = 'rejected',
                    updated_at = NOW()
                WHERE id = ?",
                array($accountId)
            );
            $message = '账户审核拒绝';
            break;
            
        case 'disable':
            $db->execute(
                "UPDATE alipay_accounts SET 
                    status = 'disabled',
                    updated_at = NOW()
                WHERE id = ?",
                array($accountId)
            );
            $message = '账户已禁用';
            break;
            
        case 'enable':
            $db->execute(
                "UPDATE alipay_accounts SET 
                    status = 'approved',
                    updated_at = NOW()
                WHERE id = ?",
                array($accountId)
            );
            $message = '账户已启用';
            break;
            
        default:
            http_response_code(400);
            echo json_encode(array(
                'success' => false,
                'message' => '不支持的操作'
            ));
            return;
    }
    
    // 记录操作日志
    if (function_exists('logUserAction')) {
        logUserAction($currentUser['id'], 'update_alipay_account', 'alipay_accounts', $accountId, 
                    "更新支付宝账户状态: {$account['status']} -> {$status}");
    }
    
    echo json_encode(array(
        'code' => 200,
        'message' => $message
    ));
}

/**
 * 删除账户
 */
function handleDeleteAccount($db, $input, $providerId, $currentUser) {
    $accountId = isset($input['id']) ? intval($input['id']) : 0;
    
    if ($accountId <= 0) {
        http_response_code(400);
        echo json_encode(array(
            'success' => false,
            'message' => '缺少账户ID'
        ));
        return;
    }
    
    // 验证账户是否存在且属于该码商
    $account = $db->fetch(
        "SELECT id FROM alipay_accounts WHERE id = ? AND provider_id = ?",
        array($accountId, $providerId)
    );
    
    if (!$account) {
        http_response_code(404);
        echo json_encode(array(
            'success' => false,
            'message' => '账户不存在或无权限访问'
        ));
        return;
    }
    
    $db->execute(
        "DELETE FROM alipay_accounts WHERE id = ?",
        array($accountId)
    );
    
    // 记录操作日志
    if (function_exists('logUserAction')) {
        logUserAction($currentUser['id'], 'delete_alipay_account', 'alipay_accounts', $accountId, 
                    "删除支付宝账户: {$account['account_name']} ({$account['account_number']})");
    }
    
    echo json_encode(array(
        'code' => 200,
        'message' => '账户删除成功'
    ));
}
?> 