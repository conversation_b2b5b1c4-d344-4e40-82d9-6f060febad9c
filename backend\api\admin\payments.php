<?php
/**
 * 支付管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'payment_requests':
        case 'get_payment_requests':
            handlePaymentRequests($auth);
            break;
        case 'alipay_accounts':
        case 'get_accounts':
            handleAlipayAccounts($auth);
            break;
        default:
            handlePaymentRequests($auth);
    }
} catch (Exception $e) {
    error_log("Payments API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 处理支付请求
function handlePaymentRequests($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('payment.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;
        
        $status = isset($_GET['status']) ? $_GET['status'] : '';
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        
        // 构建查询条件
        $whereConditions = array();
        $params = array();
        
        // 数据隔离：非管理员只能查看自己相关的支付请求
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = "t.merchant_id = ?";
            $params[] = $currentUser['profile_id'];
        } elseif ($currentUser['user_type'] === 'provider') {
            $whereConditions[] = "t.provider_id = ?";
            $params[] = $currentUser['profile_id'];
        }
        
        if ($status && in_array($status, array('pending', 'paid', 'failed', 'cancelled'))) {
            $whereConditions[] = "t.status = ?";
            $params[] = $status;
        }
        
        if ($search) {
            $whereConditions[] = "(t.order_id LIKE ? OR t.transaction_id LIKE ? OR m.company_name LIKE ?)";
            $searchTerm = '%' . $search . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // 获取支付请求列表
        $requests = $db->fetchAll(
            "SELECT 
                t.*,
                m.company_name as merchant_name,
                p.company_name as provider_name
             FROM transactions t
             LEFT JOIN merchants m ON t.merchant_id = m.id
             LEFT JOIN payment_providers p ON t.provider_id = p.id
             $whereClause
             ORDER BY t.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, array($limit, $offset))
        );
        
        // 获取总数
        $totalResult = $db->fetch("SELECT COUNT(*) as count FROM transactions t LEFT JOIN merchants m ON t.merchant_id = m.id LEFT JOIN payment_providers p ON t.provider_id = p.id $whereClause", $params);
        $total = $totalResult['count'] ?: 0;
        
        // 获取统计信息
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_requests,
                COUNT(CASE WHEN t.status = 'paid' THEN 1 END) as paid_requests,
                COUNT(CASE WHEN t.status = 'failed' THEN 1 END) as failed_requests,
                SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as total_amount
             FROM transactions t
             LEFT JOIN merchants m ON t.merchant_id = m.id
             LEFT JOIN payment_providers p ON t.provider_id = p.id
             $whereClause",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_payment_requests', 'payment', 0, '查看支付请求列表');
        
        $response = array(
            'requests' => $requests ?: array(),
            'stats' => $stats ?: array(),
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        );
        
        $auth->sendSuccess($response, '支付请求列表获取成功');
        
    } catch (Exception $e) {
        error_log("获取支付请求失败: " . $e->getMessage());
        $auth->sendError('获取支付请求失败: ' . $e->getMessage(), 500);
    }
}

// 处理支付宝账户
function handleAlipayAccounts($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('alipay.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;
        
        $status = isset($_GET['status']) ? $_GET['status'] : '';
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        
        // 构建查询条件
        $whereConditions = array();
        $params = array();
        
        // 数据隔离：非管理员只能查看自己相关的账户
        if ($currentUser['user_type'] === 'provider') {
            $whereConditions[] = "a.provider_id = ?";
            $params[] = $currentUser['profile_id'];
        }
        
        if ($status && in_array($status, array('active', 'inactive', 'suspended'))) {
            $whereConditions[] = "a.status = ?";
            $params[] = $status;
        }
        
        if ($search) {
            $whereConditions[] = "(a.account_name LIKE ? OR a.real_name LIKE ? OR p.company_name LIKE ?)";
            $searchTerm = '%' . $search . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // 获取支付宝账户列表
        $accounts = $db->fetchAll(
            "SELECT 
                a.*,
                p.company_name as provider_name,
                d.device_name
             FROM alipay_accounts a
             LEFT JOIN payment_providers p ON a.provider_id = p.id
             LEFT JOIN devices d ON a.device_id = d.id
             $whereClause
             ORDER BY a.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, array($limit, $offset))
        );
        
        // 脱敏处理
        if ($accounts) {
            foreach ($accounts as &$account) {
                if (isset($account['account_number']) && $account['account_number']) {
                    $account['account_number_masked'] = substr($account['account_number'], 0, 3) . '****' . substr($account['account_number'], -4);
                    unset($account['account_number']); // 移除原始账号
                }
            }
        }
        
        // 获取总数
        $totalResult = $db->fetch("SELECT COUNT(*) as count FROM alipay_accounts a LEFT JOIN payment_providers p ON a.provider_id = p.id $whereClause", $params);
        $total = $totalResult['count'] ?: 0;
        
        // 获取统计信息
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_accounts,
                COUNT(CASE WHEN a.status = 'active' THEN 1 END) as active_accounts,
                COUNT(CASE WHEN a.status = 'inactive' THEN 1 END) as inactive_accounts,
                COUNT(CASE WHEN a.status = 'suspended' THEN 1 END) as suspended_accounts,
                SUM(CASE WHEN a.status = 'active' THEN a.balance ELSE 0 END) as total_balance
             FROM alipay_accounts a
             LEFT JOIN payment_providers p ON a.provider_id = p.id
             $whereClause",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_alipay_accounts', 'alipay', 0, '查看支付宝账户列表');
        
        $response = array(
            'accounts' => $accounts ?: array(),
            'stats' => $stats ?: array(),
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        );
        
        $auth->sendSuccess($response, '支付宝账户列表获取成功');
        
    } catch (Exception $e) {
        error_log("获取支付宝账户失败: " . $e->getMessage());
        $auth->sendError('获取支付宝账户失败: ' . $e->getMessage(), 500);
    }
}
?> 