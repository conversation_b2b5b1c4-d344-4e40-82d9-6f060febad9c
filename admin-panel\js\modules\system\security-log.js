/**
 * 安全日志管理模块
 * 负责安全事件监控、日志管理和风险分析
 */
class SecurityLogManager {
    constructor() {
        this.apiUrl = '/api/admin.php?do=security_logs';
        this.currentPage = 1;
        this.pageSize = 20;
        this.currentFilters = {};
        this.refreshInterval = null;
        this.isRealTimeMode = false;
        this.auth = new AuthManager();
    }

    // 初始化
    initialize() {
        this.loadSecurityStats();
        this.loadSecurityLogs();
        this.loadRecentEvents();
        this.initializeEventListeners();
        this.startRealTimeUpdates();
    }

    // 初始化事件监听器
    initializeEventListeners() {
        // 搜索功能
        document.getElementById('logSearchBtn')?.addEventListener('click', () => {
            this.searchLogs();
        });

        document.getElementById('logSearchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchLogs();
            }
        });

        // 筛选器
        ['logEventTypeFilter', 'logRiskLevelFilter', 'logTimeRangeFilter', 'logUserTypeFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });

        // 实时更新控制
        document.getElementById('realTimeToggle')?.addEventListener('change', (e) => {
            this.toggleRealTimeMode(e.target.checked);
        });

        // 导出功能
        document.getElementById('exportLogsBtn')?.addEventListener('click', () => {
            this.exportSecurityLogs();
        });

        // 刷新按钮
        document.getElementById('refreshLogsBtn')?.addEventListener('click', () => {
            this.refreshData();
        });

        // 清空筛选
        document.getElementById('clearFiltersBtn')?.addEventListener('click', () => {
            this.clearFilters();
        });
    }

    // 加载安全统计
    async loadSecurityStats() {
        try {
            const response = await fetch(`${this.apiUrl}&action=stats`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });
            const result = await response.json();
            
            if (result.code === 200) {
                this.updateStatsDisplay(result.data);
            }
        } catch (error) {
            console.error('加载安全统计失败:', error);
        }
    }

    // 更新统计显示
    updateStatsDisplay(stats) {
        const elements = {
            todayEvents: stats.today_events || 0,
            riskEvents: stats.risk_events || 0,
            suspiciousIPs: stats.suspicious_ips || 0,
            alertsCount: stats.alerts_count || 0,
            riskScore: stats.risk_score || 0
        };

        Object.keys(elements).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (key === 'riskScore') {
                    element.textContent = `${elements[key]}/100`;
                    // 根据风险分数设置颜色
                    element.className = this.getRiskScoreClass(elements[key]);
                } else {
                    element.textContent = this.formatNumber(elements[key]);
                }
            }
        });

        // 更新风险等级分布图表
        if (stats.risk_distribution) {
            this.updateRiskDistributionChart(stats.risk_distribution);
        }
    }

    // 加载安全日志
    async loadSecurityLogs() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                ...this.currentFilters
            });

            const response = await fetch(`${this.apiUrl}&${params}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });
            const result = await response.json();
            
            if (result.code === 200) {
                this.renderSecurityLogsTable(result.data.logs);
                this.renderPagination(result.data.pagination);
                this.updateLogCount(result.data.pagination.total_records);
            } else {
                this.showError('加载安全日志失败: ' + result.message);
            }
        } catch (error) {
            console.error('加载安全日志失败:', error);
            this.showError('网络连接失败，请重试');
        }
    }

    // 渲染安全日志表格
    renderSecurityLogsTable(logs) {
        const container = document.getElementById('securityLogsTableContainer');
        if (!container) return;

        if (!logs || logs.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-shield-exclamation display-1 text-muted"></i>
                    <div class="mt-3">暂无安全日志记录</div>
                </div>
            `;
            return;
        }

        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>时间</th>
                            <th>事件类型</th>
                            <th>用户</th>
                            <th>风险等级</th>
                            <th>IP地址</th>
                            <th>描述</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${logs.map(log => `
                            <tr class="${this.getLogRowClass(log.risk_level)}">
                                <td>
                                    <small class="text-nowrap">${this.formatDateTime(log.created_at)}</small>
                                </td>
                                <td>
                                    <span class="badge ${this.getEventTypeBadgeClass(log.event_type)}">
                                        ${this.getEventTypeText(log.event_type)}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        ${log.username ? `
                                            <div class="avatar-xs bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                ${log.username.charAt(0).toUpperCase()}
                                            </div>
                                            <div>
                                                <div class="fw-bold">${log.username}</div>
                                                <small class="text-muted">${this.getUserTypeText(log.user_type)}</small>
                                            </div>
                                        ` : '<span class="text-muted">系统</span>'}
                                    </div>
                                </td>
                                <td>
                                    <span class="badge ${this.getRiskLevelBadgeClass(log.risk_level)}">
                                        ${this.getRiskLevelText(log.risk_level)}
                                    </span>
                                </td>
                                <td>
                                    <code class="small">${log.ip_address || '-'}</code>
                                    ${log.location ? `<br><small class="text-muted">${log.location}</small>` : ''}
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="${log.description || ''}">
                                        ${log.description || '-'}
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" onclick="window.securityLogManager.viewLogDetail('${log.id}')" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        ${log.risk_level === 'high' || log.risk_level === 'critical' ? `
                                            <button class="btn btn-outline-warning" onclick="window.securityLogManager.markAsHandled('${log.id}')" title="标记已处理">
                                                <i class="bi bi-check"></i>
                                            </button>
                                        ` : ''}
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = tableHtml;
    }

    // 渲染分页
    renderPagination(pagination) {
        const container = document.getElementById('securityLogsPagination');
        if (!container || pagination.total_pages <= 1) {
            if (container) container.style.display = 'none';
            return;
        }

        container.style.display = 'block';
        let paginationHtml = '<nav><ul class="pagination justify-content-center">';

        // 上一页
        if (pagination.current_page > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.securityLogManager.goToPage(${pagination.current_page - 1})">上一页</a>
                </li>
            `;
        }

        // 页码
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="window.securityLogManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (pagination.current_page < pagination.total_pages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.securityLogManager.goToPage(${pagination.current_page + 1})">下一页</a>
                </li>
            `;
        }

        paginationHtml += '</ul></nav>';
        container.innerHTML = paginationHtml;
    }

    // 加载最近事件
    async loadRecentEvents() {
        try {
            const response = await fetch(`${this.apiUrl}&action=recent_events&limit=10`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });
            const result = await response.json();
            
            if (result.code === 200) {
                this.renderRecentEvents(result.data);
            }
        } catch (error) {
            console.error('加载最近事件失败:', error);
        }
    }

    // 渲染最近事件
    renderRecentEvents(events) {
        const container = document.getElementById('recentEventsContainer');
        if (!container) return;

        if (!events || events.length === 0) {
            container.innerHTML = `
                <div class="text-center py-3">
                    <i class="bi bi-clock text-muted"></i>
                    <div class="mt-2">暂无最近事件</div>
                </div>
            `;
            return;
        }

        const eventsHtml = events.map(event => `
            <div class="d-flex align-items-center py-2 border-bottom">
                <div class="flex-shrink-0 me-3">
                    <i class="bi bi-${this.getEventIcon(event.event_type)} text-${this.getEventColor(event.risk_level)}"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-bold small">${this.getEventTypeText(event.event_type)}</div>
                    <div class="text-muted small">${event.description || ''}</div>
                    <div class="text-muted small">${this.formatDateTime(event.created_at)}</div>
                </div>
                <div class="flex-shrink-0">
                    <span class="badge ${this.getRiskLevelBadgeClass(event.risk_level)} small">
                        ${this.getRiskLevelText(event.risk_level)}
                    </span>
                </div>
            </div>
        `).join('');

        container.innerHTML = eventsHtml;
    }

    // 搜索日志
    searchLogs() {
        const searchInput = document.getElementById('logSearchInput');
        if (searchInput) {
            this.currentFilters.search = searchInput.value.trim();
            this.currentPage = 1;
            this.loadSecurityLogs();
        }
    }

    // 应用筛选
    applyFilters() {
        const filters = {
            event_type: document.getElementById('logEventTypeFilter')?.value || '',
            risk_level: document.getElementById('logRiskLevelFilter')?.value || '',
            time_range: document.getElementById('logTimeRangeFilter')?.value || '',
            user_type: document.getElementById('logUserTypeFilter')?.value || ''
        };

        // 移除空值
        Object.keys(filters).forEach(key => {
            if (!filters[key]) {
                delete filters[key];
            }
        });

        this.currentFilters = { ...this.currentFilters, ...filters };
        this.currentPage = 1;
        this.loadSecurityLogs();
    }

    // 清空筛选
    clearFilters() {
        this.currentFilters = {};
        this.currentPage = 1;
        
        // 重置表单
        ['logEventTypeFilter', 'logRiskLevelFilter', 'logTimeRangeFilter', 'logUserTypeFilter', 'logSearchInput'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.value = '';
            }
        });

        this.loadSecurityLogs();
    }

    // 切换实时模式
    toggleRealTimeMode(enabled) {
        this.isRealTimeMode = enabled;
        if (enabled) {
            this.startRealTimeUpdates();
        } else {
            this.stopRealTimeUpdates();
        }
    }

    // 开始实时更新
    startRealTimeUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        // 每30秒自动刷新
        this.refreshInterval = setInterval(() => {
            if (this.isRealTimeMode) {
                this.loadSecurityStats();
                this.loadRecentEvents();
                // 如果在第一页，也刷新日志
                if (this.currentPage === 1) {
                    this.loadSecurityLogs();
                }
            }
        }, 30000);
    }

    // 停止实时更新
    stopRealTimeUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // 刷新数据
    refreshData() {
        this.loadSecurityStats();
        this.loadSecurityLogs();
        this.loadRecentEvents();
    }

    // 跳转到指定页
    goToPage(page) {
        this.currentPage = page;
        this.loadSecurityLogs();
    }

    // 更新日志计数
    updateLogCount(count) {
        const element = document.getElementById('logCount');
        if (element) {
            element.textContent = this.formatNumber(count);
        }
    }

    // 查看日志详情
    async viewLogDetail(logId) {
        try {
            const response = await fetch(`${this.apiUrl}&action=detail&id=${logId}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });
            const result = await response.json();
            
            if (result.code === 200) {
                this.showLogDetailModal(result.data);
            } else {
                this.showError('获取日志详情失败: ' + result.message);
            }
        } catch (error) {
            console.error('获取日志详情失败:', error);
            this.showError('网络连接失败');
        }
    }

    // 显示日志详情模态框
    showLogDetailModal(log) {
        const modalHtml = `
            <div class="modal fade" id="logDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">安全日志详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>事件时间:</strong>
                                    <div class="mb-3">${this.formatDateTime(log.created_at)}</div>
                                </div>
                                <div class="col-md-6">
                                    <strong>事件类型:</strong>
                                    <div class="mb-3">
                                        <span class="badge ${this.getEventTypeBadgeClass(log.event_type)}">
                                            ${this.getEventTypeText(log.event_type)}
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <strong>风险等级:</strong>
                                    <div class="mb-3">
                                        <span class="badge ${this.getRiskLevelBadgeClass(log.risk_level)}">
                                            ${this.getRiskLevelText(log.risk_level)}
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <strong>IP地址:</strong>
                                    <div class="mb-3"><code>${log.ip_address || '-'}</code></div>
                                </div>
                                <div class="col-md-6">
                                    <strong>用户:</strong>
                                    <div class="mb-3">${log.username || '系统'}</div>
                                </div>
                                <div class="col-md-6">
                                    <strong>用户类型:</strong>
                                    <div class="mb-3">${this.getUserTypeText(log.user_type)}</div>
                                </div>
                                <div class="col-12">
                                    <strong>事件描述:</strong>
                                    <div class="mb-3">${log.description || '-'}</div>
                                </div>
                                ${log.details ? `
                                <div class="col-12">
                                    <strong>详细信息:</strong>
                                    <div class="mb-3">
                                        <pre class="bg-light p-3 rounded"><code>${JSON.stringify(log.details, null, 2)}</code></pre>
                                    </div>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            ${log.risk_level === 'high' || log.risk_level === 'critical' ? `
                                <button type="button" class="btn btn-warning" onclick="window.securityLogManager.markAsHandled('${log.id}')">标记已处理</button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('logDetailModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
        modal.show();
    }

    // 标记为已处理
    async markAsHandled(logId) {
        try {
            const response = await fetch(`${this.apiUrl}&action=mark_handled`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({ log_id: logId })
            });

            const result = await response.json();
            
            if (result.code === 200) {
                this.showSuccess('已标记为处理');
                this.loadSecurityLogs();
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('logDetailModal'));
                if (modal) modal.hide();
            } else {
                this.showError('操作失败: ' + result.message);
            }
        } catch (error) {
            console.error('标记处理失败:', error);
            this.showError('网络连接失败');
        }
    }

    // 导出安全日志
    async exportSecurityLogs() {
        try {
            const params = new URLSearchParams(this.currentFilters);
            const response = await fetch(`${this.apiUrl}&action=export&${params}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `security_logs_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                this.showSuccess('安全日志导出成功');
            } else {
                throw new Error('导出失败');
            }
        } catch (error) {
            console.error('导出安全日志失败:', error);
            this.showError('导出失败: ' + error.message);
        }
    }

    // 更新风险分布图表
    updateRiskDistributionChart(distribution) {
        // 这里可以集成图表库来显示风险分布
        console.log('Risk distribution:', distribution);
    }

    // 工具方法
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    formatNumber(num) {
        if (num === null || num === undefined) return '0';
        return Number(num).toLocaleString();
    }

    getLogRowClass(riskLevel) {
        const classes = {
            'low': '',
            'medium': 'table-warning',
            'high': 'table-danger',
            'critical': 'table-danger'
        };
        return classes[riskLevel] || '';
    }

    getEventTypeBadgeClass(eventType) {
        const classes = {
            'login': 'bg-info',
            'logout': 'bg-secondary',
            'failed_login': 'bg-warning',
            'password_change': 'bg-primary',
            'permission_denied': 'bg-danger',
            'suspicious_activity': 'bg-warning',
            'data_breach': 'bg-danger',
            'system_error': 'bg-dark'
        };
        return classes[eventType] || 'bg-secondary';
    }

    getEventTypeText(eventType) {
        const texts = {
            'login': '登录',
            'logout': '登出',
            'failed_login': '登录失败',
            'password_change': '密码修改',
            'permission_denied': '权限拒绝',
            'suspicious_activity': '可疑活动',
            'data_breach': '数据泄露',
            'system_error': '系统错误'
        };
        return texts[eventType] || eventType;
    }

    getRiskLevelBadgeClass(riskLevel) {
        const classes = {
            'low': 'bg-success',
            'medium': 'bg-warning',
            'high': 'bg-danger',
            'critical': 'bg-dark'
        };
        return classes[riskLevel] || 'bg-secondary';
    }

    getRiskLevelText(riskLevel) {
        const texts = {
            'low': '低风险',
            'medium': '中风险',
            'high': '高风险',
            'critical': '严重'
        };
        return texts[riskLevel] || riskLevel;
    }

    getRiskScoreClass(score) {
        if (score >= 80) return 'text-danger fw-bold';
        if (score >= 60) return 'text-warning fw-bold';
        if (score >= 40) return 'text-info fw-bold';
        return 'text-success fw-bold';
    }

    getUserTypeText(userType) {
        const texts = {
            'admin': '管理员',
            'provider': '支付商',
            'merchant': '商户'
        };
        return texts[userType] || userType;
    }

    getEventIcon(eventType) {
        const icons = {
            'login': 'box-arrow-in-right',
            'logout': 'box-arrow-left',
            'failed_login': 'exclamation-triangle',
            'password_change': 'key',
            'permission_denied': 'shield-x',
            'suspicious_activity': 'eye',
            'data_breach': 'shield-exclamation',
            'system_error': 'bug'
        };
        return icons[eventType] || 'info-circle';
    }

    getEventColor(riskLevel) {
        const colors = {
            'low': 'success',
            'medium': 'warning',
            'high': 'danger',
            'critical': 'dark'
        };
        return colors[riskLevel] || 'secondary';
    }

    showSuccess(message) {
        // 简单的成功提示，以后可以改为更好的UI组件
        alert('✓ ' + message);
    }

    showError(message) {
        // 简单的错误提示，以后可以改为更好的UI组件
        alert('✗ ' + message);
    }

    // 销毁方法
    destroy() {
        this.stopRealTimeUpdates();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecurityLogManager;
} else if (typeof window !== 'undefined') {
    window.SecurityLogManager = SecurityLogManager;
}