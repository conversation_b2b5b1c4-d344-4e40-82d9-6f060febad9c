/**
 * 模块加载器 - admin.js拆分重构项目的核心组件
 * 负责动态加载模块、依赖管理、缓存机制和错误处理
 * 
 * <AUTHOR>
 * @version 1.0.1
 * @created 2024年12月
 * @updated 2024年12月22日 - 修复设备管理模块路径配置
 */

// 模块加载器类定义
class ModuleLoader {
    constructor() {
        this.loadedModules = new Set();
        this.moduleCache = new Map();
        this.loadingPromises = new Map();
        this.dependencies = new Map();
        this.baseUrl = '/js/modules/';
        this.retryCount = 3;
        this.retryDelay = 1000;
        
        // 模块配置
        this.moduleConfig = {
            // 核心模块
            'utils': {
                path: 'core/utils.js',
                dependencies: [],
                priority: 1
            },
            'auth': {
                path: 'core/auth.js',
                dependencies: ['utils'],
                priority: 2
            },
            'ui-manager': {
                path: 'core/ui-manager.js',
                dependencies: ['utils', 'auth'],
                priority: 3
            },
            'api-client': {
                path: 'core/api-client.js',
                dependencies: ['utils'],
                priority: 1
            },
            
            // 业务模块

            'provider-management': {
                path: 'business/provider-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: '码商管理模块',
                globalName: 'ProviderManagementModule'
            },
            'merchant-management': {
                path: 'business/merchant-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: '商户管理模块',
                globalName: 'MerchantManagementModule'
            },
            'transaction-management': {
                path: 'business/transaction-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: '交易管理模块',
                globalName: 'TransactionManager'
            },
            'finance-management': {
                path: 'business/finance-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: '财务管理模块',
                globalName: 'FinanceManager'
            },
            'product-management': {
                path: 'business/product-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 5,
                description: '产品管理模块',
                globalName: 'ProductManager'
            },
            'payment-management': {
                path: 'business/payment-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 5,
                description: '支付管理模块',
                globalName: 'PaymentManager'
            },
            'api-management': {
                path: 'business/api-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 5,
                description: 'API管理模块',
                globalName: 'APIManager'
            },
            
            // 安全模块 (阶段五)
            'security-management': {
                path: 'security/security-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 6,
                description: '安全管理模块',
                globalName: 'SecurityManagementModule'
            },
            
            // 商户模块 (阶段五)
            'merchant-tools': {
                path: 'merchant/merchant-tools.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 6,
                description: '商户工具模块',
                globalName: 'MerchantToolsModule'
            },
            
            // 系统模块 (阶段五)
            'system-monitoring': {
                path: 'system/system-monitoring.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 7,
                description: '系统监控模块',
                globalName: 'SystemMonitoringModule'
            },
            
            // 设备管理模块
            'device-management': {
                path: 'device/management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: '设备管理核心模块',
                globalName: 'DeviceManagementModule'
            },
            'group-management': {
                path: 'device/group-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: '小组管理模块',
                globalName: 'GroupManagementModule'
            },
            'checkin-monitor': {
                path: 'device/checkin-monitor.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: '签到监控模块',
                globalName: 'CheckinMonitorModule'
            },
            'device-module-manager': {
                path: 'device/module-manager.js',
                dependencies: ['device-management', 'group-management', 'checkin-monitor'],
                priority: 5,
                description: '设备模块统一管理器',
                globalName: 'DeviceModuleManagerModule'
            },
            
            // 新模块架构 - 方案5配置驱动
            'FinanceModule': {
                path: 'FinanceModule.js',
                dependencies: ['auth', 'utils', 'api-client', 'UIComponents', 'RoleDataAdapter'],
                priority: 3,
                description: '财务模块 - 统一财务管理',
                globalName: 'financeModule'
            },
            'OrderModule': {
                path: 'OrderModule.js',
                dependencies: ['auth', 'utils', 'api-client', 'UIComponents', 'RoleDataAdapter'],
                priority: 3,
                description: '订单模块 - 统一订单管理',
                globalName: 'orderModule'
            },
            'ApiModule': {
                path: 'ApiModule.js',
                dependencies: ['auth', 'utils', 'api-client', 'UIComponents', 'RoleDataAdapter'],
                priority: 3,
                description: 'API模块 - 统一API管理',
                globalName: 'apiModule'
            },
            
            // API子模块系统 - 拆分后的模块
            'api-module-loader': {
                path: 'api/index.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 3,
                description: 'API模块加载器',
                globalName: 'apiModuleLoader'
            },
            'ApiKeyManager': {
                path: 'api/ApiKeyManager.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: 'API密钥管理器',
                globalName: 'ApiKeyManager'
            },
            'IpWhitelistManager': {
                path: 'api/IpWhitelistManager.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: 'IP白名单管理器',
                globalName: 'IpWhitelistManager'
            },
            'ApiTester': {
                path: 'api/ApiTester.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: 'API测试工具',
                globalName: 'ApiTester'
            },
            'ApiStatsManager': {
                path: 'api/ApiStatsManager.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: 'API统计管理器',
                globalName: 'ApiStatsManager'
            },
            'ApiCodeGenerator': {
                path: 'api/ApiCodeGenerator.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: 'API代码生成器',
                globalName: 'ApiCodeGenerator'
            },
            'ApiSignatureTool': {
                path: 'api/ApiSignatureTool.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: 'API签名工具',
                globalName: 'ApiSignatureTool'
            },
            'DeviceModule': {
                path: 'DeviceModule.js',
                dependencies: ['auth', 'utils', 'api-client', 'UIComponents', 'RoleDataAdapter'],
                priority: 3,
                description: '设备模块 - 统一设备管理',
                globalName: 'deviceModule'
            },
            'ConfigModule': {
                path: 'ConfigModule.js',
                dependencies: ['auth', 'utils', 'api-client', 'UIComponents', 'RoleDataAdapter'],
                priority: 3,
                description: '配置模块 - 统一配置管理',
                globalName: 'configModule'
            },
            'SystemModule': {
                path: 'SystemModule.js',
                dependencies: ['auth', 'utils', 'api-client', 'UIComponents', 'RoleDataAdapter'],
                priority: 3,
                description: '系统模块 - 统一系统管理',
                globalName: 'systemModule'
            },
            'AlipayBillsModule': {
                path: 'AlipayBillsModule.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: '支付宝账单管理模块 - 码商专用',
                globalName: 'alipayBillsModule'
            },
            'ApiRouterModule': {
                path: 'ApiRouterModule.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: 'API路由管理模块 - 系统管理员专用',
                globalName: 'apiRouterModule'
            },
            'SqlRouterModule': {
                path: 'SqlRouterModule.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: 'SQL路由管理模块 - 系统管理员专用',
                globalName: 'sqlRouterModule'
            },
            
            // 员工管理模块
            'employee-management': {
                path: 'platform/employee-manager.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: '员工管理模块',
                globalName: 'EmployeeManager'
            },
            
            // 职位管理模块
            'job-position-management': {
                path: 'platform/job-position-manager.js',
                dependencies: ['auth', 'utils', 'api-client'],
                priority: 4,
                description: '职位管理模块',
                globalName: 'JobPositionManager'
            },

            // 通用组件库 - 阶段3新增
            'UIComponents': {
                path: 'components/UIComponents.js',
                dependencies: [],
                priority: 1,
                description: '通用UI组件库',
                globalName: 'UIComponents'
            },
            'RoleDataAdapter': {
                path: 'components/RoleDataAdapter.js',
                dependencies: [],
                priority: 1,
                description: '角色数据适配器',
                globalName: 'RoleDataAdapter'
            }
        };
        
        // 性能监控
        this.performanceMetrics = {
            loadTimes: new Map(),
            errorCounts: new Map(),
            cacheHits: 0,
            cacheMisses: 0
        };
        
        console.log('ModuleLoader initialized');
    }
    
    /**
     * 加载模块
     * @param {string} moduleName - 模块名称
     * @param {Object} options - 加载选项
     * @returns {Promise} 模块对象
     */
    async loadModule(moduleName, options = {}) {
        const startTime = performance.now();
        
        try {
            // 检查是否已经静态加载
            const globalName = this._getModuleGlobalName(moduleName);
            if (globalName && typeof window[globalName] !== 'undefined') {
                console.log(`✅ 模块 ${moduleName} 已静态加载，跳过动态加载`);
                this.performanceMetrics.cacheHits++;
                return window[globalName];
            }
            
            // 检查缓存
            if (this.moduleCache.has(moduleName) && !options.forceReload) {
                this.performanceMetrics.cacheHits++;
                console.log(`Module ${moduleName} loaded from cache`);
                return this.moduleCache.get(moduleName);
            }
            
            // 检查是否正在加载
            if (this.loadingPromises.has(moduleName)) {
                console.log(`Module ${moduleName} is already loading, waiting...`);
                return await this.loadingPromises.get(moduleName);
            }
            
            // 开始加载
            const loadPromise = this._loadModuleInternal(moduleName, options);
            this.loadingPromises.set(moduleName, loadPromise);
            
            const module = await loadPromise;
            
            // 记录性能
            const loadTime = performance.now() - startTime;
            this.performanceMetrics.loadTimes.set(moduleName, loadTime);
            this.performanceMetrics.cacheMisses++;
            
            console.log(`Module ${moduleName} loaded successfully in ${loadTime.toFixed(2)}ms`);
            
            // 清理加载中的Promise
            this.loadingPromises.delete(moduleName);
            
            return module;
            
        } catch (error) {
            this.loadingPromises.delete(moduleName);
            this._recordError(moduleName, error);
            throw error;
        }
    }
    
    /**
     * 内部加载模块方法
     * @private
     */
    async _loadModuleInternal(moduleName, options) {
        const config = this.moduleConfig[moduleName];
        if (!config) {
            throw new Error(`Module ${moduleName} not found in configuration`);
        }
        
        // 加载依赖
        await this._loadDependencies(moduleName);
        
        // 加载模块文件
        const moduleUrl = this.baseUrl + config.path;
        const module = await this._loadScript(moduleUrl, moduleName);
        
        // 缓存模块
        this.moduleCache.set(moduleName, module);
        this.loadedModules.add(moduleName);
        
        // 初始化模块
        if (module && typeof module.init === 'function') {
            await module.init();
        }
        
        return module;
    }
    
    /**
     * 加载依赖模块
     * @private
     */
    async _loadDependencies(moduleName) {
        const config = this.moduleConfig[moduleName];
        if (!config.dependencies || config.dependencies.length === 0) {
            return;
        }
        
        console.log(`Loading dependencies for ${moduleName}:`, config.dependencies);
        
        // 并行加载所有依赖
        const dependencyPromises = config.dependencies.map(dep => 
            this.loadModule(dep)
        );
        
        await Promise.all(dependencyPromises);
    }
    
    /**
     * 加载脚本文件
     * @private
     * @param {string} url - 脚本URL
     * @param {string} moduleName - 模块名称
     * @returns {Promise}
     */
    async _loadScript(url, moduleName) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            
            // 智能选择压缩版本
            let finalUrl = url;
            const isProduction = window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1');
            const forceRefresh = new URLSearchParams(window.location.search).has('refresh');
            
            // 在生产环境使用压缩版本（如果存在）
            if (isProduction && !forceRefresh) {
                const minUrl = url.replace('.js', '.min.js');
                // 检查是否存在压缩版本的关键文件
                const coreFiles = ['ui-manager.js', 'login-manager.js', 'router-manager.js', 'utils.js'];
                if (coreFiles.some(file => url.includes(file))) {
                    finalUrl = minUrl;
                }
            }
            
            // 添加时间戳防止缓存（测试期间必要）
            const timestamp = Date.now();
            const separator = finalUrl.includes('?') ? '&' : '?';
            script.src = `${finalUrl}${separator}v=${timestamp}`;
            
            script.onload = () => {
                console.log(`✅ Script loaded: ${script.src}`);
                
                // 检查模块是否成功加载到全局作用域
                const globalName = this._getModuleGlobalName(moduleName);
                if (globalName && typeof window[globalName] === 'undefined') {
                    const error = new Error(`Module ${moduleName} not found in global scope after loading`);
                    this._recordError(moduleName, error);
                    reject(error);
                    return;
                }
                
                // 返回模块对象
                const ModuleClass = window[globalName];
                
                // 返回模块类或对象
                resolve(ModuleClass);
            };
            
            script.onerror = () => {
                // 如果压缩版本加载失败，尝试原版本
                if (finalUrl.includes('.min.js')) {
                    console.warn(`❌ Minified version failed, trying original: ${url}`);
                    const originalUrl = url.replace('.min.js', '.js');
                    script.src = `${originalUrl}${separator}v=${timestamp}`;
                    return;
                }
                
                const error = new Error(`Failed to load script: ${finalUrl}`);
                this._recordError(moduleName, error);
                reject(error);
            };

            // 添加模块标识和加载时间记录
            script.setAttribute('data-module', moduleName);
            script.setAttribute('data-load-start', Date.now().toString());
            document.head.appendChild(script);
        });
    }
    
    /**
     * 获取模块的全局变量名
     * @private
     */
    _getModuleGlobalName(moduleName) {
        // 优先使用配置中指定的全局名称
        const config = this.moduleConfig[moduleName];
        if (config && config.globalName) {
            return config.globalName;
        }
        
        // 将模块名转换为全局变量名
        // 例如: 'user-management' -> 'UserManagementModule'
        return moduleName
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('') + 'Module';
    }
    
    /**
     * 批量加载模块
     * @param {Array} moduleNames - 模块名称数组
     * @param {Object} options - 加载选项
     * @returns {Promise} 所有模块的对象数组
     */
    async loadModules(moduleNames, options = {}) {
        console.log('Loading modules:', moduleNames);
        
        // 按优先级排序
        const sortedModules = moduleNames.sort((a, b) => {
            const priorityA = this.moduleConfig[a]?.priority || 999;
            const priorityB = this.moduleConfig[b]?.priority || 999;
            return priorityA - priorityB;
        });
        
        const results = [];
        
        if (options.parallel) {
            // 并行加载
            const promises = sortedModules.map(name => this.loadModule(name, options));
            results.push(...await Promise.all(promises));
        } else {
            // 串行加载
            for (const moduleName of sortedModules) {
                const module = await this.loadModule(moduleName, options);
                results.push(module);
            }
        }
        
        return results;
    }
    
    /**
     * 预加载模块
     * @param {Array} moduleNames - 要预加载的模块名称
     */
    async preloadModules(moduleNames) {
        console.log('Preloading modules:', moduleNames);
        
        try {
            await this.loadModules(moduleNames, { parallel: true });
            console.log('Modules preloaded successfully');
        } catch (error) {
            console.warn('Some modules failed to preload:', error);
        }
    }
    
    /**
     * 卸载模块
     * @param {string} moduleName - 模块名称
     */
    unloadModule(moduleName) {
        if (this.moduleCache.has(moduleName)) {
            const module = this.moduleCache.get(moduleName);
            
            // 调用模块的清理方法
            if (module && typeof module.destroy === 'function') {
                module.destroy();
            }
            
            // 从缓存中移除
            this.moduleCache.delete(moduleName);
            this.loadedModules.delete(moduleName);
            
            // 移除脚本标签
            const script = document.querySelector(`script[data-module="${moduleName}"]`);
            if (script) {
                document.head.removeChild(script);
            }
            
            // 清理全局变量
            const globalName = this._getModuleGlobalName(moduleName);
            if (window[globalName]) {
                delete window[globalName];
            }
            
            console.log(`Module ${moduleName} unloaded`);
        }
    }
    
    /**
     * 重新加载模块
     * @param {string} moduleName - 模块名称
     */
    async reloadModule(moduleName) {
        console.log(`Reloading module: ${moduleName}`);
        this.unloadModule(moduleName);
        return await this.loadModule(moduleName, { forceReload: true });
    }
    
    /**
     * 检查模块是否已加载
     * @param {string} moduleName - 模块名称
     * @returns {boolean}
     */
    isModuleLoaded(moduleName) {
        return this.loadedModules.has(moduleName);
    }
    
    /**
     * 获取已加载的模块列表
     * @returns {Array}
     */
    getLoadedModules() {
        return Array.from(this.loadedModules);
    }
    
    /**
     * 获取模块
     * @param {string} moduleName - 模块名称
     * @returns {Object|null}
     */
    getModule(moduleName) {
        return this.moduleCache.get(moduleName) || null;
    }
    
    /**
     * 记录错误
     * @private
     */
    _recordError(moduleName, error) {
        const count = this.performanceMetrics.errorCounts.get(moduleName) || 0;
        this.performanceMetrics.errorCounts.set(moduleName, count + 1);
        
        console.error(`Module ${moduleName} load error (${count + 1} times):`, error);
        
        // 发送错误报告
        this._sendErrorReport(moduleName, error);
    }
    
    /**
     * 发送错误报告
     * @private
     */
    _sendErrorReport(moduleName, error) {
        // 这里可以实现错误报告功能
        // 例如发送到日志服务器
        console.log('Error report:', {
            module: moduleName,
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
        });
    }
    
    /**
     * 获取性能指标
     * @returns {Object}
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            loadedModulesCount: this.loadedModules.size,
            cachedModulesCount: this.moduleCache.size,
            averageLoadTime: this._calculateAverageLoadTime()
        };
    }
    
    /**
     * 计算平均加载时间
     * @private
     */
    _calculateAverageLoadTime() {
        const times = Array.from(this.performanceMetrics.loadTimes.values());
        if (times.length === 0) return 0;
        
        const sum = times.reduce((acc, time) => acc + time, 0);
        return sum / times.length;
    }
    
    /**
     * 清理缓存
     * @param {boolean} force - 是否强制清理所有缓存
     */
    clearCache(force = false) {
        if (force) {
            // 清理所有模块
            for (const moduleName of this.loadedModules) {
                this.unloadModule(moduleName);
            }
        }
        
        // 清理性能指标
        this.performanceMetrics.loadTimes.clear();
        this.performanceMetrics.errorCounts.clear();
        this.performanceMetrics.cacheHits = 0;
        this.performanceMetrics.cacheMisses = 0;
        
        console.log('Cache cleared');
    }
    
    /**
     * 调试信息
     * @returns {Object}
     */
    getDebugInfo() {
        return {
            loadedModules: this.getLoadedModules(),
            moduleCache: Object.fromEntries(this.moduleCache),
            loadingPromises: Array.from(this.loadingPromises.keys()),
            performanceMetrics: this.getPerformanceMetrics(),
            moduleConfig: this.moduleConfig
        };
    }
}

// 全局导出ModuleLoader类 (防止重复)
if (typeof window.ModuleLoader === 'undefined') {
    window.ModuleLoader = ModuleLoader;
}

// 延迟初始化全局模块加载器实例
if (typeof window.AdminModuleLoader === 'undefined') {
    // 使用setTimeout确保类定义完全完成
    setTimeout(() => {
        if (typeof window.AdminModuleLoader === 'undefined') {
            if (typeof ModuleLoader !== 'undefined') {
                window.AdminModuleLoader = new ModuleLoader();
                console.log('AdminModuleLoader instance created successfully');
            } else {
                console.error('ModuleLoader class is still not defined');
            }
        }
    }, 0);
}

// 导出模块加载器
window.ModuleLoaderModule = {
    ModuleLoader: window.ModuleLoader,
    get instance() {
        // 动态获取实例，支持延迟初始化
        return window.AdminModuleLoader;
    },
    
    // 便捷方法 - 使用getter确保实例存在
    loadModule: (name, options) => {
        if (!window.AdminModuleLoader) {
            throw new Error('AdminModuleLoader instance not ready yet');
        }
        return window.AdminModuleLoader.loadModule(name, options);
    },
    loadModules: (names, options) => {
        if (!window.AdminModuleLoader) {
            throw new Error('AdminModuleLoader instance not ready yet');
        }
        return window.AdminModuleLoader.loadModules(names, options);
    },
    preloadModules: (names) => {
        if (!window.AdminModuleLoader) {
            throw new Error('AdminModuleLoader instance not ready yet');
        }
        return window.AdminModuleLoader.preloadModules(names);
    },
    unloadModule: (name) => {
        if (!window.AdminModuleLoader) {
            throw new Error('AdminModuleLoader instance not ready yet');
        }
        return window.AdminModuleLoader.unloadModule(name);
    },
    reloadModule: (name) => {
        if (!window.AdminModuleLoader) {
            throw new Error('AdminModuleLoader instance not ready yet');
        }
        return window.AdminModuleLoader.reloadModule(name);
    },
    isModuleLoaded: (name) => {
        if (!window.AdminModuleLoader) {
            return false;
        }
        return window.AdminModuleLoader.isModuleLoaded(name);
    },
    getModule: (name) => {
        if (!window.AdminModuleLoader) {
            return null;
        }
        return window.AdminModuleLoader.getModule(name);
    },
    getLoadedModules: () => {
        if (!window.AdminModuleLoader) {
            return [];
        }
        return window.AdminModuleLoader.getLoadedModules();
    },
    getPerformanceMetrics: () => {
        if (!window.AdminModuleLoader) {
            return {};
        }
        return window.AdminModuleLoader.getPerformanceMetrics();
    },
    clearCache: (force) => {
        if (!window.AdminModuleLoader) {
            console.warn('AdminModuleLoader instance not available for cache clearing');
            return;
        }
        return window.AdminModuleLoader.clearCache(force);
    },
    getDebugInfo: () => {
        if (!window.AdminModuleLoader) {
            return { error: 'AdminModuleLoader instance not available' };
        }
        return window.AdminModuleLoader.getDebugInfo();
    }
};

console.log('ModuleLoader module loaded successfully'); 