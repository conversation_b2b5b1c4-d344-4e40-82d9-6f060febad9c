<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- 防止浏览器缓存 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>支付设备认证管理系统 v2.0</title>
    <!-- 🔄 版本标识：强制清除浏览器缓存 -->
    <meta name="version" content="2024-01-15-v2.0">
    <!-- Cache Buster: 1737870000 -->
    <!-- 使用国内CDN和备用方案 -->
    <!-- 使用国内稳定CDN资源 -->
    <link href="https://cdn.staticfile.org/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/bootstrap-icons/1.10.3/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Chart.js 加载器 -->
    <script src="js/libs/chart-loader.js"></script>
    
    <!-- 备用CDN已移除，直接使用稳定的staticfile CDN -->
    <!-- 备用方案：如果CDN不可用，使用内联样式 -->
    <style>
        /* Bootstrap Icons 备用图标 */
        .bi {
            display: inline-block;
            width: 1em;
            height: 1em;
            fill: currentcolor;
        }
        
        /* 如果Bootstrap CSS加载失败的基础样式 */
        .btn {
            display: inline-block;
            padding: 6px 12px;
            margin-bottom: 0;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.42857143;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            cursor: pointer;
            border: 1px solid transparent;
            border-radius: 4px;
            text-decoration: none;
        }
        .btn-primary {
            color: #fff;
            background-color: #337ab7;
            border-color: #2e6da4;
        }
        .form-control {
            display: block;
            width: 100%;
            padding: 6px 12px;
            font-size: 14px;
            line-height: 1.42857143;
            color: #555;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-danger {
            color: #a94442;
            background-color: #f2dede;
            border-color: #ebccd1;
        }
        .alert-success {
            color: #3c763d;
            background-color: #dff0d8;
            border-color: #d6e9c6;
        }
        
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --dark-text: #2c3e50;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }

        .login-container {
            min-height: 100vh;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100vh !important;
            z-index: 9999 !important;
            box-sizing: border-box !important;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 20px !important;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
            padding: 40px !important;
            width: 100% !important;
            max-width: 400px !important;
            text-align: center !important;
            backdrop-filter: blur(10px) !important;
            margin: 0 auto !important;
            position: relative !important;
            z-index: 10000 !important;
        }

        .login-logo {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .login-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 30px;
        }

        .form-floating {
            margin-bottom: 20px;
            position: relative;
        }

        .form-floating .form-control {
            border-radius: 10px;
            border: 2px solid #e1e5e9;
            transition: all 0.3s ease;
            padding: 12px;
        }

        .form-floating .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
            outline: none;
        }

        .form-floating label {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            padding: 12px;
            pointer-events: none;
            border: 1px solid transparent;
            transform-origin: 0 0;
            transition: opacity .1s ease-in-out,transform .1s ease-in-out;
            color: #6c757d;
        }

        .btn-login {
            background: linear-gradient(45deg, var(--accent-color), #5dade2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            color: white;
        }

        .btn-login:hover {
            background: linear-gradient(45deg, #2980b9, var(--accent-color));
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }

        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
            border: 0.25em solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border .75s linear infinite;
        }

        @keyframes spinner-border {
            to { transform: rotate(360deg); }
        }

        .system-info {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* 主应用容器 - 初始隐藏 */
        .app-container {
            display: none;
            min-height: 100vh;
            padding: 0;
            background: #f8f9fa;
            width: 100%;
            box-sizing: border-box;
        }

        /* 特殊处理：确保全屏显示 */
        #appContainer {
            display: none;
            min-height: 100vh;
            width: 100%;
            padding: 0;
            background: #f8f9fa;
            box-sizing: border-box;
        }

        /* 🎯 全局统一样式：主界面全屏显示 */
        .main-container {
            width: 100% !important;
            display: flex !important;
            min-height: 100vh !important;
        }

        /* 强制移除固定定位，改为弹性布局 */
        .main-container .sidebar {
            position: sticky !important;
            flex-shrink: 0 !important;
            width: 280px !important;
            min-width: 280px !important;
        }

        .main-container .main-content {
            flex: 1 !important;
            margin-left: 0 !important;
            width: auto !important;
            min-width: 0 !important;
        }

        /* 🚀 超强优先级：覆盖所有可能的样式 */
        body .main-container {
            width: 100% !important;
        }

        body .main-container .sidebar {
            position: sticky !important;
            top: 0 !important;
            height: 100vh !important;
        }

        body .main-container .main-content {
            flex: 1 !important;
            margin-left: 0 !important;
            width: auto !important;
        }

        /* 🔍 调试样式：添加边框和背景色便于查看布局 */
        .debug-layout .main-container {
            border: 3px solid red !important;
            background: rgba(255, 0, 0, 0.1) !important;
        }

        .debug-layout .sidebar {
            border: 2px solid blue !important;
            background: rgba(0, 0, 255, 0.1) !important;
        }

        .debug-layout .main-content {
            border: 2px solid green !important;
            background: rgba(0, 255, 0, 0.1) !important;
        }

        /* 系统加载器样式 */
        .system-loader {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .loader-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .loader-logo {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .loader-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 30px;
        }

        .loader-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .loader-text {
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* 租户专用主题样式 */
        .tenant-system_admin { --primary-color: #1890ff; --accent-color: #40a9ff; }
        .tenant-platform_admin { --primary-color: #722ed1; --accent-color: #9254de; }
        .tenant-provider { --primary-color: #52c41a; --accent-color: #73d13d; }
        .tenant-merchant { --primary-color: #fa8c16; --accent-color: #ffa940; }

        .tenant-merchant .login-card,
        .tenant-provider .login-card,
        .tenant-platform_admin .login-card,
        .tenant-system_admin .login-card {
            border-left: 4px solid var(--primary-color);
            animation: tenantThemeSlideIn 0.6s ease-out;
        }

        .tenant-merchant .login-card {
            background: linear-gradient(135deg, rgba(250, 140, 22, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
        }

        .tenant-provider .login-card {
            background: linear-gradient(135deg, rgba(82, 196, 26, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
        }

        @keyframes tenantThemeSlideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Bootstrap Icons 简化版本 */
        .bi-shield-check::before { content: "🛡️"; }
        .bi-person::before { content: "👤"; }
        .bi-lock::before { content: "🔒"; }
        .bi-box-arrow-in-right::before { content: "➡️"; }
        
        /* 辅助类 */
        .me-2 { margin-right: 0.5rem; }
        .mb-1 { margin-bottom: 0.25rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .ms-2 { margin-left: 0.5rem; }
        .text-primary { color: var(--accent-color) !important; }
        .visually-hidden {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }

        /* 码商管理专用样式 */
        .provider-management {
            background: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
        }

        .page-header {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .page-header h2 {
            color: var(--primary-color);
            font-weight: 600;
            margin: 0;
        }

        .stats-section {
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        .controls-section {
            margin-bottom: 30px;
        }

        .providers-section .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .providers-section .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 12px 12px 0 0 !important;
            padding: 20px;
        }

        .user-info {
            min-width: 200px;
        }

        .user-info .fw-bold {
            color: var(--primary-color);
            font-weight: 600;
        }

        .company-info {
            min-width: 180px;
        }

        .company-info .fw-bold {
            color: var(--secondary-color);
            font-weight: 600;
        }

        .business-stats {
            font-size: 0.85rem;
            min-width: 120px;
        }

        .business-stats .small {
            margin-bottom: 3px;
            color: #6c757d;
        }

        .badge {
            font-size: 0.8rem;
            padding: 6px 12px;
            border-radius: 8px;
        }

        .btn-group-sm .btn {
            padding: 4px 8px;
            font-size: 0.8rem;
            border-radius: 6px;
        }

        .table-responsive {
            border-radius: 12px;
            overflow: hidden;
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            background: var(--light-bg);
            border: none;
            color: var(--dark-text);
            font-weight: 600;
            padding: 15px;
        }

        .table td {
            border: none;
            padding: 15px;
            vertical-align: middle;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .pagination-section {
            margin-top: 30px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .pagination-info {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .pagination {
            margin: 0;
        }

        .page-link {
            border: none;
            color: var(--primary-color);
            font-weight: 500;
            border-radius: 8px;
            margin: 0 2px;
            transition: all 0.3s ease;
        }

        .page-link:hover {
            color: white;
            background-color: var(--accent-color);
            transform: translateY(-2px);
        }

        .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .no-data-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .modal-content {
            border: none;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-body {
            padding: 30px;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid #e9ecef;
        }

        .form-label {
            font-weight: 600;
            color: var(--dark-text);
            margin-bottom: 8px;
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .provider-management {
                padding: 10px;
            }
            
            .page-header {
                padding: 20px;
            }
            
            .stat-card {
                margin-bottom: 15px;
            }
            
            .user-info,
            .company-info,
            .business-stats {
                min-width: auto;
            }
            
            .table-responsive {
                font-size: 0.85rem;
            }
            
            .btn-group-sm .btn {
                padding: 3px 6px;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <!-- 系统初始化Loading界面 -->
    <div id="systemLoader" class="system-loader">
        <div class="loader-card">
            <div class="loader-logo">
                <i class="bi bi-shield-check"></i>
            </div>
            <h2 class="loader-title">支付设备认证管理系统</h2>
            <div class="loader-content">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="loader-text">正在初始化系统...</div>
            </div>
        </div>
    </div>

    <!-- 动态登录界面容器 - 将根据租户类型动态生成 -->
    <div id="loginContainer" class="login-container" style="display: none;">
        <!-- 租户专用登录界面将在这里动态生成 -->
    </div>

    <!-- 主应用容器 - 登录后动态填充 -->
    <div id="appContainer" class="app-container">
        <!-- 主界面内容将通过JavaScript动态加载 -->
    </div>

    <!-- 使用国内CDN和JavaScript备用方案 -->
    <script>
        // 检测Bootstrap是否加载成功，如果失败则使用简化版本
        window.addEventListener('load', function() {
            // 检查Bootstrap是否加载
            if (typeof bootstrap === 'undefined') {
                console.log('Bootstrap JS未加载，使用简化版本');
                // 这里可以添加简化的JavaScript功能
            }
        });
    </script>
    <!-- 使用国内稳定CDN资源 -->
    <script src="https://cdn.staticfile.org/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    
    <!-- 简化的错误处理 -->
    <script>
        // 检测Bootstrap是否加载成功
        window.addEventListener('load', function() {
            if (typeof bootstrap === 'undefined') {
                console.log('Bootstrap未加载，使用内联样式备用方案');
            } else {
                console.log('Bootstrap加载成功');
            }
        });
    </script>
    <!-- 新的模块化架构：核心模块 -->
    <script>
        // 生成时间戳用于缓存破坏
        const timestamp = Date.now();
        document.write('<script src="js/modules/core/utils.js?v=' + timestamp + '"><\/script>');
        document.write('<script src="js/modules/core/module-loader.js?v=' + timestamp + '"><\/script>');
        document.write('<script src="js/modules/core/auth.js?v=' + timestamp + '"><\/script>');
        document.write('<script src="js/modules/core/router-manager.js?v=' + timestamp + '"><\/script>');
        document.write('<script src="js/modules/core/ui-manager.js?v=' + timestamp + '"><\/script>');
        document.write('<script src="js/modules/core/app-initializer.js?v=' + timestamp + '"><\/script>');
    </script>
    
    <!-- 开发模式缓存清理工具 -->
    <script>
        // 检测是否为开发环境
        const isDevelopment = window.location.hostname === 'localhost' || 
                             window.location.hostname === '127.0.0.1' ||
                             window.location.hostname.includes('dev') ||
                             window.location.hostname.includes('test');
        
        if (isDevelopment) {
            // 添加缓存清理按钮
            const clearCacheBtn = document.createElement('button');
            clearCacheBtn.innerHTML = '🔄 清理缓存';
            clearCacheBtn.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 10000;
                background: #dc3545;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            `;
            
            clearCacheBtn.onclick = function() {
                if (window.AdminUtilsInstance && window.AdminUtilsInstance.clearBrowserCache) {
                    window.AdminUtilsInstance.clearBrowserCache();
                } else {
                    // 备用方案
                    window.location.reload(true);
                }
            };
            
            document.body.appendChild(clearCacheBtn);
            
            // 添加快捷键支持
            document.addEventListener('keydown', function(e) {
                // Ctrl+Shift+R 强制刷新
                if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                    e.preventDefault();
                    window.location.reload(true);
                }
                // Ctrl+F5 强制刷新
                if (e.ctrlKey && e.key === 'F5') {
                    e.preventDefault();
                    window.location.reload(true);
                }
            });
            
            console.log('🔧 开发模式已启用 - 缓存清理工具已加载');
        }
    </script>
</body>
</html> 