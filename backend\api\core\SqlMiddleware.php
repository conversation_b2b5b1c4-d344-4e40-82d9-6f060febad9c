<?php
/**
 * SQL路由中间件
 * 统一处理SQL查询请求，提供安全验证、性能监控和查询优化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

require_once __DIR__ . '/SqlRouter.php';

class SqlMiddleware {
    
    private $tenantAuth;
    private $userContext;
    private $queryConfig;
    private $performanceThreshold = 1000; // 慢查询阈值(毫秒)
    
    public function __construct($tenantAuth = null) {
        $this->tenantAuth = $tenantAuth;
        $this->userContext = $tenantAuth ? $tenantAuth->getUserContext() : null;
    }
    
    /**
     * 执行SQL查询
     */
    public function executeQuery($queryName, $params = [], $options = []) {
        try {
            // 验证查询名称
            if (empty($queryName)) {
                return $this->sendError('查询名称不能为空', 400);
            }
            
            $this->queryConfig = SqlRouter::getQuery($queryName);
            if (!$this->queryConfig) {
                return $this->sendError("查询 {$queryName} 不存在", 404);
            }
            
            // 安全验证
            if (!$this->validateSecurity($queryName, $params)) {
                return $this->sendError('安全验证失败', 403);
            }
            
            // 参数验证
            if (!$this->validateParameters($params)) {
                return $this->sendError('参数验证失败', 400);
            }
            
            // 权限检查
            if (!$this->checkPermissions()) {
                return $this->sendError('权限不足', 403);
            }
            
            // 频率限制检查
            if (!$this->checkRateLimit($queryName)) {
                return $this->sendError('请求过于频繁', 429);
            }
            
            // 预处理参数
            $processedParams = $this->preprocessParameters($params);
            
            // 执行查询
            $startTime = microtime(true);
            $result = SqlRouter::execute($queryName, $processedParams, $this->userContext);
            $executeTime = (microtime(true) - $startTime) * 1000;
            
            // 性能监控
            $this->monitorPerformance($queryName, $executeTime);
            
            // 后处理结果
            $processedResult = $this->postprocessResult($result, $options);
            
            return [
                'success' => true,
                'data' => $processedResult,
                'meta' => [
                    'query_name' => $queryName,
                    'execute_time' => round($executeTime, 2),
                    'record_count' => is_array($processedResult) ? count($processedResult) : 1,
                    'cached' => false // TODO: 从SqlRouter获取缓存状态
                ]
            ];
            
        } catch (Exception $e) {
            error_log("SQL中间件错误: " . $e->getMessage());
            return $this->sendError('查询执行失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 批量执行查询
     */
    public function executeBatch($queries, $options = []) {
        $results = [];
        $totalTime = 0;
        
        foreach ($queries as $index => $query) {
            $queryName = isset($query['name']) ? $query['name'] : '';
            $params = isset($query['params']) ? $query['params'] : [];
            
            $startTime = microtime(true);
            $result = $this->executeQuery($queryName, $params, $options);
            $executeTime = (microtime(true) - $startTime) * 1000;
            
            $totalTime += $executeTime;
            
            $results[] = [
                'index' => $index,
                'query_name' => $queryName,
                'result' => $result,
                'execute_time' => round($executeTime, 2)
            ];
            
            // 如果有错误且设置了fail_fast，则停止执行
            if (!$result['success'] && (isset($options['fail_fast']) ? $options['fail_fast'] : false)) {
                break;
            }
        }
        
        return [
            'success' => true,
            'data' => $results,
            'meta' => [
                'total_queries' => count($queries),
                'executed_queries' => count($results),
                'total_time' => round($totalTime, 2),
                'avg_time' => count($results) > 0 ? round($totalTime / count($results), 2) : 0
            ]
        ];
    }
    
    /**
     * 安全验证
     */
    private function validateSecurity($queryName, $params) {
        // SQL注入检测
        foreach ($params as $key => $value) {
            if (is_string($value) && $this->detectSqlInjection($value)) {
                error_log("检测到SQL注入尝试: {$key} = {$value}");
                return false;
            }
        }
        
        // 检查危险查询模式
        if ($this->isDangerousQuery($queryName)) {
            error_log("尝试执行危险查询: {$queryName}");
            return false;
        }
        
        return true;
    }
    
    /**
     * SQL注入检测
     */
    private function detectSqlInjection($value) {
        $patterns = [
            '/(\bUNION\b.*\bSELECT\b)/i',
            '/(\bDROP\b.*\bTABLE\b)/i',
            '/(\bDELETE\b.*\bFROM\b)/i',
            '/(\bUPDATE\b.*\bSET\b)/i',
            '/(\bINSERT\b.*\bINTO\b)/i',
            '/(\bEXEC\b|\bEXECUTE\b)/i',
            '/(\bSCRIPT\b.*\b>)/i',
            '/(\'|\")[^\'\"]*(\bOR\b|\bAND\b)[^\'\"]*\1/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为危险查询
     */
    private function isDangerousQuery($queryName) {
        $dangerousQueries = [
            'admin.delete_all',
            'system.drop_table',
            'security.bypass',
            'debug.raw_sql'
        ];
        
        return in_array($queryName, $dangerousQueries);
    }
    
    /**
     * 参数验证
     */
    private function validateParameters($params) {
        if (!$this->queryConfig) {
            return false;
        }
        
        // 检查必需参数
        $requiredParams = isset($this->queryConfig['params']) ? $this->queryConfig['params'] : [];
        foreach ($requiredParams as $param) {
            if (!isset($params[$param]) || $params[$param] === '') {
                error_log("缺少必需参数: {$param}");
                return false;
            }
        }
        
        // 验证参数类型和范围
        if (isset($params['limit'])) {
            $limit = (int)$params['limit'];
            if ($limit < 1 || $limit > 1000) {
                error_log("limit参数超出范围: {$limit}");
                return false;
            }
        }
        
        if (isset($params['offset'])) {
            $offset = (int)$params['offset'];
            if ($offset < 0) {
                error_log("offset参数不能为负数: {$offset}");
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 权限检查
     */
    private function checkPermissions() {
        if (!$this->queryConfig) {
            return false;
        }
        
        // 检查查询级别的权限要求
        $permissionRequired = isset($this->queryConfig['permission_required']) ? $this->queryConfig['permission_required'] : null;
        if ($permissionRequired && $this->userContext) {
            if ($this->userContext['user_type'] !== $permissionRequired) {
                return false;
            }
        }
        
        // 检查租户过滤要求
        if ($this->queryConfig['tenant_filter'] && !$this->userContext) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 频率限制检查
     */
    private function checkRateLimit($queryName) {
        if (!$this->userContext) {
            return true;
        }
        
        $userId = $this->userContext['user_id'];
        $key = "sql_rate_limit_{$userId}_{$queryName}";
        
        // 简单的频率限制实现（实际项目中应使用Redis）
        $rateLimitFile = sys_get_temp_dir() . '/' . md5($key);
        $now = time();
        $limit = 100; // 每分钟最多100次
        $window = 60; // 1分钟窗口
        
        if (file_exists($rateLimitFile)) {
            $data = json_decode(file_get_contents($rateLimitFile), true);
            
            // 清理过期记录
            $data['requests'] = array_filter($data['requests'], function($timestamp) use ($now, $window) {
                return ($now - $timestamp) < $window;
            });
            
            // 检查是否超限
            if (count($data['requests']) >= $limit) {
                return false;
            }
            
            $data['requests'][] = $now;
        } else {
            $data = ['requests' => [$now]];
        }
        
        file_put_contents($rateLimitFile, json_encode($data));
        return true;
    }
    
    /**
     * 预处理参数
     */
    private function preprocessParameters($params) {
        $processed = [];
        
        foreach ($params as $key => $value) {
            // 类型转换
            switch ($key) {
                case 'limit':
                case 'offset':
                case 'user_id':
                case 'merchant_id':
                case 'provider_id':
                case 'order_id':
                case 'device_id':
                case 'platform_id':
                    $processed[$key] = (int)$value;
                    break;
                    
                case 'amount':
                case 'balance':
                case 'service_rate':
                    $processed[$key] = (float)$value;
                    break;
                    
                case 'start_date':
                case 'end_date':
                    // 验证日期格式
                    if ($value && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                        throw new Exception("日期格式错误: {$value}");
                    }
                    $processed[$key] = $value;
                    break;
                    
                default:
                    // 字符串参数进行清理
                    if (is_string($value)) {
                        $processed[$key] = trim($value);
                    } else {
                        $processed[$key] = $value;
                    }
                    break;
            }
        }
        
        return $processed;
    }
    
    /**
     * 后处理结果
     */
    private function postprocessResult($result, $options = []) {
        if (!is_array($result)) {
            return $result;
        }
        
        // 数据脱敏
        if (isset($options['mask_sensitive']) ? $options['mask_sensitive'] : false) {
            $result = $this->maskSensitiveData($result);
        }
        
        // 格式化金额
        if (isset($options['format_amounts']) ? $options['format_amounts'] : false) {
            $result = $this->formatAmounts($result);
        }
        
        // 添加计算字段
        if (isset($options['add_computed_fields']) ? $options['add_computed_fields'] : false) {
            $result = $this->addComputedFields($result);
        }
        
        return $result;
    }
    
    /**
     * 数据脱敏
     */
    private function maskSensitiveData($data) {
        $sensitiveFields = ['password', 'api_key', 'salt', 'token'];
        
        if (isset($data[0]) && is_array($data[0])) {
            // 多行数据
            foreach ($data as &$row) {
                foreach ($sensitiveFields as $field) {
                    if (isset($row[$field])) {
                        $row[$field] = '***';
                    }
                }
            }
        } else {
            // 单行数据
            foreach ($sensitiveFields as $field) {
                if (isset($data[$field])) {
                    $data[$field] = '***';
                }
            }
        }
        
        return $data;
    }
    
    /**
     * 格式化金额
     */
    private function formatAmounts($data) {
        $amountFields = ['amount', 'balance', 'total_amount', 'service_rate'];
        
        if (isset($data[0]) && is_array($data[0])) {
            // 多行数据
            foreach ($data as &$row) {
                foreach ($amountFields as $field) {
                    if (isset($row[$field])) {
                        $row[$field . '_formatted'] = number_format($row[$field], 2);
                    }
                }
            }
        } else {
            // 单行数据
            foreach ($amountFields as $field) {
                if (isset($data[$field])) {
                    $data[$field . '_formatted'] = number_format($data[$field], 2);
                }
            }
        }
        
        return $data;
    }
    
    /**
     * 添加计算字段
     */
    private function addComputedFields($data) {
        if (isset($data[0]) && is_array($data[0])) {
            // 多行数据
            foreach ($data as &$row) {
                // 添加时间相关字段
                if (isset($row['created_at'])) {
                    $row['created_at_timestamp'] = strtotime($row['created_at']);
                    $row['created_at_relative'] = $this->getRelativeTime($row['created_at']);
                }
                
                // 添加状态相关字段
                if (isset($row['status'])) {
                    $row['status_display'] = $this->getStatusDisplay($row['status']);
                    $row['status_color'] = $this->getStatusColor($row['status']);
                }
            }
        } else {
            // 单行数据
            if (isset($data['created_at'])) {
                $data['created_at_timestamp'] = strtotime($data['created_at']);
                $data['created_at_relative'] = $this->getRelativeTime($data['created_at']);
            }
            
            if (isset($data['status'])) {
                $data['status_display'] = $this->getStatusDisplay($data['status']);
                $data['status_color'] = $this->getStatusColor($data['status']);
            }
        }
        
        return $data;
    }
    
    /**
     * 性能监控
     */
    private function monitorPerformance($queryName, $executeTime) {
        // 记录慢查询
        if ($executeTime > $this->performanceThreshold) {
            error_log("慢查询检测: {$queryName} 执行时间 {$executeTime}ms");
            
            // 可以在这里添加告警逻辑
            $this->sendSlowQueryAlert($queryName, $executeTime);
        }
        
        // 更新性能统计
        $this->updatePerformanceStats($queryName, $executeTime);
    }
    
    /**
     * 发送慢查询告警
     */
    private function sendSlowQueryAlert($queryName, $executeTime) {
        // 这里可以实现告警逻辑（发送邮件、短信等）
        $alertData = [
            'type' => 'slow_query',
            'query_name' => $queryName,
            'execute_time' => $executeTime,
            'threshold' => $this->performanceThreshold,
            'user_id' => isset($this->userContext['user_id']) ? $this->userContext['user_id'] : null,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // 记录到告警日志
        $alertFile = '/data/logs/sql_alerts.log';
        file_put_contents($alertFile, json_encode($alertData) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 更新性能统计
     */
    private function updatePerformanceStats($queryName, $executeTime) {
        // 这里可以实现性能统计逻辑
        // 例如更新到监控系统或数据库
    }
    
    /**
     * 获取相对时间
     */
    private function getRelativeTime($datetime) {
        $time = strtotime($datetime);
        $diff = time() - $time;
        
        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return intval($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return intval($diff / 3600) . '小时前';
        } else {
            return intval($diff / 86400) . '天前';
        }
    }
    
    /**
     * 获取状态显示文本
     */
    private function getStatusDisplay($status) {
        $statusMap = [
            'active' => '活跃',
            'inactive' => '非活跃',
            'pending' => '待处理',
            'approved' => '已通过',
            'rejected' => '已拒绝',
            'completed' => '已完成',
            'failed' => '失败',
            'online' => '在线',
            'offline' => '离线'
        ];
        
        return isset($statusMap[$status]) ? $statusMap[$status] : $status;
    }
    
    /**
     * 获取状态颜色
     */
    private function getStatusColor($status) {
        $colorMap = [
            'active' => 'success',
            'inactive' => 'secondary',
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            'completed' => 'success',
            'failed' => 'danger',
            'online' => 'success',
            'offline' => 'secondary'
        ];
        
        return isset($colorMap[$status]) ? $colorMap[$status] : 'secondary';
    }
    
    /**
     * 获取查询统计信息
     */
    public function getQueryStats($timeRange = '1 hour') {
        return SqlRouter::getQueryStats();
    }
    
    /**
     * 获取性能报告
     */
    public function getPerformanceReport($timeRange = '1 day') {
        $stats = SqlRouter::getQueryStats();
        
        // 计算性能指标
        $totalQueries = array_sum(array_column($stats, 'total_calls'));
        $totalTime = array_sum(array_column($stats, 'total_time'));
        $avgTime = $totalQueries > 0 ? $totalTime / $totalQueries : 0;
        
        // 找出最慢的查询
        uasort($stats, function($a, $b) {
            return $b['avg_time'] <=> $a['avg_time'];
        });
        $slowestQueries = array_slice($stats, 0, 10, true);
        
        // 找出调用最频繁的查询
        uasort($stats, function($a, $b) {
            return $b['total_calls'] <=> $a['total_calls'];
        });
        $mostUsedQueries = array_slice($stats, 0, 10, true);
        
        return [
            'summary' => [
                'total_queries' => $totalQueries,
                'total_time' => round($totalTime, 2),
                'avg_time' => round($avgTime, 2),
                'time_range' => $timeRange
            ],
            'slowest_queries' => $slowestQueries,
            'most_used_queries' => $mostUsedQueries,
            'cache_stats' => SqlRouter::getCacheStats()
        ];
    }
    
    /**
     * 发送错误响应
     */
    private function sendError($message, $code = 400) {
        return [
            'success' => false,
            'error' => [
                'message' => $message,
                'code' => $code
            ]
        ];
    }
}

error_log("SqlMiddleware 类加载完成");
?> 