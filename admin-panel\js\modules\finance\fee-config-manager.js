/**
 * 手续费配置管理器 - 四层架构财务系统核心模块
 * 负责平台、码商、商户的多层级手续费配置和管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class FeeConfigManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        
        this.currentPage = 1;
        this.pageSize = 20;
        this.feeConfigs = [];
        this.feeTypes = ['platform', 'provider', 'merchant', 'product'];
        this.rateTypes = ['percentage', 'fixed', 'tiered'];
        
        console.log('💰 FeeConfigManager initialized for tenant:', this.tenantInfo?.tenant_type);
    }

    /**
     * 初始化手续费配置管理器
     */
    async init(container) {
        try {
            this.container = container;
            await this.renderFeeConfigPage();
            await this.loadFeeConfigs();
            await this.loadFeeStats();
            this.bindEvents();
            
            console.log('✅ 手续费配置管理器初始化完成');
        } catch (error) {
            console.error('❌ 手续费配置管理器初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    /**
     * 渲染手续费配置页面
     */
    async renderFeeConfigPage() {
        this.container.innerHTML = `
            <div class="fee-config-manager">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="bi bi-percent me-2"></i>手续费配置</h2>
                            <p class="text-muted mb-0">管理平台、码商、商户的多层级手续费配置</p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="feeConfigManager.showCreateFeeModal()">
                                <i class="bi bi-plus me-2"></i>新增配置
                            </button>
                            <button class="btn btn-success" onclick="feeConfigManager.showBatchConfigModal()">
                                <i class="bi bi-collection me-2"></i>批量配置
                            </button>
                            <button class="btn btn-outline-info" onclick="feeConfigManager.exportConfigs()">
                                <i class="bi bi-download me-2"></i>导出配置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 手续费概览统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="platformFeeCount">0</div>
                                <div class="stat-label">平台费率</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-shop"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="providerFeeCount">0</div>
                                <div class="stat-label">码商费率</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="merchantFeeCount">0</div>
                                <div class="stat-label">商户费率</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-box"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="productFeeCount">0</div>
                                <div class="stat-label">产品费率</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 费率层级关系图 -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-diagram-3 me-2"></i>费率层级关系
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="fee-hierarchy">
                                    <div class="hierarchy-level">
                                        <div class="level-card platform-level">
                                            <div class="level-icon">
                                                <i class="bi bi-building"></i>
                                            </div>
                                            <div class="level-content">
                                                <h6>平台费率</h6>
                                                <div class="level-rate" id="platformRate">0.5%</div>
                                                <small class="text-muted">平台向码商收取</small>
                                            </div>
                                        </div>
                                        <div class="hierarchy-arrow">
                                            <i class="bi bi-arrow-down"></i>
                                        </div>
                                    </div>
                                    <div class="hierarchy-level">
                                        <div class="level-card provider-level">
                                            <div class="level-icon">
                                                <i class="bi bi-shop"></i>
                                            </div>
                                            <div class="level-content">
                                                <h6>码商费率</h6>
                                                <div class="level-rate" id="providerRate">1.0%</div>
                                                <small class="text-muted">码商向商户收取</small>
                                            </div>
                                        </div>
                                        <div class="hierarchy-arrow">
                                            <i class="bi bi-arrow-down"></i>
                                        </div>
                                    </div>
                                    <div class="hierarchy-level">
                                        <div class="level-card merchant-level">
                                            <div class="level-icon">
                                                <i class="bi bi-person-badge"></i>
                                            </div>
                                            <div class="level-content">
                                                <h6>商户费率</h6>
                                                <div class="level-rate" id="merchantRate">1.5%</div>
                                                <small class="text-muted">商户实际支付</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置管理界面 -->
                <div class="row">
                    <div class="col-md-3">
                        <!-- 费率类型选择 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">费率类型</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush" id="feeTypeNavigation">
                                    <a href="#" class="list-group-item list-group-item-action active" data-type="all">
                                        <i class="bi bi-list me-2"></i>全部配置
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" data-type="platform">
                                        <i class="bi bi-building me-2"></i>平台费率
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" data-type="provider">
                                        <i class="bi bi-shop me-2"></i>码商费率
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" data-type="merchant">
                                        <i class="bi bi-person-badge me-2"></i>商户费率
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" data-type="product">
                                        <i class="bi bi-box me-2"></i>产品费率
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- 筛选条件 -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">筛选条件</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">费率类型</label>
                                    <select class="form-select" id="rateTypeFilter">
                                        <option value="">全部类型</option>
                                        <option value="percentage">百分比</option>
                                        <option value="fixed">固定金额</option>
                                        <option value="tiered">阶梯费率</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <select class="form-select" id="statusFilter">
                                        <option value="">全部状态</option>
                                        <option value="active">激活</option>
                                        <option value="inactive">禁用</option>
                                        <option value="pending">待生效</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">关键词</label>
                                    <input type="text" class="form-control" id="searchKeyword" placeholder="搜索名称或描述">
                                </div>
                                <button class="btn btn-outline-primary w-100" onclick="feeConfigManager.applyFilters()">
                                    <i class="bi bi-funnel me-2"></i>应用筛选
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-9">
                        <!-- 配置列表 -->
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">费率配置列表</h6>
                                    <div class="d-flex align-items-center gap-2">
                                        <span class="text-muted" id="configCount">共 0 条配置</span>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-secondary" onclick="feeConfigManager.refreshConfigs()">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>配置名称</th>
                                                <th>适用对象</th>
                                                <th>费率类型</th>
                                                <th>费率值</th>
                                                <th>生效时间</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="feeConfigTableBody">
                                            <tr>
                                                <td colspan="7" class="text-center py-4">
                                                    <div class="spinner-border spinner-border-sm me-2"></div>
                                                    加载中...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- 分页 -->
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div class="text-muted">
                                        显示第 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="totalCount">0</span> 条
                                    </div>
                                    <nav>
                                        <ul class="pagination pagination-sm mb-0" id="pagination">
                                            <!-- 分页将动态生成 -->
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 创建/编辑费率配置模态框 -->
                <div class="modal fade" id="feeConfigModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="feeConfigModalTitle">新增费率配置</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="feeConfigForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">配置名称 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="configName" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">适用对象类型 <span class="text-danger">*</span></label>
                                                <select class="form-select" id="targetType" required>
                                                    <option value="">请选择</option>
                                                    <option value="platform">平台</option>
                                                    <option value="provider">码商</option>
                                                    <option value="merchant">商户</option>
                                                    <option value="product">产品</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">费率类型 <span class="text-danger">*</span></label>
                                                <select class="form-select" id="rateType" required>
                                                    <option value="">请选择</option>
                                                    <option value="percentage">百分比费率</option>
                                                    <option value="fixed">固定金额</option>
                                                    <option value="tiered">阶梯费率</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">费率值 <span class="text-danger">*</span></label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="rateValue" step="0.01" min="0" required>
                                                    <span class="input-group-text" id="rateUnit">%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">最小费用</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">¥</span>
                                                    <input type="number" class="form-control" id="minFee" step="0.01" min="0">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">最大费用</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">¥</span>
                                                    <input type="number" class="form-control" id="maxFee" step="0.01" min="0">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">生效时间</label>
                                                <input type="datetime-local" class="form-control" id="effectiveTime">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">失效时间</label>
                                                <input type="datetime-local" class="form-control" id="expireTime">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">配置描述</label>
                                        <textarea class="form-control" id="configDescription" rows="3"></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="isActive" checked>
                                            <label class="form-check-label" for="isActive">
                                                立即生效
                                            </label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="feeConfigManager.saveFeeConfig()">保存配置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 费率类型导航
        document.querySelectorAll('#feeTypeNavigation a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const type = e.target.closest('a').dataset.type;
                this.switchFeeType(type);
            });
        });

        // 费率类型变化
        document.getElementById('rateType')?.addEventListener('change', (e) => {
            this.updateRateUnit(e.target.value);
        });

        // 搜索框回车事件
        document.getElementById('searchKeyword')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.applyFilters();
            }
        });
    }

    /**
     * 加载费率配置
     */
    async loadFeeConfigs() {
        try {
            this.showLoading(true);
            
            const params = this.getFilterParams();
            const response = await this.apiClient.get('/admin/fee-configs', { params });
            
            if (response.success) {
                this.feeConfigs = response.data.configs || [];
                this.renderFeeConfigTable();
                this.updatePagination(response.data.pagination);
            } else {
                this.showTableError('加载费率配置失败: ' + response.message);
            }
        } catch (error) {
            console.error('加载费率配置失败:', error);
            this.showTableError('网络错误，请重试');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 加载费率统计
     */
    async loadFeeStats() {
        try {
            const response = await this.apiClient.get('/admin/fee-configs/stats');
            
            if (response.success) {
                this.updateStatsDisplay(response.data);
            }
        } catch (error) {
            console.error('加载费率统计失败:', error);
        }
    }

    /**
     * 渲染费率配置表格
     */
    renderFeeConfigTable() {
        const tbody = document.getElementById('feeConfigTableBody');
        if (!tbody) return;

        if (this.feeConfigs.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="text-muted">
                            <i class="bi bi-inbox" style="font-size: 2rem; opacity: 0.3;"></i>
                            <p class="mt-2 mb-0">暂无费率配置</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.feeConfigs.map(config => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="config-icon me-2">
                            <i class="bi ${this.getTargetTypeIcon(config.target_type)}"></i>
                        </div>
                        <div>
                            <div class="fw-medium">${config.name}</div>
                            <small class="text-muted">${config.description || '无描述'}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${this.getTargetTypeBadgeColor(config.target_type)}">
                        ${this.getTargetTypeText(config.target_type)}
                    </span>
                    ${config.target_name ? `<br><small class="text-muted">${config.target_name}</small>` : ''}
                </td>
                <td>
                    <span class="badge bg-${this.getRateTypeBadgeColor(config.rate_type)}">
                        ${this.getRateTypeText(config.rate_type)}
                    </span>
                </td>
                <td>
                    <div class="rate-display">
                        <span class="fw-bold">${this.formatRateValue(config.rate_value, config.rate_type)}</span>
                        ${config.min_fee || config.max_fee ? `
                            <br><small class="text-muted">
                                ${config.min_fee ? `最小: ¥${config.min_fee}` : ''}
                                ${config.min_fee && config.max_fee ? ' / ' : ''}
                                ${config.max_fee ? `最大: ¥${config.max_fee}` : ''}
                            </small>
                        ` : ''}
                    </div>
                </td>
                <td>
                    <div class="time-display">
                        <div>${this.formatDateTime(config.effective_time)}</div>
                        ${config.expire_time ? `<small class="text-muted">至 ${this.formatDateTime(config.expire_time)}</small>` : ''}
                    </div>
                </td>
                <td>
                    <span class="badge bg-${this.getStatusBadgeColor(config.status)}">
                        ${this.getStatusText(config.status)}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="feeConfigManager.editFeeConfig(${config.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="feeConfigManager.viewFeeConfig(${config.id})" title="查看">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-${config.status === 'active' ? 'warning' : 'success'}" 
                                onclick="feeConfigManager.toggleFeeConfig(${config.id})" 
                                title="${config.status === 'active' ? '禁用' : '启用'}">
                            <i class="bi bi-${config.status === 'active' ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="feeConfigManager.deleteFeeConfig(${config.id})" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // 更新配置数量
        document.getElementById('configCount').textContent = `共 ${this.feeConfigs.length} 条配置`;
    }

    /**
     * 显示创建费率配置模态框
     */
    showCreateFeeModal() {
        document.getElementById('feeConfigModalTitle').textContent = '新增费率配置';
        document.getElementById('feeConfigForm').reset();
        
        // 设置默认值
        document.getElementById('isActive').checked = true;
        document.getElementById('rateUnit').textContent = '%';
        
        const modal = new bootstrap.Modal(document.getElementById('feeConfigModal'));
        modal.show();
    }

    /**
     * 保存费率配置
     */
    async saveFeeConfig() {
        try {
            const formData = this.getFeeConfigFormData();
            
            // 表单验证
            if (!this.validateFeeConfigForm(formData)) {
                return;
            }

            const response = await this.apiClient.post('/admin/fee-configs', formData);
            
            if (response.success) {
                this.showSuccess('费率配置保存成功');
                bootstrap.Modal.getInstance(document.getElementById('feeConfigModal')).hide();
                await this.loadFeeConfigs();
                await this.loadFeeStats();
            } else {
                this.showError('保存失败: ' + response.message);
            }
        } catch (error) {
            console.error('保存费率配置失败:', error);
            this.showError('保存失败，请重试');
        }
    }

    /**
     * 获取表单数据
     */
    getFeeConfigFormData() {
        return {
            name: document.getElementById('configName').value,
            target_type: document.getElementById('targetType').value,
            rate_type: document.getElementById('rateType').value,
            rate_value: parseFloat(document.getElementById('rateValue').value),
            min_fee: parseFloat(document.getElementById('minFee').value) || null,
            max_fee: parseFloat(document.getElementById('maxFee').value) || null,
            effective_time: document.getElementById('effectiveTime').value || null,
            expire_time: document.getElementById('expireTime').value || null,
            description: document.getElementById('configDescription').value,
            status: document.getElementById('isActive').checked ? 'active' : 'inactive'
        };
    }

    /**
     * 验证表单数据
     */
    validateFeeConfigForm(data) {
        if (!data.name) {
            this.showError('请输入配置名称');
            return false;
        }
        
        if (!data.target_type) {
            this.showError('请选择适用对象类型');
            return false;
        }
        
        if (!data.rate_type) {
            this.showError('请选择费率类型');
            return false;
        }
        
        if (data.rate_value <= 0) {
            this.showError('费率值必须大于0');
            return false;
        }
        
        if (data.rate_type === 'percentage' && data.rate_value > 100) {
            this.showError('百分比费率不能超过100%');
            return false;
        }
        
        if (data.min_fee && data.max_fee && data.min_fee > data.max_fee) {
            this.showError('最小费用不能大于最大费用');
            return false;
        }
        
        return true;
    }

    /**
     * 更新费率单位
     */
    updateRateUnit(rateType) {
        const unitElement = document.getElementById('rateUnit');
        if (!unitElement) return;
        
        switch (rateType) {
            case 'percentage':
                unitElement.textContent = '%';
                break;
            case 'fixed':
                unitElement.textContent = '元';
                break;
            case 'tiered':
                unitElement.textContent = '%';
                break;
            default:
                unitElement.textContent = '%';
        }
    }

    /**
     * 切换费率类型
     */
    switchFeeType(type) {
        // 更新导航状态
        document.querySelectorAll('#feeTypeNavigation a').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`#feeTypeNavigation a[data-type="${type}"]`).classList.add('active');
        
        // 重新加载数据
        this.currentFeeType = type;
        this.loadFeeConfigs();
    }

    /**
     * 应用筛选条件
     */
    applyFilters() {
        this.currentPage = 1;
        this.loadFeeConfigs();
    }

    /**
     * 获取筛选参数
     */
    getFilterParams() {
        return {
            page: this.currentPage,
            page_size: this.pageSize,
            fee_type: this.currentFeeType || '',
            rate_type: document.getElementById('rateTypeFilter')?.value || '',
            status: document.getElementById('statusFilter')?.value || '',
            keyword: document.getElementById('searchKeyword')?.value || ''
        };
    }

    /**
     * 更新统计显示
     */
    updateStatsDisplay(stats) {
        document.getElementById('platformFeeCount').textContent = stats.platform_count || 0;
        document.getElementById('providerFeeCount').textContent = stats.provider_count || 0;
        document.getElementById('merchantFeeCount').textContent = stats.merchant_count || 0;
        document.getElementById('productFeeCount').textContent = stats.product_count || 0;
        
        // 更新层级关系显示
        document.getElementById('platformRate').textContent = (stats.avg_platform_rate || 0) + '%';
        document.getElementById('providerRate').textContent = (stats.avg_provider_rate || 0) + '%';
        document.getElementById('merchantRate').textContent = (stats.avg_merchant_rate || 0) + '%';
    }

    /**
     * 格式化费率值
     */
    formatRateValue(value, type) {
        switch (type) {
            case 'percentage':
                return value + '%';
            case 'fixed':
                return '¥' + value;
            case 'tiered':
                return value + '%';
            default:
                return value;
        }
    }

    /**
     * 获取目标类型图标
     */
    getTargetTypeIcon(type) {
        const iconMap = {
            'platform': 'bi-building',
            'provider': 'bi-shop',
            'merchant': 'bi-person-badge',
            'product': 'bi-box'
        };
        return iconMap[type] || 'bi-question';
    }

    /**
     * 获取目标类型文本
     */
    getTargetTypeText(type) {
        const textMap = {
            'platform': '平台',
            'provider': '码商',
            'merchant': '商户',
            'product': '产品'
        };
        return textMap[type] || type;
    }

    /**
     * 获取目标类型徽章颜色
     */
    getTargetTypeBadgeColor(type) {
        const colorMap = {
            'platform': 'primary',
            'provider': 'success',
            'merchant': 'info',
            'product': 'warning'
        };
        return colorMap[type] || 'secondary';
    }

    /**
     * 获取费率类型文本
     */
    getRateTypeText(type) {
        const textMap = {
            'percentage': '百分比',
            'fixed': '固定金额',
            'tiered': '阶梯费率'
        };
        return textMap[type] || type;
    }

    /**
     * 获取费率类型徽章颜色
     */
    getRateTypeBadgeColor(type) {
        const colorMap = {
            'percentage': 'primary',
            'fixed': 'success',
            'tiered': 'info'
        };
        return colorMap[type] || 'secondary';
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const textMap = {
            'active': '激活',
            'inactive': '禁用',
            'pending': '待生效',
            'expired': '已过期'
        };
        return textMap[status] || status;
    }

    /**
     * 获取状态徽章颜色
     */
    getStatusBadgeColor(status) {
        const colorMap = {
            'active': 'success',
            'inactive': 'secondary',
            'pending': 'warning',
            'expired': 'danger'
        };
        return colorMap[status] || 'secondary';
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(datetime) {
        if (!datetime) return '--';
        return new Date(datetime).toLocaleString('zh-CN');
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.utils.showMessage(message, 'success');
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        this.utils.showMessage(message, 'error');
    }

    /**
     * 显示加载状态
     */
    showLoading(show) {
        const tbody = document.getElementById('feeConfigTableBody');
        if (!tbody) return;
        
        if (show) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <div class="spinner-border spinner-border-sm me-2"></div>
                        加载中...
                    </td>
                </tr>
            `;
        }
    }

    /**
     * 显示表格错误
     */
    showTableError(message) {
        const tbody = document.getElementById('feeConfigTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="text-danger">
                        <i class="bi bi-exclamation-triangle" style="font-size: 2rem; opacity: 0.3;"></i>
                        <p class="mt-2 mb-0">${message}</p>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * 更新分页
     */
    updatePagination(pagination) {
        // 分页逻辑实现
        console.log('更新分页:', pagination);
    }

    /**
     * 刷新配置
     */
    async refreshConfigs() {
        await this.loadFeeConfigs();
        await this.loadFeeStats();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FeeConfigManager;
} else {
    window.FeeConfigManager = FeeConfigManager;
}

console.log('📦 FeeConfigManager 模块加载完成'); 