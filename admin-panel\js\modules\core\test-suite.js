/**
 * 自动化测试套件
 * 测试核心功能、性能指标和用户体验
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class TestSuite {
    constructor() {
        this.tests = new Map();
        this.results = new Map();
        this.isRunning = false;
        this.testResults = { passed: 0, failed: 0, total: 0 };
        
        console.log('🧪 TestSuite initialized');
        this.setupTests();
    }

    setupTests() {
        this.addTest('module-loader', '模块加载器测试', this.testModuleLoader.bind(this));
        this.addTest('event-bus', '事件总线测试', this.testEventBus.bind(this));
        this.addTest('state-manager', '状态管理器测试', this.testStateManager.bind(this));
        this.addTest('components', '组件库测试', this.testComponents.bind(this));
        this.addTest('performance', '性能监控测试', this.testPerformanceMonitor.bind(this));
        this.addTest('load-performance', '加载性能测试', this.testLoadPerformance.bind(this));
        this.addTest('browser-compatibility', '浏览器兼容性测试', this.testBrowserCompatibility.bind(this));
    }

    addTest(id, name, testFunction) {
        this.tests.set(id, { id, name, testFunction, timeout: 5000 });
    }

    async runAllTests() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.resetResults();
        
        console.log('🚀 开始运行测试套件...');
        
        for (const testId of this.tests.keys()) {
            await this.runSingleTest(testId);
        }
        
        this.isRunning = false;
        return this.generateReport();
    }

    async runSingleTest(testId) {
        const test = this.tests.get(testId);
        if (!test) return;
        
        const result = {
            id: testId,
            name: test.name,
            status: 'running',
            startTime: Date.now(),
            error: null,
            details: {}
        };
        
        try {
            const testResult = await Promise.race([
                test.testFunction(),
                new Promise((_, reject) => setTimeout(() => reject(new Error('测试超时')), test.timeout))
            ]);
            
            result.status = 'passed';
            result.details = testResult || {};
            this.testResults.passed++;
            
        } catch (error) {
            result.status = 'failed';
            result.error = error.message;
            this.testResults.failed++;
            console.error(`❌ 测试失败: ${test.name}`, error);
        }
        
        result.duration = Date.now() - result.startTime;
        this.results.set(testId, result);
        this.testResults.total++;
    }

    async testModuleLoader() {
        if (!window.smartModuleLoader) throw new Error('SmartModuleLoader未初始化');
        
        const results = {};
        try {
            const startTime = Date.now();
            await window.smartModuleLoader.loadModule('test-module', { silent: true });
            results.loadTime = Date.now() - startTime;
            results.loadSuccess = true;
        } catch (error) {
            results.loadSuccess = false;
        }
        
        results.cacheWorking = window.smartModuleLoader.moduleCache.size > 0;
        return results;
    }

    async testEventBus() {
        if (!window.eventBus) throw new Error('EventBus未初始化');
        
        const results = {};
        let eventReceived = false;
        
        const unsubscribe = window.eventBus.on('test:event', () => {
            eventReceived = true;
        });
        
        window.eventBus.emit('test:event');
        await new Promise(resolve => setTimeout(resolve, 100));
        
        results.eventPublishSubscribe = eventReceived;
        unsubscribe();
        
        return results;
    }

    async testStateManager() {
        if (!window.GlobalState) throw new Error('GlobalState未初始化');
        
        const results = {};
        const testValue = { test: 'value' };
        
        window.GlobalState.setState('test.data', testValue);
        const retrieved = window.GlobalState.getState('test.data');
        results.stateSetGet = JSON.stringify(retrieved) === JSON.stringify(testValue);
        
        return results;
    }

    async testComponents() {
        if (!window.UI) throw new Error('UI组件库未初始化');
        
        const results = {};
        try {
            const modal = window.UI.create('modal', { title: '测试' });
            results.modalCreation = !!modal.element;
            modal.destroy();
        } catch (error) {
            results.modalCreation = false;
        }
        
        return results;
    }

    async testPerformanceMonitor() {
        if (!window.performanceMonitor) throw new Error('PerformanceMonitor未初始化');
        
        const results = {};
        const summary = window.performanceMonitor.getPerformanceSummary();
        results.performanceDataCollection = summary && typeof summary === 'object';
        results.monitoringActive = window.performanceMonitor.isMonitoring;
        
        return results;
    }

    async testLoadPerformance() {
        const results = {};
        
        if (window.performance && window.performance.timing) {
            const timing = window.performance.timing;
            results.domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
            results.performanceGrade = results.domReady < 2000 ? 'Good' : 'Poor';
        }
        
        return results;
    }

    async testBrowserCompatibility() {
        const results = {};
        
        results.es6Support = typeof Promise !== 'undefined';
        results.fetchSupport = typeof fetch !== 'undefined';
        results.localStorageSupport = typeof localStorage !== 'undefined';
        results.performanceAPISupport = typeof performance !== 'undefined';
        
        results.browserName = this.getBrowserName();
        
        return results;
    }

    getBrowserName() {
        const userAgent = navigator.userAgent;
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        return 'Unknown';
    }

    resetResults() {
        this.results.clear();
        this.testResults = { passed: 0, failed: 0, total: 0 };
    }

    generateReport() {
        const report = {
            summary: {
                ...this.testResults,
                successRate: (this.testResults.passed / this.testResults.total * 100).toFixed(2)
            },
            details: Object.fromEntries(this.results),
            timestamp: new Date().toISOString()
        };
        
        console.log('📊 测试报告生成完成');
        console.table(this.testResults);
        
        return report;
    }

    getTestResults() {
        return {
            summary: this.testResults,
            details: Object.fromEntries(this.results),
            isRunning: this.isRunning
        };
    }
}

// 创建全局测试套件实例
if (typeof window !== 'undefined') {
    window.TestSuite = TestSuite;
    window.testSuite = new TestSuite();
    
    console.log('🎯 测试套件已就绪');
} 