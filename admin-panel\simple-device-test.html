<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备模块简化测试</title>
    <!-- 使用内联样式替代Bootstrap CDN -->
    <style>
        /* 基础Bootstrap样式 */
        .container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
        .card { border: 1px solid #dee2e6; border-radius: 0.375rem; margin-bottom: 1rem; background: #fff; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .card-header { padding: 0.75rem 1.25rem; background-color: rgba(0, 0, 0, 0.03); border-bottom: 1px solid #dee2e6; border-radius: 0.375rem 0.375rem 0 0; }
        .card-body { padding: 1.25rem; }
        .btn { display: inline-block; padding: 0.375rem 0.75rem; margin-bottom: 0; font-size: 1rem; font-weight: 400; line-height: 1.5; text-align: center; text-decoration: none; vertical-align: middle; cursor: pointer; border: 1px solid transparent; border-radius: 0.375rem; }
        .btn-primary { color: #fff; background-color: #007bff; border-color: #007bff; }
        .btn-primary:hover { background-color: #0056b3; border-color: #004085; }
        .btn-secondary { color: #fff; background-color: #6c757d; border-color: #6c757d; }
        .btn-secondary:hover { background-color: #545b62; border-color: #4e555b; }
        .alert { padding: 0.75rem 1.25rem; margin-bottom: 1rem; border: 1px solid transparent; border-radius: 0.375rem; }
        .alert-info { color: #055160; background-color: #d1ecf1; border-color: #bee5eb; }
        .mb-0 { margin-bottom: 0 !important; }
        .mb-3 { margin-bottom: 1rem !important; }
        .ms-2 { margin-left: 0.5rem !important; }
        .me-2 { margin-right: 0.5rem !important; }
        h4 { font-size: 1.5rem; font-weight: 500; line-height: 1.2; margin-top: 0; margin-bottom: 0.5rem; }
        code { font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; font-size: 0.875em; color: #e83e8c; background-color: #f8f9fa; padding: 0.2em 0.4em; border-radius: 0.25rem; }
    </style>
    <style>
        .test-container { max-width: 800px; margin: 50px auto; padding: 20px; }
        .log-area { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            border-radius: 5px; 
            padding: 15px; 
            height: 400px; 
            overflow-y: auto; 
            font-family: monospace; 
            font-size: 12px;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #007bff; }
        .log-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    ⚙️ 设备模块简化测试
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    ℹ️ 测试新的设备模块目录结构：<code>js/modules/device/</code>
                </div>
                
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="testModuleLoading()">
                        ▶️ 开始测试
                    </button>
                    <button class="btn btn-secondary ms-2" onclick="clearLog()">
                        🗑️ 清空日志
                    </button>
                    <button class="btn btn-secondary ms-2" onclick="reloadPage()">
                        🔄 强制刷新
                    </button>
                </div>
                
                <div class="log-area" id="logArea">
                    <div class="log-info">[等待] 点击"开始测试"按钮开始测试...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 移除Bootstrap JS依赖，使用纯JavaScript -->
    <script src="js/modules/core/module-loader.js"></script>
    
    <script>
        let moduleLoader;
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = `log-${type}`;
            div.innerHTML = `[${time}] ${message}`;
            logArea.appendChild(div);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }
        
        function reloadPage() {
            // 强制刷新页面，清除所有缓存
            window.location.reload(true);
        }
        
        async function testModuleLoading() {
            try {
                clearLog();
                log('🚀 开始测试设备模块加载...', 'info');
                
                // 1. 初始化模块加载器
                log('1️⃣ 初始化模块加载器...', 'info');
                
                // 强制清除缓存
                if (moduleLoader) {
                    log('🧹 清除旧的模块缓存...', 'info');
                    moduleLoader.clearCache(true);
                }
                
                moduleLoader = new ModuleLoader();
                log('✅ 模块加载器初始化成功', 'success');
                
                // 显示配置信息
                const deviceConfig = moduleLoader.moduleConfig['device-management'];
                if (deviceConfig) {
                    log(`📋 设备管理模块配置: ${deviceConfig.path}`, 'info');
                    log(`📋 完整URL: ${moduleLoader.baseUrl}${deviceConfig.path}`, 'info');
                } else {
                    log('❌ 设备管理模块配置未找到', 'error');
                }
                
                // 2. 设置测试认证
                log('2️⃣ 设置测试认证信息...', 'info');
                localStorage.setItem('admin_token', 'test_token');
                localStorage.setItem('admin_user', JSON.stringify({
                    id: 1, username: 'test_admin', role: 'admin'
                }));
                log('✅ 测试认证信息设置完成', 'success');
                
                // 3. 测试模块配置
                log('3️⃣ 检查模块配置...', 'info');
                const allConfigs = moduleLoader.moduleConfig;
                if (allConfigs['device-management']) {
                    log(`✅ 设备管理模块配置: ${allConfigs['device-management'].path}`, 'success');
                } else {
                    log('❌ 设备管理模块配置未找到', 'error');
                    return;
                }
                
                // 4. 测试加载设备管理模块
                log('4️⃣ 加载设备管理模块...', 'info');
                
                // 显示即将加载的URL
                const deviceConfig2 = moduleLoader.moduleConfig['device-management'];
                const fullUrl = moduleLoader.baseUrl + deviceConfig2.path;
                log(`🔗 即将加载: ${fullUrl}`, 'info');
                
                const deviceModule = await moduleLoader.loadModule('device-management');
                
                if (deviceModule) {
                    log('✅ 设备管理模块加载成功', 'success');
                    log(`📋 模块类型: ${typeof deviceModule}`, 'info');
                    log(`📋 模块方法: ${Object.getOwnPropertyNames(deviceModule).join(', ')}`, 'info');
                } else {
                    log('❌ 设备管理模块加载失败', 'error');
                    return;
                }
                
                // 5. 测试模块初始化
                log('5️⃣ 测试模块初始化...', 'info');
                const testContainer = document.createElement('div');
                testContainer.style.display = 'none';
                document.body.appendChild(testContainer);
                
                if (typeof deviceModule.init === 'function') {
                    await deviceModule.init(testContainer);
                    log('✅ 设备管理模块初始化成功', 'success');
                } else {
                    log('⚠️ 设备管理模块没有init方法', 'warning');
                }
                
                // 6. 测试其他模块
                log('6️⃣ 测试其他设备模块...', 'info');
                
                const modules = ['group-management', 'checkin-monitor', 'device-module-manager'];
                for (const moduleName of modules) {
                    try {
                        log(`📦 加载模块: ${moduleName}`, 'info');
                        const module = await moduleLoader.loadModule(moduleName);
                        if (module) {
                            log(`✅ ${moduleName} 加载成功`, 'success');
                        } else {
                            log(`❌ ${moduleName} 加载失败`, 'error');
                        }
                    } catch (error) {
                        log(`❌ ${moduleName} 加载异常: ${error.message}`, 'error');
                    }
                }
                
                // 7. 完成测试
                log('🎉 所有测试完成！', 'success');
                log('📊 加载统计:', 'info');
                const metrics = moduleLoader.getPerformanceMetrics();
                log(`   - 已加载模块数: ${metrics.loadedModulesCount}`, 'info');
                log(`   - 缓存模块数: ${metrics.cachedModulesCount}`, 'info');
                log(`   - 平均加载时间: ${metrics.averageLoadTime.toFixed(2)}ms`, 'info');
                
            } catch (error) {
                log(`💥 测试过程中发生错误: ${error.message}`, 'error');
                console.error('详细错误信息:', error);
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成，准备测试新的设备模块结构', 'info');
        });
    </script>
</body>
</html> 