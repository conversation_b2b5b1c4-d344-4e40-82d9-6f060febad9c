<?php
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

$auth = TenantAuth::init();
if (!$auth->requireAdmin()) exit;
$db = $auth->getDatabase();

try {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'list':
            handleGetDailyReports();
            break;
        case 'generate':
            handleGenerateDailyReport();
            break;
        case 'detail':
            handleGetReportDetail();
            break;
        case 'export':
            handleExportReport();
            break;
        case 'schedule':
            handleScheduleReport();
            break;
        case 'templates':
            handleGetReportTemplates();
            break;
        case 'today_stats':
            handleTodayStats();
            break;
        case 'chart_data':
            handleChartData();
            break;
        default:
            handleGetDailyReports();
    }
} catch (Exception $e) {
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 获取日报列表
function handleGetDailyReports() {
    global $db, $auth;
    
    try {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;
        
        $startDate = isset($_GET['start_date']) ? $_GET['start_date'] : '';
        $endDate = isset($_GET['end_date']) ? $_GET['end_date'] : '';
        $type = isset($_GET['type']) ? $_GET['type'] : '';
        
        // 构建查询条件
        $conditions = array();
        $params = array();
        
        if ($startDate) {
            $conditions[] = "report_date >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $conditions[] = "report_date <= ?";
            $params[] = $endDate;
        }
        
        if ($type) {
            $conditions[] = "report_type = ?";
            $params[] = $type;
        }
        
        $whereClause = $conditions ? 'WHERE ' . implode(' AND ', $conditions) : '';
        
        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM daily_reports $whereClause";
        $totalResult = $db->fetch($countSql, $params);
        $total = $totalResult['total'];
        
        // 获取日报列表
        $sql = "SELECT 
                    dr.*
                FROM daily_reports dr
                $whereClause
                ORDER BY dr.report_date DESC, dr.created_at DESC
                LIMIT $limit OFFSET $offset";
        
        $reports = $db->fetchAll($sql, $params);
        
        // 格式化数据
        foreach ($reports as &$report) {
            // 根据现有字段构建report_data结构
            $report['report_data'] = array(
                'total_orders' => $report['total_orders'],
                'successful_orders' => $report['successful_orders'],
                'failed_orders' => $report['failed_orders'],
                'total_amount' => $report['total_amount'],
                'successful_amount' => $report['successful_amount'],
                'commission_amount' => $report['commission_amount'],
                'new_merchants' => $report['new_merchants'],
                'active_merchants' => $report['active_merchants'],
                'new_providers' => $report['new_providers'],
                'active_providers' => $report['active_providers']
            );
            $report['creator_name'] = '系统'; // 默认创建者
            $report['created_at'] = date('Y-m-d H:i:s', strtotime($report['created_at']));
        }
        
        $auth->sendSuccess(array(
            'reports' => $reports,
            'pagination' => array(
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            )
        ));
        
    } catch (Exception $e) {
        $auth->sendError('获取日报列表失败: ' . $e->getMessage(), 500);
    }
}

// 生成日报
function handleGenerateDailyReport() {
    global $db, $auth;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $reportDate = isset($input['report_date']) ? $input['report_date'] : date('Y-m-d');
        $reportType = isset($input['report_type']) ? $input['report_type'] : 'comprehensive';
        
        // 检查是否已存在该日期的报告
        $existingReport = $db->fetch(
            "SELECT id FROM daily_reports WHERE report_date = ?",
            array($reportDate)
        );
        
        if ($existingReport) {
            $auth->sendError('该日期的报告已存在', 400);
            return;
        }
        
        // 生成报告数据
        $reportData = generateReportData($reportDate);
        
        // 保存报告
        $sql = "INSERT INTO daily_reports (
                    report_date, total_orders, successful_orders, failed_orders,
                    total_amount, successful_amount, commission_amount,
                    new_merchants, active_merchants, new_providers, active_providers
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = array(
            $reportDate,
            $reportData['total_orders'],
            $reportData['successful_orders'],
            $reportData['failed_orders'],
            $reportData['total_amount'],
            $reportData['successful_amount'],
            $reportData['commission_amount'],
            $reportData['new_merchants'],
            $reportData['active_merchants'],
            $reportData['new_providers'],
            $reportData['active_providers']
        );
        
        $db->execute($sql, $params);
        $reportId = $db->lastInsertId();
        
        $auth->logUserAction('report_generate', 'daily_report', $reportId, "生成日报: {$reportDate}");
        $auth->sendSuccess(array(
            'id' => $reportId,
            'report_data' => $reportData
        ), '日报生成成功');
        
    } catch (Exception $e) {
        $auth->sendError('生成日报失败: ' . $e->getMessage(), 500);
    }
}

// 获取报告详情
function handleGetReportDetail() {
    global $db, $auth;
    
    try {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if (!$id) {
            $auth->sendError('缺少报告ID', 400);
            return;
        }
        
        $report = $db->fetch("SELECT * FROM daily_reports WHERE id = ?", array($id));
        
        if (!$report) {
            $auth->sendError('报告不存在', 404);
            return;
        }
        
        // 构建详细数据
        $report['report_data'] = array(
            'total_orders' => $report['total_orders'],
            'successful_orders' => $report['successful_orders'],
            'failed_orders' => $report['failed_orders'],
            'total_amount' => $report['total_amount'],
            'successful_amount' => $report['successful_amount'],
            'commission_amount' => $report['commission_amount'],
            'new_merchants' => $report['new_merchants'],
            'active_merchants' => $report['active_merchants'],
            'new_providers' => $report['new_providers'],
            'active_providers' => $report['active_providers']
        );
        
        $auth->sendSuccess($report);
        
    } catch (Exception $e) {
        $auth->sendError('获取报告详情失败: ' . $e->getMessage(), 500);
    }
}

// 导出报告
function handleExportReport() {
    global $db, $auth;
    
    try {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        $format = isset($_GET['format']) ? $_GET['format'] : 'excel';
        
        if (!$id) {
            $auth->sendError('缺少报告ID', 400);
            return;
        }
        
        $report = $db->fetch("SELECT * FROM daily_reports WHERE id = ?", array($id));
        
        if (!$report) {
            $auth->sendError('报告不存在', 404);
            return;
        }
        
        if ($format === 'excel') {
            // 设置Excel导出头
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="daily_report_' . $report['report_date'] . '.xls"');
            header('Cache-Control: max-age=0');
            
            // 输出Excel内容
            echo "<table border='1'>";
            echo "<tr><th>日期</th><th>总订单数</th><th>成功订单数</th><th>失败订单数</th><th>总金额</th><th>成功金额</th><th>佣金金额</th></tr>";
            echo "<tr>";
            echo "<td>{$report['report_date']}</td>";
            echo "<td>{$report['total_orders']}</td>";
            echo "<td>{$report['successful_orders']}</td>";
            echo "<td>{$report['failed_orders']}</td>";
            echo "<td>{$report['total_amount']}</td>";
            echo "<td>{$report['successful_amount']}</td>";
            echo "<td>{$report['commission_amount']}</td>";
            echo "</tr>";
            echo "</table>";
        } else {
            $auth->sendError('不支持的导出格式', 400);
        }
        
    } catch (Exception $e) {
        $auth->sendError('导出报告失败: ' . $e->getMessage(), 500);
    }
}

// 计划报告
function handleScheduleReport() {
    global $db, $auth;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $schedule = isset($input['schedule']) ? $input['schedule'] : 'daily';
        $enabled = isset($input['enabled']) ? intval($input['enabled']) : 1;
        
        // 这里可以实现计划任务的逻辑
        // 简化版本直接返回成功
        
        $auth->sendSuccess(array(
            'schedule' => $schedule,
            'enabled' => $enabled
        ), '报告计划设置成功');
        
    } catch (Exception $e) {
        $auth->sendError('设置报告计划失败: ' . $e->getMessage(), 500);
    }
}

// 获取报告模板
function handleGetReportTemplates() {
    global $auth;
    
    try {
        $templates = array(
            'comprehensive' => array(
                'name' => '综合报告',
                'description' => '包含所有业务数据的综合报告',
                'fields' => array('total_orders', 'successful_orders', 'failed_orders', 'total_amount', 'successful_amount', 'commission_amount')
            ),
            'financial' => array(
                'name' => '财务报告',
                'description' => '专注于财务数据的报告',
                'fields' => array('total_amount', 'successful_amount', 'commission_amount')
            ),
            'operational' => array(
                'name' => '运营报告',
                'description' => '专注于运营数据的报告',
                'fields' => array('total_orders', 'successful_orders', 'failed_orders', 'new_merchants', 'active_merchants')
            )
        );
        
        $auth->sendSuccess($templates);
        
    } catch (Exception $e) {
        $auth->sendError('获取报告模板失败: ' . $e->getMessage(), 500);
    }
}

// 生成报告数据
function generateReportData($reportDate) {
    global $db;
    
    $startDate = $reportDate . ' 00:00:00';
    $endDate = $reportDate . ' 23:59:59';
    
    // 订单统计
    $orderStats = $db->fetch("
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_orders,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_orders,
            SUM(amount) as total_amount,
            SUM(CASE WHEN status = 'success' THEN amount ELSE 0 END) as successful_amount,
            SUM(CASE WHEN status = 'success' THEN fee ELSE 0 END) as commission_amount
        FROM payment_requests 
        WHERE created_at BETWEEN ? AND ?
    ", array($startDate, $endDate));
    
    // 商户统计
    $merchantStats = $db->fetch("
        SELECT 
            COUNT(*) as new_merchants,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_merchants
        FROM merchants 
        WHERE created_at BETWEEN ? AND ?
    ", array($startDate, $endDate));
    
    // 服务商统计
    $providerStats = $db->fetch("
        SELECT 
            COUNT(*) as new_providers,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_providers
        FROM providers 
        WHERE created_at BETWEEN ? AND ?
    ", array($startDate, $endDate));
    
    return array(
        'total_orders' => $orderStats['total_orders'] ?: 0,
        'successful_orders' => $orderStats['successful_orders'] ?: 0,
        'failed_orders' => $orderStats['failed_orders'] ?: 0,
        'total_amount' => $orderStats['total_amount'] ?: 0,
        'successful_amount' => $orderStats['successful_amount'] ?: 0,
        'commission_amount' => $orderStats['commission_amount'] ?: 0,
        'new_merchants' => $merchantStats['new_merchants'] ?: 0,
        'active_merchants' => $merchantStats['active_merchants'] ?: 0,
        'new_providers' => $providerStats['new_providers'] ?: 0,
        'active_providers' => $providerStats['active_providers'] ?: 0
    );
}

// 今日统计
function handleTodayStats() {
    global $db, $auth;
    
    try {
        $today = date('Y-m-d');
        $todayStats = generateReportData($today);
        
        $auth->sendSuccess($todayStats);
        
    } catch (Exception $e) {
        $auth->sendError('获取今日统计失败: ' . $e->getMessage(), 500);
    }
}

// 图表数据
function handleChartData() {
    global $db, $auth;
    
    try {
        $days = isset($_GET['days']) ? intval($_GET['days']) : 7;
        $days = min(30, max(1, $days)); // 限制在1-30天之间
        
        $chartData = array();
        
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $dayData = generateReportData($date);
            $dayData['date'] = $date;
            $chartData[] = $dayData;
        }
        
        $auth->sendSuccess($chartData);
        
    } catch (Exception $e) {
        $auth->sendError('获取图表数据失败: ' . $e->getMessage(), 500);
    }
}
