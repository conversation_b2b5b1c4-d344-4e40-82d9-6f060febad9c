<?php
require_once dirname(__FILE__) . '/../config/database.php';

/**
 * 多租户管理器
 * 负责域名识别、租户信息获取和会话管理
 */
class TenantManager {
    private $db;
    private $currentTenant = null;
    private $currentDomain = null;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->currentDomain = $this->getCurrentDomain();
    }
    
    /**
     * 获取当前访问域名
     */
    private function getCurrentDomain() {
        // 优先从HTTP_HOST获取，然后从SERVER_NAME
        $domain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 
                 (isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : '');
        
        // 调试日志
        error_log("Debug getCurrentDomain - HTTP_HOST: " . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'NULL'));
        error_log("Debug getCurrentDomain - SERVER_NAME: " . (isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'NULL'));
        error_log("Debug getCurrentDomain - Raw domain: " . $domain);
        
        // 移除端口号
        $domain = preg_replace('/:\d+$/', '', $domain);
        
        error_log("Debug getCurrentDomain - Final domain: " . $domain);
        return strtolower($domain);
    }
    
    /**
     * 根据域名获取租户信息（智能识别）
     */
    public function getTenantByDomain($domain = null) {
        if ($domain === null) {
            $domain = $this->currentDomain;
        }
        
        if ($this->currentTenant && 
            ($this->currentTenant['default_domain'] === $domain || 
             $this->currentTenant['custom_domain'] === $domain)) {
            return $this->currentTenant;
        }
        
        try {
            // 1. 先检查是否是默认管理员域名
            if ($this->isAdminDomain($domain)) {
                return [
                    'tenant_type' => 'admin',
                    'tenant_id' => 0,
                    'default_domain' => $domain,
                    'custom_domain' => null,
                    'brand_name' => '系统管理后台',
                    'theme_config' => json_encode(['primary_color' => '#1890ff'])
                ];
            }
            
            // 2. 直接尝试解析子域名模式（跳过tenant_domains表查询）
            if ($this->isSubdomainPattern($domain)) {
                return $this->createTenantFromSubdomain($domain);
            }
            
            return null;
            
        } catch (Exception $e) {
            error_log("TenantManager::getTenantByDomain Error: " . $e->getMessage());
            // 如果是tenant_domains表不存在的错误，尝试子域名解析
            if (strpos($e->getMessage(), 'tenant_domains') !== false) {
                try {
                    if ($this->isSubdomainPattern($domain)) {
                        return $this->createTenantFromSubdomain($domain);
                    }
                } catch (Exception $e2) {
                    error_log("TenantManager::getTenantByDomain Fallback Error: " . $e2->getMessage());
                }
            }
            return null;
        }
    }
    
    /**
     * 检查是否是管理员域名
     */
    private function isAdminDomain($domain) {
        $adminDomains = [
            'admin.paypal-system.com',
            'localhost',
            '127.0.0.1',
            '**************' // 应用服务器IP
        ];
        
        return in_array($domain, $adminDomains);
    }
    
    /**
     * 检查是否是子域名模式（如 providerXX.xxx.com 或 merchantXX.xxx.com）
     */
    private function isSubdomainPattern($domain) {
        // 匹配 provider1.top005.com 或 merchant1.top005.com 格式
        $result = preg_match('/^(provider|merchant)(\d+)\.top005\.com$/', $domain);
        error_log("Debug isSubdomainPattern - domain: $domain, result: $result");
        return $result;
    }
    
    /**
     * 从子域名创建租户信息（自动分配新租户）
     */
    private function createTenantFromSubdomain($domain) {
        if (!preg_match('/^(provider|merchant)(\d+)\.top005\.com$/', $domain, $matches)) {
            error_log("Debug createTenantFromSubdomain - domain pattern not matched: $domain");
            return null;
        }
        
        $tenantType = $matches[1];
        $tenantId = intval($matches[2]);
        error_log("Debug createTenantFromSubdomain - tenantType: $tenantType, tenantId: $tenantId");
        
        try {
            // 检查对应的租户是否存在
            if ($tenantType === 'provider') {
                $tenant = $this->db->fetch(
                    "SELECT pp.id, pp.provider_code 
                     FROM payment_providers pp 
                     WHERE pp.id = ? AND pp.status = 'approved'",
                    [$tenantId]
                );
                
                error_log("Debug createTenantFromSubdomain - provider query result: " . print_r($tenant, true));
                
                if ($tenant) {
                    $displayName = '码商-' . ($tenant['provider_code'] ?: $tenant['id']);
                    
                    // 自动创建域名记录
                    $this->autoCreateTenantDomain('provider', $tenantId, $domain, $displayName);
                    
                    return [
                        'tenant_type' => 'provider',
                        'tenant_id' => $tenantId,
                        'default_domain' => $domain,
                        'custom_domain' => null,
                        'brand_name' => $displayName,
                        'company_name' => $displayName,
                        'contact_name' => $tenant['provider_code'],
                        'theme_config' => json_encode(['primary_color' => '#1890ff']),
                        'current_domain' => $domain,
                        'using_custom_domain' => false
                    ];
                }
            } else if ($tenantType === 'merchant') {
                $tenant = $this->db->fetch(
                    "SELECT m.id, m.merchant_code 
                     FROM merchants m 
                     WHERE m.id = ? AND m.status = 'approved'",
                    [$tenantId]
                );
                
                error_log("Debug createTenantFromSubdomain - merchant query result: " . print_r($tenant, true));
                
                if ($tenant) {
                    $displayName = '商户-' . ($tenant['merchant_code'] ?: $tenant['id']);
                    
                    // 自动创建域名记录
                    $this->autoCreateTenantDomain('merchant', $tenantId, $domain, $displayName);
                    
                    return [
                        'tenant_type' => 'merchant',
                        'tenant_id' => $tenantId,
                        'default_domain' => $domain,
                        'custom_domain' => null,
                        'brand_name' => $displayName,
                        'company_name' => $displayName,
                        'contact_name' => $tenant['merchant_code'],
                        'theme_config' => json_encode(['primary_color' => '#722ed1']),
                        'current_domain' => $domain,
                        'using_custom_domain' => false
                    ];
                }
            }
            
            error_log("Debug createTenantFromSubdomain - no matching tenant found for type: $tenantType, id: $tenantId");
            return null;
            
        } catch (Exception $e) {
            error_log("TenantManager::createTenantFromSubdomain Error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 自动创建租户域名记录
     * 注意：域名配置已存储在各业务表中(payment_providers, merchants)，此方法暂时禁用
     */
    private function autoCreateTenantDomain($tenantType, $tenantId, $domain, $brandName) {
        // 域名信息已存储在对应的业务表中：
        // - 码商域名：payment_providers 表的 custom_domain, dns_domain, domain_config 字段
        // - 商户域名：merchants 表的相应字段
        // 不需要额外的 tenant_domains 表，直接返回
        return;
    }
    
    /**
     * 获取当前租户信息
     */
    public function getCurrentTenant() {
        if ($this->currentTenant === null) {
            $this->currentTenant = $this->getTenantByDomain();
        }
        return $this->currentTenant;
    }
    
    /**
     * 验证租户访问权限
     */
    public function validateTenantAccess($userId, $userType) {
        $tenant = $this->getCurrentTenant();
        
        if (!$tenant) {
            return false;
        }
        
        // 管理员可以访问所有域名
        if ($userType === 'admin') {
            return true;
        }
        
        // 码商只能访问自己的域名
        if ($tenant['tenant_type'] === 'provider' && $userType === 'provider') {
            $provider = $this->db->fetch(
                "SELECT id FROM payment_providers WHERE user_id = ?",
                [$userId]
            );
            return $provider && $provider['id'] == $tenant['tenant_id'];
        }
        
        // 商户只能访问自己的域名
        if ($tenant['tenant_type'] === 'merchant' && $userType === 'merchant') {
            $merchant = $this->db->fetch(
                "SELECT id FROM merchants WHERE user_id = ?",
                [$userId]
            );
            return $merchant && $merchant['id'] == $tenant['tenant_id'];
        }
        
        return false;
    }
    
    /**
     * 为租户创建域名
     */
    public function createTenantDomain($tenantType, $tenantId, $domainName, $domainType = 'subdomain', $options = []) {
        try {
            // 检查域名是否已存在
            $existing = $this->db->fetch(
                "SELECT id FROM tenant_domains WHERE domain_name = ?",
                [$domainName]
            );
            
            if ($existing) {
                throw new Exception('域名已被使用');
            }
            
            // 获取租户信息用于品牌名称
            $brandName = '';
            if ($tenantType === 'provider') {
                $provider = $this->db->fetch(
                    "SELECT provider_code FROM payment_providers WHERE id = ?",
                    [$tenantId]
                );
                $brandName = $provider ? ('码商-' . ($provider['provider_code'] ?: $tenantId)) : '码商后台';
            } else if ($tenantType === 'merchant') {
                $merchant = $this->db->fetch(
                    "SELECT merchant_code FROM merchants WHERE id = ?",
                    [$tenantId]
                );
                $brandName = $merchant ? ('商户-' . ($merchant['merchant_code'] ?: $tenantId)) : '商户后台';
            }
            
            // 默认主题配置
            $defaultTheme = [
                'primary_color' => isset($options['primary_color']) ? $options['primary_color'] : '#1890ff',
                'sidebar_style' => isset($options['sidebar_style']) ? $options['sidebar_style'] : 'light',
                'logo_position' => isset($options['logo_position']) ? $options['logo_position'] : 'left'
            ];
            
            // 插入域名配置
            $this->db->execute(
                "INSERT INTO tenant_domains (
                    tenant_type, tenant_id, domain_name, domain_type, 
                    brand_name, theme_config, logo_url, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', NOW())",
                [
                    $tenantType,
                    $tenantId,
                    $domainName,
                    $domainType,
                    $brandName,
                    json_encode($defaultTheme),
                    isset($options['logo_url']) ? $options['logo_url'] : null
                ]
            );
            
            return $this->db->lastInsertId();
            
        } catch (Exception $e) {
            error_log("TenantManager::createTenantDomain Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 更新租户域名配置
     */
    public function updateTenantDomain($domainId, $options = []) {
        try {
            $updateFields = [];
            $params = [];
            
            $allowedFields = ['brand_name', 'theme_config', 'logo_url', 'ssl_enabled', 'status'];
            
            foreach ($allowedFields as $field) {
                if (isset($options[$field])) {
                    $updateFields[] = "$field = ?";
                    if ($field === 'theme_config' && is_array($options[$field])) {
                        $params[] = json_encode($options[$field]);
                    } else {
                        $params[] = $options[$field];
                    }
                }
            }
            
            if (!empty($updateFields)) {
                $params[] = $domainId;
                $this->db->execute(
                    "UPDATE tenant_domains SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?",
                    $params
                );
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("TenantManager::updateTenantDomain Error: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 获取租户域名信息
     */
    public function getTenantDomains($tenantType, $tenantId) {
        return $this->db->fetch(
            "SELECT * FROM tenant_domains 
             WHERE tenant_type = ? AND tenant_id = ?",
            [$tenantType, $tenantId]
        );
    }
    
    /**
     * 设置商户自定义域名
     */
    public function setCustomDomain($tenantType, $tenantId, $customDomain) {
        try {
            // 验证域名格式
            if (!$this->verifyCustomDomain($customDomain)) {
                throw new Exception('无效的域名格式');
            }
            
            // 检查域名是否已被使用
            $existing = $this->db->fetch(
                "SELECT tenant_type, tenant_id FROM tenant_domains 
                 WHERE (default_domain = ? OR custom_domain = ?) AND NOT (tenant_type = ? AND tenant_id = ?)",
                [$customDomain, $customDomain, $tenantType, $tenantId]
            );
            
            if ($existing) {
                throw new Exception('域名已被其他租户使用');
            }
            
            // 获取默认域名信息
            $tenantDomainInfo = $this->getTenantDomains($tenantType, $tenantId);
            if (!$tenantDomainInfo) {
                throw new Exception('租户信息不存在');
            }
            
            // 生成DNS配置说明
            $dnsInstructions = $this->generateDNSInstructions($customDomain, $tenantDomainInfo['default_domain']);
            
            // 更新自定义域名
            $this->db->execute(
                "UPDATE tenant_domains 
                 SET custom_domain = ?, domain_verified = FALSE, dns_instructions = ?, updated_at = NOW()
                 WHERE tenant_type = ? AND tenant_id = ?",
                [$customDomain, $dnsInstructions, $tenantType, $tenantId]
            );
            
            return [
                'success' => true,
                'custom_domain' => $customDomain,
                'dns_instructions' => $dnsInstructions,
                'verification_status' => 'pending'
            ];
            
        } catch (Exception $e) {
            throw new Exception('设置自定义域名失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 生成DNS配置说明
     */
    private function generateDNSInstructions($customDomain, $defaultDomain) {
        return "请在您的域名管理后台添加以下记录：\n\n" .
               "方式一（推荐）：\n" .
               "记录类型：CNAME\n" .
               "主机记录：{$this->getSubdomain($customDomain)}\n" .
               "记录值：{$defaultDomain}\n\n" .
               "方式二：\n" .
               "记录类型：A\n" .
               "主机记录：{$this->getSubdomain($customDomain)}\n" .
               "记录值：**************\n\n" .
               "配置完成后，请等待DNS生效（通常5-30分钟）";
    }
    
    /**
     * 获取子域名部分
     */
    private function getSubdomain($domain) {
        $parts = explode('.', $domain);
        if (count($parts) > 2) {
            return $parts[0]; // 返回子域名部分，如 admin.myshop.com 返回 admin
        }
        return '@'; // 根域名
    }
    
    /**
     * 验证自定义域名DNS配置
     */
    public function verifyCustomDomainDNS($tenantType, $tenantId) {
        try {
            $tenantDomainInfo = $this->getTenantDomains($tenantType, $tenantId);
            
            if (!$tenantDomainInfo || !$tenantDomainInfo['custom_domain']) {
                throw new Exception('未设置自定义域名');
            }
            
            $customDomain = $tenantDomainInfo['custom_domain'];
            $defaultDomain = $tenantDomainInfo['default_domain'];
            
            // 检查DNS解析
            $verified = false;
            $resolvedIPs = [];
            
            // 方法1：检查CNAME记录
            $cnameRecords = dns_get_record($customDomain, DNS_CNAME);
            if (!empty($cnameRecords)) {
                foreach ($cnameRecords as $record) {
                    if ($record['target'] === $defaultDomain) {
                        $verified = true;
                        break;
                    }
                }
            }
            
            // 方法2：检查A记录
            if (!$verified) {
                $aRecords = dns_get_record($customDomain, DNS_A);
                if (!empty($aRecords)) {
                    foreach ($aRecords as $record) {
                        $resolvedIPs[] = $record['ip'];
                        // 检查是否指向我们的服务器
                        if ($record['ip'] === '**************') {
                            $verified = true;
                        }
                    }
                }
            }
            
            // 更新验证状态
            if ($verified) {
                $this->db->execute(
                    "UPDATE tenant_domains SET domain_verified = TRUE, updated_at = NOW() 
                     WHERE tenant_type = ? AND tenant_id = ?",
                    [$tenantType, $tenantId]
                );
            }
            
            return [
                'verified' => $verified,
                'custom_domain' => $customDomain,
                'resolved_ips' => $resolvedIPs,
                'cname_records' => $cnameRecords,
                'message' => $verified ? 'DNS配置正确' : 'DNS配置未生效或有误'
            ];
            
        } catch (Exception $e) {
            return [
                'verified' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 移除自定义域名
     */
    public function removeCustomDomain($tenantType, $tenantId) {
        try {
            $this->db->execute(
                "UPDATE tenant_domains 
                 SET custom_domain = NULL, domain_verified = FALSE, 
                     dns_instructions = '系统自动分配，无需配置DNS', updated_at = NOW()
                 WHERE tenant_type = ? AND tenant_id = ?",
                [$tenantType, $tenantId]
            );
            
            return ['success' => true, 'message' => '自定义域名已移除'];
            
        } catch (Exception $e) {
            throw new Exception('移除自定义域名失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 记录域名访问日志
     */
    public function logDomainAccess($responseCode = 200, $responseTime = 0) {
        // 暂时禁用访问日志记录，避免domain_access_logs表不存在的问题
        // 可以在后续创建表后重新启用
        return;
        
        $tenant = $this->getCurrentTenant();
        
        try {
            $this->db->execute(
                "INSERT INTO domain_access_logs (
                    domain_name, tenant_type, tenant_id, ip_address, 
                    user_agent, request_uri, response_code, response_time_ms, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())",
                [
                    $this->currentDomain,
                    $tenant ? $tenant['tenant_type'] : null,
                    $tenant ? $tenant['tenant_id'] : null,
                    $this->getClientIP(),
                    isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
                    isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '',
                    $responseCode,
                    $responseTime
                ]
            );
        } catch (Exception $e) {
            // 静默记录，不影响主要功能
            error_log("TenantManager::logDomainAccess Error: " . $e->getMessage());
        }
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // 如果是逗号分隔的IP列表，取第一个
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                return $ip;
            }
        }
        
        return '0.0.0.0';
    }
    
    /**
     * 生成租户专用的子域名
     */
    public function generateSubdomain($tenantType, $tenantId, $baseDomain = 'top005.com') {
        $prefix = $tenantType === 'provider' ? 'provider' : 'merchant';
        return $prefix . $tenantId . '.' . $baseDomain;
    }
    
    /**
     * 验证自定义域名
     */
    public function verifyCustomDomain($domain) {
        // 基本域名格式验证
        if (!filter_var('http://' . $domain, FILTER_VALIDATE_URL)) {
            return false;
        }
        
        // DNS解析验证（可选，用于生产环境）
        // return checkdnsrr($domain, 'A') || checkdnsrr($domain, 'CNAME');
        
        return true;
    }
} 