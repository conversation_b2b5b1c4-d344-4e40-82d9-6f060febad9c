<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由系统测试 - PayPal Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 col-lg-2 bg-dark min-vh-100">
                <div class="sidebar p-3">
                    <h5 class="text-white mb-4">PayPal Admin</h5>
                    <div id="menuContainer">
                        <!-- 菜单将动态生成 -->
                    </div>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-9 col-lg-10">
                <div class="main-content p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 id="pageTitle">路由系统测试</h4>
                        <div class="tenant-info">
                            <span class="badge bg-primary" id="tenantType">Provider</span>
                        </div>
                    </div>
                    
                    <!-- 路由信息显示 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">当前路由信息</h6>
                                </div>
                                <div class="card-body">
                                    <div id="routeInfo">
                                        <p><strong>当前路径:</strong> <span id="currentPath">-</span></p>
                                        <p><strong>页面标题:</strong> <span id="currentTitle">-</span></p>
                                        <p><strong>模块名称:</strong> <span id="currentModule">-</span></p>
                                        <p><strong>组件名称:</strong> <span id="currentComponent">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">路由测试</h6>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group-vertical w-100" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="testRoute('/dashboard')">测试仪表板</button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="testRoute('/users')">测试用户管理</button>
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="testRoute('/devices')">测试设备管理</button>
                                        <button type="button" class="btn btn-outline-info btn-sm" onclick="testRoute('/transactions')">测试交易记录</button>
                                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="changeTenant()">切换租户类型</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 页面内容区 -->
                    <div id="contentArea">
                        <div class="text-center py-5">
                            <i class="bi bi-gear" style="font-size: 4rem; color: #6c757d;"></i>
                            <h3 class="mt-3">路由系统就绪</h3>
                            <p class="text-muted">点击左侧菜单或使用测试按钮来验证路由功能</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 模拟认证管理器 -->
    <script>
        // 模拟AuthManager
        class MockAuthManager {
            constructor() {
                this.user = {
                    id: 1,
                    username: 'test_user',
                    tenant_type: 'provider',
                    is_staff: false
                };
            }
            
            getUser() {
                return this.user;
            }
            
            isLoggedIn() {
                return true;
            }
        }
        
        // 模拟ModuleLoader
        class MockModuleLoader {
            async loadModule(moduleName) {
                console.log('模拟加载模块:', moduleName);
                return {
                    render: () => `<div class="alert alert-info">模块 ${moduleName} 已加载</div>`,
                    init: () => console.log('模块初始化:', moduleName)
                };
            }
        }
        
        // 模拟UIManager
        class MockUIManager {
            constructor() {
                this.tenantInfo = {
                    tenant_type: 'provider'
                };
                this.auth = new MockAuthManager();
            }
            
            generateMenuItems() {
                return `
                    <a href="#" class="menu-item d-block text-white text-decoration-none py-2" data-page="dashboard">
                        <i class="bi bi-speedometer2 me-2"></i>仪表板
                    </a>
                    <a href="#" class="menu-item d-block text-white text-decoration-none py-2" data-page="devices">
                        <i class="bi bi-phone me-2"></i>设备管理
                    </a>
                    <a href="#" class="menu-item d-block text-white text-decoration-none py-2" data-page="transactions">
                        <i class="bi bi-receipt me-2"></i>交易记录
                    </a>
                    <a href="#" class="menu-item d-block text-white text-decoration-none py-2" data-page="alipay">
                        <i class="bi bi-credit-card me-2"></i>支付宝账户
                    </a>
                `;
            }
        }
        
        // 初始化全局对象
        window.authManager = new MockAuthManager();
        window.AdminModuleLoader = new MockModuleLoader();
        window.uiManager = new MockUIManager();
    </script>
    
    <!-- 加载路由管理器 -->
    <script src="js/modules/core/router-manager.js"></script>
    
    <!-- 测试脚本 -->
    <script>
        let currentTenantType = 'provider';
        const tenantTypes = ['provider', 'merchant', 'platform_admin', 'system_admin'];
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 开始初始化路由测试页面...');
            
            // 生成菜单
            generateMenu();
            
            // 绑定菜单事件
            bindMenuEvents();
            
            // 初始化路由管理器
            if (window.routerManager) {
                window.routerManager.setManagers({
                    moduleLoader: window.AdminModuleLoader,
                    uiManager: window.uiManager,
                    authManager: window.authManager
                });
                
                // 初始化路由系统
                await window.routerManager.initialize();
                
                console.log('✅ 路由系统初始化完成');
            }
        });
        
        // 生成菜单
        function generateMenu() {
            const menuContainer = document.getElementById('menuContainer');
            menuContainer.innerHTML = window.uiManager.generateMenuItems();
        }
        
        // 绑定菜单事件
        function bindMenuEvents() {
            const menuItems = document.querySelectorAll('.menu-item[data-page]');
            menuItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = item.getAttribute('data-page');
                    
                    // 移除所有激活状态
                    menuItems.forEach(mi => mi.classList.remove('bg-primary'));
                    // 激活当前菜单
                    item.classList.add('bg-primary');
                    
                    // 使用路由管理器导航
                    if (window.routerManager) {
                        window.routerManager.navigateByMenuId(page);
                        updateRouteInfo();
                    }
                });
            });
        }
        
        // 测试路由
        function testRoute(path) {
            if (window.routerManager) {
                window.routerManager.navigateToRoute(path);
                updateRouteInfo();
            }
        }
        
        // 切换租户类型
        function changeTenant() {
            const currentIndex = tenantTypes.indexOf(currentTenantType);
            const nextIndex = (currentIndex + 1) % tenantTypes.length;
            currentTenantType = tenantTypes[nextIndex];
            
            // 更新UI
            document.getElementById('tenantType').textContent = currentTenantType;
            window.uiManager.tenantInfo.tenant_type = currentTenantType;
            window.authManager.user.tenant_type = currentTenantType;
            
            // 重新生成菜单
            generateMenu();
            bindMenuEvents();
            
            console.log('切换到租户类型:', currentTenantType);
        }
        
        // 更新路由信息显示
        function updateRouteInfo() {
            if (window.routerManager) {
                const currentRoute = window.routerManager.getCurrentRoute();
                if (currentRoute) {
                    document.getElementById('currentPath').textContent = currentRoute.path;
                    document.getElementById('currentTitle').textContent = currentRoute.title;
                    document.getElementById('currentModule').textContent = currentRoute.module;
                    document.getElementById('currentComponent').textContent = currentRoute.component;
                }
            }
        }
    </script>
</body>
</html> 