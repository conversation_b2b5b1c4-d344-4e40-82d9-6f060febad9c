/**
 * 界面管理模块
 * 负责主界面创建、菜单管理、页面导航、布局控制等UI功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-15
 */

// 界面管理器类
class UIManager {
    constructor(authManager, tenantInfo = null) {
        this.auth = authManager;
        this.tenantInfo = tenantInfo; // 新增：租户信息
        this.currentPage = 'dashboard';
        this.pageLoadHandlers = new Map();
        this.menuItems = [];
        this.isInitialized = false;
        
        // 初始化工具对象
        this.utils = {
            showLoading: (message = '加载中...') => {
                this.showLoading(true, message);
            },
            hideLoading: () => {
                this.showLoading(false);
            },
            showMessage: (message, type = 'info') => {
                this.showMessage(message, type);
            }
        };
        
        // 绑定主界面创建事件
        document.addEventListener('mainInterfaceCreate', (e) => {
            console.log('🎯 UI管理器接收到主界面创建事件:', e.detail);
            this.createMainInterface(e.detail.container, e.detail.user);
        });
    }

    /**
     * 创建主界面
     * @param {HTMLElement} container 容器元素
     * @param {Object} user 用户信息
     */
    createMainInterface(container, user) {
        if (!container || !user) {
            console.error('❌ 无法创建主界面：缺少必要参数', { container, user });
            return;
        }

        console.log('🎨 正在创建主界面...', { user });

        try {
            // 生成主界面HTML
            console.log('📝 生成主界面HTML...');
            container.innerHTML = this.generateMainInterfaceHTML(user);

            // 设置全局引用
            window.authManager = this.auth;

            // 初始化界面功能
            console.log('⚙️ 初始化界面功能...');
            this.initializeInterface();
            this.isInitialized = true;

            console.log('✅ 主界面创建完成');
        } catch (error) {
            console.error('❌ 主界面创建失败:', error);
        }
    }

    /**
     * 生成主界面HTML
     * @param {Object} user 用户信息
     * @returns {string} HTML字符串
     */
    generateMainInterfaceHTML(user) {
        return `
            ${this.generateMainStyles()}
            
            <div class="main-container">
                <!-- 侧边栏 -->
                <div class="sidebar">
                    <div class="sidebar-header">
                        <h4 class="mb-0">
                            <i class="bi bi-speedometer2"></i> ${this.getTenantDisplayName()}
                        </h4>
                        <small style="opacity: 0.7">
                            ${user.display_name || user.real_name || user.username}
                        </small>
                        <div style="font-size: 10px; opacity: 0.5; margin-top: 5px;">
                            ${this.getTenantTypeText()}
                        </div>
                    </div>
                    <nav class="sidebar-menu">
                        ${this.generateMenuItems(user.user_type)}
                        <div class="mt-auto">
                            <a href="#" class="menu-item logout-btn">
                                <i class="bi bi-box-arrow-right me-3"></i>退出登录
                            </a>
                        </div>
                    </nav>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <!-- 顶部栏 -->
                    <div class="top-bar">
                        <div>
                            <h3 class="mb-0" id="pageTitle">仪表板</h3>
                            <small class="text-muted">欢迎使用支付设备认证管理系统</small>
                        </div>
                        <div class="top-bar-actions">
                            <button class="btn btn-outline-primary refresh-btn">
                                <i class="bi bi-arrow-clockwise me-2"></i>刷新
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-person-circle me-2"></i>${user.username}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" data-action="profile">
                                        <i class="bi bi-person me-2"></i>个人资料
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" data-action="settings">
                                        <i class="bi bi-gear me-2"></i>系统设置
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item logout-btn" href="#">
                                        <i class="bi bi-box-arrow-right me-2"></i>退出登录
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 内容区域 -->
                    <div class="content-wrapper" id="contentArea">
                        ${this.generateDashboardContent(user)}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成主界面样式
     * @returns {string} CSS样式
     */
    generateMainStyles() {
        return `
            <style>
                .main-container {
                    display: flex;
                    min-height: 100vh;
                    background: #f8fafc;
                    width: 100%;
                }

                .sidebar {
                    width: 280px;
                    min-width: 280px;
                    background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
                    color: white;
                    position: sticky;
                    top: 0;
                    height: 100vh;
                    z-index: 1000;
                    box-shadow: 4px 0 15px rgba(0,0,0,0.1);
                    overflow-y: auto;
                    flex-shrink: 0;
                }

                .sidebar-header {
                    padding: 30px 25px;
                    border-bottom: 1px solid rgba(255,255,255,0.1);
                    text-align: center;
                }

                .sidebar-menu {
                    padding: 20px 0;
                    display: flex;
                    flex-direction: column;
                    min-height: calc(100vh - 120px);
                }

                .menu-item {
                    display: flex;
                    align-items: center;
                    padding: 15px 25px;
                    color: rgba(255,255,255,0.7);
                    text-decoration: none;
                    transition: all 0.3s ease;
                    border-left: 3px solid transparent;
                    cursor: pointer;
                }

                .menu-item:hover,
                .menu-item.active {
                    background: rgba(255,255,255,0.1);
                    color: white;
                    border-left-color: #3b82f6;
                    text-decoration: none;
                }

                .menu-header {
                    padding: 20px 25px 10px;
                    font-size: 12px;
                    font-weight: 600;
                    color: rgba(255,255,255,0.5);
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }

                .main-content {
                    flex: 1;
                    padding: 30px;
                    min-width: 0;
                    min-height: 100vh;
                }

                .top-bar {
                    background: #ffffff;
                    padding: 20px 30px;
                    border-radius: 15px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    margin-bottom: 30px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .top-bar-actions {
                    display: flex;
                    gap: 10px;
                    align-items: center;
                }

                .content-wrapper {
                    background: #ffffff;
                    border-radius: 15px;
                    padding: 30px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    min-height: 600px;
                }

                .welcome-card {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-radius: 15px;
                    padding: 30px;
                    margin-bottom: 30px;
                    text-align: center;
                }

                .stats-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin-bottom: 30px;
                }

                .stat-card {
                    background: #ffffff;
                    border: 1px solid #e5e7eb;
                    border-radius: 15px;
                    padding: 25px;
                    text-align: center;
                    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
                    transition: all 0.3s ease;
                }

                .stat-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                }

                .stat-number {
                    font-size: 2.5rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin: 10px 0;
                }

                .stat-label {
                    color: #6b7280;
                    font-size: 14px;
                    font-weight: 500;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .sidebar {
                        width: 100%;
                        transform: translateX(-100%);
                        transition: transform 0.3s ease;
                    }
                    
                    .sidebar.show {
                        transform: translateX(0);
                    }
                    
                    .main-content {
                        margin-left: 0;
                        width: 100%;
                        padding: 15px;
                    }
                    
                    .top-bar {
                        padding: 15px 20px;
                        flex-direction: column;
                        gap: 15px;
                    }
                    
                    .stats-grid {
                        grid-template-columns: 1fr;
                    }
                }

                /* 加载状态 */
                .loading-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                }

                .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid #f3f4f6;
                    border-top: 4px solid #3b82f6;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
    }

    /**
     * 生成菜单项（四层架构版本）
     * @param {string} userType 用户类型
     * @returns {string} 菜单HTML
     */
    generateMenuItems(userType) {
        const user = this.auth.getUser();
        const isStaff = user && user.is_staff == 1;
        const tenantType = this.tenantInfo ? this.tenantInfo.tenant_type : 'provider';
        
        console.log('🎯 生成菜单项:', { userType, tenantType, isStaff });

        // 基于租户类型生成菜单配置
        let menuConfig = [];

        switch (tenantType) {
            case 'system_admin':
                menuConfig = this.getSystemAdminMenuItems();
                break;
            case 'platform_admin':
                menuConfig = this.getPlatformAdminMenuItems();
                break;
            case 'provider':
                menuConfig = this.getProviderMenuItems();
                break;
            case 'merchant':
                menuConfig = this.getMerchantMenuItems();
                break;
            default:
                console.warn('⚠️ 未知的租户类型，使用默认菜单:', tenantType);
                menuConfig = this.getProviderMenuItems();
        }

        // 如果是员工账户，过滤掉管理类菜单
        if (isStaff) {
            menuConfig = menuConfig.filter(item => 
                !['employees', 'job-positions', 'platform-users'].includes(item.id)
            );
        }

        this.menuItems = menuConfig;

        return menuConfig.map(item => {
            if (item.type === 'header') {
                return `<div class="menu-header">${item.text}</div>`;
            }
            return `
                <a href="#" class="menu-item" data-page="${item.id}">
                    <i class="${item.icon} me-3"></i>${item.text}
                </a>
            `;
        }).join('');
    }

    /**
     * 获取系统管理员菜单项
     * @returns {Array} 菜单项数组
     */
    getSystemAdminMenuItems() {
        return [
            { id: 'dashboard', icon: 'bi-speedometer2', text: '系统总览' },
            { type: 'header', text: '平台管理' },
            { id: 'platforms', icon: 'bi-building-gear', text: '平台管理' },
            { id: 'domain-management', icon: 'bi-globe', text: '域名管理' },
            { id: 'users', icon: 'bi-people', text: '系统用户' },
            { type: 'header', text: 'App版本管理' },
            { id: 'app-versions', icon: 'bi-phone-vibrate', text: '版本发布管理' },
            { id: 'provider-apps', icon: 'bi-app-indicator', text: '码商App管理' },
            { id: 'update-push', icon: 'bi-broadcast', text: '更新推送管理' },
            { type: 'header', text: '脚本管理' },
            { id: 'system-scripts', icon: 'bi-terminal', text: '系统脚本' },
            { id: 'business-scripts', icon: 'bi-code-square', text: '业务脚本' },
            { id: 'script-execution', icon: 'bi-play-circle', text: '脚本执行' },
            { type: 'header', text: '高级监控' },
            { id: 'system-monitoring', icon: 'bi-activity', text: '系统监控' },
            { id: 'server-monitor', icon: 'bi-cpu', text: '服务器监控' },
            { id: 'database-monitor', icon: 'bi-database', text: '数据库监控' },
            { id: 'application-monitor', icon: 'bi-speedometer', text: '应用监控' },
            { id: 'performance-monitor', icon: 'bi-speedometer2', text: '性能监控' },
            { id: 'security-logs', icon: 'bi-shield-exclamation', text: '安全日志' },
            { type: 'header', text: '全系统财务' },
            { id: 'global-finance', icon: 'bi-cash-stack', text: '全系统财务概览' },
            { type: 'header', text: '系统设置' },
            { id: 'system-settings', icon: 'bi-gear', text: '系统设置' },
            { id: 'backup-restore', icon: 'bi-archive', text: '备份恢复' }
        ];
    }

    /**
     * 获取平台管理员菜单项
     * @returns {Array} 菜单项数组
     */
    getPlatformAdminMenuItems() {
        return [
            { id: 'dashboard', icon: 'bi-speedometer2', text: '平台总览' },
            { type: 'header', text: '团队管理' },
            { id: 'employees', icon: 'bi-person-badge', text: '员工管理' },
            { id: 'job-positions', icon: 'bi-diagram-3', text: '职位管理' },
            { type: 'header', text: '码商管理' },
            { id: 'providers', icon: 'bi-building', text: '码商列表' },
            { id: 'alipay', icon: 'bi-credit-card', text: '支付宝账户' },
            { type: 'header', text: '商户管理' },
            { id: 'merchants', icon: 'bi-shop', text: '商户列表' },
            { id: 'products', icon: 'bi-box-seam', text: '产品管理' },
            { type: 'header', text: '财务系统' },
            { id: 'financial', icon: 'bi-graph-up', text: '财务管理' },
            { id: 'realtime-data', icon: 'bi-speedometer2', text: '实时数据' },
            { id: 'fee-config', icon: 'bi-gear', text: '手续费配置' },
            { id: 'settlement-mgmt', icon: 'bi-bank', text: '结算管理' },
            { id: 'finance-reports', icon: 'bi-file-earmark-bar-graph', text: '财务报表' },
            { id: 'transactions', icon: 'bi-receipt', text: '交易记录' },
            { type: 'header', text: '系统管理' },
            { id: 'risk-control', icon: 'bi-shield-check', text: '风控管理' },
            { id: 'notifications', icon: 'bi-bell', text: '通知管理' },
            { id: 'platform-config', icon: 'bi-sliders', text: '平台配置' },
            { id: 'blacklist', icon: 'bi-shield-exclamation', text: '黑名单管理' }
        ];
    }

    /**
     * 获取码商菜单项
     * @returns {Array} 菜单项数组
     */
    getProviderMenuItems() {
        return [
            { id: 'dashboard', icon: 'bi-speedometer2', text: '仪表板' },
            { type: 'header', text: '团队管理' },
            { id: 'employees', icon: 'bi-person-badge', text: '员工管理' },
            { id: 'job-positions', icon: 'bi-diagram-3', text: '职位管理' },
            { type: 'header', text: '设备管理' },
            { id: 'devices', icon: 'bi-phone', text: '设备列表' },
            { id: 'device-groups', icon: 'bi-collection', text: '设备小组' },
            { id: 'device-monitor', icon: 'bi-activity', text: '设备监控' },
            { id: 'alipay-accounts', icon: 'bi-credit-card', text: '支付宝账户' },
            { id: 'totp', icon: 'bi-shield-lock', text: 'TOTP管理' },
            { type: 'header', text: '订单管理' },
            { id: 'realtime-orders', icon: 'bi-receipt', text: '实时订单' },
            { id: 'order-process', icon: 'bi-gear', text: '订单处理' },
            { id: 'order-stats', icon: 'bi-bar-chart', text: '订单统计' },
            { id: 'order-export', icon: 'bi-download', text: '订单导出' },
            { type: 'header', text: '风控管理' },
            { id: 'risk-rules', icon: 'bi-shield-check', text: '风控规则' },
            { id: 'blacklist-mgmt', icon: 'bi-shield-exclamation', text: '黑名单管理' },
            { id: 'risk-monitor', icon: 'bi-eye', text: '风险监控' },
            { id: 'risk-reports', icon: 'bi-file-text', text: '风控报表' },
            { type: 'header', text: '财务管理' },
            { id: 'flow-records', icon: 'bi-list-ul', text: '流水记录' },
            { id: 'finance-settlement', icon: 'bi-bank2', text: '财务结算' },
            { id: 'realtime-balance', icon: 'bi-wallet2', text: '实时余额' },
            { id: 'finance-reconcile', icon: 'bi-check2-square', text: '财务对账' },
            { id: 'transactions', icon: 'bi-receipt', text: '交易记录' }
        ];
    }

    /**
     * 获取商户菜单项
     * @returns {Array} 菜单项数组
     */
    getMerchantMenuItems() {
        return [
            { id: 'merchant-dashboard', icon: 'bi-speedometer2', text: '仪表板' },
            { type: 'header', text: '业务管理' },
            { id: 'merchant-orders', icon: 'bi-receipt', text: '我的订单' },
            { id: 'order-records', icon: 'bi-receipt', text: '订单记录' },
            { id: 'business-analysis', icon: 'bi-graph-up', text: '业务分析' },
            { id: 'merchant-stats', icon: 'bi-graph-up', text: '数据统计' },
            { id: 'merchant-products', icon: 'bi-box-seam', text: '产品管理' },
            { type: 'header', text: '开发者工具' },
            { id: 'api-settings', icon: 'bi-gear', text: 'API设置' },
            { id: 'api-test', icon: 'bi-play-circle', text: '接口测试' },
            { id: 'code-generator', icon: 'bi-code-square', text: '代码生成器' },
            { id: 'signature-tool', icon: 'bi-shield-check', text: '签名工具' },
            { id: 'api-docs', icon: 'bi-book', text: '接口文档' },
            { id: 'dev-support', icon: 'bi-question-circle', text: '开发支持' },
            { type: 'header', text: '财务管理' },
            { id: 'realtime-flow', icon: 'bi-speedometer2', text: '实时流水' },
            { id: 'finance-reconcile', icon: 'bi-check2-all', text: '财务对账' },
            { id: 'settlement-records', icon: 'bi-bank', text: '结算记录' },
            { id: 'realtime-balance', icon: 'bi-wallet', text: '实时余额' },
            { id: 'finance-reports', icon: 'bi-file-earmark-spreadsheet', text: '财务报表' },
            { type: 'header', text: '系统设置' },
            { id: 'merchant-config', icon: 'bi-gear', text: '系统配置' },
            { id: 'merchant-profile', icon: 'bi-person-circle', text: '商户资料' },
            { id: 'employees', icon: 'bi-person-badge', text: '员工管理' },
            { id: 'job-positions', icon: 'bi-diagram-3', text: '职位管理' }
        ];
    }

    /**
     * 生成仪表板内容
     * @param {Object} user 用户信息
     * @returns {string} 仪表板HTML
     */
    generateDashboardContent(user) {
        return `
            <!-- 欢迎信息 -->
            <div class="welcome-card">
                <h2>欢迎回来，${user.display_name || user.real_name || user.username}！</h2>
                <p>您的身份：${this.getUserTypeText(user.user_type)}</p>
                <small>上次登录：${new Date().toLocaleString()}</small>
            </div>

            <!-- 统计数据 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="bi bi-people" style="font-size: 2rem; color: #3b82f6;"></i>
                    <div class="stat-number" id="stat-users">-</div>
                    <div class="stat-label">总用户数</div>
                </div>
                <div class="stat-card">
                    <i class="bi bi-phone" style="font-size: 2rem; color: #10b981;"></i>
                    <div class="stat-number" id="stat-devices">-</div>
                    <div class="stat-label">活跃设备</div>
                </div>
                <div class="stat-card">
                    <i class="bi bi-building" style="font-size: 2rem; color: #f59e0b;"></i>
                    <div class="stat-number" id="stat-providers">-</div>
                    <div class="stat-label">码商数量</div>
                </div>
                <div class="stat-card">
                    <i class="bi bi-graph-up" style="font-size: 2rem; color: #ef4444;"></i>
                    <div class="stat-number" id="stat-transactions">-</div>
                    <div class="stat-label">今日交易额</div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-lightning-charge me-2"></i>快速操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" data-quick-action="add-merchant">
                                    <i class="bi bi-shop-plus me-2"></i>添加商户
                                </button>
                                <button class="btn btn-success" data-quick-action="add-provider">
                                    <i class="bi bi-building-add me-2"></i>添加码商
                                </button>
                                <button class="btn btn-info" data-quick-action="view-transactions">
                                    <i class="bi bi-receipt me-2"></i>查看交易
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-clock-history me-2"></i>最近活动</h5>
                        </div>
                        <div class="card-body">
                            <div id="recent-activities">
                                <div class="text-center text-muted">
                                    <i class="bi bi-clock"></i>
                                    <p>正在加载最近活动...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 初始化界面功能
     */
    initializeInterface() {
        this.initializeMenuEvents();
        this.initializeTopBarEvents();
        this.initializeQuickActions();
        this.initializeRouterManager();
        this.loadDashboardStats();
    }

    /**
     * 初始化路由管理器
     */
    initializeRouterManager() {
        try {
            if (window.routerManager) {
                // 设置路由管理器的依赖
                window.routerManager.setManagers({
                    moduleLoader: window.AdminModuleLoader,
                    uiManager: this,
                    authManager: this.auth,
                    apiClient: window.AdminApiClient || window.apiClient,
                    utils: window.utils
                });
                
                console.log('✅ 路由管理器依赖设置完成');
            } else {
                console.warn('⚠️ 路由管理器未找到，使用传统导航方式');
            }
        } catch (error) {
            console.error('❌ 路由管理器初始化失败:', error);
        }
    }

    /**
     * 初始化菜单事件
     */
    initializeMenuEvents() {
        const menuItems = document.querySelectorAll('.menu-item[data-page]');
        
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.getAttribute('data-page');
                this.navigateToPage(page, item);
            });
        });

        // 默认激活仪表板
        const dashboardItem = document.querySelector('.menu-item[data-page="dashboard"]');
        if (dashboardItem) {
            dashboardItem.classList.add('active');
        }
    }

    /**
     * 初始化顶部栏事件
     */
    initializeTopBarEvents() {
        // 刷新按钮
        const refreshBtn = document.querySelector('.refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshCurrentPage();
            });
        }

        // 退出登录按钮
        const logoutBtns = document.querySelectorAll('.logout-btn');
        logoutBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogout();
            });
        });

        // 下拉菜单操作
        const dropdownItems = document.querySelectorAll('.dropdown-item[data-action]');
        dropdownItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const action = item.getAttribute('data-action');
                this.handleTopBarAction(action);
            });
        });
    }

    /**
     * 初始化快速操作
     */
    initializeQuickActions() {
        const quickActionBtns = document.querySelectorAll('[data-quick-action]');
        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.getAttribute('data-quick-action');
                this.handleQuickAction(action);
            });
        });
    }

    /**
     * 页面导航 - 使用路由管理器
     * @param {string} page 页面ID
     * @param {HTMLElement} menuItem 菜单项元素
     */
    navigateToPage(page, menuItem) {
        if (this.currentPage === page) return;

        // 使用路由管理器进行导航
        if (window.routerManager) {
            window.routerManager.navigateByMenuId(page);
        } else {
            // 降级处理：使用原有的导航方式
            this.legacyNavigateToPage(page, menuItem);
        }

        this.currentPage = page;
        console.log(`导航到页面: ${page}`);
    }

    /**
     * 传统页面导航方式（降级处理）
     * @param {string} page 页面ID
     * @param {HTMLElement} menuItem 菜单项元素
     */
    legacyNavigateToPage(page, menuItem) {
        // 更新菜单状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
        if (menuItem) {
            menuItem.classList.add('active');
        }

        // 更新页面标题
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle && menuItem) {
            pageTitle.textContent = menuItem.textContent.trim();
        }

        // 加载页面内容
        this.loadPageContent(page).catch(error => {
            console.error('页面加载失败:', error);
        });
    }

    /**
     * 加载页面内容
     * @param {string} page 页面ID
     */
    async loadPageContent(page) {
        const contentWrapper = document.querySelector('.content-wrapper');
        if (!contentWrapper) return;

        // 检查租户权限
        if (!this.canAccessPage(page)) {
            this.showAccessDeniedPage(contentWrapper, page);
            return;
        }

        // 显示加载状态
        this.showLoading(true);

        try {
            // 检查是否有自定义加载处理器
            if (this.pageLoadHandlers.has(page)) {
                const handler = this.pageLoadHandlers.get(page);
                try {
                    await handler(contentWrapper);
                } catch (error) {
                    console.error(`页面加载处理器错误 (${page}):`, error);
                    this.showErrorPage(contentWrapper, `加载页面失败: ${error.message}`);
                }
            } else {
                // 使用默认的页面加载逻辑
                await this.loadDefaultPageContent(page, contentWrapper);
            }
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 检查用户是否有权限访问指定页面
     * @param {string} pageId 页面ID
     * @returns {boolean} 是否有权限访问
     */
    canAccessPage(pageId) {
        if (!this.tenantInfo) {
            console.warn('⚠️ 租户信息未初始化，拒绝访问页面:', pageId);
            return false;
        }

        const tenantType = this.tenantInfo.tenant_type;
        
        // 定义各租户类型可访问的页面
        const pagePermissions = {
            'system_admin': [
                // 基础页面
                'dashboard', 'platforms', 'domain-management', 'users', 'system-users',
                // App版本管理
                'app-versions', 'provider-apps', 'update-push',
                // 脚本管理
                'system-scripts', 'business-scripts', 'script-execution', 'scripts',
                // 高级监控
                'system-monitoring', 'server-monitor', 'database-monitor', 'application-monitor',
                'performance-monitor', 'security-logs',
                // 全系统财务
                'global-finance',
                // 系统设置
                'system-settings', 'backup-restore'
            ],
            'platform_admin': [
                // 基础页面
                'dashboard', 'users', 'employees', 'job-positions', 'products', 'devices',
                // 码商管理
                'providers',
                // 商户管理
                'merchants', 'merchant-audit', 'merchant-service',
                // 财务系统
                'financial', 'realtime-data', 'fee-config', 'settlement-mgmt', 'finance-reports',
                'transactions', 'alipay',
                // 系统管理
                'risk-control', 'notifications', 'platform-config', 'blacklist', 'scripts',
                'security-logs', 'performance-monitor'
            ],
            'provider': [
                // 基础页面
                'dashboard', 'employees', 'job-positions', 'totp',
                // 设备管理
                'devices', 'device-groups', 'device-monitor', 'alipay-accounts',
                // 订单管理
                'realtime-orders', 'order-process', 'order-stats', 'order-export',
                // 风控管理
                'risk-rules', 'blacklist-mgmt', 'risk-monitor', 'risk-reports',
                // 财务管理
                'flow-records', 'finance-settlement', 'realtime-balance', 'finance-reconcile',
                'transactions', 'alipay'
            ],
            'merchant': [
                // 基础页面
                'merchant-dashboard', 'employees', 'job-positions',
                // 业务管理
                'merchant-orders', 'order-records', 'business-analysis', 'merchant-stats', 'merchant-products',
                // 开发者工具
                'api-keys', 'api-settings', 'api-test', 'code-generator', 'signature-tool', 'api-docs', 'dev-support',
                // 财务管理
                'realtime-flow', 'finance-reconcile', 'settlement-records', 'realtime-balance', 'finance-reports',
                // 系统设置
                'merchant-config'
            ]
        };

        const allowedPages = pagePermissions[tenantType] || [];
        const hasAccess = allowedPages.includes(pageId);
        
        if (!hasAccess) {
            console.warn(`⚠️ 租户类型 ${tenantType} 无权访问页面: ${pageId}`);
        }
        
        return hasAccess;
    }

    /**
     * 显示访问被拒绝页面
     * @param {HTMLElement} container 容器元素
     * @param {string} pageId 页面ID
     */
    showAccessDeniedPage(container, pageId) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-shield-exclamation text-warning" style="font-size: 4rem;"></i>
                <h3 class="mt-3">访问被拒绝</h3>
                <p class="text-muted">您没有权限访问此页面</p>
                <div class="mt-3">
                    <p class="text-muted"><strong>页面ID：</strong>${pageId}</p>
                    <p class="text-muted"><strong>当前租户类型：</strong>${this.getTenantTypeText()}</p>
                    <p class="text-muted"><strong>当前域名：</strong>${window.location.hostname}</p>
                </div>
                <div class="mt-4">
                    <button class="btn btn-primary me-2" onclick="window.uiManager.navigateToPage('dashboard')">
                        <i class="bi bi-house me-2"></i>返回首页
                    </button>
                    <button class="btn btn-outline-secondary" onclick="history.back()">
                        <i class="bi bi-arrow-left me-2"></i>返回上一页
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 加载默认页面内容
     * @param {string} page 页面ID
     * @param {HTMLElement} container 容器元素
     */
    async loadDefaultPageContent(page, container) {
        // 根据页面ID调用相应的加载函数
        const pageLoadMap = {
            // 基础页面
            'dashboard': () => this.loadDashboard(container),
            'platforms': () => this.loadPlatformsPage(container),
            'domain-management': () => this.loadDomainManagementPage(container),

            'employees': () => this.loadEmployeesPage(container),
            'job-positions': () => this.loadJobPositionsPage(container),
            'providers': () => this.loadProvidersPage(container),
            'merchants': () => this.loadMerchantsPage(container),
            'products': () => this.loadProductsPage(container),
            'financial': () => this.loadFinancialPage(container),
            'transactions': () => this.loadTransactionsPage(container),
            'alipay': () => this.loadAlipayPage(container),
            'risk-control': () => this.loadRiskControlPage(container),
            'notifications': () => this.loadNotificationsPage(container),
            'blacklist': () => this.loadBlacklistPage(container),

            'totp': () => this.loadTOTPPage(container),

            // 系统管理员专用页面
            'app-versions': () => this.loadAppVersionsPage(container),
            'provider-apps': () => this.loadProviderAppsPage(container),
            'update-push': () => this.loadUpdatePushPage(container),
            'system-scripts': () => this.loadSystemScriptsPage(container),
            'business-scripts': () => this.loadBusinessScriptsPage(container),
            'script-execution': () => this.loadScriptExecutionPage(container),
            'system-monitoring': () => this.loadSystemMonitoringPage(container),
            'server-monitor': () => this.loadServerMonitorPage(container),
            'database-monitor': () => this.loadDatabaseMonitorPage(container),
            'application-monitor': () => this.loadApplicationMonitorPage(container),
            'global-finance': () => this.loadGlobalFinancePage(container),
            'system-settings': () => this.loadSystemSettingsPage(container),
            'backup-restore': () => this.loadBackupRestorePage(container),

            // 平台管理员专用页面
            'realtime-data': () => this.loadRealtimeDataPage(container),
            'fee-config': () => this.loadFeeConfigPage(container),
            'settlement-mgmt': () => this.loadSettlementMgmtPage(container),
            'finance-reports': () => this.loadFinanceReportsPage(container),
            'platform-config': () => this.loadPlatformConfigPage(container),

            // 码商专用页面
            'device-groups': () => this.loadDeviceGroupsPage(container),
            'device-monitor': () => this.loadDeviceMonitorPage(container),
            'alipay-accounts': () => this.loadAlipayAccountsPage(container),
            'realtime-orders': () => this.loadRealtimeOrdersPage(container),
            'order-process': () => this.loadOrderProcessPage(container),
            'order-stats': () => this.loadOrderStatsPage(container),
            'order-export': () => this.loadOrderExportPage(container),
            'risk-rules': () => this.loadRiskRulesPage(container),
            'blacklist-mgmt': () => this.loadBlacklistMgmtPage(container),
            'risk-monitor': () => this.loadRiskMonitorPage(container),
            'risk-reports': () => this.loadRiskReportsPage(container),
            'flow-records': () => this.loadFlowRecordsPage(container),
            'finance-settlement': () => this.loadFinanceSettlementPage(container),
            'realtime-balance': () => this.loadRealtimeBalancePage(container),
            'finance-reconcile': () => this.loadFinanceReconcilePage(container),

            // 商户专用页面
            'merchant-dashboard': () => this.loadMerchantDashboard(container),
            'merchant-orders': () => this.loadMerchantOrdersPage(container),
            'order-records': () => this.loadOrderRecordsPage(container),
            'business-analysis': () => this.loadBusinessAnalysisPage(container),
            'merchant-stats': () => this.loadMerchantStatsPage(container),
            'merchant-products': () => this.loadMerchantProductsPage(container),
            'api-keys': () => this.loadApiKeysPage(container),
            'api-settings': () => this.loadApiSettingsPage(container),
            'api-test': () => this.loadApiTestPage(container),
            'code-generator': () => this.loadCodeGeneratorPage(container),
            'signature-tool': () => this.loadSignatureToolPage(container),
            'api-docs': () => this.loadApiDocsPage(container),
            'dev-support': () => this.loadDevSupportPage(container),
            'realtime-flow': () => this.loadRealtimeFlowPage(container),
            'settlement-records': () => this.loadSettlementRecordsPage(container),
            'merchant-config': () => this.loadMerchantConfigPage(container)
        };

        const loadFunction = pageLoadMap[page];
        if (loadFunction) {
            await loadFunction();
        } else {
            // 显示开发中页面
            this.showDevelopmentPage(container, page);
        }
    }

    /**
     * 注册页面加载处理器
     * @param {string} page 页面ID
     * @param {Function} handler 处理函数
     */
    registerPageHandler(page, handler) {
        this.pageLoadHandlers.set(page, handler);
    }

    /**
     * 获取租户显示名称
     * @returns {string} 租户显示名称
     */
    getTenantDisplayName() {
        if (!this.tenantInfo) {
            return '控制面板';
        }

        const tenantType = this.tenantInfo.tenant_type;
        switch (tenantType) {
            case 'system_admin':
                return '系统管理后台';
            case 'platform_admin':
                return `${this.tenantInfo.platform_name || '平台'} 管理后台`;
            case 'provider':
                return `${this.tenantInfo.brand_name || '码商'} 管理系统`;
            case 'merchant':
                return `${this.tenantInfo.brand_name || '商户'} 管理系统`;
            default:
                return '控制面板';
        }
    }

    /**
     * 获取租户类型文本
     * @returns {string} 租户类型文本
     */
    getTenantTypeText() {
        if (!this.tenantInfo) {
            return '';
        }

        const tenantType = this.tenantInfo.tenant_type;
        const typeMap = {
            'system_admin': '系统管理员',
            'platform_admin': '平台管理员',
            'provider': '码商',
            'merchant': '商户'
        };
        return typeMap[tenantType] || '';
    }

    /**
     * 获取用户类型文本
     * @param {string} userType 用户类型
     * @returns {string} 用户类型文本
     */
    getUserTypeText(userType) {
        const typeMap = {
            'admin': '系统管理员',
            'provider': '支付码商',
            'merchant': '商户用户'
        };
        return typeMap[userType] || '未知用户';
    }

    /**
     * 显示加载状态
     * @param {boolean} show 是否显示
     * @param {string} message 加载消息
     */
    showLoading(show, message = '加载中...') {
        let overlay = document.querySelector('.loading-overlay');
        
        if (show) {
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                document.body.appendChild(overlay);
            }
            overlay.innerHTML = `
                <div class="text-center">
                    <div class="loading-spinner mb-3"></div>
                    <p class="text-muted">${message}</p>
                </div>
            `;
            overlay.style.display = 'flex';
        } else {
            if (overlay) {
                overlay.style.display = 'none';
            }
        }
    }

    /**
     * 显示消息提示
     * @param {string} message 消息内容
     * @param {string} type 消息类型 (success, error, warning, info)
     */
    showMessage(message, type = 'info') {
        // 移除现有的消息提示
        const existingToast = document.querySelector('.toast-message');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建新的消息提示
        const toast = document.createElement('div');
        toast.className = `toast-message alert alert-${this.getAlertClass(type)} alert-dismissible fade show`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        toast.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi ${this.getIconClass(type)} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // 自动隐藏
        setTimeout(() => {
            if (toast && toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }

    /**
     * 获取Bootstrap alert类名
     * @param {string} type 消息类型
     * @returns {string} Bootstrap类名
     */
    getAlertClass(type) {
        const classMap = {
            'success': 'success',
            'error': 'danger',
            'warning': 'warning',
            'info': 'info'
        };
        return classMap[type] || 'info';
    }

    /**
     * 获取图标类名
     * @param {string} type 消息类型
     * @returns {string} 图标类名
     */
    getIconClass(type) {
        const iconMap = {
            'success': 'bi-check-circle',
            'error': 'bi-exclamation-circle',
            'warning': 'bi-exclamation-triangle',
            'info': 'bi-info-circle'
        };
        return iconMap[type] || 'bi-info-circle';
    }

    /**
     * 刷新当前页面
     */
    refreshCurrentPage() {
        this.loadPageContent(this.currentPage).catch(error => {
            console.error('页面刷新失败:', error);
        });
    }

    /**
     * 处理退出登录
     */
    handleLogout() {
        if (confirm('确定要退出登录吗？')) {
            this.auth.logout();
        }
    }

    /**
     * 处理顶部栏操作
     * @param {string} action 操作类型
     */
    handleTopBarAction(action) {
        switch (action) {
            case 'profile':
                this.navigateToPage('profile');
                break;
            case 'settings':
                this.navigateToPage('settings');
                break;
            default:
                console.log(`未处理的顶部栏操作: ${action}`);
        }
    }

    /**
     * 处理快速操作
     * @param {string} action 操作类型
     */
    handleQuickAction(action) {
        switch (action) {
            case 'add-merchant':
                this.navigateToPage('merchants');
                break;
            case 'add-provider':
                this.navigateToPage('providers');
                break;
            case 'view-transactions':
                this.navigateToPage('transactions');
                break;
            default:
                console.log(`未处理的快速操作: ${action}`);
        }
    }

    /**
     * 加载仪表板页面
     * @param {HTMLElement} container 容器元素
     */
    loadDashboard(container) {
        console.log('加载仪表板页面...');
        
        // 重新生成仪表板内容
        const user = this.auth.getUser();
        container.innerHTML = this.generateDashboardContent(user);
        
        // 重新加载统计数据
        this.loadDashboardStats();
        
        // 重新绑定快速操作事件
        this.initializeQuickActions();
    }

    /**
     * 加载仪表板统计数据
     */
    async loadDashboardStats() {
        try {
            // 这里可以调用API获取统计数据
            // 暂时使用模拟数据
            setTimeout(() => {
                document.getElementById('stat-users').textContent = '156';
                document.getElementById('stat-devices').textContent = '42';
                document.getElementById('stat-providers').textContent = '8';
                document.getElementById('stat-transactions').textContent = '¥12,345';
            }, 500);
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }





    /**
     * 加载交易管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadTransactionsPage(container) {
        console.log('加载交易管理页面...');
        
        // 检查是否有交易管理模块
        if (window.TransactionManagementModule) {
            try {
                const transactionManager = new window.TransactionManagementModule.TransactionManager();
                transactionManager.render(container);
            } catch (error) {
                console.error('交易管理模块加载失败:', error);
                this.showErrorPage(container, '交易管理模块加载失败');
            }
        } else {
            // 显示基础的交易管理页面
            container.innerHTML = `
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-credit-card me-2"></i>交易管理
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" placeholder="搜索订单号...">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select">
                                            <option>全部状态</option>
                                            <option>待支付</option>
                                            <option>已支付</option>
                                            <option>已取消</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="date" class="form-control">
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-primary">搜索</button>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>订单号</th>
                                                <th>金额</th>
                                                <th>状态</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>TXN001</td>
                                                <td>¥100.00</td>
                                                <td><span class="badge bg-success">已支付</span></td>
                                                <td>2024-01-15 10:30</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary">详情</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>TXN002</td>
                                                <td>¥50.00</td>
                                                <td><span class="badge bg-warning">待支付</span></td>
                                                <td>2024-01-15 10:25</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary">详情</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 显示开发中页面
     * @param {HTMLElement} container 容器
     * @param {string} page 页面ID
     */
    showDevelopmentPage(container, page) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-tools" style="font-size: 4rem; color: #6b7280;"></i>
                <h3 class="mt-3">页面开发中</h3>
                <p class="text-muted">页面 "${page}" 正在开发中，敬请期待...</p>
                <button class="btn btn-primary" onclick="history.back()">返回上一页</button>
            </div>
        `;
    }

    /**
     * 加载员工管理页面
     */
    loadEmployeesPage(container) {
        console.log('加载员工管理页面...');
        
        // 使用统一的业务模块加载器
        this.loadBusinessModule(container, 'employee-management', '员工管理');
    }

    /**
     * 加载职位管理页面
     */
    loadJobPositionsPage(container) {
        console.log('加载职位管理页面...');
        
        // 使用统一的业务模块加载器
        this.loadBusinessModule(container, 'job-position-management', '职位管理');
    }

    /**
     * 加载码商管理页面
     */
    async loadProvidersPage(container) {
        await this.loadBusinessModule(container, 'provider-management', '码商管理');
    }

    /**
     * 加载商户管理页面
     */
    async loadMerchantsPage(container) {
        await this.loadBusinessModule(container, 'merchant-management', '商户管理');
    }

    /**
     * 加载产品管理页面
     */
    async loadProductsPage(container) {
        await this.loadBusinessModule(container, 'product-management', '产品管理');
    }

    /**
     * 加载财务管理页面
     */
    async loadFinancialPage(container) {
        await this.loadBusinessModule(container, 'finance-management', '财务管理');
    }

    /**
     * 加载支付宝账户页面
     */
    async loadAlipayPage(container) {
        await this.loadBusinessModule(container, 'payment-management', '支付宝账户');
    }

    /**
     * 加载风控管理页面
     */
    loadRiskControlPage(container) {
        this.loadSecurityModule(container, 'security-management', '风控管理', 'RiskControlManager');
    }

    /**
     * 加载通知管理页面
     */
    loadNotificationsPage(container) {
        this.loadSystemModule(container, 'system-monitoring', '通知管理', 'NotificationManager');
    }

    /**
     * 加载黑名单管理页面
     */
    loadBlacklistPage(container) {
        this.loadSecurityModule(container, 'security-management', '黑名单管理', 'BlacklistManager');
    }



    /**
     * 加载TOTP管理页面
     */
    loadTOTPPage(container) {
        this.loadSecurityModule(container, 'security-management', 'TOTP管理', 'TOTPManager');
    }

    // ========== 系统管理员专用页面 ==========

    /**
     * 加载App版本管理页面
     */
    loadAppVersionsPage(container) {
        this.loadSystemModule(container, 'app-management', 'App版本管理');
    }

    /**
     * 加载码商App管理页面
     */
    loadProviderAppsPage(container) {
        this.loadSystemModule(container, 'app-management', '码商App管理');
    }

    /**
     * 加载更新推送管理页面
     */
    loadUpdatePushPage(container) {
        this.loadSystemModule(container, 'app-management', '更新推送管理');
    }

    /**
     * 加载系统脚本页面
     */
    loadSystemScriptsPage(container) {
        this.loadSystemModule(container, 'script-management', '系统脚本', 'ScriptManager');
    }

    /**
     * 加载业务脚本页面
     */
    loadBusinessScriptsPage(container) {
        this.loadSystemModule(container, 'script-management', '业务脚本', 'ScriptManager');
    }

    /**
     * 加载脚本执行页面
     */
    loadScriptExecutionPage(container) {
        this.loadSystemModule(container, 'script-management', '脚本执行', 'ScriptManager');
    }

    /**
     * 加载系统监控页面
     */
    loadSystemMonitoringPage(container) {
        this.loadSystemModule(container, 'system-monitoring', '系统监控', 'PerformanceManager');
    }

    /**
     * 加载服务器监控页面
     */
    loadServerMonitorPage(container) {
        this.loadSystemModule(container, 'system-monitoring', '服务器监控', 'PerformanceManager');
    }

    /**
     * 加载数据库监控页面
     */
    loadDatabaseMonitorPage(container) {
        this.loadSystemModule(container, 'system-monitoring', '数据库监控', 'PerformanceManager');
    }

    /**
     * 加载应用监控页面
     */
    loadApplicationMonitorPage(container) {
        this.loadSystemModule(container, 'system-monitoring', '应用监控', 'PerformanceManager');
    }

    /**
     * 加载全系统财务概览页面
     */
    loadGlobalFinancePage(container) {
        this.loadSystemModule(container, 'global-finance', '全系统财务概览', 'FinancialManager');
    }

    /**
     * 加载系统设置页面
     */
    loadSystemSettingsPage(container) {
        this.loadSystemModule(container, 'system-settings', '系统设置');
    }

    /**
     * 加载备份恢复页面
     */
    loadBackupRestorePage(container) {
        this.loadSystemModule(container, 'system-settings', '备份恢复');
    }

    // ========== 平台管理员专用页面 ==========



    /**
     * 加载实时数据页面
     */
    loadRealtimeDataPage(container) {
        this.loadFinanceModule(container, 'realtime-data', '实时数据', 'RealtimeDataManager');
    }

    /**
     * 加载手续费配置页面
     */
    loadFeeConfigPage(container) {
        this.loadFinanceModule(container, 'fee-config', '手续费配置', 'FeeConfigManager');
    }

    /**
     * 加载结算管理页面
     */
    loadSettlementMgmtPage(container) {
        this.loadFinanceModule(container, 'settlement', '结算管理', 'SettlementManager');
    }

    /**
     * 加载财务报表页面
     */
    loadFinanceReportsPage(container) {
        this.loadFinanceModule(container, 'finance-reports', '财务报表', 'FinanceReportManager');
    }

    /**
     * 加载平台配置页面
     */
    loadPlatformConfigPage(container) {
        this.loadSystemModule(container, 'platform-config', '平台配置');
    }

    // ========== 码商专用页面 ==========

    /**
     * 加载设备小组页面
     */
    loadDeviceGroupsPage(container) {
        this.loadBusinessModule(container, 'device-management', '设备小组');
    }

    /**
     * 加载设备监控页面
     */
    loadDeviceMonitorPage(container) {
        this.loadBusinessModule(container, 'device-management', '设备监控');
    }

    /**
     * 加载支付宝账户页面
     */
    loadAlipayAccountsPage(container) {
        this.loadBusinessModule(container, 'account-management', '支付宝账户');
    }

    /**
     * 加载实时订单页面
     */
    loadRealtimeOrdersPage(container) {
        this.loadBusinessModule(container, 'transaction-management', '实时订单');
    }

    /**
     * 加载订单处理页面
     */
    loadOrderProcessPage(container) {
        this.loadBusinessModule(container, 'transaction-management', '订单处理');
    }

    /**
     * 加载订单统计页面
     */
    loadOrderStatsPage(container) {
        this.loadBusinessModule(container, 'transaction-management', '订单统计');
    }

    /**
     * 加载订单导出页面
     */
    loadOrderExportPage(container) {
        this.loadBusinessModule(container, 'transaction-management', '订单导出');
    }

    /**
     * 加载风控规则页面
     */
    loadRiskRulesPage(container) {
        this.loadSecurityModule(container, 'risk-management', '风控规则', 'RiskControlManager');
    }

    /**
     * 加载黑名单管理页面
     */
    loadBlacklistMgmtPage(container) {
        this.loadSecurityModule(container, 'risk-management', '黑名单管理', 'BlacklistManager');
    }

    /**
     * 加载风险监控页面
     */
    loadRiskMonitorPage(container) {
        this.loadSecurityModule(container, 'risk-management', '风险监控', 'RiskControlManager');
    }

    /**
     * 加载风控报表页面
     */
    loadRiskReportsPage(container) {
        this.loadSecurityModule(container, 'risk-management', '风控报表', 'RiskControlManager');
    }

    /**
     * 加载流水记录页面
     */
    loadFlowRecordsPage(container) {
        this.loadBusinessModule(container, 'finance-management', '流水记录');
    }

    /**
     * 加载财务结算页面
     */
    loadFinanceSettlementPage(container) {
        this.loadFinanceModule(container, 'settlement', '财务结算', 'SettlementManager');
    }

    /**
     * 加载实时余额页面
     */
    loadRealtimeBalancePage(container) {
        this.loadFinanceModule(container, 'realtime-data', '实时余额', 'RealtimeDataManager');
    }

    /**
     * 加载财务对账页面
     */
    loadFinanceReconcilePage(container) {
        this.loadFinanceModule(container, 'reconciliation', '财务对账', 'ReconciliationManager');
    }

    // ========== 商户专用页面 ==========

    /**
     * 加载订单记录页面
     */
    loadOrderRecordsPage(container) {
        this.loadMerchantModule(container, 'order-records', '订单记录');
    }

    /**
     * 加载业务分析页面
     */
    loadBusinessAnalysisPage(container) {
        this.loadMerchantModule(container, 'business-analysis', '业务分析');
    }

    /**
     * 加载API设置页面
     */
    loadApiSettingsPage(container) {
        this.loadMerchantModule(container, 'api-settings', 'API设置');
    }

    /**
     * 加载接口测试页面
     */
    loadApiTestPage(container) {
        this.loadMerchantModule(container, 'api-test', '接口测试');
    }

    /**
     * 加载开发支持页面
     */
    loadDevSupportPage(container) {
        this.loadMerchantModule(container, 'dev-support', '开发支持');
    }

    /**
     * 加载实时流水页面
     */
    loadRealtimeFlowPage(container) {
        this.loadMerchantModule(container, 'realtime-flow', '实时流水');
    }

    /**
     * 加载结算记录页面
     */
    loadSettlementRecordsPage(container) {
        this.loadMerchantModule(container, 'settlement-records', '结算记录');
    }

    /**
     * 加载商户仪表板
     */
    loadMerchantDashboard(container) {
        this.loadMerchantModule(container, 'dashboard', '商户仪表板');
    }

    /**
     * 加载商户订单页面
     */
    loadMerchantOrdersPage(container) {
        this.loadMerchantModule(container, 'orders', '商户订单');
    }

    /**
     * 加载商户统计页面
     */
    loadMerchantStatsPage(container) {
        this.loadMerchantModule(container, 'statistics', '商户统计');
    }

    /**
     * 加载商户产品页面
     */
    loadMerchantProductsPage(container) {
        this.loadMerchantModule(container, 'products', '商户产品');
    }

    /**
     * 加载API密钥页面
     */
    loadApiKeysPage(container) {
        this.loadMerchantModule(container, 'api-keys', 'API密钥');
    }

    /**
     * 加载代码生成器页面
     */
    loadCodeGeneratorPage(container) {
        this.loadMerchantModule(container, 'code-generator', '代码生成器');
    }

    /**
     * 加载签名工具页面
     */
    loadSignatureToolPage(container) {
        this.loadMerchantModule(container, 'signature-tool', '签名工具');
    }

    /**
     * 加载API文档页面
     */
    loadApiDocsPage(container) {
        this.loadMerchantModule(container, 'api-docs', 'API文档');
    }

    /**
     * 加载商户配置页面
     */
    loadMerchantConfigPage(container) {
        this.loadMerchantModule(container, 'configuration', '商户配置');
    }

    /**
     * 财务模块加载器
     */
    async loadFinanceModule(container, moduleName, pageTitle, managerClassName = null) {
        console.log(`加载财务模块: ${moduleName}`);
        
        try {
            // 显示加载状态
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-3 text-muted">正在加载${pageTitle}...</p>
                </div>
            `;

            // 财务模块映射
            const financeModuleMap = {
                'realtime-data': 'RealtimeDataManager',
                'fee-config': 'FeeConfigManager', 
                'settlement': 'SettlementManager',
                'finance-reports': 'FinanceReportManager',
                'reconciliation': 'ReconciliationManager',
                'risk-control': 'RiskControlManager'
            };

            const className = managerClassName || financeModuleMap[moduleName];
            
            if (className && window[className]) {
                const manager = new window[className]();
                if (typeof manager.initialize === 'function') {
                    await manager.initialize();
                    manager.render(container);
                } else {
                    container.innerHTML = `
                        <div class="alert alert-info">
                            <h4>${pageTitle}</h4>
                            <p>财务模块 ${className} 已加载，但缺少初始化方法。</p>
                        </div>
                    `;
                }
            } else {
                this.showErrorPage(container, `${pageTitle}模块加载失败: 找不到${className}类`);
            }
        } catch (error) {
            console.error(`财务模块加载失败:`, error);
            this.showErrorPage(container, `${pageTitle}模块加载失败: ${error.message}`);
        }
    }

    /**
     * 通用业务模块加载器
     */
    async loadBusinessModule(container, moduleName, pageTitle) {
        console.log(`加载业务模块: ${moduleName}`);
        
        const moduleMap = {
            'provider-management': 'ProviderManager',
            'merchant-management': 'MerchantManager',
            'product-management': 'ProductManager',
            'finance-management': 'FinanceManager',
            'payment-management': 'PaymentManager',
            'transaction-management': 'TransactionManager',
            'api-management': 'APIManager',
            // 商户相关模块 - 使用merchant-tools.js中的功能
            'merchant-dashboard': 'MerchantToolsManager',
            'merchant-order-management': 'MerchantToolsManager',
            'merchant-statistics': 'MerchantToolsManager',
            'merchant-product-management': 'MerchantToolsManager',
            'api-key-management': 'MerchantToolsManager',
            'code-generator': 'MerchantToolsManager',
            'signature-tool': 'MerchantToolsManager',
            'api-documentation': 'MerchantToolsManager',
            'merchant-configuration': 'MerchantToolsManager'
        };

        const moduleInstanceName = moduleMap[moduleName];
        
        // 先检查模块是否已经加载
        if (!window[moduleInstanceName]) {
            try {
                // 显示加载状态
                this.showLoading(true, `正在加载${pageTitle}模块...`);
                
                // 动态加载模块
                if (window.AdminModuleLoader) {
                    console.log(`动态加载模块: ${moduleName}`);
                    await window.AdminModuleLoader.loadModule(moduleName);
                } else {
                    console.warn('模块加载器未找到，尝试直接加载脚本');
                    // 备用方案：直接加载脚本
                    await this.loadModuleScript(moduleName);
                }
                
                // 等待一下让模块完全初始化
                await new Promise(resolve => setTimeout(resolve, 100));
            } catch (error) {
                console.error(`动态加载模块 ${moduleName} 失败:`, error);
                this.showLoading(false);
                this.showErrorPage(container, `${pageTitle}模块加载失败: ${error.message}`);
                return;
            } finally {
                this.showLoading(false);
            }
        }

        // 现在尝试使用模块
        if (window[moduleInstanceName]) {
            try {
                let manager = window[moduleInstanceName];
                
                // 如果是类构造函数，需要实例化
                if (typeof manager === 'function') {
                    console.log(`实例化模块类: ${moduleInstanceName}`);
                    manager = new manager();
                }
                
                // 准备依赖项 - 四层架构改造
                const dependencies = {
                    apiClient: window.AdminAPI || window.apiClient,
                    utils: window.AdminUtils || this.utils,
                    authManager: this.auth,
                    tenantInfo: this.tenantInfo
                };
                
                // 如果模块有init方法，传递依赖项
                if (manager.init && typeof manager.init === 'function') {
                    console.log(`初始化模块 ${moduleName}，传递依赖项:`, dependencies);
                    manager.init(dependencies);
                }
                
                // 根据模块类型调用相应的加载方法
                if (manager.render) {
                    manager.render(container);

                } else if (manager.loadProviderManagementPage && moduleName === 'provider-management') {
                    // 特殊处理码商管理
                    manager.loadProviderManagementPage(container);
                } else if (manager.loadMerchantManagementPage && moduleName === 'merchant-management') {
                    // 特殊处理商户管理
                    manager.loadMerchantManagementPage(container);
                } else if (manager.loadPage) {
                    // 通用页面加载方法
                    manager.loadPage(container);
                } else {
                    // 尝试寻找合适的方法
                    const possibleMethods = ['show', 'display', 'load', 'initialize'];
                    let methodFound = false;
                    
                    for (const method of possibleMethods) {
                        if (manager[method] && typeof manager[method] === 'function') {
                            console.log(`使用方法 ${method} 加载模块 ${moduleName}`);
                            manager[method](container);
                            methodFound = true;
                            break;
                        }
                    }
                    
                    if (!methodFound) {
                        throw new Error(`模块 ${moduleInstanceName} 缺少可用的加载方法 (render, load, show, display, initialize)`);
                    }
                }
            } catch (error) {
                console.error(`业务模块 ${moduleName} 加载失败:`, error);
                this.showErrorPage(container, `${pageTitle}模块加载失败: ${error.message}`);
            }
        } else {
            // 模块仍然没有加载成功
            console.error(`模块 ${moduleName} 加载后仍然找不到实例 ${moduleInstanceName}`);
            console.error('当前全局对象中可用的管理器:', Object.keys(window).filter(key => key.includes('Manager')));
            
            // 显示调试信息页面
            container.innerHTML = `
                <div class="alert alert-warning">
                    <h4><i class="bi bi-exclamation-triangle"></i> 模块加载调试信息</h4>
                    <p><strong>模块名：</strong>${moduleName}</p>
                    <p><strong>期望的全局变量：</strong>window.${moduleInstanceName}</p>
                    <p><strong>当前可用的管理器：</strong></p>
                    <ul>
                        ${Object.keys(window).filter(key => key.includes('Manager')).map(key => `<li>${key}</li>`).join('')}
                    </ul>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载页面</button>
                    <button class="btn btn-secondary" onclick="window.uiManager.navigateToPage('dashboard')">返回首页</button>
                </div>
            `;
        }
    }

    /**
     * 备用模块脚本加载方法
     */
    async loadModuleScript(moduleName) {
        const moduleScripts = {
            'provider-management': '/js/modules/business/provider-management.js',
            'merchant-management': '/js/modules/business/merchant-management.js',
            'product-management': '/js/modules/business/product-management.js',
            'finance-management': '/js/modules/business/finance-management.js',
            'payment-management': '/js/modules/business/payment-management.js',
            'transaction-management': '/js/modules/business/transaction-management.js',
            'api-management': '/js/modules/business/api-management.js'
        };

        const scriptPath = moduleScripts[moduleName];
        if (!scriptPath) {
            throw new Error(`未找到模块 ${moduleName} 的脚本路径`);
        }

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = scriptPath + '?v=' + Date.now();
            script.onload = resolve;
            script.onerror = () => reject(new Error(`加载脚本 ${scriptPath} 失败`));
            document.head.appendChild(script);
        });
    }

    /**
     * 通用安全模块加载器
     */
    loadSecurityModule(container, moduleName, pageTitle, managerClassName = null) {
        console.log(`加载安全模块: ${moduleName}`);
        
        // 检查是否有对应的安全模块
        const moduleMap = {
            'security-management': 'SecurityManagementModule'
        };

        const moduleClass = moduleMap[moduleName];
        if (window[moduleClass]) {
            try {
                // 如果指定了特定的Manager类名，直接使用
                if (managerClassName && window[managerClassName]) {
                    const manager = new window[managerClassName]();
                    if (manager.initialize) {
                        manager.initialize();
                    } else if (manager.init) {
                        manager.init(container);
                    } else {
                        throw new Error(`管理器 ${managerClassName} 缺少 initialize 或 init 方法`);
                    }
                } else {
                    // 否则使用模块的默认初始化
                    const module = window[moduleClass];
                    if (module.init) {
                        module.init(container);
                    } else {
                        throw new Error(`模块 ${moduleClass} 缺少 init 方法`);
                    }
                }
            } catch (error) {
                console.error(`安全模块 ${moduleName} 加载失败:`, error);
                this.showErrorPage(container, `${pageTitle}模块加载失败: ${error.message}`);
            }
        } else {
            // 显示开发中页面
            this.showDevelopmentPage(container, pageTitle);
        }
    }

    /**
     * 通用系统模块加载器
     */
    loadSystemModule(container, moduleName, pageTitle, managerClassName = null) {
        console.log(`加载系统模块: ${moduleName}`);
        
        // 检查是否有对应的系统模块
        const moduleMap = {
            'system-monitoring': 'SystemMonitoringModule'
        };

        const moduleClass = moduleMap[moduleName];
        if (window[moduleClass]) {
            try {
                // 如果指定了特定的Manager类名，直接使用
                if (managerClassName && window[managerClassName]) {
                    const manager = new window[managerClassName]();
                    if (manager.initialize) {
                        manager.initialize();
                    } else if (manager.init) {
                        manager.init(container);
                    } else {
                        throw new Error(`管理器 ${managerClassName} 缺少 initialize 或 init 方法`);
                    }
                } else {
                    // 否则使用模块的默认初始化
                    const module = window[moduleClass];
                    if (module.init) {
                        module.init(container);
                    } else {
                        throw new Error(`模块 ${moduleClass} 缺少 init 方法`);
                    }
                }
            } catch (error) {
                console.error(`系统模块 ${moduleName} 加载失败:`, error);
                this.showErrorPage(container, `${pageTitle}模块加载失败: ${error.message}`);
            }
        } else {
            // 显示开发中页面
            this.showDevelopmentPage(container, pageTitle);
        }
    }

    /**
     * 显示错误页面
     * @param {HTMLElement} container 容器
     * @param {string} message 错误信息
     */
    showErrorPage(container, message) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-exclamation-triangle" style="font-size: 4rem; color: #ef4444;"></i>
                <h3 class="mt-3">页面加载失败</h3>
                <p class="text-muted">${message}</p>
                <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
            </div>
        `;
    }

    /**
     * 获取当前页面
     * @returns {string} 当前页面ID
     */
    getCurrentPage() {
        return this.currentPage;
    }

    /**
     * 检查是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isInterfaceInitialized() {
        return this.isInitialized;
    }

    /**
     * 通用商户模块加载器
     */
    loadMerchantModule(container, moduleName, pageTitle) {
        console.log(`加载商户模块功能: ${moduleName}`);
        
        if (window.MerchantToolsManager) {
            try {
                const manager = new window.MerchantToolsManager();
                // 根据功能类型加载不同的页面
                switch (moduleName) {
                    case 'dashboard':
                        manager.loadMerchantDashboard(container);
                        break;
                    case 'orders':
                        manager.loadMerchantOrders(container);
                        break;
                    case 'statistics':
                        manager.loadMerchantStatistics(container);
                        break;
                    case 'products':
                        manager.loadMerchantProducts(container);
                        break;
                    case 'api-keys':
                        manager.loadAPIKeys(container);
                        break;
                    case 'code-generator':
                        manager.loadCodeGenerator(container);
                        break;
                    case 'signature-tool':
                        manager.loadSignatureTool(container);
                        break;
                    case 'api-docs':
                        manager.loadAPIDocumentation(container);
                        break;
                    case 'configuration':
                        manager.loadMerchantConfiguration(container);
                        break;
                    default:
                        manager.loadMerchantToolsPage(container);
                }
            } catch (error) {
                console.error(`商户模块 ${moduleName} 加载失败:`, error);
                this.showErrorPage(container, `${pageTitle}模块加载失败: ${error.message}`);
            }
        } else {
            // 显示开发中页面
            this.showDevelopmentPage(container, pageTitle);
        }
    }

    /**
     * 加载平台管理页面（系统管理员专用）
     * @param {HTMLElement} container 容器元素
     */
    async loadPlatformsPage(container) {
        container.innerHTML = `
            <div class="platform-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4><i class="bi bi-building me-2"></i>平台管理</h4>
                        <p class="text-muted mb-0">管理所有平台方及其配置</p>
                    </div>
                    <button class="btn btn-primary" onclick="window.uiManager.showAddPlatformModal()">
                        <i class="bi bi-plus"></i> 添加平台
                    </button>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-building text-primary" style="font-size: 2rem;"></i>
                                <h5 class="mt-2">总平台数</h5>
                                <h3 class="text-primary" id="totalPlatforms">-</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-shop text-success" style="font-size: 2rem;"></i>
                                <h5 class="mt-2">总码商数</h5>
                                <h3 class="text-success" id="totalProviders">-</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-storefront text-info" style="font-size: 2rem;"></i>
                                <h5 class="mt-2">总商户数</h5>
                                <h3 class="text-info" id="totalMerchants">-</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-globe text-warning" style="font-size: 2rem;"></i>
                                <h5 class="mt-2">总域名数</h5>
                                <h3 class="text-warning" id="totalDomains">-</h3>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">平台列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="platformsTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>平台名称</th>
                                        <th>平台代码</th>
                                        <th>联系人</th>
                                        <th>域名</th>
                                        <th>码商数</th>
                                        <th>商户数</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="platformsTableBody">
                                    <tr><td colspan="10" class="text-center">加载中...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 加载平台数据
        await this.loadPlatformsData();
    }

    /**
     * 加载域名管理页面
     * @param {HTMLElement} container 容器元素
     */
    async loadDomainManagementPage(container) {
        container.innerHTML = `
            <div class="domain-management">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4><i class="bi bi-globe me-2"></i>域名管理</h4>
                        <p class="text-muted mb-0">管理所有租户的域名配置</p>
                    </div>
                    <button class="btn btn-primary" onclick="window.uiManager.showAddDomainModal()">
                        <i class="bi bi-plus"></i> 添加域名
                    </button>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">域名配置列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="domainsTable">
                                <thead>
                                    <tr>
                                        <th>域名</th>
                                        <th>租户类型</th>
                                        <th>租户名称</th>
                                        <th>所属平台</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="domainsTableBody">
                                    <tr><td colspan="7" class="text-center">加载中...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 加载域名数据
        await this.loadDomainsData();
    }

    /**
     * 加载平台数据
     */
    async loadPlatformsData() {
        try {
            const response = await fetch('/api/admin.php', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({
                    action: 'get_platforms_list'
                })
            });
            
            const result = await response.json();
            
            if (result.error_code === 0) {
                this.renderPlatformsTable(result.data.platforms);
                this.updatePlatformStats(result.data.stats);
            } else {
                this.showMessage('加载平台数据失败: ' + result.error_message, 'error');
            }
        } catch (error) {
            console.error('加载平台数据异常:', error);
            this.showMessage('网络请求失败', 'error');
        }
    }

    /**
     * 渲染平台列表表格
     */
    renderPlatformsTable(platforms) {
        const tbody = document.getElementById('platformsTableBody');
        if (!tbody) return;

        if (!platforms || platforms.length === 0) {
            tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted">暂无平台数据</td></tr>';
            return;
        }

        tbody.innerHTML = platforms.map(platform => `
            <tr>
                <td>${platform.id}</td>
                <td>${platform.platform_name}</td>
                <td><code>${platform.platform_code}</code></td>
                <td>${platform.contact_name || '-'}</td>
                <td><span class="badge bg-info">${platform.domain_count || 0}个</span></td>
                <td><span class="badge bg-success">${platform.provider_count || 0}个</span></td>
                <td><span class="badge bg-primary">${platform.merchant_count || 0}个</span></td>
                <td>
                    <span class="badge bg-${platform.status === 'active' ? 'success' : 'secondary'}">
                        ${platform.status === 'active' ? '正常' : '停用'}
                    </span>
                </td>
                <td>${new Date(platform.created_at).toLocaleDateString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="window.uiManager.editPlatform(${platform.id})">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="window.uiManager.viewPlatformDetails(${platform.id})">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="window.uiManager.deletePlatform(${platform.id})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 更新平台统计数据
     */
    updatePlatformStats(stats) {
        if (!stats) return;

        const elements = {
            'totalPlatforms': stats.total_platforms || 0,
            'totalProviders': stats.total_providers || 0,
            'totalMerchants': stats.total_merchants || 0,
            'totalDomains': stats.total_domains || 0
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    /**
     * 显示添加平台模态框
     */
    showAddPlatformModal() {
        // 这里应该显示添加平台的模态框
        this.showMessage('添加平台功能开发中...', 'info');
    }

    /**
     * 显示添加域名模态框
     */
    showAddDomainModal() {
        // 这里应该显示添加域名的模态框
        this.showMessage('添加域名功能开发中...', 'info');
    }

    /**
     * 编辑平台
     */
    editPlatform(platformId) {
        this.showMessage(`编辑平台 ${platformId} 功能开发中...`, 'info');
    }

    /**
     * 查看平台详情
     */
    viewPlatformDetails(platformId) {
        this.showMessage(`查看平台 ${platformId} 详情功能开发中...`, 'info');
    }

    /**
     * 删除平台
     */
    deletePlatform(platformId) {
        if (confirm('确定要删除这个平台吗？此操作不可恢复。')) {
            this.showMessage(`删除平台 ${platformId} 功能开发中...`, 'info');
        }
    }

    /**
     * 加载域名数据
     */
    async loadDomainsData() {
        try {
            const response = await fetch('/api/admin.php', {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({
                    action: 'get_domains_list'
                })
            });
            
            const result = await response.json();
            
            if (result.error_code === 0) {
                this.renderDomainsTable(result.data);
            } else {
                this.showMessage('加载域名数据失败: ' + result.error_message, 'error');
            }
        } catch (error) {
            console.error('加载域名数据异常:', error);
            this.showMessage('网络请求失败', 'error');
        }
    }

    /**
     * 渲染域名列表表格
     */
    renderDomainsTable(domains) {
        const tbody = document.getElementById('domainsTableBody');
        if (!tbody) return;

        if (!domains || domains.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无域名数据</td></tr>';
            return;
        }

        tbody.innerHTML = domains.map(domain => `
            <tr>
                <td><code>${domain.domain}</code></td>
                <td>
                    <span class="badge bg-${this.getTenantTypeBadgeColor(domain.tenant_type)}">
                        ${this.getTenantTypeText(domain.tenant_type)}
                    </span>
                </td>
                <td>${domain.tenant_name || '-'}</td>
                <td>${domain.platform_name || '-'}</td>
                <td>
                    <span class="badge bg-${domain.status === 'active' ? 'success' : 'secondary'}">
                        ${domain.status === 'active' ? '正常' : '停用'}
                    </span>
                </td>
                <td>${new Date(domain.created_at).toLocaleDateString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="window.uiManager.editDomain('${domain.domain}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="window.uiManager.deleteDomain('${domain.domain}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 获取租户类型徽章颜色
     */
    getTenantTypeBadgeColor(tenantType) {
        const colorMap = {
            'system_admin': 'danger',
            'platform_admin': 'warning',
            'provider': 'success',
            'merchant': 'info'
        };
        return colorMap[tenantType] || 'secondary';
    }

    /**
     * 获取租户类型文本（用于域名管理）
     */
    getTenantTypeText(tenantType) {
        const typeMap = {
            'system_admin': '系统管理员',
            'platform_admin': '平台管理员',
            'provider': '码商',
            'merchant': '商户'
        };
        return typeMap[tenantType] || '未知';
    }

    /**
     * 编辑域名
     */
    editDomain(domain) {
        this.showMessage(`编辑域名 ${domain} 功能开发中...`, 'info');
    }

    /**
     * 删除域名
     */
    deleteDomain(domain) {
        if (confirm(`确定要删除域名 ${domain} 吗？此操作不可恢复。`)) {
            this.showMessage(`删除域名 ${domain} 功能开发中...`, 'info');
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UIManager };
} else {
    // 浏览器环境 - 导出类到全局作用域
    window.UIManager = UIManager;
    
    // 为模块加载器导出整个模块
    window.UiManagerModule = {
        UIManager,
        version: '1.0.0',
        initialized: true
    };
    
    console.log('✅ UI管理器模块已导出到全局作用域');
}