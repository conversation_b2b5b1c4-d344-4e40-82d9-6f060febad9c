<?php
/**
 * 域名管理API - 系统管理员专用
 * 负责管理所有租户的域名配置
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-12-17
 */

// 防止直接访问
if (!defined('IN_ADMIN_PANEL')) {
    http_response_code(403);
    exit('Access denied');
}

// 获取全局变量
$tenantContext = $GLOBALS['tenant_context'];
$currentUser = $GLOBALS['current_user'];

// 验证权限：只有系统管理员可以管理域名
if (!$tenantContext || $tenantContext['tenant_type'] !== 'system_admin') {
    http_response_code(403);
    echo json_encode(array(
        'error_code' => 403,
        'error_message' => '权限不足，只有系统管理员可以管理域名'
    ));
    exit;
}

try {
    $db = Database::getInstance();
    
    switch ($action) {
        case 'get_domains_list':
            // 获取域名列表
            $domains = $db->fetchAll("
                SELECT 
                    d.*,
                    p.platform_name,
                    CASE 
                        WHEN d.tenant_type = 'system_admin' THEN '系统管理后台'
                        WHEN d.tenant_type = 'platform_admin' THEN CONCAT(p.platform_name, ' - 管理后台')
                        WHEN d.tenant_type = 'provider' THEN d.brand_name
                        WHEN d.tenant_type = 'merchant' THEN d.brand_name
                        ELSE d.brand_name
                    END as tenant_name
                FROM tenant_domains d
                LEFT JOIN platforms p ON d.platform_id = p.id
                ORDER BY d.created_at DESC
            ");
            
            echo json_encode(array(
                'error_code' => 0,
                'message' => '获取域名列表成功',
                'data' => $domains
            ));
            break;
            
        case 'get_domain_details':
            // 获取域名详情
            $domain = trim($input['domain']);
            if (!$domain) {
                throw new Exception('域名不能为空');
            }
            
            $domainInfo = $db->fetch("
                SELECT 
                    d.*,
                    p.platform_name,
                    p.platform_code
                FROM tenant_domains d
                LEFT JOIN platforms p ON d.platform_id = p.id
                WHERE d.domain = ?
            ", array($domain));
            
            if (!$domainInfo) {
                throw new Exception('域名不存在');
            }
            
            echo json_encode(array(
                'error_code' => 0,
                'message' => '获取域名详情成功',
                'data' => $domainInfo
            ));
            break;
            
        case 'add_domain':
            // 添加域名
            $domain = trim($input['domain']);
            $tenantType = trim($input['tenant_type']);
            $platformId = isset($input['platform_id']) ? intval($input['platform_id']) : null;
            $brandName = trim($input['brand_name']);
            $description = trim($input['description']);
            
            if (!$domain || !$tenantType) {
                throw new Exception('域名和租户类型不能为空');
            }
            
            // 验证域名格式
            if (!filter_var("http://{$domain}", FILTER_VALIDATE_URL)) {
                throw new Exception('域名格式不正确');
            }
            
            // 检查域名是否已存在
            $existing = $db->fetch("SELECT domain FROM tenant_domains WHERE domain = ?", array($domain));
            if ($existing) {
                throw new Exception('域名已存在');
            }
            
            // 验证租户类型
            $validTypes = array('system_admin', 'platform_admin', 'provider', 'merchant');
            if (!in_array($tenantType, $validTypes)) {
                throw new Exception('无效的租户类型');
            }
            
            // 如果是平台管理员、码商或商户，必须指定平台ID
            if (in_array($tenantType, array('platform_admin', 'provider', 'merchant')) && !$platformId) {
                throw new Exception('该租户类型必须指定所属平台');
            }
            
            // 验证平台是否存在
            if ($platformId) {
                $platform = $db->fetch("SELECT id FROM platforms WHERE id = ?", array($platformId));
                if (!$platform) {
                    throw new Exception('指定的平台不存在');
                }
            }
            
            $insertData = array(
                'domain' => $domain,
                'tenant_type' => $tenantType,
                'platform_id' => $platformId,
                'brand_name' => $brandName,
                'description' => $description,
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            );
            
            $db->insert('tenant_domains', $insertData);
            
            // 记录操作日志
            logTenantAction('add_domain', 'domain', 0, "添加域名: {$domain}");
            
            echo json_encode(array(
                'error_code' => 0,
                'message' => '添加域名成功'
            ));
            break;
            
        case 'edit_domain':
            // 编辑域名
            $domain = trim($input['domain']);
            if (!$domain) {
                throw new Exception('域名不能为空');
            }
            
            $domainInfo = $db->fetch("SELECT * FROM tenant_domains WHERE domain = ?", array($domain));
            if (!$domainInfo) {
                throw new Exception('域名不存在');
            }
            
            $updateData = array();
            if (isset($input['tenant_type'])) {
                $tenantType = $input['tenant_type'];
                $validTypes = array('system_admin', 'platform_admin', 'provider', 'merchant');
                if (!in_array($tenantType, $validTypes)) {
                    throw new Exception('无效的租户类型');
                }
                $updateData['tenant_type'] = $tenantType;
            }
            
            if (isset($input['platform_id'])) {
                $platformId = intval($input['platform_id']);
                if ($platformId) {
                    $platform = $db->fetch("SELECT id FROM platforms WHERE id = ?", array($platformId));
                    if (!$platform) {
                        throw new Exception('指定的平台不存在');
                    }
                }
                $updateData['platform_id'] = $platformId;
            }
            
            if (isset($input['brand_name'])) {
                $updateData['brand_name'] = trim($input['brand_name']);
            }
            if (isset($input['description'])) {
                $updateData['description'] = trim($input['description']);
            }
            if (isset($input['status'])) {
                $updateData['status'] = $input['status'];
            }
            
            $updateData['updated_at'] = date('Y-m-d H:i:s');
            
            $db->update('tenant_domains', $updateData, array('domain' => $domain));
            
            // 记录操作日志
            logTenantAction('edit_domain', 'domain', 0, "编辑域名: {$domain}");
            
            echo json_encode(array(
                'error_code' => 0,
                'message' => '编辑域名成功'
            ));
            break;
            
        case 'delete_domain':
            // 删除域名
            $domain = trim($input['domain']);
            if (!$domain) {
                throw new Exception('域名不能为空');
            }
            
            $domainInfo = $db->fetch("SELECT * FROM tenant_domains WHERE domain = ?", array($domain));
            if (!$domainInfo) {
                throw new Exception('域名不存在');
            }
            
            // 检查是否有关联的用户或数据
            $userCount = 0;
            if ($domainInfo['platform_id']) {
                $userCount = $db->fetchColumn("SELECT COUNT(*) FROM users WHERE platform_id = ?", array($domainInfo['platform_id']));
            }
            
            if ($userCount > 0) {
                throw new Exception('该域名下还有用户数据，无法删除');
            }
            
            $db->delete('tenant_domains', array('domain' => $domain));
            
            // 记录操作日志
            logTenantAction('delete_domain', 'domain', 0, "删除域名: {$domain}");
            
            echo json_encode(array(
                'error_code' => 0,
                'message' => '删除域名成功'
            ));
            break;
            
        default:
            throw new Exception('未知的操作类型');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'error_code' => 400,
        'error_message' => $e->getMessage()
    ));
}
?> 