<?php
require_once dirname(dirname(__FILE__)) . '/config/database.php';

/**
 * 商户业务API处理器
 * 处理商户租户的所有业务逻辑
 */
class MerchantAPI {
    private $tenantAuth;
    private $tenantManager;
    private $db;
    
    public function __construct($tenantAuth, $tenantManager) {
        $this->tenantAuth = $tenantAuth;
        $this->tenantManager = $tenantManager;
        $this->db = Database::getInstance();
    }
    
    /**
     * 处理API动作
     */
    public function handleAction($action, $method, $user) {
        // 验证用户是否为商户
        if ($user['user_type'] !== 'merchant') {
            throw new Exception('权限不足：只有商户可以访问此API', 403);
        }
        
        $merchantId = $user['profile_id'];
        
        switch ($action) {
            // 产品管理
            case 'products':
                return $this->handleProducts($method, $user, $merchantId);
            case 'product_details':
                return $this->handleProductDetails($method, $user, $merchantId);
            case 'product_status':
                return $this->handleProductStatus($method, $user, $merchantId);
                
            // 订单管理
            case 'orders':
                return $this->handleOrders($method, $user, $merchantId);
            case 'order_details':
                return $this->getOrderDetails($user, $merchantId);
            case 'order_status':
                return $this->updateOrderStatus($user, $merchantId);
                
            // API配置管理
            case 'api_config':
                return $this->handleAPIConfig($method, $user, $merchantId);
            case 'api_keys':
                return $this->handleAPIKeys($method, $user, $merchantId);
            case 'generate_api_key':
                return $this->generateAPIKey($user, $merchantId);
            case 'revoke_api_key':
                return $this->revokeAPIKey($user, $merchantId);
                
            // 员工管理
            case 'staff':
                return $this->handleStaff($method, $user, $merchantId);
            case 'staff_permissions':
                return $this->handleStaffPermissions($method, $user, $merchantId);
                
            // 业务统计
            case 'dashboard_stats':
                return $this->getDashboardStats($user, $merchantId);
            case 'business_reports':
                return $this->getBusinessReports($user, $merchantId);
                
            default:
                throw new Exception('不支持的API动作', 400);
        }
    }
    
    /**
     * 产品管理
     */
    private function handleProducts($method, $user, $merchantId) {
        switch ($method) {
            case 'GET':
                return $this->getProducts($merchantId);
            case 'POST':
                return $this->createProduct($user, $merchantId);
            case 'PUT':
                return $this->updateProduct($user, $merchantId);
            case 'DELETE':
                return $this->deleteProduct($user, $merchantId);
            default:
                throw new Exception('不支持的请求方法', 405);
        }
    }
    
    /**
     * 获取产品列表
     */
    private function getProducts($merchantId) {
        $filters = $_GET;
        $where = array('merchant_id = ?');
        $params = array($merchantId);
        
        // 搜索过滤
        if (!empty($filters['search'])) {
            $where[] = '(product_name LIKE ? OR product_id LIKE ?)';
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        // 状态过滤
        if (!empty($filters['status'])) {
            $where[] = 'status = ?';
            $params[] = $filters['status'];
        }
        
        // 分类过滤
        if (!empty($filters['category'])) {
            $where[] = 'category = ?';
            $params[] = $filters['category'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        // 分页
        $page = isset($filters['page']) ? max(1, intval($filters['page'])) : 1;
        $pageSize = isset($filters['page_size']) ? min(100, max(1, intval($filters['page_size']))) : 20;
        $offset = ($page - 1) * $pageSize;
        
        // 获取总数
        $total = $this->db->fetch(
            "SELECT COUNT(*) as count FROM products WHERE {$whereClause}",
            $params
        )['count'];
        
        // 获取数据
        $products = $this->db->fetchAll(
            "SELECT p.*, 
                    COUNT(o.id) as order_count,
                    SUM(CASE WHEN o.status = 'completed' THEN o.amount ELSE 0 END) as total_sales
             FROM products p 
             LEFT JOIN orders o ON p.id = o.product_id 
             WHERE {$whereClause} 
             GROUP BY p.id 
             ORDER BY p.created_at DESC 
             LIMIT {$pageSize} OFFSET {$offset}",
            $params
        );
        
        return array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array(
                'products' => $products,
                'pagination' => array(
                    'page' => $page,
                    'page_size' => $pageSize,
                    'total' => $total,
                    'total_pages' => ceil($total / $pageSize)
                )
            )
        );
    }
    
    /**
     * 创建产品
     */
    private function createProduct($user, $merchantId) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('请求数据无效', 400);
        }
        
        // 验证必填字段
        $required = array('product_name', 'product_id', 'price');
        foreach ($required as $field) {
            if (!isset($input[$field]) || $input[$field] === '') {
                throw new Exception("字段 {$field} 不能为空", 400);
            }
        }
        
        // 检查产品ID是否已存在
        $existing = $this->db->fetch(
            "SELECT id FROM products WHERE product_id = ?",
            array($input['product_id'])
        );
        
        if ($existing) {
            throw new Exception('产品ID已存在', 400);
        }
        
        // 创建产品
        $productId = $this->db->execute(
            "INSERT INTO products (
                merchant_id, product_name, product_id, description, 
                price, category, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())",
            array(
                $merchantId,
                $input['product_name'],
                $input['product_id'],
                isset($input['description']) ? $input['description'] : '',
                $input['price'],
                isset($input['category']) ? $input['category'] : '',
                isset($input['status']) ? $input['status'] : 'active'
            )
        );
        
        // 记录操作日志
        $this->tenantAuth->logTenantAction(
            $user['id'], 
            'create_product', 
            'product', 
            $productId,
            "创建产品: {$input['product_name']}"
        );
        
        return array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array('product_id' => $productId)
        );
    }
    
    /**
     * 更新产品
     */
    private function updateProduct($user, $merchantId) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['id'])) {
            throw new Exception('产品ID不能为空', 400);
        }
        
        $productId = $input['id'];
        
        // 验证产品归属
        $product = $this->db->fetch(
            "SELECT * FROM products WHERE id = ? AND merchant_id = ?",
            array($productId, $merchantId)
        );
        
        if (!$product) {
            throw new Exception('产品不存在或权限不足', 404);
        }
        
        // 构建更新字段
        $updateFields = array();
        $updateParams = array();
        
        if (isset($input['product_name'])) {
            $updateFields[] = 'product_name = ?';
            $updateParams[] = $input['product_name'];
        }
        
        if (isset($input['description'])) {
            $updateFields[] = 'description = ?';
            $updateParams[] = $input['description'];
        }
        
        if (isset($input['price'])) {
            $updateFields[] = 'price = ?';
            $updateParams[] = $input['price'];
        }
        
        if (isset($input['category'])) {
            $updateFields[] = 'category = ?';
            $updateParams[] = $input['category'];
        }
        
        if (isset($input['status'])) {
            $updateFields[] = 'status = ?';
            $updateParams[] = $input['status'];
        }
        
        if (empty($updateFields)) {
            throw new Exception('没有需要更新的字段', 400);
        }
        
        $updateFields[] = 'updated_at = NOW()';
        $updateParams[] = $productId;
        $updateParams[] = $merchantId;
        
        $this->db->execute(
            "UPDATE products SET " . implode(', ', $updateFields) . " WHERE id = ? AND merchant_id = ?",
            $updateParams
        );
        
        // 记录操作日志
        $this->tenantAuth->logTenantAction(
            $user['id'], 
            'update_product', 
            'product', 
            $productId,
            "更新产品: {$product['product_name']}"
        );
        
        return array(
            'error_code' => 0,
            'error_message' => 'success'
        );
    }
    
    /**
     * 删除产品
     */
    private function deleteProduct($user, $merchantId) {
        $productId = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if (!$productId) {
            throw new Exception('产品ID不能为空', 400);
        }
        
        // 验证产品归属
        $product = $this->db->fetch(
            "SELECT * FROM products WHERE id = ? AND merchant_id = ?",
            array($productId, $merchantId)
        );
        
        if (!$product) {
            throw new Exception('产品不存在或权限不足', 404);
        }
        
        // 检查是否有关联订单
        $orderCount = $this->db->fetch(
            "SELECT COUNT(*) as count FROM orders WHERE product_id = ?",
            array($productId)
        )['count'];
        
        if ($orderCount > 0) {
            throw new Exception('该产品存在关联订单，无法删除', 400);
        }
        
        // 删除产品
        $this->db->execute(
            "DELETE FROM products WHERE id = ? AND merchant_id = ?",
            array($productId, $merchantId)
        );
        
        // 记录操作日志
        $this->tenantAuth->logTenantAction(
            $user['id'], 
            'delete_product', 
            'product', 
            $productId,
            "删除产品: {$product['product_name']}"
        );
        
        return array(
            'error_code' => 0,
            'error_message' => 'success'
        );
    }
    
    /**
     * 订单管理
     */
    private function handleOrders($method, $user, $merchantId) {
        switch ($method) {
            case 'GET':
                return $this->getOrders($merchantId);
            default:
                throw new Exception('不支持的请求方法', 405);
        }
    }
    
    /**
     * 获取订单列表
     */
    private function getOrders($merchantId) {
        $filters = $_GET;
        $where = array('o.merchant_id = ?');
        $params = array($merchantId);
        
        // 状态过滤
        if (!empty($filters['status'])) {
            $where[] = 'o.status = ?';
            $params[] = $filters['status'];
        }
        
        // 日期范围过滤
        if (!empty($filters['start_date'])) {
            $where[] = 'o.created_at >= ?';
            $params[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $where[] = 'o.created_at <= ?';
            $params[] = $filters['end_date'] . ' 23:59:59';
        }
        
        // 产品过滤
        if (!empty($filters['product_id'])) {
            $where[] = 'o.product_id = ?';
            $params[] = $filters['product_id'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        // 分页
        $page = isset($filters['page']) ? max(1, intval($filters['page'])) : 1;
        $pageSize = isset($filters['page_size']) ? min(100, max(1, intval($filters['page_size']))) : 20;
        $offset = ($page - 1) * $pageSize;
        
        // 获取总数
        $total = $this->db->fetch(
            "SELECT COUNT(*) as count FROM orders o WHERE {$whereClause}",
            $params
        )['count'];
        
        // 获取数据
        $orders = $this->db->fetchAll(
            "SELECT o.*, p.product_name, p.price as product_price 
             FROM orders o 
             LEFT JOIN products p ON o.product_id = p.id 
             WHERE {$whereClause} 
             ORDER BY o.created_at DESC 
             LIMIT {$pageSize} OFFSET {$offset}",
            $params
        );
        
        return array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array(
                'orders' => $orders,
                'pagination' => array(
                    'page' => $page,
                    'page_size' => $pageSize,
                    'total' => $total,
                    'total_pages' => ceil($total / $pageSize)
                )
            )
        );
    }
    
    /**
     * 更新订单状态
     */
    private function updateOrderStatus($user, $merchantId) {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['order_id']) || !isset($input['status'])) {
            throw new Exception('订单ID和状态不能为空', 400);
        }
        
        $orderId = $input['order_id'];
        $status = $input['status'];
        
        // 验证订单归属
        $order = $this->db->fetch(
            "SELECT * FROM orders WHERE id = ? AND merchant_id = ?",
            array($orderId, $merchantId)
        );
        
        if (!$order) {
            throw new Exception('订单不存在或权限不足', 404);
        }
        
        // 更新订单状态
        $this->db->execute(
            "UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ? AND merchant_id = ?",
            array($status, $orderId, $merchantId)
        );
        
        // 记录操作日志
        $this->tenantAuth->logTenantAction(
            $user['id'], 
            'update_order_status', 
            'order', 
            $orderId,
            "更新订单状态: {$order['order_no']} -> {$status}"
        );
        
        return array(
            'error_code' => 0,
            'error_message' => 'success'
        );
    }
    
    /**
     * API配置管理
     */
    private function handleAPIConfig($method, $user, $merchantId) {
        switch ($method) {
            case 'GET':
                return $this->getAPIConfig($merchantId);
            case 'PUT':
                return $this->updateAPIConfig($user, $merchantId);
            default:
                throw new Exception('不支持的请求方法', 405);
        }
    }
    
    /**
     * 获取API配置
     */
    private function getAPIConfig($merchantId) {
        $config = $this->db->fetch(
            "SELECT * FROM merchant_api_config WHERE merchant_id = ?",
            array($merchantId)
        );
        
        if (!$config) {
            // 如果没有配置，创建默认配置
            $developerId = $this->generateDeveloperId();
            $developerKey = $this->generateDeveloperKey();
            
            $this->db->execute(
                "INSERT INTO merchant_api_config (
                    merchant_id, developer_id, developer_key, 
                    webhook_url, ip_whitelist, status, created_at
                ) VALUES (?, ?, ?, '', '', 'active', NOW())",
                array($merchantId, $developerId, $developerKey)
            );
            
            $config = $this->db->fetch(
                "SELECT * FROM merchant_api_config WHERE merchant_id = ?",
                array($merchantId)
            );
        }
        
        return array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array('config' => $config)
        );
    }
    
    /**
     * 生成API密钥
     */
    private function generateAPIKey($user, $merchantId) {
        $newKey = $this->generateDeveloperKey();
        
        $this->db->execute(
            "UPDATE merchant_api_config SET developer_key = ?, updated_at = NOW() WHERE merchant_id = ?",
            array($newKey, $merchantId)
        );
        
        // 记录操作日志
        $this->tenantAuth->logTenantAction(
            $user['id'], 
            'generate_api_key', 
            'api_config', 
            $merchantId,
            "重新生成API密钥"
        );
        
        return array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array('developer_key' => $newKey)
        );
    }
    
    /**
     * 获取仪表板统计数据
     */
    private function getDashboardStats($user, $merchantId) {
        // 基础统计
        $stats = array();
        
        // 产品统计
        $productStats = $this->db->fetch(
            "SELECT 
                COUNT(*) as total_products,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_products
             FROM products WHERE merchant_id = ?",
            array($merchantId)
        );
        $stats['products'] = $productStats;
        
        // 今日订单统计
        $todayStats = $this->db->fetch(
            "SELECT 
                COUNT(*) as today_orders,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as today_revenue
             FROM orders 
             WHERE merchant_id = ? AND DATE(created_at) = CURDATE()",
            array($merchantId)
        );
        $stats['today'] = $todayStats;
        
        // 本月订单统计
        $monthStats = $this->db->fetch(
            "SELECT 
                COUNT(*) as month_orders,
                SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as month_revenue
             FROM orders 
             WHERE merchant_id = ? AND YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())",
            array($merchantId)
        );
        $stats['month'] = $monthStats;
        
        // 订单状态统计
        $statusStats = $this->db->fetchAll(
            "SELECT status, COUNT(*) as count 
             FROM orders 
             WHERE merchant_id = ? 
             GROUP BY status",
            array($merchantId)
        );
        $stats['order_status'] = $statusStats;
        
        return array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array('stats' => $stats)
        );
    }
    
    /**
     * 生成开发者ID
     */
    private function generateDeveloperId() {
        return 'M' . date('ymd') . rand(10000, 99999);
    }
    
    /**
     * 生成开发者密钥
     */
    private function generateDeveloperKey() {
        return md5(uniqid(rand(), true));
    }
    
    /**
     * 验证权限
     */
    private function validatePermission($user, $permission) {
        return $this->tenantAuth->hasTenantPermission($user, $permission);
    }
}
?> 