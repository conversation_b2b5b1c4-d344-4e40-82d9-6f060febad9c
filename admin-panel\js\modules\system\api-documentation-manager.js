/**
 * 系统API文档管理器
 * 从admin.js第15790-16203行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class ApiDocumentationManager {
    constructor() {
        this.auth = new AuthManager();
        this.apiDocs = null;
        this.merchantInfo = null;
    }
    async initialize() {
        await this.loadApiDocumentation();
    }
    async loadApiDocumentation() {
        try {
            // 统一调用管理后台的API文档接口
            const apiUrl = `${
                CONFIG.API_BASE_URL
            }/admin.php?action=api_docs`;
            const response = await fetch(apiUrl, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.apiDocs = result.data || result;
                this.renderApiDocumentation();
            } else {
                this.showError('加载API文档失败: ' + (result.message || result.error_message || '未知错误'));
            }
        } catch (error) {
            console.error('加载API文档失败:',
            error);
            this.showError('加载API文档失败，请重试');
        }
    }
    renderApiDocumentation() {
        const container = document.getElementById('apiDocsContainer');
        if (!container || !this.apiDocs) return;
        let html = '';
        // 基本信息
        if (this.apiDocs.info) {
            html += this.renderInfoSection(this.apiDocs.info);
        }
        // 认证说明
        if (this.apiDocs.authentication) {
            html += this.renderAuthSection(this.apiDocs.authentication);
        }
        // API接口
        if (this.apiDocs.apis) {
            html += this.renderApisSection(this.apiDocs.apis);
        }
        // 回调说明
        if (this.apiDocs.callback) {
            html += this.renderCallbackSection(this.apiDocs.callback);
        }
        container.innerHTML = html;
    }
    renderInfoSection(info) {
        return `
        <div class="api-section">
        <div class="api-section-header">
        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>基本信息</h5>
        </div>
        <div class="api-section-content">
        <div class="row">
        <div class="col-md-6">
        <table class="table table-borderless">
        <tr>
        <td><strong>文档标题:</strong></td>
        <td>${
            info.title || 'PayPal支付系统API'
        }</td>
        </tr>
        <tr>
        <td><strong>版本:</strong></td>
        <td><span class="badge bg-primary">${
            info.version || '2.0.0'
        }</span></td>
        </tr>
        <tr>
        <td><strong>基础URL:</strong></td>
        <td><code>${
            info.base_url || 'https://qrcode.top670.com'
        }</code></td>
        </tr>
        </table>
        </div>
        <div class="col-md-6">
        <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <strong>重要提示:</strong><br>
        ${
            info.notice || '调用接口前请提供IP，只有添加到白名单的IP才可成功调用。'
        }
        </div>
        </div>
        </div>
        ${
            info.description ? `<p class="text-muted mt-3">${
                info.description
            }</p>` : ''
        }
        </div>
        </div>
        `;
    }
    renderAuthSection(auth) {
        let stepsHtml = '';
        if (auth.signature_steps) {
            auth.signature_steps.forEach(step => {
                stepsHtml += `
                <div class="mb-3">
                <h6><span class="badge bg-primary me-2">${
                    step.step
                }</span>${
                    step.title
                }</h6>
                <p class="text-muted mb-1">${
                    step.description
                }</p>
                </div>
                `;
            });
        }
        return `
        <div class="api-section">
        <div class="api-section-header">
        <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>认证说明</h5>
        </div>
        <div class="api-section-content">
        <div class="alert alert-info">
        <h6><i class="bi bi-key me-2"></i>${
            auth.type || 'SHA-256签名'
        }</h6>
        <p class="mb-0">${
            auth.description || '为确保接口调用安全可靠，平台对所有开发者请求增加签名校验'
        }</p>
        </div>
        <h6 class="mt-4 mb-3">签名生成步骤:</h6>
        ${
            stepsHtml
        }
        </div>
        </div>
        `;
    }
    renderApisSection(apis) {
        let html = `
        <div class="api-section">
        <div class="api-section-header">
        <h5 class="mb-0"><i class="bi bi-code-slash me-2"></i>API接口</h5>
        </div>
        <div class="api-section-content">
        `;
        Object.keys(apis).forEach(apiKey => {
            const api = apis[apiKey];
            html += this.renderSingleApi(apiKey,
            api);
        });
        html += `
        </div>
        </div>
        `;
        return html;
    }
    renderSingleApi(apiKey,
    api) {
        let paramsHtml = '';
        if (api.parameters) {
            paramsHtml = `
            <h6 class="mt-4 mb-3">请求参数:</h6>
            <div class="table-responsive">
            <table class="table table-sm param-table">
            <thead>
            <tr>
            <th>参数名</th>
            <th>类型</th>
            <th>必需</th>
            <th>说明</th>
            </tr>
            </thead>
            <tbody>
            `;
            Object.keys(api.parameters).forEach(paramKey => {
                const param = api.parameters[paramKey];
                paramsHtml += `
                <tr>
                <td><code>${
                    paramKey
                }</code></td>
                <td><span class="badge bg-secondary">${
                    param.type
                }</span></td>
                <td>${
                    param.required ? '<span class="text-danger">是</span>' : '<span class="text-muted">否</span>'
                }</td>
                <td>${
                    param.description || ''
                }</td>
                </tr>
                `;
            });
            paramsHtml += `
            </tbody>
            </table>
            </div>
            `;
        }
        let responseHtml = '';
        if (api.response) {
            responseHtml = `
            <h6 class="mt-4 mb-3">响应参数:</h6>
            <div class="table-responsive">
            <table class="table table-sm param-table">
            <thead>
            <tr>
            <th>参数名</th>
            <th>类型</th>
            <th>说明</th>
            </tr>
            </thead>
            <tbody>
            `;
            Object.keys(api.response).forEach(respKey => {
                const resp = api.response[respKey];
                responseHtml += `
                <tr>
                <td><code>${
                    respKey
                }</code></td>
                <td><span class="badge bg-info">${
                    resp.type || 'string'
                }</span></td>
                <td>${
                    resp.description || ''
                }</td>
                </tr>
                `;
            });
            responseHtml += `
            </tbody>
            </table>
            </div>
            `;
        }
        let exampleHtml = '';
        if (api.example) {
            exampleHtml = `
            <h6 class="mt-4 mb-3">请求示例:</h6>
            <div class="code-block">
            <strong>请求参数:</strong><br>
            ${
                api.example.request || ''
            }
            </div>
            <h6 class="mt-3 mb-2">成功响应:</h6>
            <div class="code-block">
            ${
                JSON.stringify(api.example.success_response || {
                },
                null, 2)
            }
            </div>
            ${
                api.example.error_response ? `
                <h6 class="mt-3 mb-2">错误响应:</h6>
                <div class="code-block">
                ${
                    JSON.stringify(api.example.error_response,
                    null, 2)
                }
                </div>
                ` : ''
            }
            `;
        }
        let errorCodesHtml = '';
        if (api.error_codes) {
            errorCodesHtml = `
            <h6 class="mt-4 mb-3">错误码说明:</h6>
            <div class="table-responsive">
            <table class="table table-sm error-code-table">
            <thead>
            <tr>
            <th>错误码</th>
            <th>说明</th>
            </tr>
            </thead>
            <tbody>
            `;
            Object.keys(api.error_codes).forEach(code => {
                const description = api.error_codes[code];
                const badgeClass = code === '0' ? 'bg-success' : 'bg-danger';
                errorCodesHtml += `
                <tr>
                <td><span class="badge ${
                    badgeClass
                }">${
                    code
                }</span></td>
                <td>${
                    description
                }</td>
                </tr>
                `;
            });
            errorCodesHtml += `
            </tbody>
            </table>
            </div>
            `;
        }
        return `
        <div class="mb-5">
        <div class="api-endpoint">
        <div class="d-flex justify-content-between align-items-start">
        <div>
        <h5 class="mb-2">
        <span class="method-badge method-post">${
            api.method || 'POST'
        }</span>
        ${
            api.name || apiKey
        }
        </h5>
        <p class="text-muted mb-2">${
            api.description || ''
        }</p>
        <code>${
            api.url || ''
        }</code>
        </div>
        </div>
        </div>
        ${
            paramsHtml
        }
        ${
            responseHtml
        }
        ${
            exampleHtml
        }
        ${
            errorCodesHtml
        }
        </div>
        `;
    }
    renderCallbackSection(callback) {
        let exampleHtml = '';
        if (callback.example) {
            exampleHtml = `
            <h6 class="mt-4 mb-3">回调示例:</h6>
            <div class="code-block">
            <strong>接收到的数据:</strong><br>
            ${
                JSON.stringify(callback.example.received_data || {
                },
                null, 2)
            }
            </div>
            ${
                callback.example.verification_string ? `
                <div class="code-block mt-3">
                <strong>签名验证字符串:</strong><br>
                ${
                    callback.example.verification_string
                }
                </div>
                ` : ''
            }
            ${
                callback.example.calculated_sign ? `
                <div class="code-block mt-3">
                <strong>计算得到的签名:</strong><br>
                ${
                    callback.example.calculated_sign
                }
                </div>
                ` : ''
            }
            `;
        }
        return `
        <div class="api-section">
        <div class="api-section-header">
        <h5 class="mb-0"><i class="bi bi-arrow-left-right me-2"></i>回调通知</h5>
        </div>
        <div class="api-section-content">
        <div class="alert alert-info">
        <h6><i class="bi bi-bell me-2"></i>回调说明</h6>
        <p class="mb-2">${
            callback.description || '回调信息说明'
        }</p>
        <ul class="mb-0">
        <li><strong>格式:</strong> ${
            callback.format || 'HTTP POST form格式'
        }</li>
        <li><strong>成功响应:</strong> <code>${
            callback.success_response || 'OK'
        }</code></li>
        <li><strong>数据结构:</strong> ${
            callback.data_structure || '与获取订单信息接口返回的信息一致'
        }</li>
        </ul>
        </div>
        ${
            exampleHtml
        }
        </div>
        </div>
        `;
    }
    async refreshDocs() {
        const container = document.getElementById('apiDocsContainer');
        if (container) {
            container.innerHTML = `
            <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">刷新中...</span>
            </div>
            <div class="mt-3">正在刷新API文档...</div>
            </div>
            `;
        }
        await this.loadApiDocumentation();
    }
    downloadDocs() {
        if (!this.apiDocs) {
            this.showError('没有可下载的文档');
            return;
        }
        const docContent = JSON.stringify(this.apiDocs,
        null, 2);
        const blob = new Blob([docContent], {
            type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'paypal-api-docs.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        this.showSuccess('API文档已下载');
    }
    showSuccess(message) {
        console.log('Success:',
        message);
        // 这里可以集成toast通知组件
    }
    showError(message) {
        console.error('Error:',
        message);
        // 这里可以集成toast通知组件
        const container = document.getElementById('apiDocsContainer');
        if (container) {
            container.innerHTML = `
            <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle me-2"></i>
            ${
                message
            }
            <button class="btn btn-outline-danger btn-sm ms-3" onclick="window.apiDocManager.refreshDocs()">
            重试
            </button>
            </div>
            `;
        }
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = ApiDocumentationManager;
} else if (typeof window !== "undefined") {
    window.ApiDocumentationManager = ApiDocumentationManager;
}

console.log('📦 ApiDocumentationManager 模块加载完成');
