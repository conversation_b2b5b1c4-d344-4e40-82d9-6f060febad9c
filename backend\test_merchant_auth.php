<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    require_once dirname(__FILE__) . '/config/database.php';
    require_once dirname(__FILE__) . '/utils/Auth.php';

    $db = new Database();
    $auth = new Auth();

    // 获取Authorization头 - PHP 5.6兼容版本
    function getAllHeaders() {
        $headers = array();
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) == 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }
        return $headers;
    }

    $headers = getAllHeaders();
    $token = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = str_replace('Bearer ', '', $token);

    // 安全地调用方法
    $tokenValid = false;
    $currentUser = null;
    
    try {
        $tokenValid = $auth->verifyToken($token);
    } catch (Exception $e) {
        $tokenValid = 'Error: ' . $e->getMessage();
    }
    
    try {
        $currentUser = $auth->getCurrentUser($token);
    } catch (Exception $e) {
        $currentUser = 'Error: ' . $e->getMessage();
    }

    echo json_encode(array(
        'status' => 'success',
        'headers' => $headers,
        'raw_token' => $token,
        'token_length' => strlen($token),
        'token_parts' => count(explode('.', $token)),
        'token_valid' => $tokenValid,
        'current_user' => $currentUser,
        'timestamp' => date('Y-m-d H:i:s'),
        'php_version' => phpversion()
    ));
    
} catch (Exception $e) {
    echo json_encode(array(
        'status' => 'error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'php_version' => phpversion()
    ));
}
?> 