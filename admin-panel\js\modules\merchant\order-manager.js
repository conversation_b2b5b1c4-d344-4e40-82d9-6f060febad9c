/**
 * 商户订单管理器
 * 从admin.js第17963-18676行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class MerchantOrderManager {
    constructor() {
        this.auth = new AuthManager();
        this.orders = [];
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalPages = 1;
        this.filters = {
            status: '',
            date_from: '',
            date_to: '',
            order_no: '',
            amount_min: '',
            amount_max: ''
        };
    }
    async initialize() {
        try {
            await this.loadOrders();
            this.renderOrderManagement();
        } catch (error) {
            console.error('初始化订单管理失败:',
            error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }
    async loadOrders(page = 1) {
        this.currentPage = page;
        try {
            const params = new URLSearchParams({
                module: 'tools',
                action: 'get_orders',
                page: this.currentPage,
                page_size: this.pageSize,
                ...this.filters
            });
            const response = await fetch(`/api/merchant/index.php?${
                params
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.orders = data.data?.orders || [];
                this.totalPages = data.data?.total_pages || 1;
                return true;
            } else {
                this.showError('加载订单失败: ' + (data.message || '未知错误'));
                return false;
            }
        } catch (error) {
            console.error('加载订单失败:',
            error);
            this.showError('网络错误，请重试');
            return false;
        }
    }
    renderOrderManagement() {
        const container = document.getElementById('merchantOrdersContainer');
        if (!container) return;
        container.innerHTML = `
        <!-- 筛选条件 -->
        <div class="card mb-4">
        <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-funnel me-2"></i>筛选条件</h6>
        </div>
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-2">
        <label class="form-label">订单状态</label>
        <select class="form-select" id="filterStatus" onchange="window.merchantOrderManager.updateFilter('status',
        this.value)">
        <option value="">全部状态</option>
        <option value="pending" ${
            this.filters.status === 'pending' ? 'selected' : ''
        }>待处理</option>
        <option value="success" ${
            this.filters.status === 'success' ? 'selected' : ''
        }>成功</option>
        <option value="failed" ${
            this.filters.status === 'failed' ? 'selected' : ''
        }>失败</option>
        <option value="cancelled" ${
            this.filters.status === 'cancelled' ? 'selected' : ''
        }>已取消</option>
        </select>
        </div>
        <div class="col-md-2">
        <label class="form-label">开始日期</label>
        <input type="date" class="form-control" id="filterDateFrom" value="${
            this.filters.date_from
        }"
        onchange="window.merchantOrderManager.updateFilter('date_from',
        this.value)">
        </div>
        <div class="col-md-2">
        <label class="form-label">结束日期</label>
        <input type="date" class="form-control" id="filterDateTo" value="${
            this.filters.date_to
        }"
        onchange="window.merchantOrderManager.updateFilter('date_to',
        this.value)">
        </div>
        <div class="col-md-2">
        <label class="form-label">订单号</label>
        <input type="text" class="form-control" id="filterOrderNo" value="${
            this.filters.order_no
        }"
        placeholder="输入订单号" onchange="window.merchantOrderManager.updateFilter('order_no',
        this.value)">
        </div>
        <div class="col-md-2">
        <label class="form-label">最小金额</label>
        <input type="number" class="form-control" id="filterAmountMin" value="${
            this.filters.amount_min
        }"
        placeholder="0.00" step="0.01" onchange="window.merchantOrderManager.updateFilter('amount_min',
        this.value)">
        </div>
        <div class="col-md-2">
        <label class="form-label">最大金额</label>
        <input type="number" class="form-control" id="filterAmountMax" value="${
            this.filters.amount_max
        }"
        placeholder="0.00" step="0.01" onchange="window.merchantOrderManager.updateFilter('amount_max',
        this.value)">
        </div>
        </div>
        <div class="mt-3">
        <button class="btn btn-primary" onclick="window.merchantOrderManager.applyFilters()">
        <i class="bi bi-search me-2"></i>搜索
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.merchantOrderManager.resetFilters()">
        <i class="bi bi-arrow-clockwise me-2"></i>重置
        </button>
        </div>
        </div>
        </div>
        <!-- 订单列表 -->
        <div class="card">
        <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
        <h6 class="mb-0"><i class="bi bi-list me-2"></i>订单列表 (共 ${
            this.orders.length
        } 条)</h6>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-primary" onclick="window.merchantOrderManager.toggleView('table')" id="tableViewBtn">
        <i class="bi bi-table me-1"></i>表格视图
        </button>
        <button class="btn btn-outline-primary" onclick="window.merchantOrderManager.toggleView('card')" id="cardViewBtn">
        <i class="bi bi-grid me-1"></i>卡片视图
        </button>
        </div>
        </div>
        </div>
        <div class="card-body p-0">
        <div id="ordersTableView">
        ${
            this.renderOrdersTable()
        }
        </div>
        <div id="ordersCardView" style="display: none;
        ">
        ${
            this.renderOrdersCards()
        }
        </div>
        </div>
        <div class="card-footer">
        ${
            this.renderPagination()
        }
        </div>
        </div>
        `;
    }
    renderOrdersTable() {
        if (this.orders.length === 0) {
            return `
            <div class="text-center py-5 text-muted">
            <i class="bi bi-inbox" style="font-size: 3rem;
            "></i>
            <p class="mt-3">暂无订单数据</p>
            <p>您可以尝试调整筛选条件或创建测试订单</p>
            </div>
            `;
        }
        return `
        <div class="table-responsive">
        <table class="table table-hover mb-0">
        <thead class="table-light">
        <tr>
        <th>订单号</th>
        <th>平台订单号</th>
        <th>金额</th>
        <th>支付方式</th>
        <th>状态</th>
        <th>创建时间</th>
        <th>支付时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        ${
            this.orders.map(order => this.renderOrderRow(order)).join('')
        }
        </tbody>
        </table>
        </div>
        `;
    }
    renderOrderRow(order) {
        const statusClass = {
            'success': 'success',
            'pending': 'warning',
            'failed': 'danger',
            'cancelled': 'secondary'
        }[order.status] || 'secondary';
        const statusText = {
            'success': '成功',
            'pending': '待处理',
            'failed': '失败',
            'cancelled': '已取消'
        }[order.status] || order.status;
        const paymentType = {
            '1': '支付宝',
            '2': '微信支付',
            '3': 'QQ钱包'
        }[order.type] || '未知';
        return `
        <tr>
        <td>
        <div class="d-flex align-items-center">
        <div>
        <div class="fw-bold">${
            order.order_no
        }</div>
        ${
            order.product_name ? `<small class="text-muted">${
                order.product_name
            }</small>` : ''
        }
        </div>
        </div>
        </td>
        <td>
        <code class="small">${
            order.platform_order_no || '-'
        }</code>
        </td>
        <td>
        <span class="fw-bold text-success">¥${
            order.amount
        }</span>
        </td>
        <td>
        <span class="badge bg-info">${
            paymentType
        }</span>
        </td>
        <td>
        <span class="badge bg-${
            statusClass
        }">${
            statusText
        }</span>
        </td>
        <td>
        <div>
        <div>${
            order.created_at
        }</div>
        <small class="text-muted">${
            this.getTimeAgo(order.created_at)
        }</small>
        </div>
        </td>
        <td>
        ${
            order.pay_time ? `
            <div>
            <div>${
                order.pay_time
            }</div>
            <small class="text-muted">${
                this.getTimeAgo(order.pay_time)
            }</small>
            </div>
            ` : '<span class="text-muted">-</span>'
        }
        </td>
        <td>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-primary" onclick="window.merchantOrderManager.viewOrderDetail('${
            order.order_no
        }')" title="查看详情">
        <i class="bi bi-eye"></i>
        </button>
        ${
            order.status === 'pending' ? `
            <button class="btn btn-outline-warning" onclick="window.merchantOrderManager.queryOrderStatus('${
                order.order_no
            }')" title="查询状态">
            <i class="bi bi-arrow-clockwise"></i>
            </button>
            ` : ''
        }
        <button class="btn btn-outline-info" onclick="window.merchantOrderManager.copyOrderInfo('${
            order.order_no
        }')" title="复制信息">
        <i class="bi bi-clipboard"></i>
        </button>
        </div>
        </td>
        </tr>
        `;
    }
    renderOrdersCards() {
        if (this.orders.length === 0) {
            return `
            <div class="text-center py-5 text-muted">
            <i class="bi bi-inbox" style="font-size: 3rem;
            "></i>
            <p class="mt-3">暂无订单数据</p>
            <p>您可以尝试调整筛选条件或创建测试订单</p>
            </div>
            `;
        }
        return `
        <div class="row p-3">
        ${
            this.orders.map(order => this.renderOrderCard(order)).join('')
        }
        </div>
        `;
    }
    renderOrderCard(order) {
        const statusClass = {
            'success': 'success',
            'pending': 'warning',
            'failed': 'danger',
            'cancelled': 'secondary'
        }[order.status] || 'secondary';
        const statusText = {
            'success': '成功',
            'pending': '待处理',
            'failed': '失败',
            'cancelled': '已取消'
        }[order.status] || order.status;
        const paymentType = {
            '1': '支付宝',
            '2': '微信支付',
            '3': 'QQ钱包'
        }[order.type] || '未知';
        return `
        <div class="col-md-6 col-lg-4 mb-3">
        <div class="card h-100">
        <div class="card-header d-flex justify-content-between align-items-center">
        <span class="badge bg-${
            statusClass
        }">${
            statusText
        }</span>
        <small class="text-muted">${
            this.getTimeAgo(order.created_at)
        }</small>
        </div>
        <div class="card-body">
        <h6 class="card-title">${
            order.order_no
        }</h6>
        <div class="mb-2">
        <div class="d-flex justify-content-between">
        <span>金额:</span>
        <span class="fw-bold text-success">¥${
            order.amount
        }</span>
        </div>
        <div class="d-flex justify-content-between">
        <span>支付方式:</span>
        <span class="badge bg-info">${
            paymentType
        }</span>
        </div>
        ${
            order.platform_order_no ? `
            <div class="d-flex justify-content-between">
            <span>平台订单号:</span>
            <code class="small">${
                order.platform_order_no.substring(0, 16)
            }...</code>
            </div>
            ` : ''
        }
        </div>
        <div class="text-muted small">
        <div>创建: ${
            order.created_at
        }</div>
        ${
            order.pay_time ? `<div>支付: ${
                order.pay_time
            }</div>` : ''
        }
        </div>
        </div>
        <div class="card-footer">
        <div class="btn-group w-100">
        <button class="btn btn-outline-primary btn-sm" onclick="window.merchantOrderManager.viewOrderDetail('${
            order.order_no
        }')">
        <i class="bi bi-eye me-1"></i>详情
        </button>
        ${
            order.status === 'pending' ? `
            <button class="btn btn-outline-warning btn-sm" onclick="window.merchantOrderManager.queryOrderStatus('${
                order.order_no
            }')">
            <i class="bi bi-arrow-clockwise me-1"></i>查询
            </button>
            ` : ''
        }
        <button class="btn btn-outline-info btn-sm" onclick="window.merchantOrderManager.copyOrderInfo('${
            order.order_no
        }')">
        <i class="bi bi-clipboard me-1"></i>复制
        </button>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    renderPagination() {
        if (this.totalPages <= 1) return '';
        const startPage = Math.max(1,
        this.currentPage - 2);
        const endPage = Math.min(this.totalPages,
        this.currentPage + 2);
        let paginationHtml = `
        <nav>
        <ul class="pagination pagination-sm mb-0">
        <li class="page-item ${
            this.currentPage === 1 ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.merchantOrderManager.goToPage(1)">首页</a>
        </li>
        <li class="page-item ${
            this.currentPage === 1 ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.merchantOrderManager.goToPage(${
            this.currentPage - 1
        })">上一页</a>
        </li>
        `;
        for (let i = startPage;
        i <= endPage;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === this.currentPage ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.merchantOrderManager.goToPage(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        paginationHtml += `
        <li class="page-item ${
            this.currentPage === this.totalPages ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.merchantOrderManager.goToPage(${
            this.currentPage + 1
        })">下一页</a>
        </li>
        <li class="page-item ${
            this.currentPage === this.totalPages ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.merchantOrderManager.goToPage(${
            this.totalPages
        })">末页</a>
        </li>
        </ul>
        </nav>
        <div class="d-flex align-items-center ms-3">
        <small class="text-muted">
        第 ${
            this.currentPage
        } 页，共 ${
            this.totalPages
        } 页
        </small>
        </div>
        `;
        return `<div class="d-flex justify-content-between align-items-center">${
            paginationHtml
        }</div>`;
    }
    // 筛选和搜索功能
    updateFilter(key,
    value) {
        this.filters[key] = value;
    }
    async applyFilters() {
        await this.loadOrders(1);
        this.renderOrderManagement();
        this.showSuccess('筛选条件已应用');
    }
    resetFilters() {
        this.filters = {
            status: '',
            date_from: '',
            date_to: '',
            order_no: '',
            amount_min: '',
            amount_max: ''
        };
        this.applyFilters();
    }
    // 视图切换
    toggleView(viewType) {
        const tableView = document.getElementById('ordersTableView');
        const cardView = document.getElementById('ordersCardView');
        const tableBtn = document.getElementById('tableViewBtn');
        const cardBtn = document.getElementById('cardViewBtn');
        if (viewType === 'table') {
            tableView.style.display = 'block';
            cardView.style.display = 'none';
            tableBtn.classList.add('active');
            cardBtn.classList.remove('active');
        } else {
            tableView.style.display = 'none';
            cardView.style.display = 'block';
            tableBtn.classList.remove('active');
            cardBtn.classList.add('active');
        }
    }
    // 分页功能
    async goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) return;
        await this.loadOrders(page);
        this.renderOrderManagement();
    }
    // 订单操作
    async viewOrderDetail(orderNo) {
        try {
            const response = await fetch(`/api/merchant/index.php?module=tools&action=get_order_detail&order_no=${
                orderNo
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showOrderDetailModal(data.data);
            } else {
                this.showError('获取订单详情失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('获取订单详情失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    showOrderDetailModal(order) {
        const statusText = {
            'success': '成功',
            'pending': '待处理',
            'failed': '失败',
            'cancelled': '已取消'
        }[order.status] || order.status;
        const paymentType = {
            '1': '支付宝',
            '2': '微信支付',
            '3': 'QQ钱包'
        }[order.type] || '未知';
        const modalHtml = `
        <div class="modal fade" id="orderDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">
        <i class="bi bi-receipt me-2"></i>订单详情
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <div class="row">
        <div class="col-md-6">
        <table class="table table-borderless">
        <tr>
        <td><strong>订单号:</strong></td>
        <td>${
            order.order_no
        }</td>
        </tr>
        <tr>
        <td><strong>平台订单号:</strong></td>
        <td><code>${
            order.platform_order_no || '-'
        }</code></td>
        </tr>
        <tr>
        <td><strong>金额:</strong></td>
        <td><span class="fw-bold text-success">¥${
            order.amount
        }</span></td>
        </tr>
        <tr>
        <td><strong>支付方式:</strong></td>
        <td><span class="badge bg-info">${
            paymentType
        }</span></td>
        </tr>
        <tr>
        <td><strong>状态:</strong></td>
        <td><span class="badge bg-${
            order.status === 'success' ? 'success' : order.status === 'pending' ? 'warning' : 'danger'
        }">${
            statusText
        }</span></td>
        </tr>
        </table>
        </div>
        <div class="col-md-6">
        <table class="table table-borderless">
        <tr>
        <td><strong>产品名称:</strong></td>
        <td>${
            order.product_name || '-'
        }</td>
        </tr>
        <tr>
        <td><strong>创建时间:</strong></td>
        <td>${
            order.created_at
        }</td>
        </tr>
        <tr>
        <td><strong>支付时间:</strong></td>
        <td>${
            order.pay_time || '-'
        }</td>
        </tr>
        <tr>
        <td><strong>回调地址:</strong></td>
        <td><code class="small">${
            order.callback_url || '-'
        }</code></td>
        </tr>
        <tr>
        <td><strong>IP地址:</strong></td>
        <td>${
            order.client_ip || '-'
        }</td>
        </tr>
        </table>
        </div>
        </div>
        ${
            order.remark ? `
            <div class="mt-3">
            <strong>备注信息:</strong>
            <div class="alert alert-info mt-2">${
                order.remark
            }</div>
            </div>
            ` : ''
        }
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" onclick="window.merchantOrderManager.copyOrderInfo('${
            order.order_no
        }')">
        <i class="bi bi-clipboard me-2"></i>复制信息
        </button>
        ${
            order.status === 'pending' ? `
            <button type="button" class="btn btn-outline-warning" onclick="window.merchantOrderManager.queryOrderStatus('${
                order.order_no
            }')">
            <i class="bi bi-arrow-clockwise me-2"></i>查询状态
            </button>
            ` : ''
        }
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
        </div>
        </div>
        </div>
        `;
        // 移除已存在的modal
        const existingModal = document.getElementById('orderDetailModal');
        if (existingModal) {
            existingModal.remove();
        }
        // 添加新modal
        document.body.insertAdjacentHTML('beforeend',
        modalHtml);
        // 显示modal
        const modal = new bootstrap.Modal(document.getElementById('orderDetailModal'));
        modal.show();
    }
    async queryOrderStatus(orderNo) {
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=query_order_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    order_no: orderNo
                })
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('订单状态查询成功');
                await this.refreshOrders();
            } else {
                this.showError('查询订单状态失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('查询订单状态失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    copyOrderInfo(orderNo) {
        const order = this.orders.find(o => o.order_no === orderNo);
        if (!order) {
            this.showError('订单信息不存在');
            return;
        }
        const orderInfo = `订单号: ${
            order.order_no
        }
        平台订单号: ${
            order.platform_order_no || '-'
        }
        金额: ¥${
            order.amount
        }
        状态: ${
            order.status
        }
        创建时间: ${
            order.created_at
        }
        支付时间: ${
            order.pay_time || '-'
        }`;
        navigator.clipboard.writeText(orderInfo).then(() => {
            this.showSuccess('订单信息已复制到剪贴板');
        }).catch(err => {
            console.error('复制失败:',
            err);
            this.showError('复制失败，请手动复制');
        });
    }
    async exportOrders() {
        try {
            const params = new URLSearchParams({
                module: 'tools',
                action: 'export_orders',
                ...this.filters
            });
            const response = await fetch(`/api/merchant/index.php?${
                params
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            if (response.ok) {
                const blob = await response.blob();
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `orders-export-${
                    new Date().toISOString().split('T')[0]
                }.xlsx`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                this.showSuccess('订单数据已导出');
            } else {
                this.showError('导出订单失败');
            }
        } catch (error) {
            console.error('导出订单失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async refreshOrders() {
        const container = document.getElementById('merchantOrdersContainer');
        if (container) {
            container.innerHTML = `
            <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">刷新中...</span>
            </div>
            <div class="mt-3">正在刷新订单数据...</div>
            </div>
            `;
        }
        await this.loadOrders(this.currentPage);
        this.renderOrderManagement();
        this.showSuccess('订单数据已刷新');
    }
    // 工具函数
    getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);
        if (diffInSeconds < 60) return '刚刚';
        if (diffInSeconds < 3600) return `${
            Math.floor(diffInSeconds / 60)
        }分钟前`;
        if (diffInSeconds < 86400) return `${
            Math.floor(diffInSeconds / 3600)
        }小时前`;
        if (diffInSeconds < 2592000) return `${
            Math.floor(diffInSeconds / 86400)
        }天前`;
        return dateString;
    }
    showSuccess(message) {
        console.log('Success:',
        message);
        // 这里可以集成toast通知组件
    }
    showError(message) {
        console.error('Error:',
        message);
        // 这里可以集成toast通知组件
        const container = document.getElementById('merchantOrdersContainer');
        if (container) {
            container.innerHTML = `
            <div class="alert alert-danger">
            <i class="bi bi-exclamation-triangle me-2"></i>
            ${
                message
            }
            <button class="btn btn-outline-danger btn-sm ms-3" onclick="window.merchantOrderManager.refreshOrders()">
            重试
            </button>
            </div>
            `;
        }
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = MerchantOrderManager;
} else if (typeof window !== "undefined") {
    window.MerchantOrderManager = MerchantOrderManager;
}

console.log('📦 MerchantOrderManager 模块加载完成');
