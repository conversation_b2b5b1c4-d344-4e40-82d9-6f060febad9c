// Service Worker - 缓存策略和离线支�?
const CACHE_NAME = 'paypal-admin-v1.0.0';
const STATIC_CACHE = 'paypal-admin-static-v1.0.0';
const DYNAMIC_CACHE = 'paypal-admin-dynamic-v1.0.0';

// 需要缓存的静态资�?
const STATIC_ASSETS = [
    '/',
    '/index-optimized.html',
    '/css/performance-optimized.css',
    '/js/app.js',
    '/js/modules/core.js',
    '/js/modules/auth.js',
    // '/js/modules/ui-components.js', // 已过期，已迁移到core/ui-manager.js
    '/js/modules/data-manager.js',
    '/js/modules/dashboard.js',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'
];

// 需要网络优先的资源
const NETWORK_FIRST_URLS = [
    '/api/',
    '/admin.php'
];

// 需要缓存优先的资源
const CACHE_FIRST_URLS = [
    'https://cdn.jsdelivr.net',
    'https://cdnjs.cloudflare.com',
    '.css',
    '.js',
    '.woff',
    '.woff2',
    '.ttf',
    '.eot'
];

// Service Worker安装事件
self.addEventListener('install', event => {
    console.log('🔧 Service Worker安装�?..');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('📦 缓存静态资�?..');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('�?静态资源缓存完�?);
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('�?静态资源缓存失�?', error);
            })
    );
});

// Service Worker激活事�?
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker激活中...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('🗑�?删除旧缓�?', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('�?Service Worker激活完�?);
                return self.clients.claim();
            })
    );
});

// 网络请求拦截
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 跳过非GET请求
    if (request.method !== 'GET') {
        return;
    }
    
    // 跳过chrome-extension等特殊协�?
    if (!url.protocol.startsWith('http')) {
        return;
    }
    
    // 根据URL选择缓存策略
    if (isNetworkFirst(request.url)) {
        event.respondWith(networkFirst(request));
    } else if (isCacheFirst(request.url)) {
        event.respondWith(cacheFirst(request));
    } else {
        event.respondWith(staleWhileRevalidate(request));
    }
});

// 判断是否需要网络优�?
function isNetworkFirst(url) {
    return NETWORK_FIRST_URLS.some(pattern => url.includes(pattern));
}

// 判断是否需要缓存优�?
function isCacheFirst(url) {
    return CACHE_FIRST_URLS.some(pattern => url.includes(pattern));
}

// 网络优先策略
async function networkFirst(request) {
    try {
        // 尝试从网络获�?
        const networkResponse = await fetch(request);
        
        // 如果成功，缓存响�?
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.warn('网络请求失败，尝试从缓存获取:', request.url);
        
        // 网络失败，尝试从缓存获取
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // 如果是API请求，返回离线响�?
        if (request.url.includes('/api/') || request.url.includes('admin.php')) {
            return new Response(JSON.stringify({
                success: false,
                message: '网络连接不可用，请检查网络设�?,
                offline: true
            }), {
                status: 503,
                statusText: 'Service Unavailable',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        
        // 其他请求返回离线页面
        return new Response('离线模式', {
            status: 503,
            statusText: 'Service Unavailable'
        });
    }
}

// 缓存优先策略
async function cacheFirst(request) {
    try {
        // 先尝试从缓存获取
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // 缓存中没有，从网络获�?
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('缓存优先策略失败:', error);
        throw error;
    }
}

// 陈旧内容重新验证策略
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    // 后台更新缓存
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(error => {
        console.warn('后台更新失败:', error);
        return cachedResponse;
    });
    
    // 如果有缓存，立即返回缓存内容
    if (cachedResponse) {
        return cachedResponse;
    }
    
    // 没有缓存，等待网络响�?
    return fetchPromise;
}

// 消息处理
self.addEventListener('message', event => {
    const { type, payload } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'GET_CACHE_INFO':
            getCacheInfo().then(info => {
                event.ports[0].postMessage(info);
            });
            break;
            
        case 'CLEAR_CACHE':
            clearCache(payload.cacheName).then(success => {
                event.ports[0].postMessage({ success });
            });
            break;
            
        case 'PREFETCH_URLS':
            prefetchUrls(payload.urls).then(results => {
                event.ports[0].postMessage(results);
            });
            break;
            
        default:
            console.warn('未知消息类型:', type);
    }
});

// 获取缓存信息
async function getCacheInfo() {
    const cacheNames = await caches.keys();
    const cacheInfo = {};
    
    for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const keys = await cache.keys();
        cacheInfo[cacheName] = {
            size: keys.length,
            urls: keys.map(request => request.url)
        };
    }
    
    return cacheInfo;
}

// 清除指定缓存
async function clearCache(cacheName) {
    try {
        if (cacheName) {
            return await caches.delete(cacheName);
        } else {
            const cacheNames = await caches.keys();
            const deletePromises = cacheNames.map(name => caches.delete(name));
            await Promise.all(deletePromises);
            return true;
        }
    } catch (error) {
        console.error('清除缓存失败:', error);
        return false;
    }
}

// 预取URL
async function prefetchUrls(urls) {
    const results = [];
    const cache = await caches.open(DYNAMIC_CACHE);
    
    for (const url of urls) {
        try {
            const response = await fetch(url);
            if (response.ok) {
                await cache.put(url, response.clone());
                results.push({ url, success: true });
            } else {
                results.push({ url, success: false, error: `HTTP ${response.status}` });
            }
        } catch (error) {
            results.push({ url, success: false, error: error.message });
        }
    }
    
    return results;
}

// 后台同步
self.addEventListener('sync', event => {
    console.log('🔄 后台同步:', event.tag);
    
    switch (event.tag) {
        case 'background-sync':
            event.waitUntil(doBackgroundSync());
            break;
            
        case 'cache-cleanup':
            event.waitUntil(cleanupCache());
            break;
            
        default:
            console.warn('未知同步标签:', event.tag);
    }
});

// 执行后台同步
async function doBackgroundSync() {
    try {
        console.log('📡 执行后台同步...');
        
        // 这里可以执行需要同步的任务
        // 例如：上传离线时的数据、更新缓存等
        
        // 通知客户端同步完�?
        const clients = await self.clients.matchAll();
        clients.forEach(client => {
            client.postMessage({
                type: 'SYNC_COMPLETE',
                timestamp: Date.now()
            });
        });
        
        console.log('�?后台同步完成');
    } catch (error) {
        console.error('�?后台同步失败:', error);
    }
}

// 清理缓存
async function cleanupCache() {
    try {
        console.log('🧹 清理过期缓存...');
        
        const cache = await caches.open(DYNAMIC_CACHE);
        const requests = await cache.keys();
        
        // 删除超过24小时的缓存项
        const maxAge = 24 * 60 * 60 * 1000; // 24小时
        const now = Date.now();
        
        for (const request of requests) {
            const response = await cache.match(request);
            const dateHeader = response.headers.get('date');
            
            if (dateHeader) {
                const cacheTime = new Date(dateHeader).getTime();
                if (now - cacheTime > maxAge) {
                    await cache.delete(request);
                    console.log('🗑�?删除过期缓存:', request.url);
                }
            }
        }
        
        console.log('�?缓存清理完成');
    } catch (error) {
        console.error('�?缓存清理失败:', error);
    }
}

// 推送通知
self.addEventListener('push', event => {
    console.log('📢 收到推送通知:', event);
    
    const options = {
        body: event.data ? event.data.text() : '您有新的消息',
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: '查看详情',
                icon: '/icon-explore.png'
            },
            {
                action: 'close',
                title: '关闭',
                icon: '/icon-close.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('PayPal管理系统', options)
    );
});

// 通知点击处理
self.addEventListener('notificationclick', event => {
    console.log('📱 通知被点�?', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        // 打开应用
        event.waitUntil(
            clients.openWindow('/')
        );
    } else if (event.action === 'close') {
        // 关闭通知
        event.notification.close();
    } else {
        // 默认行为：打开应用
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// 错误处理
self.addEventListener('error', event => {
    console.error('Service Worker错误:', event.error);
});

self.addEventListener('unhandledrejection', event => {
    console.error('Service Worker未处理的Promise拒绝:', event.reason);
});

console.log('🎯 Service Worker已加�?); 
