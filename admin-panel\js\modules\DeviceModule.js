/**
 * 设备管理模块 - 统一设备管理
 * 整合device/management.js、device-monitor.js、group-management.js等功能
 * 实现界面共用+数据差异化的设计模式
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-01-20
 */

class DeviceModule {
    constructor() {

        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        this.currentRole = null;
        this.devices = [];
        this.groups = [];
        this.monitorData = {};
        
        console.log('✅ DeviceModule initialized');
    }

    /**
     * 统一渲染方法 - 方案5配置驱动的入口
     */
    async render(container, params = {}) {
        try {
            this.currentRole = params.role || this.getCurrentUserRole();
            console.log(`🎯 设备模块渲染 - 角色: ${this.currentRole}`);
            
            container.innerHTML = this.generateHTML();
            this.initializeEvents();
            await this.loadData();
            
        } catch (error) {
            console.error('❌ 设备模块渲染失败:', error);
            this.showError(container, '设备模块加载失败');
        }
    }

    getCurrentUserRole() {
        if (this.tenantInfo?.tenant_type) {
            return this.tenantInfo.tenant_type;
        }
        const user = this.authManager?.getUser();
        return user?.user_type || 'provider';
    }

    generateHTML() {
        const roleConfig = this.getRoleConfig(this.currentRole);
        
        return `
            <div class="device-module" data-role="${this.currentRole}">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="${roleConfig.icon} me-2"></i>${roleConfig.title}</h2>
                            <p class="text-muted mb-0">${roleConfig.description}</p>
                        </div>
                        <div>
                            ${this.generateHeaderButtons()}
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4" id="deviceStatsCards">
                    ${this.generateStatsCards()}
                </div>

                <!-- 功能选项卡 -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="deviceTabs">
                            ${this.generateTabHeaders()}
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="deviceTabContent">
                            ${this.generateTabContent()}
                        </div>
                    </div>
                </div>
            </div>

            <style>${this.generateStyles()}</style>
        `;
    }

    getRoleConfig(role) {
        const configs = {
            'system_admin': {
                title: '系统设备管理',
                description: '全系统设备监控与管理',
                icon: 'bi bi-phone-vibrate',
                color: 'primary'
            },
            'platform_admin': {
                title: '平台设备管理', 
                description: '平台设备配置与监控',
                icon: 'bi bi-diagram-3',
                color: 'success'
            },
            'provider': {
                title: '我的设备管理',
                description: '设备配置、分组、监控和维护',
                icon: 'bi bi-phone',
                color: 'info'
            },
            'merchant': {
                title: '设备信息查看',
                description: '查看关联设备状态',
                icon: 'bi bi-phone-check',
                color: 'warning'
            }
        };
        return configs[role] || configs['provider'];
    }

    generateHeaderButtons() {
        if (this.currentRole === 'provider') {
            return `
                <button class="btn btn-primary" id="addDeviceBtn">
                    <i class="bi bi-plus-circle me-2"></i>添加设备
                </button>
                <button class="btn btn-success ms-2" id="createGroupBtn">
                    <i class="bi bi-collection me-2"></i>创建分组
                </button>
                <button class="btn btn-outline-secondary ms-2" id="refreshDevicesBtn">
                    <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                </button>
            `;
        } else if (this.currentRole === 'merchant') {
            return `
                <button class="btn btn-outline-secondary" id="refreshDevicesBtn">
                    <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                </button>
            `;
        } else {
            return `
                <button class="btn btn-primary" id="exportDevicesBtn">
                    <i class="bi bi-download me-2"></i>导出数据
                </button>
                <button class="btn btn-outline-secondary ms-2" id="refreshDevicesBtn">
                    <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                </button>
            `;
        }
    }

    generateStatsCards() {
        const statsConfig = this.getStatsConfig(this.currentRole);
        return statsConfig.map(stat => `
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-${stat.color}">
                        <i class="${stat.icon}"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="${stat.id}">-</div>
                        <div class="stat-label">${stat.label}</div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    getStatsConfig(role) {
        const configs = {
            'system_admin': [
                { id: 'totalDevices', label: '总设备数', icon: 'bi bi-phone', color: 'primary' },
                { id: 'onlineDevices', label: '在线设备', icon: 'bi bi-phone-check', color: 'success' },
                { id: 'todayTransactions', label: '今日交易', icon: 'bi bi-graph-up', color: 'info' },
                { id: 'errorDevices', label: '异常设备', icon: 'bi bi-exclamation-triangle', color: 'danger' }
            ],
            'platform_admin': [
                { id: 'platformDevices', label: '平台设备', icon: 'bi bi-phone', color: 'primary' },
                { id: 'activeDevices', label: '活跃设备', icon: 'bi bi-phone-check', color: 'success' },
                { id: 'deviceGroups', label: '设备分组', icon: 'bi bi-collection', color: 'info' },
                { id: 'offlineDevices', label: '离线设备', icon: 'bi bi-phone-x', color: 'warning' }
            ],
            'provider': [
                { id: 'myDevices', label: '我的设备', icon: 'bi bi-phone', color: 'primary' },
                { id: 'onlineCount', label: '在线数量', icon: 'bi bi-phone-check', color: 'success' },
                { id: 'todayEarnings', label: '今日收益', icon: 'bi bi-currency-dollar', color: 'info' },
                { id: 'pendingTasks', label: '待处理', icon: 'bi bi-exclamation-circle', color: 'warning' }
            ],
            'merchant': [
                { id: 'linkedDevices', label: '关联设备', icon: 'bi bi-phone-check', color: 'primary' },
                { id: 'availableDevices', label: '可用设备', icon: 'bi bi-phone', color: 'success' },
                { id: 'todayOrders', label: '今日订单', icon: 'bi bi-receipt', color: 'info' },
                { id: 'successRate', label: '成功率', icon: 'bi bi-check-circle', color: 'warning' }
            ]
        };
        return configs[role] || configs['provider'];
    }

    generateTabHeaders() {
        const tabs = this.getTabsConfig(this.currentRole);
        return tabs.map((tab, index) => `
            <li class="nav-item">
                <a class="nav-link ${index === 0 ? 'active' : ''}" 
                   id="${tab.id}-tab" 
                   data-bs-toggle="tab" 
                   href="#${tab.id}">
                    <i class="${tab.icon} me-2"></i>${tab.title}
                </a>
            </li>
        `).join('');
    }

    getTabsConfig(role) {
        const configs = {
            'system_admin': [
                { id: 'overview', title: '设备概览', icon: 'bi bi-graph-up', type: 'overview' },
                { id: 'devices', title: '设备管理', icon: 'bi bi-phone', type: 'devices' },
                { id: 'monitoring', title: '实时监控', icon: 'bi bi-activity', type: 'monitoring' },
                { id: 'analytics', title: '数据分析', icon: 'bi bi-bar-chart', type: 'analytics' }
            ],
            'platform_admin': [
                { id: 'overview', title: '平台概览', icon: 'bi bi-graph-up', type: 'overview' },
                { id: 'devices', title: '设备管理', icon: 'bi bi-phone', type: 'devices' },
                { id: 'groups', title: '分组管理', icon: 'bi bi-collection', type: 'groups' },
                { id: 'monitoring', title: '监控告警', icon: 'bi bi-bell', type: 'monitoring' }
            ],
            'provider': [
                { id: 'devices', title: '设备列表', icon: 'bi bi-phone', type: 'devices' },
                { id: 'groups', title: '设备分组', icon: 'bi bi-collection', type: 'groups' },
                { id: 'monitoring', title: '设备监控', icon: 'bi bi-activity', type: 'monitoring' },
                { id: 'config', title: '设备配置', icon: 'bi bi-gear', type: 'config' }
            ],
            'merchant': [
                { id: 'linked', title: '关联设备', icon: 'bi bi-phone-check', type: 'linked' },
                { id: 'status', title: '设备状态', icon: 'bi bi-activity', type: 'status' },
                { id: 'orders', title: '设备订单', icon: 'bi bi-receipt', type: 'orders' },
                { id: 'reports', title: '使用报告', icon: 'bi bi-file-text', type: 'reports' }
            ]
        };
        return configs[role] || configs['provider'];
    }

    generateTabContent() {
        const tabs = this.getTabsConfig(this.currentRole);
        return tabs.map((tab, index) => `
            <div class="tab-pane fade ${index === 0 ? 'show active' : ''}" id="${tab.id}">
                ${this.generateTabPaneContent(tab)}
            </div>
        `).join('');
    }

    generateTabPaneContent(tab) {
        switch (tab.type) {
            case 'overview':
                return this.generateOverviewContent();
            case 'devices':
                return this.generateDevicesContent();
            case 'groups':
                return this.generateGroupsContent();
            case 'monitoring':
                return this.generateMonitoringContent();
            case 'config':
                return this.generateConfigContent();
            case 'linked':
                return this.generateLinkedDevicesContent();
            case 'status':
                return this.generateStatusContent();
            case 'orders':
                return this.generateOrdersContent();
            default:
                return '<div class="text-center py-4">内容加载中...</div>';
        }
    }

    generateOverviewContent() {
        return `
            <div class="row g-4">
                <div class="col-md-8">
                    <div class="chart-container">
                        <h6 class="mb-3">设备状态趋势</h6>
                        <canvas id="deviceChart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="device-summary">
                        <h6 class="mb-3">实时状态</h6>
                        <div id="realtimeDeviceStats">
                            <div class="stat-item">
                                <span class="label">在线设备:</span>
                                <span class="value text-success" id="onlineDevicesCount">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">离线设备:</span>
                                <span class="value text-danger" id="offlineDevicesCount">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">异常设备:</span>
                                <span class="value text-warning" id="errorDevicesCount">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">今日交易:</span>
                                <span class="value text-primary" id="todayTransactionsCount">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateDevicesContent() {
        return `
            <div class="devices-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="d-flex align-items-center gap-3">
                        <h6 class="mb-0">设备列表</h6>
                        <div class="input-group" style="width: 300px;">
                            <input type="text" class="form-control" id="deviceSearchInput" placeholder="搜索设备...">
                            <button class="btn btn-outline-secondary" type="button" id="searchDeviceBtn">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <select class="form-select" id="deviceStatusFilter" style="width: 150px;">
                            <option value="">全部状态</option>
                            <option value="online">在线</option>
                            <option value="offline">离线</option>
                            <option value="error">异常</option>
                        </select>
                        <select class="form-select" id="deviceGroupFilter" style="width: 150px;">
                            <option value="">全部分组</option>
                        </select>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>设备信息</th>
                                <th>状态</th>
                                <th>分组</th>
                                <th>最后在线</th>
                                <th>今日交易</th>
                                <th>收益</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="devicesTableBody">
                            <tr><td colspan="7" class="text-center py-4">加载中...</td></tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="设备列表分页" class="mt-3">
                    <ul class="pagination justify-content-center" id="devicesPagination">
                        <!-- 分页内容将通过JS生成 -->
                    </ul>
                </nav>
            </div>
        `;
    }

    generateGroupsContent() {
        return `
            <div class="groups-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">设备分组管理</h6>
                    ${this.currentRole === 'provider' ? `
                        <button class="btn btn-primary" id="createGroupBtn">
                            <i class="bi bi-plus-circle me-2"></i>创建分组
                        </button>
                    ` : ''}
                </div>
                
                <div class="row" id="groupsContainer">
                    <div class="col-12 text-center py-4">加载中...</div>
                </div>
            </div>
        `;
    }

    generateMonitoringContent() {
        return `
            <div class="monitoring-section">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">设备状态监控</h6>
                            </div>
                            <div class="card-body">
                                <div id="deviceStatusMonitor">
                                    <div class="text-center py-4">加载监控数据中...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">交易监控</h6>
                            </div>
                            <div class="card-body">
                                <div id="transactionMonitor">
                                    <div class="text-center py-4">加载交易数据中...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">实时日志</h6>
                    </div>
                    <div class="card-body">
                        <div id="deviceLogs" style="height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                            <div class="text-center text-muted">等待日志数据...</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateConfigContent() {
        return `
            <div class="config-section">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">设备配置</h6>
                            </div>
                            <div class="card-body">
                                <form id="deviceConfigForm">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">设备名称</label>
                                            <input type="text" class="form-control" id="deviceName" placeholder="输入设备名称">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">设备类型</label>
                                            <select class="form-select" id="deviceType">
                                                <option value="">请选择设备类型</option>
                                                <option value="mobile">手机</option>
                                                <option value="tablet">平板</option>
                                                <option value="pos">POS机</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">支付方式</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="supportAlipay" value="alipay">
                                                <label class="form-check-label" for="supportAlipay">支付宝</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="supportWechat" value="wechat">
                                                <label class="form-check-label" for="supportWechat">微信支付</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">设备分组</label>
                                            <select class="form-select" id="deviceGroupSelect">
                                                <option value="">请选择分组</option>
                                            </select>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">保存配置</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">配置说明</h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <h6>配置提示</h6>
                                    <ul class="mb-0">
                                        <li>设备名称建议使用易识别的命名</li>
                                        <li>根据实际情况选择支付方式</li>
                                        <li>合理分组便于管理</li>
                                        <li>配置后需要重启设备生效</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateLinkedDevicesContent() {
        return `
            <div class="linked-devices-section">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    以下是与您商户账户关联的设备信息
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>设备信息</th>
                                <th>状态</th>
                                <th>关联时间</th>
                                <th>今日订单</th>
                                <th>成功率</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="linkedDevicesTableBody">
                            <tr><td colspan="6" class="text-center py-4">加载中...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    generateStatusContent() {
        return `
            <div class="status-section">
                <div class="row g-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">设备状态实时监控</h6>
                            </div>
                            <div class="card-body">
                                <div id="deviceStatusGrid" class="row g-3">
                                    <div class="col-12 text-center py-4">加载设备状态中...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateOrdersContent() {
        return `
            <div class="orders-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">设备订单记录</h6>
                    <div class="d-flex gap-2">
                        <input type="date" class="form-control" id="orderDateFrom" style="width: 150px;">
                        <span class="align-self-center">至</span>
                        <input type="date" class="form-control" id="orderDateTo" style="width: 150px;">
                        <button class="btn btn-outline-secondary" id="filterOrdersBtn">筛选</button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>设备</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>完成时间</th>
                            </tr>
                        </thead>
                        <tbody id="deviceOrdersTableBody">
                            <tr><td colspan="6" class="text-center py-4">加载中...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    generateStyles() {
        return `
            .device-module { padding: 0; }
            .stat-card {
                background: white; border-radius: 10px; padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                display: flex; align-items: center; gap: 15px;
                transition: transform 0.2s;
            }
            .stat-card:hover { transform: translateY(-2px); }
            .stat-icon {
                width: 50px; height: 50px; border-radius: 50%;
                display: flex; align-items: center; justify-content: center;
                color: white; font-size: 20px;
            }
            .stat-content { flex: 1; }
            .stat-number { font-size: 24px; font-weight: bold; color: #333; }
            .stat-label { color: #666; font-size: 14px; }
            .chart-container, .device-summary, .devices-section, .groups-section, .monitoring-section {
                background: white; border-radius: 10px; padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .stat-item {
                display: flex; justify-content: space-between;
                margin-bottom: 10px; padding-bottom: 10px;
                border-bottom: 1px solid #eee;
            }
            .stat-item:last-child {
                border-bottom: none; margin-bottom: 0;
            }
            .device-status-online { color: #28a745; }
            .device-status-offline { color: #dc3545; }
            .device-status-error { color: #ffc107; }
        `;
    }

    initializeEvents() {
        // 基础事件监听
        const refreshBtn = document.getElementById('refreshDevicesBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }

        // 设备搜索
        const searchBtn = document.getElementById('searchDeviceBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.searchDevices());
        }

        // 筛选器
        const statusFilter = document.getElementById('deviceStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.filterDevices());
        }

        // 角色特定事件
        if (this.currentRole === 'provider') {
            this.initializeProviderEvents();
        }
    }

    initializeProviderEvents() {
        // 添加设备
        const addDeviceBtn = document.getElementById('addDeviceBtn');
        if (addDeviceBtn) {
            addDeviceBtn.addEventListener('click', () => this.showAddDeviceModal());
        }

        // 创建分组
        const createGroupBtn = document.getElementById('createGroupBtn');
        if (createGroupBtn) {
            createGroupBtn.addEventListener('click', () => this.showCreateGroupModal());
        }

        // 设备配置表单
        const configForm = document.getElementById('deviceConfigForm');
        if (configForm) {
            configForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveDeviceConfig();
            });
        }
    }

    async loadData() {
        try {
            console.log(`📊 加载${this.currentRole}的设备数据...`);
            
            // 加载统计数据
            await this.loadStatsData();
            
            // 加载设备列表
            await this.loadDevices();
            
            // 根据角色加载不同数据
            if (this.currentRole === 'provider') {
                await this.loadGroups();
            }
            
        } catch (error) {
            console.error('❌ 加载设备数据失败:', error);
        }
    }

    async loadStatsData() {
        const mockStats = this.getMockStatsData(this.currentRole);
        this.updateStatsCards(mockStats);
    }

    getMockStatsData(role) {
        const mockData = {
            'system_admin': {
                'totalDevices': '1,234',
                'onlineDevices': '987',
                'todayTransactions': '45,678',
                'errorDevices': '12'
            },
            'provider': {
                'myDevices': '25',
                'onlineCount': '23',
                'todayEarnings': '¥2,345',
                'pendingTasks': '3'
            },
            'merchant': {
                'linkedDevices': '5',
                'availableDevices': '5',
                'todayOrders': '156',
                'successRate': '98.5%'
            }
        };
        return mockData[role] || mockData['provider'];
    }

    updateStatsCards(data) {
        Object.keys(data).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = data[key];
            }
        });
    }

    async loadDevices() {
        // 模拟设备数据
        this.devices = [
            {
                id: 1,
                name: '设备001',
                type: 'mobile',
                status: 'online',
                group: '默认分组',
                last_online: '2024-01-20 15:30:00',
                today_transactions: 156,
                today_earnings: 2345.67
            },
            {
                id: 2,
                name: '设备002',
                type: 'tablet',
                status: 'offline',
                group: '备用分组',
                last_online: '2024-01-20 12:15:00',
                today_transactions: 89,
                today_earnings: 1234.56
            }
        ];

        this.renderDevicesTable();
    }

    renderDevicesTable() {
        const tbody = document.getElementById('devicesTableBody');
        if (!tbody) return;

        if (this.devices.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center py-4">暂无设备数据</td></tr>';
            return;
        }

        tbody.innerHTML = this.devices.map(device => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-phone me-2 text-primary"></i>
                        <div>
                            <div class="fw-bold">${device.name}</div>
                            <small class="text-muted">ID: ${device.id}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${device.status === 'online' ? 'success' : device.status === 'offline' ? 'secondary' : 'warning'}">
                        ${device.status === 'online' ? '在线' : device.status === 'offline' ? '离线' : '异常'}
                    </span>
                </td>
                <td>${device.group}</td>
                <td>${device.last_online}</td>
                <td>${device.today_transactions}</td>
                <td>¥${device.today_earnings.toFixed(2)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="deviceModule.viewDevice(${device.id})">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="deviceModule.editDevice(${device.id})">
                            <i class="bi bi-pencil"></i>
                        </button>
                        ${this.currentRole === 'provider' ? `
                            <button class="btn btn-outline-danger" onclick="deviceModule.deleteDevice(${device.id})">
                                <i class="bi bi-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    async loadGroups() {
        // 模拟分组数据
        this.groups = [
            { id: 1, name: '默认分组', device_count: 15, description: '默认设备分组' },
            { id: 2, name: '备用分组', device_count: 8, description: '备用设备分组' },
            { id: 3, name: '测试分组', device_count: 2, description: '测试用设备' }
        ];

        this.renderGroups();
    }

    renderGroups() {
        const container = document.getElementById('groupsContainer');
        if (!container) return;

        container.innerHTML = this.groups.map(group => `
            <div class="col-md-4 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title">${group.name}</h6>
                                <p class="card-text text-muted">${group.description}</p>
                                <span class="badge bg-primary">${group.device_count} 台设备</span>
                            </div>
                            ${this.currentRole === 'provider' ? `
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                        <i class="bi bi-three-dots"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="deviceModule.editGroup(${group.id})">编辑</a></li>
                                        <li><a class="dropdown-item text-danger" href="#" onclick="deviceModule.deleteGroup(${group.id})">删除</a></li>
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    searchDevices() {
        const keyword = document.getElementById('deviceSearchInput').value.toLowerCase();
        // 实现搜索逻辑
        console.log('搜索设备:', keyword);
    }

    filterDevices() {
        const status = document.getElementById('deviceStatusFilter').value;
        // 实现筛选逻辑
        console.log('筛选状态:', status);
    }

    showAddDeviceModal() {
        // 显示添加设备模态框
        console.log('显示添加设备模态框');
    }

    showCreateGroupModal() {
        // 显示创建分组模态框
        console.log('显示创建分组模态框');
    }

    saveDeviceConfig() {
        // 保存设备配置
        console.log('保存设备配置');
    }

    viewDevice(deviceId) {
        console.log('查看设备:', deviceId);
    }

    editDevice(deviceId) {
        console.log('编辑设备:', deviceId);
    }

    deleteDevice(deviceId) {
        console.log('删除设备:', deviceId);
    }

    editGroup(groupId) {
        console.log('编辑分组:', groupId);
    }

    deleteGroup(groupId) {
        console.log('删除分组:', groupId);
    }

    refreshData() {
        console.log('🔄 刷新设备数据');
        this.loadData();
    }

    showError(container, message) {
        container.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
}

// 全局注册
window.DeviceModule = DeviceModule;
window.deviceModule = new DeviceModule();

console.log('✅ DeviceModule 已注册到全局'); 