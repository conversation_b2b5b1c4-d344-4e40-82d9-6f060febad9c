/**
 * 日报管理模块
 * 负责生成、管理和分析系统日报
 */
class DailyReportManager {
    constructor() {
        this.apiUrl = '/api/admin.php?do=daily_reports';
        this.currentPage = 1;
        this.pageSize = 20;
        this.currentFilters = {};
        this.reportChart = null;
        this.auth = new AuthManager();
    }

    // 初始化
    initialize() {
        this.loadReports();
        this.loadTodayStats();
        this.initializeEventListeners();
        this.initializeChart();
    }

    // 初始化事件监听器
    initializeEventListeners() {
        // 搜索和筛选
        document.getElementById('reportSearchBtn')?.addEventListener('click', () => {
            this.searchReports();
        });

        document.getElementById('reportSearchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchReports();
            }
        });

        // 筛选器变化
        ['reportDateFilter', 'reportMerchantFilter', 'reportStatusFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });

        // 生成报告按钮
        document.getElementById('generateReportBtn')?.addEventListener('click', () => {
            this.showGenerateReportModal();
        });

        // 导出按钮
        document.getElementById('exportReportsBtn')?.addEventListener('click', () => {
            this.exportReports();
        });

        // 刷新按钮
        document.getElementById('refreshReportsBtn')?.addEventListener('click', () => {
            this.refreshData();
        });
    }

    // 加载今日统计数据
    async loadTodayStats() {
        try {
            const response = await fetch(`${this.apiUrl}&action=today_stats`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });
            const result = await response.json();
            
            if (result.code === 200) {
                this.updateStatsDisplay(result.data);
            }
        } catch (error) {
            console.error('加载今日统计失败:', error);
        }
    }

    // 更新统计显示
    updateStatsDisplay(stats) {
        const elements = {
            todayRevenue: stats.today_revenue || 0,
            todayOrders: stats.today_orders || 0,
            successRate: stats.success_rate || 0,
            comparedYesterday: stats.compared_yesterday || 0
        };

        Object.keys(elements).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (key === 'todayRevenue') {
                    element.textContent = `¥${this.formatNumber(elements[key])}`;
                } else if (key === 'successRate') {
                    element.textContent = `${elements[key]}%`;
                } else if (key === 'comparedYesterday') {
                    const isPositive = elements[key] >= 0;
                    element.innerHTML = `
                        <span class="badge ${isPositive ? 'bg-success' : 'bg-danger'}">
                            <i class="bi bi-arrow-${isPositive ? 'up' : 'down'}"></i>
                            ${Math.abs(elements[key])}%
                        </span>
                    `;
                } else {
                    element.textContent = this.formatNumber(elements[key]);
                }
            }
        });

        // 更新趋势图表
        if (stats.trend_data) {
            this.updateTrendChart(stats.trend_data);
        }
    }

    // 初始化图表
    initializeChart() {
        const ctx = document.getElementById('reportChart');
        if (ctx && typeof Chart !== 'undefined') {
            this.reportChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '营业额',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }, {
                        label: '订单数',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '日期'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '营业额 (¥)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '订单数'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }
    }

    // 更新趋势图表
    updateTrendChart(trendData) {
        if (this.reportChart && trendData) {
            this.reportChart.data.labels = trendData.dates || [];
            this.reportChart.data.datasets[0].data = trendData.revenues || [];
            this.reportChart.data.datasets[1].data = trendData.orders || [];
            this.reportChart.update();
        }
    }

    // 加载报告列表
    async loadReports() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                ...this.currentFilters
            });

            const response = await fetch(`${this.apiUrl}&${params}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });
            const result = await response.json();
            
            if (result.code === 200) {
                this.renderReportsTable(result.data.reports);
                this.renderPagination(result.data.pagination);
                this.updateReportCount(result.data.pagination.total_records);
            } else {
                this.showError('加载报告列表失败: ' + result.message);
            }
        } catch (error) {
            console.error('加载报告列表失败:', error);
            this.showError('网络连接失败，请重试');
        }
    }

    // 渲染报告表格
    renderReportsTable(reports) {
        const container = document.getElementById('reportsTableContainer');
        if (!container) return;

        if (!reports || reports.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-file-earmark-text display-1 text-muted"></i>
                    <div class="mt-3">暂无报告记录</div>
                    <button class="btn btn-primary mt-2" onclick="window.dailyReportManager.showGenerateReportModal()">
                        <i class="bi bi-plus"></i> 生成报告
                    </button>
                </div>
            `;
            return;
        }

        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>报告日期</th>
                            <th>报告类型</th>
                            <th>生成时间</th>
                            <th>状态</th>
                            <th>营业额</th>
                            <th>订单数</th>
                            <th>成功率</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${reports.map(report => `
                            <tr>
                                <td>
                                    <div class="fw-bold">${this.formatDate(report.report_date)}</div>
                                    <small class="text-muted">${this.getWeekday(report.report_date)}</small>
                                </td>
                                <td>
                                    <span class="badge ${this.getReportTypeBadgeClass(report.report_type)}">
                                        ${this.getReportTypeText(report.report_type)}
                                    </span>
                                </td>
                                <td>
                                    <small class="text-nowrap">${this.formatDateTime(report.created_at)}</small>
                                </td>
                                <td>
                                    <span class="badge ${this.getStatusBadgeClass(report.status)}">
                                        ${this.getStatusText(report.status)}
                                    </span>
                                </td>
                                <td class="text-end">
                                    <div class="fw-bold text-success">¥${this.formatNumber(report.total_revenue)}</div>
                                    ${report.revenue_change ? `
                                        <small class="text-${report.revenue_change >= 0 ? 'success' : 'danger'}">
                                            <i class="bi bi-arrow-${report.revenue_change >= 0 ? 'up' : 'down'}"></i>
                                            ${Math.abs(report.revenue_change)}%
                                        </small>
                                    ` : ''}
                                </td>
                                <td class="text-end">
                                    <div class="fw-bold">${this.formatNumber(report.total_orders)}</div>
                                    ${report.orders_change ? `
                                        <small class="text-${report.orders_change >= 0 ? 'success' : 'danger'}">
                                            <i class="bi bi-arrow-${report.orders_change >= 0 ? 'up' : 'down'}"></i>
                                            ${Math.abs(report.orders_change)}%
                                        </small>
                                    ` : ''}
                                </td>
                                <td class="text-end">
                                    <div class="fw-bold text-${report.success_rate >= 95 ? 'success' : report.success_rate >= 90 ? 'warning' : 'danger'}">
                                        ${report.success_rate}%
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" onclick="window.dailyReportManager.viewReport('${report.id}')" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="window.dailyReportManager.downloadReport('${report.id}')" title="下载报告">
                                            <i class="bi bi-download"></i>
                                        </button>
                                        ${report.status === 'draft' ? `
                                            <button class="btn btn-outline-success" onclick="window.dailyReportManager.publishReport('${report.id}')" title="发布报告">
                                                <i class="bi bi-send"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="window.dailyReportManager.deleteReport('${report.id}')" title="删除报告">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        ` : ''}
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = tableHtml;
    }

    // 渲染分页
    renderPagination(pagination) {
        const container = document.getElementById('reportsPagination');
        if (!container || pagination.total_pages <= 1) {
            if (container) container.style.display = 'none';
            return;
        }

        container.style.display = 'block';
        let paginationHtml = '<nav><ul class="pagination justify-content-center">';

        // 上一页
        if (pagination.current_page > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.dailyReportManager.goToPage(${pagination.current_page - 1})">上一页</a>
                </li>
            `;
        }

        // 页码
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="window.dailyReportManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (pagination.current_page < pagination.total_pages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.dailyReportManager.goToPage(${pagination.current_page + 1})">下一页</a>
                </li>
            `;
        }

        paginationHtml += '</ul></nav>';
        container.innerHTML = paginationHtml;
    }

    // 搜索报告
    searchReports() {
        const searchInput = document.getElementById('reportSearchInput');
        if (searchInput) {
            this.currentFilters.search = searchInput.value.trim();
            this.currentPage = 1;
            this.loadReports();
        }
    }

    // 应用筛选
    applyFilters() {
        const filters = {
            date_range: document.getElementById('reportDateFilter')?.value || '',
            merchant_id: document.getElementById('reportMerchantFilter')?.value || '',
            status: document.getElementById('reportStatusFilter')?.value || ''
        };

        // 移除空值
        Object.keys(filters).forEach(key => {
            if (!filters[key]) {
                delete filters[key];
            }
        });

        this.currentFilters = { ...this.currentFilters, ...filters };
        this.currentPage = 1;
        this.loadReports();
    }

    // 显示生成报告模态框
    showGenerateReportModal() {
        const modalHtml = `
            <div class="modal fade" id="generateReportModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">生成日报</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="generateReportForm">
                                <div class="mb-3">
                                    <label class="form-label">报告日期 *</label>
                                    <input type="date" class="form-control" id="reportDate" required value="${new Date().toISOString().split('T')[0]}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">报告类型 *</label>
                                    <select class="form-select" id="reportType" required>
                                        <option value="">请选择报告类型</option>
                                        <option value="daily">日报</option>
                                        <option value="weekly">周报</option>
                                        <option value="monthly">月报</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">商户范围</label>
                                    <select class="form-select" id="merchantScope">
                                        <option value="all">所有商户</option>
                                        <option value="active">活跃商户</option>
                                        <option value="specific">指定商户</option>
                                    </select>
                                </div>
                                <div class="mb-3" id="specificMerchantContainer" style="display: none;">
                                    <label class="form-label">选择商户</label>
                                    <select class="form-select" id="specificMerchant" multiple>
                                        <!-- 动态加载商户列表 -->
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">包含内容</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeRevenue" checked>
                                        <label class="form-check-label" for="includeRevenue">营业额统计</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeOrders" checked>
                                        <label class="form-check-label" for="includeOrders">订单统计</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeChart" checked>
                                        <label class="form-check-label" for="includeChart">趋势图表</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeAnalysis" checked>
                                        <label class="form-check-label" for="includeAnalysis">数据分析</label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">报告描述</label>
                                    <textarea class="form-control" id="reportDescription" rows="3" placeholder="可选的报告描述信息"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="window.dailyReportManager.generateReport()">
                                <span class="generate-text">生成报告</span>
                                <span class="generate-loading d-none">
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    生成中...
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('generateReportModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 设置事件监听
        document.getElementById('merchantScope').addEventListener('change', (e) => {
            const container = document.getElementById('specificMerchantContainer');
            container.style.display = e.target.value === 'specific' ? 'block' : 'none';
            if (e.target.value === 'specific') {
                this.loadMerchantList();
            }
        });

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('generateReportModal'));
        modal.show();
    }

    // 加载商户列表
    async loadMerchantList() {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=merchants_list`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });
            const result = await response.json();
            
            if (result.code === 200) {
                const select = document.getElementById('specificMerchant');
                if (select) {
                    select.innerHTML = result.data.map(merchant => 
                        `<option value="${merchant.id}">${merchant.name}</option>`
                    ).join('');
                }
            }
        } catch (error) {
            console.error('加载商户列表失败:', error);
        }
    }

    // 生成报告
    async generateReport() {
        const form = document.getElementById('generateReportForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const formData = {
            report_date: document.getElementById('reportDate').value,
            report_type: document.getElementById('reportType').value,
            merchant_scope: document.getElementById('merchantScope').value,
            specific_merchants: document.getElementById('merchantScope').value === 'specific' 
                ? Array.from(document.getElementById('specificMerchant').selectedOptions).map(o => o.value)
                : [],
            include_revenue: document.getElementById('includeRevenue').checked,
            include_orders: document.getElementById('includeOrders').checked,
            include_chart: document.getElementById('includeChart').checked,
            include_analysis: document.getElementById('includeAnalysis').checked,
            description: document.getElementById('reportDescription').value
        };

        this.setGenerateLoading(true);

        try {
            const response = await fetch(`${this.apiUrl}&action=generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();
            
            if (result.code === 200) {
                this.showSuccess('报告生成成功');
                bootstrap.Modal.getInstance(document.getElementById('generateReportModal')).hide();
                this.loadReports(); // 重新加载列表
                this.loadTodayStats(); // 更新统计
            } else {
                this.showError('生成失败: ' + result.message);
            }
        } catch (error) {
            console.error('生成报告失败:', error);
            this.showError('网络连接失败');
        }

        this.setGenerateLoading(false);
    }

    // 查看报告详情
    async viewReport(reportId) {
        try {
            const response = await fetch(`${this.apiUrl}&action=detail&id=${reportId}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });
            const result = await response.json();
            
            if (result.code === 200) {
                this.showReportDetailModal(result.data);
            } else {
                this.showError('获取报告详情失败: ' + result.message);
            }
        } catch (error) {
            console.error('获取报告详情失败:', error);
            this.showError('网络连接失败');
        }
    }

    // 显示报告详情模态框
    showReportDetailModal(report) {
        const modalHtml = `
            <div class="modal fade" id="reportDetailModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">报告详情 - ${this.formatDate(report.report_date)}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-success">¥${this.formatNumber(report.total_revenue)}</h5>
                                            <p class="card-text">总营业额</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-primary">${this.formatNumber(report.total_orders)}</h5>
                                            <p class="card-text">总订单数</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-info">${report.success_rate}%</h5>
                                            <p class="card-text">成功率</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-warning">${this.formatNumber(report.avg_order_amount)}</h5>
                                            <p class="card-text">平均订单金额</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            ${report.content ? `
                                <div class="mb-4">
                                    <h6>报告内容</h6>
                                    <div class="border rounded p-3 bg-light">
                                        ${report.content}
                                    </div>
                                </div>
                            ` : ''}
                            
                            ${report.analysis ? `
                                <div class="mb-4">
                                    <h6>数据分析</h6>
                                    <div class="border rounded p-3">
                                        ${report.analysis}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" onclick="window.dailyReportManager.downloadReport('${report.id}')">
                                <i class="bi bi-download"></i> 下载报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('reportDetailModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('reportDetailModal'));
        modal.show();
    }

    // 下载报告
    async downloadReport(reportId) {
        try {
            const response = await fetch(`${this.apiUrl}&action=download&id=${reportId}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `daily_report_${reportId}.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                this.showSuccess('报告下载成功');
            } else {
                throw new Error('下载失败');
            }
        } catch (error) {
            console.error('下载报告失败:', error);
            this.showError('下载失败: ' + error.message);
        }
    }

    // 发布报告
    async publishReport(reportId) {
        if (!confirm('确定要发布这个报告吗？发布后将不能修改。')) {
            return;
        }

        try {
            const response = await fetch(`${this.apiUrl}&action=publish`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({ report_id: reportId })
            });

            const result = await response.json();
            
            if (result.code === 200) {
                this.showSuccess('报告发布成功');
                this.loadReports();
            } else {
                this.showError('发布失败: ' + result.message);
            }
        } catch (error) {
            console.error('发布报告失败:', error);
            this.showError('网络连接失败');
        }
    }

    // 删除报告
    async deleteReport(reportId) {
        if (!confirm('确定要删除这个报告吗？此操作不可撤销！')) {
            return;
        }

        try {
            const response = await fetch(`${this.apiUrl}&action=delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({ report_id: reportId })
            });

            const result = await response.json();
            
            if (result.code === 200) {
                this.showSuccess('报告删除成功');
                this.loadReports();
            } else {
                this.showError('删除失败: ' + result.message);
            }
        } catch (error) {
            console.error('删除报告失败:', error);
            this.showError('网络连接失败');
        }
    }

    // 导出报告
    async exportReports() {
        try {
            const params = new URLSearchParams(this.currentFilters);
            const response = await fetch(`${this.apiUrl}&action=export&${params}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `daily_reports_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                this.showSuccess('报告导出成功');
            } else {
                throw new Error('导出失败');
            }
        } catch (error) {
            console.error('导出报告失败:', error);
            this.showError('导出失败: ' + error.message);
        }
    }

    // 刷新数据
    refreshData() {
        this.loadReports();
        this.loadTodayStats();
    }

    // 跳转到指定页
    goToPage(page) {
        this.currentPage = page;
        this.loadReports();
    }

    // 更新报告计数
    updateReportCount(count) {
        const element = document.getElementById('reportCount');
        if (element) {
            element.textContent = this.formatNumber(count);
        }
    }

    // 设置生成按钮加载状态
    setGenerateLoading(loading) {
        const textSpan = document.querySelector('.generate-text');
        const loadingSpan = document.querySelector('.generate-loading');
        const button = textSpan?.closest('button');
        
        if (textSpan) textSpan.style.display = loading ? 'none' : 'inline';
        if (loadingSpan) loadingSpan.classList.toggle('d-none', !loading);
        if (button) button.disabled = loading;
    }

    // 工具方法
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }

    formatNumber(num) {
        if (num === null || num === undefined) return '0';
        return Number(num).toLocaleString();
    }

    getWeekday(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return weekdays[date.getDay()];
    }

    getReportTypeBadgeClass(type) {
        const classes = {
            'daily': 'bg-primary',
            'weekly': 'bg-info',
            'monthly': 'bg-success'
        };
        return classes[type] || 'bg-secondary';
    }

    getReportTypeText(type) {
        const texts = {
            'daily': '日报',
            'weekly': '周报',
            'monthly': '月报'
        };
        return texts[type] || type;
    }

    getStatusBadgeClass(status) {
        const classes = {
            'draft': 'bg-warning',
            'published': 'bg-success',
            'archived': 'bg-secondary'
        };
        return classes[status] || 'bg-secondary';
    }

    getStatusText(status) {
        const texts = {
            'draft': '草稿',
            'published': '已发布',
            'archived': '已归档'
        };
        return texts[status] || status;
    }

    showSuccess(message) {
        // 简单的成功提示，以后可以改为更好的UI组件
        alert('✓ ' + message);
    }

    showError(message) {
        // 简单的错误提示，以后可以改为更好的UI组件
        alert('✗ ' + message);
    }

    // 销毁方法
    destroy() {
        if (this.reportChart) {
            this.reportChart.destroy();
            this.reportChart = null;
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DailyReportManager;
} else if (typeof window !== 'undefined') {
    window.DailyReportManager = DailyReportManager;
} 