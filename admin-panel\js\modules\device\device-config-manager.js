/**
 * 设备配置和安全管理模块
 * 提供远程设备配置推送、批量配置管理、设备安全管理和设备指纹验证功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class DeviceConfigManager {
    constructor() {
        this.apiBase = '/api/device';
        this.configTemplates = {};
        this.selectedDevices = new Set();
        this.isInitialized = false;
        
        console.log('DeviceConfigManager initialized');
    }

    /**
     * 初始化配置管理器
     */
    async initialize() {
        try {
            console.log('🔧 初始化设备配置管理器...');
            
            // 加载配置界面
            await this.loadConfigInterface();
            
            // 加载配置模板
            await this.loadConfigTemplates();
            
            // 绑定事件监听
            this.bindEvents();
            
            this.isInitialized = true;
            console.log('✅ 设备配置管理器初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 设备配置管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 加载配置界面
     */
    async loadConfigInterface() {
        const container = document.getElementById('main-content');
        if (!container) return;

        container.innerHTML = this.getConfigHTML();
    }

    /**
     * 获取配置界面HTML
     */
    getConfigHTML() {
        return `
            <div class="device-config-container">
                <!-- 配置管理头部 -->
                <div class="config-header">
                    <div class="row">
                        <div class="col-md-8">
                            <h2><i class="bi bi-gear"></i> 设备配置管理</h2>
                            <p class="text-muted">远程配置设备参数、批量管理配置模板和设备安全设置</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="config-controls">
                                <button class="btn btn-primary" id="createTemplate">
                                    <i class="bi bi-plus-circle"></i> 新建模板
                                </button>
                                <button class="btn btn-success" id="batchConfig">
                                    <i class="bi bi-broadcast"></i> 批量配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置选项卡 -->
                <div class="config-tabs">
                    <ul class="nav nav-tabs" id="configTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="templates-tab" data-bs-toggle="tab" data-bs-target="#templates" type="button" role="tab">配置模板</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="devices-tab" data-bs-toggle="tab" data-bs-target="#devices" type="button" role="tab">设备配置</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">安全管理</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">配置日志</button>
                        </li>
                    </ul>
                    <div class="tab-content" id="configTabContent">
                        <!-- 配置模板 -->
                        <div class="tab-pane fade show active" id="templates" role="tabpanel">
                            <div class="templates-content">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="template-list-panel">
                                            <h5>配置模板</h5>
                                            <div class="template-list" id="templateList">
                                                <div class="loading-spinner">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">加载中...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="template-editor-panel">
                                            <h5>模板编辑器</h5>
                                            <div class="template-editor" id="templateEditor">
                                                <div class="empty-state">
                                                    <i class="bi bi-file-earmark-text" style="font-size: 3rem; color: #6c757d;"></i>
                                                    <p class="text-muted">选择一个模板开始编辑</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 设备配置 -->
                        <div class="tab-pane fade" id="devices" role="tabpanel">
                            <div class="devices-config-content">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="device-selector-panel">
                                            <h5>选择设备</h5>
                                            <div class="device-filters mb-3">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <select class="form-select" id="groupFilter">
                                                            <option value="">全部小组</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <select class="form-select" id="statusFilter">
                                                            <option value="">全部状态</option>
                                                            <option value="online">在线</option>
                                                            <option value="offline">离线</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="device-list" id="deviceList">
                                                <!-- 设备列表将在此处动态加载 -->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="config-preview-panel">
                                            <h5>配置预览</h5>
                                            <div class="selected-devices mb-3">
                                                <span class="badge bg-primary" id="selectedCount">已选择 0 台设备</span>
                                                <button class="btn btn-sm btn-outline-secondary ms-2" id="clearSelection">清除选择</button>
                                            </div>
                                            <div class="config-form" id="configForm">
                                                <!-- 配置表单将在此处动态生成 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 安全管理 -->
                        <div class="tab-pane fade" id="security" role="tabpanel">
                            <div class="security-content">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="mb-0">设备指纹管理</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="fingerprint-controls mb-3">
                                                    <button class="btn btn-primary" id="scanFingerprints">
                                                        <i class="bi bi-fingerprint"></i> 扫描指纹
                                                    </button>
                                                    <button class="btn btn-warning" id="updateFingerprints">
                                                        <i class="bi bi-arrow-clockwise"></i> 更新指纹
                                                    </button>
                                                </div>
                                                <div class="fingerprint-list" id="fingerprintList">
                                                    <!-- 指纹列表 -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5 class="mb-0">安全策略</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="security-policies" id="securityPolicies">
                                                    <!-- 安全策略配置 -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 配置日志 -->
                        <div class="tab-pane fade" id="logs" role="tabpanel">
                            <div class="logs-content">
                                <div class="card">
                                    <div class="card-header">
                                        <div class="row align-items-center">
                                            <div class="col">
                                                <h5 class="mb-0">配置操作日志</h5>
                                            </div>
                                            <div class="col-auto">
                                                <div class="log-filters">
                                                    <select class="form-select form-select-sm" id="logTypeFilter">
                                                        <option value="">全部类型</option>
                                                        <option value="config">配置变更</option>
                                                        <option value="security">安全操作</option>
                                                        <option value="template">模板操作</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>时间</th>
                                                        <th>操作类型</th>
                                                        <th>设备/模板</th>
                                                        <th>操作内容</th>
                                                        <th>操作人</th>
                                                        <th>状态</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="configLogsTable">
                                                    <!-- 日志数据 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            ${this.getConfigCSS()}
        `;
    }

    /**
     * 获取配置样式CSS
     */
    getConfigCSS() {
        return `
            <style>
                .device-config-container {
                    padding: 20px;
                }

                .config-header {
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 1px solid #e9ecef;
                }

                .config-controls .btn {
                    margin-left: 10px;
                }

                .template-list-panel, .template-editor-panel {
                    background: white;
                    border-radius: 8px;
                    padding: 20px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    height: 600px;
                }

                .template-list {
                    max-height: 500px;
                    overflow-y: auto;
                }

                .template-item {
                    padding: 15px;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    margin-bottom: 10px;
                    cursor: pointer;
                    transition: all 0.2s;
                }

                .template-item:hover {
                    background: #f8f9fa;
                    border-color: #007bff;
                }

                .template-item.active {
                    background: #e3f2fd;
                    border-color: #007bff;
                }

                .template-editor {
                    height: 500px;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    padding: 20px;
                }

                .empty-state {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                }

                .device-selector-panel, .config-preview-panel {
                    background: white;
                    border-radius: 8px;
                    padding: 20px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    height: 600px;
                }

                .device-list {
                    max-height: 450px;
                    overflow-y: auto;
                }

                .device-item {
                    padding: 12px;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    margin-bottom: 8px;
                    cursor: pointer;
                    transition: all 0.2s;
                    display: flex;
                    align-items: center;
                }

                .device-item:hover {
                    background: #f8f9fa;
                }

                .device-item.selected {
                    background: #e3f2fd;
                    border-color: #007bff;
                }

                .device-checkbox {
                    margin-right: 10px;
                }

                .device-info {
                    flex: 1;
                }

                .device-name {
                    font-weight: 500;
                    margin-bottom: 2px;
                }

                .device-status {
                    font-size: 12px;
                    color: #6c757d;
                }

                .config-form {
                    max-height: 400px;
                    overflow-y: auto;
                }

                .fingerprint-list {
                    max-height: 300px;
                    overflow-y: auto;
                }

                .fingerprint-item {
                    padding: 10px;
                    border: 1px solid #e9ecef;
                    border-radius: 4px;
                    margin-bottom: 8px;
                    font-family: monospace;
                    font-size: 12px;
                }

                .security-policies {
                    max-height: 400px;
                    overflow-y: auto;
                }

                .policy-item {
                    padding: 15px;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    margin-bottom: 10px;
                }

                .policy-toggle {
                    float: right;
                }

                .log-filters {
                    display: flex;
                    gap: 10px;
                }

                .loading-spinner {
                    text-align: center;
                    padding: 40px;
                }

                .status-badge {
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .status-online {
                    background: #d4edda;
                    color: #155724;
                }

                .status-offline {
                    background: #f8d7da;
                    color: #721c24;
                }
            </style>
        `;
    }

    /**
     * 绑定事件监听
     */
    bindEvents() {
        // 创建模板按钮
        document.getElementById('createTemplate')?.addEventListener('click', () => {
            this.showTemplateModal();
        });

        // 批量配置按钮
        document.getElementById('batchConfig')?.addEventListener('click', () => {
            this.showBatchConfigModal();
        });

        // 扫描指纹按钮
        document.getElementById('scanFingerprints')?.addEventListener('click', () => {
            this.scanDeviceFingerprints();
        });

        // 更新指纹按钮
        document.getElementById('updateFingerprints')?.addEventListener('click', () => {
            this.updateDeviceFingerprints();
        });
    }

    /**
     * 加载配置模板
     */
    async loadConfigTemplates() {
        try {
            const response = await this.makeApiRequest('/config.php?action=templates', 'GET');
            
            if (response.error_code === 0) {
                this.configTemplates = response.data.templates;
                this.renderTemplateList();
            } else {
                this.showError('加载配置模板失败: ' + response.error_message);
            }
        } catch (error) {
            console.error('加载配置模板失败:', error);
            this.showError('加载配置模板失败');
        }
    }

    /**
     * 渲染模板列表
     */
    renderTemplateList() {
        const container = document.getElementById('templateList');
        if (!container) return;

        if (Object.keys(this.configTemplates).length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-file-earmark-plus" style="font-size: 2rem; color: #6c757d;"></i>
                    <p class="text-muted">暂无配置模板</p>
                    <button class="btn btn-primary btn-sm" onclick="deviceConfigManager.showTemplateModal()">
                        创建第一个模板
                    </button>
                </div>
            `;
            return;
        }

        container.innerHTML = Object.values(this.configTemplates).map(template => `
            <div class="template-item" data-template-id="${template.id}" onclick="deviceConfigManager.selectTemplate('${template.id}')">
                <div class="template-name">${template.name}</div>
                <div class="template-description text-muted">${template.description || '无描述'}</div>
                <div class="template-meta">
                    <span class="badge bg-secondary">${template.category}</span>
                    <small class="text-muted ms-2">${this.formatDateTime(template.created_at)}</small>
                </div>
            </div>
        `).join('');
    }

    /**
     * 选择模板
     */
    selectTemplate(templateId) {
        // 更新选中状态
        document.querySelectorAll('.template-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-template-id="${templateId}"]`)?.classList.add('active');

        // 加载模板编辑器
        this.loadTemplateEditor(templateId);
    }

    /**
     * 加载模板编辑器
     */
    loadTemplateEditor(templateId) {
        const template = this.configTemplates[templateId];
        if (!template) return;

        const editor = document.getElementById('templateEditor');
        if (!editor) return;

        editor.innerHTML = `
            <div class="template-editor-content">
                <div class="template-header mb-3">
                    <h6>${template.name}</h6>
                    <div class="template-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="deviceConfigManager.editTemplate('${templateId}')">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="deviceConfigManager.duplicateTemplate('${templateId}')">
                            <i class="bi bi-files"></i> 复制
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deviceConfigManager.deleteTemplate('${templateId}')">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
                <div class="template-config">
                    <pre><code>${JSON.stringify(template.config, null, 2)}</code></pre>
                </div>
                <div class="template-usage mt-3">
                    <h6>使用统计</h6>
                    <p class="text-muted">已应用到 ${template.usage_count || 0} 台设备</p>
                </div>
            </div>
        `;
    }

    /**
     * 扫描设备指纹
     */
    async scanDeviceFingerprints() {
        try {
            this.showInfo('正在扫描设备指纹...');
            
            const response = await this.makeApiRequest('/security.php?action=scan_fingerprints', 'POST');
            
            if (response.error_code === 0) {
                this.showSuccess('设备指纹扫描完成');
                this.loadDeviceFingerprints();
            } else {
                this.showError('扫描设备指纹失败: ' + response.error_message);
            }
        } catch (error) {
            console.error('扫描设备指纹失败:', error);
            this.showError('扫描设备指纹失败');
        }
    }

    /**
     * 更新设备指纹
     */
    async updateDeviceFingerprints() {
        try {
            this.showInfo('正在更新设备指纹...');
            
            const response = await this.makeApiRequest('/security.php?action=update_fingerprints', 'POST');
            
            if (response.error_code === 0) {
                this.showSuccess('设备指纹更新完成');
                this.loadDeviceFingerprints();
            } else {
                this.showError('更新设备指纹失败: ' + response.error_message);
            }
        } catch (error) {
            console.error('更新设备指纹失败:', error);
            this.showError('更新设备指纹失败');
        }
    }

    /**
     * API请求方法
     */
    async makeApiRequest(url, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
            }
        };

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(this.apiBase + url, options);
        return await response.json();
    }

    /**
     * 工具方法
     */
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    showSuccess(message) {
        console.log('SUCCESS:', message);
    }

    showError(message) {
        console.log('ERROR:', message);
    }

    showInfo(message) {
        console.log('INFO:', message);
    }
}

// 全局实例
window.DeviceConfigManager = DeviceConfigManager;

if (typeof window !== 'undefined') {
    window.deviceConfigManager = new DeviceConfigManager();
} 