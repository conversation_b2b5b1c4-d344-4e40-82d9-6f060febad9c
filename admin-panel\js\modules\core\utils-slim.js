/**
 * 核心工具模块 - 精简版
 * 只保留必要的工具函数，移除冗余功能
 * 
 * <AUTHOR>
 * @version 2.0.0 - 精简优化版本
 */

// 全局配置 (简化版)
if (typeof window.CONFIG === 'undefined') {
    window.CONFIG = {
        API_BASE_URL: '/api',
        TOKEN_KEY: 'auth_token',
        USER_KEY: 'user_info'
    };
}

// 精简版工具类
class Utils {
    constructor() {
        this.messageContainer = null;
        this.debounceTimers = new Map();
        this.init();
    }
    
    init() {
        this.createMessageContainer();
        console.log('Utils initialized');
    }
    
    // === 消息提示系统 ===
    createMessageContainer() {
        if (!document.getElementById('admin-message-container')) {
            const container = document.createElement('div');
            container.id = 'admin-message-container';
            container.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                max-width: 400px; pointer-events: none;
            `;
            document.body.appendChild(container);
            this.messageContainer = container;
        }
    }
    
    showMessage(message, type = 'info', duration = 3000) {
        const container = this.messageContainer || document.getElementById('admin-message-container');
        if (!container) return;
        
        const alertElement = document.createElement('div');
        alertElement.className = `alert alert-${this.getAlertType(type)} alert-dismissible fade show mb-2`;
        alertElement.style.cssText = 'pointer-events: auto; animation: slideInRight 0.3s ease;';
        alertElement.innerHTML = `
            <i class="${this.getIcon(type)} me-2"></i>${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        container.appendChild(alertElement);
        
        setTimeout(() => {
            if (alertElement.parentNode) {
                alertElement.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => alertElement.remove(), 300);
            }
        }, duration);
    }
    
    getAlertType(type) {
        const types = { success: 'success', error: 'danger', warning: 'warning', info: 'info' };
        return types[type] || 'info';
    }
    
    getIcon(type) {
        const icons = { 
            success: 'bi-check-circle', error: 'bi-exclamation-triangle',
            warning: 'bi-exclamation-triangle', info: 'bi-info-circle'
        };
        return icons[type] || 'bi-info-circle';
    }
    
    showSuccess(message, duration = 3000) { this.showMessage(message, 'success', duration); }
    showError(message, duration = 5000) { this.showMessage(message, 'error', duration); }
    showWarning(message, duration = 4000) { this.showMessage(message, 'warning', duration); }
    showInfo(message, duration = 3000) { this.showMessage(message, 'info', duration); }
    
    // === 加载状态管理 ===
    showLoading(element, text = '加载中...') {
        if (!element) return;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.style.cssText = `
            position: absolute; top: 0; left: 0; right: 0; bottom: 0;
            background: rgba(255,255,255,0.8); display: flex;
            align-items: center; justify-content: center; z-index: 1000;
        `;
        overlay.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary mb-2"></div>
                <div class="small text-muted">${text}</div>
            </div>
        `;
        
        element.appendChild(overlay);
    }
    
    hideLoading(element) {
        if (!element) return;
        const overlay = element.querySelector('.loading-overlay');
        if (overlay) overlay.remove();
    }
    
    // === 格式化工具 ===
    formatMoney(amount, currency = '¥') {
        if (amount === null || amount === undefined) return '-';
        const num = parseFloat(amount);
        if (isNaN(num)) return '-';
        return currency + num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }
    
    formatDateTime(datetime, format = 'full') {
        if (!datetime) return '-';
        const date = new Date(datetime);
        if (isNaN(date.getTime())) return '-';
        
        const options = {
            full: { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' },
            date: { year: 'numeric', month: '2-digit', day: '2-digit' },
            time: { hour: '2-digit', minute: '2-digit', second: '2-digit' },
            short: { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }
        };
        
        return date.toLocaleString('zh-CN', options[format] || options.full);
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    // === 验证工具 ===
    validateEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }
    
    validatePhone(phone) {
        return /^1[3-9]\d{9}$/.test(phone);
    }
    
    validateUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }
    
    // === 防抖节流 ===
    debounce(func, delay = 300, key = 'default') {
        if (this.debounceTimers.has(key)) {
            clearTimeout(this.debounceTimers.get(key));
        }
        
        const timer = setTimeout(() => {
            func();
            this.debounceTimers.delete(key);
        }, delay);
        
        this.debounceTimers.set(key, timer);
    }
    
    throttle(func, delay = 300) {
        let lastCall = 0;
        return function(...args) {
            const now = Date.now();
            if (now - lastCall >= delay) {
                lastCall = now;
                return func.apply(this, args);
            }
        };
    }
    
    // === DOM工具 ===
    $(selector, parent = document) {
        return parent.querySelector(selector);
    }
    
    $$(selector, parent = document) {
        return Array.from(parent.querySelectorAll(selector));
    }
    
    createElement(tag, attrs = {}, html = '') {
        const element = document.createElement(tag);
        Object.entries(attrs).forEach(([key, value]) => {
            if (key === 'className') element.className = value;
            else if (key === 'innerHTML') element.innerHTML = value;
            else element.setAttribute(key, value);
        });
        if (html) element.innerHTML = html;
        return element;
    }
    
    // === 数据工具 ===
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const copy = {};
            Object.keys(obj).forEach(key => copy[key] = this.deepClone(obj[key]));
            return copy;
        }
    }
    
    // === API请求工具 ===
    async apiRequest(url, options = {}) {
        const token = localStorage.getItem(window.CONFIG.TOKEN_KEY);
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        if (finalOptions.headers && options.headers) {
            finalOptions.headers = { ...defaultOptions.headers, ...options.headers };
        }
        
        try {
            const response = await fetch(url, finalOptions);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }
    
    // === 简单缓存 ===
    setCache(key, value, ttl = 300000) { // 5分钟默认
        const item = { value, expiry: Date.now() + ttl };
        localStorage.setItem(`cache_${key}`, JSON.stringify(item));
    }
    
    getCache(key) {
        try {
            const item = JSON.parse(localStorage.getItem(`cache_${key}`));
            if (!item || Date.now() > item.expiry) {
                localStorage.removeItem(`cache_${key}`);
                return null;
            }
            return item.value;
        } catch {
            return null;
        }
    }
    
    clearCache(key) {
        if (key) {
            localStorage.removeItem(`cache_${key}`);
        } else {
            Object.keys(localStorage).forEach(k => {
                if (k.startsWith('cache_')) localStorage.removeItem(k);
            });
        }
    }
}

// 导出到全局
window.Utils = Utils;

// 错误处理
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
});

console.log('✅ 精简版工具模块加载完成'); 