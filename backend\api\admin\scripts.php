<?php
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

$auth = TenantAuth::init();
if (!$auth->requireAdmin()) exit;
$db = $auth->getDatabase();

try {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'brands':
            handleGetDeviceBrands();
            break;
        case 'matrix':
            handleGetScriptMatrix();
            break;
        case 'brand_script':
            handleGetBrandScript();
            break;
        case 'upload_brand':
            handleUploadBrandScript();
            break;
        case 'upload_encrypted':
            handleUploadScriptEncrypted();
            break;
        case 'download':
            handleDownloadScript();
            break;
        case 'delete':
            handleDeleteScript();
            break;
        case 'versions':
            handleGetScriptVersions();
            break;
        default:
            handleGetScriptMatrix();
    }
} catch (Exception $e) {
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 获取设备品牌列表
function handleGetDeviceBrands() {
    global $db, $auth;
    
    try {
        $brands = $db->fetchAll(
            "SELECT DISTINCT brand FROM devices WHERE brand IS NOT NULL AND brand != '' ORDER BY brand"
        );
        
        $brandList = array();
        foreach ($brands as $brand) {
            $brandList[] = $brand['brand'];
        }
        
        $auth->sendSuccess($brandList);
        
    } catch (Exception $e) {
        $auth->sendError('获取设备品牌失败: ' . $e->getMessage(), 500);
    }
}

// 获取脚本矩阵
function handleGetScriptMatrix() {
    global $db, $auth;
    
    try {
        // 获取所有品牌
        $brands = $db->fetchAll(
            "SELECT DISTINCT brand FROM devices WHERE brand IS NOT NULL AND brand != '' ORDER BY brand"
        );
        
        // 获取脚本类型
        $scriptTypes = array('payment', 'monitor', 'auto_click', 'screenshot', 'heartbeat');
        
        $matrix = array();
        
        foreach ($brands as $brandRow) {
            $brand = $brandRow['brand'];
            $brandScripts = array();
            
            foreach ($scriptTypes as $type) {
                // 检查是否存在该品牌和类型的脚本
                $script = $db->fetch(
                    "SELECT * FROM device_scripts WHERE brand = ? AND script_type = ? ORDER BY version DESC LIMIT 1",
                    array($brand, $type)
                );
                
                $brandScripts[$type] = array(
                    'exists' => $script ? true : false,
                    'version' => $script ? $script['version'] : null,
                    'updated_at' => $script ? date('Y-m-d H:i:s', strtotime($script['updated_at'])) : null,
                    'file_size' => $script ? formatFileSize($script['file_size']) : null
                );
            }
            
            $matrix[$brand] = $brandScripts;
        }
        
        $auth->sendSuccess(array(
            'brands' => array_column($brands, 'brand'),
            'script_types' => $scriptTypes,
            'matrix' => $matrix
        ));
        
    } catch (Exception $e) {
        $auth->sendError('获取脚本矩阵失败: ' . $e->getMessage(), 500);
    }
}

// 获取品牌脚本
function handleGetBrandScript() {
    global $db, $auth;
    
    try {
        $brand = isset($_GET['brand']) ? trim($_GET['brand']) : '';
        $scriptType = isset($_GET['script_type']) ? trim($_GET['script_type']) : '';
        
        if (!$brand || !$scriptType) {
            $auth->sendError('缺少品牌或脚本类型参数', 400);
            return;
        }
        
        // 获取最新版本的脚本
        $script = $db->fetch(
            "SELECT * FROM device_scripts WHERE brand = ? AND script_type = ? ORDER BY version DESC LIMIT 1",
            array($brand, $scriptType)
        );
        
        if (!$script) {
            $auth->sendError('脚本不存在', 404);
            return;
        }
        
        // 解密脚本内容
        $decryptedContent = decryptScript($script['encrypted_content']);
        
        $script['content'] = $decryptedContent;
        $script['created_at'] = date('Y-m-d H:i:s', strtotime($script['created_at']));
        $script['updated_at'] = date('Y-m-d H:i:s', strtotime($script['updated_at']));
        $script['file_size_formatted'] = formatFileSize($script['file_size']);
        
        // 移除加密内容字段
        unset($script['encrypted_content']);
        
        $auth->sendSuccess($script);
        
    } catch (Exception $e) {
        $auth->sendError('获取脚本失败: ' . $e->getMessage(), 500);
    }
}

// 上传品牌脚本
function handleUploadBrandScript() {
    global $db, $auth;
    
    try {
        $brand = isset($_POST['brand']) ? trim($_POST['brand']) : '';
        $scriptType = isset($_POST['script_type']) ? trim($_POST['script_type']) : '';
        $version = isset($_POST['version']) ? trim($_POST['version']) : '';
        $description = isset($_POST['description']) ? trim($_POST['description']) : '';
        
        if (!$brand || !$scriptType || !$version) {
            $auth->sendError('缺少必填参数', 400);
            return;
        }
        
        // 检查文件上传
        if (!isset($_FILES['script_file']) || $_FILES['script_file']['error'] !== UPLOAD_ERR_OK) {
            $auth->sendError('文件上传失败', 400);
            return;
        }
        
        $file = $_FILES['script_file'];
        $content = file_get_contents($file['tmp_name']);
        $encryptedContent = encryptScript($content);
        
        // 检查版本是否已存在
        $existingScript = $db->fetch(
            "SELECT id FROM device_scripts WHERE brand = ? AND script_type = ? AND version = ?",
            array($brand, $scriptType, $version)
        );
        
        if ($existingScript) {
            $auth->sendError('该版本脚本已存在', 400);
            return;
        }
        
        // 插入脚本记录
        $sql = "INSERT INTO device_scripts (brand, script_type, version, encrypted_content, file_size, description, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        $db->execute($sql, array(
            $brand,
            $scriptType,
            $version,
            $encryptedContent,
            $file['size'],
            $description
        ));
        
        $auth->logUserAction('script_upload', 'device_script', $db->lastInsertId(), "上传脚本: {$brand}-{$scriptType}-{$version}");
        $auth->sendSuccess(array('id' => $db->lastInsertId()), '脚本上传成功');
        
    } catch (Exception $e) {
        $auth->sendError('上传脚本失败: ' . $e->getMessage(), 500);
    }
}

// 上传加密脚本
function handleUploadScriptEncrypted() {
    global $db, $auth;
    
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $brand = isset($input['brand']) ? trim($input['brand']) : '';
        $scriptType = isset($input['script_type']) ? trim($input['script_type']) : '';
        $version = isset($input['version']) ? trim($input['version']) : '';
        $encryptedContent = isset($input['encrypted_content']) ? $input['encrypted_content'] : '';
        $description = isset($input['description']) ? trim($input['description']) : '';
        
        if (!$brand || !$scriptType || !$version || !$encryptedContent) {
            $auth->sendError('缺少必填参数', 400);
            return;
        }
        
        // 检查版本是否已存在
        $existingScript = $db->fetch(
            "SELECT id FROM device_scripts WHERE brand = ? AND script_type = ? AND version = ?",
            array($brand, $scriptType, $version)
        );
        
        if ($existingScript) {
            $auth->sendError('该版本脚本已存在', 400);
            return;
        }
        
        // 插入脚本记录
        $sql = "INSERT INTO device_scripts (brand, script_type, version, encrypted_content, file_size, description, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        $db->execute($sql, array(
            $brand,
            $scriptType,
            $version,
            $encryptedContent,
            strlen($encryptedContent),
            $description
        ));
        
        $auth->logUserAction('script_upload', 'device_script', $db->lastInsertId(), "上传加密脚本: {$brand}-{$scriptType}-{$version}");
        $auth->sendSuccess(array('id' => $db->lastInsertId()), '加密脚本上传成功');
        
    } catch (Exception $e) {
        $auth->sendError('上传加密脚本失败: ' . $e->getMessage(), 500);
    }
}

// 下载脚本
function handleDownloadScript() {
    global $db, $auth;
    
    try {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if (!$id) {
            $auth->sendError('缺少脚本ID', 400);
            return;
        }
        
        $script = $db->fetch("SELECT * FROM device_scripts WHERE id = ?", array($id));
        
        if (!$script) {
            $auth->sendError('脚本不存在', 404);
            return;
        }
        
        // 解密脚本内容
        $content = decryptScript($script['encrypted_content']);
        
        // 设置下载头
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $script['brand'] . '_' . $script['script_type'] . '_' . $script['version'] . '.js"');
        header('Content-Length: ' . strlen($content));
        
        echo $content;
        
    } catch (Exception $e) {
        $auth->sendError('下载脚本失败: ' . $e->getMessage(), 500);
    }
}

// 删除脚本
function handleDeleteScript() {
    global $db, $auth;
    
    try {
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if (!$id) {
            $auth->sendError('缺少脚本ID', 400);
            return;
        }
        
        $script = $db->fetch("SELECT * FROM device_scripts WHERE id = ?", array($id));
        
        if (!$script) {
            $auth->sendError('脚本不存在', 404);
            return;
        }
        
        $db->execute("DELETE FROM device_scripts WHERE id = ?", array($id));
        
        $auth->logUserAction('script_delete', 'device_script', $id, "删除脚本: {$script['brand']}-{$script['script_type']}-{$script['version']}");
        $auth->sendSuccess(null, '脚本删除成功');
        
    } catch (Exception $e) {
        $auth->sendError('删除脚本失败: ' . $e->getMessage(), 500);
    }
}

// 获取脚本版本列表
function handleGetScriptVersions() {
    global $db, $auth;
    
    try {
        $brand = isset($_GET['brand']) ? trim($_GET['brand']) : '';
        $scriptType = isset($_GET['script_type']) ? trim($_GET['script_type']) : '';
        
        if (!$brand || !$scriptType) {
            $auth->sendError('缺少品牌或脚本类型参数', 400);
            return;
        }
        
        $versions = $db->fetchAll(
            "SELECT id, version, description, file_size, created_at, updated_at 
             FROM device_scripts 
             WHERE brand = ? AND script_type = ? 
             ORDER BY version DESC",
            array($brand, $scriptType)
        );
        
        foreach ($versions as &$version) {
            $version['created_at'] = date('Y-m-d H:i:s', strtotime($version['created_at']));
            $version['updated_at'] = date('Y-m-d H:i:s', strtotime($version['updated_at']));
            $version['file_size_formatted'] = formatFileSize($version['file_size']);
        }
        
        $auth->sendSuccess($versions);
        
    } catch (Exception $e) {
        $auth->sendError('获取脚本版本失败: ' . $e->getMessage(), 500);
    }
}

// 加密脚本内容
function encryptScript($content) {
    return base64_encode($content); // 简单的base64编码，实际项目中应使用更安全的加密方式
}

// 解密脚本内容
function decryptScript($encryptedContent) {
    return base64_decode($encryptedContent);
}

// 格式化文件大小
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
