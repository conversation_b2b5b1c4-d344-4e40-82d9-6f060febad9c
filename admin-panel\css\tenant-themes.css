/**
 * 租户专用主题CSS
 * 根据不同租户类型提供差异化的视觉体验
 */

/* 系统管理员主题 */
.tenant-system_admin {
    --primary-color: #1890ff;
    --secondary-color: #096dd9;
    --accent-color: #40a9ff;
}

.tenant-system_admin .login-card {
    border-left: 4px solid var(--primary-color);
}

.tenant-system_admin .login-title {
    color: var(--primary-color);
}

.tenant-system_admin .btn-login {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
}

/* 平台管理员主题 */
.tenant-platform_admin {
    --primary-color: #722ed1;
    --secondary-color: #531dab;
    --accent-color: #9254de;
}

.tenant-platform_admin .login-card {
    border-left: 4px solid var(--primary-color);
}

.tenant-platform_admin .login-title {
    color: var(--primary-color);
}

.tenant-platform_admin .btn-login {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
}

/* 码商主题 */
.tenant-provider {
    --primary-color: #52c41a;
    --secondary-color: #389e0d;
    --accent-color: #73d13d;
}

.tenant-provider .login-card {
    border-left: 4px solid var(--primary-color);
    background: linear-gradient(135deg, rgba(82, 196, 26, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.tenant-provider .login-title {
    color: var(--primary-color);
}

.tenant-provider .btn-login {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
}

.tenant-provider .login-logo {
    color: var(--primary-color);
}

/* 商户主题 */
.tenant-merchant {
    --primary-color: #fa8c16;
    --secondary-color: #d4600b;
    --accent-color: #ffa940;
}

.tenant-merchant .login-card {
    border-left: 4px solid var(--primary-color);
    background: linear-gradient(135deg, rgba(250, 140, 22, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.tenant-merchant .login-title {
    color: var(--primary-color);
}

.tenant-merchant .btn-login {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
}

.tenant-merchant .login-logo {
    color: var(--primary-color);
}

.tenant-merchant .system-info {
    background: linear-gradient(135deg, rgba(250, 140, 22, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 30px;
}

/* 通用的租户样式增强 */
.tenant-provider .system-info,
.tenant-platform_admin .system-info {
    background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 10px;
    padding: 20px;
    margin-top: 30px;
}

/* 加载状态的租户特定样式 */
.tenant-merchant .loading .spinner-border {
    color: var(--primary-color);
}

.tenant-provider .loading .spinner-border {
    color: var(--primary-color);
}

.tenant-platform_admin .loading .spinner-border {
    color: var(--primary-color);
}

.tenant-system_admin .loading .spinner-border {
    color: var(--primary-color);
}

/* 表单控件焦点状态 */
.tenant-merchant .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(250, 140, 22, 0.25);
}

.tenant-provider .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(82, 196, 26, 0.25);
}

.tenant-platform_admin .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(114, 46, 209, 0.25);
}

.tenant-system_admin .form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(24, 144, 255, 0.25);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .tenant-merchant .login-card,
    .tenant-provider .login-card,
    .tenant-platform_admin .login-card,
    .tenant-system_admin .login-card {
        margin: 10px;
        padding: 30px;
    }
    
    .tenant-merchant .system-info,
    .tenant-provider .system-info,
    .tenant-platform_admin .system-info,
    .tenant-system_admin .system-info {
        padding: 15px;
        margin-top: 20px;
    }
}

/* 动画效果 */
@keyframes tenantThemeSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tenant-merchant .login-card,
.tenant-provider .login-card,
.tenant-platform_admin .login-card,
.tenant-system_admin .login-card {
    animation: tenantThemeSlideIn 0.6s ease-out;
} 