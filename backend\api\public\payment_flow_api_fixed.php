<?php
/**
 * 支付流程管理API接口 - PHP 5.6兼容版本
 * 
 * 提供完整的支付流程操作接口：
 * 1. 创建支付请求
 * 2. 查询支付状态
 * 3. 处理支付通知
 * 4. 取消支付请求
 * 5. 获取支付统计
 * 6. 清理过期请求
 * 
 * <AUTHOR> System
 * @version 1.0
 */

require_once dirname(__FILE__) . '/config.php';
require_once dirname(__FILE__) . '/PaymentFlowManager.php';
require_once dirname(__FILE__) . '/auth.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $paymentFlowManager = new PaymentFlowManager();
    
    // 支持两种路由方式：?action=xxx 或 ?do=xxx
    $action = isset($_GET['action']) ? $_GET['action'] : (isset($_GET['do']) ? $_GET['do'] : '');
    
    switch ($action) {
        case 'create_payment':
            handleCreatePayment($paymentFlowManager);
            break;
            
        case 'query_payment':
            handleQueryPayment($paymentFlowManager);
            break;
            
        case 'payment_notify':
            handlePaymentNotify($paymentFlowManager);
            break;
            
        case 'cancel_payment':
            handleCancelPayment($paymentFlowManager);
            break;
            
        case 'payment_statistics':
            handlePaymentStatistics($paymentFlowManager);
            break;
            
        case 'clean_expired':
            handleCleanExpired($paymentFlowManager);
            break;
            
        case 'payment_list':
            handlePaymentList($paymentFlowManager);
            break;
            
        case 'retry_notification':
            handleRetryNotification($paymentFlowManager);
            break;
            
        default:
            // 返回API信息
            echo json_encode(array(
                'success' => true,
                'message' => '支付流程管理API',
                'version' => '1.0',
                'endpoints' => array(
                    'create_payment' => '创建支付请求',
                    'query_payment' => '查询支付状态',
                    'payment_notify' => '处理支付通知',
                    'cancel_payment' => '取消支付请求',
                    'payment_statistics' => '获取支付统计',
                    'clean_expired' => '清理过期请求',
                    'payment_list' => '获取支付列表',
                    'retry_notification' => '重试通知'
                )
            ));
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'success' => false,
        'error' => $e->getMessage(),
        'error_code' => 'API_ERROR'
    ));
}

/**
 * 创建支付请求
 */
function handleCreatePayment($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    // 支持JSON和表单数据
    $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';
    
    if (strpos($contentType, 'application/json') !== false) {
        $input = json_decode(file_get_contents('php://input'), true);
    } else {
        $input = $_POST;
    }
    
    if (!$input) {
        throw new Exception('无效的请求数据');
    }
    
    // 验证必需参数
    $required = array('merchant_id', 'amount', 'merchant_order_no');
    foreach ($required as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            throw new Exception("缺少必需参数: {$field}");
        }
    }
    
    // 权限验证（如果需要）
    $currentUser = getCurrentUser();
    if ($currentUser && $currentUser['user_type'] === 'merchant') {
        if ($input['merchant_id'] != $currentUser['profile_id']) {
            throw new Exception('无权为其他商户创建支付请求');
        }
    }
    
    $result = $paymentFlowManager->createPaymentRequest($input);
    
    if ($result['success']) {
        http_response_code(201);
    } else {
        http_response_code(400);
    }
    
    echo json_encode($result);
}

/**
 * 查询支付请求状态
 */
function handleQueryPayment($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持GET或POST请求');
    }
    
    // 支持GET和POST参数
    $requestId = '';
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $requestId = isset($_GET['request_id']) ? $_GET['request_id'] : '';
    } else {
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }
        $requestId = isset($input['request_id']) ? $input['request_id'] : '';
    }
    
    if (!$requestId) {
        throw new Exception('请求ID不能为空');
    }
    
    // 权限验证
    $merchantId = null;
    $currentUser = getCurrentUser();
    if ($currentUser && $currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    }
    
    $result = $paymentFlowManager->queryPaymentRequest($requestId, $merchantId);
    
    if (!$result['success']) {
        http_response_code(404);
    }
    
    echo json_encode($result);
}

/**
 * 处理支付通知（第三方支付平台回调）
 */
function handlePaymentNotify($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    // 获取通知数据（可能是JSON或表单数据）
    $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';
    
    if (strpos($contentType, 'application/json') !== false) {
        $notifyData = json_decode(file_get_contents('php://input'), true);
    } else {
        $notifyData = $_POST;
    }
    
    if (!$notifyData) {
        throw new Exception('无效的通知数据');
    }
    
    // 记录原始通知数据（用于调试）
    error_log('Payment Notification Received: ' . json_encode($notifyData));
    
    $result = $paymentFlowManager->handlePaymentNotification($notifyData);
    
    if ($result['success']) {
        // 返回成功响应（第三方平台期望的格式）
        echo json_encode(array(
            'code' => 'SUCCESS',
            'message' => 'OK'
        ));
    } else {
        http_response_code(400);
        echo json_encode(array(
            'code' => 'FAIL',
            'message' => $result['error']
        ));
    }
}

/**
 * 获取支付统计数据
 */
function handlePaymentStatistics($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持GET或POST请求');
    }
    
    // 简化版本：不需要权限验证，返回基础统计
    $merchantId = null;
    $filters = array();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $merchantId = isset($_GET['merchant_id']) ? $_GET['merchant_id'] : null;
        $filters = array(
            'start_date' => isset($_GET['start_date']) ? $_GET['start_date'] : '',
            'end_date' => isset($_GET['end_date']) ? $_GET['end_date'] : ''
        );
    } else {
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }
        $merchantId = isset($input['merchant_id']) ? $input['merchant_id'] : null;
        $filters = array(
            'start_date' => isset($input['start_date']) ? $input['start_date'] : '',
            'end_date' => isset($input['end_date']) ? $input['end_date'] : ''
        );
    }
    
    $result = $paymentFlowManager->getPaymentFlowStatistics($merchantId, $filters);
    
    if (!$result['success']) {
        http_response_code(400);
    }
    
    echo json_encode($result);
}

/**
 * 清理过期支付请求
 */
function handleCleanExpired($paymentFlowManager) {
    $result = $paymentFlowManager->cleanExpiredRequests();
    
    if (!$result['success']) {
        http_response_code(400);
    }
    
    echo json_encode($result);
}

/**
 * 获取支付请求列表
 */
function handlePaymentList($paymentFlowManager) {
    $page = 1;
    $limit = 20;
    $filters = array();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
        $filters = array(
            'merchant_id' => isset($_GET['merchant_id']) ? $_GET['merchant_id'] : null,
            'status' => isset($_GET['status']) ? $_GET['status'] : '',
            'start_date' => isset($_GET['start_date']) ? $_GET['start_date'] : '',
            'end_date' => isset($_GET['end_date']) ? $_GET['end_date'] : '',
            'search' => isset($_GET['search']) ? $_GET['search'] : ''
        );
    } else {
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }
        $page = isset($input['page']) ? intval($input['page']) : 1;
        $limit = isset($input['limit']) ? intval($input['limit']) : 20;
        $filters = array(
            'merchant_id' => isset($input['merchant_id']) ? $input['merchant_id'] : null,
            'status' => isset($input['status']) ? $input['status'] : '',
            'start_date' => isset($input['start_date']) ? $input['start_date'] : '',
            'end_date' => isset($input['end_date']) ? $input['end_date'] : '',
            'search' => isset($input['search']) ? $input['search'] : ''
        );
    }
    
    $result = getPaymentRequestList($filters['merchant_id'], $page, $limit, $filters);
    echo json_encode($result);
}

/**
 * 重试通知
 */
function handleRetryNotification($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $requestId = isset($input['request_id']) ? $input['request_id'] : '';
    if (!$requestId) {
        throw new Exception('请求ID不能为空');
    }
    
    $result = retryPaymentNotification($requestId, null);
    echo json_encode($result);
}

/**
 * 获取当前用户信息（简化版）
 */
function getCurrentUser() {
    // 简化版本：返回null表示无需权限验证
    return null;
}

/**
 * 获取支付请求列表
 */
function getPaymentRequestList($merchantId, $page, $limit, $filters) {
    global $pdo;
    
    try {
        $where = array('1=1');
        $params = array();
        
        if ($merchantId) {
            $where[] = 'merchant_id = ?';
            $params[] = $merchantId;
        }
        
        if (!empty($filters['status'])) {
            $where[] = 'status = ?';
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['start_date'])) {
            $where[] = 'created_at >= ?';
            $params[] = $filters['start_date'] . ' 00:00:00';
        }
        
        if (!empty($filters['end_date'])) {
            $where[] = 'created_at <= ?';
            $params[] = $filters['end_date'] . ' 23:59:59';
        }
        
        if (!empty($filters['search'])) {
            $where[] = '(request_id LIKE ? OR merchant_order_no LIKE ? OR subject LIKE ?)';
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        $whereClause = implode(' AND ', $where);
        
        // 获取总数
        $countSql = "SELECT COUNT(*) FROM payment_requests WHERE {$whereClause}";
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();
        
        // 获取列表数据
        $offset = ($page - 1) * $limit;
        $listSql = "SELECT * FROM payment_requests WHERE {$whereClause} ORDER BY created_at DESC LIMIT {$limit} OFFSET {$offset}";
        $listStmt = $pdo->prepare($listSql);
        $listStmt->execute($params);
        $requests = $listStmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 格式化数据
        $formattedRequests = array();
        foreach ($requests as $request) {
            $formattedRequests[] = formatPaymentRequestForList($request);
        }
        
        return array(
            'success' => true,
            'data' => array(
                'list' => $formattedRequests,
                'pagination' => array(
                    'page' => $page,
                    'limit' => $limit,
                    'total' => intval($total),
                    'pages' => ceil($total / $limit)
                )
            )
        );
        
    } catch (Exception $e) {
        return array(
            'success' => false,
            'error' => $e->getMessage()
        );
    }
}

/**
 * 重试支付通知
 */
function retryPaymentNotification($requestId, $currentUser) {
    global $pdo;
    
    try {
        // 查找支付请求
        $stmt = $pdo->prepare("SELECT * FROM payment_requests WHERE request_id = ?");
        $stmt->execute(array($requestId));
        $request = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$request) {
            return array(
                'success' => false,
                'error' => '支付请求不存在'
            );
        }
        
        if ($request['status'] !== 'paid') {
            return array(
                'success' => false,
                'error' => '只有已支付的请求才能重试通知'
            );
        }
        
        if (empty($request['notification_url'])) {
            return array(
                'success' => false,
                'error' => '该请求没有配置通知地址'
            );
        }
        
        // 准备通知数据
        $notifyData = array(
            'request_id' => $request['request_id'],
            'merchant_order_no' => $request['merchant_order_no'],
            'amount' => $request['amount'],
            'actual_amount' => isset($request['actual_amount']) ? $request['actual_amount'] : $request['amount'],
            'status' => $request['status'],
            'paid_at' => $request['paid_at'],
            'payer_name' => isset($request['payer_name']) ? $request['payer_name'] : '',
            'transaction_id' => isset($request['transaction_id']) ? $request['transaction_id'] : ''
        );
        
        // 发送通知
        $response = sendHttpRequest($request['notification_url'], $notifyData);
        
        // 记录通知日志
        $logStmt = $pdo->prepare("
            INSERT INTO merchant_notification_tasks 
            (request_id, notification_url, notification_data, response_data, status, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $status = ($response !== false) ? 'success' : 'failed';
        $logStmt->execute(array(
            $requestId,
            $request['notification_url'],
            json_encode($notifyData),
            json_encode($response),
            $status
        ));
        
        if ($response !== false) {
            return array(
                'success' => true,
                'message' => '通知发送成功',
                'response' => $response
            );
        } else {
            return array(
                'success' => false,
                'error' => '通知发送失败'
            );
        }
        
    } catch (Exception $e) {
        return array(
            'success' => false,
            'error' => $e->getMessage()
        );
    }
}

/**
 * 格式化支付请求数据用于列表显示
 */
function formatPaymentRequestForList($request) {
    return array(
        'id' => intval($request['id']),
        'request_id' => $request['request_id'],
        'merchant_id' => intval($request['merchant_id']),
        'merchant_name' => isset($request['merchant_name']) ? $request['merchant_name'] : '',
        'merchant_order_no' => $request['merchant_order_no'],
        'amount' => floatval($request['amount']),
        'actual_amount' => floatval(isset($request['actual_amount']) ? $request['actual_amount'] : 0),
        'fee' => floatval(isset($request['fee']) ? $request['fee'] : 0),
        'status' => $request['status'],
        'status_text' => getStatusText($request['status']),
        'qr_code_url' => isset($request['qr_code_url']) ? $request['qr_code_url'] : '',
        'subject' => isset($request['subject']) ? $request['subject'] : '',
        'client_ip' => isset($request['client_ip']) ? $request['client_ip'] : '',
        'created_at' => $request['created_at'],
        'paid_at' => $request['paid_at'],
        'expired_at' => $request['expired_at'],
        'account_name' => isset($request['account_name']) ? $request['account_name'] : '',
        'account_number' => isset($request['account_number']) ? $request['account_number'] : ''
    );
}

/**
 * 获取状态文本
 */
function getStatusText($status) {
    $statusMap = array(
        'pending' => '待支付',
        'paid' => '已支付',
        'expired' => '已过期',
        'cancelled' => '已取消'
    );
    
    return isset($statusMap[$status]) ? $statusMap[$status] : $status;
}

/**
 * 发送HTTP请求
 */
function sendHttpRequest($url, $data) {
    $postData = http_build_query($data);
    
    $context = stream_context_create(array(
        'http' => array(
            'method' => 'POST',
            'header' => 'Content-type: application/x-www-form-urlencoded',
            'content' => $postData,
            'timeout' => 30
        )
    ));
    
    $result = @file_get_contents($url, false, $context);
    return $result;
}
?> 