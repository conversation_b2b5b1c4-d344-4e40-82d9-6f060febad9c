<?php
/**
 * 多租户用户管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'users':
        case 'get_users':
            handleGetTenantUsers($auth);
            break;
            
        case 'add_user':
        case 'create_user':
            handleCreateTenantUser($auth);
            break;
            
        case 'edit_user':
        case 'update_user':
            handleUpdateTenantUser($auth);
            break;
            
        case 'delete_user':
            handleDeleteTenantUser($auth);
            break;
            
        case 'update_user_status':
            handleUpdateTenantUserStatus($auth);
            break;
            
        case 'get_user':
        case 'get_user_detail':
            handleGetTenantUser($auth);
            break;
            
        case 'get_user_types':
            handleGetAvailableUserTypes($auth);
            break;
            
        default:
            $auth->sendError('接口不存在', 404);
    }
} catch (Exception $e) {
    error_log("Users API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

/**
 * 获取租户用户列表
 */
function handleGetTenantUsers($auth) {
    global $db, $domainInfo;
    
    // 检查权限
    if (!$auth->checkPermission('user.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    // 分页参数
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    // 搜索参数
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $userType = isset($_GET['user_type']) ? $_GET['user_type'] : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    
    // 构建租户过滤条件
    $tenantFilter = buildTenantUserFilter($domainInfo);
    $whereConditions = $tenantFilter['conditions'];
    $params = $tenantFilter['params'];
    
    // 添加搜索条件
    if (!empty($search)) {
        $whereConditions[] = "(username LIKE ? OR email LIKE ? OR real_name LIKE ?)";
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if (!empty($userType) && $userType !== 'all') {
        $whereConditions[] = "user_type = ?";
        $params[] = $userType;
    }
    
    if ($status !== '' && $status !== 'all') {
        $whereConditions[] = "status = ?";
        $params[] = $status;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $countSql = "SELECT COUNT(*) as total FROM users {$whereClause}";
    $totalResult = $db->fetch($countSql, $params);
    $total = $totalResult['total'];
    
    // 获取用户列表
    $sql = "SELECT 
                id, username, email, real_name, user_type, status, 
                last_login_at, created_at, updated_at
            FROM users 
            {$whereClause}
            ORDER BY created_at DESC 
            LIMIT {$limit} OFFSET {$offset}";
    
    $users = $db->fetchAll($sql, $params);
    
    // 记录操作日志
    $auth->logUserAction('view_users', 'user', 0, '查看用户列表');
    
    $response = array(
        'users' => $users,
        'pagination' => array(
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        )
    );
    
    $auth->sendSuccess($response, '用户列表获取成功');
}

/**
 * 创建租户用户
 */
function handleCreateTenantUser($auth) {
    global $db, $domainInfo;
    
    // 检查权限
    if (!$auth->checkPermission('user.create')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证必填字段
    $requiredFields = array('username', 'password', 'email', 'user_type');
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            $auth->sendError("字段 {$field} 不能为空", 400);
            return;
        }
    }
    
    $username = trim($input['username']);
    $password = $input['password'];
    $email = trim($input['email']);
    $userType = $input['user_type'];
    $realName = isset($input['real_name']) ? trim($input['real_name']) : '';
    $status = isset($input['status']) ? intval($input['status']) : 1;
    
    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $auth->sendError('邮箱格式不正确', 400);
        return;
    }
    
    // 检查用户名是否已存在
    $existingUser = $db->fetch("SELECT id FROM users WHERE username = ?", array($username));
    if ($existingUser) {
        $auth->sendError('用户名已存在', 400);
        return;
    }
    
    // 检查邮箱是否已存在
    $existingEmail = $db->fetch("SELECT id FROM users WHERE email = ?", array($email));
    if ($existingEmail) {
        $auth->sendError('邮箱已存在', 400);
        return;
    }
    
    try {
        // 创建用户
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $sql = "INSERT INTO users (username, password, email, real_name, user_type, status, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        $result = $db->execute($sql, array(
            $username, $hashedPassword, $email, $realName, $userType, $status
        ));
        
        if ($result) {
            $userId = $db->lastInsertId();
            
            // 记录操作日志
            $auth->logUserAction('create_user', 'user', $userId, "创建用户: {$username}");
            
            $auth->sendSuccess(array('user_id' => $userId), '用户创建成功');
        } else {
            $auth->sendError('用户创建失败', 500);
        }
        
    } catch (Exception $e) {
        error_log("创建用户失败: " . $e->getMessage());
        $auth->sendError('用户创建失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 获取可用的用户类型
 */
function handleGetAvailableUserTypes($auth) {
    global $domainInfo;
    
    $userTypes = array();
    
    switch ($domainInfo['tenant_type']) {
        case 'system_admin':
            $userTypes = array(
                array('value' => 'admin', 'label' => '系统管理员'),
                array('value' => 'platform_admin', 'label' => '平台管理员'),
                array('value' => 'provider', 'label' => '码商'),
                array('value' => 'merchant', 'label' => '商户'),
                array('value' => 'employee', 'label' => '员工')
            );
            break;
            
        case 'platform_admin':
            $userTypes = array(
                array('value' => 'provider', 'label' => '码商'),
                array('value' => 'merchant', 'label' => '商户'),
                array('value' => 'employee', 'label' => '员工')
            );
            break;
            
        case 'provider':
            $userTypes = array(
                array('value' => 'employee', 'label' => '员工')
            );
            break;
            
        case 'merchant':
            $userTypes = array(
                array('value' => 'employee', 'label' => '员工')
            );
            break;
    }
    
    $auth->sendSuccess($userTypes, '用户类型获取成功');
}

// 构建租户数据过滤条件
function buildTenantUserFilter($domainInfo) {
    $conditions = array();
    $params = array();
    
    switch ($domainInfo['tenant_type']) {
        case 'system_admin':
            // 系统管理员可以查看所有用户
            break;
            
        case 'platform_admin':
            // 平台管理员只能查看本平台的用户
            $conditions[] = "(platform_id = ? OR platform_id IS NULL)";
            $params[] = $domainInfo['platform_id'];
            break;
            
        case 'provider':
            // 码商只能查看自己的员工用户
            $conditions[] = "(belongs_to_type = 'provider' AND belongs_to_id = ?)";
            $params[] = $domainInfo['tenant_id'];
            break;
            
        case 'merchant':
            // 商户只能查看自己的员工用户
            $conditions[] = "(belongs_to_type = 'merchant' AND belongs_to_id = ?)";
            $params[] = $domainInfo['tenant_id'];
            break;
    }
    
    return array(
        'conditions' => $conditions,
        'params' => $params
    );
}

// 其他处理函数（暂时简化）
function handleUpdateTenantUser($auth) {
    $auth->sendError('功能开发中', 501);
}

function handleDeleteTenantUser($auth) {
    $auth->sendError('功能开发中', 501);
}

function handleUpdateTenantUserStatus($auth) {
    $auth->sendError('功能开发中', 501);
}

function handleGetTenantUser($auth) {
    $auth->sendError('功能开发中', 501);
}
?> 