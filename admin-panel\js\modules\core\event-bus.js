/**
 * 全局事件总线系统
 * 实现模块间解耦通信、状态同步和事件管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class EventBus {
    constructor() {
        this.events = new Map();
        this.onceEvents = new Map();
        this.eventHistory = [];
        this.maxHistorySize = 100;
        this.debugMode = false;
        
        // 事件统计
        this.stats = {
            totalEvents: 0,
            totalListeners: 0,
            eventCounts: new Map()
        };
        
        console.log('🚌 EventBus initialized');
    }

    /**
     * 注册事件监听器
     * @param {string} eventName - 事件名称
     * @param {function} callback - 回调函数
     * @param {object} options - 选项
     */
    on(eventName, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('回调函数必须是function类型');
        }

        if (!this.events.has(eventName)) {
            this.events.set(eventName, []);
        }

        const listener = {
            callback,
            context: options.context || null,
            priority: options.priority || 0,
            once: false,
            id: this.generateListenerId()
        };

        const listeners = this.events.get(eventName);
        listeners.push(listener);
        
        // 按优先级排序
        listeners.sort((a, b) => b.priority - a.priority);
        
        this.stats.totalListeners++;
        
        if (this.debugMode) {
            console.log(`📝 注册事件监听器: ${eventName}`, listener);
        }

        // 返回取消监听的函数
        return () => this.off(eventName, listener.id);
    }

    /**
     * 注册一次性事件监听器
     * @param {string} eventName - 事件名称
     * @param {function} callback - 回调函数
     * @param {object} options - 选项
     */
    once(eventName, callback, options = {}) {
        const unsubscribe = this.on(eventName, (...args) => {
            callback(...args);
            unsubscribe();
        }, options);

        return unsubscribe;
    }

    /**
     * 移除事件监听器
     * @param {string} eventName - 事件名称
     * @param {string|function} listenerOrId - 监听器ID或回调函数
     */
    off(eventName, listenerOrId) {
        if (!this.events.has(eventName)) {
            return false;
        }

        const listeners = this.events.get(eventName);
        let removedCount = 0;

        if (typeof listenerOrId === 'string') {
            // 按ID移除
            const index = listeners.findIndex(l => l.id === listenerOrId);
            if (index !== -1) {
                listeners.splice(index, 1);
                removedCount = 1;
            }
        } else if (typeof listenerOrId === 'function') {
            // 按回调函数移除
            for (let i = listeners.length - 1; i >= 0; i--) {
                if (listeners[i].callback === listenerOrId) {
                    listeners.splice(i, 1);
                    removedCount++;
                }
            }
        } else {
            // 移除所有监听器
            removedCount = listeners.length;
            listeners.length = 0;
        }

        // 如果没有监听器了，删除事件
        if (listeners.length === 0) {
            this.events.delete(eventName);
        }

        this.stats.totalListeners -= removedCount;

        if (this.debugMode && removedCount > 0) {
            console.log(`🗑️ 移除事件监听器: ${eventName}, 数量: ${removedCount}`);
        }

        return removedCount > 0;
    }

    /**
     * 触发事件
     * @param {string} eventName - 事件名称
     * @param {...any} args - 事件参数
     */
    emit(eventName, ...args) {
        this.stats.totalEvents++;
        
        // 更新事件计数
        const currentCount = this.stats.eventCounts.get(eventName) || 0;
        this.stats.eventCounts.set(eventName, currentCount + 1);

        // 记录事件历史
        this.recordEvent(eventName, args);

        if (!this.events.has(eventName)) {
            if (this.debugMode) {
                console.log(`📢 触发事件: ${eventName} (无监听器)`);
            }
            return false;
        }

        const listeners = this.events.get(eventName);
        let executedCount = 0;

        if (this.debugMode) {
            console.log(`📢 触发事件: ${eventName}`, args, `监听器数量: ${listeners.length}`);
        }

        // 执行所有监听器
        for (const listener of listeners.slice()) { // 使用副本避免执行过程中修改数组
            try {
                if (listener.context) {
                    listener.callback.apply(listener.context, args);
                } else {
                    listener.callback(...args);
                }
                executedCount++;
            } catch (error) {
                console.error(`事件监听器执行错误 (${eventName}):`, error);
                
                // 触发错误事件
                this.emit('error', {
                    eventName,
                    error,
                    listener: listener.id
                });
            }
        }

        return executedCount > 0;
    }

    /**
     * 异步触发事件
     * @param {string} eventName - 事件名称
     * @param {...any} args - 事件参数
     */
    async emitAsync(eventName, ...args) {
        if (!this.events.has(eventName)) {
            return false;
        }

        const listeners = this.events.get(eventName);
        const promises = [];

        for (const listener of listeners) {
            try {
                const result = listener.context 
                    ? listener.callback.apply(listener.context, args)
                    : listener.callback(...args);
                
                if (result instanceof Promise) {
                    promises.push(result);
                }
            } catch (error) {
                console.error(`异步事件监听器执行错误 (${eventName}):`, error);
                promises.push(Promise.reject(error));
            }
        }

        if (promises.length > 0) {
            await Promise.allSettled(promises);
        }

        return true;
    }

    /**
     * 等待事件触发
     * @param {string} eventName - 事件名称
     * @param {number} timeout - 超时时间(ms)
     */
    waitFor(eventName, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                this.off(eventName, listener);
                reject(new Error(`等待事件超时: ${eventName}`));
            }, timeout);

            const listener = this.once(eventName, (...args) => {
                clearTimeout(timer);
                resolve(args);
            });
        });
    }

    /**
     * 记录事件历史
     */
    recordEvent(eventName, args) {
        const event = {
            name: eventName,
            args: this.cloneArgs(args),
            timestamp: Date.now(),
            id: this.generateEventId()
        };

        this.eventHistory.push(event);

        // 限制历史记录大小
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }

    /**
     * 克隆事件参数
     */
    cloneArgs(args) {
        try {
            return JSON.parse(JSON.stringify(args));
        } catch (error) {
            // 如果无法序列化，返回简化版本
            return args.map(arg => {
                if (typeof arg === 'object' && arg !== null) {
                    return '[Object]';
                }
                return arg;
            });
        }
    }

    /**
     * 生成监听器ID
     */
    generateListenerId() {
        return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 生成事件ID
     */
    generateEventId() {
        return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取事件监听器列表
     * @param {string} eventName - 事件名称
     */
    getListeners(eventName) {
        return this.events.get(eventName) || [];
    }

    /**
     * 获取所有事件名称
     */
    getEventNames() {
        return Array.from(this.events.keys());
    }

    /**
     * 获取事件统计信息
     */
    getStats() {
        return {
            ...this.stats,
            activeEvents: this.events.size,
            topEvents: Array.from(this.stats.eventCounts.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10)
        };
    }

    /**
     * 获取事件历史
     * @param {number} limit - 限制数量
     */
    getHistory(limit = 20) {
        return this.eventHistory.slice(-limit);
    }

    /**
     * 清理事件总线
     */
    clear() {
        this.events.clear();
        this.onceEvents.clear();
        this.eventHistory.length = 0;
        this.stats = {
            totalEvents: 0,
            totalListeners: 0,
            eventCounts: new Map()
        };
        
        console.log('🧹 EventBus cleared');
    }

    /**
     * 开启/关闭调试模式
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`🐛 EventBus debug mode: ${enabled ? 'ON' : 'OFF'}`);
    }

    /**
     * 创建命名空间事件总线
     * @param {string} namespace - 命名空间
     */
    namespace(namespace) {
        return new NamespacedEventBus(this, namespace);
    }
}

/**
 * 命名空间事件总线
 */
class NamespacedEventBus {
    constructor(eventBus, namespace) {
        this.eventBus = eventBus;
        this.namespace = namespace;
    }

    on(eventName, callback, options = {}) {
        return this.eventBus.on(`${this.namespace}:${eventName}`, callback, options);
    }

    once(eventName, callback, options = {}) {
        return this.eventBus.once(`${this.namespace}:${eventName}`, callback, options);
    }

    off(eventName, listenerOrId) {
        return this.eventBus.off(`${this.namespace}:${eventName}`, listenerOrId);
    }

    emit(eventName, ...args) {
        return this.eventBus.emit(`${this.namespace}:${eventName}`, ...args);
    }

    emitAsync(eventName, ...args) {
        return this.eventBus.emitAsync(`${this.namespace}:${eventName}`, ...args);
    }

    waitFor(eventName, timeout) {
        return this.eventBus.waitFor(`${this.namespace}:${eventName}`, timeout);
    }
}

// 创建全局事件总线实例
if (typeof window !== 'undefined') {
    window.EventBus = EventBus;
    window.eventBus = new EventBus();
    
    // 创建常用命名空间
    window.deviceEvents = window.eventBus.namespace('device');
    window.financeEvents = window.eventBus.namespace('finance');
    window.merchantEvents = window.eventBus.namespace('merchant');
    window.systemEvents = window.eventBus.namespace('system');
    
    console.log('🎯 全局事件总线已就绪');
} 