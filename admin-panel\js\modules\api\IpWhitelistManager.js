/**
 * IP白名单管理器
 * 从ApiModule.js提取的IP白名单相关功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class IpWhitelistManager {
    constructor() {
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.ipList = [];
        
        console.log('✅ IpWhitelistManager initialized');
    }

    /**
     * 统一渲染方法 - 路由系统调用
     */
    async render(container, params = {}) {
        try {
            console.log('🎯 IpWhitelistManager渲染 - 参数:', params);
            
            // 显示加载状态
            container.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><div class="mt-2">正在加载IP白名单...</div></div>';
            
            // 生成HTML内容
            container.innerHTML = this.generateIpWhitelistContent();
            
            // 设置全局引用以便HTML中的onclick调用
            window.apiModule = this;
            
            // 加载IP白名单数据
            setTimeout(() => {
                this.loadIpWhitelistData();
            }, 100);
            
        } catch (error) {
            console.error('❌ IpWhitelistManager渲染失败:', error);
            container.innerHTML = `<div class="alert alert-danger">IP白名单管理器加载失败: ${error.message}</div>`;
        }
    }

    /**
     * 生成IP白名单相关的HTML内容
     */
    generateIpWhitelistContent() {
        return `
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-shield-check me-2"></i>IP白名单管理
                    </h6>
                    <button class="btn btn-sm btn-primary" onclick="apiModule.addIpWhitelist()">
                        <i class="bi bi-plus-circle me-1"></i>添加IP
                    </button>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <input type="text" class="form-control" id="newIpAddress" 
                                   placeholder="请输入IP地址，如：*********** 或 ***********/24">
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-primary" onclick="apiModule.addIpWhitelist()">
                                <i class="bi bi-plus-circle me-2"></i>添加到白名单
                            </button>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>说明：</strong>只有白名单中的IP地址才能调用API接口。支持单个IP和CIDR格式的网段。
                    </div>
                    
                    <div id="ipWhitelistContainer">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在加载IP白名单...</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载IP白名单数据
     */
    async loadIpWhitelist() {
        try {
            console.log('🔄 开始加载IP白名单...');
            
            const response = await fetch('/api/merchant/index.php?module=tools&action=get_ip_whitelist', {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                // 确保data是数组格式
                const ipList = Array.isArray(data.data) ? data.data : [];
                console.log('✅ IP白名单加载成功:', ipList);
                this.ipList = ipList;
                this.renderIpWhitelist(ipList);
            } else {
                console.error('❌ IP白名单加载失败:', data.message);
                this.showError('IP白名单加载失败: ' + (data.message || '未知错误'));
                this.renderIpWhitelist([]);
            }
        } catch (error) {
            console.error('❌ 加载IP白名单异常:', error);
            this.showError('加载IP白名单时发生错误: ' + error.message);
            this.renderIpWhitelist([]);
        }
    }

    /**
     * 渲染IP白名单列表
     */
    renderIpWhitelist(ipList) {
        const container = document.getElementById('ipWhitelistContainer');
        if (!container) return;

        if (!ipList || ipList.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-shield-x text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">暂无IP白名单</h5>
                    <p class="text-muted">添加IP地址到白名单以限制API访问</p>
                </div>
            `;
            return;
        }

        const listHtml = ipList.map((ip, index) => `
            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                <div>
                    <span class="fw-bold">${ip}</span>
                    <small class="text-muted ms-2">
                        ${this.isValidIP(ip) ? '单个IP' : '网段'}
                    </small>
                </div>
                <button class="btn btn-sm btn-outline-danger" 
                        onclick="apiModule.removeIpWhitelist(${index})"
                        title="删除此IP">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `).join('');

        container.innerHTML = `
            <div class="ip-whitelist-list">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">已添加的IP地址 (${ipList.length}/50)</h6>
                    <small class="text-muted">最多支持50个IP地址</small>
                </div>
                ${listHtml}
            </div>
        `;
    }

    /**
     * 添加IP到白名单
     */
    async addIpWhitelist() {
        const ipInput = document.getElementById('newIpAddress');
        if (!ipInput) return;

        const ipAddress = ipInput.value.trim();
        if (!ipAddress) {
            this.showError('请输入IP地址');
            return;
        }

        // 验证IP格式
        if (!this.isValidIPOrCIDR(ipAddress)) {
            this.showError('IP地址格式无效，请输入正确的IP地址或CIDR格式');
            return;
        }

        // 检查是否已存在
        if (this.ipList.includes(ipAddress)) {
            this.showError('该IP地址已在白名单中');
            return;
        }

        // 检查数量限制
        if (this.ipList.length >= 50) {
            this.showError('IP白名单最多支持50个地址');
            return;
        }

        try {
            console.log('🔄 添加IP到白名单:', ipAddress);
            
            const response = await fetch('/api/merchant/index.php?module=tools&action=add_ip_whitelist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify({ ip_address: ipAddress })
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                console.log('✅ IP添加成功');
                this.showSuccess('IP地址已添加到白名单');
                ipInput.value = '';
                
                // 重新加载列表
                await this.loadIpWhitelist();
            } else {
                console.error('❌ IP添加失败:', data.message);
                this.showError('添加失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 添加IP异常:', error);
            this.showError('添加IP时发生错误: ' + error.message);
        }
    }

    /**
     * 从白名单中删除IP
     */
    async removeIpWhitelist(index) {
        if (index < 0 || index >= this.ipList.length) {
            this.showError('无效的IP索引');
            return;
        }

        const ipAddress = this.ipList[index];
        if (!confirm(`确定要从白名单中删除 ${ipAddress} 吗？`)) {
            return;
        }

        try {
            console.log('🔄 从白名单删除IP:', ipAddress);
            
            const response = await fetch('/api/merchant/index.php?module=tools&action=remove_ip_whitelist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify({ index: index })
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                console.log('✅ IP删除成功');
                this.showSuccess('IP地址已从白名单中删除');
                
                // 重新加载列表
                await this.loadIpWhitelist();
            } else {
                console.error('❌ IP删除失败:', data.message);
                this.showError('删除失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 删除IP异常:', error);
            this.showError('删除IP时发生错误: ' + error.message);
        }
    }

    /**
     * 验证IP地址格式
     */
    isValidIP(ip) {
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
    }

    /**
     * 验证IP地址或CIDR格式
     */
    isValidIPOrCIDR(ip) {
        // 检查是否为单个IP
        if (this.isValidIP(ip)) {
            return true;
        }

        // 检查是否为CIDR格式
        const cidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/;
        return cidrRegex.test(ip);
    }

    /**
     * 获取当前客户端IP（用于快速添加）
     */
    async getCurrentClientIP() {
        try {
            // 可以调用一个获取客户端IP的API
            const response = await fetch('/api/get-client-ip');
            const data = await response.json();
            return data.ip || '';
        } catch (error) {
            console.error('获取客户端IP失败:', error);
            return '';
        }
    }

    /**
     * 快速添加当前IP
     */
    async addCurrentIP() {
        try {
            const currentIP = await this.getCurrentClientIP();
            if (currentIP) {
                const ipInput = document.getElementById('newIpAddress');
                if (ipInput) {
                    ipInput.value = currentIP;
                    this.showSuccess(`已填入当前IP: ${currentIP}`);
                }
            } else {
                this.showError('无法获取当前IP地址');
            }
        } catch (error) {
            console.error('添加当前IP失败:', error);
            this.showError('获取当前IP失败: ' + error.message);
        }
    }

    /**
     * 批量导入IP
     */
    async importIPs(ipList) {
        if (!Array.isArray(ipList) || ipList.length === 0) {
            this.showError('IP列表为空');
            return;
        }

        const validIPs = [];
        const invalidIPs = [];

        // 验证所有IP
        ipList.forEach(ip => {
            const trimmedIP = ip.trim();
            if (this.isValidIPOrCIDR(trimmedIP)) {
                validIPs.push(trimmedIP);
            } else {
                invalidIPs.push(trimmedIP);
            }
        });

        if (invalidIPs.length > 0) {
            this.showError(`以下IP格式无效: ${invalidIPs.join(', ')}`);
            return;
        }

        if (this.ipList.length + validIPs.length > 50) {
            this.showError(`导入后将超过50个IP限制（当前${this.ipList.length}个，导入${validIPs.length}个）`);
            return;
        }

        try {
            console.log('🔄 批量导入IP:', validIPs);
            
            const response = await fetch('/api/merchant/index.php?module=tools&action=batch_add_ip_whitelist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify({ ip_addresses: validIPs })
            });

            const data = await response.json();
            
            if (data.code === 200 || data.success) {
                console.log('✅ IP批量导入成功');
                this.showSuccess(`成功导入${validIPs.length}个IP地址`);
                
                // 重新加载列表
                await this.loadIpWhitelist();
            } else {
                console.error('❌ IP批量导入失败:', data.message);
                this.showError('批量导入失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 批量导入IP异常:', error);
            this.showError('批量导入IP时发生错误: ' + error.message);
        }
    }

    /**
     * 导出IP白名单
     */
    exportIPs() {
        if (this.ipList.length === 0) {
            this.showError('IP白名单为空，无法导出');
            return;
        }

        const content = this.ipList.join('\n');
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ip-whitelist-${new Date().getTime()}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess('IP白名单已导出');
    }

    /**
     * 清空所有IP白名单
     */
    async clearAllIPs() {
        if (this.ipList.length === 0) {
            this.showError('IP白名单已为空');
            return;
        }

        if (!confirm(`确定要清空所有${this.ipList.length}个IP地址吗？此操作不可恢复！`)) {
            return;
        }

        try {
            console.log('🔄 清空所有IP白名单');
            
            const response = await fetch('/api/merchant/index.php?module=tools&action=clear_ip_whitelist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            
            if (data.code === 200 || data.success) {
                console.log('✅ IP白名单清空成功');
                this.showSuccess('IP白名单已清空');
                
                // 重新加载列表
                await this.loadIpWhitelist();
            } else {
                console.error('❌ IP白名单清空失败:', data.message);
                this.showError('清空失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 清空IP白名单异常:', error);
            this.showError('清空IP白名单时发生错误: ' + error.message);
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        if (window.apiModule && window.apiModule.showSuccess && window.apiModule.showSuccess !== this.showSuccess) {
            window.apiModule.showSuccess(message);
        } else {
            console.log('✅ 成功:', message);
            // 简单的成功提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-check-circle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        if (window.apiModule && window.apiModule.showError && window.apiModule.showError !== this.showError) {
            window.apiModule.showError(message);
        } else {
            console.error('❌ 错误:', message);
            // 简单的错误提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }
    }
}

// 导出类
window.IpWhitelistManager = IpWhitelistManager; 