<?php
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

$auth = TenantAuth::init();
if (!$auth->checkAuth()) {
    $auth->sendUnauthorized('请先登录');
    exit;
}

$db = $auth->getDatabase();
$currentUser = $auth->getCurrentUser();

// 验证商户权限
if ($currentUser['user_type'] !== 'merchant') {
    $auth->sendForbidden('无权限访问');
    exit;
}

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'get_docs':
            handleGetApiDocs();
            break;
        case 'get_merchant_info':
            handleGetMerchantInfo();
            break;
        default:
            $auth->sendError('接口不存在', 404);
    }
} catch (Exception $e) {
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 获取API文档
function handleGetApiDocs() {
    global $auth;
    
    $docs = array(
        'info' => array(
            'title' => 'PayPal支付系统API接口文档',
            'version' => '2.0.0',
            'description' => '商户接入支付系统的完整API文档',
            'base_url' => 'https://qrcode.top670.com',
            'notice' => '调用接口前请提供IP，只有添加到白名单的IP才可成功调用。'
        ),
        'authentication' => array(
            'type' => 'SHA-256签名',
            'description' => '为确保接口调用安全可靠，平台对所有开发者请求增加签名校验',
            'signature_steps' => array(
                array(
                    'step' => 1,
                    'title' => '准备业务参数',
                    'description' => '把要POST提交的字段放入数组'
                ),
                array(
                    'step' => 2,
                    'title' => '按字段名升序排序',
                    'description' => 'ksort($post, SORT_STRING);'
                ),
                array(
                    'step' => 3,
                    'title' => '拼接为 key=value 串',
                    'description' => '用 & 连接得到：a=1&b=3&c=55'
                ),
                array(
                    'step' => 4,
                    'title' => '追加三段固定信息',
                    'description' => '拼接字符串 &developer_id&developer_key&timestamp'
                ),
                array(
                    'step' => 5,
                    'title' => '计算 SHA-256 并转大写',
                    'description' => '$sign = strtoupper(hash("sha256", $post_str));'
                ),
                array(
                    'step' => 6,
                    'title' => '在POST请求中携带字段',
                    'description' => 'developer_id、timestamp、sign与业务数据一起提交'
                )
            )
        ),
        'apis' => array(
            'pay_get_qrcode' => array(
                'name' => '获取收款页接口',
                'description' => '接口接收请求后，会返回有可用收款信息的收款页面url，供使用。',
                'url' => 'https://qrcode.top670.com/api.php?do=pay_get_qrcode',
                'method' => 'POST',
                'content_type' => 'application/x-www-form-urlencoded',
                'parameters' => array(
                    'developer_id' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者id，在开发者后台设置中查看'
                    ),
                    'timestamp' => array(
                        'type' => 'integer',
                        'required' => true,
                        'description' => '开发者签名时间戳(五分钟内签名时间有效)'
                    ),
                    'sign' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者sign签名（签名生成方式见签名说明）'
                    ),
                    'amount' => array(
                        'type' => 'decimal',
                        'required' => true,
                        'description' => '交易金额，单位是元'
                    ),
                    'type' => array(
                        'type' => 'integer',
                        'required' => true,
                        'description' => '传入固定数值 2'
                    ),
                    'product_id' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '传入产品id（由开发者在后台添加）'
                    ),
                    'order_no' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '传入开发者的订单号,数字,字母均可以最长50,要保持唯一'
                    ),
                    'notification_url' => array(
                        'type' => 'string',
                        'required' => false,
                        'description' => '订单状态回调地址。传入此值时若产品已有设定回调地址，则优先传入该地址。'
                    )
                ),
                'response' => array(
                    'error_code' => array(
                        'type' => 'integer',
                        'description' => '错误代码，0表示成功，非0表示失败'
                    ),
                    'error_message' => array(
                        'type' => 'string',
                        'description' => '错误信息，成功时为空'
                    ),
                    'url' => array(
                        'type' => 'string',
                        'description' => '收款页面完整url'
                    )
                ),
                'example' => array(
                    'request' => 'developer_id=11&sign=CDFAFE235B6B37AF641FB94F1D4AD31BC6035A294A3B509790EEF0750FC157FB&amount=200&product_id=12&type=2&timestamp=1746809221&order_no=201120339120099200929844892001&notification_url=http://www.baidu.com/',
                    'success_response' => array(
                        'error_code' => 0,
                        'error_message' => '',
                        'url' => 'https://pay.top670.com/?key=12_1746676802_573882'
                    ),
                    'error_response' => array(
                        'error_code' => 22,
                        'error_message' => '无可用账户'
                    )
                ),
                'error_codes' => array(
                    0 => '成功',
                    2 => '收款金额输入错误',
                    6 => '请输入有效的product_id',
                    7 => '请输入有效的order_no',
                    8 => '该order_no已经被使用,请换一个',
                    22 => '该金额不存在可用的账户',
                    23 => '不安全发起,无法使用',
                    212 => 'developer_id错误',
                    213 => 'sign错误',
                    214 => 'ip不在白名单中,请去开发者后台配置白名单',
                    215 => 'sign超时,请重新生成'
                )
            ),
            'pay_get_orders_inquiry' => array(
                'name' => '查询订单信息接口',
                'description' => '可传入订单号id，主动查询订单状态和信息。',
                'url' => 'https://qrcode.top670.com/api.php?do=pay_get_orders_inquiry',
                'method' => 'POST',
                'content_type' => 'application/x-www-form-urlencoded',
                'parameters' => array(
                    'developer_id' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者id，在开发者后台设置中查看'
                    ),
                    'timestamp' => array(
                        'type' => 'integer',
                        'required' => true,
                        'description' => '开发者签名时间戳(五分钟内签名时间有效)'
                    ),
                    'sign' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者sign签名（签名生成方式见签名说明）'
                    ),
                    'order_no' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '传入开发者的订单号,数字,字母均可以最长50,要保持唯一'
                    )
                ),
                'response' => array(
                    'error_code' => array(
                        'type' => 'integer',
                        'description' => '错误代码，0表示成功，非0表示失败'
                    ),
                    'error_message' => array(
                        'type' => 'string',
                        'description' => '错误信息，成功时为空'
                    ),
                    'order_data' => array(
                        'type' => 'object',
                        'description' => '订单详细信息',
                        'properties' => array(
                            'order_no' => '订单号',
                            'amount' => '订单金额',
                            'status' => '订单状态',
                            'created_time' => '创建时间',
                            'paid_time' => '支付时间'
                        )
                    )
                ),
                'error_codes' => array(
                    0 => '成功',
                    7 => '请输入有效的order_no',
                    9 => '订单不存在',
                    212 => 'developer_id错误',
                    213 => 'sign错误',
                    214 => 'ip不在白名单中',
                    215 => 'sign超时'
                )
            ),
            'pay_get_payment_quota' => array(
                'name' => '获取支付额度接口',
                'description' => '查询当前可用的支付额度信息。',
                'url' => 'https://qrcode.top670.com/api.php?do=pay_get_payment_quota',
                'method' => 'POST',
                'content_type' => 'application/x-www-form-urlencoded',
                'parameters' => array(
                    'developer_id' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者id'
                    ),
                    'timestamp' => array(
                        'type' => 'integer',
                        'required' => true,
                        'description' => '签名时间戳'
                    ),
                    'sign' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '签名'
                    )
                ),
                'response' => array(
                    'error_code' => array(
                        'type' => 'integer',
                        'description' => '错误代码'
                    ),
                    'quota_data' => array(
                        'type' => 'object',
                        'description' => '额度信息',
                        'properties' => array(
                            'total_quota' => '总额度',
                            'used_quota' => '已用额度',
                            'available_quota' => '可用额度'
                        )
                    )
                )
            )
        )
    );
    
    $auth->logUserAction('view_api_docs', 'merchant_tool', 0, '查看API文档');
    $auth->sendSuccess($docs, 'API文档获取成功');
}

// 获取商户信息
function handleGetMerchantInfo() {
    global $auth, $db, $currentUser;
    
    try {
        // 获取商户基本信息
        $merchant = $db->fetch("
            SELECT m.*, u.username, u.real_name 
            FROM merchants m 
            LEFT JOIN users u ON m.user_id = u.id 
            WHERE m.id = ?
        ", array($currentUser['profile_id']));
        
        if (!$merchant) {
            $auth->sendError('商户信息不存在', 404);
            return;
        }
        
        // 获取商户产品
        $products = getMerchantProducts($currentUser['profile_id']);
        
        $merchantInfo = array(
            'id' => $merchant['id'],
            'company_name' => $merchant['company_name'],
            'username' => $merchant['username'],
            'real_name' => $merchant['real_name'],
            'status' => $merchant['status'],
            'api_key' => $merchant['api_key'],
            'created_at' => $merchant['created_at'],
            'products' => $products
        );
        
        $auth->sendSuccess($merchantInfo, '商户信息获取成功');
        
    } catch (Exception $e) {
        $auth->sendError('获取商户信息失败: ' . $e->getMessage(), 500);
    }
}

// 获取商户产品列表
function getMerchantProducts($merchantId) {
    global $db;
    
    try {
        $products = $db->fetchAll("
            SELECT id, name, rate, min_amount, max_amount, status 
            FROM products 
            WHERE merchant_id = ? 
            ORDER BY created_at DESC
        ", array($merchantId));
        
        return $products ?: array();
        
    } catch (Exception $e) {
        return array();
    }
}
?> 