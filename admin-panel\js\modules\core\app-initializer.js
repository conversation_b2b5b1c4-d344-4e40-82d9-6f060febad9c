/**
 * 应用初始化器 - 四层架构版本
 * 负责整个PayPal管理系统的启动和模块化架构的初始化
 * 支持系统方→平台方→码商→商户四层架构
 * 
 * <AUTHOR> Team
 * @version 2.0.0 - 四层架构改造版本
 * @since 2024-12-17
 */

class AppInitializer {
    constructor() {
        this.moduleLoader = null;
        this.authManager = null;
        this.uiManager = null;
        this.tenantInfo = null;  // 新增：租户信息
        this.isInitialized = false;
        this.isLoading = true;   // 新增：加载状态
        this.initializationPromise = null;
        
        // 绑定DOM加载事件
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initialize());
        } else {
            this.initialize();
        }
    }

    /**
     * 初始化应用
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.initializationPromise) {
            return this.initializationPromise;
        }

        this.initializationPromise = this._performInitialization();
        return this.initializationPromise;
    }

    /**
     * 执行实际的初始化过程
     * @private
     * @returns {Promise<void>}
     */
    async _performInitialization() {
        try {
            console.log('🚀 PayPal管理系统启动中...');
            
            // 第一步：初始化租户信息（四层架构核心）
            await this._initializeTenantInfo();
            
            // 第二步：初始化模块加载器
            await this._initializeModuleLoader();
            
            // 第三步：加载核心模块
            await this._loadCoreModules();
            
            // 第四步：初始化认证系统
            await this._initializeAuth();
            
            // 第五步：检查登录状态并启动相应流程
            await this._checkAuthAndStart();
            
            this.isInitialized = true;
            this.isLoading = false;
            console.log('✅ PayPal管理系统启动完成');
            
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this.isLoading = false;
            this._showInitializationError(error);
            throw error;
        }
    }

    /**
     * 初始化租户信息（四层架构核心功能）
     * @private
     */
    async _initializeTenantInfo() {
        try {
            const hostname = window.location.hostname;
            console.log('🌐 正在解析域名:', hostname);
            
            const response = await fetch('/api/admin.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    action: 'resolve_domain',
                    domain: hostname
                })
            });
            
            const result = await response.json();
            
            if (result.error_code === 0) {
                this.tenantInfo = result.data;
                console.log('✅ 租户信息获取成功:', this.tenantInfo);
                
                // 设置全局租户信息
                window.TENANT_INFO = this.tenantInfo;
                
                // 动态设置页面标题
                this._updatePageTitle();
                
                // 🔧 立即生成租户专用登录界面
                this._generateTenantLoginInterface();
                
            } else {
                console.error('❌ 域名解析失败:', result);
                this._showDomainAlert(result.message || '域名解析失败');
                return;
            }
        } catch (error) {
            console.error('❌ 域名解析异常:', error);
            this._showDomainAlert(error.message || '域名解析失败，请联系管理员');
            return;
        }
    }

    /**
     * 更新页面标题根据租户类型
     * @private
     */
    _updatePageTitle() {
        if (!this.tenantInfo) return;
        
        const titleMap = {
            'system_admin': '系统管理后台',
            'platform_admin': `${this.tenantInfo.platform_name || '平台'} - 管理后台`,
            'provider': `${this.tenantInfo.brand_name || '码商'} - 管理系统`,
            'merchant': `${this.tenantInfo.brand_name || '商户'} - 管理系统`
        };
        
        const title = titleMap[this.tenantInfo.tenant_type] || 'PayPal管理系统';
        document.title = title;
        
        // 更新登录页面的标题
        const loginTitle = document.querySelector('.login-title');
        if (loginTitle) {
            loginTitle.textContent = title;
        }
    }

    /**
     * 动态生成租户专用登录界面
     * @private
     */
    _generateTenantLoginInterface() {
        if (!this.tenantInfo) return;
        
        console.log('🎨 生成租户专用登录界面:', this.tenantInfo.tenant_type);
        
        // 根据租户类型设置主题配置
        const themeConfig = {
            'system_admin': {
                primaryColor: '#1890ff',
                brandName: '系统管理后台',
                testAccount: '管理员：admin / password',
                accentColor: '#40a9ff'
            },
            'platform_admin': {
                primaryColor: '#722ed1', 
                brandName: this.tenantInfo.platform_name || '平台管理后台',
                testAccount: '管理员：admin / password',
                accentColor: '#9254de'
            },
            'provider': {
                primaryColor: '#52c41a',
                brandName: this.tenantInfo.brand_name || '码商管理系统', 
                testAccount: '码商：provider1 / password',
                accentColor: '#73d13d'
            },
            'merchant': {
                primaryColor: '#fa8c16',
                brandName: this.tenantInfo.brand_name || '商户管理系统',
                testAccount: '商户：merchant1 / password',
                accentColor: '#ffa940'
            }
        };
        
        const theme = themeConfig[this.tenantInfo.tenant_type];
        if (!theme) return;
        
        // 隐藏系统加载器
        const systemLoader = document.getElementById('systemLoader');
        if (systemLoader) {
            systemLoader.style.display = 'none';
        }
        
        // 生成登录界面HTML
        const loginContainer = document.getElementById('loginContainer');
        if (loginContainer) {
            // 🔧 确保登录容器的样式正确
            loginContainer.style.display = 'flex';
            loginContainer.style.alignItems = 'center';
            loginContainer.style.justifyContent = 'center';
            loginContainer.style.position = 'fixed';
            loginContainer.style.top = '0';
            loginContainer.style.left = '0';
            loginContainer.style.width = '100%';
            loginContainer.style.height = '100vh';
            loginContainer.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            loginContainer.style.zIndex = '9999';
            
            loginContainer.innerHTML = `
                <div class="login-card">
                    <div class="login-logo">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <h2 class="login-title">${theme.brandName}</h2>
                    
                    <div id="alertContainer"></div>
                    
                    <form id="loginForm">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="username" placeholder="用户名" required>
                            <label for="username"><i class="bi bi-person me-2"></i>用户名</label>
                        </div>
                        <div class="form-floating">
                            <input type="password" class="form-control" id="password" placeholder="密码" required>
                            <label for="password"><i class="bi bi-lock me-2"></i>密码</label>
                        </div>
                        <button type="submit" class="btn btn-primary btn-login">
                            <i class="bi bi-box-arrow-in-right me-2"></i>登录
                        </button>
                    </form>
                    
                    <div class="loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-2">正在登录...</span>
                    </div>
                    
                    <div class="system-info">
                        <div class="mb-2">
                            <strong>测试账户：</strong>
                        </div>
                        <div class="mb-1">${theme.testAccount}</div>
                    </div>
                </div>
            `;
            
            // 应用CSS变量和租户类型类
            document.documentElement.style.setProperty('--primary-color', theme.primaryColor);
            document.documentElement.style.setProperty('--accent-color', theme.accentColor);
            document.body.classList.add(`tenant-${this.tenantInfo.tenant_type}`);
            
            console.log('✅ 租户登录界面生成完成，强制居中样式已应用');
        }
    }
    


    /**
     * 弹窗显示域名错误信息
     * @param {string} message 错误信息
     * @private
     */
    _showDomainAlert(message) {
        console.error('💀 域名错误:', message);
        
        // 隐藏系统加载器
        const systemLoader = document.getElementById('systemLoader');
        if (systemLoader) {
            systemLoader.style.display = 'none';
        }
        
        // 显示错误弹窗
        const errorMessage = `域名配置错误：\n\n${message}\n\n当前域名：${window.location.hostname}\n\n请联系管理员或检查访问域名是否正确。`;
        alert(errorMessage);
        
        // 弹窗确认后重新加载页面
        setTimeout(() => {
            location.reload();
        }, 100);
    }

    /**
     * 初始化模块加载器
     * @private
     */
    async _initializeModuleLoader() {
        console.log('📦 初始化模块加载器...');
        
        // 检查ModuleLoader类是否可用
        if (typeof ModuleLoader === 'undefined') {
            throw new Error('ModuleLoader类未找到，请确保module-loader.js已正确加载');
        }
        
        // 优先使用现有的全局实例
        if (window.AdminModuleLoader) {
            console.log('✅ 使用现有的AdminModuleLoader实例');
            this.moduleLoader = window.AdminModuleLoader;
        } else {
            console.log('⚠️ AdminModuleLoader实例未找到，创建新实例');
            this.moduleLoader = new ModuleLoader();
            // 设置为全局实例
            window.AdminModuleLoader = this.moduleLoader;
        }
        
        // 设置便捷的全局引用
        window.moduleLoader = this.moduleLoader;
        
        console.log('✅ 模块加载器初始化完成');
    }

    /**
     * 检查核心模块
     * @private
     */
    async _loadCoreModules() {
        console.log('🔧 检查核心模块...');
        
        try {
            // 检查核心模块是否已经静态加载
            const coreModules = [
                { name: 'utils', check: () => typeof window.AdminUtils !== 'undefined' },
                { name: 'auth', check: () => typeof window.AuthManager !== 'undefined' },
                { name: 'ui-manager', check: () => typeof window.UIManager !== 'undefined' }
            ];
            
            for (const module of coreModules) {
                if (module.check()) {
                    console.log(`✅ ${module.name} 模块已静态加载，跳过重复加载`);
                } else {
                    console.log(`📦 加载模块: ${module.name}`);
                    // 如果模块未静态加载，则通过模块加载器加载
                    await this.moduleLoader.loadModule(module.name);
                }
            }
            
            console.log('✅ 核心模块检查完成');
            
        } catch (error) {
            console.error('❌ 核心模块加载失败:', error);
            throw new Error(`核心模块加载失败: ${error.message}`);
        }
    }

    /**
     * 初始化认证系统
     * @private
     */
    async _initializeAuth() {
        console.log('🔐 初始化认证系统...');
        
        // 获取认证管理器类
        const AuthManager = window.AuthManager;
        if (!AuthManager) {
            throw new Error('AuthManager类未找到');
        }
        
        // 创建认证管理器实例，传入租户信息
        this.authManager = new AuthManager(this.tenantInfo);
        
        // 设置全局引用
        window.authManager = this.authManager;
        
        console.log('✅ 认证系统初始化完成');
    }

    /**
     * 检查认证状态并启动相应流程
     */
    async _checkAuthAndStart() {
        console.log('🔍 检查认证状态...');
        
        if (this.authManager.isAuthenticated()) {
            console.log('👤 用户已登录，启动主应用界面');
            await this._startMainApplication();
        } else {
            console.log('🔑 用户未登录，启动登录流程');
            await this._startLoginFlow();
        }
    }

    /**
     * 启动主应用界面
     * @private
     */
    async _startMainApplication() {
        console.log('🏠 启动主应用界面...');
        
        try {
            // 获取UI管理器类
            const UIManager = window.UIManager;
            if (!UIManager) {
                throw new Error('UIManager类未找到');
            }
            
            // 创建UI管理器实例，传入认证管理器和租户信息
            this.uiManager = new UIManager(this.authManager, this.tenantInfo);
            
            // 设置全局引用
            window.uiManager = this.uiManager;
            
            // 隐藏登录界面，显示主应用界面
            this._switchToMainInterface();
            
            console.log('✅ 主应用界面启动完成');
            
        } catch (error) {
            console.error('❌ 主应用界面启动失败:', error);
            throw error;
        }
    }

    /**
     * 启动登录流程
     * @private
     */
    async _startLoginFlow() {
        console.log('🔑 启动登录流程...');
        
        try {
            // 获取登录管理器类
            const LoginManager = window.LoginManager;
            if (!LoginManager) {
                throw new Error('LoginManager类未找到');
            }
            
            // 🔧 关键修复：先生成登录界面HTML，再显示界面
            this._generateTenantLoginInterface();
            this._switchToLoginInterface();
            
            // 等待DOM更新后再创建登录管理器实例
            setTimeout(() => {
                // 创建登录管理器实例
                this.loginManager = new LoginManager();
                
                // 设置全局引用
                window.loginManager = this.loginManager;
                
                console.log('✅ 登录管理器初始化完成，表单事件已绑定');
            }, 100);
            
            console.log('✅ 登录流程启动完成');
            
        } catch (error) {
            console.error('❌ 登录流程启动失败:', error);
            throw error;
        }
    }

    /**
     * 切换到主界面
     * @private
     */
    _switchToMainInterface() {
        const loginContainer = document.getElementById('loginContainer');
        const appContainer = document.getElementById('appContainer');

        if (loginContainer) {
            loginContainer.style.display = 'none';
            loginContainer.style.visibility = 'hidden';
        }

        if (appContainer) {
            // 保持app-container类，确保居中布局生效
            appContainer.classList.add('app-container');
            appContainer.style.display = 'block';
            appContainer.style.visibility = 'visible';
            
            // 🔍 添加调试模式
            document.body.classList.add('debug-layout');
            console.log('🔍 已启用布局调试模式，将显示红色/蓝色/绿色边框');
            
            // 触发主界面创建事件
            const user = this.authManager.getUser();
            const event = new CustomEvent('mainInterfaceCreate', {
                detail: { user, container: appContainer }
            });
            document.dispatchEvent(event);
            
            // 5秒后关闭调试模式
            setTimeout(() => {
                document.body.classList.remove('debug-layout');
                console.log('🔍 布局调试模式已关闭');
            }, 5000);
        }
    }

    /**
     * 切换到登录界面
     * @private
     */
    _switchToLoginInterface() {
        const loginContainer = document.getElementById('loginContainer');
        const appContainer = document.getElementById('appContainer');

        if (appContainer) {
            appContainer.style.display = 'none';
            appContainer.style.visibility = 'hidden';
        }

        if (loginContainer) {
            loginContainer.style.display = 'block';
            loginContainer.style.visibility = 'visible';
        }
    }

    /**
     * 显示初始化错误
     * @private
     * @param {Error} error 错误对象
     */
    _showInitializationError(error) {
        const errorHtml = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <div style="
                    background: white;
                    padding: 40px;
                    border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    text-align: center;
                    max-width: 500px;
                    margin: 20px;
                ">
                    <div style="
                        color: #ef4444;
                        font-size: 48px;
                        margin-bottom: 20px;
                    ">⚠️</div>
                    <h2 style="
                        color: #1f2937;
                        margin-bottom: 15px;
                        font-size: 24px;
                    ">系统初始化失败</h2>
                    <p style="
                        color: #6b7280;
                        margin-bottom: 20px;
                        line-height: 1.6;
                    ">系统启动过程中遇到错误，请检查网络连接或联系技术支持。</p>
                    <details style="
                        text-align: left;
                        margin-bottom: 20px;
                        padding: 10px;
                        background: #f9fafb;
                        border-radius: 8px;
                        font-size: 14px;
                    ">
                        <summary style="cursor: pointer; font-weight: 600;">错误详情</summary>
                        <pre style="
                            margin-top: 10px;
                            color: #ef4444;
                            font-size: 12px;
                            white-space: pre-wrap;
                        ">${error.message}\n${error.stack || ''}</pre>
                    </details>
                    <button onclick="location.reload()" style="
                        background: #3b82f6;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 16px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: background-color 0.2s;
                    " onmouseover="this.style.backgroundColor='#2563eb'" 
                       onmouseout="this.style.backgroundColor='#3b82f6'">
                        重新加载
                    </button>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', errorHtml);
    }

    /**
     * 注册页面处理器
     * @param {string} page 页面ID
     * @param {Function} handler 处理函数
     */
    registerPageHandler(page, handler) {
        if (this.uiManager) {
            this.uiManager.registerPageHandler(page, handler);
        } else {
            // 如果UI管理器还未初始化，延迟注册
            setTimeout(() => {
                if (this.uiManager) {
                    this.uiManager.registerPageHandler(page, handler);
                }
            }, 1000);
        }
    }

    /**
     * 加载业务模块
     * @param {string|Array} modules 模块名称或模块数组
     * @returns {Promise<void>}
     */
    async loadBusinessModules(modules) {
        if (!this.moduleLoader) {
            throw new Error('模块加载器未初始化');
        }

        const moduleList = Array.isArray(modules) ? modules : [modules];
        
        console.log('📦 加载业务模块:', moduleList);
        
        try {
            for (const moduleName of moduleList) {
                await this.moduleLoader.loadModule(moduleName);
            }
            console.log('✅ 业务模块加载完成');
        } catch (error) {
            console.error('❌ 业务模块加载失败:', error);
            throw error;
        }
    }

    /**
     * 获取应用状态
     * @returns {Object} 应用状态信息
     */
    getAppStatus() {
        return {
            initialized: this.isInitialized,
            authenticated: this.authManager ? this.authManager.isAuthenticated() : false,
            currentUser: this.authManager ? this.authManager.getUser() : null,
            loadedModules: this.moduleLoader ? this.moduleLoader.getLoadedModules() : [],
            currentPage: this.uiManager ? this.uiManager.getCurrentPage() : null
        };
    }

    /**
     * 重启应用
     * @returns {Promise<void>}
     */
    async restart() {
        console.log('🔄 重启应用...');
        
        // 清理现有状态
        this.isInitialized = false;
        this.initializationPromise = null;
        
        // 清理模块缓存
        if (this.moduleLoader) {
            this.moduleLoader.clearCache(true);
        }
        
        // 重新初始化
        await this.initialize();
    }
}

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
});

// 创建应用初始化器实例
const appInitializer = new AppInitializer();

// 设置全局引用
window.appInitializer = appInitializer;

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AppInitializer };
} else {
    // 浏览器环境 - 导出类到全局作用域
    window.AppInitializer = AppInitializer;
    
    // 为模块加载器导出整个模块
    window.AppInitializerModule = {
        AppInitializer,
        version: '2.0.0',
        initialized: true
    };
    
    console.log('✅ 应用初始化器模块已导出到全局作用域');
}

console.log('📋 应用初始化器已加载'); 