<?php
/**
 * 统一错误处理类
 * 提供标准化的错误处理和响应格式
 */
class ErrorHandler {
    
    // 错误代码常量
    const SUCCESS = 200;
    const BAD_REQUEST = 400;
    const UNAUTHORIZED = 401;
    const FORBIDDEN = 403;
    const NOT_FOUND = 404;
    const METHOD_NOT_ALLOWED = 405;
    const INTERNAL_ERROR = 500;
    const SERVICE_UNAVAILABLE = 503;
    
    // 业务错误代码
    const VALIDATION_ERROR = 1001;
    const DATABASE_ERROR = 1002;
    const PERMISSION_DENIED = 1003;
    const RESOURCE_NOT_FOUND = 1004;
    const DUPLICATE_RESOURCE = 1005;
    const BUSINESS_LOGIC_ERROR = 1006;
    
    private static $errorMessages = array(
        self::SUCCESS => '操作成功',
        self::BAD_REQUEST => '请求参数错误',
        self::UNAUTHORIZED => '未授权访问',
        self::FORBIDDEN => '权限不足',
        self::NOT_FOUND => '资源不存在',
        self::METHOD_NOT_ALLOWED => '请求方法不被允许',
        self::INTERNAL_ERROR => '服务器内部错误',
        self::SERVICE_UNAVAILABLE => '服务暂时不可用',
        
        self::VALIDATION_ERROR => '数据验证失败',
        self::DATABASE_ERROR => '数据库操作失败',
        self::PERMISSION_DENIED => '权限验证失败',
        self::RESOURCE_NOT_FOUND => '请求的资源不存在',
        self::DUPLICATE_RESOURCE => '资源已存在',
        self::BUSINESS_LOGIC_ERROR => '业务逻辑错误'
    );
    
    /**
     * 统一响应格式
     */
    public static function response($code = self::SUCCESS, $message = '', $data = null, $httpCode = null) {
        // 如果没有指定HTTP状态码，根据业务代码自动设置
        if ($httpCode === null) {
            $httpCode = self::getHttpCodeFromBusinessCode($code);
        }
        
        // 如果没有指定消息，使用默认消息
        if (empty($message)) {
            $message = isset(self::$errorMessages[$code]) ? self::$errorMessages[$code] : '未知错误';
        }
        
        $response = array(
            'code' => $code,
            'message' => $message,
            'timestamp' => time(),
            'request_id' => self::generateRequestId()
        );
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        // 设置HTTP状态码
        http_response_code($httpCode);
        
        // 输出JSON响应
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 成功响应
     */
    public static function success($data = null, $message = '操作成功') {
        self::response(self::SUCCESS, $message, $data);
    }
    
    /**
     * 参数错误响应
     */
    public static function badRequest($message = '请求参数错误', $data = null) {
        self::response(self::VALIDATION_ERROR, $message, $data, self::BAD_REQUEST);
    }
    
    /**
     * 未授权响应
     */
    public static function unauthorized($message = '未授权访问') {
        self::response(self::UNAUTHORIZED, $message, null, self::UNAUTHORIZED);
    }
    
    /**
     * 权限不足响应
     */
    public static function forbidden($message = '权限不足') {
        self::response(self::PERMISSION_DENIED, $message, null, self::FORBIDDEN);
    }
    
    /**
     * 资源不存在响应
     */
    public static function notFound($message = '资源不存在') {
        self::response(self::RESOURCE_NOT_FOUND, $message, null, self::NOT_FOUND);
    }
    
    /**
     * 服务器错误响应
     */
    public static function internalError($message = '服务器内部错误', $error = null) {
        // 记录详细错误信息到日志
        if ($error) {
            error_log("Internal Error: " . $error);
        }
        
        self::response(self::INTERNAL_ERROR, $message, null, self::INTERNAL_ERROR);
    }
    
    /**
     * 数据库错误响应
     */
    public static function databaseError($message = '数据库操作失败', $error = null) {
        // 记录数据库错误到日志
        if ($error) {
            error_log("Database Error: " . $error);
        }
        
        self::response(self::DATABASE_ERROR, $message, null, self::INTERNAL_ERROR);
    }
    
    /**
     * 业务逻辑错误响应
     */
    public static function businessError($message, $code = self::BUSINESS_LOGIC_ERROR) {
        self::response($code, $message, null, self::BAD_REQUEST);
    }
    
    /**
     * 处理异常
     */
    public static function handleException($exception) {
        $message = $exception->getMessage();
        $file = $exception->getFile();
        $line = $exception->getLine();
        
        // 记录异常到日志
        error_log("Exception: {$message} in {$file}:{$line}");
        error_log("Stack trace: " . $exception->getTraceAsString());
        
        // 在生产环境中不暴露详细错误信息
        if (defined('ENVIRONMENT') && ENVIRONMENT === 'production') {
            $message = '系统异常，请稍后重试';
        }
        
        self::internalError($message);
    }
    
    /**
     * 验证输入参数
     */
    public static function validateInput($input, $rules) {
        $errors = array();
        
        foreach ($rules as $field => $rule) {
            if (is_string($rule)) {
                // 简单的必填验证
                if ($rule === 'required' && (!isset($input[$field]) || empty($input[$field]))) {
                    $errors[$field] = "字段 {$field} 不能为空";
                }
            } elseif (is_array($rule)) {
                // 复杂验证规则
                $value = isset($input[$field]) ? $input[$field] : null;
                
                if (isset($rule['required']) && $rule['required'] && empty($value)) {
                    $errors[$field] = isset($rule['message']) ? $rule['message'] : "字段 {$field} 不能为空";
                    continue;
                }
                
                if (!empty($value)) {
                    // 类型验证
                    if (isset($rule['type'])) {
                        if (!self::validateType($value, $rule['type'])) {
                            $errors[$field] = "字段 {$field} 类型不正确";
                            continue;
                        }
                    }
                    
                    // 长度验证
                    if (isset($rule['min_length']) && strlen($value) < $rule['min_length']) {
                        $errors[$field] = "字段 {$field} 长度不能少于 {$rule['min_length']} 个字符";
                    }
                    
                    if (isset($rule['max_length']) && strlen($value) > $rule['max_length']) {
                        $errors[$field] = "字段 {$field} 长度不能超过 {$rule['max_length']} 个字符";
                    }
                    
                    // 正则验证
                    if (isset($rule['pattern']) && !preg_match($rule['pattern'], $value)) {
                        $errors[$field] = isset($rule['pattern_message']) ? $rule['pattern_message'] : "字段 {$field} 格式不正确";
                    }
                    
                    // 枚举值验证
                    if (isset($rule['in']) && !in_array($value, $rule['in'])) {
                        $errors[$field] = "字段 {$field} 值不在允许范围内";
                    }
                }
            }
        }
        
        if (!empty($errors)) {
            self::badRequest('输入参数验证失败', $errors);
        }
        
        return true;
    }
    
    /**
     * 验证数据类型
     */
    private static function validateType($value, $type) {
        switch ($type) {
            case 'int':
            case 'integer':
                return is_numeric($value) && is_int($value + 0);
            case 'float':
            case 'double':
                return is_numeric($value);
            case 'string':
                return is_string($value);
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            case 'url':
                return filter_var($value, FILTER_VALIDATE_URL) !== false;
            case 'boolean':
            case 'bool':
                return is_bool($value) || in_array($value, array('0', '1', 'true', 'false'), true);
            default:
                return true;
        }
    }
    
    /**
     * 根据业务代码获取HTTP状态码
     */
    private static function getHttpCodeFromBusinessCode($code) {
        if ($code >= 200 && $code < 300) {
            return 200;
        } elseif ($code >= 400 && $code < 500) {
            return $code;
        } elseif ($code >= 1000 && $code < 2000) {
            return 400; // 业务错误通常返回400
        } else {
            return 500;
        }
    }
    
    /**
     * 生成请求ID
     */
    private static function generateRequestId() {
        return uniqid('req_', true);
    }
    
    /**
     * 设置全局异常处理器
     */
    public static function setGlobalExceptionHandler() {
        set_exception_handler(array('ErrorHandler', 'handleException'));
        
        // 设置错误处理器
        set_error_handler(function($severity, $message, $file, $line) {
            if (!(error_reporting() & $severity)) {
                return;
            }
            throw new ErrorException($message, 0, $severity, $file, $line);
        });
    }
}
?> 