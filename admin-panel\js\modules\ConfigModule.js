/**
 * 配置模块 - 统一配置管理
 * 整合系统设置、平台配置、商户配置等功能
 * 实现界面共用+数据差异化的设计模式
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-01-20
 */

class ConfigModule {
    constructor() {

        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        this.currentRole = null;
        this.configs = {};
        
        console.log('✅ ConfigModule initialized');
    }

    /**
     * 统一渲染方法 - 方案5配置驱动的入口
     */
    async render(container, params = {}) {
        try {
            this.currentRole = params.role || this.getCurrentUserRole();
            console.log(`🎯 配置模块渲染 - 角色: ${this.currentRole}`);
            
            container.innerHTML = this.generateHTML();
            this.initializeEvents();
            await this.loadData();
            
        } catch (error) {
            console.error('❌ 配置模块渲染失败:', error);
            this.showError(container, '配置模块加载失败');
        }
    }

    getCurrentUserRole() {
        if (this.tenantInfo?.tenant_type) {
            return this.tenantInfo.tenant_type;
        }
        const user = this.authManager?.getUser();
        return user?.user_type || 'merchant';
    }

    generateHTML() {
        const roleConfig = this.getRoleConfig(this.currentRole);
        
        return `
            <div class="config-module" data-role="${this.currentRole}">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="${roleConfig.icon} me-2"></i>${roleConfig.title}</h2>
                            <p class="text-muted mb-0">${roleConfig.description}</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="saveConfigBtn">
                                <i class="bi bi-check-circle me-2"></i>保存配置
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="resetConfigBtn">
                                <i class="bi bi-arrow-clockwise me-2"></i>重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 配置选项卡 -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="configTabs">
                            ${this.generateTabHeaders()}
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="configTabContent">
                            ${this.generateTabContent()}
                        </div>
                    </div>
                </div>
            </div>

            <style>${this.generateStyles()}</style>
        `;
    }

    getRoleConfig(role) {
        const configs = {
            'system_admin': {
                title: '系统配置管理',
                description: '全系统配置参数管理',
                icon: 'bi bi-gear-wide-connected',
                color: 'primary'
            },
            'platform_admin': {
                title: '平台配置管理', 
                description: '平台级配置参数管理',
                icon: 'bi bi-sliders',
                color: 'success'
            },
            'provider': {
                title: '码商配置',
                description: '码商业务配置管理',
                icon: 'bi bi-gear',
                color: 'info'
            },
            'merchant': {
                title: '商户配置',
                description: '商户个人配置管理',
                icon: 'bi bi-gear-fill',
                color: 'warning'
            }
        };
        return configs[role] || configs['merchant'];
    }

    generateTabHeaders() {
        const tabs = this.getTabsConfig(this.currentRole);
        return tabs.map((tab, index) => `
            <li class="nav-item">
                <a class="nav-link ${index === 0 ? 'active' : ''}" 
                   id="${tab.id}-tab" 
                   data-bs-toggle="tab" 
                   href="#${tab.id}">
                    <i class="${tab.icon} me-2"></i>${tab.title}
                </a>
            </li>
        `).join('');
    }

    getTabsConfig(role) {
        const configs = {
            'system_admin': [
                { id: 'system', title: '系统配置', icon: 'bi bi-gear-wide', type: 'system' },
                { id: 'database', title: '数据库配置', icon: 'bi bi-database', type: 'database' },
                { id: 'security', title: '安全配置', icon: 'bi bi-shield-check', type: 'security' },
                { id: 'email', title: '邮件配置', icon: 'bi bi-envelope', type: 'email' }
            ],
            'platform_admin': [
                { id: 'platform', title: '平台配置', icon: 'bi bi-sliders', type: 'platform' },
                { id: 'payment', title: '支付配置', icon: 'bi bi-credit-card', type: 'payment' },
                { id: 'fee', title: '费率配置', icon: 'bi bi-percent', type: 'fee' },
                { id: 'limits', title: '限额配置', icon: 'bi bi-speedometer2', type: 'limits' }
            ],
            'provider': [
                { id: 'basic', title: '基础配置', icon: 'bi bi-gear', type: 'basic' },
                { id: 'device', title: '设备配置', icon: 'bi bi-phone', type: 'device' },
                { id: 'notification', title: '通知配置', icon: 'bi bi-bell', type: 'notification' },
                { id: 'settlement', title: '结算配置', icon: 'bi bi-currency-dollar', type: 'settlement' }
            ],
            'merchant': [
                { id: 'profile', title: '个人资料', icon: 'bi bi-person', type: 'profile' },
                { id: 'api', title: 'API配置', icon: 'bi bi-code-slash', type: 'api' },
                { id: 'webhook', title: '回调配置', icon: 'bi bi-arrow-left-right', type: 'webhook' },
                { id: 'security', title: '安全设置', icon: 'bi bi-shield-lock', type: 'security' }
            ]
        };
        return configs[role] || configs['merchant'];
    }

    generateTabContent() {
        const tabs = this.getTabsConfig(this.currentRole);
        return tabs.map((tab, index) => `
            <div class="tab-pane fade ${index === 0 ? 'show active' : ''}" id="${tab.id}">
                ${this.generateTabPaneContent(tab)}
            </div>
        `).join('');
    }

    generateTabPaneContent(tab) {
        switch (tab.type) {
            case 'system':
                return this.generateSystemConfigContent();
            case 'platform':
                return this.generatePlatformConfigContent();
            case 'basic':
                return this.generateBasicConfigContent();
            case 'profile':
                return this.generateProfileConfigContent();
            case 'api':
                return this.generateApiConfigContent();
            case 'webhook':
                return this.generateWebhookConfigContent();
            case 'security':
                return this.generateSecurityConfigContent();
            default:
                return '<div class="text-center py-4">配置内容加载中...</div>';
        }
    }

    generateSystemConfigContent() {
        return `
            <div class="config-form">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">基础配置</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">系统名称</label>
                                    <input type="text" class="form-control" id="systemName" value="PayPal支付系统">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">系统版本</label>
                                    <input type="text" class="form-control" id="systemVersion" value="v2.0.0">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">维护模式</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                        <label class="form-check-label" for="maintenanceMode">启用维护模式</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">性能配置</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">缓存过期时间（秒）</label>
                                    <input type="number" class="form-control" id="cacheExpire" value="3600">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">日志级别</label>
                                    <select class="form-select" id="logLevel">
                                        <option value="debug">Debug</option>
                                        <option value="info" selected>Info</option>
                                        <option value="warning">Warning</option>
                                        <option value="error">Error</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generatePlatformConfigContent() {
        return `
            <div class="config-form">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">平台基础配置</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">平台名称</label>
                                    <input type="text" class="form-control" id="platformName" value="PayPal平台">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">平台域名</label>
                                    <input type="text" class="form-control" id="platformDomain" value="paypal.com">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">客服邮箱</label>
                                    <input type="email" class="form-control" id="supportEmail" value="<EMAIL>">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">业务配置</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">默认手续费率（%）</label>
                                    <input type="number" class="form-control" id="defaultFeeRate" value="0.6" step="0.01">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">最小交易金额</label>
                                    <input type="number" class="form-control" id="minAmount" value="0.01" step="0.01">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">最大交易金额</label>
                                    <input type="number" class="form-control" id="maxAmount" value="50000" step="0.01">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateBasicConfigContent() {
        return `
            <div class="config-form">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">基本信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">码商名称</label>
                                    <input type="text" class="form-control" id="providerName" value="">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">联系电话</label>
                                    <input type="tel" class="form-control" id="providerPhone" value="">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">营业时间</label>
                                    <input type="text" class="form-control" id="businessHours" value="9:00-18:00">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">业务配置</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">单笔限额</label>
                                    <input type="number" class="form-control" id="singleLimit" value="5000">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">日限额</label>
                                    <input type="number" class="form-control" id="dailyLimit" value="100000">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">自动接单</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoAccept" checked>
                                        <label class="form-check-label" for="autoAccept">启用自动接单</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateProfileConfigContent() {
        return `
            <div class="config-form">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">个人资料</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">商户名称</label>
                                    <input type="text" class="form-control" id="merchantName" value="">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">联系邮箱</label>
                                    <input type="email" class="form-control" id="merchantEmail" value="">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">联系电话</label>
                                    <input type="tel" class="form-control" id="merchantPhone" value="">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">公司名称</label>
                                    <input type="text" class="form-control" id="companyName" value="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">业务信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">网站地址</label>
                                    <input type="url" class="form-control" id="websiteUrl" value="">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">行业类型</label>
                                    <select class="form-select" id="industryType">
                                        <option value="">请选择行业</option>
                                        <option value="ecommerce">电子商务</option>
                                        <option value="gaming">游戏娱乐</option>
                                        <option value="education">教育培训</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">业务描述</label>
                                    <textarea class="form-control" id="businessDesc" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateApiConfigContent() {
        return `
            <div class="config-form">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">API配置</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">API域名</label>
                                    <input type="text" class="form-control" id="apiDomain" value="api.paypal.com" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">API版本</label>
                                    <select class="form-select" id="apiVersion">
                                        <option value="v1" selected>v1</option>
                                        <option value="v2">v2</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">请求超时（秒）</label>
                                    <input type="number" class="form-control" id="apiTimeout" value="30">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">IP白名单</label>
                                    <textarea class="form-control" id="ipWhitelist" rows="4" placeholder="每行一个IP地址"></textarea>
                                    <small class="form-text text-muted">留空表示不限制IP</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateWebhookConfigContent() {
        return `
            <div class="config-form">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">回调配置</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">回调地址</label>
                                    <input type="url" class="form-control" id="webhookUrl" placeholder="https://your-domain.com/webhook">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">回调超时（秒）</label>
                                    <input type="number" class="form-control" id="webhookTimeout" value="10">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">重试次数</label>
                                    <input type="number" class="form-control" id="retryCount" value="3">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">回调事件</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="paymentSuccess" checked>
                                        <label class="form-check-label" for="paymentSuccess">支付成功</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="paymentFailed" checked>
                                        <label class="form-check-label" for="paymentFailed">支付失败</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="refund">
                                        <label class="form-check-label" for="refund">退款</label>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-outline-primary" id="testWebhookBtn">
                                        <i class="bi bi-play-circle me-2"></i>测试回调
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateSecurityConfigContent() {
        return `
            <div class="config-form">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">登录安全</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">双因子认证</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enable2FA">
                                        <label class="form-check-label" for="enable2FA">启用双因子认证</label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">登录失败锁定</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="loginLock" checked>
                                        <label class="form-check-label" for="loginLock">启用登录锁定</label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">最大失败次数</label>
                                    <input type="number" class="form-control" id="maxLoginAttempts" value="5">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">密码安全</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <button class="btn btn-outline-primary" id="changePasswordBtn">
                                        <i class="bi bi-key me-2"></i>修改密码
                                    </button>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">密码强度要求</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireUppercase" checked>
                                        <label class="form-check-label" for="requireUppercase">包含大写字母</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireNumbers" checked>
                                        <label class="form-check-label" for="requireNumbers">包含数字</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requireSymbols" checked>
                                        <label class="form-check-label" for="requireSymbols">包含特殊字符</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateStyles() {
        return `
            .config-module { padding: 0; }
            .config-form { padding: 20px 0; }
            .card { margin-bottom: 20px; }
            .form-check { margin-bottom: 10px; }
            .form-text { font-size: 0.875em; color: #6c757d; }
        `;
    }

    initializeEvents() {
        // 保存配置
        const saveBtn = document.getElementById('saveConfigBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveConfig());
        }

        // 重置配置
        const resetBtn = document.getElementById('resetConfigBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetConfig());
        }

        // 测试回调
        const testWebhookBtn = document.getElementById('testWebhookBtn');
        if (testWebhookBtn) {
            testWebhookBtn.addEventListener('click', () => this.testWebhook());
        }

        // 修改密码
        const changePasswordBtn = document.getElementById('changePasswordBtn');
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', () => this.showChangePasswordModal());
        }
    }

    async loadData() {
        try {
            console.log(`📊 加载${this.currentRole}的配置数据...`);
            
            // 根据角色加载不同配置
            if (this.currentRole === 'merchant') {
                await this.loadMerchantConfig();
            } else if (this.currentRole === 'provider') {
                await this.loadProviderConfig();
            } else {
                await this.loadSystemConfig();
            }
            
        } catch (error) {
            console.error('❌ 加载配置数据失败:', error);
        }
    }

    async loadMerchantConfig() {
        // 加载商户配置
        this.configs = {
            merchantName: '测试商户',
            merchantEmail: '<EMAIL>',
            merchantPhone: '***********',
            companyName: '测试公司',
            websiteUrl: 'https://test.com',
            industryType: 'ecommerce'
        };
        
        this.updateFormValues();
    }

    async loadProviderConfig() {
        // 加载码商配置
        this.configs = {
            providerName: '测试码商',
            providerPhone: '***********',
            businessHours: '9:00-18:00',
            singleLimit: 5000,
            dailyLimit: 100000,
            autoAccept: true
        };
        
        this.updateFormValues();
    }

    async loadSystemConfig() {
        // 加载系统配置
        this.configs = {
            systemName: 'PayPal支付系统',
            systemVersion: 'v2.0.0',
            maintenanceMode: false,
            cacheExpire: 3600,
            logLevel: 'info'
        };
        
        this.updateFormValues();
    }

    updateFormValues() {
        Object.keys(this.configs).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = this.configs[key];
                } else {
                    element.value = this.configs[key];
                }
            }
        });
    }

    saveConfig() {
        console.log('💾 保存配置');
        
        // 收集表单数据
        const formData = this.collectFormData();
        
        // 模拟保存
        setTimeout(() => {
            this.utils?.showMessage('配置保存成功', 'success');
        }, 500);
    }

    collectFormData() {
        const formData = {};
        const inputs = document.querySelectorAll('.config-form input, .config-form select, .config-form textarea');
        
        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                formData[input.id] = input.checked;
            } else {
                formData[input.id] = input.value;
            }
        });
        
        return formData;
    }

    resetConfig() {
        if (confirm('确定要重置所有配置吗？')) {
            console.log('🔄 重置配置');
            this.loadData();
            this.utils?.showMessage('配置已重置', 'info');
        }
    }

    testWebhook() {
        console.log('🧪 测试回调');
        
        const webhookUrl = document.getElementById('webhookUrl').value;
        if (!webhookUrl) {
            this.utils?.showMessage('请先配置回调地址', 'warning');
            return;
        }
        
        // 模拟测试
        setTimeout(() => {
            this.utils?.showMessage('回调测试成功', 'success');
        }, 1000);
    }

    showChangePasswordModal() {
        console.log('🔑 显示修改密码模态框');
        // 实现修改密码模态框
    }

    showError(container, message) {
        container.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
}

// 全局注册
window.ConfigModule = ConfigModule;
window.configModule = new ConfigModule();

console.log('✅ ConfigModule 已注册到全局'); 