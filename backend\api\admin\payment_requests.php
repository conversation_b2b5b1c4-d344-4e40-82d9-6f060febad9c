<?php
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

$auth = TenantAuth::init();
if (!$auth->requireAdmin()) exit;
$db = $auth->getDatabase();

try {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($action) {
        case 'payment_requests':
            handlePaymentRequests();
            break;
        case 'payment_request_detail':
            handlePaymentRequestDetail();
            break;
        case 'update_payment_status':
            handleUpdatePaymentStatus();
            break;
        case 'retry_payment_request':
            handleRetryPaymentRequest();
            break;
        default:
            handlePaymentRequests();
    }
} catch (Exception $e) {
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 支付请求管理主入口
function handlePaymentRequests() {
    global $method;
    
    switch ($method) {
        case 'GET':
            handleGetPaymentRequests();
            break;
        case 'POST':
            handleCreatePaymentRequest();
            break;
        case 'PUT':
            handleUpdatePaymentRequest();
            break;
        default:
            global $auth;
            $auth->sendError('不支持的请求方法', 405);
    }
}

// 获取支付请求列表
function handleGetPaymentRequests() {
    global $db, $auth;
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    // 管理员可以查看所有商户的支付请求
    if ($merchantId > 0) {
        $whereConditions[] = "pr.merchant_id = ?";
        $params[] = $merchantId;
    }
    
    if (!empty($status)) {
        $whereConditions[] = "pr.status = ?";
        $params[] = $status;
    }
    
    // 日期范围筛选
    if (!empty($dateRange)) {
        switch ($dateRange) {
            case 'today':
                $whereConditions[] = "DATE(pr.created_at) = CURDATE()";
                break;
            case 'yesterday':
                $whereConditions[] = "DATE(pr.created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
                break;
            case 'week':
                $whereConditions[] = "pr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $whereConditions[] = "pr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
        }
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(pr.request_id LIKE ? OR pr.merchant_order_no LIKE ? OR pr.subject LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $totalResult = $db->fetch(
        "SELECT COUNT(*) as count FROM payment_requests pr $whereClause",
        $params
    );
    $total = $totalResult['count'];
    
    // 获取支付请求列表
    $requests = $db->fetchAll(
        "SELECT pr.*, 
                m.company_name as merchant_name,
                u.real_name as merchant_real_name,
                p.name as product_name,
                a.account_name as alipay_account_name,
                a.account_number as alipay_account_number,
                pr.request_id as out_trade_no,
                pr.merchant_order_no as order_id,
                'alipay' as payment_method,
                pr.fee as service_fee
         FROM payment_requests pr
         LEFT JOIN merchants m ON pr.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         LEFT JOIN products p ON pr.product_id = p.id
         LEFT JOIN alipay_accounts a ON pr.alipay_account_id = a.id
         $whereClause
         ORDER BY pr.created_at DESC
         LIMIT ? OFFSET ?",
        array_merge($params, array($limit, $offset))
    );
    
    // 获取统计信息
    $stats = $db->fetch(
        "SELECT 
            COUNT(*) as total_requests,
            COUNT(CASE WHEN pr.status = 'pending' THEN 1 END) as pending_requests,
            COUNT(CASE WHEN pr.status = 'processing' THEN 1 END) as processing_requests,
            COUNT(CASE WHEN pr.status = 'success' OR pr.status = 'paid' THEN 1 END) as success_requests,
            COUNT(CASE WHEN pr.status = 'failed' THEN 1 END) as failed_requests,
            COUNT(CASE WHEN pr.status = 'cancelled' THEN 1 END) as cancelled_requests,
            SUM(CASE WHEN pr.status IN ('success', 'paid') THEN pr.amount ELSE 0 END) as total_amount,
            AVG(CASE WHEN pr.status IN ('success', 'paid') THEN pr.amount ELSE NULL END) as avg_amount
         FROM payment_requests pr $whereClause",
        $params
    );
    
    $auth->sendSuccess(array(
        'payment_requests' => $requests,
        'stats' => $stats,
        'pagination' => array(
            'current_page' => $page,
            'total_pages' => ceil($total / $limit),
            'total_records' => $total,
            'per_page' => $limit
        )
    ));
}

// 创建支付请求
function handleCreatePaymentRequest() {
    global $db, $auth;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    $required_fields = ['product_id', 'amount', 'out_trade_no'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            $auth->sendError("缺少必需字段: $field", 400);
            return;
        }
    }
    
    $productId = intval($input['product_id']);
    $amount = floatval($input['amount']);
    $outTradeNo = $input['out_trade_no'];
    $subject = isset($input['subject']) ? $input['subject'] : '';
    $body = isset($input['body']) ? $input['body'] : '';
    $merchantId = isset($input['merchant_id']) ? intval($input['merchant_id']) : 0;
    
    if (!$merchantId) {
        $auth->sendError('缺少商户ID', 400);
        return;
    }
    
    // 验证产品存在
    $product = $db->fetch("SELECT * FROM products WHERE id = ?", array($productId));
    if (!$product) {
        $auth->sendError('产品不存在', 400);
        return;
    }
    
    // 验证商户存在
    $merchant = $db->fetch("SELECT * FROM merchants WHERE id = ?", array($merchantId));
    if (!$merchant) {
        $auth->sendError('商户不存在', 400);
        return;
    }
    
    // 检查订单号是否重复
    $existingRequest = $db->fetch(
        "SELECT id FROM payment_requests WHERE merchant_order_no = ? AND merchant_id = ?",
        array($outTradeNo, $merchantId)
    );
    
    if ($existingRequest) {
        $auth->sendError('订单号已存在', 400);
        return;
    }
    
    // 创建支付请求
    $requestId = 'PR' . date('YmdHis') . rand(1000, 9999);
    $fee = $amount * ($product['rate'] / 100);
    
    $sql = "INSERT INTO payment_requests (
        request_id, merchant_id, product_id, merchant_order_no, subject, body,
        amount, fee, status, client_ip, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', ?, NOW(), NOW())";
    
    $db->execute($sql, array(
        $requestId, $merchantId, $productId, $outTradeNo, $subject, $body,
        $amount, $fee, getClientIP()
    ));
    
    $newId = $db->lastInsertId();
    
    $auth->logUserAction('payment_request_create', 'payment_request', $newId, "创建支付请求: {$requestId}");
    $auth->sendSuccess(array('id' => $newId, 'request_id' => $requestId), '支付请求创建成功');
}

// 更新支付请求
function handleUpdatePaymentRequest() {
    global $db, $auth;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $id = isset($input['id']) ? intval($input['id']) : 0;
    
    if (!$id) {
        $auth->sendError('缺少支付请求ID', 400);
        return;
    }
    
    $request = $db->fetch("SELECT * FROM payment_requests WHERE id = ?", array($id));
    if (!$request) {
        $auth->sendError('支付请求不存在', 404);
        return;
    }
    
    $updateFields = array();
    $params = array();
    
    if (isset($input['status'])) {
        $updateFields[] = "status = ?";
        $params[] = $input['status'];
    }
    
    if (isset($input['remark'])) {
        $updateFields[] = "remark = ?";
        $params[] = $input['remark'];
    }
    
    if (empty($updateFields)) {
        $auth->sendError('没有要更新的字段', 400);
        return;
    }
    
    $updateFields[] = "updated_at = NOW()";
    $params[] = $id;
    
    $sql = "UPDATE payment_requests SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $db->execute($sql, $params);
    
    $auth->logUserAction('payment_request_update', 'payment_request', $id, "更新支付请求状态");
    $auth->sendSuccess(null, '支付请求更新成功');
}

// 获取支付请求详情
function handlePaymentRequestDetail() {
    global $db, $auth;
    
    $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if (!$id) {
        $auth->sendError('缺少支付请求ID', 400);
        return;
    }
    
    $request = $db->fetch(
        "SELECT pr.*, 
                m.company_name as merchant_name,
                u.real_name as merchant_real_name,
                p.name as product_name,
                p.rate as product_rate
         FROM payment_requests pr
         LEFT JOIN merchants m ON pr.merchant_id = m.id
         LEFT JOIN users u ON m.user_id = u.id
         LEFT JOIN products p ON pr.product_id = p.id
         WHERE pr.id = ?",
        array($id)
    );
    
    if (!$request) {
        $auth->sendError('支付请求不存在', 404);
        return;
    }
    
    $auth->sendSuccess($request);
}

// 更新支付状态
function handleUpdatePaymentStatus() {
    global $db, $auth;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $id = isset($input['id']) ? intval($input['id']) : 0;
    $status = isset($input['status']) ? $input['status'] : '';
    $remark = isset($input['remark']) ? $input['remark'] : '';
    
    if (!$id || !$status) {
        $auth->sendError('缺少必需参数', 400);
        return;
    }
    
    $request = $db->fetch("SELECT * FROM payment_requests WHERE id = ?", array($id));
    if (!$request) {
        $auth->sendError('支付请求不存在', 404);
        return;
    }
    
    $sql = "UPDATE payment_requests SET status = ?, remark = ?, updated_at = NOW() WHERE id = ?";
    $db->execute($sql, array($status, $remark, $id));
    
    $auth->logUserAction('payment_status_update', 'payment_request', $id, "更新支付状态为: {$status}");
    $auth->sendSuccess(null, '支付状态更新成功');
}

// 重试支付请求
function handleRetryPaymentRequest() {
    global $db, $auth;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $id = isset($input['id']) ? intval($input['id']) : 0;
    
    if (!$id) {
        $auth->sendError('缺少支付请求ID', 400);
        return;
    }
    
    $request = $db->fetch("SELECT * FROM payment_requests WHERE id = ?", array($id));
    if (!$request) {
        $auth->sendError('支付请求不存在', 404);
        return;
    }
    
    if ($request['status'] === 'success' || $request['status'] === 'paid') {
        $auth->sendError('该支付请求已成功，无需重试', 400);
        return;
    }
    
    // 重置状态为pending
    $sql = "UPDATE payment_requests SET status = 'pending', updated_at = NOW() WHERE id = ?";
    $db->execute($sql, array($id));
    
    $auth->logUserAction('payment_retry', 'payment_request', $id, "重试支付请求");
    $auth->sendSuccess(null, '支付请求重试成功');
}

// 获取客户端IP
function getClientIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}
?> 