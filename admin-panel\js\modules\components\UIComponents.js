/**
 * 通用UI组件库
 * 提供可重用的界面组件，支持角色驱动的界面生成
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-20
 */

class UIComponents {
    /**
     * 生成统计卡片组
     * @param {Array} stats 统计数据数组
     * @param {string} role 用户角色
     * @returns {string} HTML字符串
     */
    static generateStatsCards(stats, role) {
        if (!stats || !Array.isArray(stats)) return '';
        
        return `
            <div class="row mb-4">
                ${stats.map(stat => `
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-${stat.color || 'primary'} shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-${stat.color || 'primary'} text-uppercase mb-1">
                                            ${stat.label}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            ${stat.value}
                                        </div>
                                        ${stat.change ? `
                                            <div class="text-xs ${stat.change.startsWith('+') ? 'text-success' : 'text-danger'}">
                                                ${stat.change}
                                            </div>
                                        ` : ''}
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi ${stat.icon} fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * 生成选项卡导航
     * @param {Array} tabs 选项卡数组
     * @param {string} activeTab 当前激活的选项卡
     * @param {string} role 用户角色
     * @returns {string} HTML字符串
     */
    static generateTabs(tabs, activeTab, role) {
        if (!tabs || !Array.isArray(tabs)) return '';
        
        return `
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                ${tabs.map((tab, index) => `
                    <li class="nav-item" role="presentation">
                        <button class="nav-link ${tab.id === activeTab ? 'active' : ''}" 
                                id="${tab.id}-tab" 
                                data-bs-toggle="tab" 
                                data-bs-target="#${tab.id}" 
                                type="button" 
                                role="tab">
                            <i class="bi ${tab.icon} me-2"></i>${tab.label}
                        </button>
                    </li>
                `).join('')}
            </ul>
            <div class="tab-content" id="mainTabsContent">
                ${tabs.map(tab => `
                    <div class="tab-pane fade ${tab.id === activeTab ? 'show active' : ''}" 
                         id="${tab.id}" 
                         role="tabpanel" 
                         aria-labelledby="${tab.id}-tab">
                        <div id="${tab.id}-content">
                            <!-- 内容将由各模块动态加载 -->
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * 生成数据表格
     * @param {Object} config 表格配置
     * @param {string} role 用户角色
     * @returns {string} HTML字符串
     */
    static generateDataTable(config, role) {
        const { 
            id = 'dataTable', 
            columns = [], 
            data = [], 
            actions = [],
            searchable = true,
            pagination = true,
            exportable = false
        } = config;

        return `
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">${config.title || '数据表格'}</h6>
                    <div class="d-flex gap-2">
                        ${searchable ? `
                            <div class="input-group" style="width: 250px;">
                                <input type="text" class="form-control" placeholder="搜索..." id="${id}Search">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        ` : ''}
                        ${exportable ? `
                            <button class="btn btn-success btn-sm" onclick="exportTable('${id}')">
                                <i class="bi bi-download me-1"></i>导出
                            </button>
                        ` : ''}
                        ${config.addButton ? `
                            <button class="btn btn-primary btn-sm" onclick="${config.addButton.onClick}">
                                <i class="bi ${config.addButton.icon || 'bi-plus'} me-1"></i>${config.addButton.text}
                            </button>
                        ` : ''}
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="${id}" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    ${columns.map(col => `
                                        <th class="${col.sortable !== false ? 'sortable' : ''}" 
                                            data-column="${col.key}">
                                            ${col.label}
                                            ${col.sortable !== false ? '<i class="bi bi-arrow-down-up ms-1"></i>' : ''}
                                        </th>
                                    `).join('')}
                                    ${actions.length > 0 ? '<th width="120">操作</th>' : ''}
                                </tr>
                            </thead>
                            <tbody id="${id}Body">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                    ${pagination ? `
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="dataTables_info">
                                显示第 <span id="${id}Start">1</span> 到 <span id="${id}End">10</span> 条，
                                共 <span id="${id}Total">0</span> 条记录
                            </div>
                            <nav>
                                <ul class="pagination mb-0" id="${id}Pagination">
                                    <!-- 分页将动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 生成筛选器组件
     * @param {Array} filters 筛选器配置
     * @param {string} role 用户角色
     * @returns {string} HTML字符串
     */
    static generateFilters(filters, role) {
        if (!filters || !Array.isArray(filters)) return '';

        return `
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-end">
                        ${filters.map(filter => {
                            switch (filter.type) {
                                case 'select':
                                    return `
                                        <div class="col-md-${filter.width || 3} mb-3">
                                            <label class="form-label">${filter.label}</label>
                                            <select class="form-select" id="${filter.id}" name="${filter.name}">
                                                <option value="">全部</option>
                                                ${filter.options.map(opt => `
                                                    <option value="${opt.value}">${opt.label}</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    `;
                                case 'daterange':
                                    return `
                                        <div class="col-md-${filter.width || 4} mb-3">
                                            <label class="form-label">${filter.label}</label>
                                            <div class="input-group">
                                                <input type="date" class="form-control" id="${filter.id}Start" name="${filter.name}Start">
                                                <span class="input-group-text">至</span>
                                                <input type="date" class="form-control" id="${filter.id}End" name="${filter.name}End">
                                            </div>
                                        </div>
                                    `;
                                case 'input':
                                    return `
                                        <div class="col-md-${filter.width || 3} mb-3">
                                            <label class="form-label">${filter.label}</label>
                                            <input type="text" class="form-control" id="${filter.id}" 
                                                   name="${filter.name}" placeholder="${filter.placeholder || ''}">
                                        </div>
                                    `;
                                default:
                                    return '';
                            }
                        }).join('')}
                        <div class="col-md-2 mb-3">
                            <button type="button" class="btn btn-primary w-100" onclick="applyFilters()">
                                <i class="bi bi-funnel me-1"></i>筛选
                            </button>
                        </div>
                        <div class="col-md-2 mb-3">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                <i class="bi bi-arrow-clockwise me-1"></i>重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成加载状态
     * @param {string} message 加载消息
     * @returns {string} HTML字符串
     */
    static generateLoadingState(message = '加载中...') {
        return `
            <div class="d-flex justify-content-center align-items-center py-5">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="text-muted">${message}</div>
                </div>
            </div>
        `;
    }

    /**
     * 生成空状态页面
     * @param {Object} config 空状态配置
     * @returns {string} HTML字符串
     */
    static generateEmptyState(config) {
        const {
            icon = 'bi-inbox',
            title = '暂无数据',
            description = '还没有相关数据',
            actionButton = null
        } = config;

        return `
            <div class="text-center py-5">
                <i class="bi ${icon}" style="font-size: 4rem; color: #6c757d; opacity: 0.5;"></i>
                <h5 class="mt-3 text-muted">${title}</h5>
                <p class="text-muted">${description}</p>
                ${actionButton ? `
                    <button type="button" 
                            class="btn btn-${actionButton.type || 'primary'}"
                            ${actionButton.onClick ? `onclick="${actionButton.onClick}"` : ''}>
                        ${actionButton.icon ? `<i class="bi ${actionButton.icon} me-1"></i>` : ''}${actionButton.text}
                    </button>
                ` : ''}
            </div>
        `;
    }
}

// 导出组件库
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIComponents;
}
if (typeof window !== 'undefined') {
    window.UIComponents = UIComponents;
}
