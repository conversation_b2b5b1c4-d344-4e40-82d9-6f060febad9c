<?php
/**
 * 数据访问层 (DAL)
 * 为所有模块提供统一的数据库操作接口
 * 整合SqlRouter和SqlMiddleware，简化业务层调用
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

require_once __DIR__ . '/SqlRouter.php';
require_once __DIR__ . '/SqlMiddleware.php';

class DataAccessLayer {
    
    private static $instance = null;
    private $sqlMiddleware;
    private $db;
    private $tenantAuth;
    
    private function __construct($db = null, $tenantAuth = null) {
        $this->db = $db;
        $this->tenantAuth = $tenantAuth;
        $this->sqlMiddleware = new SqlMiddleware($tenantAuth);
        
        // 初始化SqlRouter
        SqlRouter::init($db);
    }
    
    /**
     * 获取单例实例
     */
    public static function getInstance($db = null, $tenantAuth = null) {
        if (self::$instance === null) {
            self::$instance = new self($db, $tenantAuth);
        }
        return self::$instance;
    }
    
    // ================================
    // 用户认证相关方法
    // ================================
    
    /**
     * 用户登录查询
     */
    public function getUserByUsername($username) {
        return $this->sqlMiddleware->executeQuery('user.login', [
            'username' => $username
        ]);
    }
    
    /**
     * 根据ID获取用户
     */
    public function getUserById($userId) {
        return $this->sqlMiddleware->executeQuery('user.by_id', [
            'user_id' => $userId
        ]);
    }
    
    /**
     * 更新用户最后登录时间
     */
    public function updateUserLastLogin($userId) {
        return $this->sqlMiddleware->executeQuery('user.update_last_login', [
            'user_id' => $userId
        ]);
    }
    
    // ================================
    // 商户管理相关方法
    // ================================
    
    /**
     * 获取商户列表
     */
    public function getMerchantList($params = []) {
        $defaultParams = [
            'limit' => 20,
            'offset' => 0
        ];
        $params = array_merge($defaultParams, $params);
        
        return $this->sqlMiddleware->executeQuery('merchant.list', $params);
    }
    
    /**
     * 获取商户详情
     */
    public function getMerchantDetail($merchantId) {
        return $this->sqlMiddleware->executeQuery('merchant.detail', [
            'merchant_id' => $merchantId
        ]);
    }
    
    /**
     * 创建商户
     */
    public function createMerchant($data) {
        $requiredFields = [
            'platform_id', 'merchant_code', 'user_id', 'balance', 
            'status', 'api_key', 'callback_url', 'service_rate', 'salt'
        ];
        
        // 验证必需字段
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new Exception("缺少必需字段: {$field}");
            }
        }
        
        return $this->sqlMiddleware->executeQuery('merchant.create', $data);
    }
    
    /**
     * 更新商户信息
     */
    public function updateMerchant($merchantId, $data) {
        $data['merchant_id'] = $merchantId;
        return $this->sqlMiddleware->executeQuery('merchant.update', $data);
    }
    
    /**
     * 获取商户统计
     */
    public function getMerchantCount($params = []) {
        return $this->sqlMiddleware->executeQuery('merchant.count', $params);
    }
    
    // ================================
    // 码商管理相关方法
    // ================================
    
    /**
     * 获取码商列表
     */
    public function getProviderList($params = []) {
        $defaultParams = [
            'limit' => 20,
            'offset' => 0
        ];
        $params = array_merge($defaultParams, $params);
        
        return $this->sqlMiddleware->executeQuery('provider.list', $params);
    }
    
    /**
     * 获取码商详情
     */
    public function getProviderDetail($providerId) {
        return $this->sqlMiddleware->executeQuery('provider.detail', [
            'provider_id' => $providerId
        ]);
    }
    
    /**
     * 获取码商统计数据
     */
    public function getProviderStats($providerId) {
        return $this->sqlMiddleware->executeQuery('provider.stats', [
            'provider_id' => $providerId
        ]);
    }
    
    // ================================
    // 设备管理相关方法
    // ================================
    
    /**
     * 获取设备列表
     */
    public function getDeviceList($params = []) {
        $defaultParams = [
            'limit' => 20,
            'offset' => 0
        ];
        $params = array_merge($defaultParams, $params);
        
        return $this->sqlMiddleware->executeQuery('device.list', $params);
    }
    
    /**
     * 根据码商ID获取设备
     */
    public function getDevicesByProvider($providerId, $status = null) {
        $params = ['provider_id' => $providerId];
        if ($status) {
            $params['status'] = $status;
        }
        
        return $this->sqlMiddleware->executeQuery('device.by_provider', $params);
    }
    
    // ================================
    // 订单管理相关方法
    // ================================
    
    /**
     * 获取订单列表
     */
    public function getOrderList($params = []) {
        $defaultParams = [
            'limit' => 20,
            'offset' => 0
        ];
        $params = array_merge($defaultParams, $params);
        
        return $this->sqlMiddleware->executeQuery('order.list', $params, [
            'format_amounts' => true,
            'add_computed_fields' => true
        ]);
    }
    
    /**
     * 获取订单统计
     */
    public function getOrderStats($params = []) {
        return $this->sqlMiddleware->executeQuery('order.stats', $params, [
            'format_amounts' => true
        ]);
    }
    
    /**
     * 获取订单详情
     */
    public function getOrderDetail($orderId) {
        return $this->sqlMiddleware->executeQuery('order.detail', [
            'order_id' => $orderId
        ], [
            'format_amounts' => true,
            'add_computed_fields' => true
        ]);
    }
    
    // ================================
    // 财务管理相关方法
    // ================================
    
    /**
     * 获取资金流水列表
     */
    public function getFinanceFlowList($params = []) {
        $defaultParams = [
            'limit' => 20,
            'offset' => 0
        ];
        $params = array_merge($defaultParams, $params);
        
        return $this->sqlMiddleware->executeQuery('finance.flow_list', $params, [
            'format_amounts' => true,
            'add_computed_fields' => true
        ]);
    }
    
    /**
     * 获取用户余额
     */
    public function getUserBalance($userId, $userType) {
        return $this->sqlMiddleware->executeQuery('finance.balance', [
            'user_id' => $userId,
            'user_type' => $userType
        ]);
    }
    
    // ================================
    // 支付宝账单相关方法
    // ================================
    
    /**
     * 获取支付宝账单列表
     */
    public function getAlipayBillsList($params = []) {
        $defaultParams = [
            'limit' => 20,
            'offset' => 0
        ];
        $params = array_merge($defaultParams, $params);
        
        return $this->sqlMiddleware->executeQuery('alipay_bills.list', $params, [
            'format_amounts' => true,
            'add_computed_fields' => true
        ]);
    }
    
    /**
     * 获取支付宝账单统计
     */
    public function getAlipayBillsStats($params = []) {
        return $this->sqlMiddleware->executeQuery('alipay_bills.stats', $params, [
            'format_amounts' => true
        ]);
    }
    
    // ================================
    // 平台管理相关方法（系统管理员专用）
    // ================================
    
    /**
     * 获取平台列表
     */
    public function getPlatformList() {
        return $this->sqlMiddleware->executeQuery('platform.list', [], [
            'add_computed_fields' => true
        ]);
    }
    
    /**
     * 获取域名配置列表
     */
    public function getDomainList() {
        return $this->sqlMiddleware->executeQuery('domain.list', [], [
            'add_computed_fields' => true
        ]);
    }
    
    // ================================
    // 日志和审计相关方法
    // ================================
    
    /**
     * 获取用户操作日志
     */
    public function getUserActionLogs($params = []) {
        $defaultParams = [
            'limit' => 50,
            'offset' => 0
        ];
        $params = array_merge($defaultParams, $params);
        
        return $this->sqlMiddleware->executeQuery('logs.user_actions', $params, [
            'add_computed_fields' => true
        ]);
    }
    
    /**
     * 获取API调用日志
     */
    public function getApiCallLogs($params = []) {
        $defaultParams = [
            'limit' => 50,
            'offset' => 0
        ];
        $params = array_merge($defaultParams, $params);
        
        return $this->sqlMiddleware->executeQuery('logs.api_calls', $params, [
            'add_computed_fields' => true
        ]);
    }
    
    // ================================
    // 批量操作方法
    // ================================
    
    /**
     * 批量执行查询
     */
    public function executeBatch($queries, $options = []) {
        return $this->sqlMiddleware->executeBatch($queries, $options);
    }
    
    /**
     * 获取仪表板数据（批量查询优化）
     */
    public function getDashboardData($userType, $userId = null, $timeRange = '7 days') {
        $queries = [];
        
        switch ($userType) {
            case 'merchant':
                $queries = [
                    ['name' => 'order.stats', 'params' => ['merchant_id' => $userId]],
                    ['name' => 'finance.balance', 'params' => ['user_id' => $userId, 'user_type' => 'merchant']],
                    ['name' => 'order.list', 'params' => ['merchant_id' => $userId, 'limit' => 10]]
                ];
                break;
                
            case 'provider':
                $queries = [
                    ['name' => 'provider.stats', 'params' => ['provider_id' => $userId]],
                    ['name' => 'finance.balance', 'params' => ['user_id' => $userId, 'user_type' => 'provider']],
                    ['name' => 'alipay_bills.stats', 'params' => []],
                    ['name' => 'order.stats', 'params' => ['provider_id' => $userId]]
                ];
                break;
                
            case 'platform_admin':
                $queries = [
                    ['name' => 'merchant.count', 'params' => []],
                    ['name' => 'provider.list', 'params' => ['limit' => 5]],
                    ['name' => 'order.stats', 'params' => []]
                ];
                break;
                
            case 'system_admin':
                $queries = [
                    ['name' => 'platform.list', 'params' => []],
                    ['name' => 'merchant.count', 'params' => []],
                    ['name' => 'order.stats', 'params' => []]
                ];
                break;
        }
        
        return $this->executeBatch($queries);
    }
    
    // ================================
    // 缓存管理方法
    // ================================
    
    /**
     * 清理缓存
     */
    public function clearCache($pattern = null) {
        SqlRouter::clearCache($pattern);
        return ['success' => true, 'message' => '缓存已清理'];
    }
    
    /**
     * 获取缓存统计
     */
    public function getCacheStats() {
        return SqlRouter::getCacheStats();
    }
    
    // ================================
    // 性能监控方法
    // ================================
    
    /**
     * 获取查询性能统计
     */
    public function getQueryStats() {
        return $this->sqlMiddleware->getQueryStats();
    }
    
    /**
     * 获取性能报告
     */
    public function getPerformanceReport($timeRange = '1 day') {
        return $this->sqlMiddleware->getPerformanceReport($timeRange);
    }
    
    // ================================
    // 数据库连接管理
    // ================================
    
    /**
     * 设置数据库连接
     */
    public function setDatabase($db) {
        $this->db = $db;
        SqlRouter::setDatabase($db);
    }
    
    /**
     * 获取数据库连接
     */
    public function getDatabase() {
        return $this->db;
    }
    
    /**
     * 测试数据库连接
     */
    public function testConnection() {
        try {
            if (!$this->db) {
                throw new Exception('数据库连接未初始化');
            }
            
            $stmt = $this->db->query('SELECT 1');
            $result = $stmt->fetch();
            
            return [
                'success' => true,
                'message' => '数据库连接正常',
                'server_info' => $this->db->getAttribute(PDO::ATTR_SERVER_INFO)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '数据库连接失败: ' . $e->getMessage()
            ];
        }
    }
    
    // ================================
    // 事务管理
    // ================================
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        if ($this->db) {
            return $this->db->beginTransaction();
        }
        return false;
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        if ($this->db) {
            return $this->db->commit();
        }
        return false;
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        if ($this->db) {
            return $this->db->rollback();
        }
        return false;
    }
    
    /**
     * 执行事务
     */
    public function transaction($callback) {
        try {
            $this->beginTransaction();
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }
}

error_log("DataAccessLayer 类加载完成");
?> 