<?php
require_once dirname(dirname(__FILE__)) . '/config/database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    // 用户登录
    public function login($username, $password) {
        $user = $this->db->fetch(
            "SELECT u.*, 
                    CASE 
                        WHEN u.user_type = 'provider' THEN p.id
                        WHEN u.user_type = 'merchant' THEN m.id
                        ELSE NULL
                    END as profile_id,
                    CASE 
                        WHEN u.user_type = 'provider' THEN CONCAT('码商-', COALESCE(p.provider_code, p.id))
                        WHEN u.user_type = 'merchant' THEN CONCAT('商户-', COALESCE(m.merchant_code, m.id))
                        WHEN u.user_type = 'employee' THEN u.username
                        ELSE u.username
                    END as display_name,
                    CASE 
                        WHEN u.user_type = 'provider' THEN p.provider_code
                        WHEN u.user_type = 'merchant' THEN m.merchant_code
                        ELSE NULL
                    END as real_name
             FROM users u
             LEFT JOIN payment_providers p ON u.id = p.user_id AND u.user_type = 'provider'
             LEFT JOIN merchants m ON u.id = m.user_id AND u.user_type = 'merchant'
             WHERE u.username = ? AND u.status = 'active'",
            array($username)
        );
        
        if ($user && password_verify($password, $user['password'])) {
            // 生成token
            $token = $this->generateToken($user['id']);
            
            // 记录登录日志
            $this->logAction($user['id'], 'login', 'user', $user['id'], '用户登录');
            
            return array(
                'success' => true,
                'token' => $token,
                'user' => array(
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'real_name' => $user['real_name'],
                    'user_type' => $user['user_type'],
                    'profile_id' => $user['profile_id'],
                    'display_name' => $user['display_name']
                )
            );
        }
        
        return array('success' => false, 'message' => '用户名或密码错误');
    }
    
    // 生成JWT token
    private function generateToken($userId) {
        $header = json_encode(array('typ' => 'JWT', 'alg' => 'HS256'));
        $payload = json_encode(array(
            'user_id' => $userId,
            'exp' => time() + (24 * 60 * 60) // 24小时过期
        ));
        
        $headerEncoded = str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($header));
        $payloadEncoded = str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($payload));
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 'your-secret-key', true);
        $signatureEncoded = str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($signature));
        
        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }
    
    // 验证token
    public function verifyToken($token) {
        if (!$token) return false;
        
        $parts = explode('.', $token);
        if (count($parts) !== 3) return false;
        
        list($headerEncoded, $payloadEncoded, $signatureEncoded) = $parts;
        
        $signature = str_replace(array('-', '_'), array('+', '/'), $signatureEncoded);
        $expectedSignature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 'your-secret-key', true);
        $expectedSignatureEncoded = str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($expectedSignature));
        
        if ($signatureEncoded !== $expectedSignatureEncoded) return false;
        
        $payload = json_decode(base64_decode(str_replace(array('-', '_'), array('+', '/'), $payloadEncoded)), true);
        
        if ($payload['exp'] < time()) return false;
        
        return $payload['user_id'];
    }
    
    // 获取当前用户信息
    public function getCurrentUser($token) {
        $userId = $this->verifyToken($token);
        if (!$userId) return false;
        
        return $this->db->fetch(
            "SELECT u.*, 
                    CASE 
                        WHEN u.user_type = 'provider' THEN p.id
                        WHEN u.user_type = 'merchant' THEN m.id
                        ELSE NULL
                    END as profile_id
             FROM users u
             LEFT JOIN payment_providers p ON u.id = p.user_id AND u.user_type = 'provider'
             LEFT JOIN merchants m ON u.id = m.user_id AND u.user_type = 'merchant'
             WHERE u.id = ?",
            array($userId)
        );
    }
    
    // 检查权限
    public function hasPermission($user, $permission) {
        $permissions = array(
            'admin' => array('*'), // 管理员拥有所有权限
            'provider' => array('view_devices', 'manage_devices', 'view_alipay_accounts', 'manage_alipay_accounts', 'view_transactions', 'employee_management', 'position_management', 'position_create', 'financial_management', 'financial_view'),
            'merchant' => array('view_api_keys', 'view_transactions', 'view_api_docs', 'employee_management', 'position_management', 'position_create', 'financial_management', 'financial_view')
        );
        
        $userPermissions = isset($permissions[$user['user_type']]) ? $permissions[$user['user_type']] : array();
        
        return in_array('*', $userPermissions) || in_array($permission, $userPermissions);
    }
    
    // 记录操作日志
    public function logAction($userId, $action, $targetType, $targetId, $description) {
        $this->db->execute(
            "INSERT INTO system_logs (user_id, action, target_type, target_id, description, ip_address, user_agent) 
             VALUES (?, ?, ?, ?, ?, ?, ?)",
            array(
                $userId,
                $action,
                $targetType,
                $targetId,
                $description,
                isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '',
                isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''
            )
        );
    }
    
    // 验证管理员token
    public function verifyAdminToken() {
        $token = $this->getAuthToken();
        $user = $this->getCurrentUser($token);
        return $user && $user['user_type'] === 'admin';
    }
    
    // 获取认证token
    public function getAuthToken() {
        // 调试信息
        error_log("Auth::getAuthToken() - 开始获取token");
        
        // 优先从POST数据获取token（适配前端发送方式）
        if (isset($_POST['token']) && !empty($_POST['token'])) {
            $token = $_POST['token'];
            error_log("Auth::getAuthToken() - 从POST数据获取到token: " . substr($token, 0, 20) . "...");
            return $token;
        }
        
        // PHP 5.6 兼容的获取headers方式
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
            error_log("Auth::getAuthToken() - getallheaders(): " . print_r($headers, true));
            if (isset($headers['Authorization'])) {
                $token = str_replace('Bearer ', '', $headers['Authorization']);
                error_log("Auth::getAuthToken() - 从getallheaders获取到token: " . substr($token, 0, 20) . "...");
                return $token;
            }
        }
        
        // 备用方式
        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
            error_log("Auth::getAuthToken() - 从HTTP_AUTHORIZATION获取到token: " . substr($token, 0, 20) . "...");
            return $token;
        }
        
        error_log("Auth::getAuthToken() - 未找到token (检查了POST数据和Authorization头)");
        error_log("Auth::getAuthToken() - POST数据: " . print_r($_POST, true));
        return null;
    }
    
    // 密码哈希
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
}
?> 