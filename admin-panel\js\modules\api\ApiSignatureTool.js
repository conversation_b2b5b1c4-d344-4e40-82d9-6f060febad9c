/**
 * API签名工具
 * 从ApiModule.js提取的签名相关功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class ApiSignatureTool {
    constructor() {
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.signatureHistory = [];
        this.currentApiKey = '';
        
        console.log('✅ ApiSignatureTool initialized');
    }

    /**
     * 统一渲染方法 - 路由系统调用
     */
    async render(container, params = {}) {
        try {
            console.log('🎯 ApiSignatureTool渲染 - 参数:', params);
            
            // 显示加载状态
            container.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><div class="mt-2">正在加载签名工具...</div></div>';
            
            // 生成HTML内容
            container.innerHTML = this.generateSignatureToolContent();
            
            // 设置全局引用以便HTML中的onclick调用
            window.apiModule = this;
            
            // 自动获取API密钥
            await this.loadApiKey();
            
        } catch (error) {
            console.error('❌ ApiSignatureTool渲染失败:', error);
            container.innerHTML = `<div class="alert alert-danger">签名工具加载失败: ${error.message}</div>`;
        }
    }

    /**
     * 加载API密钥
     */
    async loadApiKey() {
        try {
            const response = await fetch('/api/merchant/tools.php?action=get_key_info', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`,
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            console.log('🔑 API密钥获取响应:', data);

            if (data.code === 200 || data.success) {
                this.currentApiKey = data.data?.api_key || data.api_key || '';
                const secretKeyInput = document.getElementById('secretKey');
                if (secretKeyInput) {
                    secretKeyInput.value = this.currentApiKey;
                    secretKeyInput.placeholder = this.currentApiKey ? 'API密钥已加载' : '未找到API密钥';
                }
                
                if (this.currentApiKey) {
                    this.showSuccess('API密钥已自动加载');
                } else {
                    this.showError('未找到API密钥，请先在API设置中生成');
                }
            } else {
                this.showError('获取API密钥失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 获取API密钥失败:', error);
            this.showError('获取API密钥失败: ' + error.message);
        }
    }

    /**
     * 刷新API密钥
     */
    async refreshApiKey() {
        const secretKeyInput = document.getElementById('secretKey');
        if (secretKeyInput) {
            secretKeyInput.placeholder = '正在刷新API密钥...';
        }
        await this.loadApiKey();
    }

    /**
     * 生成签名工具HTML内容
     */
    generateSignatureToolContent() {
        return `
            <div class="row">
                <!-- 签名生成器 -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-shield-check me-2"></i>签名生成工具
                            </h6>
                        </div>
                        <div class="card-body">
                            <form id="signatureForm">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">签名算法</label>
                                        <div class="form-control-plaintext">
                                            <strong>SHA256</strong>
                                            <small class="text-muted d-block">PayPal API 标准签名算法</small>
                                        </div>
                                        <input type="hidden" id="signatureAlgorithm" value="SHA256">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">编码格式</label>
                                        <select class="form-select" id="encodingFormat">
                                            <option value="hex" selected>HEX (十六进制)</option>
                                            <option value="base64">Base64</option>
                                            <option value="binary">Binary</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">API密钥 (API Key)</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="secretKey" readonly
                                               placeholder="正在获取API密钥...">
                                        <button class="btn btn-outline-secondary" type="button" 
                                                onclick="apiModule.togglePasswordVisibility('secretKey')">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" type="button" 
                                                onclick="apiModule.refreshApiKey()">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">API密钥将自动获取，SHA256签名：将待签名数据与API密钥拼接后进行SHA256哈希</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">待签名数据</label>
                                    <textarea class="form-control" id="signatureData" rows="8" 
                                              placeholder="输入需要签名的数据，例如：
amount=100.00&currency=USD&order_id=12345&timestamp=1640995200"></textarea>
                                </div>
                                
                                <div class="d-flex gap-2 mb-3">
                                    <button type="button" class="btn btn-primary" onclick="apiModule.generateSignature()">
                                        <i class="bi bi-shield-check me-2"></i>生成签名
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="apiModule.clearSignatureForm()">
                                        <i class="bi bi-arrow-clockwise me-2"></i>清空
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="apiModule.loadSignatureTemplate()">
                                        <i class="bi bi-file-text me-2"></i>加载模板
                                    </button>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">生成的签名</label>
                                    <div class="input-group">
                                        <textarea class="form-control" id="generatedSignature" rows="3" readonly 
                                                  placeholder="点击生成签名按钮后，签名结果将显示在这里"></textarea>
                                        <button class="btn btn-outline-secondary" type="button" 
                                                onclick="apiModule.copySignature()">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 快速工具 -->
                <div class="col-lg-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-lightning me-2"></i>快速工具
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2 mb-3">
                                <button class="btn btn-outline-primary" onclick="apiModule.generateTimestamp()">
                                    <i class="bi bi-clock me-2"></i>生成时间戳
                                </button>
                                <button class="btn btn-outline-primary" onclick="apiModule.generateNonce()">
                                    <i class="bi bi-hash me-2"></i>生成随机数
                                </button>
                                <button class="btn btn-outline-success" onclick="apiModule.verifySignature()">
                                    <i class="bi bi-check-circle me-2"></i>验证签名
                                </button>
                            </div>
                            
                            <hr class="my-3">
                            
                            <h6 class="text-muted">快速模板</h6>
                            <div class="list-group list-group-flush">
                                <a href="#" class="list-group-item list-group-item-action" 
                                   onclick="apiModule.loadQuickTemplate('basic_payment')">
                                    <i class="bi bi-credit-card me-2"></i>基础支付签名
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" 
                                   onclick="apiModule.loadQuickTemplate('batch_payment')">
                                    <i class="bi bi-layers me-2"></i>批量支付签名
                                </a>
                                <a href="#" class="list-group-item list-group-item-action" 
                                   onclick="apiModule.loadQuickTemplate('callback_verify')">
                                    <i class="bi bi-arrow-left-right me-2"></i>回调验证签名
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 签名验证 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-check-circle me-2"></i>签名验证
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">原始签名</label>
                            <textarea class="form-control" id="originalSignature" rows="3" 
                                      placeholder="输入需要验证的签名"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">重新计算的签名</label>
                            <textarea class="form-control" id="recalculatedSignature" rows="3" readonly 
                                      placeholder="使用相同参数重新计算的签名"></textarea>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div id="verificationResult" class="alert" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- 签名历史 -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-clock-history me-2"></i>签名历史
                    </h6>
                    <button class="btn btn-sm btn-outline-secondary" onclick="apiModule.clearSignatureHistory()">
                        <i class="bi bi-trash me-1"></i>清空历史
                    </button>
                </div>
                <div class="card-body">
                    <div id="signatureHistoryContainer">
                        <div class="text-center py-4 text-muted">
                            <i class="bi bi-clock" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">暂无签名历史</h5>
                            <p>生成签名后，历史记录将在这里显示</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成签名
     */
    async generateSignature() {
        const algorithm = document.getElementById('signatureAlgorithm')?.value;
        const encoding = document.getElementById('encodingFormat')?.value;
        const secretKey = this.currentApiKey || document.getElementById('secretKey')?.value?.trim();
        const data = document.getElementById('signatureData')?.value?.trim();

        if (!secretKey) {
            this.showError('API密钥未加载，请点击刷新按钮重新获取');
            return;
        }

        if (!data) {
            this.showError('请输入待签名数据');
            return;
        }

        try {
            // SHA256签名：数据 + API密钥 进行SHA256哈希
            const signatureData = data + secretKey;
            const signature = await this.generateSHA256Signature(signatureData, encoding);

            document.getElementById('generatedSignature').value = signature;
            document.getElementById('recalculatedSignature').value = signature;
            
            // 添加到历史记录
            this.addSignatureHistory(algorithm, encoding, signature);
            
            this.showSuccess('签名生成成功');
            
        } catch (error) {
            console.error('签名生成失败:', error);
            this.showError('签名生成失败: ' + error.message);
        }
    }

    /**
     * 生成SHA256签名
     */
    async generateSHA256Signature(data, encoding) {
        const encoder = new TextEncoder();
        const messageData = encoder.encode(data);
        
        const hashBuffer = await crypto.subtle.digest('SHA-256', messageData);
        
        return this.formatSignature(hashBuffer, encoding);
    }

    /**
     * 格式化签名输出
     */
    formatSignature(signatureBuffer, encoding) {
        const signatureArray = new Uint8Array(signatureBuffer);
        
        switch (encoding) {
            case 'hex':
                return Array.from(signatureArray)
                    .map(b => b.toString(16).padStart(2, '0'))
                    .join('');
            case 'base64':
                return btoa(String.fromCharCode.apply(null, signatureArray));
            case 'binary':
                return String.fromCharCode.apply(null, signatureArray);
            default:
                throw new Error('不支持的编码格式');
        }
    }

    /**
     * 验证签名
     */
    verifySignature() {
        const originalSignature = document.getElementById('originalSignature')?.value?.trim();
        const recalculatedSignature = document.getElementById('recalculatedSignature')?.value?.trim();
        const resultContainer = document.getElementById('verificationResult');

        if (!originalSignature || !recalculatedSignature) {
            this.showError('请先生成签名并输入原始签名');
            return;
        }

        const isMatch = originalSignature === recalculatedSignature;
        
        resultContainer.className = `alert ${isMatch ? 'alert-success' : 'alert-danger'}`;
        resultContainer.innerHTML = `
            <i class="bi bi-${isMatch ? 'check-circle' : 'x-circle'} me-2"></i>
            <strong>验证结果：</strong>${isMatch ? '签名匹配 ✓' : '签名不匹配 ✗'}
        `;
        resultContainer.style.display = 'block';
    }

    /**
     * 添加签名历史
     */
    addSignatureHistory(algorithm, encoding, signature) {
        const historyItem = {
            timestamp: new Date().toLocaleString(),
            algorithm,
            encoding,
            signature: signature.substring(0, 50) + (signature.length > 50 ? '...' : ''),
            fullSignature: signature
        };

        this.signatureHistory.unshift(historyItem);
        
        // 限制历史记录数量
        if (this.signatureHistory.length > 20) {
            this.signatureHistory = this.signatureHistory.slice(0, 20);
        }

        this.updateSignatureHistoryDisplay();
    }

    /**
     * 更新签名历史显示
     */
    updateSignatureHistoryDisplay() {
        const container = document.getElementById('signatureHistoryContainer');
        
        if (this.signatureHistory.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="bi bi-clock" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">暂无签名历史</h5>
                    <p>生成签名后，历史记录将在这里显示</p>
                </div>
            `;
            return;
        }

        const historyHtml = this.signatureHistory.map((item, index) => `
            <div class="border-bottom py-2">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="fw-bold">${item.algorithm} (${item.encoding})</div>
                        <div class="text-muted small">${item.timestamp}</div>
                        <div class="font-monospace small text-break">${item.signature}</div>
                    </div>
                    <div class="ms-2">
                        <button class="btn btn-sm btn-outline-secondary" 
                                onclick="apiModule.copyHistorySignature('${item.fullSignature}')">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = historyHtml;
    }

    /**
     * 生成时间戳
     */
    generateTimestamp() {
        const timestamp = Math.floor(Date.now() / 1000);
        const currentData = document.getElementById('signatureData')?.value || '';
        const newData = currentData + (currentData ? '&' : '') + `timestamp=${timestamp}`;
        document.getElementById('signatureData').value = newData;
        this.showSuccess(`已添加时间戳: ${timestamp}`);
    }

    /**
     * 生成随机数
     */
    generateNonce() {
        const nonce = Math.random().toString(36).substring(2, 15);
        const currentData = document.getElementById('signatureData')?.value || '';
        const newData = currentData + (currentData ? '&' : '') + `nonce=${nonce}`;
        document.getElementById('signatureData').value = newData;
        this.showSuccess(`已添加随机数: ${nonce}`);
    }

    /**
     * 加载快速模板
     */
    loadQuickTemplate(templateType) {
        const templates = {
            basic_payment: {
                encoding: 'hex',
                data: 'amount=100.00&currency=USD&order_id=ORDER_12345&merchant_id=MERCHANT_123&timestamp=1640995200&nonce=abc123'
            },
            batch_payment: {
                encoding: 'base64',
                data: 'batch_id=BATCH_001&total_amount=500.00&count=5&currency=USD&timestamp=1640995200'
            },
            callback_verify: {
                encoding: 'hex',
                data: 'order_id=ORDER_12345&status=success&amount=100.00&currency=USD&timestamp=1640995200'
            }
        };

        const template = templates[templateType];
        if (template) {
            document.getElementById('encodingFormat').value = template.encoding;
            document.getElementById('signatureData').value = template.data;
            this.showSuccess('模板加载成功');
        }
    }

    /**
     * 复制签名
     */
    copySignature() {
        const signature = document.getElementById('generatedSignature')?.value;
        if (signature) {
            navigator.clipboard.writeText(signature).then(() => {
                this.showSuccess('签名已复制到剪贴板');
            }).catch(() => {
                this.showError('复制失败');
            });
        }
    }

    /**
     * 复制历史签名
     */
    copyHistorySignature(signature) {
        navigator.clipboard.writeText(signature).then(() => {
            this.showSuccess('签名已复制到剪贴板');
        }).catch(() => {
            this.showError('复制失败');
        });
    }

    /**
     * 清空签名表单
     */
    clearSignatureForm() {
        // 不清空API密钥，只清空其他字段
        document.getElementById('signatureData').value = '';
        document.getElementById('generatedSignature').value = '';
        document.getElementById('originalSignature').value = '';
        document.getElementById('recalculatedSignature').value = '';
        document.getElementById('verificationResult').style.display = 'none';
    }

    /**
     * 清空签名历史
     */
    clearSignatureHistory() {
        this.signatureHistory = [];
        this.updateSignatureHistoryDisplay();
        this.showSuccess('签名历史已清空');
    }

    /**
     * 切换密码可见性
     */
    togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        const button = input.nextElementSibling.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            button.className = 'bi bi-eye-slash';
        } else {
            input.type = 'password';
            button.className = 'bi bi-eye';
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        if (window.apiModule && window.apiModule.showSuccess && window.apiModule.showSuccess !== this.showSuccess) {
            window.apiModule.showSuccess(message);
        } else {
            console.log('✅ 成功:', message);
            // 简单的成功提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-check-circle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        if (window.apiModule && window.apiModule.showError && window.apiModule.showError !== this.showError) {
            window.apiModule.showError(message);
        } else {
            console.error('❌ 错误:', message);
            // 简单的错误提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiSignatureTool;
} else if (typeof window !== 'undefined') {
    window.ApiSignatureTool = ApiSignatureTool;
} 