<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多租户认证系统测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background-color: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .test-result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace; 
            white-space: pre-wrap; 
        }
        .success { 
            background-color: #d4edda; 
            border: 1px solid #c3e6cb; 
            color: #155724; 
        }
        .error { 
            background-color: #f8d7da; 
            border: 1px solid #f5c6cb; 
            color: #721c24; 
        }
        .info { 
            background-color: #d1ecf1; 
            border: 1px solid #bee5eb; 
            color: #0c5460; 
        }
        button { 
            background-color: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px; 
        }
        button:hover { 
            background-color: #0056b3; 
        }
        button:disabled { 
            background-color: #6c757d; 
            cursor: not-allowed; 
        }
        input[type="text"], input[type="password"] { 
            width: 200px; 
            padding: 8px; 
            margin: 5px; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
        }
        .json-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-success { background-color: #28a745; color: white; }
        .status-error { background-color: #dc3545; color: white; }
        .status-pending { background-color: #ffc107; color: black; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 多租户认证系统测试 - Day 6 Step 2</h1>
        <div class="info test-result">
            <strong>测试目标:</strong> 验证auth.php多租户登录系统改造结果<br>
            <strong>测试域名:</strong> <span id="currentDomain"></span><br>
            <strong>API地址:</strong> <span id="apiUrl"></span>
        </div>
        
        <!-- 测试状态总览 -->
        <div class="test-section">
            <h2>📊 测试状态总览</h2>
            <p>租户信息: <span id="statusTenant" class="status-badge status-pending">待测试</span></p>
            <p>用户登录: <span id="statusLogin" class="status-badge status-pending">待测试</span></p>
            <p>认证检查: <span id="statusAuth" class="status-badge status-pending">待测试</span></p>
            <p>用户登出: <span id="statusLogout" class="status-badge status-pending">待测试</span></p>
        </div>
        
        <!-- 测试1: 获取租户信息 -->
        <div class="test-section">
            <h2>测试1: 获取租户信息</h2>
            <button onclick="testGetTenantInfo()">获取租户信息</button>
            <button onclick="runAllTests()">🚀 运行所有测试</button>
            <div id="tenantInfoResult" class="test-result"></div>
        </div>
        
        <!-- 测试2: 用户登录 -->
        <div class="test-section">
            <h2>测试2: 用户登录测试</h2>
            <div>
                <input type="text" id="username" placeholder="用户名" value="admin">
                <input type="password" id="password" placeholder="密码" value="admin123">
                <button onclick="testLogin()">登录</button>
                <button onclick="testInvalidLogin()">测试错误登录</button>
            </div>
            <div id="loginResult" class="test-result"></div>
        </div>
        
        <!-- 测试3: 检查认证状态 -->
        <div class="test-section">
            <h2>测试3: 检查认证状态</h2>
            <button onclick="testCheckAuth()" id="checkAuthBtn" disabled>检查认证状态</button>
            <div id="authCheckResult" class="test-result"></div>
        </div>
        
        <!-- 测试4: 登出 -->
        <div class="test-section">
            <h2>测试4: 用户登出</h2>
            <button onclick="testLogout()" id="logoutBtn" disabled>登出</button>
            <div id="logoutResult" class="test-result"></div>
        </div>
        
        <!-- 全局状态 -->
        <div class="test-section">
            <h2>🔍 当前状态</h2>
            <p><strong>Token:</strong> <span id="currentToken">未登录</span></p>
            <p><strong>用户信息:</strong></p>
            <div id="currentUser" class="json-display">未登录</div>
            <p><strong>租户信息:</strong></p>
            <div id="tenantInfo" class="json-display">未获取</div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentToken = '';
        let currentUser = null;
        let tenantInfo = null;
        const API_BASE = window.location.protocol + '//' + window.location.hostname + '/api/admin/auth.php';
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('currentDomain').textContent = window.location.hostname;
            document.getElementById('apiUrl').textContent = API_BASE;
            
            // 自动获取租户信息
            testGetTenantInfo();
        });
        
        // 通用API请求函数
        async function apiRequest(action, data = {}, useToken = false) {
            const requestData = { action, ...data };
            
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (useToken && currentToken) {
                headers['Authorization'] = 'Bearer ' + currentToken;
            }
            
            try {
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestData)
                });
                
                let result;
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    result = await response.json();
                } else {
                    const text = await response.text();
                    result = { error: 'Non-JSON response', response_text: text };
                }
                
                return { 
                    success: response.ok, 
                    data: result, 
                    status: response.status,
                    statusText: response.statusText
                };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        // 更新状态标记
        function updateStatus(statusId, success) {
            const element = document.getElementById(statusId);
            element.className = 'status-badge ' + (success ? 'status-success' : 'status-error');
            element.textContent = success ? '✅ 通过' : '❌ 失败';
        }
        
        // 显示结果
        function displayResult(elementId, result, isSuccess = null) {
            const element = document.getElementById(elementId);
            
            if (isSuccess === null) {
                isSuccess = result.success;
            }
            
            element.className = 'test-result ' + (isSuccess ? 'success' : 'error');
            element.textContent = JSON.stringify(result, null, 2);
        }
        
        // 更新按钮状态
        function updateButtonStates() {
            const hasToken = !!currentToken;
            document.getElementById('checkAuthBtn').disabled = !hasToken;
            document.getElementById('logoutBtn').disabled = !hasToken;
        }
        
        // 测试1: 获取租户信息
        async function testGetTenantInfo() {
            displayResult('tenantInfoResult', { message: '正在获取租户信息...' }, true);
            
            const result = await apiRequest('get_tenant_info');
            displayResult('tenantInfoResult', result);
            
            if (result.success && result.data && result.data.data) {
                tenantInfo = result.data.data;
                document.getElementById('tenantInfo').textContent = JSON.stringify(tenantInfo, null, 2);
                updateStatus('statusTenant', true);
            } else {
                updateStatus('statusTenant', false);
            }
        }
        
        // 测试2: 用户登录
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                displayResult('loginResult', { error: '请输入用户名和密码' }, false);
                return;
            }
            
            displayResult('loginResult', { message: '正在登录...' }, true);
            
            const result = await apiRequest('login', { username, password });
            displayResult('loginResult', result);
            
            if (result.success && result.data && result.data.data) {
                // 保存token和用户信息
                currentToken = result.data.data.token;
                currentUser = result.data.data.user;
                
                // 更新显示
                document.getElementById('currentToken').textContent = currentToken.substring(0, 50) + '...';
                document.getElementById('currentUser').textContent = JSON.stringify(currentUser, null, 2);
                
                updateStatus('statusLogin', true);
                updateButtonStates();
            } else {
                updateStatus('statusLogin', false);
            }
        }
        
        // 测试错误登录
        async function testInvalidLogin() {
            displayResult('loginResult', { message: '正在测试错误登录...' }, true);
            
            const result = await apiRequest('login', { username: 'wrong_user', password: 'wrong_pass' });
            displayResult('loginResult', result);
            
            // 错误登录应该返回失败，这是正确的行为
            const isCorrectBehavior = !result.success || (result.data && result.data.code !== 200);
            if (isCorrectBehavior) {
                displayResult('loginResult', { 
                    ...result, 
                    test_note: '✅ 错误登录正确被拒绝，这是预期行为' 
                }, true);
            }
        }
        
        // 测试3: 检查认证状态
        async function testCheckAuth() {
            if (!currentToken) {
                displayResult('authCheckResult', { error: '请先登录获取Token' }, false);
                return;
            }
            
            displayResult('authCheckResult', { message: '正在检查认证状态...' }, true);
            
            const result = await apiRequest('check_auth', {}, true);
            displayResult('authCheckResult', result);
            
            if (result.success) {
                updateStatus('statusAuth', true);
            } else {
                updateStatus('statusAuth', false);
            }
        }
        
        // 测试4: 用户登出
        async function testLogout() {
            if (!currentToken) {
                displayResult('logoutResult', { error: '当前未登录' }, false);
                return;
            }
            
            displayResult('logoutResult', { message: '正在登出...' }, true);
            
            const result = await apiRequest('logout', {}, true);
            displayResult('logoutResult', result);
            
            if (result.success) {
                // 清除本地状态
                currentToken = '';
                currentUser = null;
                document.getElementById('currentToken').textContent = '未登录';
                document.getElementById('currentUser').textContent = '未登录';
                
                updateStatus('statusLogout', true);
                updateButtonStates();
            } else {
                updateStatus('statusLogout', false);
            }
        }
        
        // 运行所有测试
        async function runAllTests() {
            console.log('开始运行完整测试流程...');
            
            // 1. 获取租户信息
            await testGetTenantInfo();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 2. 用户登录
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 3. 检查认证状态
            if (currentToken) {
                await testCheckAuth();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 4. 用户登出
                await testLogout();
            }
            
            console.log('完整测试流程结束');
        }
    </script>
</body>
</html> 