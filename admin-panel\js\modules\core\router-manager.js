/**
 * 路由管理器 - PayPal四层架构路由系统
 * 负责菜单路由映射、URL管理、权限控制和页面导航
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class RouterManager {
    constructor() {
        this.currentRoute = null;
        this.routeHistory = [];
        this.routeConfig = new Map();
        this.moduleLoader = null;
        this.uiManager = null;
        this.authManager = null;
        
        // 初始化路由配置
        this.initializeRouteConfig();
        
        // 绑定浏览器历史记录事件
        this.bindHistoryEvents();
        
        console.log('🚀 RouterManager initialized');
    }

    /**
     * 初始化路由配置
     * 根据四层架构菜单设计配置所有路由
     */
    initializeRouteConfig() {
        // 系统管理员路由 - 重构后11个功能
        this.addRouteGroup('system_admin', [
            // 基础管理
            { path: '/dashboard', menuId: 'dashboard', module: 'dashboard', component: 'SystemDashboard', title: '系统总览' },
            { path: '/platforms', menuId: 'platforms', module: 'platform-management', component: 'PlatformManagement', title: '平台管理' },
            { path: '/domain-management', menuId: 'domain-management', module: 'domain-management', component: 'DomainManagement', title: '域名管理' },
            
            // App管理
            { path: '/app-versions', menuId: 'app-versions', module: 'app-management', component: 'AppVersionManager', title: '版本发布管理' },
            
            // 脚本管理
            { path: '/system-scripts', menuId: 'system-scripts', module: 'SystemModule', method: 'render', params: { role: 'system_admin', type: 'script' }, title: '系统脚本' },
            
            // 安全管理
            { path: '/system-monitoring', menuId: 'system-monitoring', module: 'system-monitoring', component: 'SystemMonitoring', title: '系统监控' },
            { path: '/security-logs', menuId: 'security-logs', module: 'security-management', component: 'SecurityLogManager', title: '安全日志' },
            { path: '/risk-rules', menuId: 'risk-rules', module: 'SystemModule', method: 'render', params: { role: 'system_admin', type: 'security' }, title: '风控规则设置' },
            { path: '/totp-system', menuId: 'totp-system', module: 'security-management', component: 'TOTPSystemManager', title: 'TOTP管理' },
            
            // 系统设置
            { path: '/global-finance', menuId: 'global-finance', module: 'FinanceModule', method: 'render', params: { role: 'system_admin' }, title: '全系统财务' },
            { path: '/system-settings', menuId: 'system-settings', module: 'ConfigModule', method: 'render', params: { role: 'system_admin' }, title: '系统设置' },
            { path: '/api-router', menuId: 'api-router', module: 'ApiRouterModule', method: 'render', params: { role: 'system_admin' }, title: 'API路由管理' },
            { path: '/sql-router', menuId: 'sql-router', module: 'SqlRouterModule', method: 'render', params: { role: 'system_admin' }, title: 'SQL路由管理' },
            { path: '/backup-restore', menuId: 'backup-restore', module: 'backup-restore', component: 'BackupRestore', title: '备份恢复' }
        ]);

        // 平台管理员路由 - 重构后12个功能
        this.addRouteGroup('platform_admin', [
            // 团队管理
            { path: '/dashboard', menuId: 'dashboard', module: 'dashboard', component: 'PlatformDashboard', title: '平台总览' },
            { path: '/employees', menuId: 'employees', module: 'employee-management', component: 'EmployeeManager', title: '员工管理' },
            { path: '/job-positions', menuId: 'job-positions', module: 'job-position-management', component: 'JobPositionManager', title: '职位管理' },
            
            // 码商管理
            { path: '/providers', menuId: 'providers', module: 'provider-management', method: 'render', params: { role: 'platform_admin' }, title: '码商列表' },
            { path: '/alipay-accounts', menuId: 'alipay-accounts', module: 'account-management', component: 'AccountManager', title: '支付宝账户' },
            
            // 商户管理
            { path: '/merchants', menuId: 'merchants', module: 'merchant-management', method: 'render', params: { role: 'platform_admin' }, title: '商户列表' },
            { path: '/products', menuId: 'products', module: 'product-management', method: 'render', params: { role: 'platform_admin' }, title: '产品管理' },
            
            // 财务管理
            { path: '/finance-flow', menuId: 'finance-flow', module: 'FinanceModule', method: 'render', params: { role: 'platform_admin', activeTab: 'flow' }, title: '资金流水' },
            { path: '/settlement-mgmt', menuId: 'settlement-mgmt', module: 'FinanceModule', method: 'render', params: { role: 'platform_admin', activeTab: 'settlement' }, title: '结算管理' },
            { path: '/finance-reports', menuId: 'finance-reports', module: 'FinanceModule', method: 'render', params: { role: 'platform_admin', activeTab: 'reports' }, title: '财务报表' },
            
            // 平台设置
            { path: '/platform-config', menuId: 'platform-config', module: 'ConfigModule', method: 'render', params: { role: 'platform_admin' }, title: '平台配置' }
        ]);

        // 码商路由 - 重构后18个功能
        this.addRouteGroup('provider', [
            // 团队管理
            { path: '/dashboard', menuId: 'dashboard', module: 'FinanceModule', method: 'render', params: { role: 'provider' }, title: '仪表板' },
            { path: '/employees', menuId: 'employees', module: 'employee-management', component: 'EmployeeManager', title: '员工管理' },
            { path: '/job-positions', menuId: 'job-positions', module: 'job-position-management', component: 'JobPositionManager', title: '职位管理' },
            
            // 设备管理
            { path: '/devices', menuId: 'devices', module: 'DeviceModule', method: 'render', params: { role: 'provider' }, title: '设备列表' },
            { path: '/device-groups', menuId: 'device-groups', module: 'DeviceModule', method: 'render', params: { role: 'provider' }, title: '设备小组' },
            
            // 账户管理
            { path: '/alipay-accounts', menuId: 'alipay-accounts', module: 'account-management', component: 'AccountManager', title: '支付宝账户' },
            { path: '/totp', menuId: 'totp', module: 'security-management', component: 'TOTPManager', title: 'TOTP管理' },
            
            // 订单管理
            { path: '/realtime-orders', menuId: 'realtime-orders', module: 'OrderModule', method: 'render', params: { role: 'provider' }, title: '实时订单' },
            { path: '/order-stats', menuId: 'order-stats', module: 'OrderModule', method: 'render', params: { role: 'provider' }, title: '订单统计' },
            { path: '/order-export', menuId: 'order-export', module: 'OrderModule', method: 'render', params: { role: 'provider' }, title: '订单导出' },
            
            // 风控管理
            { path: '/blacklist-mgmt', menuId: 'blacklist-mgmt', module: 'SystemModule', method: 'render', params: { role: 'provider', type: 'security' }, title: '黑名单管理' },
            { path: '/risk-monitor', menuId: 'risk-monitor', module: 'SystemModule', method: 'render', params: { role: 'provider', type: 'security' }, title: '风险监控' },
            { path: '/risk-reports', menuId: 'risk-reports', module: 'SystemModule', method: 'render', params: { role: 'provider', type: 'security' }, title: '风控报表' },
            
            // 财务管理
            { path: '/finance-flow', menuId: 'finance-flow', module: 'FinanceModule', method: 'render', params: { role: 'provider', activeTab: 'flow' }, title: '资金流水' },
            { path: '/settlement-mgmt', menuId: 'settlement-mgmt', module: 'FinanceModule', method: 'render', params: { role: 'provider', activeTab: 'settlement' }, title: '结算管理' },
            { path: '/reconcile-stats', menuId: 'reconcile-stats', module: 'FinanceModule', method: 'render', params: { role: 'provider', activeTab: 'reconcile' }, title: '对账统计' },
            { path: '/finance-reports', menuId: 'finance-reports', module: 'FinanceModule', method: 'render', params: { role: 'provider', activeTab: 'reports' }, title: '财务报表' },
            { path: '/transaction-flow', menuId: 'transaction-flow', module: 'OrderModule', method: 'render', params: { role: 'provider' }, title: '交易流水' },
            { path: '/alipay-bills', menuId: 'alipay-bills', module: 'AlipayBillsModule', method: 'render', params: { role: 'provider' }, title: '支付宝账单' }
        ]);

        // 商户路由 - 重构后16个功能
        this.addRouteGroup('merchant', [
            // 团队管理
            { path: '/dashboard', menuId: 'dashboard', module: 'FinanceModule', method: 'render', params: { role: 'merchant' }, title: '仪表板' },
            { path: '/employees', menuId: 'employees', module: 'employee-management', component: 'EmployeeManager', title: '员工管理' },
            { path: '/job-positions', menuId: 'job-positions', module: 'job-position-management', component: 'JobPositionManager', title: '职位管理' },
            
            // 业务管理
            { path: '/orders', menuId: 'orders', module: 'OrderModule', method: 'render', params: { role: 'merchant' }, title: '订单管理' },
            { path: '/business-analysis', menuId: 'business-analysis', module: 'OrderModule', method: 'render', params: { role: 'merchant' }, title: '业务分析' },
            { path: '/products', menuId: 'products', module: 'product-management', method: 'render', params: { role: 'merchant' }, title: '产品管理' },
            
            // 开发者工具
            { path: '/api-settings', menuId: 'api-settings', module: 'ApiKeyManager', method: 'render', params: { role: 'merchant', view: 'settings' }, title: 'API设置' },
            { path: '/api-test', menuId: 'api-test', module: 'ApiTester', method: 'render', params: { role: 'merchant' }, title: '接口测试' },
            { path: '/code-generator', menuId: 'code-generator', module: 'ApiCodeGenerator', method: 'render', params: { role: 'merchant' }, title: '代码生成器' },
            { path: '/signature-tool', menuId: 'signature-tool', module: 'ApiSignatureTool', method: 'render', params: { role: 'merchant' }, title: '签名工具' },
            { path: '/api-docs', menuId: 'api-docs', module: 'ApiCodeGenerator', method: 'render', params: { role: 'merchant', view: 'docs' }, title: '接口文档' },
            
            // 财务管理
            { path: '/finance-flow', menuId: 'finance-flow', module: 'FinanceModule', method: 'render', params: { role: 'merchant', activeTab: 'flow' }, title: '资金流水' },
            { path: '/settlement-mgmt', menuId: 'settlement-mgmt', module: 'FinanceModule', method: 'render', params: { role: 'merchant', activeTab: 'settlement' }, title: '结算管理' },
            { path: '/finance-reports', menuId: 'finance-reports', module: 'FinanceModule', method: 'render', params: { role: 'merchant', activeTab: 'reports' }, title: '财务报表' },
            { path: '/transaction-flow', menuId: 'transaction-flow', module: 'OrderModule', method: 'render', params: { role: 'merchant' }, title: '交易流水' },
            
            // 系统设置
            { path: '/config', menuId: 'config', module: 'ConfigModule', method: 'render', params: { role: 'merchant' }, title: '系统配置' }
        ]);

        console.log('📋 路由配置初始化完成，共配置', this.routeConfig.size, '个租户类型的路由');
    }

    /**
     * 添加路由组
     * @param {string} tenantType - 租户类型
     * @param {Array} routes - 路由配置数组
     */
    addRouteGroup(tenantType, routes) {
        if (!this.routeConfig.has(tenantType)) {
            this.routeConfig.set(tenantType, new Map());
        }
        
        const tenantRoutes = this.routeConfig.get(tenantType);
        routes.forEach(route => {
            tenantRoutes.set(route.path, route);
        });
        
        console.log(`📝 添加 ${tenantType} 路由组，包含 ${routes.length} 个路由`);
    }

    /**
     * 设置依赖管理器
     * @param {Object} managers - 管理器对象
     */
    setManagers(managers) {
        this.moduleLoader = managers.moduleLoader;
        this.uiManager = managers.uiManager;
        this.authManager = managers.authManager;
        this.apiClient = managers.apiClient || window.AdminApiClient || window.apiClient;
        this.utils = managers.utils || window.utils;
        console.log('🔗 RouterManager 依赖管理器设置完成', {
            hasApiClient: !!this.apiClient,
            hasUtils: !!this.utils,
            hasAuthManager: !!this.authManager,
            hasModuleLoader: !!this.moduleLoader
        });
    }

    /**
     * 绑定浏览器历史记录事件
     */
    bindHistoryEvents() {
        window.addEventListener('popstate', (event) => {
            if (event.state && event.state.route) {
                this.navigateToRoute(event.state.route, false);
            }
        });
    }

    /**
     * 导航到指定路由
     * @param {string} path - 路由路径
     * @param {boolean} addToHistory - 是否添加到历史记录
     * @param {Object} options - 导航选项
     */
    async navigateToRoute(path, addToHistory = true, options = {}) {
        try {
            console.log('🧭 导航到路由:', path);

            // 获取当前用户信息
            const user = this.authManager?.getUser();
            const tenantType = this.getTenantType();

            if (!tenantType) {
                throw new Error('无法获取租户类型');
            }

            // 获取路由配置
            const route = this.getRoute(tenantType, path);
            if (!route) {
                throw new Error(`路由 ${path} 不存在或无权限访问`);
            }

            // 权限检查
            if (!this.checkRoutePermission(route, user, tenantType)) {
                throw new Error(`无权限访问路由 ${path}`);
            }

            // 更新浏览器历史记录
            if (addToHistory) {
                const url = `#${path}`;
                history.pushState({ route: path }, route.title, url);
            }

            // 更新当前路由
            this.currentRoute = route;
            this.routeHistory.push({
                path,
                timestamp: Date.now(),
                title: route.title
            });

            // 更新页面标题
            document.title = `${route.title} - PayPal Admin`;

            // 更新菜单状态
            this.updateMenuState(route.menuId);

            // 加载并渲染页面
            await this.renderRoute(route, options);

            console.log('✅ 路由导航成功:', path);

        } catch (error) {
            console.error('❌ 路由导航失败:', error);
            this.handleNavigationError(error, path);
        }
    }

    /**
     * 通过菜单ID导航
     * @param {string} menuId - 菜单项ID
     * @param {Object} options - 导航选项
     */
    async navigateByMenuId(menuId, options = {}) {
        const tenantType = this.getTenantType();
        const route = this.getRouteByMenuId(tenantType, menuId);
        
        if (route) {
            await this.navigateToRoute(route.path, true, options);
        } else {
            console.error('❌ 找不到菜单ID对应的路由:', menuId);
        }
    }

    /**
     * 获取路由配置
     * @param {string} tenantType - 租户类型
     * @param {string} path - 路由路径
     * @returns {Object|null} 路由配置
     */
    getRoute(tenantType, path) {
        const tenantRoutes = this.routeConfig.get(tenantType);
        return tenantRoutes ? tenantRoutes.get(path) : null;
    }

    /**
     * 通过菜单ID获取路由
     * @param {string} tenantType - 租户类型
     * @param {string} menuId - 菜单ID
     * @returns {Object|null} 路由配置
     */
    getRouteByMenuId(tenantType, menuId) {
        const tenantRoutes = this.routeConfig.get(tenantType);
        if (!tenantRoutes) return null;

        for (const route of tenantRoutes.values()) {
            if (route.menuId === menuId) {
                return route;
            }
        }
        return null;
    }

    /**
     * 获取当前租户类型
     * @returns {string} 租户类型
     */
    getTenantType() {
        // 从UIManager获取租户信息
        if (this.uiManager && this.uiManager.tenantInfo) {
            return this.uiManager.tenantInfo.tenant_type;
        }
        
        // 从AuthManager获取用户信息
        if (this.authManager) {
            const user = this.authManager.getUser();
            if (user && user.tenant_type) {
                return user.tenant_type;
            }
        }
        
        // 默认返回provider
        return 'provider';
    }

    /**
     * 检查路由权限
     * @param {Object} route - 路由配置
     * @param {Object} user - 用户信息
     * @param {string} tenantType - 租户类型
     * @returns {boolean} 是否有权限
     */
    checkRoutePermission(route, user, tenantType) {
        // 基本租户类型检查
        const tenantRoutes = this.routeConfig.get(tenantType);
        if (!tenantRoutes || !tenantRoutes.has(route.path)) {
            return false;
        }

        // 如果路由有特定权限要求
        if (route.permissions && route.permissions.length > 0) {
            if (!user || !user.permissions) {
                return false;
            }
            
            return route.permissions.some(permission => 
                user.permissions.includes(permission)
            );
        }

        // 员工权限检查
        if (user && user.is_staff && route.staffOnly === false) {
            return false;
        }

        return true;
    }

    /**
     * 更新菜单状态
     * @param {string} activeMenuId - 激活的菜单ID
     */
    updateMenuState(activeMenuId) {
        // 移除所有菜单项的激活状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });

        // 激活当前菜单项
        const activeMenuItem = document.querySelector(`[data-page="${activeMenuId}"]`);
        if (activeMenuItem) {
            activeMenuItem.classList.add('active');
        }

        // 更新页面标题
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle && this.currentRoute) {
            pageTitle.textContent = this.currentRoute.title;
        }
    }

    /**
     * 渲染路由页面 - 方案5配置驱动
     * @param {Object} route - 路由配置
     * @param {Object} options - 渲染选项
     */
    async renderRoute(route, options = {}) {
        // 尝试多种可能的内容区域容器
        let contentArea = document.getElementById('contentArea') ||
                         document.querySelector('.content-area') ||
                         document.querySelector('.main-content') ||
                         document.getElementById('main-content') ||
                         document.getElementById('mainContent') ||
                         document.querySelector('#content-wrapper') ||
                         document.querySelector('.content-wrapper');
                         
        if (!contentArea) {
            // 如果还是找不到，尝试创建一个内容区域
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                // 清除现有内容区域（保留顶部栏）
                const topBar = mainContent.querySelector('.top-bar');
                const existingContent = mainContent.querySelectorAll('.content-area, .welcome-card, .stats-grid, .device-management, .script-management, .management-page');
                existingContent.forEach(element => element.remove());
                
                // 创建新的内容区域
                contentArea = document.createElement('div');
                contentArea.className = 'content-area';
                contentArea.id = 'contentArea'; // 同时设置ID以便后续查找
                
                // 在顶部栏后面插入内容区域
                if (topBar && topBar.nextSibling) {
                    mainContent.insertBefore(contentArea, topBar.nextSibling);
                } else {
                    mainContent.appendChild(contentArea);
                }
            } else {
                throw new Error('找不到内容区域元素');
            }
        }

        // 显示加载状态
        this.showLoadingState(contentArea);

        try {
            console.log('📦 加载模块:', route.module);

            // 方案5配置驱动：检查是否为新模块架构
            if (route.method && route.params) {
                // 新模块架构：直接调用模块的render方法
                let module = await this.moduleLoader.loadModule(route.module);
                
                if (!module) {
                    throw new Error(`模块 ${route.module} 加载失败`);
                }

                // 特殊处理API子模块：如果是类，需要实例化
                const apiModules = ['ApiKeyManager', 'IpWhitelistManager', 'ApiTester', 'ApiStatsManager', 'ApiCodeGenerator', 'ApiSignatureTool'];
                if (apiModules.includes(route.module) && typeof module === 'function') {
                    // 这是一个类，需要实例化（无参数构造函数）
                    module = new module();
                    console.log(`✅ API子模块实例化成功: ${route.module}`);
                }

                // 🔧 关键修复：在调用render方法之前先初始化模块
                if (typeof module.init === 'function') {
                    const dependencies = {
                        apiClient: this.apiClient,
                        utils: this.utils,
                        authManager: this.authManager,
                        tenantInfo: this.uiManager?.tenantInfo || window.TENANT_INFO
                    };
                    console.log('🔧 初始化模块依赖:', route.module, dependencies);
                    await module.init(dependencies);
                }

                // 检查模块是否有指定的方法
                if (typeof module[route.method] !== 'function') {
                    throw new Error(`模块 ${route.module} 没有 ${route.method} 方法`);
                }

                // 合并路由参数和选项
                const renderParams = { ...route.params, ...options };
                
                console.log('🎯 方案5配置驱动 - 调用:', `${route.module}.${route.method}`, renderParams);
                
                // 调用模块方法渲染内容
                await module[route.method](contentArea, renderParams);
                
                console.log('✅ 方案5路由渲染完成:', route.title);
                return;
            }

            // 传统组件架构兼容处理
            const module = await this.moduleLoader.loadModule(route.module);

            if (!module) {
                throw new Error(`模块 ${route.module} 加载失败`);
            }

            // 渲染组件
            let content = '';
            const componentName = route.component;

            // 尝试不同的组件获取方式
            if (typeof module[componentName] === 'function') {
                // 如果是构造函数
                const componentInstance = new module[componentName]();
                if (typeof componentInstance.render === 'function') {
                    content = await componentInstance.render(options);
                }
            } else if (typeof module.render === 'function') {
                // 如果模块有render方法
                content = await module.render(componentName, options);
            } else if (typeof window[componentName] !== 'undefined') {
                // 如果是全局组件
                const globalComponent = window[componentName];
                if (typeof globalComponent.render === 'function') {
                    content = await globalComponent.render(options);
                }
            }

            // 如果没有获取到内容，显示默认页面
            if (!content) {
                content = this.getDefaultPageContent(route);
            }

            // 渲染内容
            contentArea.innerHTML = content;

            // 初始化页面
            await this.initializePage(route, module, options);

        } catch (error) {
            console.error('❌ 路由渲染失败:', error);
            contentArea.innerHTML = this.getErrorPageContent(error, route);
        }
    }

    /**
     * 显示加载状态
     * @param {HTMLElement} container - 容器元素
     */
    showLoadingState(container) {
        container.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-3">
                        <h5>加载中...</h5>
                        <p class="text-muted">正在加载页面内容</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取默认页面内容
     * @param {Object} route - 路由配置
     * @returns {string} HTML内容
     */
    getDefaultPageContent(route) {
        return `
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title mb-0">
                                    <i class="bi bi-gear me-2"></i>${route.title}
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    ${route.title} 功能正在开发中，敬请期待...
                                </div>
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <h6>模块信息</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>路由路径:</strong> ${route.path}</li>
                                            <li><strong>模块名称:</strong> ${route.module}</li>
                                            <li><strong>组件名称:</strong> ${route.component}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取错误页面内容
     * @param {Error} error - 错误对象
     * @param {Object} route - 路由配置
     * @returns {string} HTML内容
     */
    getErrorPageContent(error, route) {
        return `
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h4 class="card-title mb-0">
                                    <i class="bi bi-exclamation-triangle me-2"></i>页面加载失败
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-danger">
                                    <strong>错误信息:</strong> ${error.message}
                                </div>
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <h6>路由信息</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>页面标题:</strong> ${route.title}</li>
                                            <li><strong>路由路径:</strong> ${route.path}</li>
                                            <li><strong>模块名称:</strong> ${route.module}</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <button class="btn btn-primary" onclick="location.reload()">
                                        <i class="bi bi-arrow-clockwise me-2"></i>重新加载
                                    </button>
                                    <button class="btn btn-secondary ms-2" onclick="window.routerManager.navigateToRoute('/dashboard')">
                                        <i class="bi bi-house me-2"></i>返回首页
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 初始化页面
     * @param {Object} route - 路由配置
     * @param {Object} module - 模块对象
     * @param {Object} options - 选项
     */
    async initializePage(route, module, options) {
        try {
            // 调用模块的初始化方法
            if (module && typeof module.init === 'function') {
                await module.init(options);
            }

            // 调用全局组件的初始化方法
            const componentName = route.component;
            if (typeof window[componentName] !== 'undefined') {
                const globalComponent = window[componentName];
                if (typeof globalComponent.init === 'function') {
                    await globalComponent.init(options);
                }
            }

            console.log('✅ 页面初始化完成:', route.title);

        } catch (error) {
            console.error('❌ 页面初始化失败:', error);
        }
    }

    /**
     * 处理导航错误
     * @param {Error} error - 错误对象
     * @param {string} path - 路由路径
     */
    handleNavigationError(error, path) {
        console.error('路由导航错误:', error);

        // 显示错误提示
        if (typeof window.showAlert === 'function') {
            window.showAlert('页面加载失败: ' + error.message, 'error');
        }

        // 如果不是首页，尝试导航到首页
        if (path !== '/dashboard') {
            console.log('尝试导航到首页...');
            setTimeout(() => {
                this.navigateToRoute('/dashboard');
            }, 2000);
        }
    }

    /**
     * 获取当前路由
     * @returns {Object|null} 当前路由配置
     */
    getCurrentRoute() {
        return this.currentRoute;
    }

    /**
     * 获取路由历史
     * @returns {Array} 路由历史数组
     */
    getRouteHistory() {
        return [...this.routeHistory];
    }

    /**
     * 清除路由历史
     */
    clearRouteHistory() {
        this.routeHistory = [];
    }

    /**
     * 获取可用路由列表
     * @param {string} tenantType - 租户类型
     * @returns {Array} 路由列表
     */
    getAvailableRoutes(tenantType) {
        const tenantRoutes = this.routeConfig.get(tenantType);
        if (!tenantRoutes) return [];

        return Array.from(tenantRoutes.values());
    }

    /**
     * 初始化路由系统
     * 从URL hash或默认路由开始
     */
    async initialize() {
        console.log('🚀 初始化路由系统...');

        // 从URL hash获取初始路由
        let initialPath = '/dashboard';
        if (window.location.hash) {
            initialPath = window.location.hash.substring(1);
        }

        // 导航到初始路由
        await this.navigateToRoute(initialPath, false);

        console.log('✅ 路由系统初始化完成');
    }
}

// 导出RouterManager
window.RouterManager = RouterManager;

// 创建全局路由管理器实例
if (typeof window.routerManager === 'undefined') {
    window.routerManager = new RouterManager();
}

console.log('🎯 RouterManager 模块加载完成'); 