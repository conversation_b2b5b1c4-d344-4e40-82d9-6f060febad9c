/**
 * 智能模块加载器 - Phase 6 重构版本
 * 实现懒加载、预加载、权限控制和性能优化
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @created 2024年12月25日
 */

class SmartModuleLoader {
    constructor() {
        this.loadedModules = new Map();
        this.moduleCache = new Map();
        this.loadingPromises = new Map();
        this.preloadQueue = new Set();
        this.userBehavior = new Map();
        this.baseUrl = '/js/modules/';
        
        // 加载策略配置
        this.loadingStrategy = {
            // 核心模块：立即加载
            immediate: ['utils', 'auth', 'ui-manager', 'api-client'],
            // 业务模块：按需加载
            onDemand: ['device-management', 'finance-management', 'merchant-management'],
            // 工具模块：延迟加载
            lazy: ['analytics', 'reports', 'export-tools']
        };
        
        // 模块权限映射
        this.modulePermissions = {
            'device-management': ['admin', 'provider', 'employee'],
            'finance-management': ['admin', 'provider'],
            'merchant-management': ['admin', 'merchant'],
            'system-monitoring': ['admin'],
            'security-management': ['admin', 'provider'],
            'analytics': ['admin', 'provider', 'merchant']
        };
        
        // 性能监控
        this.performanceMetrics = {
            loadTimes: new Map(),
            cacheHits: 0,
            cacheMisses: 0,
            totalLoadTime: 0,
            loadCount: 0
        };
        
        // 用户行为分析
        this.behaviorAnalyzer = {
            visitHistory: [],
            moduleUsage: new Map(),
            lastVisitTime: new Map(),
            sessionDuration: new Map()
        };
        
        console.log('🚀 SmartModuleLoader v2.0 initialized');
        this.initializeBehaviorTracking();
    }

    /**
     * 智能模块加载 - 主入口
     */
    async loadModule(moduleName, options = {}) {
        const startTime = performance.now();
        
        try {
            // 权限检查
            if (!this.hasPermission(moduleName)) {
                throw new Error(`无权限访问模块: ${moduleName}`);
            }
            
            // 记录用户行为
            this.recordBehavior(moduleName);
            
            // 检查缓存
            if (this.moduleCache.has(moduleName) && !options.forceReload) {
                this.performanceMetrics.cacheHits++;
                console.log(`📦 模块 ${moduleName} 从缓存加载`);
                return this.moduleCache.get(moduleName);
            }
            
            // 检查是否正在加载
            if (this.loadingPromises.has(moduleName)) {
                console.log(`⏳ 模块 ${moduleName} 正在加载中，等待完成...`);
                return await this.loadingPromises.get(moduleName);
            }
            
            // 开始加载
            const loadPromise = this.loadModuleInternal(moduleName, options);
            this.loadingPromises.set(moduleName, loadPromise);
            
            const module = await loadPromise;
            
            // 清理加载状态
            this.loadingPromises.delete(moduleName);
            
            // 记录性能指标
            const loadTime = performance.now() - startTime;
            this.recordPerformance(moduleName, loadTime);
            
            // 触发预加载
            this.triggerPredictivePreload(moduleName);
            
            console.log(`✅ 模块 ${moduleName} 加载完成 (${loadTime.toFixed(2)}ms)`);
            return module;
            
        } catch (error) {
            this.loadingPromises.delete(moduleName);
            console.error(`❌ 模块 ${moduleName} 加载失败:`, error);
            throw error;
        }
    }

    /**
     * 内部模块加载逻辑
     */
    async loadModuleInternal(moduleName, options) {
        this.performanceMetrics.cacheMisses++;
        
        // 加载依赖
        await this.loadDependencies(moduleName);
        
        // 加载模块文件
        const module = await this.loadModuleFile(moduleName);
        
        // 缓存模块
        this.moduleCache.set(moduleName, module);
        this.loadedModules.set(moduleName, {
            module,
            loadTime: Date.now(),
            usage: 0
        });
        
        // 初始化模块
        if (module && typeof module.initialize === 'function') {
            await module.initialize();
        }
        
        return module;
    }

    /**
     * 权限检查
     */
    hasPermission(moduleName) {
        const user = this.getCurrentUser();
        if (!user) return false;
        
        const requiredPermissions = this.modulePermissions[moduleName];
        if (!requiredPermissions) return true; // 无权限要求的模块
        
        return requiredPermissions.includes(user.user_type);
    }

    /**
     * 获取当前用户
     */
    getCurrentUser() {
        // 从全局状态或localStorage获取用户信息
        if (window.GlobalState && window.GlobalState.user) {
            return window.GlobalState.user;
        }
        
        const userStr = localStorage.getItem('user_info');
        return userStr ? JSON.parse(userStr) : null;
    }

    /**
     * 加载模块依赖
     */
    async loadDependencies(moduleName) {
        const config = this.getModuleConfig(moduleName);
        if (!config || !config.dependencies) return;
        
        const dependencyPromises = config.dependencies.map(dep => 
            this.loadModule(dep, { isDependency: true })
        );
        
        await Promise.all(dependencyPromises);
    }

    /**
     * 加载模块文件
     */
    async loadModuleFile(moduleName) {
        const config = this.getModuleConfig(moduleName);
        if (!config) {
            throw new Error(`模块配置未找到: ${moduleName}`);
        }
        
        const url = this.baseUrl + config.path;
        
        return new Promise((resolve, reject) => {
            // 检查是否已经通过script标签加载
            const globalName = config.globalName;
            if (globalName && window[globalName]) {
                resolve(window[globalName]);
                return;
            }
            
            const script = document.createElement('script');
            script.src = url;
            script.async = true;
            
            script.onload = () => {
                // 获取模块实例
                const module = globalName ? window[globalName] : null;
                resolve(module);
            };
            
            script.onerror = () => {
                reject(new Error(`模块文件加载失败: ${url}`));
            };
            
            document.head.appendChild(script);
        });
    }

    /**
     * 预测性预加载
     */
    async triggerPredictivePreload(currentModule) {
        const predictedModules = this.predictNextModules(currentModule);
        
        for (const moduleName of predictedModules) {
            if (!this.moduleCache.has(moduleName) && 
                !this.loadingPromises.has(moduleName) &&
                this.hasPermission(moduleName)) {
                
                this.preloadQueue.add(moduleName);
            }
        }
        
        // 异步预加载
        this.processPreloadQueue();
    }

    /**
     * 预测下一个可能访问的模块
     */
    predictNextModules(currentModule) {
        const predictions = [];
        
        // 基于用户历史行为预测
        const history = this.behaviorAnalyzer.visitHistory;
        const currentIndex = history.lastIndexOf(currentModule);
        
        if (currentIndex >= 0 && currentIndex < history.length - 1) {
            const nextModule = history[currentIndex + 1];
            predictions.push(nextModule);
        }
        
        // 基于模块关联性预测
        const relatedModules = this.getRelatedModules(currentModule);
        predictions.push(...relatedModules);
        
        // 基于使用频率预测
        const frequentModules = this.getFrequentModules();
        predictions.push(...frequentModules.slice(0, 2));
        
        return [...new Set(predictions)]; // 去重
    }

    /**
     * 获取相关模块
     */
    getRelatedModules(moduleName) {
        const relations = {
            'device-management': ['device-analytics', 'device-config-manager'],
            'finance-management': ['finance-report-manager', 'reconciliation-manager'],
            'merchant-management': ['merchant-tools', 'api-management'],

        };
        
        return relations[moduleName] || [];
    }

    /**
     * 获取高频使用模块
     */
    getFrequentModules() {
        const usage = Array.from(this.behaviorAnalyzer.moduleUsage.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([module]) => module);
        
        return usage;
    }

    /**
     * 处理预加载队列
     */
    async processPreloadQueue() {
        if (this.preloadQueue.size === 0) return;
        
        // 限制并发预加载数量
        const maxConcurrent = 2;
        const modules = Array.from(this.preloadQueue).slice(0, maxConcurrent);
        
        for (const moduleName of modules) {
            this.preloadQueue.delete(moduleName);
            
            // 低优先级预加载
            setTimeout(async () => {
                try {
                    await this.loadModule(moduleName, { isPreload: true });
                    console.log(`🔮 预加载完成: ${moduleName}`);
                } catch (error) {
                    console.warn(`预加载失败: ${moduleName}`, error);
                }
            }, 100);
        }
    }

    /**
     * 批量加载模块
     */
    async loadModules(moduleNames, options = {}) {
        const loadPromises = moduleNames.map(name => this.loadModule(name, options));
        return await Promise.allSettled(loadPromises);
    }

    /**
     * 卸载模块
     */
    unloadModule(moduleName) {
        if (this.loadedModules.has(moduleName)) {
            const moduleInfo = this.loadedModules.get(moduleName);
            
            // 调用模块的清理方法
            if (moduleInfo.module && typeof moduleInfo.module.destroy === 'function') {
                moduleInfo.module.destroy();
            }
            
            // 清理缓存
            this.loadedModules.delete(moduleName);
            this.moduleCache.delete(moduleName);
            
            console.log(`🗑️ 模块 ${moduleName} 已卸载`);
        }
    }

    /**
     * 记录用户行为
     */
    recordBehavior(moduleName) {
        const now = Date.now();
        
        // 记录访问历史
        this.behaviorAnalyzer.visitHistory.push(moduleName);
        if (this.behaviorAnalyzer.visitHistory.length > 50) {
            this.behaviorAnalyzer.visitHistory.shift();
        }
        
        // 记录使用次数
        const currentUsage = this.behaviorAnalyzer.moduleUsage.get(moduleName) || 0;
        this.behaviorAnalyzer.moduleUsage.set(moduleName, currentUsage + 1);
        
        // 记录访问时间
        const lastVisit = this.behaviorAnalyzer.lastVisitTime.get(moduleName);
        if (lastVisit) {
            const duration = now - lastVisit;
            const currentDuration = this.behaviorAnalyzer.sessionDuration.get(moduleName) || 0;
            this.behaviorAnalyzer.sessionDuration.set(moduleName, currentDuration + duration);
        }
        this.behaviorAnalyzer.lastVisitTime.set(moduleName, now);
    }

    /**
     * 记录性能指标
     */
    recordPerformance(moduleName, loadTime) {
        this.performanceMetrics.loadTimes.set(moduleName, loadTime);
        this.performanceMetrics.totalLoadTime += loadTime;
        this.performanceMetrics.loadCount++;
    }

    /**
     * 初始化行为追踪
     */
    initializeBehaviorTracking() {
        // 页面卸载时保存行为数据
        window.addEventListener('beforeunload', () => {
            this.saveBehaviorData();
        });
        
        // 加载历史行为数据
        this.loadBehaviorData();
    }

    /**
     * 保存行为数据
     */
    saveBehaviorData() {
        const data = {
            visitHistory: this.behaviorAnalyzer.visitHistory.slice(-20), // 只保存最近20条
            moduleUsage: Object.fromEntries(this.behaviorAnalyzer.moduleUsage),
            sessionDuration: Object.fromEntries(this.behaviorAnalyzer.sessionDuration)
        };
        
        localStorage.setItem('module_behavior_data', JSON.stringify(data));
    }

    /**
     * 加载行为数据
     */
    loadBehaviorData() {
        try {
            const data = localStorage.getItem('module_behavior_data');
            if (data) {
                const parsed = JSON.parse(data);
                this.behaviorAnalyzer.visitHistory = parsed.visitHistory || [];
                this.behaviorAnalyzer.moduleUsage = new Map(Object.entries(parsed.moduleUsage || {}));
                this.behaviorAnalyzer.sessionDuration = new Map(Object.entries(parsed.sessionDuration || {}));
            }
        } catch (error) {
            console.warn('加载行为数据失败:', error);
        }
    }

    /**
     * 获取模块配置
     */
    getModuleConfig(moduleName) {
        // 这里应该从配置文件或API获取模块配置
        // 暂时使用硬编码配置
        const configs = {
            'device-management': {
                path: 'device/management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                globalName: 'DeviceManager'
            },
            'device-analytics': {
                path: 'device/device-analytics.js',
                dependencies: ['device-management'],
                globalName: 'DeviceAnalytics'
            },
            'device-config-manager': {
                path: 'device/device-config-manager.js',
                dependencies: ['device-management'],
                globalName: 'DeviceConfigManager'
            },
            'finance-management': {
                path: 'finance/finance-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                globalName: 'FinanceManager'
            },
            'merchant-management': {
                path: 'merchant/merchant-management.js',
                dependencies: ['auth', 'utils', 'api-client'],
                globalName: 'MerchantManager'
            }
        };
        
        return configs[moduleName];
    }

    /**
     * 获取性能指标
     */
    getPerformanceMetrics() {
        const avgLoadTime = this.performanceMetrics.loadCount > 0 ? 
            this.performanceMetrics.totalLoadTime / this.performanceMetrics.loadCount : 0;
        
        return {
            ...this.performanceMetrics,
            averageLoadTime: avgLoadTime,
            cacheHitRate: this.performanceMetrics.cacheHits / 
                (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) * 100
        };
    }

    /**
     * 获取调试信息
     */
    getDebugInfo() {
        return {
            loadedModules: Array.from(this.loadedModules.keys()),
            cachedModules: Array.from(this.moduleCache.keys()),
            preloadQueue: Array.from(this.preloadQueue),
            behaviorData: {
                visitHistory: this.behaviorAnalyzer.visitHistory.slice(-10),
                topModules: this.getFrequentModules()
            },
            performance: this.getPerformanceMetrics()
        };
    }

    /**
     * 清理缓存
     */
    clearCache(force = false) {
        if (force) {
            this.moduleCache.clear();
            this.loadedModules.clear();
            console.log('🧹 强制清理所有模块缓存');
        } else {
            // 只清理长时间未使用的模块
            const now = Date.now();
            const maxIdleTime = 30 * 60 * 1000; // 30分钟
            
            for (const [moduleName, info] of this.loadedModules.entries()) {
                if (now - info.loadTime > maxIdleTime && info.usage === 0) {
                    this.unloadModule(moduleName);
                }
            }
        }
    }
}

// 创建全局实例
if (typeof window !== 'undefined') {
    window.SmartModuleLoader = SmartModuleLoader;
    window.smartModuleLoader = new SmartModuleLoader();
    
    console.log('🎯 智能模块加载器已就绪');
} 