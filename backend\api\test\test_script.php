<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 测试基本功能
echo json_encode([
    'success' => true,
    'message' => '测试成功',
    'data' => [
        'php_version' => phpversion(),
        'request_method' => $_SERVER['REQUEST_METHOD'],
        'get_params' => $_GET,
        'headers' => getallheaders()
    ]
], JSON_UNESCAPED_UNICODE);
?> 