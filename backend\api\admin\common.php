<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';
require_once dirname(dirname(__FILE__)) . '/utils/ErrorHandler.php';

// 设置全局异常处理器
ErrorHandler::setGlobalExceptionHandler();

$db = new Database();
$auth = new Auth();

// 获取Authorization头（兼容FastCGI）
if (!function_exists('getAllHeaders')) {
    function getAllHeaders() {
        $headers = array();
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) == 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }
        return $headers;
    }
}

$headers = getAllHeaders();
$token = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = str_replace('Bearer ', '', $token);

// 调试信息已移除

// 对于登录接口，跳过token验证
$skipAuthActions = array('login', 'logout');
$currentAction = isset($_GET['action']) ? $_GET['action'] : (isset($_GET['do']) ? $_GET['do'] : '');

if (in_array($currentAction, $skipAuthActions)) {
    $currentUser = null;
} else {
    // 验证用户身份
    $currentUser = $auth->getCurrentUser($token);
    
    // 如果没有用户信息（无token或token无效），返回401
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(array(
            'code' => 401, 
            'message' => '未授权访问，请先登录'
        ));
        exit;
    }
    
    // 如果有有效用户，检查权限：系统管理员、平台管理员、员工、商户、码商可以访问管理功能
    if (!in_array($currentUser['user_type'], array('system_admin', 'platform_admin', 'employee', 'merchant', 'provider'))) {
        http_response_code(403);
        echo json_encode(array(
            'code' => 403, 
            'message' => '权限不足，无法访问管理功能'
        ));
        exit;
    }
}

// 记录安全事件
function logSecurityEvent($userId, $eventType, $eventData = array()) {
    global $db;
    
    $db->execute("INSERT INTO security_logs (user_id, event_type, event_data, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, NOW())", array(
        $userId,
        $eventType,
        json_encode($eventData),
        $_SERVER['REMOTE_ADDR'],
        $_SERVER['HTTP_USER_AGENT']
    ));
}

// 检查权限
function checkPermission($permission) {
    global $currentUser, $auth;
    
    if (!$currentUser) {
        return false;
    }
    
    // 系统管理员拥有所有权限
    if ($currentUser['user_type'] === 'system_admin') {
        return true;
    }
    
    // 使用Auth类的hasPermission方法，这样可以利用预定义的权限
    return $auth->hasPermission($currentUser, $permission);
}

// 验证输入参数
function validateInput($input, $required = []) {
    if (!$input) {
        return '请求数据不能为空';
    }
    
    foreach ($required as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            return "参数 {$field} 不能为空";
        }
    }
    
    return null;
}

// 分页处理
function getPaginationParams() {
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    return array(
        'page' => $page,
        'limit' => $limit,
        'offset' => $offset
    );
}

// 构建分页响应
function buildPaginatedResponse($data, $total, $page, $limit) {
    return array(
        'code' => 200,
        'message' => '获取成功',
        'data' => $data,
        'pagination' => array(
            'current_page' => $page,
            'per_page' => $limit,
            'total' => $total,
            'total_pages' => ceil($total / $limit)
        )
    );
}

// ================================
// 四层架构多租户功能
// ================================

/**
 * 解析域名配置
 * @param string $domain 域名
 * @return array 解析结果
 */
function resolveDomainConfig($domain) {
    global $db;
    
    try {
        $stmt = $db->query("
            SELECT 
                dc.tenant_type, 
                dc.tenant_id, 
                dc.custom_config,
                dc.status,
                dc.ssl_enabled,
                dc.is_custom
            FROM domain_configs dc
            WHERE dc.domain = ? AND dc.status = 'active'
        ", array($domain));
        
        $config = $stmt->fetch();
        
        if ($config) {
            // 解析JSON字段
            $config['custom_config'] = json_decode($config['custom_config'], true) ?: array();
            
            // 根据租户类型获取关联的平台信息
            $platformInfo = getPlatformInfoByTenant($config['tenant_type'], $config['tenant_id']);
            if ($platformInfo) {
                $config['platform_id'] = $platformInfo['platform_id'];
                $config['platform_name'] = $platformInfo['platform_name'];
                $config['platform_code'] = $platformInfo['platform_code'];
                $config['platform_settings'] = json_decode($platformInfo['settings'], true) ?: array();
            }
            
            return array(
                'error_code' => 0, 
                'message' => '域名解析成功',
                'data' => $config
            );
        }
        
        return array(
            'error_code' => 1, 
            'message' => '域名未配置或已停用',
            'data' => null
        );
        
    } catch (Exception $e) {
        return array(
            'error_code' => 500,
            'message' => '域名解析失败: ' . $e->getMessage(),
            'data' => null
        );
    }
}

/**
 * 获取域名信息（内部使用）
 * @param string $domain 域名
 * @return array|null 域名信息
 */
function getDomainInfo($domain) {
    $result = resolveDomainConfig($domain);
    return ($result['error_code'] === 0) ? $result['data'] : null;
}

/**
 * 根据租户类型和ID获取关联的平台信息
 * @param string $tenantType 租户类型
 * @param int $tenantId 租户ID
 * @return array|null 平台信息
 */
function getPlatformInfoByTenant($tenantType, $tenantId) {
    global $db;
    
    try {
        $platformInfo = null;
        
        switch ($tenantType) {
            case 'platform_admin':
                // 平台管理员直接从platforms表获取
                $platformInfo = $db->fetch("
                    SELECT id as platform_id, platform_name, platform_code, settings 
                    FROM platforms 
                    WHERE id = ?
                ", array($tenantId));
                break;
                
            case 'provider':
                // 码商从payment_providers表关联获取
                $platformInfo = $db->fetch("
                    SELECT p.id as platform_id, p.platform_name, p.platform_code, p.settings 
                    FROM payment_providers pp 
                    JOIN platforms p ON pp.platform_id = p.id 
                    WHERE pp.id = ?
                ", array($tenantId));
                break;
                
            case 'merchant':
                // 商户从merchants表关联获取
                $platformInfo = $db->fetch("
                    SELECT p.id as platform_id, p.platform_name, p.platform_code, p.settings 
                    FROM merchants m 
                    JOIN platforms p ON m.platform_id = p.id 
                    WHERE m.id = ?
                ", array($tenantId));
                break;
                
            case 'system_admin':
            default:
                // 系统管理员不关联具体平台
                return null;
        }
        
        return $platformInfo;
        
    } catch (Exception $e) {
        error_log("getPlatformInfoByTenant - 异常: " . $e->getMessage());
        return null;
    }
}

/**
 * 验证租户访问权限
 * @param array $domainInfo 域名信息
 * @param array $requestData 请求数据
 * @return array 验证结果
 */
function validateTenantAccess($domainInfo, $requestData = array()) {
    if (!$domainInfo) {
        return array('valid' => false, 'message' => '域名未配置');
    }
    
    $tenantType = $domainInfo['tenant_type'];
    $tenantId = $domainInfo['tenant_id'];
    $platformId = $domainInfo['platform_id'];
    
    // 根据租户类型验证权限
    switch ($tenantType) {
        case 'system_admin':
            return array('valid' => true, 'message' => '系统管理员权限'); // 系统管理员无限制
        case 'platform_admin':
            return validatePlatformAccess($platformId, $requestData);
        case 'provider':
            return validateProviderAccess($tenantId, $requestData);
        case 'merchant':
            return validateMerchantAccess($tenantId, $requestData);
    }
    
    return array('valid' => false, 'message' => '权限验证失败');
}

/**
 * 验证平台方访问权限
 * @param int $platformId 平台ID
 * @param array $requestData 请求数据
 * @return array 验证结果
 */
function validatePlatformAccess($platformId, $requestData) {
    // 平台方可以访问自己平台下的所有数据
    return array(
        'valid' => true, 
        'message' => '平台访问权限验证通过',
        'platform_id' => $platformId
    );
}

/**
 * 验证码商访问权限
 * @param int $providerId 码商ID
 * @param array $requestData 请求数据
 * @return array 验证结果
 */
function validateProviderAccess($providerId, $requestData) {
    // 码商只能访问自己的数据
    return array(
        'valid' => true, 
        'message' => '码商访问权限验证通过',
        'provider_id' => $providerId
    );
}

/**
 * 验证商户访问权限
 * @param int $merchantId 商户ID
 * @param array $requestData 请求数据
 * @return array 验证结果
 */
function validateMerchantAccess($merchantId, $requestData) {
    // 商户只能访问自己的数据
    return array(
        'valid' => true, 
        'message' => '商户访问权限验证通过',
        'merchant_id' => $merchantId
    );
}

/**
 * 根据租户信息构建数据过滤条件
 * @param array $domainInfo 域名信息
 * @param string $tableName 表名
 * @return array 过滤条件 array('where' => '...', 'params' => [...])
 */
function buildTenantDataFilter($domainInfo, $tableName = '') {
    if (!$domainInfo) {
        return array('where' => '', 'params' => array());
    }
    
    $tenantType = $domainInfo['tenant_type'];
    $tenantId = $domainInfo['tenant_id'];
    $platformId = $domainInfo['platform_id'];
    
    switch ($tenantType) {
        case 'system_admin':
            // 系统管理员可以看到所有数据
            return array('where' => '', 'params' => array());
            
        case 'platform_admin':
            // 平台方只能看到自己平台的数据
            if (in_array($tableName, array('users', 'payment_providers', 'merchants'))) {
                return array(
                    'where' => " AND {$tableName}.platform_id = ?",
                    'params' => array($platformId)
                );
            }
            break;
            
        case 'provider':
            // 码商只能看到自己的数据
            if ($tableName === 'devices') {
                return array(
                    'where' => " AND {$tableName}.provider_id = ?",
                    'params' => array($tenantId)
                );
            }
            break;
            
        case 'merchant':
            // 商户只能看到自己的数据
            if ($tableName === 'orders') {
                return array(
                    'where' => " AND {$tableName}.merchant_id = ?",
                    'params' => array($tenantId)
                );
            }
            break;
    }
    
    return array('where' => '', 'params' => array());
}

/**
 * 检查是否为保留域名
 * @param string $domainPrefix 域名前缀
 * @return boolean 是否保留
 */
function isReservedDomain($domainPrefix) {
    global $db;
    
    $stmt = $db->query(
        "SELECT COUNT(*) as count FROM reserved_domains WHERE domain_prefix = ?",
        array($domainPrefix)
    );
    $result = $stmt->fetch();
    
    return $result['count'] > 0;
}
?> 