<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Token认证测试 ===\n";

try {
    // 1. 首先测试基础的认证组件
    echo "1. 包含认证文件...\n";
    require_once dirname(__FILE__) . '/../utils/TenantAuth.php';
    require_once dirname(__FILE__) . '/../core/TenantManager.php';
    echo "   认证文件包含成功\n";
    
    // 2. 创建认证对象
    echo "2. 创建认证对象...\n";
    $tenantAuth = new TenantAuth();
    $tenantManager = new TenantManager();
    echo "   认证对象创建成功\n";
    
    // 3. 测试模拟登录获取真实token
    echo "3. 测试获取真实token...\n";
    $loginResult = $tenantAuth->tenantLogin('provider_user', 'test123');
    echo "   登录结果: " . ($loginResult['success'] ? '成功' : '失败') . "\n";
    
    if ($loginResult['success']) {
        $realToken = $loginResult['token'];
        echo "   获得真实token: " . substr($realToken, 0, 50) . "...\n";
        
        // 4. 测试token验证
        echo "4. 测试token验证...\n";
        
        // 模拟POST数据中的token
        $_POST['token'] = $realToken;
        echo "   设置POST['token']: " . substr($_POST['token'], 0, 50) . "...\n";
        
        // 测试getAuthToken方法
        echo "5. 测试getAuthToken()...\n";
        $retrievedToken = $tenantAuth->getAuthToken();
        echo "   获取的token: " . ($retrievedToken ? substr($retrievedToken, 0, 50) . "..." : 'null') . "\n";
        
        // 测试getCurrentTenantUser方法
        echo "6. 测试getCurrentTenantUser()...\n";
        $user = $tenantAuth->getCurrentTenantUser($retrievedToken);
        
        if ($user) {
            echo "   用户验证成功\n";
            echo "   用户信息: ID={$user['id']}, 用户名={$user['username']}, 类型={$user['user_type']}\n";
            
            // 7. 现在用真实的认证数据测试API
            echo "7. 测试完整的API调用...\n";
            
            // 模拟真实的API请求
            $_POST['action'] = 'provider_dashboard_stats';
            $_SERVER['REQUEST_METHOD'] = 'POST';
            
            // 包含ProviderAPI
            require_once dirname(__FILE__) . '/tenant/provider_api.php';
            $providerAPI = new ProviderAPI($tenantAuth, $tenantManager);
            
            echo "   调用ProviderAPI::handleAction...\n";
            $result = $providerAPI->handleAction('provider_dashboard_stats', 'POST', $user);
            
            echo "8. API调用成功！\n";
            echo "   结果: " . json_encode($result) . "\n";
            
        } else {
            echo "   用户验证失败\n";
            
            // 详细分析token验证失败的原因
            echo "7. 分析token验证失败原因...\n";
            
            echo "   尝试手动验证token...\n";
            $tokenValidation = $tenantAuth->verifyTenantToken($retrievedToken);
            echo "   verifyTenantToken结果: " . ($tokenValidation ? '有效' : '无效') . "\n";
            
            if ($tokenValidation) {
                echo "   Token payload: " . print_r($tokenValidation, true) . "\n";
            }
        }
        
    } else {
        echo "   登录失败原因: " . $loginResult['message'] . "\n";
        echo "   无法获取真实token进行测试\n";
    }
    
} catch (ParseError $e) {
    echo "PHP语法错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "PHP错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Token认证测试完成 ===\n";
?> 