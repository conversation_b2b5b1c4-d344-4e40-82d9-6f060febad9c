<?php
/**
 * API管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

// 检查基本权限：系统管理员、平台管理员、商户可以访问API管理
if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin', 'merchant'])) {
    $auth->sendForbidden('无权限访问API管理');
}

try {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($action) {
        case 'api_management':
        case 'get_key_info':
            handleGetKeyInfo($auth);
            break;
            
        case 'get_call_stats':
            handleGetCallStats($auth);
            break;
            
        case 'update_callback_url':
            handleUpdateCallbackUrl($auth);
            break;
            
        case 'regenerate_api_key':
            handleRegenerateApiKey($auth);
            break;
            
        case 'get_ip_whitelist':
            handleGetIpWhitelist($auth);
            break;
            
        case 'update_ip_whitelist':
            handleUpdateIpWhitelist($auth);
            break;
            
        case 'api_test':
            handleApiTest($auth);
            break;
            
        case 'download_postman':
            handleDownloadPostman($auth);
            break;
            
        default:
            handleGetKeyInfo($auth);
            break;
    }
} catch (Exception $e) {
    error_log("API Management error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

/**
 * 获取API密钥信息
 */
function handleGetKeyInfo($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('api.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        // 根据用户类型获取商户ID
        $merchantId = getMerchantIdByUser($currentUser);
        
        if (!$merchantId) {
            $auth->sendError('未找到商户信息', 404);
            return;
        }
        
        // 获取API密钥信息
        $apiInfo = $db->fetch(
            "SELECT * FROM merchant_api_keys WHERE merchant_id = ?",
            array($merchantId)
        );
        
        if (!$apiInfo) {
            // 创建新的API密钥
            $apiKey = 'pk_' . bin2hex(random_bytes(16));
            $secretKey = 'sk_' . bin2hex(random_bytes(24));
            
            $db->execute(
                "INSERT INTO merchant_api_keys (merchant_id, api_key, secret_key, status, created_at, updated_at) VALUES (?, ?, ?, 'active', NOW(), NOW())",
                array($merchantId, $apiKey, $secretKey)
            );
            
            $apiInfo = array(
                'merchant_id' => $merchantId,
                'api_key' => $apiKey,
                'secret_key' => $secretKey,
                'status' => 'active',
                'callback_url' => '',
                'ip_whitelist' => '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            );
        }
        
        // 获取商户信息（基于新表结构）
        $merchantInfo = $db->fetch(
            "SELECT merchant_code, callback_url, service_rate FROM merchants WHERE id = ?",
            array($merchantId)
        );
        
        // 获取今日统计
        $todayStats = $db->fetch(
            "SELECT 
                COUNT(*) as total_calls,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as success_calls,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_calls
             FROM api_call_logs 
             WHERE merchant_id = ? AND DATE(created_at) = CURDATE()",
            array($merchantId)
        );
        
        $successRate = $todayStats['total_calls'] > 0 ? 
            round(($todayStats['success_calls'] / $todayStats['total_calls']) * 100, 2) : 0;
        
        $result = array(
            'merchant_id' => $merchantId,
            'developer_id' => 'DEV' . str_pad($merchantId, 6, '0', STR_PAD_LEFT),
            'api_key' => $apiInfo['api_key'],
            'secret_key' => $apiInfo['secret_key'],
            'status' => $apiInfo['status'],
            'callback_url' => $apiInfo['callback_url'] ?: '',
            'ip_whitelist' => $apiInfo['ip_whitelist'] ?: '',
            'permissions' => array('pay', 'query', 'refund'),
            'created_at' => $apiInfo['created_at'],
            'updated_at' => $apiInfo['updated_at'],
            'merchant_info' => $merchantInfo ?: array(),
            'today_stats' => array(
                'total_calls' => $todayStats['total_calls'] ?: 0,
                'success_calls' => $todayStats['success_calls'] ?: 0,
                'failed_calls' => $todayStats['failed_calls'] ?: 0,
                'success_rate' => $successRate
            )
        );
        
        // 记录操作日志
        $auth->logUserAction('view_api_key', 'api', $merchantId, '查看API密钥信息');
        
        $auth->sendSuccess($result, 'API密钥信息获取成功');
        
    } catch (Exception $e) {
        error_log("获取API密钥信息失败: " . $e->getMessage());
        $auth->sendError('获取API密钥信息失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 获取调用统计
 */
function handleGetCallStats($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('api.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        $merchantId = getMerchantIdByUser($currentUser);
        $days = isset($_GET['days']) ? intval($_GET['days']) : 7;
        
        if (!$merchantId) {
            $auth->sendError('未找到商户信息', 404);
            return;
        }
        
        // 获取每日统计
        $dailyStats = $db->fetchAll(
            "SELECT 
                DATE(created_at) as date,
                COUNT(*) as total_calls,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as success_calls,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_calls,
                AVG(response_time) as avg_response_time
             FROM api_call_logs 
             WHERE merchant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
             GROUP BY DATE(created_at)
             ORDER BY date DESC",
            array($merchantId, $days)
        );
        
        // 获取API接口统计
        $apiStats = $db->fetchAll(
            "SELECT 
                api_name,
                COUNT(*) as total_calls,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as success_calls,
                AVG(response_time) as avg_response_time
             FROM api_call_logs 
             WHERE merchant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
             GROUP BY api_name
             ORDER BY total_calls DESC
             LIMIT 10",
            array($merchantId, $days)
        );
        
        // 获取总体统计
        $totalStats = $db->fetch(
            "SELECT 
                COUNT(*) as total_calls,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as success_calls,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_calls,
                AVG(response_time) as avg_response_time,
                MAX(created_at) as last_call_time
             FROM api_call_logs 
             WHERE merchant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)",
            array($merchantId, $days)
        );
        
        // 记录操作日志
        $auth->logUserAction('view_api_stats', 'api', $merchantId, '查看API调用统计');
        
        $response = array(
            'daily_stats' => $dailyStats ?: array(),
            'api_stats' => $apiStats ?: array(),
            'total_stats' => $totalStats ?: array()
        );
        
        $auth->sendSuccess($response, 'API调用统计获取成功');
        
    } catch (Exception $e) {
        error_log("获取调用统计失败: " . $e->getMessage());
        $auth->sendError('获取调用统计失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 更新回调URL
 */
function handleUpdateCallbackUrl($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('api.update')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        $auth->sendError('不支持的请求方法', 405);
        return;
    }
    
    try {
        $merchantId = getMerchantIdByUser($currentUser);
        $input = json_decode(file_get_contents('php://input'), true);
        $callbackUrl = isset($input['callback_url']) ? trim($input['callback_url']) : '';
        
        if (!$merchantId) {
            $auth->sendError('未找到商户信息', 404);
            return;
        }
        
        // 验证URL格式
        if ($callbackUrl && !filter_var($callbackUrl, FILTER_VALIDATE_URL)) {
            $auth->sendError('回调URL格式不正确', 400);
            return;
        }
        
        // 更新回调URL
        $result = $db->execute(
            "UPDATE merchant_api_keys SET callback_url = ?, updated_at = NOW() WHERE merchant_id = ?",
            array($callbackUrl, $merchantId)
        );
        
        if ($result) {
            // 记录操作日志
            $auth->logUserAction('update_callback_url', 'api', $merchantId, "更新回调URL: $callbackUrl");
            
            $auth->sendSuccess(null, '回调URL更新成功');
        } else {
            $auth->sendError('回调URL更新失败', 500);
        }
        
    } catch (Exception $e) {
        error_log("更新回调URL失败: " . $e->getMessage());
        $auth->sendError('更新回调URL失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 重新生成API密钥（暂时简化）
 */
function handleRegenerateApiKey($auth) {
    // 检查权限
    if (!$auth->checkPermission('api.regenerate')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

/**
 * 获取IP白名单（暂时简化）
 */
function handleGetIpWhitelist($auth) {
    // 检查权限
    if (!$auth->checkPermission('api.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

/**
 * 更新IP白名单（暂时简化）
 */
function handleUpdateIpWhitelist($auth) {
    // 检查权限
    if (!$auth->checkPermission('api.update')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

/**
 * API测试（暂时简化）
 */
function handleApiTest($auth) {
    // 检查权限
    if (!$auth->checkPermission('api.test')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

/**
 * 下载Postman文档（暂时简化）
 */
function handleDownloadPostman($auth) {
    // 检查权限
    if (!$auth->checkPermission('api.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

/**
 * 根据用户获取商户ID
 */
function getMerchantIdByUser($user) {
    if ($user['user_type'] === 'merchant') {
        return $user['profile_id'];
    } elseif (in_array($user['user_type'], ['system_admin', 'platform_admin'])) {
        // 系统管理员和平台管理员可以指定商户ID
        return isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : null;
    }
    return null;
}
?>
