<?php
/**
 * 订单管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

try {
    // 检查用户权限
    if (!$auth->checkPermission('order.view')) {
        $auth->sendForbidden('无权限访问订单管理');
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($action) {
        case 'orders':
            handleOrders($auth);
            break;
        case 'order_detail':
            handleOrderDetail($auth);
            break;
        case 'update_order_status':
            handleUpdateOrderStatus($auth);
            break;
        case 'order_statistics':
            handleOrderStatistics($auth);
            break;
        default:
            // 默认订单管理
            handleOrders($auth);
    }
} catch (Exception $e) {
    error_log("Orders API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 订单管理主入口
function handleOrders($auth) {
    global $method;
    
    switch ($method) {
        case 'GET':
            handleGetOrders($auth);
            break;
        case 'PUT':
            handleUpdateOrder($auth);
            break;
        default:
            $auth->sendError('不支持的请求方法', 405);
    }
}

// 获取订单列表
function handleGetOrders($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('order.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "t.merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        if ($merchantId > 0) {
            $whereConditions[] = "t.merchant_id = ?";
            $params[] = $merchantId;
        }
    } else {
        $auth->sendForbidden('无权限访问订单');
        return;
    }
    
    if (!empty($status)) {
        $whereConditions[] = "t.status = ?";
        $params[] = $status;
    }
    
    // 日期范围筛选
    if (!empty($dateRange)) {
        switch ($dateRange) {
            case 'today':
                $whereConditions[] = "DATE(t.created_at) = CURDATE()";
                break;
            case 'yesterday':
                $whereConditions[] = "DATE(t.created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
                break;
            case 'week':
                $whereConditions[] = "t.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $whereConditions[] = "t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
        }
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(t.order_id LIKE ? OR t.out_trade_no LIKE ? OR t.remark LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    try {
        // 获取总数
        $totalResult = $db->fetch(
            "SELECT COUNT(*) as count FROM transactions t $whereClause",
            $params
        );
        $total = $totalResult['count'];
        
        // 获取订单列表
        $orders = $db->fetchAll(
            "SELECT t.*, 
                    m.company_name as merchant_name,
                    u.real_name as merchant_real_name,
                    p.name as product_name
             FROM transactions t
             LEFT JOIN merchants m ON t.merchant_id = m.id
             LEFT JOIN users u ON m.user_id = u.id
             LEFT JOIN products p ON t.product_id = p.id
             $whereClause
             ORDER BY t.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, array($limit, $offset))
        );
        
        // 获取统计信息
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_orders,
                COUNT(CASE WHEN t.status = 'paid' THEN 1 END) as paid_orders,
                COUNT(CASE WHEN t.status = 'failed' THEN 1 END) as failed_orders,
                SUM(CASE WHEN t.status = 'paid' THEN t.amount ELSE 0 END) as total_amount
             FROM transactions t $whereClause",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_orders', 'order', 0, '查看订单列表');
        
        $response = array(
            'orders' => $orders,
            'stats' => $stats,
            'pagination' => array(
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            )
        );
        
        $auth->sendSuccess($response, '订单列表获取成功');
        
    } catch (Exception $e) {
        error_log("获取订单列表失败: " . $e->getMessage());
        $auth->sendError('获取订单列表失败: ' . $e->getMessage(), 500);
    }
}

// 更新订单（暂时简化）
function handleUpdateOrder($auth) {
    $auth->sendError('功能开发中', 501);
}

// 获取订单详情
function handleOrderDetail($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('order.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $orderId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    if ($orderId <= 0) {
        $auth->sendError('无效的订单ID', 400);
        return;
    }
    
    try {
        // 构建查询条件
        $whereConditions = array("t.id = ?");
        $params = array($orderId);
        
        // 数据隔离
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = "t.merchant_id = ?";
            $params[] = $currentUser['profile_id'];
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        
        $order = $db->fetch(
            "SELECT t.*, 
                    m.company_name as merchant_name,
                    u.real_name as merchant_real_name,
                    p.name as product_name
             FROM transactions t
             LEFT JOIN merchants m ON t.merchant_id = m.id
             LEFT JOIN users u ON m.user_id = u.id
             LEFT JOIN products p ON t.product_id = p.id
             $whereClause",
            $params
        );
        
        if (!$order) {
            $auth->sendError('订单不存在', 404);
            return;
        }
        
        // 记录操作日志
        $auth->logUserAction('view_order_detail', 'order', $orderId, "查看订单详情: {$order['order_id']}");
        
        $auth->sendSuccess($order, '订单详情获取成功');
        
    } catch (Exception $e) {
        error_log("获取订单详情失败: " . $e->getMessage());
        $auth->sendError('获取订单详情失败: ' . $e->getMessage(), 500);
    }
}

// 更新订单状态
function handleUpdateOrderStatus($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('order.update')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $orderId = isset($input['id']) ? intval($input['id']) : 0;
    $status = isset($input['status']) ? trim($input['status']) : '';
    
    if ($orderId <= 0) {
        $auth->sendError('无效的订单ID', 400);
        return;
    }
    
    if (empty($status) || !in_array($status, array('pending', 'paid', 'failed', 'cancelled'))) {
        $auth->sendError('无效的订单状态', 400);
        return;
    }
    
    try {
        // 检查订单是否存在
        $order = $db->fetch("SELECT * FROM transactions WHERE id = ?", array($orderId));
        if (!$order) {
            $auth->sendError('订单不存在', 404);
            return;
        }
        
        // 数据隔离检查
        if ($currentUser['user_type'] === 'merchant' && $order['merchant_id'] != $currentUser['profile_id']) {
            $auth->sendForbidden('无权限操作此订单');
            return;
        }
        
        // 更新订单状态
        $db->execute(
            "UPDATE transactions SET status = ?, updated_at = NOW() WHERE id = ?",
            array($status, $orderId)
        );
        
        // 记录操作日志
        $auth->logUserAction('update_order_status', 'order', $orderId, "更新订单状态: {$order['order_id']} -> {$status}");
        
        $auth->sendSuccess(null, '订单状态更新成功');
        
    } catch (Exception $e) {
        error_log("更新订单状态失败: " . $e->getMessage());
        $auth->sendError('更新订单状态失败: ' . $e->getMessage(), 500);
    }
}

// 订单统计
function handleOrderStatistics($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('order.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : 'today';
    
    $whereConditions = array();
    $params = array();
    
    // 数据隔离
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "merchant_id = ?";
        $params[] = $currentUser['profile_id'];
    }
    
    // 日期范围
    switch ($dateRange) {
        case 'today':
            $whereConditions[] = "DATE(created_at) = CURDATE()";
            break;
        case 'week':
            $whereConditions[] = "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            break;
        case 'month':
            $whereConditions[] = "created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            break;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    try {
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
                COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_orders,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_orders,
                SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as total_amount,
                AVG(CASE WHEN status = 'paid' THEN amount ELSE NULL END) as avg_amount
             FROM transactions $whereClause",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_order_statistics', 'order', 0, '查看订单统计');
        
        $auth->sendSuccess($stats, '订单统计获取成功');
        
    } catch (Exception $e) {
        error_log("获取订单统计失败: " . $e->getMessage());
        $auth->sendError('获取订单统计失败: ' . $e->getMessage(), 500);
    }
}
?> 