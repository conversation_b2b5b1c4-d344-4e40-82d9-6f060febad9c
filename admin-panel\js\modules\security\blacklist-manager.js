/**
 * 黑名单管理模块
 * 从admin.js第13496行开始提取
 * 负责IP黑名单、用户黑名单、设备黑名单等安全防护功能
 */
// 客户端黑名单管理类
class BlacklistManager {
    constructor() {
        this.auth = new AuthManager();
        this.currentPage = 1;
        this.pageSize = 20;
        this.blacklistItems = [];
        this.filters = {
            blacklist_type: '',
            status: '',
            search: ''
        };
        this.selectedBlacklistId = null;
    }

    async initialize() {
        this.bindEvents();
        await this.loadBlacklistItems();
    }

    bindEvents() {
        // 筛选器变化事件
        const typeFilter = document.getElementById('blacklistTypeFilter');
        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => {
                this.filters.blacklist_type = e.target.value;
                this.applyFilters();
            });
        }

        const statusFilter = document.getElementById('blacklistStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }

        // 搜索框回车事件
        const searchInput = document.getElementById('blacklistSearchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchBlacklistItems();
                }
            });
        }
    }

    async loadBlacklistItems(page = 1) {
        try {
            this.currentPage = page;
            this.showLoadingState();

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                blacklist_type: this.filters.blacklist_type,
                status: this.filters.status,
                search: this.filters.search
            });

            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=blacklist&${params}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`,
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.code === 200) {
                this.blacklistItems = result.data.blacklist_items || [];
                this.updateStatsCards(result.data.stats || {});
                this.renderBlacklistList(this.blacklistItems);
                this.renderPagination(result.data.pagination || {});
            } else {
                throw new Error(result.message || '加载黑名单数据失败');
            }
        } catch (error) {
            console.error('加载黑名单数据失败:', error);
            this.showErrorState('加载黑名单数据失败: ' + error.message);
        }
    }

    updateStatsCards(stats) {
        const totalEl = document.getElementById('totalBlacklist');
        const activeEl = document.getElementById('activeBlacklist');
        const ipBlacklistEl = document.getElementById('ipBlacklist');
        const deviceBlacklistEl = document.getElementById('deviceBlacklist');
        
        if (totalEl) totalEl.textContent = stats.total_blacklist || 0;
        if (activeEl) activeEl.textContent = stats.active_blacklist || 0;
        if (ipBlacklistEl) ipBlacklistEl.textContent = stats.ip_blacklist || 0;
        if (deviceBlacklistEl) deviceBlacklistEl.textContent = stats.device_blacklist || 0;
    }

    renderBlacklistList(items) {
        const container = document.getElementById('blacklistTableContainer');
        const countEl = document.getElementById('blacklistCount');
        
        if (countEl) countEl.textContent = items.length;

        if (items.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="no-data-icon">🚫</div>
                    <h5>暂无黑名单数据</h5>
                    <p class="text-muted">还没有添加任何黑名单项目，或者当前筛选条件下没有找到匹配的数据。</p>
                </div>
            `;
            return;
        }

        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>黑名单类型</th>
                            <th>黑名单值</th>
                            <th>添加原因</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>创建人</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${items.map(item => this.renderBlacklistRow(item)).join('')}
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = tableHtml;
    }

    renderBlacklistRow(item) {
        const statusBadges = {
            'active': 'badge bg-danger',
            'inactive': 'badge bg-secondary'
        };

        const statusTexts = {
            'active': '生效中',
            'inactive': '已禁用'
        };

        const typeTexts = {
            'ip': 'IP地址',
            'device_id': '设备ID',
            'user_agent': '用户代理',
            'phone': '手机号',
            'email': '邮箱地址'
        };

        return `
            <tr>
                <td>
                    <div class="fw-bold">#${item.id}</div>
                </td>
                <td>
                    <span class="badge bg-light text-dark">
                        ${typeTexts[item.blacklist_type] || item.blacklist_type}
                    </span>
                </td>
                <td>
                    <div class="fw-bold text-danger">${item.blacklist_value}</div>
                    ${item.blacklist_type === 'ip' ? `<small class="text-muted">IP地址</small>` : ''}
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 200px;" title="${item.reason}">
                        ${item.reason || '未提供'}
                    </div>
                </td>
                <td>
                    <span class="${statusBadges[item.status] || 'badge bg-secondary'}">
                        ${statusTexts[item.status] || item.status}
                    </span>
                </td>
                <td>
                    <div class="small">${this.formatDateTime(item.created_at)}</div>
                </td>
                <td>
                    <div class="small">${item.created_by_name || '系统'}</div>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="window.blacklistManager.viewBlacklistDetail(${item.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${item.status === 'active' ? `
                            <button class="btn btn-outline-warning btn-sm" onclick="window.blacklistManager.disableBlacklistItem(${item.id})" title="禁用">
                                <i class="bi bi-pause-circle"></i>
                            </button>
                        ` : `
                            <button class="btn btn-outline-success btn-sm" onclick="window.blacklistManager.enableBlacklistItem(${item.id})" title="启用">
                                <i class="bi bi-play-circle"></i>
                            </button>
                        `}
                        <button class="btn btn-outline-danger btn-sm" onclick="window.blacklistManager.deleteBlacklistItem(${item.id})" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    applyFilters() {
        this.loadBlacklistItems(1);
    }

    searchBlacklistItems() {
        const searchInput = document.getElementById('blacklistSearchInput');
        if (searchInput) {
            this.filters.search = searchInput.value.trim();
            this.loadBlacklistItems(1);
        }
    }

    refreshBlacklistItems() {
        this.loadBlacklistItems(this.currentPage);
    }

    showAddModal() {
        const modal = new bootstrap.Modal(document.getElementById('addBlacklistModal'));
        modal.show();
    }

    async addBlacklistItem() {
        const form = document.getElementById('addBlacklistForm');
        const formData = new FormData(form);
        
        const data = {
            blacklist_type: formData.get('blacklist_type'),
            blacklist_value: formData.get('blacklist_value'),
            reason: formData.get('reason')
        };

        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=blacklist`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.code === 200) {
                this.showSuccess('黑名单项目添加成功');
                const modal = bootstrap.Modal.getInstance(document.getElementById('addBlacklistModal'));
                modal.hide();
                form.reset();
                this.refreshBlacklistItems();
            } else {
                throw new Error(result.message || '添加黑名单项目失败');
            }
        } catch (error) {
            console.error('添加黑名单项目失败:', error);
            this.showError('添加黑名单项目失败: ' + error.message);
        }
    }

    async viewBlacklistDetail(blacklistId) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=blacklist&id=${blacklistId}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const result = await response.json();

            if (result.code === 200) {
                this.showBlacklistDetailModal(result.data);
            } else {
                throw new Error(result.message || '获取黑名单详情失败');
            }
        } catch (error) {
            console.error('获取黑名单详情失败:', error);
            this.showError('获取黑名单详情失败: ' + error.message);
        }
    }

    showBlacklistDetailModal(item) {
        const modalContent = document.getElementById('blacklistDetailContent');
        if (!modalContent) return;

        const typeTexts = {
            'ip': 'IP地址',
            'device_id': '设备ID',
            'user_agent': '用户代理',
            'phone': '手机号',
            'email': '邮箱地址'
        };

        modalContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>黑名单ID:</strong></td>
                            <td>#${item.id}</td>
                        </tr>
                        <tr>
                            <td><strong>类型:</strong></td>
                            <td>${typeTexts[item.blacklist_type] || item.blacklist_type}</td>
                        </tr>
                        <tr>
                            <td><strong>黑名单值:</strong></td>
                            <td class="text-danger fw-bold">${item.blacklist_value}</td>
                        </tr>
                        <tr>
                            <td><strong>状态:</strong></td>
                            <td>${item.status === 'active' ? '<span class="badge bg-danger">生效中</span>' : '<span class="badge bg-secondary">已禁用</span>'}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>管理信息</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>创建时间:</strong></td>
                            <td>${this.formatDateTime(item.created_at)}</td>
                        </tr>
                        <tr>
                            <td><strong>创建人:</strong></td>
                            <td>${item.created_by_name || '系统'}</td>
                        </tr>
                        ${item.updated_at ? `
                        <tr>
                            <td><strong>更新时间:</strong></td>
                            <td>${this.formatDateTime(item.updated_at)}</td>
                        </tr>
                        ` : ''}
                    </table>
                </div>
            </div>
            
            <div class="mt-3">
                <h6>添加原因</h6>
                <div class="alert alert-info">
                    ${item.reason || '未提供添加原因'}
                </div>
            </div>
            
            ${item.hit_count ? `
            <div class="mt-3">
                <h6>拦截统计</h6>
                <div class="alert alert-warning">
                    <i class="bi bi-shield-check me-2"></i>
                    已拦截 <strong>${item.hit_count}</strong> 次访问
                </div>
            </div>
            ` : ''}
        `;

        const modal = new bootstrap.Modal(document.getElementById('blacklistDetailModal'));
        modal.show();
    }

    async disableBlacklistItem(blacklistId) {
        if (!confirm('确定要禁用此黑名单项目吗？')) return;

        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=blacklist&disable=1`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    blacklist_id: blacklistId
                })
            });

            const result = await response.json();

            if (result.code === 200) {
                this.showSuccess('黑名单项目已禁用');
                this.refreshBlacklistItems();
            } else {
                throw new Error(result.message || '禁用黑名单项目失败');
            }
        } catch (error) {
            console.error('禁用黑名单项目失败:', error);
            this.showError('禁用黑名单项目失败: ' + error.message);
        }
    }

    async enableBlacklistItem(blacklistId) {
        if (!confirm('确定要启用此黑名单项目吗？')) return;

        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=blacklist&enable=1`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    blacklist_id: blacklistId
                })
            });

            const result = await response.json();

            if (result.code === 200) {
                this.showSuccess('黑名单项目已启用');
                this.refreshBlacklistItems();
            } else {
                throw new Error(result.message || '启用黑名单项目失败');
            }
        } catch (error) {
            console.error('启用黑名单项目失败:', error);
            this.showError('启用黑名单项目失败: ' + error.message);
        }
    }

    async deleteBlacklistItem(blacklistId) {
        if (!confirm('确定要删除此黑名单项目吗？此操作不可恢复！')) return;

        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=blacklist&delete=1`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    blacklist_id: blacklistId
                })
            });

            const result = await response.json();

            if (result.code === 200) {
                this.showSuccess('黑名单项目删除成功');
                this.refreshBlacklistItems();
            } else {
                throw new Error(result.message || '删除黑名单项目失败');
            }
        } catch (error) {
            console.error('删除黑名单项目失败:', error);
            this.showError('删除黑名单项目失败: ' + error.message);
        }
    }

    renderPagination(pagination) {
        const container = document.getElementById('blacklistPagination');
        if (!container || !pagination.total_pages || pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }

        const currentPage = pagination.current_page || 1;
        const totalPages = pagination.total_pages;
        const maxVisiblePages = 5;

        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage < maxVisiblePages - 1) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        let paginationHtml = `
            <nav aria-label="黑名单分页">
                <ul class="pagination pagination-sm">
        `;

        // 上一页
        if (currentPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.blacklistManager.loadBlacklistItems(${currentPage - 1}); return false;">&laquo;</a>
                </li>
            `;
        }

        // 页码
        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="window.blacklistManager.loadBlacklistItems(${i}); return false;">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.blacklistManager.loadBlacklistItems(${currentPage + 1}); return false;">&raquo;</a>
                </li>
            `;
        }

        paginationHtml += `
                </ul>
            </nav>
        `;

        container.innerHTML = paginationHtml;
    }

    showLoadingState() {
        const container = document.getElementById('blacklistTableContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-3">正在加载黑名单数据...</div>
                </div>
            `;
        }
    }

    showErrorState(message) {
        const container = document.getElementById('blacklistTableContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="text-danger">
                        <i class="bi bi-exclamation-triangle fa-3x mb-3"></i>
                        <h5>加载失败</h5>
                        <p>${message}</p>
                        <button class="btn btn-primary" onclick="window.blacklistManager.refreshBlacklistItems()">重试</button>
                    </div>
                </div>
            `;
        }
    }

    showSuccess(message) {
        console.log('Success:', message);
        // TODO: 实现更好的提示组件
    }

    showError(message) {
        console.error('Error:', message);
        // TODO: 实现更好的提示组件
    }

    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
}

// 黑名单管理页面加载函数
async function loadBlacklistManagement(container) {
   try {
        const auth = new AuthManager();
        const user = auth.getUser();
        const userRole = user ? user.user_type : null;
        
        // 权限检查 - 只有admin可以访问
        if (userRole !== 'admin') {
            if (container) {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        您没有权限访问黑名单管理功能。
                    </div>
                `;
            }
            return;
        }

        const targetContainer = container || document.querySelector('.content-area') || document.body;
        targetContainer.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-shield-exclamation me-2"></i>客户端黑名单管理</h2>
                    <p class="text-muted mb-0">管理系统黑名单，拦截恶意访问</p>
                </div>
                <button class="btn btn-danger" onclick="window.blacklistManager.showAddModal()">
                    <i class="bi bi-plus-circle me-2"></i>添加黑名单
                </button>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-white-50">黑名单总数</div>
                                    <div class="h4 mb-0" id="totalBlacklist">0</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-shield-exclamation fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-white-50">生效中</div>
                                    <div class="h4 mb-0" id="activeBlacklist">0</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-shield-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-white-50">IP黑名单</div>
                                    <div class="h4 mb-0" id="ipBlacklist">0</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-geo-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <div class="text-white-50">设备黑名单</div>
                                    <div class="h4 mb-0" id="deviceBlacklist">0</div>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-phone fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">筛选和搜索</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="blacklistTypeFilter" class="form-label">黑名单类型</label>
                            <select class="form-select" id="blacklistTypeFilter">
                                <option value="">全部类型</option>
                                <option value="ip">IP地址</option>
                                <option value="device_id">设备ID</option>
                                <option value="user_agent">用户代理</option>
                                <option value="phone">手机号</option>
                                <option value="email">邮箱地址</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="blacklistStatusFilter" class="form-label">状态</label>
                            <select class="form-select" id="blacklistStatusFilter">
                                <option value="">全部状态</option>
                                <option value="active">生效中</option>
                                <option value="inactive">已禁用</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="blacklistSearchInput" class="form-label">搜索</label>
                            <input type="text" class="form-control" id="blacklistSearchInput" placeholder="搜索黑名单值或原因...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button class="btn btn-primary" onclick="window.blacklistManager.searchBlacklistItems()">
                                    <i class="bi bi-search me-1"></i>搜索
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 黑名单列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">黑名单列表 (<span id="blacklistCount">0</span>)</h5>
                    <button class="btn btn-outline-secondary btn-sm" onclick="window.blacklistManager.refreshBlacklistItems()">
                        <i class="bi bi-arrow-clockwise me-1"></i>刷新
                    </button>
                </div>
                <div class="card-body p-0">
                    <div id="blacklistTableContainer">
                        <!-- 黑名单数据表格将在这里动态生成 -->
                    </div>
                </div>
                <div class="card-footer">
                    <div id="blacklistPagination"></div>
                </div>
            </div>

            <!-- 添加黑名单弹窗 -->
            <div class="modal fade" id="addBlacklistModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">添加黑名单项目</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="addBlacklistForm">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="blacklistType" class="form-label">黑名单类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="blacklistType" name="blacklist_type" required>
                                        <option value="">请选择类型</option>
                                        <option value="ip">IP地址</option>
                                        <option value="device_id">设备ID</option>
                                        <option value="user_agent">用户代理</option>
                                        <option value="phone">手机号</option>
                                        <option value="email">邮箱地址</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="blacklistValue" class="form-label">黑名单值 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="blacklistValue" name="blacklist_value" required placeholder="请输入要拉黑的值">
                                    <div class="form-text">根据选择的类型输入相应的值，如IP地址、设备ID等</div>
                                </div>
                                <div class="mb-3">
                                    <label for="blacklistReason" class="form-label">添加原因</label>
                                    <textarea class="form-control" id="blacklistReason" name="reason" rows="3" placeholder="请说明添加到黑名单的原因"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-danger" onclick="window.blacklistManager.addBlacklistItem()">添加黑名单</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 黑名单详情弹窗 -->
            <div class="modal fade" id="blacklistDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">黑名单详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="blacklistDetailContent">
                                <!-- 黑名单详情内容将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 初始化黑名单管理器
        window.blacklistManager = new BlacklistManager();
        await window.blacklistManager.initialize();

    } catch (error) {
        console.error('加载黑名单管理页面失败:', error);
        const targetContainer = container || document.querySelector('.content-area') || document.body;
        if (targetContainer) {
            targetContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    加载黑名单管理页面失败: ${error.message}
                </div>
            `;
        }
    }
}



// 通知管理页面加载函数
function loadNotificationManagement(container) {
    const user = window.authManager.getUser();
    if (!user || (user.user_type !== 'admin' && user.user_type !== 'merchant')) {
        container.innerHTML = `
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>您没有权限访问此功能
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="management-page">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4><i class="bi bi-bell me-2"></i>通知任务管理</h4>
                        <p class="text-muted mb-0">管理和监控所有通知任务</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="window.notificationManager.exportData()">
                            <i class="bi bi-download me-2"></i>导出数据
                        </button>
                        <button class="btn btn-primary" onclick="window.notificationManager.refreshNotifications()">
                            <i class="bi bi-arrow-clockwise me-2"></i>刷新
                        </button>
                    </div>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row mb-4" id="notificationStats">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="stat-icon bg-primary text-white">
                                        <i class="bi bi-bell"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="stat-label">总通知数</div>
                                    <div class="stat-value" id="totalNotifications">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="stat-icon bg-warning text-white">
                                        <i class="bi bi-clock"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="stat-label">待发送</div>
                                    <div class="stat-value" id="pendingNotifications">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="stat-icon bg-success text-white">
                                        <i class="bi bi-check-circle"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="stat-label">已成功</div>
                                    <div class="stat-value" id="successNotifications">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="stat-icon bg-danger text-white">
                                        <i class="bi bi-x-circle"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="stat-label">发送失败</div>
                                    <div class="stat-value" id="failedNotifications">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        ${user.user_type === 'admin' ? `
                        <div class="col-md-3">
                            <label class="form-label">商户筛选</label>
                            <select class="form-select" id="notificationMerchantFilter">
                                <option value="">所有商户</option>
                            </select>
                        </div>
                        ` : ''}
                        <div class="col-md-3">
                            <label class="form-label">状态筛选</label>
                            <select class="form-select" id="notificationStatusFilter">
                                <option value="">所有状态</option>
                                <option value="pending">待发送</option>
                                <option value="processing">发送中</option>
                                <option value="success">已成功</option>
                                <option value="failed">失败</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">通知类型</label>
                            <select class="form-select" id="notificationTypeFilter">
                                <option value="">所有类型</option>
                                <option value="payment">支付通知</option>
                                <option value="system">系统通知</option>
                                <option value="promotion">营销通知</option>
                                <option value="alert">警报通知</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">搜索</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="notificationSearchInput" placeholder="搜索通知标题">
                                <button class="btn btn-outline-secondary" type="button" onclick="window.notificationManager.searchNotifications()">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通知任务列表 -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">通知任务列表</h6>
                        <div class="d-flex align-items-center gap-2">
                            <small class="text-muted">共 <span id="notificationCount">0</span> 条记录</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="notificationTableContainer">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-3">正在加载通知任务数据...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div id="notificationPagination" class="d-flex justify-content-center mt-4"></div>
        </div>

        <!-- 通知详情模态框 -->
        <div class="modal fade" id="notificationDetailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">通知任务详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="notificationDetailContent">
                        <!-- 详情内容 -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 初始化通知管理器
    if (!window.notificationManager) {
        window.notificationManager = new NotificationManager();
    }
    window.notificationManager.initialize();
}

// 黑名单管理页面加载函数（MenuManager版本）
function menuLoadBlacklistManagement(container) {
    // 调用全局的loadBlacklistManagement函数
    try {
        if (typeof loadBlacklistManagement === 'function') {
            loadBlacklistManagement(container);
        } else {
            if (container) {
                container.innerHTML = '<div class="alert alert-info">黑名单管理功能正在初始化...</div>';
            }
        }
    } catch (error) {
        console.error('调用黑名单管理函数失败:', error);
        if (container) {
            container.innerHTML = '<div class="alert alert-danger">黑名单管理功能加载失败</div>';
        }
    }
}

// 日报管理页面加载函数
function loadDailyReportManagement(container) {
    const user = window.authManager.getUser();
    
    if (!user) {
        container.innerHTML = '<div class="alert alert-danger">请先登录</div>';
        return;
    }

    // 检查权限
    if (user.user_type !== 'admin' && user.user_type !== 'merchant') {
        container.innerHTML = '<div class="alert alert-warning">您没有权限访问日报管理</div>';
        return;
    }

    container.innerHTML = `
        <div class="daily-report-management">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">📊 日报管理</h4>
                    <p class="text-muted mb-0">管理和查看每日业务报告</p>
                </div>
                <div>
                    <button class="btn btn-primary" id="generateReportBtn">
                        <i class="bi bi-plus-circle me-2"></i>生成报告
                    </button>
                </div>
            </div>

            <!-- 今日统计面板 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="stat-icon bg-success text-white">
                                        <i class="bi bi-currency-dollar"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="stat-label">今日收入</div>
                                    <div class="stat-value" id="todayRevenue">-</div>
                                    <div class="stat-change mt-1">
                                        <span id="comparedYesterday">-</span>
                                        <small class="text-muted">vs 昨日</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="stat-icon bg-primary text-white">
                                        <i class="bi bi-cart-check"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="stat-label">今日订单</div>
                                    <div class="stat-value" id="todayOrders">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="stat-icon bg-info text-white">
                                        <i class="bi bi-percent"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="stat-label">成功率</div>
                                    <div class="stat-value" id="successRate">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="stat-icon bg-warning text-white">
                                        <i class="bi bi-shield-exclamation"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="stat-label">风控拦截</div>
                                    <div class="stat-value" id="riskBlocked">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趋势图表 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">📈 近7天趋势</h6>
                </div>
                <div class="card-body">
                    <canvas id="reportChart" height="100"></canvas>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">日期范围</label>
                            <select class="form-select" id="reportDateFilter">
                                <option value="">所有日期</option>
                                <option value="today">今天</option>
                                <option value="yesterday">昨天</option>
                                <option value="week">最近7天</option>
                                <option value="month">最近30天</option>
                            </select>
                        </div>
                        ${user.user_type === 'admin' ? `
                        <div class="col-md-3">
                            <label class="form-label">商户筛选</label>
                            <select class="form-select" id="reportMerchantFilter">
                                <option value="">所有商户</option>
                            </select>
                        </div>
                        ` : ''}
                        <div class="col-md-3">
                            <label class="form-label">状态筛选</label>
                            <select class="form-select" id="reportStatusFilter">
                                <option value="">所有状态</option>
                                <option value="completed">已完成</option>
                                <option value="processing">生成中</option>
                                <option value="failed">失败</option>
                                <option value="pending">待生成</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">搜索</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="reportSearchInput" placeholder="搜索日期或商户">
                                <button class="btn btn-outline-secondary" type="button" id="reportSearchBtn">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 日报列表 -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">日报列表</h6>
                        <div class="d-flex align-items-center gap-2">
                            <small class="text-muted">共 <span id="reportCount">0</span> 条记录</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="reportsTableContainer">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-3">正在加载日报数据...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div id="reportPagination" class="d-flex justify-content-center mt-4"></div>
        </div>

        <!-- 生成报告模态框 -->
        <div class="modal fade" id="generateReportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">生成日报</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="generateReportForm">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">报告日期 <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" name="report_date" required 
                                       max="${new Date().toISOString().split('T')[0]}">
                            </div>
                            ${user.user_type === 'admin' ? `
                            <div class="mb-3">
                                <label class="form-label">指定商户</label>
                                <select class="form-select" name="merchant_id">
                                    <option value="">生成系统报告（所有商户）</option>
                                </select>
                                <div class="form-text">留空将生成包含所有商户的系统报告</div>
                            </div>
                            ` : ''}
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                报告将包含指定日期的交易数据、财务统计、风控信息等内容。
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="window.dailyReportManager.generateReport()">
                                <i class="bi bi-gear me-2"></i>开始生成
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 报告详情模态框 -->
        <div class="modal fade" id="reportDetailModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">📊 日报详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="reportDetailContent">
                        <!-- 详情内容 -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 初始化日报管理器
    if (!window.dailyReportManager) {
        window.dailyReportManager = new DailyReportManager();
    }
    window.dailyReportManager.initialize();
}



// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = BlacklistManager;
} else if (typeof window !== "undefined") {
    window.BlacklistManager = BlacklistManager;
} 