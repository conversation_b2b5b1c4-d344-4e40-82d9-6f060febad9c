<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';

$db = new Database();
$auth = new Auth();

// 路由处理
$method = $_SERVER['REQUEST_METHOD'];
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    // 获取认证用户信息
    $token = $auth->getAuthToken();
    $currentUser = $auth->getCurrentUser($token);
    
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(array(
            'error_code' => 401,
            'error_message' => '未授权访问，请先登录',
            'data' => null
        ));
        exit;
    }
    
    // 检查小组管理权限
    if (!$auth->hasPermission($currentUser, 'manage_devices') && 
        !$auth->hasPermission($currentUser, 'view_devices') && 
        $currentUser['user_type'] !== 'admin' &&
        $currentUser['user_type'] !== 'provider') {
        http_response_code(403);
        echo json_encode(array(
            'error_code' => 403,
            'error_message' => '无权限访问小组管理功能',
            'data' => null
        ));
        exit;
    }

    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'list':
                    handleGroupList($currentUser);
                    break;
                case 'detail':
                    handleGroupDetail($currentUser);
                    break;
                case 'leaders':
                    handleAvailableLeaders($currentUser);
                    break;
                case 'dns_records':
                    handleDnsRecords($currentUser);
                    break;
                default:
                    throw new Exception('不支持的GET操作');
            }
            break;
            
        case 'POST':
            switch ($action) {
                case 'create':
                    handleGroupCreate($currentUser);
                    break;
                case 'update':
                    handleGroupUpdate($currentUser);
                    break;
                case 'delete':
                    handleGroupDelete($currentUser);
                    break;
                case 'generate_code':
                    handleGenerateGroupCode($currentUser);
                    break;
                case 'update_dns':
                    handleUpdateDnsRecord($currentUser);
                    break;
                default:
                    throw new Exception('不支持的POST操作');
            }
            break;
            
        default:
            throw new Exception('不支持的HTTP方法');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'error_code' => 400,
        'error_message' => $e->getMessage(),
        'data' => null
    ));
}

// 获取小组列表
function handleGroupList($currentUser) {
    global $db;
    
    // 分页参数
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(10, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    // 筛选参数
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    // 构建查询条件
    $where = array();
    $params = array();
    
    // 根据用户类型限制数据访问范围
    if ($currentUser['user_type'] === 'provider') {
        $where[] = "g.provider_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'employee') {
        $jobPosition = getEmployeePosition($currentUser['id']);
        if ($jobPosition === 'group_manager') {
            // 大组长能看到自己管理的小组
            $where[] = "g.group_manager_id = ?";
            $params[] = $currentUser['id'];
        } elseif ($jobPosition === 'team_leader') {
            // 小组长只能看到自己负责的小组
            $where[] = "g.team_leader_id = ?";
            $params[] = $currentUser['id'];
        } else {
            $where[] = "1 = 0"; // 其他员工无权查看
        }
    }
    
    // 状态筛选
    if ($status) {
        $where[] = "g.status = ?";
        $params[] = $status;
    }
    
    // 搜索条件（小组识别码、备注）
    if ($search) {
        $where[] = "(g.group_code LIKE ? OR g.description LIKE ?)";
        $searchParam = '%' . $search . '%';
        $params[] = $searchParam;
        $params[] = $searchParam;
    }
    
    $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
    
    // 查询小组列表
    $sql = "SELECT g.*, 
                   p.company_name as provider_name,
                   u1.real_name as team_leader_name,
                   u2.real_name as group_manager_name,
                   u3.real_name as created_by_name,
                   COUNT(d.id) as device_count,
                   COUNT(CASE WHEN d.status = 'active' THEN 1 END) as active_device_count
            FROM provider_groups g
            LEFT JOIN payment_providers p ON g.provider_id = p.id
            LEFT JOIN users u1 ON g.team_leader_id = u1.id
            LEFT JOIN users u2 ON g.group_manager_id = u2.id
            LEFT JOIN users u3 ON g.created_by = u3.id
            LEFT JOIN devices d ON g.id = d.group_id
            $whereClause
            GROUP BY g.id
            ORDER BY g.created_at DESC
            LIMIT $limit OFFSET $offset";
    
    $groups = $db->fetchAll($sql, $params);
    
    // 查询总数
    $countSql = "SELECT COUNT(DISTINCT g.id) as total 
                 FROM provider_groups g
                 LEFT JOIN payment_providers p ON g.provider_id = p.id
                 $whereClause";
    $totalResult = $db->fetch($countSql, $params);
    $total = $totalResult['total'];
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'groups' => $groups,
            'pagination' => array(
                'page' => $page,
                'limit' => $limit,
                'total' => intval($total),
                'pages' => ceil($total / $limit)
            )
        )
    ));
}

// 获取小组详情
function handleGroupDetail($currentUser) {
    global $db;
    
    $groupId = isset($_GET['group_id']) ? intval($_GET['group_id']) : 0;
    if ($groupId === 0) {
        throw new Exception('缺少小组ID参数');
    }
    
    // 构建权限检查条件
    $whereClause = "g.id = ?";
    $params = array($groupId);
    
    if ($currentUser['user_type'] === 'provider') {
        $whereClause .= " AND g.provider_id = ?";
        $params[] = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'employee') {
        $jobPosition = getEmployeePosition($currentUser['id']);
        if ($jobPosition === 'group_manager') {
            $whereClause .= " AND g.group_manager_id = ?";
            $params[] = $currentUser['id'];
        } elseif ($jobPosition === 'team_leader') {
            $whereClause .= " AND g.team_leader_id = ?";
            $params[] = $currentUser['id'];
        } else {
            $whereClause .= " AND 1 = 0";
        }
    }
    
    // 查询小组信息
    $group = $db->fetch(
        "SELECT g.*, 
                p.company_name as provider_name,
                u1.real_name as team_leader_name,
                u1.username as team_leader_username,
                u2.real_name as group_manager_name,
                u2.username as group_manager_username,
                u3.real_name as created_by_name
         FROM provider_groups g
         LEFT JOIN payment_providers p ON g.provider_id = p.id
         LEFT JOIN users u1 ON g.team_leader_id = u1.id
         LEFT JOIN users u2 ON g.group_manager_id = u2.id
         LEFT JOIN users u3 ON g.created_by = u3.id
         WHERE $whereClause",
        $params
    );
    
    if (!$group) {
        throw new Exception('小组不存在或无权限访问');
    }
    
    // 查询小组设备列表
    $devices = $db->fetchAll(
        "SELECT d.device_id, d.device_name, d.device_brand, d.device_model, 
                d.status, d.last_checkin_time, d.checkin_status, d.created_at
         FROM devices d
         WHERE d.group_id = ?
         ORDER BY d.created_at DESC",
        array($groupId)
    );
    
    // 统计信息
    $stats = $db->fetch(
        "SELECT COUNT(*) as total_devices,
                COUNT(CASE WHEN d.status = 'active' THEN 1 END) as active_devices,
                COUNT(CASE WHEN d.status = 'pending' THEN 1 END) as pending_devices,
                COUNT(CASE WHEN d.checkin_status = 'overdue' THEN 1 END) as overdue_devices
         FROM devices d
         WHERE d.group_id = ?",
        array($groupId)
    );
    
    $group['devices'] = $devices;
    $group['stats'] = $stats;
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => $group
    ));
}

// 获取可用的小组长列表
function handleAvailableLeaders($currentUser) {
    global $db;
    
    $leaderType = isset($_GET['type']) ? $_GET['type'] : 'team_leader';
    
    if ($currentUser['user_type'] !== 'provider' && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限查看员工列表');
    }
    
    // 构建查询条件
    $whereClause = "u.user_type = 'employee' AND u.status = 'active'";
    $params = array();
    
    if ($leaderType === 'team_leader') {
        $whereClause .= " AND jp.position_name = 'team_leader'";
    } elseif ($leaderType === 'group_manager') {
        $whereClause .= " AND jp.position_name = 'group_manager'";
    }
    
    // 如果是码商，只能看到自己的员工
    if ($currentUser['user_type'] === 'provider') {
        $whereClause .= " AND u.parent_id = ?";
        $params[] = $currentUser['id'];
    }
    
    $leaders = $db->fetchAll(
        "SELECT u.id, u.real_name, u.username, u.email, jp.position_name,
                COUNT(g.id) as managed_groups_count
         FROM users u
         LEFT JOIN job_positions jp ON u.job_position_id = jp.id
         LEFT JOIN provider_groups g ON (
             (jp.position_name = 'team_leader' AND g.team_leader_id = u.id) OR
             (jp.position_name = 'group_manager' AND g.group_manager_id = u.id)
         )
         WHERE $whereClause
         GROUP BY u.id
         ORDER BY u.real_name",
        $params
    );
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => $leaders
    ));
}

// 获取DNS记录信息
function handleDnsRecords($currentUser) {
    global $db;
    
    if ($currentUser['user_type'] !== 'provider' && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限查看DNS记录');
    }
    
    $providerId = $currentUser['user_type'] === 'provider' ? $currentUser['profile_id'] : intval($_GET['provider_id']);
    
    // 获取码商DNS域名
    $provider = $db->fetch(
        "SELECT dns_domain, merchant_code FROM payment_providers WHERE id = ?",
        array($providerId)
    );
    
    if (!$provider) {
        throw new Exception('码商信息不存在');
    }
    
    // 获取所有小组的DNS记录
    $groups = $db->fetchAll(
        "SELECT g.group_code, g.decrypt_key, g.dns_record, g.status,
                p.company_name as provider_name
         FROM provider_groups g
         LEFT JOIN payment_providers p ON g.provider_id = p.id
         WHERE g.provider_id = ?
         ORDER BY g.created_at DESC",
        array($providerId)
    );
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'dns_domain' => $provider['dns_domain'],
            'merchant_code' => $provider['merchant_code'],
            'groups' => $groups
        )
    ));
}

// 创建小组
function handleGroupCreate($currentUser) {
    global $db, $auth;
    
    if ($currentUser['user_type'] !== 'provider' && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限创建小组');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('缺少请求数据');
    }
    
    $providerId = $currentUser['user_type'] === 'provider' ? $currentUser['profile_id'] : intval($input['provider_id']);
    $teamLeaderId = isset($input['team_leader_id']) ? intval($input['team_leader_id']) : 0;
    $groupManagerId = isset($input['group_manager_id']) ? intval($input['group_manager_id']) : 0;
    $maxDevices = isset($input['max_devices']) ? intval($input['max_devices']) : 50;
    $description = isset($input['description']) ? trim($input['description']) : '';
    
    // 生成小组识别码（6位随机字符）
    $groupCode = generateGroupCode();
    
    // 获取码商信息
    $provider = $db->fetch(
        "SELECT merchant_code, dns_domain FROM payment_providers WHERE id = ?",
        array($providerId)
    );
    
    if (!$provider) {
        throw new Exception('码商信息不存在');
    }
    
    // 生成解密密钥
    $decryptKey = generateDecryptKey($provider['merchant_code'], $groupCode);
    
    // 生成API服务器地址（示例）
    $apiServerUrl = "https://api.paypal-system.com/api/";
    
    // 加密API地址
    $encryptedData = encryptApiData($apiServerUrl, $decryptKey);
    
    $db->beginTransaction();
    try {
        // 创建小组记录
        $db->execute(
            "INSERT INTO provider_groups (
                provider_id, group_code, team_leader_id, group_manager_id, 
                decrypt_key, dns_record, max_devices, description, 
                status, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW())",
            array(
                $providerId, $groupCode, $teamLeaderId, $groupManagerId,
                $decryptKey, $encryptedData, $maxDevices, $description,
                $currentUser['id']
            )
        );
        
        $groupId = $db->lastInsertId();
        
        // 记录日志
        $auth->logAction($currentUser['id'], 'create_group', 'group', $groupId, "创建小组: $groupCode");
        
        $db->commit();
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array(
                'group_id' => $groupId,
                'group_code' => $groupCode,
                'decrypt_key' => $decryptKey,
                'dns_record' => $encryptedData,
                'message' => '小组创建成功'
            )
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 更新小组
function handleGroupUpdate($currentUser) {
    global $db, $auth;
    
    if ($currentUser['user_type'] !== 'provider' && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限修改小组');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['group_id'])) {
        throw new Exception('缺少小组ID参数');
    }
    
    $groupId = intval($input['group_id']);
    $teamLeaderId = isset($input['team_leader_id']) ? intval($input['team_leader_id']) : null;
    $groupManagerId = isset($input['group_manager_id']) ? intval($input['group_manager_id']) : null;
    $maxDevices = isset($input['max_devices']) ? intval($input['max_devices']) : null;
    $description = isset($input['description']) ? trim($input['description']) : null;
    $status = isset($input['status']) ? $input['status'] : null;
    
    // 检查小组是否存在
    $group = $db->fetch("SELECT * FROM provider_groups WHERE id = ?", array($groupId));
    if (!$group) {
        throw new Exception('小组不存在');
    }
    
    // 检查权限
    if ($currentUser['user_type'] === 'provider' && $group['provider_id'] !== $currentUser['profile_id']) {
        throw new Exception('只能修改自己的小组');
    }
    
    $db->beginTransaction();
    try {
        // 构建更新语句
        $updateFields = array();
        $updateParams = array();
        
        if ($teamLeaderId !== null) {
            $updateFields[] = "team_leader_id = ?";
            $updateParams[] = $teamLeaderId;
        }
        
        if ($groupManagerId !== null) {
            $updateFields[] = "group_manager_id = ?";
            $updateParams[] = $groupManagerId;
        }
        
        if ($maxDevices !== null) {
            $updateFields[] = "max_devices = ?";
            $updateParams[] = $maxDevices;
        }
        
        if ($description !== null) {
            $updateFields[] = "description = ?";
            $updateParams[] = $description;
        }
        
        if ($status !== null && in_array($status, array('active', 'inactive', 'suspended'))) {
            $updateFields[] = "status = ?";
            $updateParams[] = $status;
        }
        
        if (!empty($updateFields)) {
            $updateFields[] = "updated_at = NOW()";
            $updateParams[] = $groupId;
            
            $sql = "UPDATE provider_groups SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $db->execute($sql, $updateParams);
        }
        
        // 记录日志
        $auth->logAction($currentUser['id'], 'update_group', 'group', $groupId, "更新小组: {$group['group_code']}");
        
        $db->commit();
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array('message' => '小组更新成功')
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 删除小组
function handleGroupDelete($currentUser) {
    global $db, $auth;
    
    if ($currentUser['user_type'] !== 'provider' && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限删除小组');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['group_id'])) {
        throw new Exception('缺少小组ID参数');
    }
    
    $groupId = intval($input['group_id']);
    
    // 检查小组是否存在
    $group = $db->fetch("SELECT * FROM provider_groups WHERE id = ?", array($groupId));
    if (!$group) {
        throw new Exception('小组不存在');
    }
    
    // 检查权限
    if ($currentUser['user_type'] === 'provider' && $group['provider_id'] !== $currentUser['profile_id']) {
        throw new Exception('只能删除自己的小组');
    }
    
    // 检查是否有关联设备
    $deviceCount = $db->fetch("SELECT COUNT(*) as count FROM devices WHERE group_id = ?", array($groupId));
    if ($deviceCount['count'] > 0) {
        throw new Exception('该小组下还有设备，请先转移或删除设备后再删除小组');
    }
    
    $db->beginTransaction();
    try {
        // 删除小组
        $db->execute("DELETE FROM provider_groups WHERE id = ?", array($groupId));
        
        // 记录日志
        $auth->logAction($currentUser['id'], 'delete_group', 'group', $groupId, "删除小组: {$group['group_code']}");
        
        $db->commit();
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array('message' => '小组删除成功')
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 生成新的小组识别码
function handleGenerateGroupCode($currentUser) {
    global $db;
    
    if ($currentUser['user_type'] !== 'provider' && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限生成小组识别码');
    }
    
    // 生成新的小组识别码
    $groupCode = generateGroupCode();
    
    // 获取码商信息
    $providerId = $currentUser['user_type'] === 'provider' ? $currentUser['profile_id'] : 0;
    $provider = $db->fetch(
        "SELECT merchant_code FROM payment_providers WHERE id = ?",
        array($providerId)
    );
    
    if (!$provider) {
        throw new Exception('码商信息不存在');
    }
    
    // 生成解密密钥
    $decryptKey = generateDecryptKey($provider['merchant_code'], $groupCode);
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'group_code' => $groupCode,
            'decrypt_key' => $decryptKey
        )
    ));
}

// 更新DNS记录
function handleUpdateDnsRecord($currentUser) {
    global $db, $auth;
    
    if ($currentUser['user_type'] !== 'provider' && $currentUser['user_type'] !== 'admin') {
        throw new Exception('无权限更新DNS记录');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['group_id']) || !isset($input['api_url'])) {
        throw new Exception('缺少必要参数（group_id, api_url）');
    }
    
    $groupId = intval($input['group_id']);
    $apiUrl = trim($input['api_url']);
    
    if (empty($apiUrl)) {
        throw new Exception('API地址不能为空');
    }
    
    // 检查小组是否存在
    $group = $db->fetch("SELECT * FROM provider_groups WHERE id = ?", array($groupId));
    if (!$group) {
        throw new Exception('小组不存在');
    }
    
    // 检查权限
    if ($currentUser['user_type'] === 'provider' && $group['provider_id'] !== $currentUser['profile_id']) {
        throw new Exception('只能更新自己小组的DNS记录');
    }
    
    $db->beginTransaction();
    try {
        // 加密新的API地址
        $encryptedData = encryptApiData($apiUrl, $group['decrypt_key']);
        
        // 更新DNS记录
        $db->execute(
            "UPDATE provider_groups SET dns_record = ?, updated_at = NOW() WHERE id = ?",
            array($encryptedData, $groupId)
        );
        
        // 记录日志
        $auth->logAction($currentUser['id'], 'update_dns', 'group', $groupId, "更新小组DNS记录: {$group['group_code']}");
        
        $db->commit();
        
        echo json_encode(array(
            'error_code' => 0,
            'error_message' => 'success',
            'data' => array(
                'dns_record' => $encryptedData,
                'message' => 'DNS记录更新成功'
            )
        ));
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// 辅助函数：生成小组识别码
function generateGroupCode() {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    for ($i = 0; $i < 6; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $code;
}

// 辅助函数：生成解密密钥
function generateDecryptKey($merchantCode, $groupCode) {
    $salt = 'paypal_secret_salt_2024';
    return hash('sha256', $merchantCode . $groupCode . $salt);
}

// 辅助函数：加密API数据
function encryptApiData($apiUrl, $decryptKey) {
    // 简单的加密实现（实际项目中应使用更安全的加密方法）
    $data = json_encode(array(
        'api_url' => $apiUrl,
        'timestamp' => time()
    ));
    
    return base64_encode($data . '|' . hash('sha256', $data . $decryptKey));
}

// 辅助函数：获取员工职位
function getEmployeePosition($userId) {
    global $db;
    
    $result = $db->fetch(
        "SELECT jp.position_name FROM users u
         LEFT JOIN job_positions jp ON u.job_position_id = jp.id
         WHERE u.id = ?",
        array($userId)
    );
    
    return $result ? $result['position_name'] : null;
}

?> 