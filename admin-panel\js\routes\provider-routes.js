/**
 * 码商路由配置
 * 优化版本 - 只加载码商需要的功能
 * 
 * <AUTHOR>
 * @version 2.0.0 - 架构优化版本
 */

const ProviderRoutes = {
    // 租户类型标识
    tenantType: 'provider',
    
    // 码商菜单配置（18个功能）
    menuConfig: [
        {
            id: 'dashboard',
            name: '码商概览',
            icon: 'bi-speedometer2',
            path: '/dashboard',
            module: 'ProviderDashboardModule',
            permission: 'provider_dashboard',
            description: '码商业务数据概览'
        },
        {
            id: 'orders',
            name: '订单管理',
            icon: 'bi-receipt',
            path: '/orders',
            module: 'ProviderOrderModule',
            permission: 'provider_orders',
            description: '处理和管理订单'
        },
        {
            id: 'qr_codes',
            name: '收款码管理',
            icon: 'bi-qr-code',
            path: '/qr-codes',
            module: 'QrCodeManagementModule',
            permission: 'qr_code_management',
            description: '管理收款二维码'
        },
        {
            id: 'alipay_accounts',
            name: '支付宝账户',
            icon: 'bi-wallet2',
            path: '/alipay-accounts',
            module: 'AlipayAccountModule',
            permission: 'alipay_account_management',
            description: '支付宝收款账户管理'
        },
        {
            id: 'alipay_bills',
            name: '支付宝账单',
            icon: 'bi-file-earmark-text',
            path: '/alipay-bills',
            module: 'AlipayBillsModule',
            permission: 'alipay_bills_management',
            description: '支付宝账单管理和匹配'
        },
        {
            id: 'wechat_accounts',
            name: '微信账户',
            icon: 'bi-chat-dots',
            path: '/wechat-accounts',
            module: 'WechatAccountModule',
            permission: 'wechat_account_management',
            description: '微信收款账户管理'
        },
        {
            id: 'balance',
            name: '余额管理',
            icon: 'bi-currency-dollar',
            path: '/balance',
            module: 'ProviderBalanceModule',
            permission: 'balance_management',
            description: '账户余额和流水'
        },
        {
            id: 'settlements',
            name: '结算管理',
            icon: 'bi-calculator',
            path: '/settlements',
            module: 'SettlementModule',
            permission: 'settlement_management',
            description: '资金结算和提现'
        },
        {
            id: 'transactions',
            name: '交易流水',
            icon: 'bi-list-ul',
            path: '/transactions',
            module: 'TransactionModule',
            permission: 'transaction_view',
            description: '查看交易记录'
        },
        {
            id: 'reports',
            name: '数据报表',
            icon: 'bi-graph-up',
            path: '/reports',
            module: 'ProviderReportsModule',
            permission: 'provider_reports',
            description: '业务数据统计报表'
        },
        {
            id: 'notifications',
            name: '消息通知',
            icon: 'bi-bell',
            path: '/notifications',
            module: 'ProviderNotificationModule',
            permission: 'provider_notifications',
            description: '系统消息和通知'
        },
        {
            id: 'api_config',
            name: 'API配置',
            icon: 'bi-gear',
            path: '/api-config',
            module: 'ProviderApiModule',
            permission: 'api_configuration',
            description: 'API接口配置管理'
        },
        {
            id: 'security',
            name: '安全设置',
            icon: 'bi-shield-check',
            path: '/security',
            module: 'ProviderSecurityModule',
            permission: 'security_settings',
            description: '账户安全设置'
        },
        {
            id: 'logs',
            name: '操作日志',
            icon: 'bi-journal-text',
            path: '/logs',
            module: 'ProviderLogsModule',
            permission: 'provider_logs',
            description: '操作记录和日志'
        },
        {
            id: 'help',
            name: '帮助中心',
            icon: 'bi-question-circle',
            path: '/help',
            module: 'ProviderHelpModule',
            permission: 'help_center',
            description: '使用帮助和文档'
        },
        {
            id: 'feedback',
            name: '意见反馈',
            icon: 'bi-chat-square-text',
            path: '/feedback',
            module: 'FeedbackModule',
            permission: 'feedback_submit',
            description: '提交意见和建议'
        },
        {
            id: 'statistics',
            name: '统计分析',
            icon: 'bi-bar-chart',
            path: '/statistics',
            module: 'ProviderStatisticsModule',
            permission: 'statistics_view',
            description: '业务统计分析'
        },
        {
            id: 'profile',
            name: '个人资料',
            icon: 'bi-person-circle',
            path: '/profile',
            module: 'ProviderProfileModule',
            permission: 'profile_management',
            description: '管理个人账户信息'
        }
    ],

    // 路由配置
    routes: {
        '/dashboard': {
            component: 'ProviderDashboardModule',
            title: '码商概览',
            requireAuth: true,
            permission: 'provider_dashboard'
        },
        '/orders': {
            component: 'ProviderOrderModule',
            title: '订单管理',
            requireAuth: true,
            permission: 'provider_orders'
        },
        '/qr-codes': {
            component: 'QrCodeManagementModule',
            title: '收款码管理',
            requireAuth: true,
            permission: 'qr_code_management'
        },
        '/alipay-accounts': {
            component: 'AlipayAccountModule',
            title: '支付宝账户',
            requireAuth: true,
            permission: 'alipay_account_management'
        },
        '/alipay-bills': {
            component: 'AlipayBillsModule',
            title: '支付宝账单',
            requireAuth: true,
            permission: 'alipay_bills_management'
        },
        '/wechat-accounts': {
            component: 'WechatAccountModule',
            title: '微信账户',
            requireAuth: true,
            permission: 'wechat_account_management'
        },
        '/balance': {
            component: 'ProviderBalanceModule',
            title: '余额管理',
            requireAuth: true,
            permission: 'balance_management'
        },
        '/settlements': {
            component: 'SettlementModule',
            title: '结算管理',
            requireAuth: true,
            permission: 'settlement_management'
        },
        '/transactions': {
            component: 'TransactionModule',
            title: '交易流水',
            requireAuth: true,
            permission: 'transaction_view'
        },
        '/reports': {
            component: 'ProviderReportsModule',
            title: '数据报表',
            requireAuth: true,
            permission: 'provider_reports'
        },
        '/notifications': {
            component: 'ProviderNotificationModule',
            title: '消息通知',
            requireAuth: true,
            permission: 'provider_notifications'
        },
        '/api-config': {
            component: 'ProviderApiModule',
            title: 'API配置',
            requireAuth: true,
            permission: 'api_configuration'
        },
        '/security': {
            component: 'ProviderSecurityModule',
            title: '安全设置',
            requireAuth: true,
            permission: 'security_settings'
        },
        '/logs': {
            component: 'ProviderLogsModule',
            title: '操作日志',
            requireAuth: true,
            permission: 'provider_logs'
        },
        '/help': {
            component: 'ProviderHelpModule',
            title: '帮助中心',
            requireAuth: true,
            permission: 'help_center'
        },
        '/feedback': {
            component: 'FeedbackModule',
            title: '意见反馈',
            requireAuth: true,
            permission: 'feedback_submit'
        },
        '/statistics': {
            component: 'ProviderStatisticsModule',
            title: '统计分析',
            requireAuth: true,
            permission: 'statistics_view'
        },
        '/profile': {
            component: 'ProviderProfileModule',
            title: '个人资料',
            requireAuth: true,
            permission: 'profile_management'
        }
    },

    // 权限配置
    permissions: [
        'provider_dashboard',
        'provider_orders',
        'qr_code_management',
        'alipay_account_management',
        'alipay_bills_management',
        'wechat_account_management',
        'balance_management',
        'settlement_management',
        'transaction_view',
        'provider_reports',
        'provider_notifications',
        'api_configuration',
        'security_settings',
        'provider_logs',
        'help_center',
        'feedback_submit',
        'statistics_view',
        'profile_management'
    ],

    // 默认路由
    defaultRoute: '/dashboard',

    // 主题配置
    theme: {
        primaryColor: '#52c41a',
        brandName: '码商管理系统',
        sidebarStyle: 'light',
        headerStyle: 'light'
    },

    // API端点配置
    apiEndpoints: {
        base: '/api/provider/index.php',
        auth: '/api/provider/index.php?module=auth&action=login',
        logout: '/api/provider/index.php?module=auth&action=logout',
        profile: '/api/provider/index.php?module=profile&action=get'
    },

    // 快速操作配置
    quickActions: [
        {
            name: '查看今日数据',
            icon: 'bi-speedometer2',
            action: 'dashboard',
            color: 'primary'
        },
        {
            name: '处理订单',
            icon: 'bi-receipt',
            action: 'process_orders',
            color: 'success'
        },
        {
            name: '添加收款码',
            icon: 'bi-plus-circle',
            action: 'add_qr_code',
            color: 'info'
        },
        {
            name: '查看余额',
            icon: 'bi-currency-dollar',
            action: 'check_balance',
            color: 'warning'
        }
    ],

    // 统计信息配置
    dashboardWidgets: [
        'today_orders',
        'today_amount',
        'success_rate',
        'account_balance',
        'pending_settlements',
        'active_qr_codes'
    ]
};

// 导出配置
window.ProviderRoutes = ProviderRoutes;
console.log('✅ 码商路由配置已加载 (18个功能)'); 