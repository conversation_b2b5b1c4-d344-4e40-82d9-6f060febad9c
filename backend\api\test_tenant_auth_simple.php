<?php
/**
 * 简化的租户认证测试脚本
 * 用于排查500错误
 */

// 开启错误显示
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>简化租户认证测试</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
    .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
</style>";

echo "<h2>第一步：检查基础依赖</h2>";

// 1. 检查Auth.php
echo "<h3>1.1 检查Auth.php</h3>";
$authPath = dirname(__FILE__) . '/../utils/Auth.php';
if (file_exists($authPath)) {
    echo "<div class='test-result success'>✅ Auth.php 文件存在: $authPath</div>";
    try {
        require_once $authPath;
        echo "<div class='test-result success'>✅ Auth.php 加载成功</div>";
        if (class_exists('Auth')) {
            echo "<div class='test-result success'>✅ Auth 类存在</div>";
        } else {
            echo "<div class='test-result error'>❌ Auth 类不存在</div>";
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>❌ Auth.php 加载失败: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div class='test-result error'>❌ Auth.php 文件不存在: $authPath</div>";
}

// 2. 检查TenantManager.php
echo "<h3>1.2 检查TenantManager.php</h3>";
$tenantManagerPath = dirname(__FILE__) . '/../core/TenantManager.php';
if (file_exists($tenantManagerPath)) {
    echo "<div class='test-result success'>✅ TenantManager.php 文件存在: $tenantManagerPath</div>";
    try {
        require_once $tenantManagerPath;
        echo "<div class='test-result success'>✅ TenantManager.php 加载成功</div>";
        if (class_exists('TenantManager')) {
            echo "<div class='test-result success'>✅ TenantManager 类存在</div>";
        } else {
            echo "<div class='test-result error'>❌ TenantManager 类不存在</div>";
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>❌ TenantManager.php 加载失败: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div class='test-result error'>❌ TenantManager.php 文件不存在: $tenantManagerPath</div>";
}

// 3. 检查数据库连接
echo "<h3>1.3 检查数据库连接</h3>";
$databasePath = dirname(__FILE__) . '/../config/database.php';
if (file_exists($databasePath)) {
    echo "<div class='test-result success'>✅ database.php 文件存在: $databasePath</div>";
    try {
        require_once $databasePath;
        echo "<div class='test-result success'>✅ database.php 加载成功</div>";
        if (class_exists('Database')) {
            echo "<div class='test-result success'>✅ Database 类存在</div>";
            
            try {
                $db = Database::getInstance();
                echo "<div class='test-result success'>✅ 数据库连接成功</div>";
            } catch (Exception $e) {
                echo "<div class='test-result error'>❌ 数据库连接失败: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='test-result error'>❌ Database 类不存在</div>";
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>❌ database.php 加载失败: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div class='test-result error'>❌ database.php 文件不存在: $databasePath</div>";
}

echo "<h2>第二步：独立域名解析测试</h2>";

// 创建独立的域名解析函数
function testGetDomainInfo($domain) {
    try {
        $db = Database::getInstance();
        
        // 1. 检查是否是保留域名
        $reservedDomains = array('admin.top005.com', 'localhost', '127.0.0.1', '**************', 'top005.com');
        if (in_array($domain, $reservedDomains)) {
            return array(
                'tenant_type' => 'system_admin',
                'tenant_id' => 0,
                'platform_id' => null,
                'domain' => $domain,
                'brand_name' => '系统管理后台',
                'theme_config' => json_encode(array('primary_color' => '#1890ff'))
            );
        }
        
        // 2. 查询域名配置表
        $stmt = $db->query("
            SELECT 
                dc.tenant_type, 
                dc.tenant_id, 
                dc.platform_id,
                dc.custom_config,
                dc.status,
                p.platform_name,
                p.platform_code
            FROM domain_configs dc
            LEFT JOIN platforms p ON dc.platform_id = p.id
            WHERE dc.domain = ? AND dc.status = 'active'
            LIMIT 1
        ", array($domain));
        
        $config = $stmt->fetch();
        
        if ($config) {
            $config['custom_config'] = json_decode($config['custom_config'], true) ?: array();
            $config['domain'] = $domain;
            return $config;
        }
        
        // 3. 如果没有找到配置，尝试解析子域名模式
        if (preg_match('/^platform-([a-z0-9]+)\.top005\.com$/', $domain, $matches)) {
            $platformCode = $matches[1];
            $platform = $db->fetch("SELECT * FROM platforms WHERE platform_code = ? AND status = 'active'", array($platformCode));
            
            if ($platform) {
                return array(
                    'tenant_type' => 'platform_admin',
                    'tenant_id' => 0,
                    'platform_id' => $platform['id'],
                    'domain' => $domain,
                    'brand_name' => $platform['platform_name'],
                    'theme_config' => json_encode(array('primary_color' => '#722ed1'))
                );
            }
        }
        
        return null;
        
    } catch (Exception $e) {
        throw new Exception("域名解析失败: " . $e->getMessage());
    }
}

// 测试域名解析
$currentDomain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
echo "<div class='test-result info'>🔍 当前域名: $currentDomain</div>";

if (class_exists('Database')) {
    try {
        $domainInfo = testGetDomainInfo($currentDomain);
        if ($domainInfo) {
            echo "<div class='test-result success'>✅ 域名解析成功</div>";
            echo "<pre>" . print_r($domainInfo, true) . "</pre>";
        } else {
            echo "<div class='test-result error'>❌ 域名解析失败，返回null</div>";
        }
    } catch (Exception $e) {
        echo "<div class='test-result error'>❌ 域名解析异常: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<div class='test-result error'>❌ Database类不可用，跳过域名解析测试</div>";
}

echo "<h2>第三步：测试TenantAuth类加载</h2>";

// 测试TenantAuth
if (class_exists('Auth') && class_exists('TenantManager') && class_exists('Database')) {
    try {
        // 先创建一个简化版本的getDomainInfo函数供TenantAuth使用
        if (!function_exists('getDomainInfo')) {
            function getDomainInfo($domain) {
                return testGetDomainInfo($domain);
            }
        }
        
        require_once dirname(__FILE__) . '/../utils/TenantAuth.php';
        echo "<div class='test-result success'>✅ TenantAuth.php 加载成功</div>";
        
        if (class_exists('TenantAuth')) {
            echo "<div class='test-result success'>✅ TenantAuth 类存在</div>";
            
            try {
                $tenantAuth = new TenantAuth();
                echo "<div class='test-result success'>✅ TenantAuth 实例创建成功</div>";
                
                $domainInfo = $tenantAuth->getDomainInfo();
                if ($domainInfo) {
                    echo "<div class='test-result success'>✅ 获取域名信息成功</div>";
                    echo "<pre>" . print_r($domainInfo, true) . "</pre>";
                } else {
                    echo "<div class='test-result error'>❌ 获取域名信息失败</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='test-result error'>❌ TenantAuth 实例创建失败: " . $e->getMessage() . "</div>";
                echo "<div class='test-result error'>错误堆栈: <pre>" . $e->getTraceAsString() . "</pre></div>";
            }
        } else {
            echo "<div class='test-result error'>❌ TenantAuth 类不存在</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='test-result error'>❌ TenantAuth.php 加载失败: " . $e->getMessage() . "</div>";
        echo "<div class='test-result error'>错误堆栈: <pre>" . $e->getTraceAsString() . "</pre></div>";
    }
} else {
    echo "<div class='test-result error'>❌ 依赖类不完整，跳过TenantAuth测试</div>";
}

echo "<h2>第四步：测试登录功能</h2>";

if (class_exists('TenantAuth')) {
    try {
        $tenantAuth = new TenantAuth();
        
        // 测试登录（使用测试账户）
        echo "<div class='test-result info'>🔍 测试登录功能（使用测试账户）</div>";
        
        $loginResult = $tenantAuth->tenantLogin('admin', 'admin123');
        
        if ($loginResult['success']) {
            echo "<div class='test-result success'>✅ 租户登录测试成功</div>";
            echo "<pre>" . print_r($loginResult, true) . "</pre>";
        } else {
            echo "<div class='test-result error'>❌ 租户登录测试失败: " . $loginResult['message'] . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='test-result error'>❌ 登录测试异常: " . $e->getMessage() . "</div>";
        echo "<div class='test-result error'>错误堆栈: <pre>" . $e->getTraceAsString() . "</pre></div>";
    }
} else {
    echo "<div class='test-result error'>❌ TenantAuth类不可用，跳过登录测试</div>";
}

echo "<h2>测试完成</h2>";
echo "<div class='test-result info'>📋 简化测试完成，请查看上述结果定位问题</div>";
?> 