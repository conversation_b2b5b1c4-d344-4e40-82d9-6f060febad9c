<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal管理系统 - 模块化架构测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .module-card {
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .test-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><i class="fas fa-flask me-2"></i>PayPal管理系统模块化架构测试</h4>
                        <p class="mb-0">验证admin.js拆分后的模块是否正常加载和工作</p>
                    </div>
                    <div class="card-body">
                        <!-- 测试控制面板 -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-success" onclick="runAllTests()">
                                        <i class="fas fa-play me-2"></i>运行所有测试
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="clearResults()">
                                        <i class="fas fa-broom me-2"></i>清除结果
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="exportResults()">
                                        <i class="fas fa-download me-2"></i>导出报告
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="runDependencyCheck()">
                                        <i class="fas fa-sitemap me-2"></i>依赖检查
                                    </button>
                                    <button class="btn btn-warning" onclick="debugExports()">
                                        <i class="fas fa-bug me-2"></i>调试导出
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 测试结果概览 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-success">通过</h5>
                                        <h2 id="passedCount">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-danger">失败</h5>
                                        <h2 id="failedCount">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-warning">警告</h5>
                                        <h2 id="warningCount">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-info">总计</h5>
                                        <h2 id="totalCount">0</h2>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 核心模块测试 -->
                        <div class="module-card">
                            <div class="card-header">
                                <h5><i class="fas fa-cog me-2"></i>核心模块测试</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="testModule('core-utils')">
                                            <i class="fas fa-tools me-2"></i>核心工具模块
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="testModule('core-auth')">
                                            <i class="fas fa-lock me-2"></i>认证模块
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="testModule('app-initializer')">
                                            <i class="fas fa-rocket me-2"></i>应用初始化器
                                        </button>
                                    </div>
                                </div>
                                <div id="coreTestResults"></div>
                            </div>
                        </div>

                        <!-- 业务模块测试 -->
                        <div class="module-card">
                            <div class="card-header">
                                <h5><i class="fas fa-briefcase me-2"></i>业务模块测试</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="testModule('device-management')">
                                            <i class="fas fa-mobile-alt me-2"></i>设备管理
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="testModule('dashboard')">
                                            <i class="fas fa-tachometer-alt me-2"></i>仪表板
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="testModule('ui-components')">
                                            <i class="fas fa-puzzle-piece me-2"></i>UI组件
                                        </button>
                                    </div>
                                </div>
                                <div id="businessTestResults"></div>
                            </div>
                        </div>

                        <!-- 新增模块测试 -->
                        <div class="module-card">
                            <div class="card-header">
                                <h5><i class="fas fa-shield-alt me-2"></i>新增模块测试</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button class="btn btn-outline-warning btn-sm w-100 mb-2" onclick="testModule('security-management')">
                                            <i class="fas fa-shield-alt me-2"></i>安全管理模块
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button class="btn btn-outline-warning btn-sm w-100 mb-2" onclick="testModule('system-monitoring')">
                                            <i class="fas fa-chart-line me-2"></i>系统监控模块
                                        </button>
                                    </div>
                                </div>
                                <div id="newModuleTestResults"></div>
                            </div>
                        </div>

                        <!-- 控制台输出 -->
                        <div class="module-card">
                            <div class="card-header">
                                <h5><i class="fas fa-terminal me-2"></i>控制台输出</h5>
                                <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearConsole()">清除</button>
                            </div>
                            <div class="card-body">
                                <div id="consoleOutput" class="console-output">
                                    等待测试开始...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 核心模块静态加载 -->
    <script src="./js/modules/core/utils.js"></script>
    <script src="./js/modules/core/module-loader.js"></script>
    <script src="./js/modules/core/auth.js"></script>
    <script src="./js/modules/core/ui-manager.js"></script>
    
    <!-- 依赖检查脚本 -->
    <script src="./js/check-dependencies.js"></script>
    
    <script>
        // 调试导出状态
        function debugExports() {
            console.log('=== 调试导出状态 ===');
            const expectedExports = {
                'core/utils': ['CONFIG', 'PerformanceMonitor', 'CacheManager', 'AdminUtils'],
                'core/module-loader': ['ModuleLoader']
            };
            
            for (const [module, exports] of Object.entries(expectedExports)) {
                console.log(`🔍 检查模块: ${module}`);
                for (const exportName of exports) {
                    const exists = typeof window[exportName] !== 'undefined';
                    const type = typeof window[exportName];
                    console.log(`  ${exists ? '✅' : '❌'} ${exportName}: ${type} ${exists ? '(存在)' : '(缺失)'}`);
                    if (exists && window[exportName]) {
                        console.log(`    值:`, window[exportName]);
                    }
                }
            }
            
            // 检查UtilsModule
            if (window.UtilsModule) {
                console.log('📦 UtilsModule 内容:');
                for (const [key, value] of Object.entries(window.UtilsModule)) {
                    console.log(`  ${key}: ${typeof value}`);
                }
            }
            
            // 检查ModuleLoaderModule
            if (window.ModuleLoaderModule) {
                console.log('📦 ModuleLoaderModule 内容:');
                for (const [key, value] of Object.entries(window.ModuleLoaderModule)) {
                    console.log(`  ${key}: ${typeof value}`);
                }
            }
        }

        // 测试结果统计
        let testStats = {
            passed: 0,
            failed: 0,
            warning: 0,
            total: 0
        };

        // 控制台日志重定向
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function logToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const consoleOutput = document.getElementById('consoleOutput');
            const colorMap = {
                'log': '#333',
                'error': '#dc3545',
                'warn': '#ffc107',
                'info': '#17a2b8'
            };
            
            consoleOutput.innerHTML += `<span style="color: ${colorMap[type] || '#333'}">[${timestamp}] ${message}</span>\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        // 重写console方法
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToConsole('ERROR: ' + args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToConsole('WARN: ' + args.join(' '), 'warn');
        };

        // 更新统计数据
        function updateStats() {
            document.getElementById('passedCount').textContent = testStats.passed;
            document.getElementById('failedCount').textContent = testStats.failed;
            document.getElementById('warningCount').textContent = testStats.warning;
            document.getElementById('totalCount').textContent = testStats.total;
        }

        // 显示测试结果
        function showTestResult(containerId, testName, status, message) {
            const container = document.getElementById(containerId);
            const resultClass = status === 'success' ? 'test-success' : 
                              status === 'error' ? 'test-error' : 'test-warning';
            const icon = status === 'success' ? 'check-circle' : 
                        status === 'error' ? 'times-circle' : 'exclamation-triangle';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${resultClass}`;
            resultDiv.innerHTML = `
                <i class="fas fa-${icon} me-2"></i>
                <strong>${testName}:</strong> ${message}
            `;
            
            container.appendChild(resultDiv);
            
            // 更新统计
            testStats.total++;
            if (status === 'success') testStats.passed++;
            else if (status === 'error') testStats.failed++;
            else testStats.warning++;
            
            updateStats();
        }

        // 测试单个模块
        async function testModule(moduleType) {
            console.log(`开始测试模块: ${moduleType}`);
            
            try {
                switch (moduleType) {
                    case 'core-utils':
                        await testCoreUtils();
                        break;
                    case 'core-auth':
                        await testCoreAuth();
                        break;
                    case 'app-initializer':
                        await testAppInitializer();
                        break;
                    case 'device-management':
                        await testDeviceManagement();
                        break;
                    case 'dashboard':
                        await testDashboard();
                        break;
                    case 'ui-components':
                        await testUIComponents();
                        break;
                    case 'security-management':
                        await testSecurityManagement();
                        break;
                    case 'system-monitoring':
                        await testSystemMonitoring();
                        break;
                    default:
                        throw new Error(`未知的模块类型: ${moduleType}`);
                }
            } catch (error) {
                console.error(`模块 ${moduleType} 测试失败:`, error);
            }
        }

        // 核心工具模块测试
        async function testCoreUtils() {
            try {
                // 核心工具模块已经静态加载，直接测试
                console.log('AdminUtils initialized');
                
                // 检查全局对象是否存在
                if (typeof window.CONFIG !== 'undefined') {
                    showTestResult('coreTestResults', '核心工具模块', 'success', 'CONFIG全局配置加载成功');
                } else {
                    showTestResult('coreTestResults', '核心工具模块', 'error', 'CONFIG全局配置未找到');
                }

                if (typeof window.AdminUtils !== 'undefined') {
                    showTestResult('coreTestResults', '核心工具模块', 'success', 'AdminUtils类加载成功');
                } else {
                    showTestResult('coreTestResults', '核心工具模块', 'error', 'AdminUtils类未找到');
                }

                if (typeof window.PerformanceMonitor !== 'undefined') {
                    showTestResult('coreTestResults', '核心工具模块', 'success', 'PerformanceMonitor模块加载成功');
                } else {
                    showTestResult('coreTestResults', '核心工具模块', 'warning', 'PerformanceMonitor模块未找到');
                }
                
                console.log('✅ 核心工具模块加载完成 (已合并core.js功能)');

            } catch (error) {
                showTestResult('coreTestResults', '核心工具模块', 'error', `测试失败: ${error.message}`);
            }
        }

        // 认证模块测试
        async function testCoreAuth() {
            try {
                // 认证模块已经静态加载，直接测试
                if (typeof window.AuthManager !== 'undefined') {
                    showTestResult('coreTestResults', '认证模块', 'success', 'AuthManager类加载成功');
                    console.log('✅ Auth模块已导出到全局作用域');
                    
                    // 测试实例化
                    try {
                        const authManager = new window.AuthManager();
                        showTestResult('coreTestResults', '认证模块', 'success', 'AuthManager实例创建成功');
                    } catch (e) {
                        showTestResult('coreTestResults', '认证模块', 'error', `实例创建失败: ${e.message}`);
                    }
                } else {
                    showTestResult('coreTestResults', '认证模块', 'error', 'AuthManager类未找到');
                }

            } catch (error) {
                showTestResult('coreTestResults', '认证模块', 'error', `测试失败: ${error.message}`);
            }
        }

        // 应用初始化器测试
        async function testAppInitializer() {
            try {
                // 核心模块已经静态加载，只需要加载app-initializer
                if (!document.querySelector('script[src="./js/modules/core/app-initializer.js"]')) {
                    console.log('加载app-initializer.js...');
                    const script = document.createElement('script');
                    script.src = './js/modules/core/app-initializer.js';
                    document.head.appendChild(script);
                    
                    await new Promise((resolve, reject) => {
                        script.onload = resolve;
                        script.onerror = reject;
                        setTimeout(reject, 5000);
                    });
                    
                    // 等待一下让脚本执行完成
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // 检查AppInitializer类
                if (typeof window.AppInitializer !== 'undefined') {
                    showTestResult('coreTestResults', '应用初始化器', 'success', 'AppInitializer类已导出到全局作用域');
                } else {
                    showTestResult('coreTestResults', '应用初始化器', 'error', 'AppInitializer类未找到');
                }
                
                // 检查appInitializer实例
                if (typeof window.appInitializer !== 'undefined') {
                    showTestResult('coreTestResults', '应用初始化器', 'success', '应用初始化器实例已创建');
                    console.log('📋 应用初始化器已加载');
                } else {
                    showTestResult('coreTestResults', '应用初始化器', 'warning', '应用初始化器实例未找到');
                }

            } catch (error) {
                showTestResult('coreTestResults', '应用初始化器', 'error', `加载失败: ${error.message}`);
            }
        }

        // 设备管理模块测试
        async function testDeviceManagement() {
            try {
                // 检查是否已加载，避免重复加载
                if (!document.querySelector('script[src="./js/modules/device/management.js"]')) {
                    const script = document.createElement('script');
                    script.src = './js/modules/device/management.js';
                    document.head.appendChild(script);
                    
                    await new Promise((resolve, reject) => {
                        script.onload = resolve;
                        script.onerror = reject;
                        setTimeout(reject, 5000);
                    });
                }

                if (typeof window.DeviceManager !== 'undefined') {
                    showTestResult('businessTestResults', '设备管理', 'success', 'DeviceManager类加载成功');
                } else {
                    showTestResult('businessTestResults', '设备管理', 'error', 'DeviceManager类未找到');
                }

            } catch (error) {
                showTestResult('businessTestResults', '设备管理', 'error', `加载失败: ${error.message}`);
            }
        }

        // 仪表板模块测试
        async function testDashboard() {
            try {
                // 检查是否已加载，避免重复加载
                if (!document.querySelector('script[src="./js/modules/dashboard.js"]')) {
                    console.log('开始加载dashboard.js文件...');
                    const script = document.createElement('script');
                    script.src = './js/modules/dashboard.js';
                    
                    // 添加详细的错误处理
                    script.onerror = (event) => {
                        console.error('Dashboard脚本加载失败:', event);
                    };
                    
                    document.head.appendChild(script);
                    
                    await new Promise((resolve, reject) => {
                        script.onload = () => {
                            console.log('Dashboard脚本加载完成');
                            resolve();
                        };
                        script.onerror = (event) => {
                            console.error('Dashboard脚本加载错误:', event);
                            reject(new Error('脚本加载失败'));
                        };
                        setTimeout(() => reject(new Error('加载超时')), 5000);
                    });
                    
                    // 等待一下让脚本执行完成
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                console.log('检查DashboardManager是否存在:', typeof window.DashboardManager);
                
                if (typeof window.DashboardManager !== 'undefined') {
                    showTestResult('businessTestResults', '仪表板', 'success', 'DashboardManager类加载成功');
                } else {
                    showTestResult('businessTestResults', '仪表板', 'error', 'DashboardManager类未找到');
                }

            } catch (error) {
                console.error('Dashboard测试异常:', error);
                showTestResult('businessTestResults', '仪表板', 'error', `加载失败: ${error.message}`);
            }
        }

        // UI组件模块测试
        async function testUIComponents() {
            try {
                // 检查是否已加载，避免重复加载
                if (!document.querySelector('script[src="./js/modules/ui-components.js"]')) {
                    console.log('开始加载ui-components.js文件...');
                    const script = document.createElement('script');
                    script.src = './js/modules/ui-components.js';
                    
                    // 添加详细的错误处理
                    script.onerror = (event) => {
                        console.error('UI组件脚本加载失败:', event);
                    };
                    
                    document.head.appendChild(script);
                    
                    await new Promise((resolve, reject) => {
                        script.onload = () => {
                            console.log('UI组件脚本加载完成');
                            resolve();
                        };
                        script.onerror = (event) => {
                            console.error('UI组件脚本加载错误:', event);
                            reject(new Error('脚本加载失败'));
                        };
                        setTimeout(() => reject(new Error('加载超时')), 5000);
                    });
                    
                    // 等待一下让脚本执行完成
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                console.log('检查UIManager是否存在:', typeof window.UIManager);

                if (typeof window.UIManager !== 'undefined') {
                    showTestResult('businessTestResults', 'UI组件', 'success', 'UIManager类加载成功');
                } else {
                    showTestResult('businessTestResults', 'UI组件', 'error', 'UIManager类未找到');
                }

            } catch (error) {
                console.error('UI组件测试异常:', error);
                showTestResult('businessTestResults', 'UI组件', 'error', `加载失败: ${error.message}`);
            }
        }

        // 安全管理模块测试
        async function testSecurityManagement() {
            try {
                // 检查是否已加载，避免重复加载
                if (!document.querySelector('script[src="./js/modules/security/security-management.js"]')) {
                    const script = document.createElement('script');
                    script.src = './js/modules/security/security-management.js';
                    document.head.appendChild(script);
                    
                    await new Promise((resolve, reject) => {
                        script.onload = resolve;
                        script.onerror = reject;
                        setTimeout(reject, 5000);
                    });
                }

                if (typeof window.SecurityModuleManager !== 'undefined') {
                    showTestResult('newModuleTestResults', '安全管理模块', 'success', 'SecurityModuleManager类加载成功');
                } else {
                    showTestResult('newModuleTestResults', '安全管理模块', 'error', 'SecurityModuleManager类未找到');
                }

                // 检查子模块
                const subModules = ['RiskControlManager', 'BlacklistManager', 'SecurityLogManager', 'TOTPManager'];
                subModules.forEach(moduleName => {
                    if (typeof window[moduleName] !== 'undefined') {
                        showTestResult('newModuleTestResults', '安全管理模块', 'success', `${moduleName}类加载成功`);
                    } else {
                        showTestResult('newModuleTestResults', '安全管理模块', 'warning', `${moduleName}类未找到（需要从admin.js提取实现）`);
                    }
                });

            } catch (error) {
                showTestResult('newModuleTestResults', '安全管理模块', 'error', `加载失败: ${error.message}`);
            }
        }

        // 系统监控模块测试
        async function testSystemMonitoring() {
            try {
                // 检查是否已加载，避免重复加载
                if (!document.querySelector('script[src="./js/modules/system/system-monitoring.js"]')) {
                    const script = document.createElement('script');
                    script.src = './js/modules/system/system-monitoring.js';
                    document.head.appendChild(script);
                    
                    await new Promise((resolve, reject) => {
                        script.onload = resolve;
                        script.onerror = reject;
                        setTimeout(reject, 5000);
                    });
                }

                if (typeof window.SystemMonitoringManager !== 'undefined') {
                    showTestResult('newModuleTestResults', '系统监控模块', 'success', 'SystemMonitoringManager类加载成功');
                } else {
                    showTestResult('newModuleTestResults', '系统监控模块', 'error', 'SystemMonitoringManager类未找到');
                }

                // 检查子模块
                const subModules = ['PerformanceManager', 'NotificationManager', 'DailyReportManager'];
                subModules.forEach(moduleName => {
                    if (typeof window[moduleName] !== 'undefined') {
                        showTestResult('newModuleTestResults', '系统监控模块', 'success', `${moduleName}类加载成功`);
                    } else {
                        showTestResult('newModuleTestResults', '系统监控模块', 'warning', `${moduleName}类未找到（需要从admin.js提取实现）`);
                    }
                });

            } catch (error) {
                showTestResult('newModuleTestResults', '系统监控模块', 'error', `加载失败: ${error.message}`);
            }
        }

        // 运行所有测试
        async function runAllTests() {
            console.log('开始运行所有模块测试...');
            clearResults();
            
            const tests = [
                'core-utils', 'core-auth', 'app-initializer',
                'device-management', 'dashboard', 'ui-components',
                'security-management', 'system-monitoring'
            ];
            
            for (const test of tests) {
                await testModule(test);
                await new Promise(resolve => setTimeout(resolve, 500)); // 间隔500ms
            }
            
            console.log('所有测试完成！');
            
            // 生成测试报告
            generateTestReport();
        }

        // 清除结果
        function clearResults() {
            document.getElementById('coreTestResults').innerHTML = '';
            document.getElementById('businessTestResults').innerHTML = '';
            document.getElementById('newModuleTestResults').innerHTML = '';
            
            testStats = { passed: 0, failed: 0, warning: 0, total: 0 };
            updateStats();
        }

        // 清除控制台
        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = '控制台已清除...\n';
        }

        // 生成测试报告
        function generateTestReport() {
            const successRate = testStats.total > 0 ? ((testStats.passed / testStats.total) * 100).toFixed(1) : 0;
            
            console.log('=== 测试报告 ===');
            console.log(`总测试数: ${testStats.total}`);
            console.log(`通过: ${testStats.passed}`);
            console.log(`失败: ${testStats.failed}`);
            console.log(`警告: ${testStats.warning}`);
            console.log(`成功率: ${successRate}%`);
            
            if (testStats.failed === 0) {
                console.log('🎉 所有核心功能测试通过！模块化架构工作正常。');
            } else {
                console.log('⚠️ 部分测试失败，请检查相关模块。');
            }
        }

        // 导出测试结果
        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                stats: testStats,
                console: document.getElementById('consoleOutput').textContent
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `module-test-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            console.log('测试报告已导出');
        }

                 // 运行依赖检查
         async function runDependencyCheck() {
             console.log('🔧 开始运行依赖检查...');
             
             if (typeof window.checkDependencies === 'function') {
                 try {
                     const result = await window.checkDependencies();
                     
                     if (result.success) {
                         console.log('✅ 依赖检查通过！');
                     } else {
                         console.log('⚠️ 依赖检查发现问题，请查看详细报告');
                     }
                 } catch (error) {
                     console.error('❌ 依赖检查失败:', error);
                 }
             } else {
                 console.error('❌ 依赖检查脚本未加载');
             }
         }

         // 页面加载完成后的初始化
         window.addEventListener('load', function() {
             console.log('PayPal管理系统模块化架构测试页面已加载');
             console.log('点击"运行所有测试"开始验证模块是否正常工作');
             console.log('点击"依赖检查"验证模块依赖关系');
         });
    </script>
</body>
</html> 