<?php
/**
 * admin.php多租户功能测试脚本
 * 测试改造后的admin.php是否正常工作
 */

// 开启错误显示
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>admin.php多租户功能测试</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
    .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .json { background-color: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
</style>";

echo "<h2>第一步：测试域名解析接口</h2>";

// 测试域名解析
$testDomain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'top005.com';
echo "<div class='test-result info'>🔍 测试域名: $testDomain</div>";

$postData = json_encode(array('action' => 'resolve_domain', 'domain' => $testDomain));
$context = stream_context_create(array(
    'http' => array(
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => $postData
    )
));

    $adminUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/api/admin.php';
$response = file_get_contents($adminUrl, false, $context);

if ($response !== false) {
    $result = json_decode($response, true);
    if ($result) {
        echo "<div class='test-result success'>✅ 域名解析接口测试成功</div>";
        echo "<div class='json'>返回结果:\n" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</div>";
    } else {
        echo "<div class='test-result error'>❌ 域名解析接口返回格式错误</div>";
        echo "<div class='json'>原始返回:\n" . htmlspecialchars($response) . "</div>";
    }
} else {
    echo "<div class='test-result error'>❌ 域名解析接口请求失败</div>";
}

echo "<h2>第二步：测试未授权访问</h2>";

// 测试未授权访问
$postData = json_encode(array('action' => 'get_users'));
$context = stream_context_create(array(
    'http' => array(
        'method' => 'POST',
        'header' => "Content-Type: application/json\r\n",
        'content' => $postData,
        'ignore_errors' => true
    )
));

$response = file_get_contents($adminUrl, false, $context);
$httpCode = null;
if (isset($http_response_header)) {
    foreach ($http_response_header as $header) {
        if (strpos($header, 'HTTP/') === 0) {
            $httpCode = intval(substr($header, 9, 3));
        }
    }
}

if ($httpCode === 401) {
    echo "<div class='test-result success'>✅ 未授权访问控制正常 (HTTP 401)</div>";
    $result = json_decode($response, true);
    if ($result) {
        echo "<div class='json'>返回结果:\n" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</div>";
    }
} else {
    echo "<div class='test-result error'>❌ 未授权访问控制异常 (HTTP $httpCode)</div>";
    echo "<div class='json'>原始返回:\n" . htmlspecialchars($response) . "</div>";
}

echo "<h2>第三步：测试工具函数</h2>";

// 测试工具函数是否能正常加载
try {
    // 模拟租户上下文
    $mockDomainInfo = array(
        'tenant_type' => 'system_admin',
        'tenant_id' => 0,
        'platform_id' => null
    );
    
    $mockUser = array(
        'id' => 1,
        'user_type' => 'admin',
        'username' => 'admin'
    );
    
    // 由于工具函数在admin.php中，我们需要通过包含文件来测试
    ob_start();
    $_GET['action'] = 'resolve_domain'; // 设置一个安全的action
    $_POST['domain'] = $testDomain;
    
    // 捕获admin.php的输出
    include dirname(__FILE__) . '/admin.php';
    $adminOutput = ob_get_clean();
    
    // 解析输出
    $adminResult = json_decode($adminOutput, true);
    if ($adminResult && isset($adminResult['data'])) {
        echo "<div class='test-result success'>✅ admin.php加载成功</div>";
        echo "<div class='json'>admin.php输出:\n" . json_encode($adminResult, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</div>";
    } else {
        echo "<div class='test-result warning'>⚠️ admin.php输出格式异常</div>";
        echo "<div class='json'>原始输出:\n" . htmlspecialchars($adminOutput) . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='test-result error'>❌ admin.php加载失败: " . $e->getMessage() . "</div>";
}

echo "<h2>第四步：功能模块路由测试</h2>";

// 测试各个功能模块的路由
$testRoutes = array(
    'dashboard' => '仪表板',
    'users' => '用户管理',
    'merchants' => '商户管理',
    'orders' => '订单管理',
    'financial' => '财务管理'
);

foreach ($testRoutes as $route => $name) {
    echo "<h3>测试路由: $route ($name)</h3>";
    
    $postData = json_encode(array('action' => $route));
    $context = stream_context_create(array(
        'http' => array(
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => $postData,
            'ignore_errors' => true
        )
    ));
    
    $response = file_get_contents($adminUrl, false, $context);
    $httpCode = null;
    if (isset($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (strpos($header, 'HTTP/') === 0) {
                $httpCode = intval(substr($header, 9, 3));
            }
        }
    }
    
    if ($httpCode === 401) {
        echo "<div class='test-result success'>✅ 路由 $route 正常 (需要认证)</div>";
    } elseif ($httpCode === 404) {
        echo "<div class='test-result warning'>⚠️ 路由 $route 模块文件不存在</div>";
    } elseif ($httpCode === 500) {
        echo "<div class='test-result error'>❌ 路由 $route 服务器错误</div>";
        echo "<div class='json'>错误信息:\n" . htmlspecialchars($response) . "</div>";
    } else {
        echo "<div class='test-result info'>ℹ️ 路由 $route HTTP状态: $httpCode</div>";
    }
}

echo "<h2>第五步：环境信息</h2>";

echo "<table>";
echo "<tr><th>项目</th><th>值</th></tr>";
echo "<tr><td>当前域名</td><td>" . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'unknown') . "</td></tr>";
echo "<tr><td>请求URI</td><td>" . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : 'unknown') . "</td></tr>";
echo "<tr><td>PHP版本</td><td>" . phpversion() . "</td></tr>";
echo "<tr><td>服务器时间</td><td>" . date('Y-m-d H:i:s') . "</td></tr>";
echo "</table>";

echo "<h2>测试完成</h2>";
echo "<div class='test-result info'>📋 admin.php多租户功能测试完成</div>";
?> 