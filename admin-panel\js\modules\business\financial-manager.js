/**
 * 财务管理器
 * 从admin.js第7260-8010行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class FinancialManager {
    constructor() {
        this.currentPage = 1;
        this.currentMerchantId = null;
        this.auth = new AuthManager();
    }
    async initialize() {
        this.initializeEventListeners();
        // 如果是管理员，加载商户选项
        const user = this.auth.getUser();
        if (user && user.user_type === 'admin') {
            await this.loadMerchantOptions();
        }
        await this.loadFinancialData();
    }
    initializeEventListeners() {
        // 日期范围筛选
        const dateRangeFilter = document.getElementById('financialDateRange');
        if (dateRangeFilter) {
            dateRangeFilter.addEventListener('change', () => {
                this.toggleCustomDateRange();
                this.applyFilters();
            });
        }
        // 商户筛选
        const merchantFilter = document.getElementById('financialMerchantFilter');
        if (merchantFilter) {
            merchantFilter.addEventListener('change', () => this.applyFilters());
        }
        // 数据类型筛选
        const dataTypeFilter = document.getElementById('financialDataType');
        if (dataTypeFilter) {
            dataTypeFilter.addEventListener('change', () => this.applyFilters());
        }
        // 自定义日期范围
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        if (startDate && endDate) {
            startDate.addEventListener('change', () => this.applyFilters());
            endDate.addEventListener('change', () => this.applyFilters());
        }
    }
    async loadFinancialData() {
        this.showLoadingState();
        try {
            const params = new URLSearchParams({
                type: this.getDataType(),
                date_range: this.getDateRange(),
                page: this.currentPage || 1,
                limit: 20
            });
            const merchantId = this.getMerchantId();
            if (merchantId) {
                params.append('merchant_id',
                merchantId);
            }
            // 添加自定义日期范围
            if (this.getDateRange() === 'custom') {
                const startDate = document.getElementById('startDate')?.value;
                const endDate = document.getElementById('endDate')?.value;
                if (startDate && endDate) {
                    params.append('start_date',
                    startDate);
                    params.append('end_date',
                    endDate);
                }
            }
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=financial&${
                params
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200) {
                this.renderFinancialData(data.data);
            } else {
                this.showErrorState(data.message || '加载财务数据失败');
            }
        } catch (error) {
            console.error('Load financial data error:',
            error);
            this.showErrorState('网络错误，请重试');
        }
    }
    renderFinancialData(data) {
        const dataType = this.getDataType();
        switch (dataType) {
            case 'overview':
            this.renderOverviewData(data);
            break;
            case 'transactions':
            this.renderTransactionData(data);
            break;
            case 'settlement':
            this.renderSettlementData(data);
            break;
            case 'statistics':
            this.renderStatisticsData(data);
            break;
            default:
            this.renderOverviewData(data);
        }
    }
    renderOverviewData(data) {
        const {
            overview,
            today,
            trend
        } = data;
        // 渲染概览卡片
        this.renderOverviewCards(overview,
        today);
        // 渲染趋势图表
        this.renderTrendChart(trend);
        // 隐藏详细数据表格
        const dataSection = document.querySelector('.data-section');
        if (dataSection) {
            dataSection.style.display = 'none';
        }
    }
    renderOverviewCards(overview,
    today) {
        const container = document.getElementById('financialOverview');
        if (!container) return;
        const totalAmount = parseFloat(overview.total_amount || 0);
        const totalServiceFee = parseFloat(overview.total_service_fee || 0);
        const todayAmount = parseFloat(today.today_amount || 0);
        const successRate = parseFloat(overview.success_rate || 0);
        container.innerHTML = `
        <div class="row g-3">
        <div class="col-md-3">
        <div class="card border-0 bg-primary text-white">
        <div class="card-body">
        <div class="d-flex justify-content-between">
        <div>
        <h6 class="card-title mb-0">总交易额</h6>
        <h4 class="mb-0">¥${
            totalAmount.toFixed(2)
        }</h4>
        <small class="opacity-75">今日: ¥${
            todayAmount.toFixed(2)
        }</small>
        </div>
        <i class="fas fa-chart-line fa-2x opacity-50"></i>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 bg-success text-white">
        <div class="card-body">
        <div class="d-flex justify-content-between">
        <div>
        <h6 class="card-title mb-0">服务费收入</h6>
        <h4 class="mb-0">¥${
            totalServiceFee.toFixed(2)
        }</h4>
        <small class="opacity-75">今日: ¥${
            parseFloat(today.today_service_fee || 0).toFixed(2)
        }</small>
        </div>
        <i class="fas fa-coins fa-2x opacity-50"></i>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 bg-info text-white">
        <div class="card-body">
        <div class="d-flex justify-content-between">
        <div>
        <h6 class="card-title mb-0">成功交易</h6>
        <h4 class="mb-0">${
            overview.successful_transactions || 0
        }</h4>
        <small class="opacity-75">今日: ${
            today.today_transactions || 0
        }</small>
        </div>
        <i class="fas fa-check-circle fa-2x opacity-50"></i>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 bg-warning text-white">
        <div class="card-body">
        <div class="d-flex justify-content-between">
        <div>
        <h6 class="card-title mb-0">成功率</h6>
        <h4 class="mb-0">${
            successRate.toFixed(1)
        }%</h4>
        <small class="opacity-75">总计: ${
            overview.total_transactions || 0
        } 笔</small>
        </div>
        <i class="fas fa-percentage fa-2x opacity-50"></i>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    renderTrendChart(trendData) {
        const container = document.getElementById('financialTrendChart');
        if (!container || !trendData || trendData.length === 0) return;
        // 简化的图表显示（实际项目中可以使用Chart.js等图表库）
        const chartHTML = `
        <div class="card">
        <div class="card-header">
        <h6 class="card-title mb-0">最近7天交易趋势</h6>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-sm">
        <thead>
        <tr>
        <th>日期</th>
        <th>交易笔数</th>
        <th>交易金额</th>
        <th>服务费</th>
        </tr>
        </thead>
        <tbody>
        ${
            trendData.map(item => `
            <tr>
            <td>${
                item.date
            }</td>
            <td>${
                item.transaction_count
            }</td>
            <td>¥${
                parseFloat(item.daily_amount || 0).toFixed(2)
            }</td>
            <td>¥${
                parseFloat(item.daily_service_fee || 0).toFixed(2)
            }</td>
            </tr>
            `).join('')
        }
        </tbody>
        </table>
        </div>
        </div>
        </div>
        `;
        container.innerHTML = chartHTML;
    }
    renderTransactionData(data) {
        const {
            transactions,
            pagination
        } = data;
        // 显示详细数据表格
        const dataSection = document.querySelector('.data-section');
        if (dataSection) {
            dataSection.style.display = 'block';
        }
        // 隐藏图表
        const chartsSection = document.querySelector('.charts-section');
        if (chartsSection) {
            chartsSection.style.display = 'none';
        }
        this.renderDataTable(transactions, 'transactions');
        this.renderPagination(pagination);
    }
    renderSettlementData(data) {
        const {
            settlements,
            pagination
        } = data;
        const dataSection = document.querySelector('.data-section');
        if (dataSection) {
            dataSection.style.display = 'block';
        }
        const chartsSection = document.querySelector('.charts-section');
        if (chartsSection) {
            chartsSection.style.display = 'none';
        }
        this.renderDataTable(settlements, 'settlements');
        this.renderPagination(pagination);
    }
    renderStatisticsData(data) {
        const {
            statistics,
            charts
        } = data;
        // 隐藏详细数据表格
        const dataSection = document.querySelector('.data-section');
        if (dataSection) {
            dataSection.style.display = 'none';
        }
        // 显示图表
        const chartsSection = document.querySelector('.charts-section');
        if (chartsSection) {
            chartsSection.style.display = 'block';
        }
        this.renderOverviewCards(statistics);
        this.renderStatisticsCharts(charts);
    }
    renderDataTable(data,
    type) {
        const headerContainer = document.getElementById('dataTableHeader');
        const bodyContainer = document.getElementById('dataTableBody');
        const countContainer = document.getElementById('dataCount');
        if (!headerContainer || !bodyContainer) return;
        // 更新数据计数
        if (countContainer) {
            countContainer.textContent = data ? data.length : 0;
        }
        // 根据数据类型设置表头
        const headers = this.getTableHeaders(type);
        headerContainer.innerHTML = `<tr>${
            headers.map(h => `<th>${
                h
            }</th>`).join('')
        }</tr>`;
        // 渲染数据行
        if (!data || data.length === 0) {
            bodyContainer.innerHTML = `
            <tr>
            <td colspan="${
                headers.length
            }" class="text-center py-4">
            <div class="text-muted">
            <i class="bi bi-inbox fa-3x mb-3"></i>
            <p>暂无数据</p>
            </div>
            </td>
            </tr>
            `;
            return;
        }
        bodyContainer.innerHTML = data.map(item => this.renderDataRow(item,
        type)).join('');
    }
    getTableHeaders(type) {
        switch (type) {
            case 'transactions':
            return ['交易ID', '商户', '金额', '手续费', '状态', '创建时间', '操作'];
            case 'settlements':
            return ['结算ID', '商户', '结算金额', '手续费', '状态', '结算时间', '操作'];
            default:
            return ['ID', '数据', '时间'];
        }
    }
    renderDataRow(item,
    type) {
        switch (type) {
            case 'transactions':
            return `
            <tr>
            <td>${
                item.transaction_id || '-'
            }</td>
            <td>${
                item.merchant_name || '-'
            }</td>
            <td>¥${
                parseFloat(item.amount || 0).toFixed(2)
            }</td>
            <td>¥${
                parseFloat(item.service_fee || 0).toFixed(2)
            }</td>
            <td>${
                this.renderStatusBadge(item.status)
            }</td>
            <td>${
                this.formatDateTime(item.created_at)
            }</td>
            </tr>
            `;
            case 'settlements':
            return `
            <tr>
            <td>${
                item.settlement_id || '-'
            }</td>
            <td>${
                item.merchant_name || '-'
            }</td>
            <td>¥${
                parseFloat(item.settlement_amount || 0).toFixed(2)
            }</td>
            <td>¥${
                parseFloat(item.service_fee || 0).toFixed(2)
            }</td>
            <td>${
                this.renderStatusBadge(item.status)
            }</td>
            <td>${
                this.formatDateTime(item.settlement_time)
            }</td>
            </tr>
            `;
            default:
            return `<tr><td colspan="6">未知数据类型</td></tr>`;
        }
    }
    renderStatusBadge(status) {
        const statusMap = {
            'completed': {
                text: '已完成',
                class: 'bg-success'
            },
            'pending': {
                text: '处理中',
                class: 'bg-warning'
            },
            'failed': {
                text: '失败',
                class: 'bg-danger'
            },
            'paid': {
                text: '已支付',
                class: 'bg-success'
            },
            'unpaid': {
                text: '未支付',
                class: 'bg-secondary'
            }
        };
        const statusInfo = statusMap[status] || {
            text: status,
            class: 'bg-secondary'
        };
        return `<span class="badge ${
            statusInfo.class
        }">${
            statusInfo.text
        }</span>`;
    }
    renderPagination(pagination) {
        const container = document.getElementById('financialPagination');
        if (!container || !pagination) return;
        const {
            current_page,
            total_pages,
            total_records
        } = pagination;
        if (total_pages <= 1) {
            container.innerHTML = '';
            return;
        }
        let paginationHTML = '<nav><ul class="pagination justify-content-center mb-0">';
        // 上一页
        if (current_page > 1) {
            paginationHTML += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.financialManager.loadPage(${
                current_page - 1
            })">
            <i class="bi bi-chevron-left"></i>
            </a>
            </li>
            `;
        }
        // 页码
        const startPage = Math.max(1,
        current_page - 2);
        const endPage = Math.min(total_pages,
        current_page + 2);
        for (let i = startPage;
        i <= endPage;
        i++) {
            paginationHTML += `
            <li class="page-item ${
                i === current_page ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.financialManager.loadPage(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        // 下一页
        if (current_page < total_pages) {
            paginationHTML += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.financialManager.loadPage(${
                current_page + 1
            })">
            <i class="bi bi-chevron-right"></i>
            </a>
            </li>
            `;
        }
        paginationHTML += '</ul></nav>';
        container.innerHTML = paginationHTML;
    }
    loadPage(page) {
        this.currentPage = page;
        this.loadFinancialData();
    }
    toggleCustomDateRange() {
        const dateRange = this.getDateRange();
        const customRange = document.getElementById('customDateRange');
        if (customRange) {
            customRange.style.display = dateRange === 'custom' ? 'block' : 'none';
        }
    }
    async loadMerchantOptions() {
        const merchantFilter = document.getElementById('financialMerchantFilter');
        if (!merchantFilter) return;
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=merchants&limit=1000`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200) {
                merchantFilter.innerHTML = '<option value="">全部商户</option>';
                data.data.merchants.forEach(merchant => {
                    merchantFilter.innerHTML += `
                    <option value="${
                        merchant.id
                    }">${
                        merchant.company_name || merchant.real_name
                    } (${
                        merchant.username
                    })</option>
                    `;
                });
            }
        } catch (error) {
            console.error('Load merchants error:',
            error);
        }
    }
    exportData() {
        const dataType = this.getDataType();
        const dateRange = this.getDateRange();
        const merchantId = this.getMerchantId();
        const params = new URLSearchParams({
            action: 'export_financial',
            type: dataType,
            date_range: dateRange
        });
        if (merchantId) {
            params.append('merchant_id',
            merchantId);
        }
        if (dateRange === 'custom') {
            const startDate = document.getElementById('startDate')?.value;
            const endDate = document.getElementById('endDate')?.value;
            if (startDate && endDate) {
                params.append('start_date',
                startDate);
                params.append('end_date',
                endDate);
            }
        }
        const url = `${
            CONFIG.API_BASE_URL
        }/admin.php?${
            params
        }&token=${
            this.auth.getToken()
        }`;
        window.open(url, '_blank');
    }
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    getDateRange() {
        const dateRangeFilter = document.getElementById('financialDateRange');
        return dateRangeFilter ? dateRangeFilter.value : 'month';
    }
    getMerchantId() {
        const merchantFilter = document.getElementById('financialMerchantFilter');
        return merchantFilter ? merchantFilter.value : '';
    }
    getDataType() {
        const dataTypeFilter = document.getElementById('financialDataType');
        return dataTypeFilter ? dataTypeFilter.value : 'overview';
    }
    applyFilters() {
        this.currentPage = 1;
        this.loadFinancialData();
    }
    refreshFinancial() {
        this.loadFinancialData();
    }
    renderStatisticsCharts(chartsData) {
        // 这里可以使用Chart.js等库来渲染统计图表
        // 目前先显示简单的统计信息
        const revenueChart = document.getElementById('revenueChart');
        const compositionChart = document.getElementById('revenueCompositionChart');
        if (revenueChart && compositionChart) {
            // 简单显示统计信息，实际项目中应该使用图表库
            revenueChart.parentElement.innerHTML = `
            <div class="p-4 text-center">
            <h6>收入趋势统计</h6>
            <p class="text-muted">图表功能开发中...</p>
            </div>
            `;
            compositionChart.parentElement.innerHTML = `
            <div class="p-4 text-center">
            <h6>收入构成分析</h6>
            <p class="text-muted">图表功能开发中...</p>
            </div>
            `;
        }
    }
    showLoadingState() {
        const container = document.getElementById('financialOverview');
        if (container) {
            container.innerHTML = `
            <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">加载财务数据中...</p>
            </div>
            `;
        }
        // 也清空表格数据
        const tableBody = document.getElementById('dataTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
            <tr>
            <td colspan="8" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载数据...</p>
            </td>
            </tr>
            `;
        }
    }
    showErrorState(message) {
        const container = document.getElementById('financialOverview');
        if (container) {
            container.innerHTML = `
            <div class="text-center py-4">
            <div class="text-danger">
            <i class="bi bi-exclamation-triangle fa-3x mb-3"></i>
            <p>${
                message
            }</p>
            <button class="btn btn-outline-primary btn-sm" onclick="window.financialManager.refreshFinancial()">
            <i class="bi bi-arrow-clockwise me-2"></i>重试
            </button>
            </div>
            </div>
            `;
        }
        // 也在表格中显示错误
        const tableBody = document.getElementById('dataTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
            <tr>
            <td colspan="8" class="text-center py-4">
            <div class="text-danger">
            <i class="bi bi-exclamation-triangle fa-2x mb-2"></i>
            <p>${
                message
            }</p>
            </div>
            </td>
            </tr>
            `;
        }
    }
    async viewTransactionDetail(transactionId) {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=transactions&id=${
                transactionId
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200 && data.data.orders.length > 0) {
                const transaction = data.data.orders[0];
                this.showTransactionDetailModal(transaction);
            } else {
                this.showError('获取交易详情失败');
            }
        } catch (error) {
            console.error('View transaction detail error:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    showTransactionDetailModal(transaction) {
        const modalHtml = `
        <div class="modal fade" id="transactionDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">
        <i class="bi bi-receipt me-2"></i>交易详情
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <div class="row">
        <div class="col-md-6">
        <h6 class="text-primary">基本信息</h6>
        <table class="table table-sm">
        <tr>
        <td><strong>交易ID:</strong></td>
        <td>${
            transaction.transaction_id || transaction.id
        }</td>
        </tr>
        <tr>
        <td><strong>商户订单号:</strong></td>
        <td>${
            transaction.merchant_order_no || '-'
        }</td>
        </tr>
        <tr>
        <td><strong>所属商户:</strong></td>
        <td>${
            transaction.merchant_name || '未知商户'
        }</td>
        </tr>
        <tr>
        <td><strong>产品名称:</strong></td>
        <td>${
            transaction.product_name || '-'
        }</td>
        </tr>
        <tr>
        <td><strong>状态:</strong></td>
        <td>${
            this.renderStatusBadge(transaction.status)
        }</td>
        </tr>
        </table>
        </div>
        <div class="col-md-6">
        <h6 class="text-primary">金额信息</h6>
        <table class="table table-sm">
        <tr>
        <td><strong>订单金额:</strong></td>
        <td class="text-success"><strong>¥${
            parseFloat(transaction.amount || 0).toFixed(2)
        }</strong></td>
        </tr>
        <tr>
        <td><strong>实收金额:</strong></td>
        <td class="text-info">¥${
            parseFloat(transaction.actual_amount || 0).toFixed(2)
        }</td>
        </tr>
        <tr>
        <td><strong>服务费:</strong></td>
        <td class="text-warning">¥${
            parseFloat(transaction.service_fee || 0).toFixed(2)
        }</td>
        </tr>
        <tr>
        <td><strong>费率:</strong></td>
        <td><span class="badge bg-info">${
            (parseFloat(transaction.service_rate || 0) * 100).toFixed(2)
        }%</span></td>
        </tr>
        <tr>
        <td><strong>净收入:</strong></td>
        <td class="text-primary"><strong>¥${
            (parseFloat(transaction.actual_amount || 0) - parseFloat(transaction.service_fee || 0)).toFixed(2)
        }</strong></td>
        </tr>
        </table>
        </div>
        </div>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" onclick="window.financialManager.exportTransactionDetail('${
            transaction.id
        }')">
        <i class="bi bi-download me-2"></i>导出详情
        </button>
        </div>
        </div>
        </div>
        </div>
        `;
        // 移除已存在的模态框
        const existingModal = document.getElementById('transactionDetailModal');
        if (existingModal) {
            existingModal.remove();
        }
        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend',
        modalHtml);
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('transactionDetailModal'));
        modal.show();
    }
    exportTransactionDetail(transactionId) {
        const url = `${
            CONFIG.API_BASE_URL
        }/admin.php?action=export_transaction_detail&id=${
            transactionId
        }&token=${
            this.auth.getToken()
        }`;
        window.open(url, '_blank');
    }
    showSuccess(message) {
        console.log('Success:',
        message);
    }
    showError(message) {
        console.error('Error:',
        message);
        alert(message);
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = FinancialManager;
} else if (typeof window !== "undefined") {
    window.FinancialManager = FinancialManager;
}

console.log('📦 FinancialManager 模块加载完成');
