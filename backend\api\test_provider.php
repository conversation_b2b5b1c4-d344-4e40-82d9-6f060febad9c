<?php
// 简单的ProviderAPI测试文件
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: application/json; charset=utf-8');

echo "开始测试ProviderAPI...\n";

try {
    // 模拟包含文件
    echo "1. 包含数据库文件...\n";
    require_once dirname(__FILE__) . '/../config/database.php';
    echo "   数据库文件包含成功\n";
    
    // 模拟TenantAuth和TenantManager
    class MockTenantAuth {
        public function logTenantAction() { return true; }
    }
    
    class MockTenantManager {
        public function test() { return true; }
    }
    
    echo "2. 包含ProviderAPI文件...\n";
    require_once dirname(__FILE__) . '/tenant/provider_api.php';
    echo "   ProviderAPI文件包含成功\n";
    
    echo "3. 实例化ProviderAPI...\n";
    $mockAuth = new MockTenantAuth();
    $mockManager = new MockTenantManager();
    $providerAPI = new ProviderAPI($mockAuth, $mockManager);
    echo "   ProviderAPI实例化成功\n";
    
    echo "4. 模拟用户数据...\n";
    $mockUser = array(
        'id' => 1,
        'user_type' => 'provider',
        'profile_id' => 1
    );
    echo "   用户数据创建成功\n";
    
    echo "5. 测试getDevices方法...\n";
    $result = $providerAPI->handleAction('devices', 'GET', $mockUser);
    echo "   devices API调用成功\n";
    echo "   返回数据: " . json_encode($result) . "\n";
    
    echo "6. 测试getGroups方法...\n";
    $result = $providerAPI->handleAction('groups', 'GET', $mockUser);
    echo "   groups API调用成功\n";
    echo "   返回数据: " . json_encode($result) . "\n";
    
    echo "7. 测试getDeviceStatusStats方法...\n";
    $result = $providerAPI->handleAction('device_status', 'GET', $mockUser);
    echo "   device_status API调用成功\n";
    echo "   返回数据: " . json_encode($result) . "\n";
    
    echo "\n=== 所有测试通过！ ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈: " . $e->getTraceAsString() . "\n";
    http_response_code(500);
}
?> 