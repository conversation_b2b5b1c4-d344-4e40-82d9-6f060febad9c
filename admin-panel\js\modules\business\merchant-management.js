/**
 * 商户管理模块 - 重新设计版本
 * 负责商户CRUD操作、审核流程、费率配置、交易统计等核心商户业务功能
 * 支持多租户环境下的数据隔离和权限控制
 * 
 * <AUTHOR> Team
 * @version 3.0.0 - 重新设计版本
 * @since 2024-12-17
 */

class MerchantManager {
    constructor() {
        this.apiClient = null;
        this.authManager = null;
        this.utils = null;
        this.tenantInfo = null;
        this.currentMerchants = [];
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalMerchants = 0;
        this.searchQuery = '';
        this.filterStatus = 'all';
        this.currentFilters = {};
        this.isEditing = false;
        this.currentMerchantId = null;
        
        console.log('MerchantManager initialized');
    }

    /**
     * 初始化商户管理模块
     * @param {object} dependencies - 依赖项
     */
    init(dependencies) {
        try {
            // 依赖注入
            if (dependencies) {
                this.apiClient = dependencies.apiClient;
                this.utils = dependencies.utils;
                this.authManager = dependencies.authManager;
                this.tenantInfo = dependencies.tenantInfo || window.TENANT_INFO;
            } else {
                this.injectDependencies();
            }
            
            console.log('✅ 商户管理模块初始化完成', {
                tenantType: this.tenantInfo?.tenant_type,
                userType: this.authManager?.getUserType(),
                hasApiClient: !!this.apiClient
            });
        } catch (error) {
            console.error('❌ 商户管理模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 检查是否可以管理商户
     * @returns {boolean} 是否有权限
     */
    canManageMerchants() {
        if (!this.tenantInfo || !this.authManager) return false;

        const tenantType = this.tenantInfo.tenant_type;
        const userType = this.authManager.getUserType();

        // 权限矩阵：系统管理员、平台管理员和码商都可以管理商户
        const permissions = {
            'system_admin': ['system_admin'],
            'platform_admin': ['platform_admin'], 
            'provider': ['provider'],
            'merchant': []
        };

        return permissions[tenantType]?.includes(userType) || false;
    }

    /**
     * 渲染方法
     * @param {HTMLElement} container 容器元素
     * @param {Object} params 参数对象
     */
    async render(container, params = {}) {
        try {
            console.log('🎯 MerchantManager.render 被调用:', params);
            
            // 确保依赖已初始化
            if (!this.apiClient) {
                console.warn('⚠️ API客户端未初始化，尝试重新注入依赖');
                this.injectDependencies();
            }
            
            this.loadMerchantManagementPage(container);
            
            console.log('✅ 商户管理页面渲染完成');
        } catch (error) {
            console.error('❌ 商户管理页面渲染失败:', error);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h4>加载失败</h4>
                    <p>商户管理页面加载失败: ${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }

    /**
     * 加载商户管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadMerchantManagementPage(container) {
        // 检查权限
        if (!this.canManageMerchants()) {
            container.innerHTML = this.generateNoPermissionHTML();
            return;
        }

        container.innerHTML = this.generateMerchantManagementHTML();
        this.initializeMerchantManagementEvents();
        this.loadMerchants();
    }

    /**
     * 生成无权限访问页面
     * @returns {string} HTML字符串
     */
    generateNoPermissionHTML() {
        return `
            <div class="text-center py-5">
                <i class="bi bi-shield-exclamation text-warning" style="font-size: 4rem;"></i>
                <h3 class="mt-3">权限不足</h3>
                <p class="text-muted">您没有权限管理商户</p>
                <div class="mt-3">
                    <p class="text-muted"><strong>当前租户类型：</strong>${this.getTenantTypeText()}</p>
                    <p class="text-muted"><strong>当前用户类型：</strong>${this.getUserTypeText()}</p>
                </div>
                <div class="mt-4">
                    <button class="btn btn-primary" onclick="window.uiManager.navigateToPage('dashboard')">
                        <i class="bi bi-house me-2"></i>返回首页
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 获取租户类型文本
     * @returns {string} 租户类型文本
     */
    getTenantTypeText() {
        if (!this.tenantInfo) return '未知';
        
        const typeMap = {
            'system_admin': '系统管理员',
            'platform_admin': '平台管理员',
            'provider': '码商',
            'merchant': '商户'
        };
        return typeMap[this.tenantInfo.tenant_type] || '未知';
    }

    /**
     * 获取用户类型文本
     * @returns {string} 用户类型文本
     */
    getUserTypeText() {
        if (!this.authManager) return '未知';
        
        const userType = this.authManager.getUserType();
        const typeMap = {
            'system_admin': '系统管理员',
            'platform_admin': '平台管理员',
            'provider': '码商',
            'merchant': '商户',
            'employee': '员工'
        };
        return typeMap[userType] || '未知';
    }

    /**
     * 依赖注入
     */
    injectDependencies() {
        try {
            // 从全局对象获取依赖
            this.apiClient = window.AdminApiClient || window.apiClient || null;
            this.utils = window.utils || {
                showMessage: (msg, type) => {
                    console.log(`[${type.toUpperCase()}] ${msg}`);
                    if (type === 'error') {
                        alert('错误: ' + msg);
                    } else if (type === 'success') {
                        console.log('成功: ' + msg);
                    }
                },
                formatDate: (dateStr) => {
                    if (!dateStr) return '-';
                    return new Date(dateStr).toLocaleString('zh-CN');
                },
                getInitials: (name) => {
                    if (!name) return 'M';
                    const words = name.trim().split(' ');
                    if (words.length === 1) {
                        return words[0].charAt(0).toUpperCase();
                    }
                    return words.slice(0, 2).map(word => word.charAt(0).toUpperCase()).join('');
                },
                formatNumber: (num) => {
                    if (!num) return '0';
                    return parseFloat(num).toLocaleString('zh-CN');
                }
            };
            this.authManager = window.authManager || {
                getUserType: () => {
                    const user = JSON.parse(localStorage.getItem('user_info') || '{}');
                    return user.user_type || 'unknown';
                }
            };
            this.tenantInfo = window.TENANT_INFO || {
                tenant_type: 'platform_admin'
            };
            
            console.log('✅ 依赖注入完成', {
                apiClient: !!this.apiClient,
                utils: !!this.utils,
                authManager: !!this.authManager,
                tenantInfo: !!this.tenantInfo
            });
        } catch (error) {
            console.error('❌ 依赖注入失败:', error);
            throw error;
        }
    }

    /**
     * 生成商户管理页面HTML - 重新设计版本
     * @returns {string} HTML字符串
     */
    generateMerchantManagementHTML() {
        return `
            <div class="merchant-management">
                <!-- 页面头部 -->
                <div class="page-header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="bi bi-shop me-2"></i>商户管理</h2>
                            <p class="text-muted mb-0">管理商户信息、审核状态和费率配置</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="addMerchantBtn">
                                <i class="bi bi-plus-circle me-2"></i>添加商户
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="bg-primary rounded p-3">
                                            <i class="bi bi-shop text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-bold fs-4" id="totalMerchantsCount">0</div>
                                        <div class="text-muted">总商户数</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="bg-success rounded p-3">
                                            <i class="bi bi-check-circle text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-bold fs-4" id="activeMerchantsCount">0</div>
                                        <div class="text-muted">活跃商户</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="bg-warning rounded p-3">
                                            <i class="bi bi-clock text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-bold fs-4" id="pendingMerchantsCount">0</div>
                                        <div class="text-muted">待审核</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <div class="bg-info rounded p-3">
                                            <i class="bi bi-currency-dollar text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-bold fs-4" id="approvedMerchantsCount">0</div>
                                        <div class="text-muted">已审核</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">搜索商户</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="merchantSearchInput" 
                                           placeholder="商户代码、用户名...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">商户状态</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="pending">待审核</option>
                                    <option value="approved">已审核</option>
                                    <option value="active">活跃</option>
                                    <option value="inactive">停用</option>
                                    <option value="disabled">禁用</option>
                                    <option value="rejected">已拒绝</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-outline-primary" id="searchBtn">
                                        <i class="bi bi-funnel me-1"></i>筛选
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 商户列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">商户列表</h5>
                        <button class="btn btn-sm btn-outline-secondary" id="refreshMerchantsBtn">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>商户信息</th>
                                        <th>商户代码</th>
                                        <th>状态</th>
                                        <th>余额</th>
                                        <th>费率</th>
                                        <th>创建时间</th>
                                        <th width="150">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="merchantTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <div class="mt-2">正在加载商户数据...</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                显示第 <span id="currentPageInfo">1-20</span> 条，共 <span id="totalMerchantsInfo">0</span> 条记录
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="merchantPagination">
                                    <!-- 分页将动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 添加/编辑商户模态框 -->
            <div class="modal fade" id="merchantModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="merchantModalTitle">添加商户</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="merchantForm">
                                <input type="hidden" id="merchant-edit-id">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">用户名 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="merchant-edit-username" name="merchant-edit-username" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">密码</label>
                                        <input type="password" class="form-control" id="merchant-edit-password" name="merchant-edit-password" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">商户代码</label>
                                        <input type="text" class="form-control" id="merchant-edit-code" name="merchant-edit-code">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">回调URL</label>
                                        <input type="url" class="form-control" id="merchant-edit-callback" name="merchant-edit-callback">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">服务费率</label>
                                        <input type="number" class="form-control" id="merchant-edit-rate" name="merchant-edit-rate"
                                               step="0.0001" min="0" max="1" value="0.0050">
                                    </div>
                                    <div class="col-md-6" style="display: none;">
                                        <label class="form-label">平台ID</label>
                                        <input type="hidden" id="merchant-edit-platform" name="merchant-edit-platform" value="1">
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveMerchantBtn">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 初始化商户管理事件
     */
    initializeMerchantManagementEvents() {
        // 添加商户按钮
        document.getElementById('addMerchantBtn')?.addEventListener('click', () => {
            this.showMerchantModal();
        });

        // 刷新按钮
        document.getElementById('refreshMerchantsBtn')?.addEventListener('click', () => {
            this.refreshMerchants();
        });

        // 搜索按钮
        document.getElementById('searchBtn')?.addEventListener('click', () => {
            this.handleSearch();
        });

        // 保存商户按钮
        document.getElementById('saveMerchantBtn')?.addEventListener('click', () => {
            this.handleSaveMerchant();
        });

        // 搜索输入框回车事件
        document.getElementById('merchantSearchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch();
            }
        });

        // 筛选器变化事件
        ['statusFilter'].forEach(id => {
            document.getElementById(id)?.addEventListener('change', () => {
                this.handleSearch();
            });
        });

        console.log('✅ 商户管理事件初始化完成');
    }

    /**
     * 加载商户数据
     * @param {number} page 页码
     */
    async loadMerchants(page = 1) {
        try {
            this.showTableLoading();
            
            // 确保API客户端可用
            if (!this.apiClient) {
                throw new Error('API客户端未初始化');
            }

            const params = new URLSearchParams({
                page: page,
                limit: this.pageSize
            });

            // 添加搜索条件
            if (this.searchQuery) {
                params.append('search', this.searchQuery);
            }
            if (this.filterStatus) {
                params.append('status', this.filterStatus);
            }

            console.log('📡 发送商户列表请求:', params.toString());

            const response = await this.apiClient.get(`/admin.php?action=get_merchants&${params.toString()}`);
            
            console.log('📨 商户列表响应:', response);

            if (response && response.data && response.data.code === 200) {
                const data = response.data.data;
                this.currentMerchants = data.merchants || [];
                this.totalMerchants = data.pagination?.total || 0;
                this.currentPage = page;

                this.renderMerchantTable();
                this.renderPagination();
                this.updateStats(data.stats);
            } else {
                throw new Error(response?.data?.message || '获取商户列表失败');
            }
        } catch (error) {
            console.error('❌ 加载商户数据失败:', error);
            this.showTableError(error.message);
        }
    }

    /**
     * 渲染商户表格
     */
    renderMerchantTable() {
        const tbody = document.getElementById('merchantTableBody');
        if (!tbody) return;

        if (!this.currentMerchants || this.currentMerchants.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="bi bi-shop text-muted" style="font-size: 3rem;"></i>
                        <div class="mt-2 text-muted">暂无商户数据</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.currentMerchants.map(merchant => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white me-3" 
                             style="width: 40px; height: 40px; font-weight: 600;">
                            ${this.utils.getInitials(merchant.username || 'M')}
                        </div>
                        <div>
                            <div class="fw-bold">${merchant.username || '-'}</div>
                            <small class="text-muted">ID: ${merchant.id}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-light text-dark">${merchant.merchant_code || '-'}</span>
                </td>
                <td>
                    ${this.renderStatusBadge(merchant.status)}
                </td>
                <td>
                    <div class="text-success fw-bold">¥${this.utils.formatNumber(merchant.balance || 0)}</div>
                </td>
                <td>
                    <span class="text-primary fw-bold">${(merchant.service_rate * 100).toFixed(2)}%</span>
                </td>
                <td>
                    <small>${this.utils.formatDate(merchant.created_at)}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" 
                                onclick="merchantManager.showMerchantDetail(${merchant.id})" 
                                title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary" 
                                onclick="merchantManager.showMerchantModal(${merchant.id})" 
                                title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        ${merchant.status === 'pending' ? `
                        <button class="btn btn-outline-success" 
                                onclick="merchantManager.approveMerchant(${merchant.id})" 
                                title="审核通过">
                            <i class="bi bi-check"></i>
                        </button>
                        <button class="btn btn-outline-danger" 
                                onclick="merchantManager.rejectMerchant(${merchant.id})" 
                                title="拒绝">
                            <i class="bi bi-x"></i>
                        </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 显示表格加载状态
     */
    showTableLoading() {
        const tbody = document.getElementById('merchantTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载商户数据...</div>
                </td>
            </tr>
        `;
    }

    /**
     * 显示表格错误状态
     * @param {string} message 错误信息
     */
    showTableError(message) {
        const tbody = document.getElementById('merchantTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                    <div class="mt-2 text-danger">${message}</div>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="merchantManager.refreshMerchants()">
                        重新加载
                    </button>
                </td>
            </tr>
        `;
    }

    /**
     * 渲染状态徽章
     * @param {string} status 状态
     * @returns {string} HTML字符串
     */
    renderStatusBadge(status) {
        const statusMap = {
            'pending': { class: 'bg-warning text-dark', text: '待审核' },
            'approved': { class: 'bg-info text-white', text: '已审核' },
            'active': { class: 'bg-success', text: '活跃' },
            'inactive': { class: 'bg-secondary', text: '停用' },
            'disabled': { class: 'bg-dark', text: '禁用' },
            'rejected': { class: 'bg-danger', text: '已拒绝' }
        };

        const statusInfo = statusMap[status] || { class: 'bg-light text-dark', text: status || '未知' };
        return `<span class="badge ${statusInfo.class}">${statusInfo.text}</span>`;
    }

    /**
     * 更新统计数据
     * @param {Object} stats 统计数据
     */
    updateStats(stats) {
        if (stats) {
            document.getElementById('totalMerchantsCount').textContent = stats.total_merchants || 0;
            document.getElementById('activeMerchantsCount').textContent = stats.active_merchants || 0;
            document.getElementById('pendingMerchantsCount').textContent = stats.pending_merchants || 0;
            document.getElementById('approvedMerchantsCount').textContent = stats.approved_merchants || 0;
        }
    }

    /**
     * 刷新商户列表
     */
    refreshMerchants() {
        this.loadMerchants(this.currentPage);
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        this.searchQuery = document.getElementById('merchantSearchInput').value.trim();
        this.filterStatus = document.getElementById('statusFilter').value;
        
        this.currentPage = 1;
        this.loadMerchants(1);
    }

    /**
     * 显示商户模态框
     * @param {number} merchantId 商户ID（编辑时）
     */
    async showMerchantModal(merchantId = null) {
        const modal = new bootstrap.Modal(document.getElementById('merchantModal'));
        const title = document.getElementById('merchantModalTitle');
        const form = document.getElementById('merchantForm');

        if (merchantId) {
            // 编辑模式
            title.textContent = '编辑商户';
            
            try {
                console.log('📡 获取商户详情:', merchantId);

                const response = await this.apiClient.get(`/admin.php?action=get_merchant_user_detail&merchant_id=${merchantId}`);
                
                console.log('📨 商户详情响应:', response);

                if (response && response.data && response.data.code === 200) {
                    const merchant = response.data.data;
                    console.log('📦 获取到的商户数据:', merchant);
                    
                    // 填充表单数据
                    const fields = {
                        'merchant-edit-id': merchant.id,
                        'merchant-edit-username': merchant.username,
                        'merchant-edit-code': merchant.merchant_code || '',
                        'merchant-edit-callback': merchant.callback_url || '',
                        'merchant-edit-rate': merchant.service_rate || '0.0050',
                        'merchant-edit-platform': merchant.platform_id || '1'
                    };
                    
                    console.log('📝 准备填充的字段数据:', fields);
                    
                    // 使用setTimeout确保模态框完全渲染后再填充数据
                    setTimeout(() => {
                        console.log('⏰ 延迟填充数据开始...');
                        
                        // 逐个设置字段值并验证
                        Object.keys(fields).forEach(fieldId => {
                            const element = document.getElementById(fieldId);
                            if (element) {
                                element.value = fields[fieldId];
                                console.log(`✅ 字段 ${fieldId} 设置为: ${fields[fieldId]}, 当前值: ${element.value}`);
                            } else {
                                console.error(`❌ 找不到字段元素: ${fieldId}`);
                            }
                        });
                        
                        // 再次验证所有字段的值
                        console.log('🔍 验证所有字段当前值:');
                        Object.keys(fields).forEach(fieldId => {
                            const element = document.getElementById(fieldId);
                            if (element) {
                                console.log(`  ${fieldId}: ${element.value}`);
                            }
                        });
                    }, 100);
                    
                    // 编辑时隐藏密码字段并移除required属性（也延迟执行）
                    setTimeout(() => {
                        const passwordField = document.getElementById('merchant-edit-password');
                        if (passwordField && passwordField.parentElement) {
                            passwordField.parentElement.style.display = 'none';
                            passwordField.removeAttribute('required');
                            passwordField.value = ''; // 清空密码字段
                            console.log('🔒 密码字段已隐藏并移除必填属性');
                        } else {
                            console.error('❌ 找不到密码字段或其父元素');
                        }
                    }, 100);
                } else {
                    throw new Error(response?.data?.message || '获取商户信息失败');
                }
            } catch (error) {
                console.error('❌ 加载商户信息失败:', error);
                this.utils.showMessage('加载商户信息失败: ' + error.message, 'error');
                return;
            }
        } else {
            // 添加模式
            title.textContent = '添加商户';
            
            // 重置表单（只在添加模式下重置）
            form.reset();
            
            // 显示密码字段并添加required属性
            const passwordField = document.getElementById('merchant-edit-password');
            if (passwordField && passwordField.parentElement) {
                passwordField.parentElement.style.display = 'block';
                passwordField.setAttribute('required', 'required');
                console.log('🔓 密码字段已显示并添加必填属性');
            }
        }

        modal.show();
    }

    /**
     * 处理保存商户
     */
    async handleSaveMerchant() {
        const form = document.getElementById('merchantForm');
        const formData = new FormData(form);
        const merchantId = document.getElementById('merchant-edit-id').value;

        // 表单验证
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const merchantData = {
            username: formData.get('merchant-edit-username') || document.getElementById('merchant-edit-username').value,
            password: formData.get('merchant-edit-password') || document.getElementById('merchant-edit-password').value,
            merchant_code: formData.get('merchant-edit-code') || document.getElementById('merchant-edit-code').value,
            callback_url: formData.get('merchant-edit-callback') || document.getElementById('merchant-edit-callback').value,
            service_rate: parseFloat(formData.get('merchant-edit-rate') || document.getElementById('merchant-edit-rate').value || '0.0050'),
            platform_id: parseInt(formData.get('merchant-edit-platform') || document.getElementById('merchant-edit-platform').value || '1')
        };

        // 编辑时不发送密码（如果为空）
        if (merchantId && !merchantData.password) {
            delete merchantData.password;
        }

        try {
            const saveBtn = document.getElementById('saveMerchantBtn');
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';

            console.log('📡 保存商户数据:', merchantData);

            let response;
            if (merchantId) {
                // 更新商户
                response = await this.apiClient.post(`/admin.php?action=update_merchant`, {
                    ...merchantData,
                    merchant_id: merchantId
                });
            } else {
                // 创建商户
                response = await this.apiClient.post('/admin.php?action=create_merchant', merchantData);
            }

            console.log('📨 保存商户响应:', response);

            if (response && response.data && response.data.code === 200) {
                this.utils.showMessage(merchantId ? '商户更新成功' : '商户添加成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('merchantModal')).hide();
                this.loadMerchants(this.currentPage);
            } else {
                throw new Error(response?.data?.message || '保存失败');
            }
        } catch (error) {
            console.error('❌ 保存商户失败:', error);
            this.utils.showMessage(error.message || '保存商户失败', 'error');
        } finally {
            const saveBtn = document.getElementById('saveMerchantBtn');
            saveBtn.disabled = false;
            saveBtn.innerHTML = '保存';
        }
    }

    /**
     * 审核通过商户
     * @param {number} merchantId 商户ID
     */
    async approveMerchant(merchantId) {
        if (!confirm('确定要审核通过这个商户吗？')) {
            return;
        }

        try {
            console.log('📡 审核通过商户:', merchantId);

            const response = await this.apiClient.post('/admin.php?action=approve_merchant', {
                merchant_id: merchantId
            });

            console.log('📨 审核响应:', response);
            
            if (response && response.data && response.data.code === 200) {
                this.utils.showMessage('商户审核通过', 'success');
                this.loadMerchants(this.currentPage);
            } else {
                throw new Error(response?.data?.message || '审核失败');
            }
        } catch (error) {
            console.error('❌ 审核商户失败:', error);
            this.utils.showMessage(error.message || '审核商户失败', 'error');
        }
    }

    /**
     * 拒绝商户
     * @param {number} merchantId 商户ID
     */
    async rejectMerchant(merchantId) {
        if (!confirm('确定要拒绝这个商户吗？')) {
            return;
        }

        try {
            console.log('📡 拒绝商户:', merchantId);

            const response = await this.apiClient.post('/admin.php?action=reject_merchant', {
                merchant_id: merchantId
            });

            console.log('📨 拒绝响应:', response);
            
            if (response && response.data && response.data.code === 200) {
                this.utils.showMessage('商户已拒绝', 'success');
                this.loadMerchants(this.currentPage);
            } else {
                throw new Error(response?.data?.message || '拒绝失败');
            }
        } catch (error) {
            console.error('❌ 拒绝商户失败:', error);
            this.utils.showMessage(error.message || '拒绝商户失败', 'error');
        }
    }

    /**
     * 显示商户详情
     * @param {number} merchantId 商户ID
     */
    async showMerchantDetail(merchantId) {
        try {
            console.log('📡 获取商户详情:', merchantId);

            const response = await this.apiClient.get(`/admin.php?action=get_merchant_user_detail&merchant_id=${merchantId}`);
            
            if (response && response.data && response.data.code === 200) {
                const merchant = response.data.data;
                
                // 这里可以显示详情模态框或跳转到详情页面
                alert(`商户详情：\n用户名：${merchant.username}\n商户代码：${merchant.merchant_code || '-'}\n状态：${merchant.status}\n余额：¥${merchant.balance}`);
            } else {
                throw new Error(response?.data?.message || '获取商户详情失败');
            }
        } catch (error) {
            console.error('❌ 获取商户详情失败:', error);
            this.utils.showMessage(error.message || '获取商户详情失败', 'error');
        }
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        const pagination = document.getElementById('merchantPagination');
        const totalPages = Math.ceil(this.totalMerchants / this.pageSize);
        
        if (!pagination || totalPages <= 1) {
            if (pagination) pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';
        
        // 上一页
        if (this.currentPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="merchantManager.loadMerchants(${this.currentPage - 1})">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
            `;
        }

        // 页码
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="merchantManager.loadMerchants(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="merchantManager.loadMerchants(${this.currentPage + 1})">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            `;
        }

        pagination.innerHTML = paginationHTML;

        // 更新信息显示
        const start = (this.currentPage - 1) * this.pageSize + 1;
        const end = Math.min(this.currentPage * this.pageSize, this.totalMerchants);
        document.getElementById('currentPageInfo').textContent = `${start}-${end}`;
        document.getElementById('totalMerchantsInfo').textContent = this.totalMerchants;
    }

    /**
     * 获取实例（单例模式）
     * @returns {MerchantManager} 实例
     */
    static getInstance() {
        if (!window.merchantManagerInstance) {
            window.merchantManagerInstance = new MerchantManager();
        }
        return window.merchantManagerInstance;
    }
}

// 导出到全局
window.MerchantManager = MerchantManager;
window.merchantManager = MerchantManager.getInstance();

// 模块加载器期望的导出格式
window.MerchantManagementModule = {
    name: 'merchant-management',
    version: '3.0.0',
    instance: null,
    
    async init(dependencies) {
        this.instance = MerchantManager.getInstance();
        await this.instance.init(dependencies);
        return this.instance;
    },
    
    render(container, params = {}) {
        if (!this.instance) {
            this.instance = MerchantManager.getInstance();
        }
        return this.instance.render(container, params);
    },
    
    getInstance() {
        if (!this.instance) {
            this.instance = MerchantManager.getInstance();
        }
        return this.instance;
    }
};

console.log('✅ 商户管理模块加载完成'); 