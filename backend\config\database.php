<?php
// 定义项目根路径常量 (PHP 5.6兼容)
if (!defined('PROJECT_ROOT')) {
    define('PROJECT_ROOT', dirname(dirname(dirname(__FILE__))));
}
if (!defined('BACKEND_ROOT')) {
    define('BACKEND_ROOT', dirname(dirname(__FILE__)));
}

// 数据库配置文件
class Database {
    private static $instance = null;
    private $host = '127.0.0.1';
    private $dbname = 'payment_system';
    private $username = 'root';
    private $password = 'yak19XTwyDx.8';
    private $charset = 'utf8mb4';
    private $pdo;
    private static $instanceId;
    
    // 租户上下文
    private $tenantContext = null;
    private $enableTenantFilter = true; // 是否启用租户过滤
    
    // 租户表映射配置
    private $tenantTableConfig = array(
        'merchants' => array(
            'filter_field' => 'platform_id',
            'tenant_types' => array('platform_admin', 'system_admin')
        ),
        'payment_providers' => array(
            'filter_field' => 'platform_id', 
            'tenant_types' => array('platform_admin', 'system_admin')
        ),
        'orders' => array(
            'filter_field' => 'platform_id',
            'tenant_types' => array('platform_admin', 'merchant', 'provider', 'system_admin')
        ),
        'devices' => array(
            'filter_field' => 'platform_id',
            'tenant_types' => array('platform_admin', 'provider', 'system_admin')
        ),

        'financial_records' => array(
            'filter_field' => 'platform_id',
            'tenant_types' => array('platform_admin', 'system_admin')
        )
    );
    
    // 不需要过滤的表（白名单）
    private $filterWhitelist = array(
        'system_logs', 'system_config', 'migrations', 'users', 'platforms', 'domain_configs', 
        'employees', 'job_positions', 'job_position_permissions'
    );
    
    public function __construct() {
        // 为调试生成唯一实例ID
        self::$instanceId = uniqid('db_', true);
        error_log("Database::__construct - 创建新实例ID: " . self::$instanceId);
    }
    
    /**
     * 设置租户上下文
     */
    public function setTenantContext($context) {
        $this->tenantContext = $context;
        error_log("Database::setTenantContext - 设置租户上下文: " . json_encode($context));
    }
    
    /**
     * 获取租户上下文
     */
    public function getTenantContext() {
        return $this->tenantContext;
    }
    
    /**
     * 启用/禁用租户过滤
     */
    public function setTenantFilterEnabled($enabled) {
        $this->enableTenantFilter = $enabled;
        error_log("Database::setTenantFilterEnabled - 租户过滤状态: " . ($enabled ? 'enabled' : 'disabled'));
    }

    /**
     * 获取数据库实例（单例模式）- 修复版本
     */
    public static function getInstance() {
        if (self::$instance === null) {
            error_log("Database::getInstance - 创建新的Database单例实例");
            self::$instance = new Database();
            // 立即建立连接，确保实例可用
            try {
                self::$instance->connect();
                error_log("Database::getInstance - 数据库连接已建立，实例ID: " . self::$instanceId);
            } catch (Exception $e) {
                error_log("Database::getInstance - 数据库连接失败: " . $e->getMessage());
                self::$instance = null;
                throw $e;
            }
        } else {
            error_log("Database::getInstance - 返回现有实例，实例ID: " . self::$instanceId);
        }
        return self::$instance;
    }
    
    public function connect() {
        if ($this->pdo == null) {
            try {
                $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset={$this->charset}";
                $options = array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                );
                
                error_log("Database::connect - 开始建立PDO连接");
                $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
                error_log("Database::connect - PDO连接成功建立");
            } catch (PDOException $e) {
                error_log("Database::connect - PDO连接失败: " . $e->getMessage());
                throw new Exception("数据库连接失败: " . $e->getMessage());
            }
        }
        
        return $this->pdo;
    }
    
    /**
     * 应用租户过滤器
     */
    private function applyTenantFilter($sql, $params) {
        // 如果未启用租户过滤或没有租户上下文，直接返回
        if (!$this->enableTenantFilter || !$this->tenantContext) {
            return array('sql' => $sql, 'params' => $params);
        }
        
        // 提取SQL中的表名
        $tables = $this->extractTablesFromSql($sql);
        
        $originalSql = $sql;
        $newParams = $params;
        
        // 为每个需要过滤的表添加条件
        foreach ($tables as $table) {
            if ($this->needsTenantFilter($table)) {
                $filterCondition = $this->buildTenantFilterCondition($table);
                if ($filterCondition) {
                    $sql = $this->injectFilterCondition($sql, $table, $filterCondition['where']);
                    $newParams = array_merge($newParams, $filterCondition['params']);
                }
            }
        }
        
        // 记录过滤日志
        if ($sql !== $originalSql) {
            error_log("Database::applyTenantFilter - 应用租户过滤");
            error_log("Original SQL: " . $originalSql);
            error_log("Filtered SQL: " . $sql);
            error_log("Additional params: " . json_encode(array_slice($newParams, count($params))));
        }
        
        return array('sql' => $sql, 'params' => $newParams);
    }
    
    /**
     * 从SQL中提取表名
     */
    private function extractTablesFromSql($sql) {
        $tables = array();
        
        // 匹配FROM子句
        if (preg_match('/FROM\s+(\w+)(\s+(\w+))?/i', $sql, $matches)) {
            $tables[] = $matches[1];
        }
        
        // 匹配JOIN子句
        if (preg_match_all('/JOIN\s+(\w+)(\s+(\w+))?/i', $sql, $matches)) {
            foreach ($matches[1] as $table) {
                $tables[] = $table;
            }
        }
        
        return array_unique($tables);
    }
    
    /**
     * 检查是否需要租户过滤
     */
    private function needsTenantFilter($table) {
        // 检查白名单
        if (in_array($table, $this->filterWhitelist)) {
            return false;
        }
        
        // 检查表配置
        return isset($this->tenantTableConfig[$table]);
    }
    
    /**
     * 构建租户过滤条件
     */
    private function buildTenantFilterCondition($table) {
        if (!$this->tenantContext) {
            return null;
        }
        
        $tenantType = $this->tenantContext['tenant_type'];
        $tenantId = $this->tenantContext['tenant_id'];
        $platformId = isset($this->tenantContext['platform_id']) ? $this->tenantContext['platform_id'] : null;
        $userId = isset($this->tenantContext['user_id']) ? $this->tenantContext['user_id'] : null;
        
        switch ($tenantType) {
            case 'system_admin':
                // 系统管理员：无限制
                return null;
                
            case 'platform_admin':
                // 平台管理员：只能看到自己平台的数据
                if ($platformId && isset($this->tenantTableConfig[$table])) {
                    $filterField = $this->tenantTableConfig[$table]['filter_field'];
                    return array(
                        'where' => "{$table}.{$filterField} = ?",
                        'params' => array($platformId)
                    );
                }
                break;
                
            case 'merchant':
                // 商户：只能看到自己的数据
                if ($table === 'merchants') {
                    return array(
                        'where' => "{$table}.id = ?",
                        'params' => array($tenantId)
                    );
                }
                if ($table === 'orders') {
                    return array(
                        'where' => "{$table}.merchant_id = ?",
                        'params' => array($tenantId)
                    );
                }
                if ($platformId && isset($this->tenantTableConfig[$table])) {
                    $filterField = $this->tenantTableConfig[$table]['filter_field'];
                    return array(
                        'where' => "{$table}.{$filterField} = ?",
                        'params' => array($platformId)
                    );
                }
                break;
                
            case 'provider':
                // 码商：只能看到自己相关的数据
                if ($table === 'payment_providers') {
                    return array(
                        'where' => "{$table}.id = ?",
                        'params' => array($tenantId)
                    );
                }
                if ($table === 'orders') {
                    return array(
                        'where' => "{$table}.provider_id = ?",
                        'params' => array($tenantId)
                    );
                }
                if ($table === 'devices') {
                    return array(
                        'where' => "{$table}.provider_id = ?",
                        'params' => array($tenantId)
                    );
                }
                if ($platformId && isset($this->tenantTableConfig[$table])) {
                    $filterField = $this->tenantTableConfig[$table]['filter_field'];
                    return array(
                        'where' => "{$table}.{$filterField} = ?",
                        'params' => array($platformId)
                    );
                }
                break;
        }
        
        return null;
    }
    
    /**
     * 在SQL中注入过滤条件
     */
    private function injectFilterCondition($sql, $table, $condition) {
        // 智能检测表别名
        $tableRef = $this->detectTableAlias($sql, $table);
        if ($tableRef && $tableRef !== $table) {
            // 替换条件中的表名为正确的别名
            $condition = str_replace($table . '.', $tableRef . '.', $condition);
        }
        
        // 记录调试信息
        error_log("Database::injectFilterCondition - 表: $table, 别名: $tableRef, 条件: $condition");
        
        // 检查是否已有WHERE子句
        if (preg_match('/\bWHERE\b/i', $sql)) {
            // 已有WHERE，添加AND条件
            $sql = preg_replace('/(\bWHERE\b)/i', "$1 {$condition} AND", $sql, 1);
            error_log("Database::injectFilterCondition - 已有WHERE，添加AND条件");
        } else {
            // 没有WHERE，添加WHERE条件
            // 在ORDER BY、GROUP BY、LIMIT之前插入
            if (preg_match('/(ORDER\s+BY|GROUP\s+BY|LIMIT|HAVING)/i', $sql, $matches, PREG_OFFSET_CAPTURE)) {
                $pos = $matches[0][1];
                $beforeWhere = substr($sql, 0, $pos);
                $afterWhere = substr($sql, $pos);
                $sql = $beforeWhere . "WHERE {$condition} " . $afterWhere;
                error_log("Database::injectFilterCondition - 在关键字前插入WHERE: {$matches[0][0]} at position $pos");
            } else {
                $sql .= " WHERE {$condition}";
                error_log("Database::injectFilterCondition - 末尾添加WHERE");
            }
        }
        
        error_log("Database::injectFilterCondition - 最终SQL: $sql");
        return $sql;
    }
    
    /**
     * 检测表别名
     */
    private function detectTableAlias($sql, $tableName) {
        // 匹配 "FROM table alias" 或 "JOIN table alias"，别名不能是SQL关键字
        $pattern = '/(?:FROM|JOIN)\s+' . preg_quote($tableName, '/') . '\s+([a-zA-Z_]\w*)\s*(?:\s|,|WHERE|ORDER|GROUP|LIMIT|$)/i';
        if (preg_match($pattern, $sql, $matches)) {
            $alias = $matches[1];
            // 确保别名不是SQL关键字
            $sqlKeywords = array('WHERE', 'ORDER', 'GROUP', 'LIMIT', 'HAVING', 'UNION', 'SELECT', 'FROM', 'JOIN', 'LEFT', 'RIGHT', 'INNER', 'OUTER');
            if (!in_array(strtoupper($alias), $sqlKeywords)) {
                return $alias;
            }
        }
        return $tableName;
    }

    public function query($sql, $params = array()) {
        try {
            // 确保连接已建立
            if ($this->pdo === null) {
                error_log("Database::query - PDO连接为null，重新建立连接");
                $this->connect();
            }
            
            // 应用租户过滤
            $filtered = $this->applyTenantFilter($sql, $params);
            $sql = $filtered['sql'];
            $params = $filtered['params'];
            
            error_log("Database::query - 执行SQL: " . substr($sql, 0, 100) . "..., 实例ID: " . self::$instanceId);
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database::query - 查询失败: " . $e->getMessage());
            throw new Exception("数据库查询失败: " . $e->getMessage());
        }
    }
    
    public function fetch($sql, $params = array()) {
        // 连接状态检查
        if ($this->pdo === null) {
            error_log("Database::fetch - PDO连接为null，重新建立连接");
            $this->connect();
        }
        
        $result = $this->query($sql, $params)->fetch();
        error_log("Database::fetch - 查询完成，实例ID: " . self::$instanceId . ", 结果: " . ($result ? 'found' : 'not found'));
        return $result;
    }
    
    public function fetchAll($sql, $params = array()) {
        // 连接状态检查
        if ($this->pdo === null) {
            error_log("Database::fetchAll - PDO连接为null，重新建立连接");
            $this->connect();
        }
        
        return $this->query($sql, $params)->fetchAll();
    }
    
    public function execute($sql, $params = array()) {
        // 连接状态检查
        if ($this->pdo === null) {
            error_log("Database::execute - PDO连接为null，重新建立连接");
            $this->connect();
        }
        
        $stmt = $this->query($sql, $params);
        return $this->connect()->lastInsertId() ? $this->connect()->lastInsertId() : $stmt->rowCount();
    }
    
    public function lastInsertId() {
        return $this->connect()->lastInsertId();
    }
    
    public function beginTransaction() {
        return $this->connect()->beginTransaction();
    }
    
    public function commit() {
        return $this->connect()->commit();
    }
    
    public function rollback() {
        return $this->connect()->rollback();
    }
    
    /**
     * 获取当前实例状态（调试用）
     */
    public function getInstanceInfo() {
        return array(
            'instance_id' => self::$instanceId,
            'pdo_status' => $this->pdo ? 'connected' : 'not connected',
            'class' => get_class($this),
            'tenant_context' => $this->tenantContext,
            'tenant_filter_enabled' => $this->enableTenantFilter
        );
    }
    
    /**
     * 临时禁用租户过滤（用于特殊场景）
     */
    public function withoutTenantFilter($callback) {
        $originalState = $this->enableTenantFilter;
        $this->enableTenantFilter = false;
        
        try {
            $result = $callback($this);
            $this->enableTenantFilter = $originalState;
            return $result;
        } catch (Exception $e) {
            $this->enableTenantFilter = $originalState;
            throw $e;
        }
    }
}
?> 