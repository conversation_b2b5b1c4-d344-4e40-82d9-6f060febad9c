<?php
// PHP 5.6 语法测试文件
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "PHP版本: " . PHP_VERSION . "\n";

// 测试数组语法
$testArray = array(
    'id' => 1,
    'name' => 'test',
    'data' => array(
        'sub1' => 'value1',
        'sub2' => 'value2'
    )
);

echo "数组测试通过\n";

// 测试类语法
class TestClass {
    private $prop;
    
    public function __construct() {
        $this->prop = 'test';
    }
    
    public function testMethod($param) {
        return array(
            'error_code' => 0,
            'message' => 'success',
            'data' => $param
        );
    }
}

$testObj = new TestClass();
$result = $testObj->testMethod('test');

echo "类测试通过\n";
echo "测试结果: " . json_encode($result) . "\n";

echo "所有语法测试通过！\n";
?> 