/**
 * 风险控制管理器
 * 从admin.js第8013-8559行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class RiskControlManager {
    constructor() {
        this.currentPage = 1;
        this.auth = new AuthManager();
    }
    async initialize() {
        this.initializeEventListeners();
        // 如果是管理员，加载商户选项
        const user = this.auth.getUser();
        if (user && user.user_type === 'admin') {
            await this.loadMerchantOptions();
        }
        await this.loadRiskConfigs();
    }
    initializeEventListeners() {
        // 商户筛选
        const merchantFilter = document.getElementById('riskMerchantFilter');
        if (merchantFilter) {
            merchantFilter.addEventListener('change', () => this.applyFilters());
        }
        // 状态筛选
        const statusFilter = document.getElementById('riskStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.applyFilters());
        }
        // 类型筛选
        const typeFilter = document.getElementById('riskTypeFilter');
        if (typeFilter) {
            typeFilter.addEventListener('change', () => this.applyFilters());
        }
    }
    async loadRiskConfigs() {
        this.showLoadingState();
        try {
            const params = new URLSearchParams();
            const merchantId = this.getMerchantId();
            if (merchantId) {
                params.append('merchant_id',
                merchantId);
            }
            const status = this.getStatusFilter();
            if (status) {
                params.append('status',
                status);
            }
            const type = this.getTypeFilter();
            if (type) {
                params.append('type',
                type);
            }
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=risk_control&${
                params
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            let data;
            try {
                data = await response.json();
            } catch (parseError) {
                console.error('JSON parse error:',
                parseError);
                throw new Error('服务器返回了无效的响应格式');
            }
            // 检查HTTP状态码和业务状态码
            if (!response.ok || data.code === 401) {
                if (response.status === 401 || data.code === 401) {
                    // 认证失败，重新登录
                    console.log('认证失败，重新登录');
                    this.auth.logout();
                    window.location.reload();
                    return;
                } else {
                    throw new Error(data.message || `HTTP ${
                        response.status
                    }: ${
                        response.statusText
                    }`);
                }
            }
            if (data.code === 200) {
                this.renderRiskConfigs(data.data.configs);
                this.renderRiskStats(data.data.stats);
                this.updateRiskConfigCount(data.data.configs ? data.data.configs.length : 0);
            } else {
                this.showErrorState(data.message || '加载风控配置失败');
            }
        } catch (error) {
            console.error('Load risk configs error:',
            error);
            this.showErrorState('网络错误，请重试');
        }
    }
    renderRiskConfigs(configs) {
        const container = document.getElementById('riskConfigsTableBody');
        if (!container) return;
        if (!configs || configs.length === 0) {
            container.innerHTML = `
            <tr>
            <td colspan="7" class="text-center py-4">
            <div class="text-muted">
            <i class="fas fa-shield-alt fa-3x mb-3"></i>
            <p>暂无风控配置</p>
            </div>
            </td>
            </tr>
            `;
            return;
        }
        container.innerHTML = configs.map(config => this.renderRiskConfigRow(config)).join('');
    }
    renderRiskConfigRow(config) {
        const statusBadge = this.renderStatusBadge(config.status);
        const user = this.auth.getUser();
        return `
        <tr>
        <td>
        <div>
        <div class="fw-bold">${
            config.rule_name || '未命名规则'
        }</div>
        <small class="text-muted">${
            this.getRuleTypeText(config.rule_type)
        }</small>
        </div>
        </td>
        ${
            user.user_type === 'admin' ? `
            <td>
            <div>
            <div class="fw-bold">${
                config.merchant_name || '未知商户'
            }</div>
            <small class="text-muted">${
                config.merchant_real_name || ''
            }</small>
            </div>
            </td>
            ` : ''
        }
        <td>${
            config.daily_limit ? '¥' + parseFloat(config.daily_limit).toFixed(2) : '无限制'
        }</td>
        <td>${
            config.single_limit ? '¥' + parseFloat(config.single_limit).toFixed(2) : '无限制'
        }</td>
        <td>${
            statusBadge
        }</td>
        <td>
        <small class="text-muted">${
            this.formatDateTime(config.created_at)
        }</small>
        </td>
        <td>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-primary" onclick="window.riskControlManager.editRiskConfig(${
            config.id
        })" title="编辑">
        <i class="bi bi-pencil"></i>
        </button>
        <button class="btn btn-outline-danger" onclick="window.riskControlManager.deleteRiskConfig(${
            config.id
        })" title="删除">
        <i class="bi bi-trash"></i>
        </button>
        </div>
        </td>
        </tr>
        `;
    }
    renderStatusBadge(status) {
        const statusConfig = {
            'active': {
                class: 'bg-success',
                text: '启用'
            },
            'inactive': {
                class: 'bg-secondary',
                text: '禁用'
            }
        };
        const config = statusConfig[status] || {
            class: 'bg-secondary',
            text: '未知'
        };
        return `<span class="badge ${
            config.class
        }">${
            config.text
        }</span>`;
    }
    renderRiskStats(stats) {
        const container = document.getElementById('riskStats');
        if (!container || !stats) return;
        container.innerHTML = `
        <div class="row g-3">
        <div class="col-md-3">
        <div class="card border-0 bg-primary text-white">
        <div class="card-body">
        <div class="d-flex justify-content-between">
        <div>
        <h6 class="card-title mb-0">总配置数</h6>
        <h4 class="mb-0">${
            stats.total_configs || 0
        }</h4>
        </div>
        <i class="fas fa-shield-alt fa-2x opacity-50"></i>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 bg-success text-white">
        <div class="card-body">
        <div class="d-flex justify-content-between">
        <div>
        <h6 class="card-title mb-0">启用配置</h6>
        <h4 class="mb-0">${
            stats.active_configs || 0
        }</h4>
        </div>
        <i class="fas fa-check-circle fa-2x opacity-50"></i>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 bg-warning text-white">
        <div class="card-body">
        <div class="d-flex justify-content-between">
        <div>
        <h6 class="card-title mb-0">平均日限额</h6>
        <h4 class="mb-0">¥${
            parseFloat(stats.avg_daily_limit || 0).toFixed(0)
        }</h4>
        </div>
        <i class="fas fa-calendar-day fa-2x opacity-50"></i>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 bg-info text-white">
        <div class="card-body">
        <div class="d-flex justify-content-between">
        <div>
        <h6 class="card-title mb-0">平均单笔限额</h6>
        <h4 class="mb-0">¥${
            parseFloat(stats.avg_single_limit || 0).toFixed(0)
        }</h4>
        </div>
        <i class="fas fa-money-bill fa-2x opacity-50"></i>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    getRuleTypeText(ruleType) {
        const typeMap = {
            'amount_limit': '金额限制',
            'frequency_limit': '频率限制',
            'ip_control': 'IP控制',
            'device_control': '设备控制'
        };
        return typeMap[ruleType] || ruleType || '未知类型';
    }
    updateRiskConfigCount(count) {
        const countElement = document.getElementById('riskConfigCount');
        if (countElement) {
            countElement.textContent = count;
        }
    }
    getMerchantId() {
        const merchantFilter = document.getElementById('riskMerchantFilter');
        return merchantFilter ? merchantFilter.value : '';
    }
    getStatusFilter() {
        const statusFilter = document.getElementById('riskStatusFilter');
        return statusFilter ? statusFilter.value : '';
    }
    getTypeFilter() {
        const typeFilter = document.getElementById('riskTypeFilter');
        return typeFilter ? typeFilter.value : '';
    }
    async loadMerchantOptions() {
        const merchantFilter = document.getElementById('riskMerchantFilter');
        const merchantSelect = document.getElementById('riskMerchant');
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=merchants&limit=1000`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200) {
                const options = data.data.merchants.map(merchant =>
                `<option value="${
                    merchant.id
                }">${
                    merchant.company_name || merchant.real_name
                } (${
                    merchant.username
                })</option>`
                ).join('');
                if (merchantFilter) {
                    merchantFilter.innerHTML = '<option value="">全部商户</option>' + options;
                }
                if (merchantSelect) {
                    merchantSelect.innerHTML = '<option value="">请选择商户</option>' + options;
                }
            }
        } catch (error) {
            console.error('Load merchants error:',
            error);
        }
    }
    showCreateModal() {
        const modal = new bootstrap.Modal(document.getElementById('riskConfigModal'));
        document.getElementById('riskConfigModalTitle').textContent = '创建风控规则';
        document.getElementById('riskConfigForm').reset();
        document.getElementById('riskConfigId').value = '';
        // 加载商户选项（仅管理员）
        this.loadMerchantOptions();
        modal.show();
    }
    async editRiskConfig(configId) {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=risk_control&id=${
                configId
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200) {
                const config = data.data;
                this.fillEditForm(config);
                const modal = new bootstrap.Modal(document.getElementById('riskConfigModal'));
                document.getElementById('riskConfigModalTitle').textContent = '编辑风控规则';
                modal.show();
            } else {
                this.showError(data.message || '获取规则详情失败');
            }
        } catch (error) {
            console.error('Edit risk config error:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    fillEditForm(config) {
        document.getElementById('riskConfigId').value = config.id;
        document.getElementById('riskRuleName').value = config.rule_name || '';
        document.getElementById('riskRuleType').value = config.rule_type || '';
        document.getElementById('riskConfigStatus').value = config.status || 'active';
        document.getElementById('riskDailyLimit').value = config.daily_limit || '';
        document.getElementById('riskSingleLimit').value = config.single_limit || '';
        document.getElementById('riskRuleDescription').value = config.description || '';
        const merchantSelect = document.getElementById('riskMerchant');
        if (merchantSelect && config.merchant_id) {
            merchantSelect.value = config.merchant_id;
        }
    }
    async saveRiskConfig() {
        const configId = document.getElementById('riskConfigId').value;
        const isEdit = configId !== '';
        const formData = {
            rule_name: document.getElementById('riskRuleName').value,
            rule_type: document.getElementById('riskRuleType').value,
            status: document.getElementById('riskConfigStatus').value,
            daily_limit: document.getElementById('riskDailyLimit').value || null,
            single_limit: document.getElementById('riskSingleLimit').value || null,
            description: document.getElementById('riskRuleDescription').value
        };
        // 管理员需要指定商户
        const user = this.auth.getUser();
        if (user.user_type === 'admin') {
            const merchantId = document.getElementById('riskMerchant').value;
            if (!merchantId) {
                this.showError('请选择商户');
                return;
            }
            formData.merchant_id = merchantId;
        }
        // 商户用户会自动使用当前用户的profile_id，不需要在前端指定
        // 验证必填字段
        if (!formData.rule_name || !formData.rule_type) {
            this.showError('请填写规则名称和类型');
            return;
        }
        this.setSaveLoading(true);
        try {
            const url = isEdit ?
            `${
                CONFIG.API_BASE_URL
            }/admin.php?action=risk_control&id=${
                configId
            }` :
            `${
                CONFIG.API_BASE_URL
            }/admin.php?action=risk_control`;
            const response = await fetch(url, {
                method: isEdit ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify(formData)
            });
            const data = await response.json();
            if (data.code === 200) {
                this.showSuccess(isEdit ? '规则更新成功' : '规则创建成功');
                bootstrap.Modal.getInstance(document.getElementById('riskConfigModal')).hide();
                this.refreshRiskConfigs();
            } else {
                this.showError(data.message || '操作失败');
            }
        } catch (error) {
            console.error('Save risk config error:',
            error);
            this.showError('网络错误，请重试');
        } finally {
            this.setSaveLoading(false);
        }
    }
    async deleteRiskConfig(configId) {
        if (!confirm('确定要删除这个风控规则吗？删除后无法恢复。')) {
            return;
        }
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=risk_control&id=${
                configId
            }`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200) {
                this.showSuccess('规则删除成功');
                this.refreshRiskConfigs();
            } else {
                this.showError(data.message || '删除失败');
            }
        } catch (error) {
            console.error('Delete risk config error:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    setSaveLoading(loading) {
        const btn = document.getElementById('saveRiskConfigBtn');
        const spinner = btn.querySelector('.spinner-border');
        if (loading) {
            btn.disabled = true;
            spinner.style.display = 'inline-block';
        } else {
            btn.disabled = false;
            spinner.style.display = 'none';
        }
    }
    applyFilters() {
        this.loadRiskConfigs();
    }
    refreshRiskConfigs() {
        this.loadRiskConfigs();
    }
    showLoadingState() {
        const container = document.getElementById('riskConfigsTableBody');
        if (container) {
            container.innerHTML = `
            <tr>
            <td colspan="7" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">加载风控配置中...</p>
            </td>
            </tr>
            `;
        }
    }
    showErrorState(message) {
        const container = document.getElementById('riskConfigsTableBody');
        if (container) {
            const user = this.auth.getUser();
            const colspan = user.user_type === 'admin' ? '7' : '6';
            container.innerHTML = `
            <tr>
            <td colspan="${
                colspan
            }" class="text-center py-4">
            <div class="text-danger">
            <i class="bi bi-exclamation-triangle fa-3x mb-3"></i>
            <p>${
                message
            }</p>
            <button class="btn btn-outline-primary btn-sm" onclick="window.riskControlManager.refreshRiskConfigs()">
            <i class="bi bi-arrow-clockwise me-2"></i>重试
            </button>
            </div>
            </td>
            </tr>
            `;
        }
    }
    renderStatusBadge(status) {
        const statusMap = {
            'active': {
                text: '启用',
                class: 'bg-success'
            },
            'inactive': {
                text: '禁用',
                class: 'bg-secondary'
            }
        };
        const statusInfo = statusMap[status] || {
            text: status,
            class: 'bg-secondary'
        };
        return `<span class="badge ${
            statusInfo.class
        }">${
            statusInfo.text
        }</span>`;
    }
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    showError(message) {
        // 简单的错误提示，实际项目中可以使用更好的提示组件
        alert('错误: ' + message);
    }
    showSuccess(message) {
        // 简单的成功提示，实际项目中可以使用更好的提示组件
        alert('成功: ' + message);
    }
    formatDateTime(dateString) {
        if (!dateString) return '-';
        try {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return dateString;
        }
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = RiskControlManager;
} else if (typeof window !== "undefined") {
    window.RiskControlManager = RiskControlManager;
}

console.log('📦 RiskControlManager 模块加载完成');
