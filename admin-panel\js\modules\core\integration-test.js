/**
 * 系统集成测试
 * 验证所有模块协同工作和端到端功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class IntegrationTest {
    constructor() {
        this.testScenarios = new Map();
        this.results = new Map();
        this.isRunning = false;
        
        console.log('🔧 IntegrationTest initialized');
        this.setupTestScenarios();
    }

    /**
     * 设置集成测试场景
     */
    setupTestScenarios() {
        // 核心系统集成测试
        this.addScenario('core-systems', '核心系统集成测试', this.testCoreSystems.bind(this));
        
        // 用户流程测试
        this.addScenario('user-login-flow', '用户登录流程测试', this.testUserLoginFlow.bind(this));
        this.addScenario('module-navigation', '模块导航测试', this.testModuleNavigation.bind(this));
        this.addScenario('data-crud-flow', '数据CRUD流程测试', this.testDataCRUDFlow.bind(this));
        
        // 性能集成测试
        this.addScenario('performance-integration', '性能集成测试', this.testPerformanceIntegration.bind(this));
        
        // 错误处理集成测试
        this.addScenario('error-handling-integration', '错误处理集成测试', this.testErrorHandlingIntegration.bind(this));
    }

    addScenario(id, name, testFunction) {
        this.testScenarios.set(id, { id, name, testFunction });
    }

    /**
     * 运行所有集成测试
     */
    async runAllIntegrationTests() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.results.clear();
        
        console.log('🚀 开始运行集成测试...');
        
        for (const [id, scenario] of this.testScenarios) {
            await this.runScenario(id);
        }
        
        this.isRunning = false;
        return this.generateIntegrationReport();
    }

    async runScenario(scenarioId) {
        const scenario = this.testScenarios.get(scenarioId);
        if (!scenario) return;
        
        console.log(`🧪 运行集成测试: ${scenario.name}`);
        
        const result = {
            id: scenarioId,
            name: scenario.name,
            status: 'running',
            startTime: Date.now(),
            steps: [],
            errors: []
        };
        
        try {
            const testResult = await scenario.testFunction();
            result.status = 'passed';
            result.steps = testResult.steps || [];
            result.details = testResult.details || {};
        } catch (error) {
            result.status = 'failed';
            result.errors.push(error.message);
            console.error(`❌ 集成测试失败: ${scenario.name}`, error);
        }
        
        result.duration = Date.now() - result.startTime;
        this.results.set(scenarioId, result);
    }

    /**
     * 测试核心系统集成
     */
    async testCoreSystems() {
        const steps = [];
        const details = {};
        
        // 步骤1: 验证所有核心系统已初始化
        steps.push('验证核心系统初始化');
        const coreSystemsReady = {
            smartModuleLoader: !!window.smartModuleLoader,
            eventBus: !!window.eventBus,
            globalState: !!window.GlobalState,
            uiComponents: !!window.UI,
            performanceMonitor: !!window.performanceMonitor,
            optimizationManager: !!window.optimizationManager,
            uxEnhancer: !!window.uxEnhancer
        };
        
        details.coreSystemsReady = coreSystemsReady;
        
        if (!Object.values(coreSystemsReady).every(Boolean)) {
            throw new Error('部分核心系统未初始化');
        }
        
        // 步骤2: 测试系统间通信
        steps.push('测试系统间通信');
        let communicationTest = false;
        
        const unsubscribe = window.eventBus.on('integration:test', () => {
            communicationTest = true;
        });
        
        window.eventBus.emit('integration:test');
        await new Promise(resolve => setTimeout(resolve, 100));
        
        details.systemCommunication = communicationTest;
        unsubscribe();
        
        if (!communicationTest) {
            throw new Error('系统间通信测试失败');
        }
        
        // 步骤3: 测试状态同步
        steps.push('测试状态同步');
        const testData = { integration: true, timestamp: Date.now() };
        
        window.GlobalState.setState('integration.test', testData);
        const retrievedData = window.GlobalState.getState('integration.test');
        
        details.stateSynchronization = JSON.stringify(retrievedData) === JSON.stringify(testData);
        
        if (!details.stateSynchronization) {
            throw new Error('状态同步测试失败');
        }
        
        return { steps, details };
    }

    /**
     * 测试用户登录流程
     */
    async testUserLoginFlow() {
        const steps = [];
        const details = {};
        
        // 步骤1: 模拟用户登录
        steps.push('模拟用户登录');
        const mockUser = {
            id: 1,
            username: 'test_user',
            user_type: 'admin',
            permissions: ['read', 'write', 'delete']
        };
        
        window.GlobalState.setState('user', mockUser);
        window.GlobalState.setState('isAuthenticated', true);
        
        // 步骤2: 验证权限状态
        steps.push('验证权限状态');
        const user = window.GlobalState.getState('user');
        const isAuthenticated = window.GlobalState.getState('isAuthenticated');
        
        details.userLogin = user && user.id === 1;
        details.authenticationStatus = isAuthenticated;
        
        // 步骤3: 测试权限检查
        steps.push('测试权限检查');
        if (window.smartModuleLoader) {
            const hasPermission = window.smartModuleLoader.hasPermission('device-management');
            details.permissionCheck = hasPermission;
        }
        
        // 步骤4: 触发登录事件
        steps.push('触发登录事件');
        window.eventBus.emit('user:login', mockUser);
        
        return { steps, details };
    }

    /**
     * 测试模块导航
     */
    async testModuleNavigation() {
        const steps = [];
        const details = {};
        
        // 步骤1: 测试模块加载
        steps.push('测试模块加载');
        if (window.smartModuleLoader) {
            try {
                await window.smartModuleLoader.loadModule('device-management', { silent: true });
                details.moduleLoading = true;
            } catch (error) {
                details.moduleLoading = false;
                details.moduleLoadError = error.message;
            }
        }
        
        // 步骤2: 测试导航状态更新
        steps.push('测试导航状态更新');
        window.GlobalState.setState('currentModule', 'device-management');
        const currentModule = window.GlobalState.getState('currentModule');
        details.navigationState = currentModule === 'device-management';
        
        // 步骤3: 测试UI更新
        steps.push('测试UI更新');
        window.eventBus.emit('navigation:changed', { module: 'device-management' });
        details.uiUpdate = true; // 假设UI已更新
        
        return { steps, details };
    }

    /**
     * 测试数据CRUD流程
     */
    async testDataCRUDFlow() {
        const steps = [];
        const details = {};
        
        // 模拟数据
        const testData = {
            id: Date.now(),
            name: '测试设备',
            status: 'active',
            created_at: new Date().toISOString()
        };
        
        // 步骤1: 创建数据
        steps.push('创建数据');
        window.GlobalState.setState('modules.device.devices', [testData]);
        const devices = window.GlobalState.getState('modules.device.devices');
        details.dataCreation = devices.length > 0;
        
        // 步骤2: 读取数据
        steps.push('读取数据');
        const retrievedDevice = devices.find(d => d.id === testData.id);
        details.dataReading = !!retrievedDevice;
        
        // 步骤3: 更新数据
        steps.push('更新数据');
        const updatedData = { ...testData, status: 'inactive' };
        const updatedDevices = devices.map(d => d.id === testData.id ? updatedData : d);
        window.GlobalState.setState('modules.device.devices', updatedDevices);
        
        const updatedDevice = window.GlobalState.getState('modules.device.devices')
            .find(d => d.id === testData.id);
        details.dataUpdate = updatedDevice.status === 'inactive';
        
        // 步骤4: 删除数据
        steps.push('删除数据');
        const filteredDevices = updatedDevices.filter(d => d.id !== testData.id);
        window.GlobalState.setState('modules.device.devices', filteredDevices);
        
        const remainingDevices = window.GlobalState.getState('modules.device.devices');
        details.dataDeletion = !remainingDevices.find(d => d.id === testData.id);
        
        return { steps, details };
    }

    /**
     * 测试性能集成
     */
    async testPerformanceIntegration() {
        const steps = [];
        const details = {};
        
        // 步骤1: 测试性能监控数据收集
        steps.push('测试性能监控数据收集');
        if (window.performanceMonitor) {
            const summary = window.performanceMonitor.getPerformanceSummary();
            details.performanceDataCollection = !!summary;
            details.performanceMetrics = summary;
        }
        
        // 步骤2: 测试优化管理器
        steps.push('测试优化管理器');
        if (window.optimizationManager) {
            const stats = window.optimizationManager.getOptimizationStats();
            details.optimizationStats = stats;
            details.optimizationWorking = stats.resourcesCached >= 0;
        }
        
        // 步骤3: 测试性能事件
        steps.push('测试性能事件');
        let performanceEventReceived = false;
        
        const unsubscribe = window.eventBus.on('performance:metric', () => {
            performanceEventReceived = true;
        });
        
        // 触发性能事件
        window.eventBus.emit('performance:metric', { type: 'test', data: {} });
        await new Promise(resolve => setTimeout(resolve, 100));
        
        details.performanceEventHandling = performanceEventReceived;
        unsubscribe();
        
        return { steps, details };
    }

    /**
     * 测试错误处理集成
     */
    async testErrorHandlingIntegration() {
        const steps = [];
        const details = {};
        
        // 步骤1: 测试JavaScript错误处理
        steps.push('测试JavaScript错误处理');
        let errorHandled = false;
        
        const originalErrorHandler = window.onerror;
        window.onerror = () => {
            errorHandled = true;
            return true;
        };
        
        try {
            throw new Error('集成测试错误');
        } catch (error) {
            details.errorCatching = true;
        }
        
        window.onerror = originalErrorHandler;
        
        // 步骤2: 测试Promise rejection处理
        steps.push('测试Promise rejection处理');
        let rejectionHandled = false;
        
        const originalRejectionHandler = window.onunhandledrejection;
        window.onunhandledrejection = () => {
            rejectionHandled = true;
            return true;
        };
        
        Promise.reject('测试rejection').catch(() => {
            details.rejectionHandling = true;
        });
        
        await new Promise(resolve => setTimeout(resolve, 100));
        
        window.onunhandledrejection = originalRejectionHandler;
        
        // 步骤3: 测试错误事件传播
        steps.push('测试错误事件传播');
        let errorEventReceived = false;
        
        const unsubscribe = window.eventBus.on('error:occurred', () => {
            errorEventReceived = true;
        });
        
        window.eventBus.emit('error:occurred', { message: '测试错误' });
        await new Promise(resolve => setTimeout(resolve, 100));
        
        details.errorEventPropagation = errorEventReceived;
        unsubscribe();
        
        return { steps, details };
    }

    /**
     * 生成集成测试报告
     */
    generateIntegrationReport() {
        const results = Array.from(this.results.values());
        const passed = results.filter(r => r.status === 'passed').length;
        const failed = results.filter(r => r.status === 'failed').length;
        
        const report = {
            summary: {
                total: results.length,
                passed,
                failed,
                successRate: (passed / results.length * 100).toFixed(2)
            },
            scenarios: results,
            timestamp: new Date().toISOString(),
            systemHealth: this.assessSystemHealth(results)
        };
        
        console.log('📊 集成测试报告生成完成');
        console.table(report.summary);
        
        // 触发报告事件
        if (window.eventBus) {
            window.eventBus.emit('integration:test-completed', report);
        }
        
        return report;
    }

    /**
     * 评估系统健康状态
     */
    assessSystemHealth(results) {
        const criticalTests = ['core-systems', 'user-login-flow', 'performance-integration'];
        const criticalResults = results.filter(r => criticalTests.includes(r.id));
        const criticalPassed = criticalResults.filter(r => r.status === 'passed').length;
        
        let healthStatus = 'healthy';
        let healthScore = 100;
        
        if (criticalPassed < criticalResults.length) {
            healthStatus = 'critical';
            healthScore = 30;
        } else if (results.some(r => r.status === 'failed')) {
            healthStatus = 'warning';
            healthScore = 70;
        }
        
        return {
            status: healthStatus,
            score: healthScore,
            criticalSystemsOk: criticalPassed === criticalResults.length,
            recommendations: this.generateRecommendations(results)
        };
    }

    /**
     * 生成改进建议
     */
    generateRecommendations(results) {
        const recommendations = [];
        
        const failedTests = results.filter(r => r.status === 'failed');
        
        if (failedTests.length > 0) {
            recommendations.push('修复失败的集成测试');
            
            failedTests.forEach(test => {
                recommendations.push(`- 检查 ${test.name} 的具体错误`);
            });
        }
        
        // 性能建议
        const performanceTest = results.find(r => r.id === 'performance-integration');
        if (performanceTest && performanceTest.details.performanceMetrics) {
            const metrics = performanceTest.details.performanceMetrics;
            if (metrics.runtime > 60000) { // 超过1分钟
                recommendations.push('考虑优化系统启动时间');
            }
        }
        
        if (recommendations.length === 0) {
            recommendations.push('系统运行良好，继续保持');
        }
        
        return recommendations;
    }

    /**
     * 运行健康检查
     */
    async runHealthCheck() {
        const healthChecks = {
            coreSystemsLoaded: this.checkCoreSystemsLoaded(),
            memoryUsage: this.checkMemoryUsage(),
            errorRate: this.checkErrorRate(),
            performanceMetrics: this.checkPerformanceMetrics()
        };
        
        const results = {};
        
        for (const [check, promise] of Object.entries(healthChecks)) {
            try {
                results[check] = await promise;
            } catch (error) {
                results[check] = { status: 'error', message: error.message };
            }
        }
        
        return {
            timestamp: new Date().toISOString(),
            checks: results,
            overallHealth: this.calculateOverallHealth(results)
        };
    }

    checkCoreSystemsLoaded() {
        const systems = [
            'smartModuleLoader',
            'eventBus', 
            'GlobalState',
            'UI',
            'performanceMonitor'
        ];
        
        const loaded = systems.filter(system => !!window[system]);
        
        return {
            status: loaded.length === systems.length ? 'ok' : 'warning',
            loaded: loaded.length,
            total: systems.length,
            missing: systems.filter(system => !window[system])
        };
    }

    checkMemoryUsage() {
        if (!window.performance || !window.performance.memory) {
            return { status: 'unknown', message: '内存API不可用' };
        }
        
        const memory = window.performance.memory;
        const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
        
        return {
            status: usagePercent < 70 ? 'ok' : usagePercent < 85 ? 'warning' : 'critical',
            usagePercent: usagePercent.toFixed(2),
            used: memory.usedJSHeapSize,
            limit: memory.jsHeapSizeLimit
        };
    }

    checkErrorRate() {
        // 简化的错误率检查
        return {
            status: 'ok',
            errorCount: 0,
            message: '错误率检查功能开发中'
        };
    }

    checkPerformanceMetrics() {
        if (!window.performanceMonitor) {
            return { status: 'error', message: 'PerformanceMonitor未初始化' };
        }
        
        const summary = window.performanceMonitor.getPerformanceSummary();
        
        return {
            status: 'ok',
            metrics: summary,
            message: '性能指标正常'
        };
    }

    calculateOverallHealth(results) {
        const statuses = Object.values(results).map(r => r.status);
        
        if (statuses.includes('critical')) return 'critical';
        if (statuses.includes('error')) return 'error';
        if (statuses.includes('warning')) return 'warning';
        return 'healthy';
    }
}

// 创建全局集成测试实例
if (typeof window !== 'undefined') {
    window.IntegrationTest = IntegrationTest;
    window.integrationTest = new IntegrationTest();
    
    console.log('🎯 集成测试系统已就绪');
} 