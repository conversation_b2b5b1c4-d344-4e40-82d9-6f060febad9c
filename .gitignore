# 默认忽略所有文件和文件夹
*

# 但是允许以下文件夹和文件
!.gitignore
!admin-panel/
!admin-panel/**
!backend/
!backend/**

# 开发工具文件
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 配置文件中的敏感信息（如果有的话）
config/local.php

# Node.js
node_modules/
npm-debug.log*

# Android开发文件
*.apk
*.dex
.gradle/
build/
*.class

# 数据库备份
*.sql.backup
*.dump

# 原始脚本文件（安全考虑）
scripts/
!scripts/README.md

*.iml
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
/captures
.externalNativeBuild
.cxx
local.properties

# PayPal项目特定忽略规则
# 完全忽略qrcode.top670.com整个文件夹
qrcode.top670.com/

# 忽略app目录（Android项目）
app/

# 忽略alipay-check项目
alipay-check-0408-main/

# 忽略gradle相关文件
gradle/
gradlew
gradlew.bat

# 忽略其他项目文件
frontend/
docs/
deploy/
database/
*.xml
*.md
*.ps1

# 测试文件
test_api.php
