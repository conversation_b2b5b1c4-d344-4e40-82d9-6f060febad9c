/**
 * 精简版应用初始化器
 * 移除冗余代码，优化性能
 * 
 * <AUTHOR>
 * @version 2.1.0 - 精简优化版本
 */

class AppInitializer {
    constructor(tenantConfig) {
        this.tenantConfig = tenantConfig;
        this.isInitialized = false;
        this.startTime = Date.now();
        this.currentUser = null;
        this.isAuthenticated = false;
        
        console.log('🚀 应用初始化器启动:', tenantConfig.tenantType);
    }

    async initialize() {
        try {
            console.log('📋 开始应用初始化...');
            
            await this._initializeCore();
            await this._checkAuthentication();
            
            if (this.isAuthenticated) {
                await this._initializeMainApp();
            } else {
                await this._initializeLoginInterface();
            }
            
            this._completeInitialization();
            
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this._showError(error);
        }
    }

    async _initializeCore() {
        console.log('⚙️ 初始化核心系统...');

        this._applyTheme();

        // 设置全局租户信息
        if (this.tenantConfig && this.tenantConfig.tenantInfo) {
            window.TENANT_INFO = this.tenantConfig.tenantInfo;
            console.log('✅ 全局租户信息已设置:', window.TENANT_INFO);
        } else {
            console.warn('⚠️ 租户配置中缺少租户信息');
        }

        if (typeof window.Utils !== 'undefined') {
            this.utils = new window.Utils();
        }

        if (typeof window.AuthManager !== 'undefined') {
            this.authManager = new window.AuthManager();
        }

        // 初始化API客户端
        await this._initializeApiClient();

        console.log('✅ 核心系统初始化完成');
    }

    async _initializeApiClient() {
        // 创建简单的API客户端
        window.apiClient = {
            baseURL: '../backend/api',

            async get(url) {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`${this.baseURL}${url}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                return await response.json();
            },

            async post(url, data) {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`${this.baseURL}${url}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                return await response.json();
            },

            async put(url, data) {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`${this.baseURL}${url}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                return await response.json();
            },

            async delete(url) {
                const token = localStorage.getItem('auth_token');
                const response = await fetch(`${this.baseURL}${url}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                return await response.json();
            }
        };

        console.log('✅ API客户端初始化完成');
    }

    async _checkAuthentication() {
        console.log('🔐 检查认证状态...');
        
        try {
            const token = localStorage.getItem('auth_token');
            const userInfo = localStorage.getItem('user_info');
            
            if (token && userInfo) {
                this.currentUser = JSON.parse(userInfo);
                this.isAuthenticated = true;
                console.log('✅ 用户已认证:', this.currentUser.username);
            } else {
                console.log('❌ 未找到认证信息');
                this.isAuthenticated = false;
            }
            
        } catch (error) {
            console.log('❌ 认证检查失败:', error.message);
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_info');
            this.isAuthenticated = false;
        }
    }

    async _initializeMainApp() {
        console.log('🏠 初始化主应用...');
        
        this._showContainer('app');
        this._createAppLayout();
        await this._loadTenantRoutes();
        this._initializeRouter();
        this._renderSidebarMenu();
        
        document.title = `${this.tenantConfig.theme.brandName} - ${this.currentUser.username}`;
        console.log('✅ 主应用初始化完成');
    }

    async _initializeLoginInterface() {
        console.log('🔑 初始化登录界面...');
        
        this._showContainer('login');
        this._createLoginInterface();
        this._bindLoginEvents();
        
        document.title = `${this.tenantConfig.theme.brandName} - 登录`;
        console.log('✅ 登录界面初始化完成');
    }

    _showContainer(type) {
        console.log(`🔄 切换容器显示模式: ${type}`);

        const loginContainer = document.getElementById('loginContainer');
        const appContainer = document.getElementById('appContainer');

        if (type === 'login') {
            console.log('📱 显示登录界面');
            if (appContainer) appContainer.style.display = 'none';
            if (loginContainer) {
                loginContainer.style.display = 'flex';
                loginContainer.className = 'login-container';
            }
            // 应用登录界面样式
            document.body.classList.add('login-mode');
            console.log('✅ 已添加 login-mode 类');
        } else {
            console.log('🏠 显示管理后台');
            if (loginContainer) loginContainer.style.display = 'none';
            if (appContainer) {
                appContainer.style.display = 'block';
                appContainer.style.backgroundColor = '#f8f9fa';
                appContainer.className = 'app-layout';
            }
            // 移除登录界面样式，应用管理后台样式
            document.body.classList.remove('login-mode');
            console.log('✅ 已移除 login-mode 类');

            // 移除登录CSS文件
            const loginCSS = document.getElementById('loginSimpleCSS');
            if (loginCSS) {
                loginCSS.remove();
                console.log('✅ 已移除登录CSS文件');
            }

            // 强制设置背景色
            document.body.style.backgroundColor = '#f8f9fa';
            document.body.style.backgroundImage = 'none';
            document.body.style.background = '#f8f9fa';

            // 强制设置HTML元素背景
            document.documentElement.style.backgroundColor = '#f8f9fa';
            document.documentElement.style.backgroundImage = 'none';
            document.documentElement.style.background = '#f8f9fa';

            // 强制设置所有可能的容器背景
            const containers = ['appContainer', 'mainContent', 'sidebarContainer'];
            containers.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.style.backgroundColor = '#f8f9fa';
                    element.style.backgroundImage = 'none';
                    element.style.background = '#f8f9fa';
                }
            });

            console.log('✅ 已强制设置背景色');

            // 持续监控和强制设置背景色
            const forceBackground = () => {
                document.body.style.backgroundColor = '#f8f9fa';
                document.body.style.backgroundImage = 'none';
                document.body.style.background = '#f8f9fa';
                document.documentElement.style.backgroundColor = '#f8f9fa';
                document.documentElement.style.backgroundImage = 'none';
                document.documentElement.style.background = '#f8f9fa';
            };

            // 每100ms检查一次，持续5秒
            let checkCount = 0;
            const backgroundInterval = setInterval(() => {
                forceBackground();
                checkCount++;
                if (checkCount >= 50) { // 5秒后停止
                    clearInterval(backgroundInterval);
                    console.log('✅ 背景色强制设置完成');
                }
            }, 100);
        }

        console.log('当前body类名:', document.body.className);
        console.log('当前body样式:', document.body.style.background);
    }

    _applyTheme() {
        const theme = this.tenantConfig.theme;
        document.documentElement.style.setProperty('--primary-color', theme.primaryColor);
        
        if (!document.getElementById('tenantTheme')) {
            const style = document.createElement('style');
            style.id = 'tenantTheme';
            style.textContent = `
                .btn-primary { background-color: ${theme.primaryColor} !important; border-color: ${theme.primaryColor} !important; }
                .text-primary { color: ${theme.primaryColor} !important; }
                .login-container { 
                    min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    align-items: center; justify-content: center; padding: 20px;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                }
            `;
            document.head.appendChild(style);
        }
    }

    _createAppLayout() {
        const appContainer = document.getElementById('appContainer');
        if (!appContainer) return;
        
        appContainer.innerHTML = `
            <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                <div class="container-fluid">
                    <a class="navbar-brand fw-bold" href="#">
                        <i class="bi-credit-card me-2"></i>${this.tenantConfig.theme.brandName}
                    </a>
                    <div class="navbar-nav ms-auto">
                        <div class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                <i class="bi-person-circle me-2"></i>${this.currentUser.username}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#/profile"><i class="bi-person me-2"></i>个人资料</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="app.logout()"><i class="bi-box-arrow-right me-2"></i>退出</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            <div class="d-flex app-layout">
                <div class="sidebar" id="sidebarContainer">
                    <nav class="nav flex-column" id="sidebarMenu">
                        <div class="nav-header">主要功能</div>
                    </nav>
                </div>
                <div class="main-content" id="mainContent">
                    <div class="page-header">
                        <h1><i class="bi-speedometer2"></i>正在加载应用...</h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="#">首页</a></li>
                                <li class="breadcrumb-item active" aria-current="page">加载中</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-3 mb-0">系统正在初始化，请稍候...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    _createLoginInterface() {
        const loginContainer = document.getElementById('loginContainer');
        if (!loginContainer) return;
        
        // 添加登录CSS
        if (!document.getElementById('loginSimpleCSS')) {
            const cssLink = document.createElement('link');
            cssLink.id = 'loginSimpleCSS';
            cssLink.rel = 'stylesheet';
            cssLink.href = './css/login-simple.css';
            document.head.appendChild(cssLink);
        }
        
        loginContainer.innerHTML = `
            <div class="login-card">
                <h1 class="login-title">PayPal</h1>
                <p class="login-subtitle">请输入您的登录凭据</p>
                <div id="loginAlert"></div>
                <form id="loginForm">
                    <div class="form-group">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" id="username" placeholder="请输入用户名" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" placeholder="请输入密码" required>
                    </div>
                    <div class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <label for="rememberMe">记住我</label>
                    </div>
                    <button type="submit" class="btn-login">登录</button>
                </form>
                <div class="test-info">
                    <strong>测试账号：</strong><br>
                    用户名: <strong>platform_admin_001</strong><br>
                    密码: <strong>password</strong>
                </div>
            </div>
        `;
    }

    async _loadTenantRoutes() {
        const tenantType = this.tenantConfig.tenantType;
        const routeConfigName = tenantType
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('') + 'Routes';
        
        console.log(`🔍 查找路由配置: ${routeConfigName}`);
        
        if (window[routeConfigName]) {
            this.routeConfig = window[routeConfigName];
            console.log(`✅ 路由配置加载完成: ${routeConfigName}`);
        } else {
            const available = Object.keys(window).filter(key => key.endsWith('Routes'));
            console.error(`❌ 可用配置:`, available);
            throw new Error(`路由配置未找到: ${routeConfigName}`);
        }
    }

    _initializeRouter() {
        this.router = {
            currentRoute: null,
            navigate: async (path) => {
                console.log('🧭 导航到:', path);
                await this._renderRoute(path);
            }
        };

        window.addEventListener('hashchange', async () => {
            const path = window.location.hash.slice(1) || this.routeConfig.defaultRoute;
            await this.router.navigate(path);
        });

        const initialPath = window.location.hash.slice(1) || this.routeConfig.defaultRoute;
        this.router.navigate(initialPath);
    }

    _renderSidebarMenu() {
        const menuContainer = document.getElementById('sidebarMenu');
        if (!menuContainer || !this.routeConfig) return;
        
        const menuItems = this.routeConfig.menuConfig.map(item => `
            <div class="mb-2">
                <a href="#${item.path}" class="btn btn-outline-secondary w-100 text-start">
                    <i class="${item.icon} me-2"></i>${item.name}
                </a>
            </div>
        `).join('');
        
        menuContainer.innerHTML = `
            <h6 class="text-muted mb-3">功能菜单</h6>
            ${menuItems}
        `;
    }

    async _renderRoute(path) {
        const route = this.routeConfig?.routes[path];
        if (!route) {
            console.warn('⚠️ 路由未找到:', path);
            return;
        }

        const mainContent = document.getElementById('mainContent');
        if (mainContent) {
            mainContent.innerHTML = `
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">${route.title}</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="#/dashboard">首页</a></li>
                            <li class="breadcrumb-item active">${route.title}</li>
                        </ol>
                    </nav>
                </div>
                <div class="alert alert-info" id="loadingAlert">
                    <i class="bi-info-circle me-2"></i>正在加载 ${route.component} 模块...
                </div>
                <div id="moduleContainer" style="display: none;"></div>
            `;
        }

        this.router.currentRoute = path;

        // 加载并渲染模块
        try {
            await this._loadAndRenderModule(route, mainContent);
        } catch (error) {
            console.error('❌ 模块加载失败:', error);
            this._showModuleError(mainContent, route, error);
        }
    }

    async _loadAndRenderModule(route, container) {
        const moduleContainer = container.querySelector('#moduleContainer');
        const loadingAlert = container.querySelector('#loadingAlert');

        // 根据组件名称加载对应的模块
        let moduleInstance = null;

        switch (route.component) {
            case 'MerchantManagementModule':
                moduleInstance = await this._loadMerchantModule();
                break;
            case 'ProviderManagementModule':
                moduleInstance = await this._loadProviderModule();
                break;
            case 'PlatformDashboardModule':
                moduleInstance = await this._loadDashboardModule();
                break;
            default:
                throw new Error(`未知的模块组件: ${route.component}`);
        }

        if (moduleInstance && typeof moduleInstance.render === 'function') {
            // 隐藏加载提示，显示模块内容
            loadingAlert.style.display = 'none';
            moduleContainer.style.display = 'block';

            // 渲染模块
            await moduleInstance.render(moduleContainer);
            console.log(`✅ ${route.component} 模块渲染完成`);
        } else {
            throw new Error(`模块 ${route.component} 没有render方法`);
        }
    }

    async _loadMerchantModule() {
        // 检查是否已经加载了商户管理模块
        if (window.MerchantManagementModule) {
            console.log('📦 使用已加载的商户管理模块');
            return window.MerchantManagementModule;
        }

        // 动态加载商户管理模块
        console.log('📥 动态加载商户管理模块...');
        await this._loadScript('js/modules/business/merchant-management.js');

        if (window.MerchantManagementModule) {
            // 初始化模块
            await window.MerchantManagementModule.init({
                apiClient: window.apiClient,
                authManager: window.authManager,
                utils: window.utils,
                tenantInfo: window.TENANT_INFO
            });
            return window.MerchantManagementModule;
        } else {
            throw new Error('商户管理模块加载失败');
        }
    }

    async _loadProviderModule() {
        if (window.ProviderManagementModule) {
            return window.ProviderManagementModule;
        }

        await this._loadScript('js/modules/business/provider-management.js');

        if (window.ProviderManagementModule) {
            await window.ProviderManagementModule.initialize();
            return window.ProviderManagementModule;
        } else {
            throw new Error('码商管理模块加载失败');
        }
    }

    async _loadDashboardModule() {
        // 简单的仪表板实现
        return {
            render: (container) => {
                container.innerHTML = `
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card text-white bg-primary">
                                <div class="card-body">
                                    <h5 class="card-title">商户总数</h5>
                                    <h2 class="card-text">--</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-success">
                                <div class="card-body">
                                    <h5 class="card-title">码商总数</h5>
                                    <h2 class="card-text">--</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-info">
                                <div class="card-body">
                                    <h5 class="card-title">今日订单</h5>
                                    <h2 class="card-text">--</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-warning">
                                <div class="card-body">
                                    <h5 class="card-title">今日收入</h5>
                                    <h2 class="card-text">--</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        };
    }

    async _loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src + '?v=' + Date.now();
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    _showModuleError(container, route, error) {
        const moduleContainer = container.querySelector('#moduleContainer');
        const loadingAlert = container.querySelector('#loadingAlert');

        loadingAlert.style.display = 'none';
        moduleContainer.style.display = 'block';
        moduleContainer.innerHTML = `
            <div class="alert alert-danger">
                <h4><i class="bi-exclamation-triangle me-2"></i>模块加载失败</h4>
                <p><strong>模块:</strong> ${route.component}</p>
                <p><strong>错误:</strong> ${error.message}</p>
                <button class="btn btn-primary" onclick="location.reload()">重新加载页面</button>
            </div>
        `;
    }

    _bindLoginEvents() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this._handleLogin();
            });
        }
    }

    async _handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const alertContainer = document.getElementById('loginAlert');
        
        if (!username || !password) {
            this._showAlert('请输入用户名和密码', 'warning');
            return;
        }
        
        try {
            this._showAlert('正在验证登录信息...', 'info');
            
            const apiUrl = `${window.CONFIG?.API_BASE_URL || '/api'}/admin.php?action=login`;
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ username, password })
            });
            
            if (!response.ok) throw new Error(`网络错误: ${response.status}`);
            
            const result = await response.json();
            
            if (result.code === 200 && result.data) {
                localStorage.setItem('auth_token', result.data.token);
                localStorage.setItem('user_info', JSON.stringify(result.data.user));
                this.currentUser = result.data.user;
                this.isAuthenticated = true;
                
                this._showAlert('登录成功，正在跳转...', 'success');
                
                setTimeout(() => {
                    this._initializeMainApp();
                }, 1000);
                
            } else {
                throw new Error(result.message || '登录失败');
            }
            
        } catch (error) {
            console.error('登录错误:', error);
            this._showAlert(error.message, 'error');
        }
    }

    _showAlert(message, type) {
        const alertContainer = document.getElementById('loginAlert');
        if (!alertContainer) return;
        
        const alertClass = {
            info: 'alert-info',
            success: 'alert-success', 
            warning: 'alert-warning',
            error: 'alert-danger'
        }[type] || 'alert-info';
        
        const icon = {
            info: 'bi-hourglass-split',
            success: 'bi-check-circle',
            warning: 'bi-exclamation-triangle', 
            error: 'bi-exclamation-triangle'
        }[type] || 'bi-info-circle';
        
        alertContainer.innerHTML = `
            <div class="alert ${alertClass}">
                <i class="${icon} me-2"></i>${message}
            </div>
        `;
    }

    _completeInitialization() {
        this.isInitialized = true;
        const initTime = Date.now() - this.startTime;
        
        window.app = this;
        
        console.log(`🎉 应用初始化完成 (${initTime}ms)`);
        console.log('📊 统计:', {
            租户类型: this.tenantConfig.tenantType,
            认证状态: this.isAuthenticated,
            初始化时间: `${initTime}ms`
        });
    }

    _showError(error) {
        document.body.innerHTML = `
            <div class="d-flex align-items-center justify-content-center min-vh-100">
                <div class="text-center">
                    <i class="bi-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                    <h2 class="mt-3">应用初始化失败</h2>
                    <p class="text-muted">${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="bi-arrow-clockwise me-2"></i>重新加载
                    </button>
                </div>
            </div>
        `;
    }

    logout() {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_info');
        location.reload();
    }
}

// 导出到全局
window.AppInitializer = AppInitializer;
console.log('✅ 精简版应用初始化器已加载'); 