<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备模块全局变量检测</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        .test-step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 设备模块全局变量检测</h1>
        <p>专门用于检测设备管理模块的全局变量导出情况</p>
        
        <div id="log-container"></div>
        
        <div class="test-step">
            <h3>📋 测试步骤</h3>
            <button onclick="runTest()">🚀 开始测试</button>
            <button onclick="clearLog()">🗑️ 清空日志</button>
            <button onclick="checkGlobalVariables()">🔍 检查全局变量</button>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('log-container');
            const logDiv = document.createElement('div');
            logDiv.className = `status ${type}`;
            logDiv.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logDiv);
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log-container').innerHTML = '';
        }

        function checkGlobalVariables() {
            log('🔍 检查当前全局变量状态...');
            
            const variables = [
                'DeviceManagementModule',
                'DeviceManager', 
                'deviceManager'
            ];
            
            variables.forEach(varName => {
                if (window[varName]) {
                    log(`✅ ${varName} 存在: ${typeof window[varName]}`, 'success');
                    if (typeof window[varName] === 'object') {
                        const keys = Object.keys(window[varName]);
                        log(`   📋 属性: ${keys.join(', ')}`, 'info');
                    }
                } else {
                    log(`❌ ${varName} 不存在`, 'error');
                }
            });
        }

        async function runTest() {
            clearLog();
            log('🚀 开始设备模块全局变量测试...');
            
            try {
                // 步骤1：检查初始状态
                log('1️⃣ 检查初始全局变量状态...');
                checkGlobalVariables();
                
                // 步骤2：加载模块加载器
                log('2️⃣ 加载模块加载器...');
                await loadScript('js/modules/core/module-loader.js');
                log('✅ 模块加载器加载完成', 'success');
                
                // 步骤3：初始化模块加载器
                log('3️⃣ 初始化模块加载器...');
                if (typeof ModuleLoader === 'undefined') {
                    throw new Error('ModuleLoader 未定义');
                }
                
                const moduleLoader = new ModuleLoader();
                window.testModuleLoader = moduleLoader;
                log('✅ 模块加载器初始化成功', 'success');
                
                // 步骤4：直接加载设备管理模块文件
                log('4️⃣ 直接加载设备管理模块文件...');
                await loadScript('js/modules/device/management.js');
                log('✅ 设备管理模块文件加载完成', 'success');
                
                // 步骤5：立即检查全局变量
                log('5️⃣ 检查模块加载后的全局变量...');
                checkGlobalVariables();
                
                // 步骤6：通过模块加载器加载
                log('6️⃣ 通过模块加载器加载设备管理模块...');
                const module = await moduleLoader.loadModule('device-management');
                log('✅ 通过模块加载器加载成功', 'success');
                log(`📋 返回的模块对象: ${typeof module}`, 'info');
                
                if (module) {
                    const moduleKeys = Object.keys(module);
                    log(`   📋 模块属性: ${moduleKeys.join(', ')}`, 'info');
                }
                
                // 步骤7：最终检查
                log('7️⃣ 最终全局变量检查...');
                checkGlobalVariables();
                
                log('🎉 测试完成！', 'success');
                
            } catch (error) {
                log(`💥 测试过程中发生错误: ${error.message}`, 'error');
                console.error('详细错误:', error);
            }
        }

        function loadScript(src) {
            return new Promise((resolve, reject) => {
                // 检查是否已经加载
                const existing = document.querySelector(`script[src="${src}"]`);
                if (existing) {
                    resolve();
                    return;
                }
                
                const script = document.createElement('script');
                script.src = src + '?v=' + Date.now();
                script.onload = () => {
                    log(`✅ 脚本加载成功: ${src}`, 'success');
                    resolve();
                };
                script.onerror = () => {
                    log(`❌ 脚本加载失败: ${src}`, 'error');
                    reject(new Error(`Failed to load ${src}`));
                };
                document.head.appendChild(script);
            });
        }

        // 页面加载完成后自动检查初始状态
        window.addEventListener('load', () => {
            log('📄 页面加载完成，检查初始状态...');
            checkGlobalVariables();
        });
    </script>
</body>
</html> 