/**
 * 财务管理模块
 * 负责财务报表、资金流水、结算管理等核心财务业务功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-15
 */

class FinanceManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.currentPage = 1;
        this.pageSize = 20;
        this.dateRange = { start: '', end: '' };
        
        console.log('FinanceManager initialized');
    }

    /**
     * 初始化财务管理模块
     */
    async init() {
        try {
            // 注册页面处理器
            if (window.uiManager) {
                window.uiManager.registerPageHandler('finance', (container) => {
                    this.loadFinanceManagementPage(container);
                });
            }
            
            console.log('✅ 财务管理模块初始化完成');
        } catch (error) {
            console.error('❌ 财务管理模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载财务管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadFinanceManagementPage(container) {
        container.innerHTML = this.generateFinanceManagementHTML();
        this.initializeFinanceManagementEvents();
        this.loadFinanceData();
    }

    /**
     * 生成财务管理页面HTML
     * @returns {string} HTML字符串
     */
    generateFinanceManagementHTML() {
        return `
            <div class="finance-management">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-graph-up me-2"></i>财务管理</h2>
                            <p class="text-muted mb-0">财务报表、资金流水和结算管理</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="generateReportBtn">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>生成报表
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="exportFinanceBtn">
                                <i class="bi bi-download me-2"></i>导出数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 财务概览卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-cash-stack"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalRevenueCount">-</div>
                                <div class="stat-label">总收入</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-wallet2"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="availableBalanceCount">-</div>
                                <div class="stat-label">可用余额</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-hourglass-split"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="pendingSettlementCount">-</div>
                                <div class="stat-label">待结算</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-percent"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="feeIncomeCount">-</div>
                                <div class="stat-label">手续费收入</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="financeTabs">
                            <li class="nav-item">
                                <a class="nav-link active" id="overview-tab" data-bs-toggle="tab" href="#overview">
                                    <i class="bi bi-graph-up me-2"></i>财务概览
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="transactions-tab" data-bs-toggle="tab" href="#transactions">
                                    <i class="bi bi-list-ul me-2"></i>资金流水
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="settlement-tab" data-bs-toggle="tab" href="#settlement">
                                    <i class="bi bi-calendar-check me-2"></i>结算管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="reports-tab" data-bs-toggle="tab" href="#reports">
                                    <i class="bi bi-file-earmark-text me-2"></i>财务报表
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="financeTabContent">
                            <!-- 财务概览 -->
                            <div class="tab-pane fade show active" id="overview">
                                <div class="row g-4">
                                    <div class="col-md-8">
                                        <div class="chart-container">
                                            <h6 class="mb-3">收入趋势图</h6>
                                            <canvas id="revenueChart" width="400" height="200"></canvas>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stats-summary">
                                            <h6 class="mb-3">本月统计</h6>
                                            <div class="stat-item">
                                                <span class="label">交易笔数:</span>
                                                <span class="value" id="monthlyTransactions">-</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="label">交易金额:</span>
                                                <span class="value text-success" id="monthlyVolume">-</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="label">手续费:</span>
                                                <span class="value text-primary" id="monthlyFees">-</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="label">净收入:</span>
                                                <span class="value text-info" id="monthlyProfit">-</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 资金流水 -->
                            <div class="tab-pane fade" id="transactions">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <input type="date" class="form-control" id="flowStartDate" placeholder="开始日期">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="date" class="form-control" id="flowEndDate" placeholder="结束日期">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="flowTypeFilter">
                                            <option value="all">全部类型</option>
                                            <option value="income">收入</option>
                                            <option value="expense">支出</option>
                                            <option value="settlement">结算</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-primary" id="searchFlowBtn">
                                            <i class="bi bi-search me-2"></i>查询
                                        </button>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>时间</th>
                                                <th>类型</th>
                                                <th>金额</th>
                                                <th>余额</th>
                                                <th>描述</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="flowTableBody">
                                            <tr>
                                                <td colspan="6" class="text-center py-4">
                                                    <div class="spinner-border text-primary" role="status"></div>
                                                    <div class="mt-2">正在加载资金流水...</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 结算管理 -->
                            <div class="tab-pane fade" id="settlement">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>结算记录</h6>
                                    <button class="btn btn-primary btn-sm" id="createSettlementBtn">
                                        <i class="bi bi-plus-circle me-2"></i>创建结算
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>结算日期</th>
                                                <th>商户</th>
                                                <th>结算金额</th>
                                                <th>手续费</th>
                                                <th>实际到账</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="settlementTableBody">
                                            <tr>
                                                <td colspan="7" class="text-center py-4">
                                                    <div class="spinner-border text-primary" role="status"></div>
                                                    <div class="mt-2">正在加载结算记录...</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 财务报表 -->
                            <div class="tab-pane fade" id="reports">
                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <div class="report-card">
                                            <h6><i class="bi bi-calendar-day me-2"></i>日报表</h6>
                                            <p class="text-muted">查看每日财务数据汇总</p>
                                            <button class="btn btn-outline-primary btn-sm" onclick="financeManager.generateReport('daily')">
                                                生成日报表
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="report-card">
                                            <h6><i class="bi bi-calendar-week me-2"></i>周报表</h6>
                                            <p class="text-muted">查看每周财务数据汇总</p>
                                            <button class="btn btn-outline-primary btn-sm" onclick="financeManager.generateReport('weekly')">
                                                生成周报表
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="report-card">
                                            <h6><i class="bi bi-calendar-month me-2"></i>月报表</h6>
                                            <p class="text-muted">查看每月财务数据汇总</p>
                                            <button class="btn btn-outline-primary btn-sm" onclick="financeManager.generateReport('monthly')">
                                                生成月报表
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="report-card">
                                            <h6><i class="bi bi-calendar-range me-2"></i>自定义报表</h6>
                                            <p class="text-muted">自定义时间范围生成报表</p>
                                            <button class="btn btn-outline-primary btn-sm" onclick="financeManager.showCustomReportModal()">
                                                自定义报表
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            ${this.generateFinanceManagementStyles()}
        `;
    }

    /**
     * 生成财务管理样式
     * @returns {string} CSS样式
     */
    generateFinanceManagementStyles() {
        return `
            <style>
                .finance-management .stat-card {
                    background: white;
                    border-radius: 12px;
                    padding: 20px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }

                .finance-management .stat-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                }

                .finance-management .stat-icon {
                    width: 50px;
                    height: 50px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .finance-management .stat-content {
                    flex: 1;
                }

                .finance-management .stat-number {
                    font-size: 1.8rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin-bottom: 4px;
                }

                .finance-management .stat-label {
                    color: #6b7280;
                    font-size: 14px;
                }

                .finance-management .chart-container {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 20px;
                }

                .finance-management .stats-summary {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 20px;
                }

                .finance-management .stat-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 0;
                    border-bottom: 1px solid #e9ecef;
                }

                .finance-management .stat-item:last-child {
                    border-bottom: none;
                }

                .finance-management .stat-item .label {
                    color: #6b7280;
                    font-weight: 500;
                }

                .finance-management .stat-item .value {
                    font-weight: 600;
                }

                .finance-management .report-card {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 20px;
                    text-align: center;
                    transition: all 0.3s ease;
                }

                .finance-management .report-card:hover {
                    background: #e9ecef;
                    transform: translateY(-2px);
                }

                .finance-management .table th {
                    font-weight: 600;
                    color: #374151;
                    border-bottom: 2px solid #e5e7eb;
                }

                .finance-management .table td {
                    vertical-align: middle;
                    border-bottom: 1px solid #f3f4f6;
                }

                .finance-management .nav-tabs .nav-link {
                    color: #6b7280;
                    border: none;
                    padding: 12px 20px;
                }

                .finance-management .nav-tabs .nav-link.active {
                    color: #3b82f6;
                    background: white;
                    border-bottom: 2px solid #3b82f6;
                }
            </style>
        `;
    }

    /**
     * 初始化财务管理事件
     */
    initializeFinanceManagementEvents() {
        // 生成报表按钮
        document.getElementById('generateReportBtn')?.addEventListener('click', () => {
            this.generateReport('custom');
        });

        // 搜索资金流水
        document.getElementById('searchFlowBtn')?.addEventListener('click', () => {
            this.loadFinanceFlow();
        });

        // 创建结算
        document.getElementById('createSettlementBtn')?.addEventListener('click', () => {
            this.createSettlement();
        });

        // 选项卡切换事件
        document.querySelectorAll('#financeTabs .nav-link').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const target = e.target.getAttribute('href').substring(1);
                this.handleTabChange(target);
            });
        });
    }

    /**
     * 加载财务数据
     */
    async loadFinanceData() {
        try {
            await this.updateFinanceStats();
            await this.loadFinanceFlow();
            await this.loadSettlementData();
        } catch (error) {
            console.error('加载财务数据失败:', error);
            this.utils.showMessage('加载财务数据失败', 'error');
        }
    }

    /**
     * 更新财务统计数据
     */
    async updateFinanceStats() {
        try {
            const response = await this.apiClient.get('/admin/finance/stats');
            
            if (response.success) {
                const stats = response.data;
                document.getElementById('totalRevenueCount').textContent = 
                    '¥' + this.utils.formatNumber(stats.total_revenue || 0);
                document.getElementById('availableBalanceCount').textContent = 
                    '¥' + this.utils.formatNumber(stats.available_balance || 0);
                document.getElementById('pendingSettlementCount').textContent = 
                    '¥' + this.utils.formatNumber(stats.pending_settlement || 0);
                document.getElementById('feeIncomeCount').textContent = 
                    '¥' + this.utils.formatNumber(stats.fee_income || 0);

                // 月度统计
                document.getElementById('monthlyTransactions').textContent = stats.monthly_transactions || 0;
                document.getElementById('monthlyVolume').textContent = 
                    '¥' + this.utils.formatNumber(stats.monthly_volume || 0);
                document.getElementById('monthlyFees').textContent = 
                    '¥' + this.utils.formatNumber(stats.monthly_fees || 0);
                document.getElementById('monthlyProfit').textContent = 
                    '¥' + this.utils.formatNumber(stats.monthly_profit || 0);
            }
        } catch (error) {
            console.error('加载财务统计失败:', error);
        }
    }

    /**
     * 加载资金流水
     */
    async loadFinanceFlow() {
        try {
            const startDate = document.getElementById('flowStartDate')?.value || '';
            const endDate = document.getElementById('flowEndDate')?.value || '';
            const type = document.getElementById('flowTypeFilter')?.value || 'all';

            const response = await this.apiClient.get('/admin/finance/flow', {
                params: { start_date: startDate, end_date: endDate, type: type }
            });
            
            if (response.success) {
                this.renderFinanceFlowTable(response.data.flows || []);
            }
        } catch (error) {
            console.error('加载资金流水失败:', error);
            this.showFlowTableError('加载资金流水失败');
        }
    }

    /**
     * 渲染资金流水表格
     * @param {Array} flows 流水数据
     */
    renderFinanceFlowTable(flows) {
        const tbody = document.getElementById('flowTableBody');
        if (!tbody) return;

        if (flows.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-4">
                        <i class="bi bi-inbox" style="font-size: 2rem; color: #6b7280;"></i>
                        <div class="mt-2 text-muted">暂无流水记录</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = flows.map(flow => `
            <tr>
                <td>${this.utils.formatDate(flow.created_at)}</td>
                <td>
                    <span class="badge bg-${this.getFlowTypeBadgeColor(flow.type)}">
                        ${this.getFlowTypeText(flow.type)}
                    </span>
                </td>
                <td class="${flow.amount > 0 ? 'text-success' : 'text-danger'}">
                    ${flow.amount > 0 ? '+' : ''}¥${this.utils.formatNumber(Math.abs(flow.amount))}
                </td>
                <td>¥${this.utils.formatNumber(flow.balance)}</td>
                <td>${flow.description}</td>
                <td>
                    <button class="btn btn-outline-primary btn-sm" 
                            onclick="financeManager.showFlowDetail(${flow.id})">
                        详情
                    </button>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 显示流水表格错误
     * @param {string} message 错误信息
     */
    showFlowTableError(message) {
        const tbody = document.getElementById('flowTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center py-4">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                    <div class="mt-2 text-danger">${message}</div>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="financeManager.loadFinanceFlow()">
                        重新加载
                    </button>
                </td>
            </tr>
        `;
    }

    /**
     * 加载结算数据
     */
    async loadSettlementData() {
        try {
            const response = await this.apiClient.get('/admin/finance/settlements');
            
            if (response.success) {
                this.renderSettlementTable(response.data.settlements || []);
            }
        } catch (error) {
            console.error('加载结算数据失败:', error);
            this.showSettlementTableError('加载结算数据失败');
        }
    }

    /**
     * 渲染结算表格
     * @param {Array} settlements 结算数据
     */
    renderSettlementTable(settlements) {
        const tbody = document.getElementById('settlementTableBody');
        if (!tbody) return;

        if (settlements.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="bi bi-inbox" style="font-size: 2rem; color: #6b7280;"></i>
                        <div class="mt-2 text-muted">暂无结算记录</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = settlements.map(settlement => `
            <tr>
                <td>${this.utils.formatDate(settlement.settlement_date)}</td>
                <td>${settlement.merchant_name}</td>
                <td class="text-primary fw-bold">¥${this.utils.formatNumber(settlement.amount)}</td>
                <td class="text-warning">¥${this.utils.formatNumber(settlement.fee_amount)}</td>
                <td class="text-success fw-bold">¥${this.utils.formatNumber(settlement.net_amount)}</td>
                <td>
                    <span class="badge bg-${this.getSettlementStatusBadgeColor(settlement.status)}">
                        ${this.getSettlementStatusText(settlement.status)}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" 
                                onclick="financeManager.showSettlementDetail(${settlement.id})">
                            详情
                        </button>
                        ${settlement.status === 'pending' ? `
                        <button class="btn btn-outline-success" 
                                onclick="financeManager.approveSettlement(${settlement.id})">
                            确认
                        </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 显示结算表格错误
     * @param {string} message 错误信息
     */
    showSettlementTableError(message) {
        const tbody = document.getElementById('settlementTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                    <div class="mt-2 text-danger">${message}</div>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="financeManager.loadSettlementData()">
                        重新加载
                    </button>
                </td>
            </tr>
        `;
    }

    /**
     * 处理选项卡切换
     * @param {string} target 目标选项卡
     */
    handleTabChange(target) {
        switch (target) {
            case 'overview':
                this.loadOverviewData();
                break;
            case 'transactions':
                this.loadFinanceFlow();
                break;
            case 'settlement':
                this.loadSettlementData();
                break;
            case 'reports':
                // 报表页面无需额外加载
                break;
        }
    }

    /**
     * 加载概览数据
     */
    async loadOverviewData() {
        // 这里可以加载图表数据
        console.log('加载财务概览数据');
    }

    /**
     * 生成报表
     * @param {string} type 报表类型
     */
    async generateReport(type) {
        try {
            this.utils.showMessage('正在生成报表...', 'info');
            
            const response = await this.apiClient.post('/admin/finance/reports', {
                type: type,
                start_date: this.dateRange.start,
                end_date: this.dateRange.end
            });

            if (response.success) {
                this.utils.showMessage('报表生成成功', 'success');
                // 可以下载或显示报表
                if (response.data.download_url) {
                    window.open(response.data.download_url, '_blank');
                }
            } else {
                throw new Error(response.message || '报表生成失败');
            }
        } catch (error) {
            console.error('生成报表失败:', error);
            this.utils.showMessage(error.message || '生成报表失败', 'error');
        }
    }

    /**
     * 创建结算
     */
    async createSettlement() {
        try {
            const response = await this.apiClient.post('/admin/finance/settlements');

            if (response.success) {
                this.utils.showMessage('结算创建成功', 'success');
                this.loadSettlementData();
            } else {
                throw new Error(response.message || '创建结算失败');
            }
        } catch (error) {
            console.error('创建结算失败:', error);
            this.utils.showMessage(error.message || '创建结算失败', 'error');
        }
    }

    /**
     * 确认结算
     * @param {number} settlementId 结算ID
     */
    async approveSettlement(settlementId) {
        if (!confirm('确定要确认这笔结算吗？')) {
            return;
        }

        try {
            const response = await this.apiClient.post(`/admin/finance/settlements/${settlementId}/approve`);

            if (response.success) {
                this.utils.showMessage('结算确认成功', 'success');
                this.loadSettlementData();
            } else {
                throw new Error(response.message || '结算确认失败');
            }
        } catch (error) {
            console.error('结算确认失败:', error);
            this.utils.showMessage(error.message || '结算确认失败', 'error');
        }
    }

    /**
     * 获取流水类型文本
     * @param {string} type 类型
     * @returns {string} 类型文本
     */
    getFlowTypeText(type) {
        const typeMap = {
            'income': '收入',
            'expense': '支出',
            'settlement': '结算',
            'refund': '退款',
            'fee': '手续费'
        };
        return typeMap[type] || type;
    }

    /**
     * 获取流水类型徽章颜色
     * @param {string} type 类型
     * @returns {string} 颜色类名
     */
    getFlowTypeBadgeColor(type) {
        const colorMap = {
            'income': 'success',
            'expense': 'danger',
            'settlement': 'primary',
            'refund': 'warning',
            'fee': 'info'
        };
        return colorMap[type] || 'secondary';
    }

    /**
     * 获取结算状态文本
     * @param {string} status 状态
     * @returns {string} 状态文本
     */
    getSettlementStatusText(status) {
        const statusMap = {
            'pending': '待确认',
            'approved': '已确认',
            'completed': '已完成',
            'failed': '失败'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取结算状态徽章颜色
     * @param {string} status 状态
     * @returns {string} 颜色类名
     */
    getSettlementStatusBadgeColor(status) {
        const colorMap = {
            'pending': 'warning',
            'approved': 'info',
            'completed': 'success',
            'failed': 'danger'
        };
        return colorMap[status] || 'secondary';
    }

    /**
     * 显示流水详情
     * @param {number} flowId 流水ID
     */
    showFlowDetail(flowId) {
        this.utils.showMessage('流水详情功能开发中', 'info');
    }

    /**
     * 显示结算详情
     * @param {number} settlementId 结算ID
     */
    showSettlementDetail(settlementId) {
        this.utils.showMessage('结算详情功能开发中', 'info');
    }

    /**
     * 显示自定义报表模态框
     */
    showCustomReportModal() {
        this.utils.showMessage('自定义报表功能开发中', 'info');
    }
}

// 创建全局实例
const financeManager = new FinanceManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { FinanceManager };
} else {
    // 浏览器环境
    window.FinanceManager = FinanceManager;
    window.financeManager = financeManager;
} 