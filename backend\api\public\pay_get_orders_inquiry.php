<?php
/**
 * 查询订单信息接口
 * 兼容旧系统API，使用新数据库结构
 */

// 引入通用函数
require_once dirname(__FILE__) . '/common.php';

// 获取全局变量
$merchant_info = $GLOBALS['merchant_info'];
$post_data = $GLOBALS['post_data'];
$pdo = $GLOBALS['pdo'];

try {
    // 验证必需参数
    if (!isset($post_data['order_no']) || $post_data['order_no'] === '') {
        apiResponse(7, '请输入有效的order_no');
    }
    
    $order_no = $post_data['order_no'];
    
    // 验证订单号格式
    if (!validateOrderNo($order_no)) {
        apiResponse(7, '请输入有效的order_no');
    }
    
    // 查询订单信息
    $stmt = $pdo->prepare("
        SELECT 
            t.id,
            t.amount,
            t.merchant_order_no as order_no,
            t.status,
            t.create_time,
            t.pay_time,
            t.merchant_fee as fee,
            p.name as product_name,
            t.product_id
        FROM transactions t
        LEFT JOIN products p ON t.product_id = p.id
        WHERE t.merchant_order_no = ? AND t.merchant_id = ?
    ");
    
    $stmt->execute([$order_no, $merchant_info['id']]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        // 记录API调用日志
        $error_response = [
            'error_code' => 217,
            'error_message' => '订单不存在'
        ];
        
        logApiCall($merchant_info['id'], 'pay_get_orders_inquiry', $post_data, $error_response, 'error');
        
        apiResponse(217, '订单不存在');
    }
    
    // 转换状态值以兼容旧系统
    // 新系统: pending, paid, expired, cancelled
    // 旧系统: 0 待付款, 1 已付款, 2 已超时
    $status_map = [
        'pending' => 0,
        'paid' => 1,
        'expired' => 2,
        'cancelled' => 2
    ];
    
            $order['status'] = isset($status_map[$order['status']]) ? $status_map[$order['status']] : 0;
    
    // 格式化时间
    if ($order['create_time']) {
        $order['create_time'] = date('Y-m-d H:i:s', strtotime($order['create_time']));
    }
    
    if ($order['pay_time']) {
        $order['pay_time'] = date('Y-m-d H:i:s', strtotime($order['pay_time']));
    } else {
        $order['pay_time'] = '';
    }
    
    // 确保fee字段存在
    if ($order['fee'] === null) {
        $order['fee'] = '';
    }
    
    // 确保product_name字段存在
    if ($order['product_name'] === null) {
        $order['product_name'] = '';
    }
    
    // 构建响应数据（与接口文档格式完全一致）
    $response_data = [
        'error_code' => 0,
        'error_message' => '',
        'order_data' => [
            'id' => (int)$order['id'],
            'amount' => (int)$order['amount'],
            'status' => (int)$order['status'],
            'create_time' => $order['create_time'],
            'pay_time' => $order['pay_time'],
            'product_id' => (int)$order['product_id'],
            'order_no' => $order['order_no'],
            'fee' => $order['fee'],
            'product_name' => $order['product_name']
        ]
    ];
    
    // 记录API调用日志
    logApiCall($merchant_info['id'], 'pay_get_orders_inquiry', $post_data, $response_data, 'success');
    
    // 返回成功响应
    apiResponse(0, '', ['order_data' => $response_data['order_data']]);
    
} catch (Exception $e) {
    error_log("pay_get_orders_inquiry异常: " . $e->getMessage());
    
    // 记录错误日志
    $error_response = [
        'error_code' => 1,
        'error_message' => '系统异常，请稍后重试'
    ];
    
    logApiCall($merchant_info['id'], 'pay_get_orders_inquiry', $post_data, $error_response, 'error');
    
    apiResponse(1, '系统异常，请稍后重试');
}
?> 