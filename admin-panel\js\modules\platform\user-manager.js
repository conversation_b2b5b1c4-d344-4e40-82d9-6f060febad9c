/**
 * 用户管理器
 * 从admin.js第8562-9086行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class UserManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.users = [];
        this.filters = {
            user_type: '',
            status: '',
            search: ''
        };
    }
    initialize() {
        this.bindEvents();
        this.loadUsers();
    }
    bindEvents() {
        // 搜索框回车事件
        const searchInput = document.getElementById('userSearchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchUsers();
                }
            });
        }
        // 筛选器变化事件
        const typeFilter = document.getElementById('userTypeFilter');
        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => {
                this.filters.user_type = e.target.value;
                this.applyFilters();
            });
        }
        const statusFilter = document.getElementById('userStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }
        // 用户类型选择事件
        const userTypeSelect = document.getElementById('userType');
        if (userTypeSelect) {
            userTypeSelect.addEventListener('change', (e) => {
                this.toggleExtendedFields(e.target.value);
            });
        }
    }
    async loadUsers(page = 1) {
        try {
            this.currentPage = page;
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                user_type: this.filters.user_type,
                status: this.filters.status,
                search: this.filters.search
            });
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=users&${
                params
            }`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`,
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.users = result.data.users;
                this.updateStatsCards(result.data.stats);
                this.renderUsersTable(result.data.users);
                this.renderPagination(result.data.pagination);
            } else {
                throw new Error(result.message || '加载用户数据失败');
            }
        } catch (error) {
            console.error('加载用户数据失败:',
            error);
            this.showError('加载用户数据失败: ' + error.message);
        }
    }
    updateStatsCards(stats) {
        document.getElementById('totalUsers').textContent = stats.total_users || 0;
        document.getElementById('adminUsers').textContent = stats.admin_users || 0;
        document.getElementById('providerUsers').textContent = stats.provider_users || 0;
        document.getElementById('merchantUsers').textContent = stats.merchant_users || 0;
        document.getElementById('activeUsers').textContent = stats.active_users || 0;
        document.getElementById('dailyActiveUsers').textContent = stats.daily_active_users || 0;
    }
    renderUsersTable(users) {
        const container = document.getElementById('usersTableContainer');
        document.getElementById('userCount').textContent = users.length;
        if (users.length === 0) {
            container.innerHTML = `
            <div class="text-center py-4">
            <div class="no-data-icon">👥</div>
            <h5>暂无用户数据</h5>
            <p class="text-muted">还没有符合条件的用户，或者当前筛选条件下没有找到匹配的数据。</p>
            <button class="btn btn-primary" onclick="window.userManager.showCreateModal()">
            <i class="bi bi-person-plus me-2"></i>添加第一个用户
            </button>
            </div>
            `;
            return;
        }
        const tableHtml = `
        <div class="table-responsive">
        <table class="table table-striped">
        <thead class="table-dark">
        <tr>
        <th>ID</th>
        <th>用户信息</th>
        <th>用户类型</th>
        <th>关联信息</th>
        <th>状态</th>
        <th>最后登录</th>
        <th>活跃会话</th>
        <th>创建时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        ${
            users.map(user => this.renderUserRow(user)).join('')
        }
        </tbody>
        </table>
        </div>
        `;
        container.innerHTML = tableHtml;
    }
    renderUserRow(user) {
        const userTypeBadges = {
            'admin': 'badge bg-primary',
            'provider': 'badge bg-info',
            'merchant': 'badge bg-warning'
        };
        const userTypeTexts = {
            'admin': '管理员',
            'provider': '码商',
            'merchant': '商户'
        };
        const statusBadges = {
            'active': 'badge bg-success',
            'inactive': 'badge bg-secondary',
            'suspended': 'badge bg-danger'
        };
        const statusTexts = {
            'active': '活跃',
            'inactive': '非活跃',
            'suspended': '已暂停'
        };
        const lastLogin = user.last_login ? new Date(user.last_login).toLocaleString() : '从未登录';
        return `
        <tr>
        <td>#${
            user.id
        }</td>
        <td>
        <div class="user-info">
        <div class="fw-bold">${
            user.real_name
        }</div>
        <div class="text-muted small">@${
            user.username
        }</div>
        <div class="text-muted small">${
            user.email
        }</div>
        ${
            user.phone ? `<div class="text-muted small">${
                user.phone
            }</div>` : ''
        }
        </div>
        </td>
        <td>
        <span class="${
            userTypeBadges[user.user_type] || 'badge bg-secondary'
        }">
        ${
            userTypeTexts[user.user_type] || user.user_type
        }
        </span>
        </td>
        <td>
        <div class="related-info">
        ${
            user.display_name !== user.real_name ? `<div class="fw-bold">${
                user.display_name
            }</div>` : ''
        }
        ${
            user.profile_id ? `<div class="text-muted small">ID: ${
                user.profile_id
            }</div>` : ''
        }
        ${
            user.profile_status ? `<div class="text-muted small">状态: ${
                user.profile_status
            }</div>` : ''
        }
        </div>
        </td>
        <td>
        <span class="${
            statusBadges[user.status] || 'badge bg-secondary'
        }">
        ${
            statusTexts[user.status] || user.status
        }
        </span>
        </td>
        <td>
        <div class="small">${
            lastLogin
        }</div>
        </td>
        <td>
        <span class="badge ${
            user.active_sessions > 0 ? 'bg-success' : 'bg-secondary'
        }">
        ${
            user.active_sessions || 0
        }
        </span>
        </td>
        <td>
        <div class="small">${
            new Date(user.created_at).toLocaleDateString()
        }</div>
        <div class="text-muted small">${
            new Date(user.created_at).toLocaleTimeString()
        }</div>
        </td>
        <td>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-primary" onclick="window.userManager.showEditModal(${
            user.id
        })" title="编辑">
        <i class="bi bi-pencil"></i>
        </button>
        <button class="btn btn-outline-info" onclick="window.userManager.showUserDetails(${
            user.id
        })" title="详情">
        <i class="bi bi-eye"></i>
        </button>
        <button class="btn btn-outline-danger" onclick="window.userManager.deleteUser(${
            user.id
        })" title="删除">
        <i class="bi bi-trash"></i>
        </button>
        </div>
        </td>
        </tr>
        `;
    }
    renderPagination(pagination) {
        const container = document.getElementById('userPagination');
        if (pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }
        const currentPage = pagination.current_page;
        const totalPages = pagination.total_pages;
        let paginationHtml = `
        <div class="d-flex justify-content-between align-items-center">
        <div class="pagination-info">
        显示第 ${
            ((currentPage - 1) * this.pageSize) + 1
        } - ${
            Math.min(currentPage * this.pageSize,
            pagination.total_records)
        } 条，
        共 ${
            pagination.total_records
        } 条记录
        </div>
        <nav>
        <ul class="pagination mb-0">
        `;
        // 上一页
        paginationHtml += `
        <li class="page-item ${
            currentPage === 1 ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.userManager.loadUsers(${
            currentPage - 1
        })">
        上一页
        </a>
        </li>
        `;
        // 页码
        const startPage = Math.max(1,
        currentPage - 2);
        const endPage = Math.min(totalPages,
        currentPage + 2);
        if (startPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="window.userManager.loadUsers(1)">1</a></li>`;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        for (let i = startPage;
        i <= endPage;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === currentPage ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.userManager.loadUsers(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="window.userManager.loadUsers(${
                totalPages
            })">${
                totalPages
            }</a></li>`;
        }
        // 下一页
        paginationHtml += `
        <li class="page-item ${
            currentPage === totalPages ? 'disabled' : ''
        }">
        <a class="page-link" href="#" onclick="window.userManager.loadUsers(${
            currentPage + 1
        })">
        下一页
        </a>
        </li>
        `;
        paginationHtml += `
        </ul>
        </nav>
        </div>
        `;
        container.innerHTML = paginationHtml;
    }
    searchUsers() {
        const searchInput = document.getElementById('userSearchInput');
        this.filters.search = searchInput.value.trim();
        this.applyFilters();
    }
    applyFilters() {
        this.currentPage = 1;
        this.loadUsers();
    }
    refreshUsers() {
        this.loadUsers(this.currentPage);
    }
    showCreateModal() {
        document.getElementById('userModalTitle').textContent = '添加用户';
        document.getElementById('userId').value = '';
        document.getElementById('userForm').reset();
        document.getElementById('userPasswordRow').style.display = 'block';
        document.getElementById('userPassword').required = true;
        document.getElementById('userUsername').disabled = false;
        document.getElementById('userType').disabled = false;
        document.getElementById('userSaveText').textContent = '创建';
        this.toggleExtendedFields('');
        const modal = new bootstrap.Modal(document.getElementById('userModal'));
        modal.show();
    }
    showEditModal(userId) {
        const user = this.users.find(u => u.id == userId);
        if (!user) {
            this.showError('找不到用户信息');
            return;
        }
        document.getElementById('userModalTitle').textContent = '编辑用户';
        document.getElementById('userId').value = user.id;
        document.getElementById('userUsername').value = user.username;
        document.getElementById('userRealName').value = user.real_name;
        document.getElementById('userEmail').value = user.email;
        document.getElementById('userPhone').value = user.phone || '';
        document.getElementById('userType').value = user.user_type;
        document.getElementById('userStatus').value = user.status;
        document.getElementById('userPasswordRow').style.display = 'none';
        document.getElementById('userPassword').required = false;
        document.getElementById('userUsername').disabled = true;
        document.getElementById('userType').disabled = true;
        document.getElementById('userSaveText').textContent = '保存';
        this.toggleExtendedFields(user.user_type);
        this.fillExtendedFields(user);
        const modal = new bootstrap.Modal(document.getElementById('userModal'));
        modal.show();
    }
    toggleExtendedFields(userType) {
        const extendedInfo = document.getElementById('extendedInfo');
        const extendedInfoTitle = document.getElementById('extendedInfoTitle');
        const websiteField = document.getElementById('websiteField');
        const settlementRateField = document.getElementById('settlementRateField');
        if (userType === 'provider' || userType === 'merchant') {
            extendedInfo.style.display = 'block';
            extendedInfoTitle.textContent = userType === 'provider' ? '码商信息' : '商户信息';
            if (userType === 'provider') {
                websiteField.style.display = 'none';
                settlementRateField.style.display = 'block';
            } else {
                websiteField.style.display = 'block';
                settlementRateField.style.display = 'none';
            }
        } else {
            extendedInfo.style.display = 'none';
        }
    }
    fillExtendedFields(user) {
        // 填充扩展字段
        document.getElementById('userCompanyName').value = user.display_name !== user.real_name ? user.display_name : '';
    }
    async saveUser() {
        const saveBtn = document.getElementById('userSaveText');
        const saveSpinner = document.getElementById('userSaveSpinner');
        try {
            saveBtn.textContent = '保存中...';
            saveSpinner.classList.remove('d-none');
            const formData = {
                username: document.getElementById('userUsername').value,
                real_name: document.getElementById('userRealName').value,
                email: document.getElementById('userEmail').value,
                phone: document.getElementById('userPhone').value,
                user_type: document.getElementById('userType').value,
                status: document.getElementById('userStatus').value
            };
            const userId = document.getElementById('userId').value;
            let url = `${
                CONFIG.API_BASE_URL
            }/admin.php?action=users`;
            let method = 'POST';
            if (userId) {
                formData.id = parseInt(userId);
                method = 'PUT';
            } else {
                formData.password = document.getElementById('userPassword').value;
            }
            // 添加扩展字段
            const userType = formData.user_type;
            if (userType === 'provider' || userType === 'merchant') {
                formData.company_name = document.getElementById('userCompanyName').value;
                formData.business_license = document.getElementById('userBusinessLicense').value;
                formData.business_type = document.getElementById('userBusinessType').value;
                if (userType === 'provider') {
                    formData.settlement_rate = parseFloat(document.getElementById('userSettlementRate').value) / 100;
                } else {
                    formData.website = document.getElementById('userWebsite').value;
                }
            }
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess(result.message || (userId ? '用户更新成功' : '用户创建成功'));
                bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
                this.loadUsers(this.currentPage);
            } else {
                throw new Error(result.message || '操作失败');
            }
        } catch (error) {
            console.error('保存用户失败:',
            error);
            this.showError('保存失败: ' + error.message);
        } finally {
            saveBtn.textContent = document.getElementById('userId').value ? '保存' : '创建';
            saveSpinner.classList.add('d-none');
        }
    }
    async deleteUser(userId) {
        const user = this.users.find(u => u.id == userId);
        if (!user) {
            this.showError('找不到用户信息');
            return;
        }
        if (!confirm(`确定要删除用户 "${
            user.real_name
        }" 吗？\n\n注意：删除后无法恢复，请谨慎操作！`)) {
            return;
        }
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=users&id=${
                userId
            }`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${
                        localStorage.getItem(CONFIG.TOKEN_KEY)
                    }`,
                    'Content-Type': 'application/json'
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('用户删除成功');
                this.loadUsers(this.currentPage);
            } else {
                throw new Error(result.message || '删除失败');
            }
        } catch (error) {
            console.error('删除用户失败:',
            error);
            this.showError('删除失败: ' + error.message);
        }
    }
    showUserDetails(userId) {
        const user = this.users.find(u => u.id == userId);
        if (!user) {
            this.showError('找不到用户信息');
            return;
        }
        const details = `用户详情：
        基本信息：
        姓名：${
            user.real_name
        } (@${
            user.username
        })
        邮箱：${
            user.email
        }
        ${
            user.phone ? `手机：${
                user.phone
            }` : ''
        }
        类型：${
            user.user_type
        }
        状态：${
            user.status
        }
        ${
            user.display_name !== user.real_name ? `关联信息：${
                user.display_name
            }` : ''
        }
        ${
            user.last_login ? `最后登录：${
                new Date(user.last_login).toLocaleString()
            }` : '最后登录：从未登录'
        }
        活跃会话：${
            user.active_sessions || 0
        }
        创建时间：${
            new Date(user.created_at).toLocaleString()
        }`;
        alert(details);
    }
    showSuccess(message) {
        // 这里可以使用toast或其他通知组件
        alert('成功：' + message);
    }
    showError(message) {
        // 这里可以使用toast或其他通知组件
        alert('错误：' + message);
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = UserManager;
} else if (typeof window !== "undefined") {
    window.UserManager = UserManager;
}

console.log('📦 UserManager 模块加载完成');
