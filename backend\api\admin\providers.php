<?php
/**
 * 码商管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

try {
    // 检查用户权限：系统管理员、平台管理员可以管理码商
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限访问码商管理');
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($action) {
        case 'providers':
        case 'get_providers':
        case 'payment_providers':
        case 'get_payment_providers':
            handleProviders($auth);
            break;
            
        case 'add_provider':
            handleCreateProvider($auth);
            break;
            
        case 'edit_provider':
        case 'update_provider':
            handleUpdateProvider($auth);
            break;
            
        case 'delete_provider':
            handleDeleteProvider($auth);
            break;
            
        case 'update_provider_status':
            handleUpdateProviderStatus($auth);
            break;
            
        case 'get_provider_stats':
            handleGetProviderStats($auth);
            break;
            
        // 新增用户操作功能
        case 'reset_provider_password':
            handleResetProviderPassword($auth);
            break;
        case 'update_provider_user_status':
            handleUpdateProviderUserStatus($auth);
            break;
        case 'get_provider_login_logs':
            handleGetProviderLoginLogs($auth);
            break;
        case 'get_provider_user_detail':
            handleGetProviderUserDetail($auth);
            break;
            
        default:
            handleProviders($auth);
            break;
    }
} catch (Exception $e) {
    error_log("Providers API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

function handleProviders($auth) {
    global $method;
    
    switch ($method) {
        case 'GET':
            handleGetProviders($auth);
            break;
        case 'POST':
            handleCreateProvider($auth);
            break;
        case 'PUT':
            handleUpdateProvider($auth);
            break;
        case 'DELETE':
            handleDeleteProvider($auth);
            break;
        default:
            $auth->sendError('不支持的请求方法', 405);
    }
}

// 获取码商列表
function handleGetProviders($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 权限检查
    if (!$currentUser) {
        $auth->sendForbidden('未认证用户');
        return;
    }
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    
    $whereConditions = array();
    $params = array();
    
    // 注意：数据隔离现在由Database类自动处理，无需手动添加platform_id过滤
    // 码商权限特殊处理：码商只能查看自己的信息
    if ($currentUser['user_type'] === 'provider') {
        $whereConditions[] = "u.id = ?";
        $params[] = $currentUser['id'];
    }
    
    if (!empty($status) && in_array($status, array('pending', 'approved', 'suspended', 'rejected'))) {
        $whereConditions[] = "p.status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(u.username LIKE ? OR p.provider_code LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    try {
        // 获取总数
        $totalResult = $db->fetch(
            "SELECT COUNT(*) as count FROM payment_providers p 
             JOIN users u ON p.user_id = u.id 
             $whereClause",
            $params
        );
        $total = $totalResult['count'];
        
        // 获取码商列表
        $providers = $db->fetchAll(
            "SELECT p.*, 
                    u.username, u.user_type, u.status as user_status,
                    u.created_at as user_created_at, u.last_login
             FROM payment_providers p
             JOIN users u ON p.user_id = u.id
             $whereClause
             ORDER BY p.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, array($limit, $offset))
        );
        
        // 获取统计信息
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_providers,
                COUNT(CASE WHEN p.status = 'pending' THEN 1 END) as pending_providers,
                COUNT(CASE WHEN p.status = 'approved' THEN 1 END) as approved_providers,
                COUNT(CASE WHEN p.status = 'suspended' THEN 1 END) as suspended_providers,
                COUNT(CASE WHEN p.status = 'rejected' THEN 1 END) as rejected_providers
             FROM payment_providers p"
        );
        
        // 记录操作日志
        $auth->logUserAction('view_providers', 'provider', 0, '查看码商列表');
        
        $response = array(
            'providers' => $providers,
            'stats' => $stats,
            'pagination' => array(
                'current_page' => $page,
                'per_page' => $limit,
                'total_records' => $total,
                'total_pages' => ceil($total / $limit)
            )
        );
        
        $auth->sendSuccess($response, '码商列表获取成功');
        
    } catch (Exception $e) {
        error_log("获取码商列表失败: " . $e->getMessage());
        $auth->sendError('获取码商列表失败: ' . $e->getMessage(), 500);
    }
}

// 创建码商
function handleCreateProvider($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 权限检查 - 只有系统管理员和平台管理员可以创建码商
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限创建码商');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证必填字段
    $requiredFields = array('username', 'password');
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            $auth->sendError("字段 {$field} 不能为空", 400);
            return;
        }
    }
    
    $username = trim($input['username']);
    $password = $input['password'];
    $providerCode = isset($input['provider_code']) ? trim($input['provider_code']) : '';
    
    // 平台隔离：确定平台ID
    if ($currentUser['user_type'] === 'platform_admin') {
        // 平台管理员只能在自己的平台创建码商
        $platformId = $domainInfo['tenant_id'];
    } else {
        // 系统管理员可以指定平台ID
        $platformId = isset($input['platform_id']) ? intval($input['platform_id']) : 1;
    }
    
    // 检查用户名是否已存在
    $existingUser = $db->fetch("SELECT id FROM users WHERE username = ?", array($username));
    if ($existingUser) {
        $auth->sendError('用户名已存在', 400);
        return;
    }
    
    // 检查码商代码是否已存在
    if ($providerCode) {
        $existingCode = $db->fetch("SELECT id FROM payment_providers WHERE provider_code = ?", array($providerCode));
        if ($existingCode) {
            $auth->sendError('码商代码已存在', 400);
            return;
        }
    }
    
    try {
        $db->execute("START TRANSACTION");
        
        // 创建用户
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $db->execute(
            "INSERT INTO users (username, password, user_type, status, created_at) 
             VALUES (?, ?, 'provider', 'pending', NOW())",
            array($username, $hashedPassword)
        );
        
        $userId = $db->lastInsertId();
        
        // 创建码商记录
        $db->execute(
            "INSERT INTO payment_providers (platform_id, user_id, provider_code, status, created_at) 
             VALUES (?, ?, ?, 'pending', NOW())",
            array($platformId, $userId, $providerCode)
        );
        
        $providerId = $db->lastInsertId();
        
        $db->execute("COMMIT");
        
        // 记录操作日志
        $logDesc = "创建码商: {$username}";
        if ($providerCode) $logDesc .= " (代码: {$providerCode})";
        $auth->logUserAction('create_provider', 'provider', $providerId, $logDesc);
        
        $auth->sendSuccess(array('provider_id' => $providerId), '码商创建成功');
        
    } catch (Exception $e) {
        $db->execute("ROLLBACK");
        error_log("创建码商失败: " . $e->getMessage());
        $auth->sendError('码商创建失败: ' . $e->getMessage(), 500);
    }
}

/**
 * 更新码商信息
 */
function handleUpdateProvider($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 检查权限
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限更新码商信息');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $providerId = isset($input['id']) ? intval($input['id']) : 0;
    if (!$providerId) {
        $auth->sendError('码商ID不能为空', 400);
        return;
    }
    
    // 验证码商是否存在且有权限操作
    $whereClause = "WHERE p.id = ?";
    $params = array($providerId);
    
    // 平台管理员只能操作自己平台的码商
    if ($currentUser['user_type'] === 'platform_admin') {
        $whereClause .= " AND p.platform_id = ?";
        $params[] = $domainInfo['tenant_id'];
    }
    
    $provider = $db->fetch(
        "SELECT p.id, p.user_id, u.username 
         FROM payment_providers p 
         JOIN users u ON p.user_id = u.id 
         $whereClause",
        $params
    );
    
    if (!$provider) {
        $auth->sendError('码商不存在或无权限操作');
        return;
    }
    
    // 确保查询结果完全处理完毕
    $providerId = intval($provider['id']);
    $userId = intval($provider['user_id']);
    $username = $provider['username'];
    
    try {
        $db->beginTransaction();
        
        // 更新用户信息
        if (isset($input['username'])) {
            $username = trim($input['username']);
            if (empty($username)) {
                $auth->sendError('用户名不能为空', 400);
                return;
            }
            
            // 检查用户名是否已被其他用户使用
            $existingUser = $db->fetch(
                "SELECT id FROM users WHERE username = ? AND id != ?", 
                array($username, $userId)
            );
            if ($existingUser) {
                $db->rollback();
                $auth->sendError('用户名已存在', 400);
                return;
            }
            
            $db->execute(
                "UPDATE users SET username = ? WHERE id = ?",
                array($username, $userId)
            );
        }
        
        // 更新用户状态
        if (isset($input['user_status'])) {
            $validStatuses = array('pending', 'active', 'disabled');
            if (in_array($input['user_status'], $validStatuses)) {
                $db->execute(
                    "UPDATE users SET status = ? WHERE id = ?",
                    array($input['user_status'], $userId)
                );
            }
        }
        
        // 更新码商信息
        $updateFields = array();
        $updateParams = array();
        
        if (isset($input['provider_code'])) {
            $providerCode = trim($input['provider_code']);
            if (!empty($providerCode)) {
                // 检查码商代码是否已被其他码商使用
                $existingCode = $db->fetch(
                    "SELECT id FROM payment_providers WHERE provider_code = ? AND id != ?", 
                    array($providerCode, $providerId)
                );
                if ($existingCode) {
                    $db->rollback();
                    $auth->sendError('码商代码已存在', 400);
                    return;
                }
            }
            $updateFields[] = "provider_code = ?";
            $updateParams[] = $providerCode;
        }
        
        if (isset($input['status'])) {
            $validProviderStatuses = array('pending', 'approved', 'active', 'inactive', 'disabled', 'suspended', 'rejected');
            if (in_array($input['status'], $validProviderStatuses)) {
                $updateFields[] = "status = ?";
                $updateParams[] = $input['status'];
            }
        }
        
        if (isset($input['max_devices'])) {
            $maxDevices = intval($input['max_devices']);
            if ($maxDevices > 0) {
                $updateFields[] = "max_devices = ?";
                $updateParams[] = $maxDevices;
            }
        }
        
        if (isset($input['max_groups'])) {
            $maxGroups = intval($input['max_groups']);
            if ($maxGroups > 0) {
                $updateFields[] = "max_groups = ?";
                $updateParams[] = $maxGroups;
            }
        }
        
        if (isset($input['device_management_enabled'])) {
            $deviceEnabled = $input['device_management_enabled'] ? 1 : 0;
            $updateFields[] = "device_management_enabled = ?";
            $updateParams[] = $deviceEnabled;
        }
        
        // 执行更新
        if (!empty($updateFields)) {
            $updateParams[] = $providerId;
            $db->execute(
                "UPDATE payment_providers SET " . implode(', ', $updateFields) . " WHERE id = ?",
                $updateParams
            );
        }
        
        $db->commit();
        
        // 记录操作日志
        $auth->logUserAction('update_provider', 'provider', $providerId, "更新码商信息: {$username}");
        
        $auth->sendSuccess(null, '码商信息更新成功');
        
    } catch (Exception $e) {
        $db->rollback();
        error_log("更新码商信息失败: " . $e->getMessage());
        $auth->sendError('更新码商信息失败: ' . $e->getMessage(), 500);
    }
}

function handleDeleteProvider($auth) {
    $auth->sendError('功能开发中', 501);
}

function handleUpdateProviderStatus($auth) {
    $auth->sendError('功能开发中', 501);
}

function handleGetProviderStats($auth) {
    // TODO: 实现码商统计功能
    $auth->sendSuccess(array(), '码商统计功能待实现');
}

// ===============================
// 新增用户操作功能
// ===============================

/**
 * 重置码商用户密码
 */
function handleResetProviderPassword($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 只有管理员和平台管理员可以重置密码
    if (!in_array($currentUser['user_type'], ['admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限重置码商密码');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $providerId = isset($input['provider_id']) ? intval($input['provider_id']) : 0;
    $newPassword = isset($input['new_password']) ? trim($input['new_password']) : '';
    
    if (!$providerId || !$newPassword) {
        $auth->sendError('码商ID和新密码不能为空');
        return;
    }
    
    if (strlen($newPassword) < 6) {
        $auth->sendError('密码长度不能少于6位');
        return;
    }
    
    try {
        // 验证码商是否存在且有权限操作
        $whereClause = "WHERE p.id = ?";
        $params = array($providerId);
        
        // 注意：数据隔离现在由Database类自动处理
        
        $provider = $db->fetch(
            "SELECT p.id, p.user_id, u.username 
             FROM payment_providers p 
             JOIN users u ON p.user_id = u.id 
             $whereClause",
            $params
        );
        
        if (!$provider) {
            $auth->sendError('码商不存在或无权限操作');
            return;
        }
        
        // 确保查询结果完全处理完毕
        $userId = intval($provider['user_id']);
        $username = $provider['username'];
        
        // 更新用户密码
        $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT);
        $db->execute(
            "UPDATE users SET password = ? WHERE id = ?",
            array($hashedPassword, $userId)
        );
        
        // 记录操作日志
        $auth->logUserAction(
            'reset_provider_password', 
            'provider', 
            $providerId, 
            "重置码商 {$username} 的密码"
        );
        
        $auth->sendSuccess(null, '码商密码重置成功');
        
    } catch (Exception $e) {
        error_log("重置码商密码失败: " . $e->getMessage());
        $auth->sendError('重置密码失败: ' . $e->getMessage());
    }
}

/**
 * 更新码商用户状态
 */
function handleUpdateProviderUserStatus($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 只有管理员和平台管理员可以修改状态
    if (!in_array($currentUser['user_type'], ['admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限修改码商状态');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $providerId = isset($input['provider_id']) ? intval($input['provider_id']) : 0;
    $userStatus = isset($input['user_status']) ? trim($input['user_status']) : '';
    $providerStatus = isset($input['provider_status']) ? trim($input['provider_status']) : '';
    
    if (!$providerId) {
        $auth->sendError('码商ID不能为空');
        return;
    }
    
    // 验证状态值
    $validUserStatuses = array('active', 'inactive', 'suspended');
    $validProviderStatuses = array('pending', 'approved', 'suspended', 'rejected');
    
    if ($userStatus && !in_array($userStatus, $validUserStatuses)) {
        $auth->sendError('无效的用户状态');
        return;
    }
    
    if ($providerStatus && !in_array($providerStatus, $validProviderStatuses)) {
        $auth->sendError('无效的码商状态');
        return;
    }
    
    try {
        // 验证码商是否存在且有权限操作
        $whereClause = "WHERE p.id = ?";
        $params = array($providerId);
        
        // 注意：数据隔离现在由Database类自动处理
        
        $provider = $db->fetch(
            "SELECT p.id, p.user_id, u.username 
             FROM payment_providers p 
             JOIN users u ON p.user_id = u.id 
             $whereClause",
            $params
        );
        
        if (!$provider) {
            $auth->sendError('码商不存在或无权限操作');
            return;
        }
        
        // 确保查询结果完全处理完毕
        $providerId = intval($provider['id']);
        $userId = intval($provider['user_id']);  
        $username = $provider['username'];
        
        // 开始事务
        $db->beginTransaction();
        
        // 更新用户状态
        if ($userStatus) {
            $db->execute(
                "UPDATE users SET status = ? WHERE id = ?",
                array($userStatus, $userId)
            );
        }
        
        // 更新码商状态
        if ($providerStatus) {
            $db->execute(
                "UPDATE payment_providers SET status = ? WHERE id = ?",
                array($providerStatus, $providerId)
            );
        }
        
        $db->commit();
        
        // 记录操作日志
        $logDesc = "更新码商 {$username} 状态";
        if ($userStatus) $logDesc .= " - 用户状态: $userStatus";
        if ($providerStatus) $logDesc .= " - 码商状态: $providerStatus";
        
        $auth->logUserAction(
            'update_provider_status', 
            'provider', 
            $providerId, 
            $logDesc
        );
        
        $auth->sendSuccess(null, '码商状态更新成功');
        
    } catch (Exception $e) {
        $db->rollback();
        error_log("更新码商状态失败: " . $e->getMessage());
        $auth->sendError('更新状态失败: ' . $e->getMessage());
    }
}

/**
 * 获取码商登录日志
 */
function handleGetProviderLoginLogs($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 检查权限
    if (!in_array($currentUser['user_type'], ['admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限查看码商登录日志');
        return;
    }
    
    $providerId = isset($_GET['provider_id']) ? intval($_GET['provider_id']) : 0;
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    if (!$providerId) {
        $auth->sendError('码商ID不能为空');
        return;
    }
    
    try {
        // 验证码商是否存在且有权限操作
        $whereClause = "WHERE p.id = ?";
        $params = array($providerId);
        
        // 注意：数据隔离现在由Database类自动处理
        
        $provider = $db->fetch(
            "SELECT p.id, p.user_id, u.username 
             FROM payment_providers p 
             JOIN users u ON p.user_id = u.id 
             $whereClause",
            $params
        );
        
        if (!$provider) {
            $auth->sendError('码商不存在或无权限操作');
            return;
        }
        
        // 获取登录日志
        $logs = $db->fetchAll(
            "SELECT 
                action, 
                description, 
                ip_address, 
                user_agent, 
                created_at
             FROM system_logs 
             WHERE user_id = ? AND action IN ('login', 'logout', 'tenant_login', 'tenant_logout')
             ORDER BY created_at DESC 
             LIMIT ? OFFSET ?",
            array($provider['user_id'], $limit, $offset)
        );
        
        // 获取总数
        $totalResult = $db->fetch(
            "SELECT COUNT(*) as count 
             FROM system_logs 
             WHERE user_id = ? AND action IN ('login', 'logout', 'tenant_login', 'tenant_logout')",
            array($provider['user_id'])
        );
        
        $response = array(
            'logs' => $logs,
            'provider' => array(
                'id' => $provider['id'],
                'username' => $provider['username']
            ),
            'pagination' => array(
                'total' => $totalResult['count'],
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($totalResult['count'] / $limit)
            )
        );
        
        $auth->sendSuccess($response, '获取码商登录日志成功');
        
    } catch (Exception $e) {
        error_log("获取码商登录日志失败: " . $e->getMessage());
        $auth->sendError('获取登录日志失败: ' . $e->getMessage());
    }
}

/**
 * 获取码商用户详情
 */
function handleGetProviderUserDetail($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 检查权限
    if (!in_array($currentUser['user_type'], ['admin', 'platform_admin', 'provider'])) {
        $auth->sendForbidden('无权限查看码商详情');
        return;
    }
    
    $providerId = isset($_GET['provider_id']) ? intval($_GET['provider_id']) : 0;
    
    if (!$providerId) {
        $auth->sendError('码商ID不能为空');
        return;
    }
    
    try {
        // 验证码商是否存在且有权限操作
        $whereClause = "WHERE p.id = ?";
        $params = array($providerId);
        
        // 注意：数据隔离现在由Database类自动处理
        // 码商权限特殊处理：码商只能查看自己的信息
        if ($currentUser['user_type'] === 'provider') {
            $whereClause .= " AND p.user_id = ?";
            $params[] = $currentUser['id'];
        }
        
        $provider = $db->fetch(
            "SELECT 
                p.*,
                u.username, u.user_type, u.status as user_status,
                u.created_at as user_created_at, u.last_login,
                plat.platform_name
             FROM payment_providers p 
             JOIN users u ON p.user_id = u.id 
             LEFT JOIN platforms plat ON p.platform_id = plat.id
             $whereClause",
            $params
        );
        
        if (!$provider) {
            $auth->sendError('码商不存在或无权限查看');
            return;
        }
        
        // 获取最近登录记录
        $recentLogins = $db->fetchAll(
            "SELECT ip_address, user_agent, created_at 
             FROM system_logs 
             WHERE user_id = ? AND action IN ('login', 'tenant_login')
             ORDER BY created_at DESC 
             LIMIT 5",
            array($provider['user_id'])
        );
        
        $provider['recent_logins'] = $recentLogins;
        
        $auth->sendSuccess($provider, '获取码商详情成功');
        
    } catch (Exception $e) {
        error_log("获取码商详情失败: " . $e->getMessage());
        $auth->sendError('获取码商详情失败: ' . $e->getMessage());
    }
}
?> 