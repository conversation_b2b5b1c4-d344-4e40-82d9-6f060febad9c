<?php
/**
 * API优化器类
 * 提供响应压缩、缓存、性能监控、错误处理等功能
 * 
 * <AUTHOR> System Team
 * @version 1.0.0
 * @since Day 14
 */

class ApiOptimizer {
    private $config;
    private $cache;
    private $metrics;
    private $startTime;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->startTime = microtime(true);
        $this->config = $this->loadConfig();
        $this->cache = new ApiCache();
        $this->metrics = new ApiMetrics();
        
        // 初始化性能监控
        $this->initializePerformanceMonitoring();
    }
    
    /**
     * 加载配置
     */
    private function loadConfig() {
        return array(
            'compression' => array(
                'enabled' => true,
                'level' => 6,
                'min_size' => 1024 // 1KB以上才压缩
            ),
            'cache' => array(
                'enabled' => true,
                'ttl' => 300, // 5分钟
                'key_prefix' => 'api_cache_'
            ),
            'rate_limit' => array(
                'enabled' => true,
                'requests_per_minute' => 60,
                'burst_limit' => 100
            ),
            'response_optimization' => array(
                'remove_null_fields' => true,
                'compress_arrays' => true,
                'format_numbers' => true
            )
        );
    }
    
    /**
     * 初始化性能监控
     */
    private function initializePerformanceMonitoring() {
        // 记录请求开始时间
        $this->metrics->recordRequestStart();
        
        // 监控内存使用
        $this->metrics->recordMemoryUsage('start', memory_get_usage(true));
        
        // 监控数据库连接
        $this->metrics->recordDatabaseConnectionStart();
    }
    
    /**
     * 处理API请求
     * 
     * @param string $endpoint 端点名称
     * @param callable $handler 处理函数
     * @param array $options 选项
     * @return mixed
     */
    public function handleRequest($endpoint, $handler, $options = array()) {
        try {
            // 检查速率限制
            if ($this->config['rate_limit']['enabled']) {
                $this->checkRateLimit();
            }
            
            // 生成缓存键
            $cacheKey = $this->generateCacheKey($endpoint);
            
            // 尝试从缓存获取
            if ($this->config['cache']['enabled'] && $_SERVER['REQUEST_METHOD'] === 'GET') {
                $cached = $this->cache->get($cacheKey);
                if ($cached !== null) {
                    $this->metrics->recordCacheHit();
                    return $this->sendResponse($cached, true);
                }
            }
            
            // 执行处理函数
            $this->metrics->recordProcessingStart();
            $result = call_user_func($handler);
            $this->metrics->recordProcessingEnd();
            
            // 优化响应数据
            $optimizedResult = $this->optimizeResponse($result);
            
            // 缓存结果（仅GET请求）
            if ($this->config['cache']['enabled'] && $_SERVER['REQUEST_METHOD'] === 'GET' && $this->isSuccessResponse($optimizedResult)) {
                $this->cache->set($cacheKey, $optimizedResult, $this->config['cache']['ttl']);
            }
            
            // 发送响应
            return $this->sendResponse($optimizedResult);
            
        } catch (ApiException $e) {
            return $this->handleApiException($e);
        } catch (Exception $e) {
            return $this->handleGenericException($e);
        } finally {
            // 记录性能指标
            $this->recordPerformanceMetrics($endpoint);
        }
    }
    
    /**
     * 检查速率限制
     */
    private function checkRateLimit() {
        $clientIp = $this->getClientIp();
        $key = "rate_limit_{$clientIp}";
        
        $current = $this->cache->get($key, 0);
        
        if ($current >= $this->config['rate_limit']['requests_per_minute']) {
            throw new ApiException('Rate limit exceeded', 429);
        }
        
        $this->cache->increment($key, 1, 60); // 1分钟过期
    }
    
    /**
     * 生成缓存键
     */
    private function generateCacheKey($endpoint) {
        $params = $_GET;
        unset($params['action']); // 移除action参数
        
        $keyData = array(
            'endpoint' => $endpoint,
            'params' => $params,
            'user_id' => $this->getCurrentUserId(),
            'version' => $this->getApiVersion()
        );
        
        return $this->config['cache']['key_prefix'] . md5(json_encode($keyData));
    }
    
    /**
     * 优化响应数据
     */
    private function optimizeResponse($data) {
        if (!is_array($data)) {
            return $data;
        }
        
        $config = $this->config['response_optimization'];
        
        // 移除null字段
        if ($config['remove_null_fields']) {
            $data = $this->removeNullFields($data);
        }
        
        // 压缩数组
        if ($config['compress_arrays']) {
            $data = $this->compressArrays($data);
        }
        
        // 格式化数字
        if ($config['format_numbers']) {
            $data = $this->formatNumbers($data);
        }
        
        return $data;
    }
    
    /**
     * 移除null字段
     */
    private function removeNullFields($data) {
        if (is_array($data)) {
            $result = array();
            foreach ($data as $key => $value) {
                if ($value !== null) {
                    $result[$key] = is_array($value) ? $this->removeNullFields($value) : $value;
                }
            }
            return $result;
        }
        return $data;
    }
    
    /**
     * 压缩数组（移除空数组）
     */
    private function compressArrays($data) {
        if (is_array($data)) {
            $result = array();
            foreach ($data as $key => $value) {
                if (is_array($value)) {
                    $compressed = $this->compressArrays($value);
                    if (!empty($compressed)) {
                        $result[$key] = $compressed;
                    }
                } else {
                    $result[$key] = $value;
                }
            }
            return $result;
        }
        return $data;
    }
    
    /**
     * 格式化数字
     */
    private function formatNumbers($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if (is_float($value)) {
                    $data[$key] = round($value, 2);
                } elseif (is_array($value)) {
                    $data[$key] = $this->formatNumbers($value);
                }
            }
        }
        return $data;
    }
    
    /**
     * 发送响应
     */
    private function sendResponse($data, $fromCache = false) {
        // 设置响应头
        $this->setResponseHeaders($fromCache);
        
        // 编码响应
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE);
        
        // 压缩响应
        if ($this->shouldCompress($jsonData)) {
            $jsonData = $this->compressResponse($jsonData);
        }
        
        // 输出响应
        echo $jsonData;
        
        // 记录响应大小
        $this->metrics->recordResponseSize(strlen($jsonData));
        
        return $data;
    }
    
    /**
     * 设置响应头
     */
    private function setResponseHeaders($fromCache = false) {
        header('Content-Type: application/json; charset=utf-8');
        header('X-API-Version: ' . $this->getApiVersion());
        header('X-Response-Time: ' . $this->getResponseTime() . 'ms');
        
        if ($fromCache) {
            header('X-Cache: HIT');
        } else {
            header('X-Cache: MISS');
        }
        
        // 设置缓存控制头
        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            header('Cache-Control: public, max-age=300');
            header('ETag: ' . md5(json_encode($_GET)));
        } else {
            header('Cache-Control: no-cache, no-store, must-revalidate');
        }
    }
    
    /**
     * 检查是否应该压缩
     */
    private function shouldCompress($data) {
        if (!$this->config['compression']['enabled']) {
            return false;
        }
        
        if (strlen($data) < $this->config['compression']['min_size']) {
            return false;
        }
        
        $acceptEncoding = isset($_SERVER['HTTP_ACCEPT_ENCODING']) ? $_SERVER['HTTP_ACCEPT_ENCODING'] : '';
        return strpos($acceptEncoding, 'gzip') !== false;
    }
    
    /**
     * 压缩响应
     */
    private function compressResponse($data) {
        if (function_exists('gzencode')) {
            header('Content-Encoding: gzip');
            return gzencode($data, $this->config['compression']['level']);
        }
        return $data;
    }
    
    /**
     * 处理API异常
     */
    private function handleApiException(ApiException $e) {
        $this->metrics->recordError($e->getCode(), $e->getMessage());
        
        $response = array(
            'success' => false,
            'error' => array(
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
                'type' => 'api_error'
            ),
            'timestamp' => time(),
            'request_id' => $this->generateRequestId()
        );
        
        // 开发环境下添加调试信息
        if ($this->isDevelopmentMode()) {
            $response['debug'] = array(
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            );
        }
        
        http_response_code($e->getCode());
        return $this->sendResponse($response);
    }
    
    /**
     * 处理通用异常
     */
    private function handleGenericException(Exception $e) {
        $this->metrics->recordError(500, $e->getMessage());
        
        // 记录错误日志
        error_log("API Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
        
        $response = array(
            'success' => false,
            'error' => array(
                'code' => 500,
                'message' => 'Internal Server Error',
                'type' => 'server_error'
            ),
            'timestamp' => time(),
            'request_id' => $this->generateRequestId()
        );
        
        // 开发环境下显示详细错误
        if ($this->isDevelopmentMode()) {
            $response['error']['message'] = $e->getMessage();
            $response['debug'] = array(
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            );
        }
        
        http_response_code(500);
        return $this->sendResponse($response);
    }
    
    /**
     * 记录性能指标
     */
    private function recordPerformanceMetrics($endpoint) {
        $endTime = microtime(true);
        $responseTime = ($endTime - $this->startTime) * 1000; // 转换为毫秒
        
        $this->metrics->recordResponseTime($responseTime);
        $this->metrics->recordMemoryUsage('end', memory_get_usage(true));
        $this->metrics->recordEndpoint($endpoint);
        
        // 保存性能数据到数据库
        $this->savePerformanceData($endpoint, $responseTime);
    }
    
    /**
     * 保存性能数据
     */
    private function savePerformanceData($endpoint, $responseTime) {
        try {
            global $db;
            
            $stmt = $db->prepare("
                INSERT INTO api_performance_logs 
                (endpoint, response_time, memory_usage, timestamp, user_id, ip_address) 
                VALUES (?, ?, ?, NOW(), ?, ?)
            ");
            
            $stmt->execute(array(
                $endpoint,
                $responseTime,
                memory_get_peak_usage(true),
                $this->getCurrentUserId(),
                $this->getClientIp()
            ));
            
        } catch (Exception $e) {
            // 静默处理性能日志错误，不影响主要功能
            error_log("Performance logging error: " . $e->getMessage());
        }
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        $headers = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP');
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                return trim($ips[0]);
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
    }
    
    /**
     * 获取当前用户ID
     */
    private function getCurrentUserId() {
        global $currentUser;
        return $currentUser ? $currentUser['id'] : null;
    }
    
    /**
     * 获取API版本
     */
    private function getApiVersion() {
        return isset($_SERVER['HTTP_X_API_VERSION']) ? $_SERVER['HTTP_X_API_VERSION'] : '1.0';
    }
    
    /**
     * 获取响应时间
     */
    private function getResponseTime() {
        return round((microtime(true) - $this->startTime) * 1000, 2);
    }
    
    /**
     * 生成请求ID
     */
    private function generateRequestId() {
        return uniqid('req_', true);
    }
    
    /**
     * 检查是否为开发模式
     */
    private function isDevelopmentMode() {
        return defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE === true;
    }
    
    /**
     * 检查是否为成功响应
     */
    private function isSuccessResponse($response) {
        return is_array($response) && isset($response['success']) && $response['success'] === true;
    }
    
    /**
     * 获取性能统计
     */
    public function getPerformanceStats() {
        return $this->metrics->getStats();
    }
}

/**
 * API缓存类
 */
class ApiCache {
    private $cacheDir;
    
    public function __construct() {
        $this->cacheDir = dirname(dirname(__FILE__)) . '/cache/api/';
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * 获取缓存
     */
    public function get($key, $default = null) {
        $file = $this->getCacheFile($key);
        
        if (!file_exists($file)) {
            return $default;
        }
        
        $data = file_get_contents($file);
        $cache = json_decode($data, true);
        
        if (!$cache || $cache['expires'] < time()) {
            unlink($file);
            return $default;
        }
        
        return $cache['data'];
    }
    
    /**
     * 设置缓存
     */
    public function set($key, $value, $ttl = 300) {
        $file = $this->getCacheFile($key);
        
        $cache = array(
            'data' => $value,
            'expires' => time() + $ttl,
            'created' => time()
        );
        
        file_put_contents($file, json_encode($cache));
    }
    
    /**
     * 增加计数器
     */
    public function increment($key, $value = 1, $ttl = 300) {
        $current = $this->get($key, 0);
        $this->set($key, $current + $value, $ttl);
        return $current + $value;
    }
    
    /**
     * 删除缓存
     */
    public function delete($key) {
        $file = $this->getCacheFile($key);
        if (file_exists($file)) {
            unlink($file);
        }
    }
    
    /**
     * 清空所有缓存
     */
    public function clear() {
        $files = glob($this->cacheDir . '*.cache');
        foreach ($files as $file) {
            unlink($file);
        }
    }
    
    /**
     * 获取缓存文件路径
     */
    private function getCacheFile($key) {
        return $this->cacheDir . md5($key) . '.cache';
    }
}

/**
 * API指标类
 */
class ApiMetrics {
    private $stats;
    
    public function __construct() {
        $this->stats = array(
            'requests' => 0,
            'errors' => 0,
            'cache_hits' => 0,
            'response_times' => array(),
            'memory_usage' => array(),
            'endpoints' => array()
        );
    }
    
    public function recordRequestStart() {
        $this->stats['requests']++;
    }
    
    public function recordError($code, $message) {
        $this->stats['errors']++;
    }
    
    public function recordCacheHit() {
        $this->stats['cache_hits']++;
    }
    
    public function recordResponseTime($time) {
        $this->stats['response_times'][] = $time;
    }
    
    public function recordMemoryUsage($type, $usage) {
        $this->stats['memory_usage'][$type] = $usage;
    }
    
    public function recordEndpoint($endpoint) {
        if (!isset($this->stats['endpoints'][$endpoint])) {
            $this->stats['endpoints'][$endpoint] = 0;
        }
        $this->stats['endpoints'][$endpoint]++;
    }
    
    public function recordProcessingStart() {
        $this->processingStart = microtime(true);
    }
    
    public function recordProcessingEnd() {
        if (isset($this->processingStart)) {
            $this->stats['processing_time'] = (microtime(true) - $this->processingStart) * 1000;
        }
    }
    
    public function recordDatabaseConnectionStart() {
        $this->dbStart = microtime(true);
    }
    
    public function recordResponseSize($size) {
        $this->stats['response_size'] = $size;
    }
    
    public function getStats() {
        return $this->stats;
    }
}

/**
 * API异常类
 */
class ApiException extends Exception {
    public function __construct($message, $code = 400, Exception $previous = null) {
        parent::__construct($message, $code, $previous);
    }
}
?> 