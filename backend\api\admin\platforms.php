<?php
/**
 * 平台管理API - 系统管理员专用
 * 负责管理所有平台方的信息和配置
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-12-17
 */

// 防止直接访问
if (!defined('IN_ADMIN_PANEL')) {
    http_response_code(403);
    exit('Access denied');
}

// 获取全局变量
$tenantContext = $GLOBALS['tenant_context'];
$currentUser = $GLOBALS['current_user'];

// 验证权限：只有系统管理员可以管理平台
if (!$tenantContext || $tenantContext['tenant_type'] !== 'system_admin') {
    http_response_code(403);
    echo json_encode(array(
        'error_code' => 403,
        'error_message' => '权限不足，只有系统管理员可以管理平台'
    ));
    exit;
}

try {
    $db = Database::getInstance();
    
    switch ($action) {
        case 'get_platforms_list':
            // 获取平台列表
            $platforms = $db->fetchAll("
                SELECT 
                    p.*,
                    (SELECT COUNT(*) FROM tenant_domains d WHERE d.platform_id = p.id) as domain_count,
                    (SELECT COUNT(*) FROM users u WHERE u.platform_id = p.id AND u.user_type = 'provider') as provider_count,
                    (SELECT COUNT(*) FROM users u WHERE u.platform_id = p.id AND u.user_type = 'merchant') as merchant_count
                FROM platforms p 
                ORDER BY p.created_at DESC
            ");
            
            // 获取统计数据
            $stats = array(
                'total_platforms' => $db->fetchColumn("SELECT COUNT(*) FROM platforms"),
                'total_providers' => $db->fetchColumn("SELECT COUNT(*) FROM users WHERE user_type = 'provider'"),
                'total_merchants' => $db->fetchColumn("SELECT COUNT(*) FROM users WHERE user_type = 'merchant'"),
                'total_domains' => $db->fetchColumn("SELECT COUNT(*) FROM tenant_domains")
            );
            
            echo json_encode(array(
                'error_code' => 0,
                'message' => '获取平台列表成功',
                'data' => array(
                    'platforms' => $platforms,
                    'stats' => $stats
                )
            ));
            break;
            
        case 'get_platform_details':
            // 获取平台详情
            $platformId = intval($input['platform_id']);
            if (!$platformId) {
                throw new Exception('平台ID不能为空');
            }
            
            $platform = $db->fetch("SELECT * FROM platforms WHERE id = ?", array($platformId));
            if (!$platform) {
                throw new Exception('平台不存在');
            }
            
            // 获取平台的域名列表
            $domains = $db->fetchAll("
                SELECT * FROM tenant_domains 
                WHERE platform_id = ? 
                ORDER BY created_at DESC
            ", array($platformId));
            
            // 获取平台的用户统计
            $userStats = $db->fetch("
                SELECT 
                    COUNT(CASE WHEN user_type = 'provider' THEN 1 END) as provider_count,
                    COUNT(CASE WHEN user_type = 'merchant' THEN 1 END) as merchant_count
                FROM users 
                WHERE platform_id = ?
            ", array($platformId));
            
            echo json_encode(array(
                'error_code' => 0,
                'message' => '获取平台详情成功',
                'data' => array(
                    'platform' => $platform,
                    'domains' => $domains,
                    'user_stats' => $userStats
                )
            ));
            break;
            
        case 'add_platform':
            // 添加平台
            $platformName = trim($input['platform_name']);
            $platformCode = trim($input['platform_code']);
            $contactName = trim($input['contact_name']);
            $contactEmail = trim($input['contact_email']);
            $contactPhone = trim($input['contact_phone']);
            $description = trim($input['description']);
            
            if (!$platformName || !$platformCode) {
                throw new Exception('平台名称和平台代码不能为空');
            }
            
            // 检查平台代码是否已存在
            $existing = $db->fetch("SELECT id FROM platforms WHERE platform_code = ?", array($platformCode));
            if ($existing) {
                throw new Exception('平台代码已存在');
            }
            
            $platformId = $db->insert('platforms', array(
                'platform_name' => $platformName,
                'platform_code' => $platformCode,
                'contact_name' => $contactName,
                'contact_email' => $contactEmail,
                'contact_phone' => $contactPhone,
                'description' => $description,
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ));
            
            // 记录操作日志
            logTenantAction('add_platform', 'platform', $platformId, "添加平台: {$platformName}");
            
            echo json_encode(array(
                'error_code' => 0,
                'message' => '添加平台成功',
                'data' => array('platform_id' => $platformId)
            ));
            break;
            
        case 'edit_platform':
            // 编辑平台
            $platformId = intval($input['platform_id']);
            if (!$platformId) {
                throw new Exception('平台ID不能为空');
            }
            
            $platform = $db->fetch("SELECT * FROM platforms WHERE id = ?", array($platformId));
            if (!$platform) {
                throw new Exception('平台不存在');
            }
            
            $updateData = array();
            if (isset($input['platform_name'])) {
                $updateData['platform_name'] = trim($input['platform_name']);
            }
            if (isset($input['contact_name'])) {
                $updateData['contact_name'] = trim($input['contact_name']);
            }
            if (isset($input['contact_email'])) {
                $updateData['contact_email'] = trim($input['contact_email']);
            }
            if (isset($input['contact_phone'])) {
                $updateData['contact_phone'] = trim($input['contact_phone']);
            }
            if (isset($input['description'])) {
                $updateData['description'] = trim($input['description']);
            }
            if (isset($input['status'])) {
                $updateData['status'] = $input['status'];
            }
            
            $updateData['updated_at'] = date('Y-m-d H:i:s');
            
            $db->update('platforms', $updateData, array('id' => $platformId));
            
            // 记录操作日志
            logTenantAction('edit_platform', 'platform', $platformId, "编辑平台: {$platform['platform_name']}");
            
            echo json_encode(array(
                'error_code' => 0,
                'message' => '编辑平台成功'
            ));
            break;
            
        case 'delete_platform':
            // 删除平台
            $platformId = intval($input['platform_id']);
            if (!$platformId) {
                throw new Exception('平台ID不能为空');
            }
            
            $platform = $db->fetch("SELECT * FROM platforms WHERE id = ?", array($platformId));
            if (!$platform) {
                throw new Exception('平台不存在');
            }
            
            // 检查是否有关联的域名或用户
            $domainCount = $db->fetchColumn("SELECT COUNT(*) FROM tenant_domains WHERE platform_id = ?", array($platformId));
            $userCount = $db->fetchColumn("SELECT COUNT(*) FROM users WHERE platform_id = ?", array($platformId));
            
            if ($domainCount > 0 || $userCount > 0) {
                throw new Exception('该平台下还有域名或用户，无法删除');
            }
            
            $db->delete('platforms', array('id' => $platformId));
            
            // 记录操作日志
            logTenantAction('delete_platform', 'platform', $platformId, "删除平台: {$platform['platform_name']}");
            
            echo json_encode(array(
                'error_code' => 0,
                'message' => '删除平台成功'
            ));
            break;
            
        default:
            throw new Exception('未知的操作类型');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'error_code' => 400,
        'error_message' => $e->getMessage()
    ));
}
?> 