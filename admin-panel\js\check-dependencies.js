/**
 * 模块依赖检查脚本
 * 用于验证PayPal管理系统模块化重构后的依赖关系
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-12-22
 */

class DependencyChecker {
    constructor() {
        this.modules = new Map();
        this.dependencies = new Map();
        this.errors = [];
        this.warnings = [];
        this.results = [];
        
        this.initializeModuleConfig();
    }
    
    /**
     * 初始化模块配置
     */
    initializeModuleConfig() {
        // 定义模块及其依赖关系
        this.moduleConfig = {
            // 核心模块
            'core/utils': {
                path: './js/modules/core/utils.js',
                dependencies: [],
                exports: ['CONFIG', 'PerformanceMonitor', 'CacheManager', 'AdminUtils'],
                critical: true
            },
            'core/auth': {
                path: './js/modules/core/auth.js',
                dependencies: ['core/utils'],
                exports: ['AuthManager', 'LoginManager', 'PermissionDecorator'],
                critical: true
            },
            'core/app-initializer': {
                path: './js/modules/core/app-initializer.js',
                dependencies: ['core/utils', 'core/auth'],
                exports: ['AppInitializer'],
                critical: true
            },
            'core/module-loader': {
                path: './js/modules/core/module-loader.js',
                dependencies: ['core/utils'],
                exports: ['ModuleLoader'],
                critical: true
            },
            
            // 业务模块
            'device/management': {
                path: './js/modules/device/management.js',
                dependencies: ['core/utils', 'core/auth'],
                exports: ['DeviceManager'],
                critical: false
            },
            'dashboard': {
                path: './js/modules/dashboard.js',
                dependencies: ['core/utils', 'core/auth'],
                exports: ['DashboardManager', 'DashboardModule'],
                critical: false
            },
            // ui-components已过期，功能迁移到core/ui-manager.js
            // 'ui-components': {
            //     path: './js/modules/ui-components.js',
            //     dependencies: ['core/utils'],
            //     exports: ['UIManager', 'UIComponentsModule'],
            //     critical: false
            // },
            
            // 新增模块
            'security/security-management': {
                path: './js/modules/security/security-management.js',
                dependencies: ['core/utils', 'core/auth'],
                exports: ['SecurityModuleManager', 'RiskControlManager', 'BlacklistManager'],
                critical: false,
                status: 'framework' // 框架完成，需要提取实现
            },
            'system/system-monitoring': {
                path: './js/modules/system/system-monitoring.js',
                dependencies: ['core/utils', 'core/auth'],
                exports: ['SystemMonitoringManager', 'PerformanceManager', 'NotificationManager'],
                critical: false,
                status: 'framework' // 框架完成，需要提取实现
            }
        };
    }
    
    /**
     * 检查所有模块依赖
     */
    async checkAllDependencies() {
        console.log('🔍 开始检查模块依赖关系...');
        
        this.errors = [];
        this.warnings = [];
        this.results = [];
        
        // 检查文件存在性
        await this.checkFileExistence();
        
        // 检查依赖关系
        await this.checkDependencyChain();
        
        // 检查循环依赖
        this.checkCircularDependencies();
        
        // 检查导出对象
        await this.checkExports();
        
        // 生成报告
        this.generateReport();
        
        return {
            success: this.errors.length === 0,
            errors: this.errors,
            warnings: this.warnings,
            results: this.results
        };
    }
    
    /**
     * 检查文件存在性
     */
    async checkFileExistence() {
        console.log('📁 检查模块文件存在性...');
        
        for (const [moduleName, config] of Object.entries(this.moduleConfig)) {
            try {
                // 使用HEAD请求检查文件存在性，不加载内容
                const response = await fetch(config.path, { 
                    method: 'HEAD',
                    cache: 'no-cache' 
                });
                
                if (response.ok) {
                    this.results.push({
                        type: 'success',
                        module: moduleName,
                        message: '文件存在'
                    });
                } else {
                    const error = `模块文件不存在: ${config.path}`;
                    if (config.critical) {
                        this.errors.push(error);
                    } else {
                        this.warnings.push(error);
                    }
                    
                    this.results.push({
                        type: config.critical ? 'error' : 'warning',
                        module: moduleName,
                        message: '文件不存在'
                    });
                }
            } catch (error) {
                const errorMsg = `无法访问模块文件: ${config.path} - ${error.message}`;
                if (config.critical) {
                    this.errors.push(errorMsg);
                } else {
                    this.warnings.push(errorMsg);
                }
                
                this.results.push({
                    type: config.critical ? 'error' : 'warning',
                    module: moduleName,
                    message: '文件访问失败'
                });
            }
        }
    }
    
    /**
     * 检查依赖链
     */
    async checkDependencyChain() {
        console.log('🔗 检查模块依赖链...');
        
        for (const [moduleName, config] of Object.entries(this.moduleConfig)) {
            for (const dependency of config.dependencies) {
                if (!this.moduleConfig[dependency]) {
                    const error = `模块 ${moduleName} 依赖的模块 ${dependency} 不存在`;
                    this.errors.push(error);
                    
                    this.results.push({
                        type: 'error',
                        module: moduleName,
                        message: `依赖模块不存在: ${dependency}`
                    });
                } else {
                    this.results.push({
                        type: 'success',
                        module: moduleName,
                        message: `依赖关系正确: ${dependency}`
                    });
                }
            }
        }
    }
    
    /**
     * 检查循环依赖
     */
    checkCircularDependencies() {
        console.log('🔄 检查循环依赖...');
        
        const visited = new Set();
        const recursionStack = new Set();
        
        const hasCycle = (moduleName, path = []) => {
            if (recursionStack.has(moduleName)) {
                const cycle = [...path, moduleName];
                const error = `发现循环依赖: ${cycle.join(' -> ')}`;
                this.errors.push(error);
                
                this.results.push({
                    type: 'error',
                    module: moduleName,
                    message: `循环依赖: ${cycle.join(' -> ')}`
                });
                return true;
            }
            
            if (visited.has(moduleName)) {
                return false;
            }
            
            visited.add(moduleName);
            recursionStack.add(moduleName);
            
            const config = this.moduleConfig[moduleName];
            if (config) {
                for (const dependency of config.dependencies) {
                    if (hasCycle(dependency, [...path, moduleName])) {
                        return true;
                    }
                }
            }
            
            recursionStack.delete(moduleName);
            return false;
        };
        
        for (const moduleName of Object.keys(this.moduleConfig)) {
            if (!visited.has(moduleName)) {
                hasCycle(moduleName);
            }
        }
        
        if (this.errors.filter(e => e.includes('循环依赖')).length === 0) {
            this.results.push({
                type: 'success',
                module: 'all',
                message: '未发现循环依赖'
            });
        }
    }
    
    /**
     * 检查导出对象
     */
    async checkExports() {
        console.log('📤 检查模块导出对象...');
        
        // 等待更长时间确保所有模块都已加载完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        for (const [moduleName, config] of Object.entries(this.moduleConfig)) {
            try {
                // 检查导出对象 (不重复加载，直接检查已存在的全局变量)
                let missingExports = [];
                let existingExports = [];
                
                for (const exportName of config.exports) {
                    if (typeof window[exportName] === 'undefined') {
                        missingExports.push(exportName);
                    } else {
                        existingExports.push(exportName);
                    }
                }
                
                if (missingExports.length > 0) {
                    const message = `缺少导出对象: ${missingExports.join(', ')}`;
                    
                    // 如果是框架状态，只给警告
                    if (config.status === 'framework') {
                        this.warnings.push(`${moduleName}: ${message} (框架完成，需要提取实现)`);
                        this.results.push({
                            type: 'warning',
                            module: moduleName,
                            message: `${message} (框架状态)`
                        });
                    } else {
                        this.errors.push(`${moduleName}: ${message}`);
                        this.results.push({
                            type: 'error',
                            module: moduleName,
                            message: message
                        });
                    }
                } else {
                    this.results.push({
                        type: 'success',
                        module: moduleName,
                        message: '所有导出对象存在'
                    });
                }
                
                // 调试信息：显示找到的导出对象
                console.log(`🔍 模块 ${moduleName} 导出检查:`);
                console.log(`  ✅ 存在: ${existingExports.join(', ') || '无'}`);
                console.log(`  ❌ 缺失: ${missingExports.join(', ') || '无'}`);
                console.log(`  📋 期望: ${config.exports.join(', ')}`);
                
            } catch (error) {
                const errorMsg = `模块导出检查失败: ${error.message}`;
                this.warnings.push(`${moduleName}: ${errorMsg}`);
                
                this.results.push({
                    type: 'warning',
                    module: moduleName,
                    message: '导出检查失败'
                });
            }
        }
    }
    
    /**
     * 生成检查报告
     */
    generateReport() {
        console.log('\n=== 模块依赖检查报告 ===');
        console.log(`检查时间: ${new Date().toLocaleString()}`);
        console.log(`总模块数: ${Object.keys(this.moduleConfig).length}`);
        console.log(`错误数: ${this.errors.length}`);
        console.log(`警告数: ${this.warnings.length}`);
        
        if (this.errors.length > 0) {
            console.log('\n❌ 错误列表:');
            this.errors.forEach((error, index) => {
                console.log(`${index + 1}. ${error}`);
            });
        }
        
        if (this.warnings.length > 0) {
            console.log('\n⚠️ 警告列表:');
            this.warnings.forEach((warning, index) => {
                console.log(`${index + 1}. ${warning}`);
            });
        }
        
        if (this.errors.length === 0) {
            console.log('\n✅ 所有关键依赖检查通过！');
        } else {
            console.log('\n🚨 发现关键错误，需要修复后才能正常使用！');
        }
        
        // 模块状态统计
        console.log('\n📊 模块状态统计:');
        const statusCount = {
            完成: 0,
            框架: 0,
            缺失: 0
        };
        
        for (const [moduleName, config] of Object.entries(this.moduleConfig)) {
            const moduleResults = this.results.filter(r => r.module === moduleName);
            const hasErrors = moduleResults.some(r => r.type === 'error');
            const isFramework = config.status === 'framework';
            
            if (hasErrors) {
                statusCount.缺失++;
            } else if (isFramework) {
                statusCount.框架++;
            } else {
                statusCount.完成++;
            }
        }
        
        console.log(`- 完成: ${statusCount.完成} 个模块`);
        console.log(`- 框架: ${statusCount.框架} 个模块 (需要提取实现)`);
        console.log(`- 缺失: ${statusCount.缺失} 个模块`);
        
        return {
            summary: {
                total: Object.keys(this.moduleConfig).length,
                errors: this.errors.length,
                warnings: this.warnings.length,
                status: statusCount
            },
            details: this.results
        };
    }
    
    /**
     * 获取模块加载顺序
     */
    getLoadOrder() {
        const loadOrder = [];
        const visited = new Set();
        
        const visit = (moduleName) => {
            if (visited.has(moduleName)) return;
            visited.add(moduleName);
            
            const config = this.moduleConfig[moduleName];
            if (config) {
                // 先访问依赖
                for (const dependency of config.dependencies) {
                    visit(dependency);
                }
                // 再添加当前模块
                loadOrder.push(moduleName);
            }
        };
        
        // 访问所有模块
        for (const moduleName of Object.keys(this.moduleConfig)) {
            visit(moduleName);
        }
        
        return loadOrder;
    }
    
    /**
     * 导出检查结果
     */
    exportResults() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                total: Object.keys(this.moduleConfig).length,
                errors: this.errors.length,
                warnings: this.warnings.length
            },
            modules: this.moduleConfig,
            results: this.results,
            errors: this.errors,
            warnings: this.warnings,
            loadOrder: this.getLoadOrder()
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `dependency-check-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log('📄 依赖检查报告已导出');
    }
}

// 全局导出
window.DependencyChecker = DependencyChecker;

// 便捷方法
window.checkDependencies = async function() {
    const checker = new DependencyChecker();
    return await checker.checkAllDependencies();
};

// 注释掉自动执行，改为手动触发
// if (window.location.href.includes('module-test.html')) {
//     window.addEventListener('load', async function() {
//         console.log('🔧 自动执行模块依赖检查...');
//         await window.checkDependencies();
//     });
// }

console.log('✅ 依赖检查脚本加载完成'); 