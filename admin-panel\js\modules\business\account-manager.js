// 支付宝账户管理类
class AccountManager {
    constructor() {
        this.auth = new AuthManager();
        this.currentPage = 1;
        this.pageSize = 20;
    }

    // 为当前用户加载账户（码商自动使用自己的ID）
    async loadAccountsForCurrentUser(page = 1) {
        const user = this.auth.getUser();
        if (!user) {
            this.showError('用户未登录');
            return;
        }

        console.log('loadAccountsForCurrentUser 用户信息:', user);

        // 码商使用自己的 profile_id，管理员需要指定 provider_id
        let providerId = null;
        if (user.user_type === 'provider') {
            providerId = user.profile_id;
            console.log('Provider用户，profile_id:', providerId);
        } else if (user.user_type === 'admin') {
            // 管理员可以从筛选器中获取 provider_id
            providerId = document.getElementById('providerFilter')?.value || '';
            console.log('Admin用户，从筛选器获取provider_id:', providerId);
            if (!providerId) {
                // 如果管理员没选择，显示提示但仍然加载所有数据
                console.log('管理员未选择码商，加载所有数据');
                this.loadAccountsForAdmin(page);
                return;
            }
        } else {
            this.showError('无权限访问支付宝账户管理');
            return;
        }

        if (!providerId) {
            console.error('无法获取码商ID，用户信息:', user);
            this.showError('无法获取码商ID，请检查用户信息');
            return;
        }

        console.log('开始调用loadAccounts，providerId:', providerId);
        this.loadAccounts(page, providerId);
    }

    // 管理员加载所有账户
    async loadAccountsForAdmin(page = 1) {
        this.currentPage = page;
        const status = document.getElementById('statusFilter')?.value || '';
        const search = document.getElementById('searchInput')?.value || '';

        // 显示加载状态
        const container = document.getElementById('accountsTableContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载账户数据...</p>
                </div>
            `;
        }

        const params = new URLSearchParams({
            page: page,
            limit: this.pageSize
        });

        if (status) params.append('status', status);
        if (search) params.append('search', search);

        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=alipay_accounts&${params}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const data = await response.json();

            if (data.code === 200) {
                this.renderAccountListNew(data.data.accounts);
                this.renderStatsNew(data.data.stats);
                this.renderPaginationNew(data.data.pagination);
                this.updateAccountCount(data.data.pagination.total_records);
            } else {
                this.showError('加载账户失败: ' + (data.message || '未知错误'));
                this.showErrorState('加载失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('Load accounts error:', error);
            this.showError('网络错误，请重试');
            this.showErrorState('网络错误，请重试');
        }
    }

    async loadAccounts(page = 1, providerId = null) {
        console.log('loadAccounts 被调用，参数:', {page, providerId});
        this.currentPage = page;

        // 如果没有提供 providerId，尝试从页面元素获取
        if (!providerId) {
            providerId = document.getElementById('providerId')?.value || '';
            if (!providerId) {
                console.error('loadAccounts: 无法获取providerId');
                this.showError('请输入码商ID');
                return;
            }
        }

        const status = document.getElementById('statusFilter')?.value || '';
        const search = document.getElementById('searchInput')?.value || '';

        console.log('loadAccounts 筛选参数:', {status, search, providerId});

        // 显示加载状态
        const loadingContainer = document.getElementById('accountsTableContainer');
        if (loadingContainer) {
            loadingContainer.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载账户数据...</p>
                </div>
            `;
        }

        const params = new URLSearchParams({
            page: page,
            limit: this.pageSize
        });

        if (status) params.append('status', status);
        if (search) params.append('search', search);

        const apiUrl = `${CONFIG.API_BASE_URL}/admin.php?action=alipay_accounts&${params}`;
        console.log('loadAccounts API请求URL:', apiUrl);
        console.log('loadAccounts 请求头:', {
            'Authorization': `Bearer ${this.auth.getToken()}`
        });

        try {
            const response = await fetch(apiUrl, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            console.log('loadAccounts API响应状态:', response.status);
            const data = await response.json();
            console.log('loadAccounts API响应数据:', data);

            if (data.code === 200) {
                console.log('loadAccounts 数据加载成功，开始渲染...');
                this.renderAccountListNew(data.data.accounts);
                this.renderStatsNew(data.data.stats);
                this.renderPaginationNew(data.data.pagination);
                this.updateAccountCount(data.data.pagination.total_records);
            } else {
                console.error('loadAccounts API返回错误:', data);
                this.showError('加载账户失败: ' + (data.message || '未知错误'));
                // 显示错误状态
                const container = document.getElementById('accountsTableContainer');
                if (container) {
                    container.innerHTML = `
                        <div class="text-center p-4">
                            <div class="text-danger">
                                <i class="bi bi-exclamation-triangle" style="font-size: 3rem; opacity: 0.5;"></i>
                                <div class="mt-3">加载失败: ${data.message || '未知错误'}</div>
                                <button class="btn btn-outline-primary mt-2" onclick="window.accountManager.loadAccountsForCurrentUser()">
                                    <i class="bi bi-arrow-clockwise"></i> 重试
                                </button>
                            </div>
                        </div>
                    `;
                }
            }
        } catch (error) {
            console.error('Load accounts error:', error);
            this.showError('网络错误，请重试');
            // 显示错误状态
            const container = document.getElementById('accountsTableContainer');
            if (container) {
                container.innerHTML = `
                    <div class="text-center p-4">
                        <div class="text-danger">
                            <i class="bi bi-exclamation-triangle" style="font-size: 3rem; opacity: 0.5;"></i>
                            <div class="mt-3">网络错误，请重试</div>
                            <button class="btn btn-outline-primary mt-2" onclick="window.accountManager.loadAccountsForCurrentUser()">
                                <i class="bi bi-arrow-clockwise"></i> 重试
                            </button>
                        </div>
                    </div>
                `;
            }
        }
    }

    // 新的渲染方法适配新的API响应格式
    renderAccountListNew(accounts) {
        const container = document.getElementById('accountsTableContainer');
        if (!container) return;

        if (!accounts || accounts.length === 0) {
            container.innerHTML = `
                <div class="text-center p-5">
                    <div class="text-muted">
                        <i class="bi bi-inbox" style="font-size: 3rem; opacity: 0.5;"></i>
                        <div class="mt-3">暂无账户数据</div>
                        <small>请尝试调整筛选条件或添加新账户</small>
                    </div>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>账户名称</th>
                            <th>支付宝账号</th>
                            <th>实名</th>
                            <th>手机号</th>
                            <th>状态</th>
                            <th>日限额</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${accounts.map(account => `
                            <tr>
                                <td><span class="badge bg-secondary">${account.id}</span></td>
                                <td>
                                    <div class="fw-medium">${account.account_name || '-'}</div>
                                    ${account.provider_name ? `<small class="text-muted">${account.provider_name}</small>` : ''}
                                </td>
                                <td><code class="small">${account.account_number || '-'}</code></td>
                                <td>${account.real_name || '<span class="text-muted">-</span>'}</td>
                                <td>${account.phone || '<span class="text-muted">-</span>'}</td>
                                <td>${this.renderStatusBadge(account.status)}</td>
                                <td>
                                    <span class="fw-medium text-success">¥${parseFloat(account.daily_limit || 0).toLocaleString()}</span>
                                </td>
                                <td>
                                    <small class="text-muted">${this.formatDateTime(account.created_at)}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        ${this.renderActionButtonsNew(account)}
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    // 渲染状态徽章
    renderStatusBadge(status) {
        const statusConfig = {
            'pending': { class: 'bg-warning text-dark', text: '待审核' },
            'approved': { class: 'bg-success', text: '已审核' },
            'rejected': { class: 'bg-danger', text: '已拒绝' },
            'disabled': { class: 'bg-secondary', text: '已禁用' }
        };
        
        const config = statusConfig[status] || { class: 'bg-secondary', text: status };
        return `<span class="badge ${config.class}">${config.text}</span>`;
    }

    // 新的操作按钮渲染
    renderActionButtonsNew(account) {
        const user = this.auth.getUser();
        let buttons = '';

        // 编辑按钮
        buttons += `
            <button class="btn btn-outline-primary btn-sm" onclick="window.accountManager.editAccount(${account.id})" title="编辑" style="color: #3b82f6 !important;">
                <i class="bi bi-pencil"></i>
            </button>
        `;

        // 根据状态显示不同的操作按钮
        if (account.status === 'pending' && ['admin', 'provider'].includes(user?.user_type)) {
            buttons += `
                <button class="btn btn-outline-success btn-sm" onclick="window.accountManager.approveAccount(${account.id})" title="通过" style="color: #10b981 !important;">
                    <i class="bi bi-check-circle"></i>
                </button>
                <button class="btn btn-outline-warning btn-sm" onclick="window.accountManager.rejectAccount(${account.id})" title="拒绝" style="color: #f59e0b !important;">
                    <i class="bi bi-x-circle"></i>
                </button>
            `;
        } else if (account.status === 'approved') {
            buttons += `
                <button class="btn btn-outline-warning btn-sm" onclick="window.accountManager.disableAccount(${account.id})" title="禁用" style="color: #f59e0b !important;">
                    <i class="bi bi-pause-circle"></i>
                </button>
            `;
        } else if (account.status === 'disabled') {
            buttons += `
                <button class="btn btn-outline-success btn-sm" onclick="window.accountManager.enableAccount(${account.id})" title="启用" style="color: #10b981 !important;">
                    <i class="bi bi-play-circle"></i>
                </button>
            `;
        }

        // 删除按钮
        if (['admin', 'provider'].includes(user?.user_type)) {
            buttons += `
                <button class="btn btn-outline-danger btn-sm" onclick="window.accountManager.deleteAccount(${account.id})" title="删除" style="color: #ef4444 !important;">
                    <i class="bi bi-trash"></i>
                </button>
            `;
        }

        return buttons;
    }

    // 渲染统计信息
    renderStatsNew(stats) {
        if (!stats) return;

        document.getElementById('totalAccounts').textContent = stats.total_accounts || 0;
        document.getElementById('pendingAccounts').textContent = stats.pending_accounts || 0;
        document.getElementById('approvedAccounts').textContent = stats.approved_accounts || 0;
        document.getElementById('rejectedAccounts').textContent = stats.rejected_accounts || 0;
        document.getElementById('disabledAccounts').textContent = stats.disabled_accounts || 0;
        
        const totalLimit = stats.total_daily_limit || 0;
        document.getElementById('totalDailyLimit').textContent = '¥' + parseFloat(totalLimit).toLocaleString();
    }

    // 新的分页渲染方法
    renderPaginationNew(pagination) {
        const container = document.getElementById('paginationContainer');
        if (!container || !pagination || pagination.total_pages <= 1) {
            if (container) container.innerHTML = '';
            return;
        }

        const currentPage = pagination.current_page;
        const totalPages = pagination.total_pages;
        
        let paginationHtml = '<nav aria-label="账户列表分页"><ul class="pagination justify-content-center">';
        
        // 上一页
        if (currentPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.accountManager.loadAccountsForCurrentUser(${currentPage - 1})">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
            `;
        }

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.accountManager.loadAccountsForCurrentUser(1)">1</a>
                </li>
            `;
            if (startPage > 2) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="window.accountManager.loadAccountsForCurrentUser(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.accountManager.loadAccountsForCurrentUser(${totalPages})">${totalPages}</a>
                </li>
            `;
        }

        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.accountManager.loadAccountsForCurrentUser(${currentPage + 1})">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            `;
        }

        paginationHtml += '</ul></nav>';
        container.innerHTML = paginationHtml;
    }

    // 更新账户计数
    updateAccountCount(count) {
        const countElement = document.getElementById('accountCount');
        if (countElement) {
            countElement.textContent = count || 0;
        }
    }

    renderAccountList(accounts) {
        const tbody = document.getElementById('accountsTableBody');
        if (!tbody) return;

        if (accounts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="11" class="text-center">
                        <div class="py-5 text-muted">
                            <i class="bi bi-inbox" style="font-size: 3rem; opacity: 0.5;"></i>
                            <div class="mt-3">暂无账户数据</div>
                            <small>请尝试调整筛选条件或添加新账户</small>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = accounts.map(account => `
            <tr>
                <td><strong>${account.id}</strong></td>
                <td>
                    <div class="fw-medium">${account.account_name || '-'}</div>
                </td>
                <td>
                    <code class="small">${account.account_number || '-'}</code>
                </td>
                <td>${account.real_name || '<span class="text-muted">-</span>'}</td>
                <td>${account.phone || '<span class="text-muted">-</span>'}</td>
                <td>${account.email || '<span class="text-muted">-</span>'}</td>
                <td>
                    <small class="text-muted">${account.device_name || account.device_string_id || '-'}</small>
                </td>
                <td>
                    <span class="status-badge status-${account.status}">${this.getStatusText(account.status)}</span>
                </td>
                <td>
                    <span class="fw-medium text-success">¥${parseFloat(account.daily_limit || 0).toLocaleString()}</span>
                </td>
                <td>
                    <small class="text-muted">${this.formatDateTime(account.created_at)}</small>
                </td>
                <td>
                    <div class="btn-group-vertical btn-group-sm" role="group">
                        <button class="btn btn-outline-primary btn-action" onclick="window.accountManager.editAccount(${account.id})" title="编辑账户">
                            <i class="bi bi-pencil"></i>
                        </button>
                        ${this.renderActionButtons(account)}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderActionButtons(account) {
        let buttons = '';

        if (account.status === 'pending') {
            buttons += `
                <button class="btn btn-outline-success btn-action" onclick="window.accountManager.approveAccount(${account.id})" title="通过审核">
                    <i class="bi bi-check-circle"></i>
                </button>
                <button class="btn btn-outline-warning btn-action" onclick="window.accountManager.rejectAccount(${account.id})" title="拒绝审核">
                    <i class="bi bi-x-circle"></i>
                </button>
            `;
        } else if (account.status === 'approved') {
            buttons += `
                <button class="btn btn-outline-warning btn-action" onclick="window.accountManager.disableAccount(${account.id})" title="禁用账户">
                    <i class="bi bi-pause-circle"></i>
                </button>
            `;
        } else if (account.status === 'disabled') {
            buttons += `
                <button class="btn btn-outline-success btn-action" onclick="window.accountManager.enableAccount(${account.id})" title="启用账户">
                    <i class="bi bi-play-circle"></i>
                </button>
            `;
        }

        // 删除按钮
        buttons += `
            <button class="btn btn-outline-danger btn-action" onclick="window.accountManager.deleteAccount(${account.id})" title="删除账户">
                <i class="bi bi-trash"></i>
            </button>
        `;

        return buttons;
    }

    renderPagination(pagination) {
        const nav = document.getElementById('accountPagination');
        if (!nav || pagination.total_pages <= 1) {
            if (nav) nav.style.display = 'none';
            return;
        }
        
        nav.style.display = 'block';
        const ul = nav.querySelector('ul');
        
        let paginationHtml = '';
        
        // 上一页
        if (pagination.current_page > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.accountManager.loadAccounts(${pagination.current_page - 1})">
                        <i class="bi bi-chevron-left"></i> 上一页
                    </a>
                </li>
            `;
        }
        
        // 页码
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
        
        if (startPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.accountManager.loadAccounts(1)">1</a>
                </li>
            `;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="window.accountManager.loadAccounts(${i})">${i}</a>
                </li>
            `;
        }
        
        if (endPage < pagination.total_pages) {
            if (endPage < pagination.total_pages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.accountManager.loadAccounts(${pagination.total_pages})">${pagination.total_pages}</a>
                </li>
            `;
        }
        
        // 下一页
        if (pagination.current_page < pagination.total_pages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="window.accountManager.loadAccounts(${pagination.current_page + 1})">
                        下一页 <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            `;
        }
        
        ul.innerHTML = paginationHtml;
    }

    showCreateModal() {
        const modalTitle = document.getElementById('modalTitle');
        const accountForm = document.getElementById('accountForm');
        const accountId = document.getElementById('accountId');
        const deviceGroup = document.getElementById('deviceGroup');
        
        if (modalTitle) modalTitle.textContent = '新增账户';
        if (accountForm) accountForm.reset();
        if (accountId) accountId.value = '';
        if (deviceGroup) deviceGroup.style.display = 'block';
        
        // 设置默认值
        const dailyLimit = document.getElementById('dailyLimit');
        if (dailyLimit) dailyLimit.value = '50000.00';
        
        const modal = new bootstrap.Modal(document.getElementById('accountModal'));
        modal.show();
    }

    refreshAccounts() {
        this.loadAccounts(this.currentPage);
    }

    async editAccount(id) {
        // 获取账户详情
        const providerId = document.getElementById('providerId').value;
        
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin_accounts.php?provider_id=${providerId}`, {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const data = await response.json();

            if (data.code === 200) {
                const account = data.data.accounts.find(acc => acc.id === id);
                if (account) {
                    this.fillEditForm(account);
                }
            }
        } catch (error) {
            this.showError('获取账户信息失败');
        }
    }

    fillEditForm(account) {
        document.getElementById('modalTitle').textContent = '编辑账户';
        document.getElementById('accountId').value = account.id;
        document.getElementById('accountName').value = account.account_name || '';
        document.getElementById('accountNumber').value = account.account_number || '';
        document.getElementById('realName').value = account.real_name || '';
        document.getElementById('phone').value = account.phone || '';
        document.getElementById('email').value = account.email || '';
        document.getElementById('dailyLimit').value = account.daily_limit || '50000.00';
        document.getElementById('status').value = account.status || 'pending';
        document.getElementById('deviceGroup').style.display = 'none';
        
        const modal = new bootstrap.Modal(document.getElementById('accountModal'));
        modal.show();
    }

    async saveAccount() {
        const providerId = document.getElementById('providerId')?.value;
        const accountId = document.getElementById('accountId')?.value;
        
        // 表单验证
        const accountName = document.getElementById('accountName')?.value;
        const accountNumber = document.getElementById('accountNumber')?.value;
        
        if (!providerId) {
            this.showError('请先输入码商ID');
            return;
        }
        
        if (!accountName || !accountNumber) {
            this.showError('请填写账户名称和支付宝账号');
            return;
        }
        
        if (!accountId) {
            const deviceId = document.getElementById('deviceId')?.value;
            if (!deviceId) {
                this.showError('新增账户时设备ID为必填项');
                return;
            }
        }
        
        this.setSaveLoading(true);
        
        const data = {
            provider_id: parseInt(providerId),
            account_name: accountName,
            account_number: accountNumber,
            real_name: document.getElementById('realName')?.value || null,
            phone: document.getElementById('phone')?.value || null,
            email: document.getElementById('email')?.value || null,
            login_password: document.getElementById('loginPassword')?.value || null,
            payment_password: document.getElementById('paymentPassword')?.value || null,
            daily_limit: parseFloat(document.getElementById('dailyLimit')?.value || 50000),
            status: document.getElementById('status')?.value || 'pending'
        };
        
        if (accountId) {
            data.id = parseInt(accountId);
        } else {
            data.device_id = parseInt(document.getElementById('deviceId').value);
        }
        
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=accounts`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                this.showSuccess(result.message);
                this.closeModal();
                this.loadAccounts(this.currentPage);
            } else {
                this.showError('保存失败: ' + result.message);
            }
        } catch (error) {
            console.error('Save account error:', error);
            this.showError('保存失败: ' + error.message);
        }
        
        this.setSaveLoading(false);
    }

    setSaveLoading(loading) {
        const textSpan = document.querySelector('.save-btn-text');
        const loadingSpan = document.querySelector('.save-btn-loading');
        const button = textSpan?.closest('button');
        
        if (textSpan) textSpan.style.display = loading ? 'none' : 'inline';
        if (loadingSpan) loadingSpan.style.display = loading ? 'inline' : 'none';
        if (button) button.disabled = loading;
    }

    async performAction(accountId, action, actionName) {
        if (!confirm(`确定要${actionName}这个账户吗？`)) {
            return;
        }
        
        const providerId = document.getElementById('providerId').value;
        
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=accounts`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({
                    provider_id: parseInt(providerId),
                    id: accountId,
                    action: action
                })
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                this.showSuccess(result.message);
                this.loadAccounts(this.currentPage);
            } else {
                this.showError('操作失败: ' + result.message);
            }
        } catch (error) {
            this.showError('操作失败: ' + error.message);
        }
    }

    approveAccount(id) { this.performAction(id, 'approve', '通过'); }
    rejectAccount(id) { this.performAction(id, 'reject', '拒绝'); }
    disableAccount(id) { this.performAction(id, 'disable', '禁用'); }
    enableAccount(id) { this.performAction(id, 'enable', '启用'); }

    async deleteAccount(id) {
        if (!confirm('确定要删除这个账户吗？此操作不可恢复！')) {
            return;
        }
        
        const providerId = document.getElementById('providerId').value;
        
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=accounts`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({
                    provider_id: parseInt(providerId),
                    id: id
                })
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                this.showSuccess(result.message);
                this.loadAccounts(this.currentPage);
            } else {
                this.showError('删除失败: ' + result.message);
            }
        } catch (error) {
            this.showError('删除失败: ' + error.message);
        }
    }

    closeModal() {
        const modalElement = document.getElementById('accountModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }
        
        // 重置加载状态
        this.setSaveLoading(false);
    }

    getStatusText(status) {
        const statusMap = {
            'pending': '待审核',
            'approved': '已通过',
            'rejected': '已拒绝',
            'disabled': '已禁用'
        };
        return statusMap[status] || status;
    }

    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    showSuccess(message) {
        alert('✅ ' + message);
    }

    showError(message) {
        alert('❌ ' + message);
    }
}

if (typeof module !== "undefined" && module.exports) {
    module.exports = AccountManager;
} else if (typeof window !== "undefined") {
    window.AccountManager = AccountManager;
}
