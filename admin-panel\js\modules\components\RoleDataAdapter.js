/**
 * 角色数据适配器
 * 根据用户角色返回不同的数据和配置，实现界面共用+数据差异化
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-20
 */

class RoleDataAdapter {
    /**
     * 获取财务模块的角色配置
     * @param {string} role 用户角色
     * @returns {Object} 角色配置
     */
    static getFinanceConfig(role) {
        const configs = {
            system_admin: {
                title: '全系统财务概览',
                stats: [
                    { label: '总交易额', icon: 'bi-currency-dollar', color: 'primary' },
                    { label: '平台收益', icon: 'bi-graph-up', color: 'success' },
                    { label: '活跃平台', icon: 'bi-building', color: 'info' },
                    { label: '系统状态', icon: 'bi-shield-check', color: 'warning' }
                ],
                tabs: [
                    { id: 'overview', label: '财务概览', icon: 'bi-pie-chart' },
                    { id: 'platforms', label: '平台收益', icon: 'bi-building' },
                    { id: 'reports', label: '财务报表', icon: 'bi-file-earmark-text' },
                    { id: 'settings', label: '系统设置', icon: 'bi-gear' }
                ],
                permissions: ['view_all', 'export', 'settings']
            },
            platform_admin: {
                title: '平台财务管理',
                stats: [
                    { label: '平台收益', icon: 'bi-currency-dollar', color: 'primary' },
                    { label: '手续费收入', icon: 'bi-percent', color: 'success' },
                    { label: '活跃商户', icon: 'bi-people', color: 'info' },
                    { label: '今日交易', icon: 'bi-arrow-repeat', color: 'warning' }
                ],
                tabs: [
                    { id: 'overview', label: '财务概览', icon: 'bi-pie-chart' },
                    { id: 'merchants', label: '商户收益', icon: 'bi-shop' },
                    { id: 'fees', label: '手续费管理', icon: 'bi-percent' },
                    { id: 'settlement', label: '结算管理', icon: 'bi-credit-card' },
                    { id: 'reports', label: '财务报表', icon: 'bi-file-earmark-text' }
                ],
                permissions: ['view_platform', 'manage_fees', 'settlement', 'export']
            },
            provider: {
                title: '码商财务中心',
                stats: [
                    { label: '设备收益', icon: 'bi-currency-dollar', color: 'primary' },
                    { label: '今日流水', icon: 'bi-cash-stack', color: 'success' },
                    { label: '活跃设备', icon: 'bi-phone', color: 'info' },
                    { label: '待结算', icon: 'bi-clock', color: 'warning' }
                ],
                tabs: [
                    { id: 'overview', label: '收益概览', icon: 'bi-pie-chart' },
                    { id: 'devices', label: '设备收益', icon: 'bi-phone' },
                    { id: 'flow', label: '流水记录', icon: 'bi-list-ul' },
                    { id: 'settlement', label: '结算记录', icon: 'bi-credit-card' }
                ],
                permissions: ['view_own', 'export_own']
            },
            merchant: {
                title: '商户财务中心',
                stats: [
                    { label: '交易金额', icon: 'bi-currency-dollar', color: 'primary' },
                    { label: '今日收入', icon: 'bi-graph-up', color: 'success' },
                    { label: '交易笔数', icon: 'bi-receipt', color: 'info' },
                    { label: '账户余额', icon: 'bi-wallet2', color: 'warning' }
                ],
                tabs: [
                    { id: 'overview', label: '财务概览', icon: 'bi-pie-chart' },
                    { id: 'transactions', label: '交易流水', icon: 'bi-list-ul' },
                    { id: 'balance', label: '余额管理', icon: 'bi-wallet2' },
                    { id: 'reports', label: '财务报表', icon: 'bi-file-earmark-text' }
                ],
                permissions: ['view_own', 'export_own']
            }
        };
        
        return configs[role] || configs.merchant;
    }

    /**
     * 获取订单模块的角色配置
     * @param {string} role 用户角色
     * @returns {Object} 角色配置
     */
    static getOrderConfig(role) {
        const configs = {
            system_admin: {
                title: '全系统订单管理',
                stats: [
                    { label: '总订单数', icon: 'bi-receipt', color: 'primary' },
                    { label: '成功率', icon: 'bi-check-circle', color: 'success' },
                    { label: '活跃平台', icon: 'bi-building', color: 'info' },
                    { label: '异常订单', icon: 'bi-exclamation-triangle', color: 'danger' }
                ],
                tabs: [
                    { id: 'overview', label: '订单概览', icon: 'bi-bar-chart' },
                    { id: 'realtime', label: '实时监控', icon: 'bi-activity' },
                    { id: 'analysis', label: '数据分析', icon: 'bi-graph-up' },
                    { id: 'reports', label: '统计报表', icon: 'bi-file-earmark-text' }
                ],
                permissions: ['view_all', 'manage_all', 'export', 'analysis']
            },
            platform_admin: {
                title: '平台订单管理',
                stats: [
                    { label: '平台订单', icon: 'bi-receipt', color: 'primary' },
                    { label: '成功率', icon: 'bi-check-circle', color: 'success' },
                    { label: '商户数', icon: 'bi-people', color: 'info' },
                    { label: '处理中', icon: 'bi-clock', color: 'warning' }
                ],
                tabs: [
                    { id: 'overview', label: '订单概览', icon: 'bi-bar-chart' },
                    { id: 'merchants', label: '商户订单', icon: 'bi-shop' },
                    { id: 'process', label: '订单处理', icon: 'bi-gear' },
                    { id: 'analysis', label: '业务分析', icon: 'bi-graph-up' }
                ],
                permissions: ['view_platform', 'manage_platform', 'export', 'analysis']
            },
            provider: {
                title: '码商订单中心',
                stats: [
                    { label: '设备订单', icon: 'bi-receipt', color: 'primary' },
                    { label: '成功率', icon: 'bi-check-circle', color: 'success' },
                    { label: '活跃设备', icon: 'bi-phone', color: 'info' },
                    { label: '待处理', icon: 'bi-clock', color: 'warning' }
                ],
                tabs: [
                    { id: 'realtime', label: '实时订单', icon: 'bi-activity' },
                    { id: 'process', label: '订单处理', icon: 'bi-gear' },
                    { id: 'statistics', label: '订单统计', icon: 'bi-bar-chart' },
                    { id: 'export', label: '订单导出', icon: 'bi-download' }
                ],
                permissions: ['view_own', 'process_own', 'export_own']
            },
            merchant: {
                title: '我的订单管理',
                stats: [
                    { label: '总订单数', icon: 'bi-receipt', color: 'primary' },
                    { label: '成功率', icon: 'bi-check-circle', color: 'success' },
                    { label: '今日订单', icon: 'bi-calendar-day', color: 'info' },
                    { label: '待确认', icon: 'bi-clock', color: 'warning' }
                ],
                tabs: [
                    { id: 'orders', label: '我的订单', icon: 'bi-receipt' },
                    { id: 'records', label: '订单记录', icon: 'bi-list-ul' },
                    { id: 'analysis', label: '业务分析', icon: 'bi-graph-up' },
                    { id: 'statistics', label: '数据统计', icon: 'bi-bar-chart' }
                ],
                permissions: ['view_own', 'export_own']
            }
        };
        
        return configs[role] || configs.merchant;
    }

    /**
     * 获取API模块的角色配置
     * @param {string} role 用户角色
     * @returns {Object} 角色配置
     */
    static getApiConfig(role) {
        const configs = {
            merchant: {
                title: '商户API中心',
                stats: [
                    { label: 'API调用', icon: 'bi-cloud', color: 'primary' },
                    { label: '成功率', icon: 'bi-check-circle', color: 'success' },
                    { label: '应用数量', icon: 'bi-app', color: 'info' },
                    { label: '今日调用', icon: 'bi-calendar-day', color: 'warning' }
                ],
                tabs: [
                    { id: 'keys', label: 'API密钥', icon: 'bi-key' },
                    { id: 'settings', label: 'API设置', icon: 'bi-gear' },
                    { id: 'test', label: '接口测试', icon: 'bi-play-circle' },
                    { id: 'generator', label: '代码生成器', icon: 'bi-code-slash' },
                    { id: 'signature', label: '签名工具', icon: 'bi-shield-lock' },
                    { id: 'docs', label: 'API文档', icon: 'bi-book' },
                    { id: 'support', label: '开发支持', icon: 'bi-question-circle' }
                ],
                permissions: ['view_own', 'manage_keys', 'test', 'generate']
            }
        };
        
        return configs[role] || configs.merchant;
    }

    /**
     * 根据角色获取API端点映射
     * @param {string} role 用户角色
     * @param {string} module 模块名称
     * @returns {Object} API端点配置
     */
    static getApiEndpoints(role, module) {
        const endpoints = {
            finance: {
                system_admin: {
                    stats: '/api/admin/finance/global-stats',
                    data: '/api/admin/finance/global-data',
                    export: '/api/admin/finance/export'
                },
                platform_admin: {
                    stats: '/api/platform/finance/stats',
                    data: '/api/platform/finance/data',
                    export: '/api/platform/finance/export'
                },
                provider: {
                    stats: '/api/provider/finance/stats',
                    data: '/api/provider/finance/data',
                    export: '/api/provider/finance/export'
                },
                merchant: {
                    stats: '/api/merchant/finance/stats',
                    data: '/api/merchant/finance/data',
                    export: '/api/merchant/finance/export'
                }
            },
            order: {
                system_admin: {
                    stats: '/api/admin/orders/global-stats',
                    data: '/api/admin/orders/global-data',
                    export: '/api/admin/orders/export'
                },
                platform_admin: {
                    stats: '/api/platform/orders/stats',
                    data: '/api/platform/orders/data',
                    export: '/api/platform/orders/export'
                },
                provider: {
                    stats: '/api/provider/orders/stats',
                    data: '/api/provider/orders/data',
                    export: '/api/provider/orders/export'
                },
                merchant: {
                    stats: '/api/merchant/orders/stats',
                    data: '/api/merchant/orders/data',
                    export: '/api/merchant/orders/export'
                }
            }
        };
        
        return endpoints[module]?.[role] || {};
    }
}

// 导出适配器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RoleDataAdapter;
}
if (typeof window !== 'undefined') {
    window.RoleDataAdapter = RoleDataAdapter;
}
