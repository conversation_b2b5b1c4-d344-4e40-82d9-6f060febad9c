/**
 * 性能监控管理器
 * 从admin.js第11946-12960行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class PerformanceManager {
    constructor() {
        this.auth = new AuthManager();
        this.currentView = 'overview';
        this.refreshInterval = null;
        this.charts = {
        };
    }
    // 显示性能监控界面
    showPerformanceMonitor() {
        const content = `
        <div class="performance-monitor">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4 class="mb-1">
        <i class="bi bi-speedometer2 me-2"></i>性能监控
        </h4>
        <p class="text-muted mb-0">系统性能实时监控和分析</p>
        </div>
        <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="window.performanceManager.exportPerformanceReport()">
        <i class="bi bi-download me-2"></i>导出报告
        </button>
        <button class="btn btn-outline-secondary" onclick="window.performanceManager.refreshData()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
        </button>
        </div>
        </div>
        <!-- 性能概览卡片 -->
        <div class="row mb-4" id="performanceOverviewCards">
        <!-- 动态加载 -->
        </div>
        <!-- 导航标签 -->
        <ul class="nav nav-tabs mb-4" role="tablist">
        <li class="nav-item">
        <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#overview-tab" onclick="window.performanceManager.switchView('overview')">
        <i class="bi bi-graph-up me-2"></i>性能概览
        </button>
        </li>
        <li class="nav-item">
        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#system-tab" onclick="window.performanceManager.switchView('system')">
        <i class="bi bi-cpu me-2"></i>系统统计
        </button>
        </li>
        <li class="nav-item">
        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#database-tab" onclick="window.performanceManager.switchView('database')">
        <i class="bi bi-database me-2"></i>数据库性能
        </button>
        </li>
        <li class="nav-item">
        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#api-tab" onclick="window.performanceManager.switchView('api')">
        <i class="bi bi-cloud me-2"></i>API性能
        </button>
        </li>
        <li class="nav-item">
        <button class="nav-link" data-bs-toggle="tab" data-bs-target="#cache-tab" onclick="window.performanceManager.switchView('cache')">
        <i class="bi bi-lightning me-2"></i>缓存统计
        </button>
        </li>
        </ul>
        <!-- 标签页内容 -->
        <div class="tab-content">
        <div class="tab-pane fade show active" id="overview-tab">
        <div id="overviewContent">
        <!-- 动态加载 -->
        </div>
        </div>
        <div class="tab-pane fade" id="system-tab">
        <div id="systemContent">
        <!-- 动态加载 -->
        </div>
        </div>
        <div class="tab-pane fade" id="database-tab">
        <div id="databaseContent">
        <!-- 动态加载 -->
        </div>
        </div>
        <div class="tab-pane fade" id="api-tab">
        <div id="apiContent">
        <!-- 动态加载 -->
        </div>
        </div>
        <div class="tab-pane fade" id="cache-tab">
        <div id="cacheContent">
        <!-- 动态加载 -->
        </div>
        </div>
        </div>
        </div>
        `;
        document.getElementById('mainContent').innerHTML = content;
        this.loadPerformanceOverview();
        this.startAutoRefresh();
    }
    // 切换视图
    switchView(view) {
        this.currentView = view;
        switch (view) {
            case 'overview':
            this.loadPerformanceOverview();
            break;
            case 'system':
            this.loadSystemStats();
            break;
            case 'database':
            this.loadDatabasePerformance();
            break;
            case 'api':
            this.loadApiPerformance();
            break;
            case 'cache':
            this.loadCacheStats();
            break;
        }
    }
    // 加载性能概览
    async loadPerformanceOverview() {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=performance_monitor`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderOverviewCards(result.data);
                this.renderOverviewCharts(result.data);
            } else {
                throw new Error(result.message || '加载性能数据失败');
            }
        } catch (error) {
            console.error('加载性能概览失败:',
            error);
            this.showError('加载性能概览失败: ' + error.message);
        }
    }
    // 渲染概览卡片
    renderOverviewCards(data) {
        const cardsContainer = document.getElementById('performanceOverviewCards');
        if (!cardsContainer) return;
        const cards = [
        {
            title: '性能评分',
            value: data.performance_score,
            unit: '分',
            icon: 'speedometer2',
            color: data.performance_score >= 80 ? 'success' : data.performance_score >= 60 ? 'warning' : 'danger'
        },
        {
            title: '数据库连接',
            value: data.database_info.connections,
            unit: '/' + data.database_info.max_connections,
            icon: 'database',
            color: 'info'
        },
        {
            title: '内存使用',
            value: data.system_info.current_memory_usage,
            unit: '',
            icon: 'memory',
            color: 'primary'
        },
        {
            title: '今日API调用',
            value: data.api_stats.today_calls,
            unit: '次',
            icon: 'cloud',
            color: 'success'
        },
        {
            title: '缓存命中率',
            value: data.cache_stats.hit_rate,
            unit: '%',
            icon: 'lightning',
            color: 'warning'
        },
        {
            title: '慢查询数',
            value: data.slow_query_count,
            unit: '个',
            icon: 'exclamation-triangle',
            color: data.slow_query_count > 10 ? 'danger' : 'success'
        }
        ];
        const cardsHtml = cards.map(card => `
        <div class="col-md-4 col-lg-2 mb-3">
        <div class="card h-100 border-0 shadow-sm">
        <div class="card-body text-center">
        <div class="d-flex align-items-center justify-content-center mb-2">
        <i class="bi bi-${
            card.icon
        } text-${
            card.color
        }" style="font-size: 2rem;
        "></i>
        </div>
        <h6 class="card-title text-muted mb-1">${
            card.title
        }</h6>
        <h4 class="card-text text-${
            card.color
        } mb-0">
        ${
            card.value
        }<small class="text-muted">${
            card.unit
        }</small>
        </h4>
        </div>
        </div>
        </div>
        `).join('');
        cardsContainer.innerHTML = cardsHtml;
    }
    // 渲染概览图表
    renderOverviewCharts(data) {
        const overviewContent = document.getElementById('overviewContent');
        if (!overviewContent) return;
        const chartsHtml = `
        <div class="row">
        <div class="col-md-6 mb-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-info-circle me-2"></i>系统信息
        </h6>
        </div>
        <div class="card-body">
        <div class="row">
        <div class="col-6">
        <small class="text-muted">PHP版本</small>
        <div class="fw-bold">${
            data.system_info.php_version
        }</div>
        </div>
        <div class="col-6">
        <small class="text-muted">MySQL版本</small>
        <div class="fw-bold">${
            data.database_info.version
        }</div>
        </div>
        <div class="col-6 mt-3">
        <small class="text-muted">内存限制</small>
        <div class="fw-bold">${
            data.system_info.memory_limit
        }</div>
        </div>
        <div class="col-6 mt-3">
        <small class="text-muted">峰值内存</small>
        <div class="fw-bold">${
            data.system_info.peak_memory_usage
        }</div>
        </div>
        <div class="col-12 mt-3">
        <small class="text-muted">服务器时间</small>
        <div class="fw-bold">${
            data.system_info.server_time
        }</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-6 mb-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-graph-up me-2"></i>API统计
        </h6>
        </div>
        <div class="card-body">
        <div class="row">
        <div class="col-6">
        <small class="text-muted">今日调用</small>
        <div class="fw-bold text-primary">${
            data.api_stats.today_calls
        }</div>
        </div>
        <div class="col-6">
        <small class="text-muted">昨日调用</small>
        <div class="fw-bold">${
            data.api_stats.yesterday_calls
        }</div>
        </div>
        <div class="col-6 mt-3">
        <small class="text-muted">本周调用</small>
        <div class="fw-bold">${
            data.api_stats.this_week_calls
        }</div>
        </div>
        <div class="col-6 mt-3">
        <small class="text-muted">本月调用</small>
        <div class="fw-bold">${
            data.api_stats.this_month_calls
        }</div>
        </div>
        <div class="col-6 mt-3">
        <small class="text-muted">平均响应时间</small>
        <div class="fw-bold">${
            data.api_stats.avg_response_time
        }ms</div>
        </div>
        <div class="col-6 mt-3">
        <small class="text-muted">错误率</small>
        <div class="fw-bold ${
            data.api_stats.error_rate > 5 ? 'text-danger' : 'text-success'
        }">${
            data.api_stats.error_rate
        }%</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
        overviewContent.innerHTML = chartsHtml;
    }
    // 加载系统统计
    async loadSystemStats() {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=performance_monitor&sub_action=system_stats`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderSystemStats(result.data);
            } else {
                throw new Error(result.message || '加载系统统计失败');
            }
        } catch (error) {
            console.error('加载系统统计失败:',
            error);
            this.showError('加载系统统计失败: ' + error.message);
        }
    }
    // 渲染系统统计
    renderSystemStats(data) {
        const systemContent = document.getElementById('systemContent');
        if (!systemContent) return;
        const tableStatsHtml = data.table_stats.map(table => `
        <tr>
        <td>${
            table.display_name
        }</td>
        <td>${
            table.table_name
        }</td>
        <td class="text-end">${
            table.record_count.toLocaleString()
        }</td>
        </tr>
        `).join('');
        const todayStatsHtml = data.today_stats.map(table => `
        <tr>
        <td>${
            table.display_name
        }</td>
        <td class="text-end">${
            table.today_count.toLocaleString()
        }</td>
        </tr>
        `).join('');
        const html = `
        <div class="row">
        <div class="col-md-6 mb-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-table me-2"></i>数据表统计
        </h6>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-sm">
        <thead>
        <tr>
        <th>表名</th>
        <th>英文名</th>
        <th class="text-end">记录数</th>
        </tr>
        </thead>
        <tbody>
        ${
            tableStatsHtml
        }
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-6 mb-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-calendar-day me-2"></i>今日新增统计
        </h6>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-sm">
        <thead>
        <tr>
        <th>表名</th>
        <th class="text-end">今日新增</th>
        </tr>
        </thead>
        <tbody>
        ${
            todayStatsHtml
        }
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        <div class="col-12 mb-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-hdd me-2"></i>磁盘使用情况
        </h6>
        </div>
        <div class="card-body">
        <div class="row">
        <div class="col-md-3">
        <small class="text-muted">总空间</small>
        <div class="fw-bold">${
            data.disk_info.total_space
        }</div>
        </div>
        <div class="col-md-3">
        <small class="text-muted">已使用</small>
        <div class="fw-bold">${
            data.disk_info.used_space
        }</div>
        </div>
        <div class="col-md-3">
        <small class="text-muted">可用空间</small>
        <div class="fw-bold">${
            data.disk_info.free_space
        }</div>
        </div>
        <div class="col-md-3">
        <small class="text-muted">使用率</small>
        <div class="fw-bold ${
            data.disk_info.usage_percentage > 80 ? 'text-danger' : 'text-success'
        }">${
            data.disk_info.usage_percentage
        }%</div>
        </div>
        </div>
        <div class="progress mt-3">
        <div class="progress-bar ${
            data.disk_info.usage_percentage > 80 ? 'bg-danger' : 'bg-success'
        }"
        style="width: ${
            data.disk_info.usage_percentage
        }%"></div>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
        systemContent.innerHTML = html;
    }
    // 加载数据库性能
    async loadDatabasePerformance() {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=performance_monitor&sub_action=database_performance`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderDatabasePerformance(result.data);
            } else {
                throw new Error(result.message || '加载数据库性能失败');
            }
        } catch (error) {
            console.error('加载数据库性能失败:',
            error);
            this.showError('加载数据库性能失败: ' + error.message);
        }
    }
    // 渲染数据库性能
    renderDatabasePerformance(data) {
        const databaseContent = document.getElementById('databaseContent');
        if (!databaseContent) return;
        const statusHtml = data.database_status.map(status => `
        <tr>
        <td>${
            status.description
        }</td>
        <td>${
            status.variable
        }</td>
        <td class="text-end">${
            status.value
        }</td>
        </tr>
        `).join('');
        const tableSizesHtml = data.table_sizes.map(table => `
        <tr>
        <td>${
            table.table_name
        }</td>
        <td class="text-end">${
            table.size_mb
        } MB</td>
        <td class="text-end">${
            table.table_rows ? table.table_rows.toLocaleString() : '0'
        }</td>
        <td class="text-end">${
            table.data_mb
        } MB</td>
        <td class="text-end">${
            table.index_mb
        } MB</td>
        </tr>
        `).join('');
        const html = `
        <div class="row">
        <div class="col-md-6 mb-4">
        <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0">
        <i class="bi bi-database me-2"></i>数据库状态
        </h6>
        <button class="btn btn-sm btn-outline-primary" onclick="window.performanceManager.optimizeDatabase()">
        <i class="bi bi-gear me-1"></i>优化数据库
        </button>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-sm">
        <thead>
        <tr>
        <th>项目</th>
        <th>变量名</th>
        <th class="text-end">值</th>
        </tr>
        </thead>
        <tbody>
        ${
            statusHtml
        }
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-6 mb-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-speedometer me-2"></i>性能指标
        </h6>
        </div>
        <div class="card-body">
        <div class="row">
        <div class="col-12 mb-3">
        <small class="text-muted">每秒查询数</small>
        <div class="fw-bold text-primary">${
            data.performance_metrics.queries_per_second
        }</div>
        </div>
        <div class="col-12 mb-3">
        <small class="text-muted">慢查询比例</small>
        <div class="fw-bold ${
            data.performance_metrics.slow_query_percentage > 1 ? 'text-danger' : 'text-success'
        }">${
            data.performance_metrics.slow_query_percentage
        }%</div>
        </div>
        <div class="col-12">
        <small class="text-muted">平均查询时间</small>
        <div class="fw-bold">${
            data.performance_metrics.avg_query_time
        }ms</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-12">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-table me-2"></i>表大小统计
        </h6>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-sm">
        <thead>
        <tr>
        <th>表名</th>
        <th class="text-end">总大小</th>
        <th class="text-end">记录数</th>
        <th class="text-end">数据大小</th>
        <th class="text-end">索引大小</th>
        </tr>
        </thead>
        <tbody>
        ${
            tableSizesHtml
        }
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
        databaseContent.innerHTML = html;
    }
    // 加载API性能
    async loadApiPerformance() {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=performance_monitor&sub_action=api_performance`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderApiPerformance(result.data);
            } else {
                throw new Error(result.message || '加载API性能失败');
            }
        } catch (error) {
            console.error('加载API性能失败:',
            error);
            this.showError('加载API性能失败: ' + error.message);
        }
    }
    // 渲染API性能
    renderApiPerformance(data) {
        const apiContent = document.getElementById('apiContent');
        if (!apiContent) return;
        const endpointsHtml = data.api_endpoints.map(endpoint => `
        <tr>
        <td>
        <code>${
            endpoint.endpoint
        }</code>
        <br><small class="text-muted">${
            endpoint.method
        }</small>
        </td>
        <td class="text-end">${
            endpoint.avg_response_time
        }ms</td>
        <td class="text-end">${
            endpoint.total_calls.toLocaleString()
        }</td>
        <td class="text-end">${
            endpoint.last_24h_calls
        }</td>
        <td class="text-end">
        <span class="badge ${
            endpoint.error_rate > 5 ? 'bg-danger' : endpoint.error_rate > 2 ? 'bg-warning' : 'bg-success'
        }">
        ${
            endpoint.error_rate
        }%
        </span>
        </td>
        </tr>
        `).join('');
        const errorTypesHtml = Object.entries(data.error_stats.error_types).map(([type,
        count]) => `
        <tr>
        <td>${
            type
        }</td>
        <td class="text-end">${
            count
        }</td>
        </tr>
        `).join('');
        const html = `
        <div class="row">
        <div class="col-12 mb-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-cloud me-2"></i>API接口性能统计
        </h6>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-sm">
        <thead>
        <tr>
        <th>接口</th>
        <th class="text-end">平均响应时间</th>
        <th class="text-end">总调用次数</th>
        <th class="text-end">24小时调用</th>
        <th class="text-end">错误率</th>
        </tr>
        </thead>
        <tbody>
        ${
            endpointsHtml
        }
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-6 mb-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-clock me-2"></i>响应时间分布
        </h6>
        </div>
        <div class="card-body">
        ${
            Object.entries(data.response_time_distribution).map(([range,
            percentage]) => `
            <div class="d-flex justify-content-between align-items-center mb-2">
            <span>${
                range
            }</span>
            <span class="fw-bold">${
                percentage
            }%</span>
            </div>
            <div class="progress mb-3" style="height: 8px;
            ">
            <div class="progress-bar" style="width: ${
                percentage
            }%"></div>
            </div>
            `).join('')
        }
        </div>
        </div>
        </div>
        <div class="col-md-6 mb-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-exclamation-triangle me-2"></i>错误统计
        </h6>
        </div>
        <div class="card-body">
        <div class="mb-3">
        <small class="text-muted">总错误数</small>
        <div class="fw-bold text-danger">${
            data.error_stats.total_errors
        }</div>
        </div>
        <div class="table-responsive">
        <table class="table table-sm">
        <thead>
        <tr>
        <th>错误类型</th>
        <th class="text-end">次数</th>
        </tr>
        </thead>
        <tbody>
        ${
            errorTypesHtml
        }
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
        apiContent.innerHTML = html;
    }
    // 加载缓存统计
    async loadCacheStats() {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=performance_monitor&sub_action=cache_stats`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.renderCacheStats(result.data);
            } else {
                throw new Error(result.message || '加载缓存统计失败');
            }
        } catch (error) {
            console.error('加载缓存统计失败:',
            error);
            this.showError('加载缓存统计失败: ' + error.message);
        }
    }
    // 渲染缓存统计
    renderCacheStats(data) {
        const cacheContent = document.getElementById('cacheContent');
        if (!cacheContent) return;
        const categoriesHtml = data.cache_categories.map(category => `
        <tr>
        <td>${
            category.category
        }</td>
        <td class="text-end">${
            category.key_count
        }</td>
        <td class="text-end">${
            category.memory_usage
        }</td>
        <td class="text-end">
        <span class="badge ${
            category.hit_rate > 90 ? 'bg-success' : category.hit_rate > 80 ? 'bg-warning' : 'bg-danger'
        }">
        ${
            category.hit_rate
        }%
        </span>
        </td>
        </tr>
        `).join('');
        const topKeysHtml = data.top_keys.map(key => `
        <tr>
        <td><code>${
            key.key
        }</code></td>
        <td class="text-end">${
            key.hits
        }</td>
        <td class="text-end">${
            key.size
        }</td>
        <td class="text-end">${
            key.ttl
        }s</td>
        </tr>
        `).join('');
        const html = `
        <div class="row">
        <div class="col-md-6 mb-4">
        <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
        <h6 class="mb-0">
        <i class="bi bi-lightning me-2"></i>缓存概览
        </h6>
        <button class="btn btn-sm btn-outline-danger" onclick="window.performanceManager.clearCache()">
        <i class="bi bi-trash me-1"></i>清理缓存
        </button>
        </div>
        <div class="card-body">
        <div class="row">
        <div class="col-6 mb-3">
        <small class="text-muted">总键数</small>
        <div class="fw-bold">${
            data.cache_stats.total_keys
        }</div>
        </div>
        <div class="col-6 mb-3">
        <small class="text-muted">内存使用</small>
        <div class="fw-bold">${
            data.cache_stats.memory_usage
        }</div>
        </div>
        <div class="col-6 mb-3">
        <small class="text-muted">命中率</small>
        <div class="fw-bold text-success">${
            data.cache_stats.hit_rate
        }%</div>
        </div>
        <div class="col-6 mb-3">
        <small class="text-muted">未命中率</small>
        <div class="fw-bold text-warning">${
            data.cache_stats.miss_rate
        }%</div>
        </div>
        <div class="col-6">
        <small class="text-muted">过期键数</small>
        <div class="fw-bold">${
            data.cache_stats.expired_keys
        }</div>
        </div>
        <div class="col-6">
        <small class="text-muted">淘汰键数</small>
        <div class="fw-bold">${
            data.cache_stats.evicted_keys
        }</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-6 mb-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-pie-chart me-2"></i>缓存分类统计
        </h6>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-sm">
        <thead>
        <tr>
        <th>分类</th>
        <th class="text-end">键数</th>
        <th class="text-end">内存</th>
        <th class="text-end">命中率</th>
        </tr>
        </thead>
        <tbody>
        ${
            categoriesHtml
        }
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        <div class="col-12">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-star me-2"></i>热门缓存键
        </h6>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-sm">
        <thead>
        <tr>
        <th>键名</th>
        <th class="text-end">命中次数</th>
        <th class="text-end">大小</th>
        <th class="text-end">TTL</th>
        </tr>
        </thead>
        <tbody>
        ${
            topKeysHtml
        }
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
        cacheContent.innerHTML = html;
    }
    // 优化数据库
    async optimizeDatabase() {
        if (!confirm('确定要优化数据库吗？此操作可能需要一些时间。')) {
            return;
        }
        try {
            this.showInfo('正在优化数据库，请稍候...');
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=performance_monitor&sub_action=optimize_database`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('数据库优化完成');
                this.loadDatabasePerformance();
                // 刷新数据库性能数据
            } else {
                throw new Error(result.message || '数据库优化失败');
            }
        } catch (error) {
            console.error('数据库优化失败:',
            error);
            this.showError('数据库优化失败: ' + error.message);
        }
    }
    // 清理缓存
    async clearCache() {
        if (!confirm('确定要清理所有缓存吗？此操作将清空所有缓存数据。')) {
            return;
        }
        try {
            this.showInfo('正在清理缓存，请稍候...');
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=performance_monitor&sub_action=clear_cache`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const result = await response.json();
            if (result.code === 200) {
                this.showSuccess('缓存清理完成');
                this.loadCacheStats();
                // 刷新缓存统计数据
            } else {
                throw new Error(result.message || '缓存清理失败');
            }
        } catch (error) {
            console.error('缓存清理失败:',
            error);
            this.showError('缓存清理失败: ' + error.message);
        }
    }
    // 导出性能报告
    exportPerformanceReport() {
        const url = `${
            CONFIG.API_BASE_URL
        }/admin.php?action=performance_monitor&sub_action=export_performance_report`;
        const link = document.createElement('a');
        link.href = url;
        link.download = `performance_report_${
            new Date().toISOString().slice(0, 10)
        }.csv`;
        link.click();
    }
    // 刷新数据
    refreshData() {
        switch (this.currentView) {
            case 'overview':
            this.loadPerformanceOverview();
            break;
            case 'system':
            this.loadSystemStats();
            break;
            case 'database':
            this.loadDatabasePerformance();
            break;
            case 'api':
            this.loadApiPerformance();
            break;
            case 'cache':
            this.loadCacheStats();
            break;
        }
        this.showSuccess('数据已刷新');
    }
    // 开始自动刷新
    startAutoRefresh() {
        // 清除之前的定时器
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        // 每30秒自动刷新概览数据
        this.refreshInterval = setInterval(() => {
            if (this.currentView === 'overview') {
                this.loadPerformanceOverview();
            }
        }, 30000);
    }
    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    // 显示成功消息
    showSuccess(message) {
        const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px;
        right: 20px;
        z-index: 9999;
        ">
        <i class="bi bi-check-circle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        `;
        document.body.insertAdjacentHTML('beforeend',
        alertHtml);
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 3000);
    }
    // 显示错误消息
    showError(message) {
        const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show position-fixed" style="top: 20px;
        right: 20px;
        z-index: 9999;
        ">
        <i class="bi bi-exclamation-triangle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        `;
        document.body.insertAdjacentHTML('beforeend',
        alertHtml);
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }
    // 显示信息消息
    showInfo(message) {
        const alertHtml = `
        <div class="alert alert-info alert-dismissible fade show position-fixed" style="top: 20px;
        right: 20px;
        z-index: 9999;
        ">
        <i class="bi bi-info-circle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        `;
        document.body.insertAdjacentHTML('beforeend',
        alertHtml);
        setTimeout(() => {
            const alert = document.querySelector('.alert-info');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 3000);
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = PerformanceManager;
} else if (typeof window !== "undefined") {
    window.PerformanceManager = PerformanceManager;
}

console.log('📦 PerformanceManager 模块加载完成');
