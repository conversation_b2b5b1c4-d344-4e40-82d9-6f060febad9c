/**
 * 商户API密钥管理模块
 * 从admin.js第19765-20393行提取
 * 负责商户API密钥的创建、管理、权限控制等功能
 */
// 商户API密钥管理类
class MerchantApiKeyManager {
    constructor() {
        this.auth = new AuthManager();
        this.keyInfo = null;
        this.callStats = null;
    }

    async initialize() {
        try {
            await this.loadKeyInfo();
            await this.loadCallStats();
            this.renderApiKeyManagement();
        } catch (error) {
            console.error('初始化API密钥管理失败:', error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }

    async loadKeyInfo() {
        try {
            const response = await fetch('/api/merchant/tools.php?action=get_key_info', {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.keyInfo = data.data || data;
                return true;
            } else {
                this.showError('加载密钥信息失败: ' + (data.message || '未知错误'));
                return false;
            }
        } catch (error) {
            console.error('加载密钥信息失败:', error);
            this.showError('网络错误，请重试');
            return false;
        }
    }

    async loadCallStats() {
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=get_call_stats', {
                headers: {
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.callStats = data.data || data;
                return true;
            } else {
                // 调用统计可能为空，不显示错误
                this.callStats = {
                    today_calls: 0,
                    month_calls: 0,
                    total_calls: 0,
                    success_rate: 0
                };
                return true;
            }
        } catch (error) {
            console.error('加载调用统计失败:', error);
            this.callStats = {
                today_calls: 0,
                month_calls: 0,
                total_calls: 0,
                success_rate: 0
            };
            return true;
        }
    }

    renderApiKeyManagement() {
        const container = document.getElementById('apiKeyManagementContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="row">
                <!-- 左侧：密钥信息 -->
                <div class="col-md-8">
                    <!-- 基本信息卡片 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>基本信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">开发者ID</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" value="${this.keyInfo?.developer_id || 'loading...'}" readonly>
                                            <button class="btn btn-outline-secondary" onclick="window.merchantApiKeyManager.copyToClipboard('${this.keyInfo?.developer_id || ''}')">
                                                <i class="bi bi-clipboard"></i>
                                            </button>
                                        </div>
                                        <small class="text-muted">用于API调用时标识您的身份</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">创建时间</label>
                                        <input type="text" class="form-control" value="${this.keyInfo?.created_at || 'loading...'}" readonly>
                                        <small class="text-muted">密钥首次生成时间</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">API密钥</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="apiKeyField" value="${this.keyInfo?.api_key || 'loading...'}" readonly>
                                    <button class="btn btn-outline-secondary" onclick="window.merchantApiKeyManager.toggleKeyVisibility()" id="toggleKeyBtn">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="window.merchantApiKeyManager.copyApiKey()">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                                <small class="text-muted">请妥善保管您的API密钥，不要在客户端代码中暴露</small>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="bi bi-exclamation-triangle me-2"></i>安全提醒</h6>
                                <ul class="mb-0 small">
                                    <li>API密钥用于签名验证，请勿泄露给第三方</li>
                                    <li>建议定期更换API密钥以确保安全</li>
                                    <li>如果密钥泄露，请立即重新生成</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- IP白名单卡片 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="bi bi-shield-check me-2"></i>IP白名单</h6>
                                <button class="btn btn-outline-primary btn-sm" onclick="window.merchantApiKeyManager.addIpWhitelist()">
                                    <i class="bi bi-plus me-1"></i>添加IP
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="ipWhitelistContainer">
                                ${this.renderIpWhitelist()}
                            </div>
                        </div>
                    </div>
                    
                    <!-- 回调配置卡片 -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-arrow-left-right me-2"></i>回调配置</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">回调地址</label>
                                <div class="input-group">
                                    <input type="url" class="form-control" id="callbackUrl" value="${this.keyInfo?.callback_url || ''}" placeholder="https://your-domain.com/callback">
                                    <button class="btn btn-outline-primary" onclick="window.merchantApiKeyManager.updateCallbackUrl()">
                                        <i class="bi bi-check"></i>
                                    </button>
                                </div>
                                <small class="text-muted">订单状态变化时将向此地址发送通知</small>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">回调验证</label>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-info btn-sm" onclick="window.merchantApiKeyManager.testCallback()">
                                        <i class="bi bi-play me-1"></i>测试回调
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="window.merchantApiKeyManager.viewCallbackLogs()">
                                        <i class="bi bi-list me-1"></i>回调日志
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧：统计信息 -->
                <div class="col-md-4">
                    <!-- 调用统计卡片 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-graph-up me-2"></i>调用统计</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="stat-item">
                                        <div class="stat-number text-primary">${this.callStats?.today_calls || 0}</div>
                                        <div class="stat-label">今日调用</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-item">
                                        <div class="stat-number text-success">${this.callStats?.month_calls || 0}</div>
                                        <div class="stat-label">本月调用</div>
                                    </div>
                                </div>
                                <div class="col-6 mt-3">
                                    <div class="stat-item">
                                        <div class="stat-number text-info">${this.callStats?.total_calls || 0}</div>
                                        <div class="stat-label">总调用次数</div>
                                    </div>
                                </div>
                                <div class="col-6 mt-3">
                                    <div class="stat-item">
                                        <div class="stat-number text-warning">${this.callStats?.success_rate || 0}%</div>
                                        <div class="stat-label">成功率</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <canvas id="apiCallsChart" height="150"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 接口权限卡片 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-key me-2"></i>接口权限</h6>
                        </div>
                        <div class="card-body">
                            <div class="permission-list">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="perm_create_order" ${this.keyInfo?.permissions?.create_order ? 'checked' : ''}>
                                    <label class="form-check-label" for="perm_create_order">
                                        <i class="bi bi-plus-circle me-1"></i>创建订单
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="perm_query_order" ${this.keyInfo?.permissions?.query_order ? 'checked' : ''}>
                                    <label class="form-check-label" for="perm_query_order">
                                        <i class="bi bi-search me-1"></i>查询订单
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="perm_close_order" ${this.keyInfo?.permissions?.close_order ? 'checked' : ''}>
                                    <label class="form-check-label" for="perm_close_order">
                                        <i class="bi bi-x-circle me-1"></i>关闭订单
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="perm_refund" ${this.keyInfo?.permissions?.refund ? 'checked' : ''}>
                                    <label class="form-check-label" for="perm_refund">
                                        <i class="bi bi-arrow-counterclockwise me-1"></i>申请退款
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="perm_balance" ${this.keyInfo?.permissions?.balance ? 'checked' : ''}>
                                    <label class="form-check-label" for="perm_balance">
                                        <i class="bi bi-wallet me-1"></i>查询余额
                                    </label>
                                </div>
                            </div>
                            <button class="btn btn-outline-primary btn-sm w-100 mt-2" onclick="window.merchantApiKeyManager.updatePermissions()">
                                <i class="bi bi-check me-1"></i>更新权限
                            </button>
                        </div>
                    </div>
                    
                    <!-- 快速操作卡片 -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-lightning me-2"></i>快速操作</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="window.merchantApiKeyManager.openApiTester()">
                                    <i class="bi bi-play me-1"></i>接口测试
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="window.merchantApiKeyManager.openCodeGenerator()">
                                    <i class="bi bi-code me-1"></i>代码生成
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="window.merchantApiKeyManager.openApiDocs()">
                                    <i class="bi bi-book me-1"></i>API文档
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="window.merchantApiKeyManager.downloadPostman()">
                                    <i class="bi bi-download me-1"></i>Postman集合
                                </button>
                                <hr class="my-2">
                                <button class="btn btn-outline-warning btn-sm" onclick="window.merchantApiKeyManager.regenerateKey()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>重新生成密钥
                                </button>
                            </div>
                        </div>
                    </div>
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary">${this.callStats?.today_calls || 0}</h4>
                                        <small class="text-muted">今日调用</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success">${this.callStats?.month_calls || 0}</h4>
                                    <small class="text-muted">本月调用</small>
                                </div>
                            </div>
                            <hr>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-info">${this.callStats?.total_calls || 0}</h4>
                                        <small class="text-muted">总调用次数</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-warning">${this.callStats?.success_rate || 0}%</h4>
                                    <small class="text-muted">成功率</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 快速操作卡片 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-lightning me-2"></i>快速操作</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="window.merchantApiKeyManager.openCodeGenerator()">
                                    <i class="bi bi-code-square me-1"></i>代码生成器
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="window.merchantApiKeyManager.openSignatureTool()">
                                    <i class="bi bi-shield-check me-1"></i>签名工具
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="window.merchantApiKeyManager.openApiDocs()">
                                    <i class="bi bi-book me-1"></i>API文档
                                </button>
                                <button class="btn btn-outline-success btn-sm" onclick="window.merchantApiKeyManager.downloadPostman()">
                                    <i class="bi bi-download me-1"></i>Postman集合
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 安全状态卡片 -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-shield-exclamation me-2"></i>安全状态</h6>
                        </div>
                        <div class="card-body">
                            <div class="security-item mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>密钥强度</span>
                                    <span class="badge bg-success">强</span>
                                </div>
                            </div>
                            <div class="security-item mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>IP白名单</span>
                                    <span class="badge ${this.keyInfo?.ip_whitelist?.length > 0 ? 'bg-success' : 'bg-warning'}">
                                        ${this.keyInfo?.ip_whitelist?.length > 0 ? '已配置' : '未配置'}
                                    </span>
                                </div>
                            </div>
                            <div class="security-item mb-2">
                                <div class="d-flex justify-content-between">
                                    <span>回调地址</span>
                                    <span class="badge ${this.keyInfo?.callback_url ? 'bg-success' : 'bg-warning'}">
                                        ${this.keyInfo?.callback_url ? '已配置' : '未配置'}
                                    </span>
                                </div>
                            </div>
                            <div class="security-item">
                                <div class="d-flex justify-content-between">
                                    <span>最后更新</span>
                                    <small class="text-muted">${this.keyInfo?.updated_at || '未知'}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderIpWhitelist() {
        const ips = this.keyInfo?.ip_whitelist || [];
        
        if (ips.length === 0) {
            return `
                <div class="text-center py-3 text-muted">
                    <i class="bi bi-shield-x" style="font-size: 2rem;"></i>
                    <p class="mt-2">未配置IP白名单</p>
                    <small>建议配置IP白名单以提高安全性</small>
                </div>
            `;
        }
        
        return ips.map((ip, index) => `
            <div class="ip-item d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                <div>
                    <code>${ip}</code>
                    <small class="text-muted ms-2">允许访问</small>
                </div>
                <button class="btn btn-outline-danger btn-sm" onclick="window.merchantApiKeyManager.removeIpWhitelist(${index})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `).join('');
    }

    toggleKeyVisibility() {
        const keyField = document.getElementById('apiKeyField');
        const toggleBtn = document.getElementById('toggleKeyBtn');
        
        if (keyField.type === 'password') {
            keyField.type = 'text';
            toggleBtn.innerHTML = '<i class="bi bi-eye-slash"></i>';
        } else {
            keyField.type = 'password';
            toggleBtn.innerHTML = '<i class="bi bi-eye"></i>';
        }
    }

    copyApiKey() {
        const keyField = document.getElementById('apiKeyField');
        this.copyToClipboard(keyField.value);
    }

    copyToClipboard(text) {
        if (!text || text === 'loading...') {
            this.showError('没有可复制的内容');
            return;
        }

        navigator.clipboard.writeText(text).then(() => {
            this.showSuccess('已复制到剪贴板');
        }).catch(err => {
            console.error('复制失败:', err);
            this.showError('复制失败，请手动复制');
        });
    }

    async regenerateKey() {
        if (!confirm('重新生成API密钥将使现有密钥失效，所有使用旧密钥的调用都将失败。确定要继续吗？')) {
            return;
        }

        try {
            const response = await fetch('/api/merchant/tools.php?action=regenerate_api_key', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const data = await response.json();
            
            if (data.code === 200 || data.success) {
                this.showSuccess('API密钥已重新生成！');
                await this.refreshData();
            } else {
                this.showError('重新生成密钥失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('重新生成密钥失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    async addIpWhitelist() {
        const ip = prompt('请输入要添加的IP地址:');
        if (!ip) return;

        // 简单的IP格式验证
        const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
        if (!ipRegex.test(ip)) {
            this.showError('请输入有效的IP地址');
            return;
        }

        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=add_ip_whitelist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({ ip: ip })
            });

            const data = await response.json();
            
            if (data.code === 200 || data.success) {
                this.showSuccess('IP地址已添加到白名单');
                await this.refreshData();
            } else {
                this.showError('添加IP白名单失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('添加IP白名单失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    async removeIpWhitelist(index) {
        if (!confirm('确定要移除这个IP地址吗？')) {
            return;
        }

        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=remove_ip_whitelist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({ index: index })
            });

            const data = await response.json();
            
            if (data.code === 200 || data.success) {
                this.showSuccess('IP地址已从白名单移除');
                await this.refreshData();
            } else {
                this.showError('移除IP白名单失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('移除IP白名单失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    async updateCallbackUrl() {
        const callbackUrl = document.getElementById('callbackUrl').value;
        
        if (callbackUrl && !callbackUrl.startsWith('https://')) {
            this.showError('回调地址必须使用HTTPS协议');
            return;
        }

        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=update_callback_url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({ callback_url: callbackUrl })
            });

            const data = await response.json();
            
            if (data.code === 200 || data.success) {
                this.showSuccess('回调地址已更新');
                await this.refreshData();
            } else {
                this.showError('更新回调地址失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('更新回调地址失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    async testCallback() {
        const callbackUrl = this.keyInfo?.callback_url;
        if (!callbackUrl) {
            this.showError('请先配置回调地址');
            return;
        }

        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=test_callback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                }
            });

            const data = await response.json();
            
            if (data.code === 200 || data.success) {
                this.showSuccess('回调测试已发送，请检查您的服务器日志');
            } else {
                this.showError('回调测试失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('回调测试失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    viewCallbackLogs() {
        // 这里可以打开回调日志查看页面
        this.showInfo('回调日志功能开发中...');
    }

    downloadPostman() {
        // 生成Postman集合文件
        const postmanCollection = {
            "info": {
                "name": "PayPal支付系统API",
                "description": "PayPal支付系统API接口集合",
                "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
            },
            "item": [
                {
                    "name": "获取收款页面",
                    "request": {
                        "method": "POST",
                        "header": [],
                        "body": {
                            "mode": "urlencoded",
                            "urlencoded": [
                                {"key": "amount", "value": "100", "type": "text"},
                                {"key": "type", "value": "2", "type": "text"},
                                {"key": "product_id", "value": "1", "type": "text"},
                                {"key": "order_no", "value": "ORDER{{$timestamp}}", "type": "text"},
                                {"key": "developer_id", "value": this.keyInfo?.developer_id || "your_developer_id", "type": "text"},
                                {"key": "timestamp", "value": "{{$timestamp}}", "type": "text"},
                                {"key": "sign", "value": "{{signature}}", "type": "text"}
                            ]
                        },
                        "url": {
                            "raw": "{{base_url}}/api.php?do=pay_get_qrcode",
                            "host": ["{{base_url}}"],
                            "path": ["", "api.php"],
                            "query": [{"key": "do", "value": "pay_get_qrcode"}]
                        }
                    }
                }
            ],
            "variable": [
                {
                    "key": "base_url",
                    "value": "https://your-domain.com"
                }
            ]
        };

        const blob = new Blob([JSON.stringify(postmanCollection, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'paypal-api-postman-collection.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess('Postman集合已下载');
    }

    // 快速操作导航
    openCodeGenerator() {
        const codeGenMenuItem = document.querySelector('[data-page="code-generator"]');
        if (codeGenMenuItem) {
            codeGenMenuItem.click();
        }
    }

    openSignatureTool() {
        const signatureMenuItem = document.querySelector('[data-page="signature-tool"]');
        if (signatureMenuItem) {
            signatureMenuItem.click();
        }
    }

    openApiDocs() {
        const apiDocsMenuItem = document.querySelector('[data-page="api-docs"]');
        if (apiDocsMenuItem) {
            apiDocsMenuItem.click();
        }
    }

    async refreshData() {
        const container = document.getElementById('apiKeyManagementContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">刷新中...</span>
                    </div>
                    <div class="mt-3">正在刷新数据...</div>
                </div>
            `;
        }
        
        await this.loadKeyInfo();
        await this.loadCallStats();
        this.renderApiKeyManagement();
        this.showSuccess('数据已刷新');
    }

    showSuccess(message) {
        console.log('Success:', message);
        // 这里可以集成toast通知组件
    }

    showError(message) {
        console.error('Error:', message);
        // 这里可以集成toast通知组件
        
        const container = document.getElementById('apiKeyManagementContainer');
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    ${message}
                    <button class="btn btn-outline-danger btn-sm ms-3" onclick="window.merchantApiKeyManager.refreshData()">
                        重试
                    </button>
                </div>
            `;
        }
    }

    showInfo(message) {
        console.log('Info:', message);
        // 这里可以集成toast通知组件
    }

    // 新增功能方法
    async updatePermissions() {
        try {
            const permissions = {
                create_order: document.getElementById('perm_create_order').checked,
                query_order: document.getElementById('perm_query_order').checked,
                close_order: document.getElementById('perm_close_order').checked,
                refund: document.getElementById('perm_refund').checked,
                balance: document.getElementById('perm_balance').checked
            };

            const response = await fetch('/api/merchant/index.php?module=tools&action=update_permissions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({ permissions })
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('权限更新成功');
                await this.loadKeyInfo();
            } else {
                this.showError('权限更新失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('更新权限失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    openApiTester() {
        // 创建API测试器模态框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-play me-2"></i>API接口测试器
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">请求配置</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">接口类型</label>
                                            <select class="form-select" id="apiMethod">
                                                <option value="create_order">创建订单</option>
                                                <option value="query_order">查询订单</option>
                                                <option value="close_order">关闭订单</option>
                                                <option value="query_balance">查询余额</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">请求参数</label>
                                            <textarea class="form-control" id="apiParams" rows="10" placeholder="JSON格式参数"></textarea>
                                        </div>
                                        <button class="btn btn-primary w-100" onclick="window.merchantApiKeyManager.executeApiTest()">
                                            <i class="bi bi-play me-2"></i>发送请求
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">响应结果</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="apiTestResult" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                                            <div class="text-muted text-center">
                                                <i class="bi bi-play-circle" style="font-size: 2rem;"></i>
                                                <div class="mt-2">点击"发送请求"开始测试</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // 模态框关闭时清理DOM
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    async executeApiTest() {
        try {
            const method = document.getElementById('apiMethod').value;
            const paramsText = document.getElementById('apiParams').value;
            let params = {};
            
            if (paramsText.trim()) {
                try {
                    params = JSON.parse(paramsText);
                } catch (e) {
                    this.showError('参数格式错误，请使用有效的JSON格式');
                    return;
                }
            }

            // 显示加载状态
            const resultContainer = document.getElementById('apiTestResult');
            resultContainer.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border" role="status"></div>
                    <div class="mt-2">正在发送请求...</div>
                </div>
            `;

            const response = await fetch('/api/merchant/index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.auth.getToken()}`
                },
                body: JSON.stringify({
                    module: 'api_test',
                    action: method,
                    ...params
                })
            });

            const result = await response.json();
            
            // 显示结果
            resultContainer.innerHTML = `
                <div class="mb-3">
                    <strong>状态码:</strong> 
                    <span class="badge bg-${result.code === 200 ? 'success' : 'danger'}">${result.code}</span>
                </div>
                <div class="mb-3">
                    <strong>响应时间:</strong> ${Date.now() - performance.now()}ms
                </div>
                <div class="mb-3">
                    <strong>响应数据:</strong>
                </div>
                <pre style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; font-size: 12px;">${JSON.stringify(result, null, 2)}</pre>
            `;

        } catch (error) {
            console.error('API测试失败:', error);
            document.getElementById('apiTestResult').innerHTML = `
                <div class="alert alert-danger">
                    <strong>请求失败:</strong> ${error.message}
                </div>
            `;
        }
    }

    initializeCharts() {
        // 初始化API调用统计图表
        const ctx = document.getElementById('apiCallsChart');
        if (ctx && this.callStats && typeof Chart !== 'undefined') {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['7天前', '6天前', '5天前', '4天前', '3天前', '2天前', '昨天', '今天'],
                    datasets: [{
                        label: 'API调用次数',
                        data: this.callStats.daily_calls || [10, 15, 12, 18, 22, 16, 25, this.callStats.today_calls],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = MerchantApiKeyManager;
} else if (typeof window !== "undefined") {
    window.MerchantApiKeyManager = MerchantApiKeyManager;
} 