<?php
/**
 * 租户数据过滤测试脚本
 * 测试新的数据库级别自动过滤机制
 */

require_once dirname(__FILE__) . '/config/database.php';
require_once dirname(__FILE__) . '/utils/TenantAuth.php';

echo "=== 租户数据过滤测试 ===\n\n";

// 获取数据库实例
$db = Database::getInstance();

echo "1. 测试无租户上下文的查询（应该返回所有数据）\n";
$merchants = $db->fetchAll("SELECT id, merchant_code, platform_id FROM merchants LIMIT 5");
echo "查询结果数量: " . count($merchants) . "\n";
foreach ($merchants as $merchant) {
    echo "  - ID: {$merchant['id']}, 商户代码: {$merchant['merchant_code']}, 平台ID: {$merchant['platform_id']}\n";
}
echo "\n";

echo "2. 设置平台管理员租户上下文（platform_id=1）\n";
$tenantContext = array(
    'tenant_type' => 'platform_admin',
    'tenant_id' => 1,
    'platform_id' => 1,
    'user_id' => 1,
    'domain' => 'platform1.top005.com'
);
$db->setTenantContext($tenantContext);

echo "查询商户（应该只返回platform_id=1的数据）\n";
$merchants = $db->fetchAll("SELECT id, merchant_code, platform_id FROM merchants LIMIT 5");
echo "查询结果数量: " . count($merchants) . "\n";
foreach ($merchants as $merchant) {
    echo "  - ID: {$merchant['id']}, 商户代码: {$merchant['merchant_code']}, 平台ID: {$merchant['platform_id']}\n";
}
echo "\n";

echo "3. 测试JOIN查询（应该自动过滤所有相关表）\n";
$results = $db->fetchAll("
    SELECT m.id, m.merchant_code, m.platform_id, u.username 
    FROM merchants m 
    JOIN users u ON m.user_id = u.id 
    LIMIT 3
");
echo "查询结果数量: " . count($results) . "\n";
foreach ($results as $result) {
    echo "  - ID: {$result['id']}, 商户代码: {$result['merchant_code']}, 平台ID: {$result['platform_id']}, 用户名: {$result['username']}\n";
}
echo "\n";

echo "4. 测试码商查询\n";
$providers = $db->fetchAll("SELECT id, provider_code, platform_id FROM payment_providers LIMIT 3");
echo "查询结果数量: " . count($providers) . "\n";
foreach ($providers as $provider) {
    echo "  - ID: {$provider['id']}, 码商代码: {$provider['provider_code']}, 平台ID: {$provider['platform_id']}\n";
}
echo "\n";

echo "5. 测试系统管理员上下文（应该看到所有数据）\n";
$systemAdminContext = array(
    'tenant_type' => 'system_admin',
    'tenant_id' => 0,
    'platform_id' => null,
    'user_id' => 0,
    'domain' => 'admin.top005.com'
);
$db->setTenantContext($systemAdminContext);

$merchants = $db->fetchAll("SELECT id, merchant_code, platform_id FROM merchants LIMIT 5");
echo "查询结果数量: " . count($merchants) . "\n";
foreach ($merchants as $merchant) {
    echo "  - ID: {$merchant['id']}, 商户代码: {$merchant['merchant_code']}, 平台ID: {$merchant['platform_id']}\n";
}
echo "\n";

echo "6. 测试商户上下文（merchant_id=1）\n";
$merchantContext = array(
    'tenant_type' => 'merchant',
    'tenant_id' => 1,
    'platform_id' => 1,
    'user_id' => 2,
    'domain' => 'merchant1.top005.com'
);
$db->setTenantContext($merchantContext);

$merchants = $db->fetchAll("SELECT id, merchant_code, platform_id FROM merchants");
echo "查询结果数量: " . count($merchants) . "\n";
foreach ($merchants as $merchant) {
    echo "  - ID: {$merchant['id']}, 商户代码: {$merchant['merchant_code']}, 平台ID: {$merchant['platform_id']}\n";
}
echo "\n";

echo "7. 测试白名单表（users表应该不被过滤）\n";
$users = $db->fetchAll("SELECT id, username, user_type FROM users LIMIT 3");
echo "查询结果数量: " . count($users) . "\n";
foreach ($users as $user) {
    echo "  - ID: {$user['id']}, 用户名: {$user['username']}, 类型: {$user['user_type']}\n";
}
echo "\n";

echo "8. 测试withoutTenantFilter方法\n";
$allMerchants = $db->withoutTenantFilter(function($db) {
    return $db->fetchAll("SELECT id, merchant_code, platform_id FROM merchants LIMIT 5");
});
echo "查询结果数量: " . count($allMerchants) . "\n";
foreach ($allMerchants as $merchant) {
    echo "  - ID: {$merchant['id']}, 商户代码: {$merchant['merchant_code']}, 平台ID: {$merchant['platform_id']}\n";
}
echo "\n";

echo "9. 测试数据库实例信息\n";
$info = $db->getInstanceInfo();
echo "实例信息: " . json_encode($info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

echo "\n=== 测试完成 ===\n";
?> 