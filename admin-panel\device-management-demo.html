<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理系统 - PayPal管理后台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/device-modules.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fc;
            font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Helvetica Neue', Arial, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        
        .main-container {
            padding: 2rem 0;
        }
        
        .welcome-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 0.25rem 1.5rem rgba(102, 126, 234, 0.25);
        }
        
        .quick-stats {
            margin-bottom: 2rem;
        }
        
        .quick-stats .card {
            border: none;
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        
        .quick-stats .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-overlay.hidden {
            display: none;
        }
        
        .footer {
            background-color: #2c3e50;
            color: white;
            text-align: center;
            padding: 1rem 0;
            margin-top: 3rem;
        }
    </style>
</head>
<body>
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-3 text-muted">正在加载设备管理系统...</p>
        </div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-shield-check me-2"></i>
                PayPal 设备管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="index.html">
                            <i class="bi bi-house-door me-1"></i>主控台
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white active" href="#">
                            <i class="bi bi-phone me-1"></i>设备管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container-fluid main-container">
        <!-- 欢迎横幅 -->
        <div class="welcome-banner">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">
                        <i class="bi bi-phone me-3"></i>
                        设备管理中心
                    </h2>
                    <p class="mb-0 opacity-75">
                        统一管理PayPal系统的移动设备，包括设备认证、小组分配、签到监控等功能
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <div class="me-3">
                            <div class="fs-4 fw-bold" id="currentTime">--:--:--</div>
                            <div class="small opacity-75" id="currentDate">加载中...</div>
                        </div>
                        <i class="bi bi-clock-history fs-1 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速统计 -->
        <div class="row quick-stats">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card bg-primary bg-opacity-10 border-primary">
                    <div class="card-body text-center">
                        <div class="display-6 fw-bold text-primary" id="quickStatsTotalDevices">
                            <i class="bi bi-hourglass-split"></i>
                        </div>
                        <div class="text-muted">总设备数</div>
                        <small class="text-primary">实时统计</small>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card bg-success bg-opacity-10 border-success">
                    <div class="card-body text-center">
                        <div class="display-6 fw-bold text-success" id="quickStatsActiveDevices">
                            <i class="bi bi-hourglass-split"></i>
                        </div>
                        <div class="text-muted">活跃设备</div>
                        <small class="text-success">已激活</small>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card bg-warning bg-opacity-10 border-warning">
                    <div class="card-body text-center">
                        <div class="display-6 fw-bold text-warning" id="quickStatsPendingDevices">
                            <i class="bi bi-hourglass-split"></i>
                        </div>
                        <div class="text-muted">待审核</div>
                        <small class="text-warning">需处理</small>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card bg-info bg-opacity-10 border-info">
                    <div class="card-body text-center">
                        <div class="display-6 fw-bold text-info" id="quickStatsGroups">
                            <i class="bi bi-hourglass-split"></i>
                        </div>
                        <div class="text-muted">小组数量</div>
                        <small class="text-info">已创建</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备管理模块容器 -->
        <div id="deviceModuleContainer" class="fade-in">
            <!-- 设备管理模块将在这里加载 -->
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p class="mb-0">
                &copy; 2024 PayPal设备管理系统. 
                <span class="mx-2">|</span>
                版本 1.0.0
                <span class="mx-2">|</span>
                <small>基于Bootstrap 5构建</small>
            </p>
        </div>
    </footer>

    <!-- 全局消息提示容器 -->
    <div id="globalMessageContainer" 
         style="position: fixed; top: 80px; right: 20px; z-index: 1050; max-width: 350px;">
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 设备管理模块 -->
    <script type="module">
        import DeviceModuleManager from './js/modules/device-module-manager.js';
        
        // 全局变量
        window.deviceModuleManager = new DeviceModuleManager();
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 初始化时间显示
                updateTimeDisplay();
                setInterval(updateTimeDisplay, 1000);
                
                // 加载快速统计数据
                await loadQuickStats();
                
                // 初始化设备管理模块
                await deviceModuleManager.init();
                
                // 隐藏加载遮罩
                hideLoadingOverlay();
                
                console.log('设备管理系统初始化完成');
                showGlobalMessage('系统初始化完成', 'success');
                
            } catch (error) {
                console.error('系统初始化失败:', error);
                showGlobalMessage('系统初始化失败: ' + error.message, 'danger');
                hideLoadingOverlay();
            }
        });
        
        /**
         * 更新时间显示
         */
        function updateTimeDisplay() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN');
            const dateString = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });
            
            document.getElementById('currentTime').textContent = timeString;
            document.getElementById('currentDate').textContent = dateString;
        }
        
        /**
         * 加载快速统计数据
         */
        async function loadQuickStats() {
            try {
                // 模拟API调用
                const stats = await mockApiCall();
                
                // 使用动画更新统计数字
                animateCountUp('quickStatsTotalDevices', stats.totalDevices);
                animateCountUp('quickStatsActiveDevices', stats.activeDevices);
                animateCountUp('quickStatsPendingDevices', stats.pendingDevices);
                animateCountUp('quickStatsGroups', stats.totalGroups);
                
            } catch (error) {
                console.error('加载快速统计失败:', error);
                // 显示错误状态
                document.getElementById('quickStatsTotalDevices').innerHTML = '<i class="bi bi-exclamation-triangle"></i>';
                document.getElementById('quickStatsActiveDevices').innerHTML = '<i class="bi bi-exclamation-triangle"></i>';
                document.getElementById('quickStatsPendingDevices').innerHTML = '<i class="bi bi-exclamation-triangle"></i>';
                document.getElementById('quickStatsGroups').innerHTML = '<i class="bi bi-exclamation-triangle"></i>';
            }
        }
        
        /**
         * 模拟API调用（实际项目中替换为真实API）
         */
        async function mockApiCall() {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve({
                        totalDevices: 156,
                        activeDevices: 142,
                        pendingDevices: 8,
                        totalGroups: 12
                    });
                }, 1000);
            });
        }
        
        /**
         * 数字动画效果
         */
        function animateCountUp(elementId, targetValue) {
            const element = document.getElementById(elementId);
            const duration = 1500; // 动画持续时间
            const startTime = Date.now();
            const startValue = 0;
            
            function updateCount() {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // 使用缓动函数
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = Math.floor(easeOutQuart * targetValue);
                
                element.textContent = currentValue;
                
                if (progress < 1) {
                    requestAnimationFrame(updateCount);
                } else {
                    element.textContent = targetValue;
                }
            }
            
            updateCount();
        }
        
        /**
         * 隐藏加载遮罩
         */
        function hideLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            overlay.classList.add('hidden');
        }
        
        /**
         * 显示全局消息
         */
        function showGlobalMessage(message, type = 'info', duration = 3000) {
            const container = document.getElementById('globalMessageContainer');
            const messageId = 'msg_' + Date.now();
            
            const messageHtml = `
                <div id="${messageId}" class="alert alert-${type} alert-dismissible fade show slide-in" role="alert">
                    <i class="bi bi-${getIconForType(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', messageHtml);
            
            // 自动移除消息
            setTimeout(() => {
                const messageElement = document.getElementById(messageId);
                if (messageElement) {
                    messageElement.remove();
                }
            }, duration);
        }
        
        /**
         * 根据消息类型获取图标
         */
        function getIconForType(type) {
            const icons = {
                success: 'check-circle',
                danger: 'exclamation-triangle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        }
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
            showGlobalMessage('系统发生错误，请刷新页面重试', 'danger', 5000);
        });
        
        // 导出全局函数供其他脚本使用
        window.showGlobalMessage = showGlobalMessage;
        window.loadQuickStats = loadQuickStats;
        
    </script>

    <!-- 开发环境调试工具 -->
    <script>
        // 开发环境下的调试功能
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('🔧 开发模式已启用');
            
            // 添加调试快捷键
            document.addEventListener('keydown', function(e) {
                // Ctrl + Shift + D 打开调试信息
                if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                    console.log('📊 当前模块状态:', {
                        currentModule: deviceModuleManager.currentModule,
                        modules: Object.keys(deviceModuleManager.modules),
                        container: deviceModuleManager.moduleContainer
                    });
                }
                
                // Ctrl + Shift + R 重新加载当前模块
                if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                    console.log('🔄 重新加载当前模块');
                    deviceModuleManager.refreshCurrentModule();
                }
            });
            
            // 性能监控
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'navigation') {
                        console.log('📈 页面加载性能:', {
                            DNS查询: entry.domainLookupEnd - entry.domainLookupStart,
                            TCP连接: entry.connectEnd - entry.connectStart,
                            请求响应: entry.responseEnd - entry.requestStart,
                            DOM解析: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
                            总加载时间: entry.loadEventEnd - entry.navigationStart
                        });
                    }
                }
            });
            observer.observe({entryTypes: ['navigation']});
        }
    </script>
</body>
</html> 