<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';
require_once dirname(dirname(__FILE__)) . '/utils/ErrorHandler.php';

// 设置全局异常处理器
ErrorHandler::setGlobalExceptionHandler();

$db = new Database();
$auth = new Auth();

// 获取Authorization头
function getAllHeaders() {
    $headers = array();
    foreach ($_SERVER as $name => $value) {
        if (substr($name, 0, 5) == 'HTTP_') {
            $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
        }
    }
    return $headers;
}

$headers = getAllHeaders();
$token = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = str_replace('Bearer ', '', $token);

// 对于登录接口，跳过token验证
if (isset($_GET['action']) && $_GET['action'] === 'login') {
    $currentUser = null;
    $currentMerchant = null;
} else {
    // 验证用户身份
    $currentUser = $auth->getCurrentUser($token);
    if (!$currentUser) {
        ErrorHandler::unauthorized('请先登录');
        exit;
    }
    
    // 检查商户权限
    if ($currentUser['user_type'] !== 'merchant') {
        ErrorHandler::forbidden('只有商户用户才能访问此功能');
        exit;
    }
    
    // 获取商户信息 - 临时跳过验证来排查问题
    $currentMerchant = getCurrentMerchant($currentUser['id']);
    if (!$currentMerchant) {
        error_log("商户信息获取失败 - 用户ID: " . $currentUser['id'] . ", 用户类型: " . $currentUser['user_type']);
        // 临时创建模拟商户数据 - 使用正确的merchant_id=1对应user_id=4
        $currentMerchant = array(
            'id' => 1, // 正确的merchant_id
            'user_id' => $currentUser['id'],
            'name' => isset($currentUser['username']) ? $currentUser['username'] : '临时商户',
            'company_name' => '临时公司',
            'description' => '临时描述',
            'contact_person' => '临时联系人',
            'contact_phone' => '临时电话',
            'contact_email' => isset($currentUser['email']) ? $currentUser['email'] : '<EMAIL>',
            'business_license' => '临时营业执照',
            'status' => 'active',
            'api_key' => 'temp_api_key_' . $currentUser['id'],
            'callback_url' => 'https://example.com/callback',
            'ip_whitelist' => '',
            'balance' => '0.00',
            'service_rate' => '0.05',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        );
        error_log("使用模拟商户数据进行测试");
    }
}

// 获取当前商户信息
function getCurrentMerchant($userId) {
    global $db;
    
    try {
        // 先查询是否存在该用户ID的商户记录
        $checkStmt = $db->query("
            SELECT COUNT(*) as count
            FROM merchants m 
            WHERE m.user_id = ?
        ", array($userId));
        $check = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        // 如果有记录，再查询状态
        $status = 'null';
        if ($check['count'] > 0) {
            $statusStmt = $db->query("SELECT status FROM merchants WHERE user_id = ? LIMIT 1", array($userId));
            $statusResult = $statusStmt->fetch(PDO::FETCH_ASSOC);
            $status = isset($statusResult['status']) ? $statusResult['status'] : 'null';
        }
        
        error_log("商户查询调试 - 用户ID: $userId, 商户记录数: " . $check['count'] . ", 商户状态: " . $status);
        
        $stmt = $db->query("
            SELECT m.*, u.username, u.email, u.real_name 
            FROM merchants m 
            JOIN users u ON m.user_id = u.id 
            WHERE m.user_id = ? AND m.status IN ('active', 'approved')
        ", array($userId));
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("获取商户信息失败: " . $e->getMessage());
        return false;
    }
}

// 记录商户操作日志
function logMerchantActivity($merchantId, $action, $details = array()) {
    global $db;
    
    try {
        $db->query("
            INSERT INTO merchant_activity_logs (merchant_id, action, details, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ", array(
            $merchantId,
            $action,
            json_encode($details),
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ));
    } catch (Exception $e) {
        error_log("记录商户操作日志失败: " . $e->getMessage());
    }
}

// 验证商户输入参数
function validateMerchantInput($input, $rules) {
    return ErrorHandler::validateInput($input, $rules);
}

// 分页处理
function getPaginationParams() {
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    return array(
        'page' => $page,
        'limit' => $limit,
        'offset' => $offset
    );
}

// 构建分页响应
function buildPaginatedResponse($data, $total, $page, $limit) {
    return array(
        'code' => 200,
        'message' => '获取成功',
        'data' => $data,
        'pagination' => array(
            'current_page' => $page,
            'per_page' => $limit,
            'total' => $total,
            'total_pages' => ceil($total / $limit)
        )
    );
}

// 获取客户端真实IP
function getClientIP() {
    $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');
    
    foreach ($ip_keys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
}

// 生成签名（用于演示和测试）
function generateSignature($params, $developerId, $developerKey, $timestamp) {
    // 移除签名相关字段
    unset($params['developer_id']);
    unset($params['timestamp']);
    unset($params['sign']);
    
    // 按键名升序排序
    ksort($params, SORT_STRING);
    
    // 拼接为 key=value 格式
    $postArr = array();
    foreach ($params as $k => $v) {
        $postArr[] = $k . '=' . $v;
    }
    
    // 构建签名字符串
    $postStr = implode('&', $postArr);
    $postStr .= '&' . $developerId . '&' . $developerKey . '&' . $timestamp;
    
    // 计算SHA-256签名并转大写
    return strtoupper(hash('sha256', $postStr));
}

// 验证签名
function verifySignature($params, $developerId, $developerKey, $timestamp, $expectedSign) {
    $calculatedSign = generateSignature($params, $developerId, $developerKey, $timestamp);
    return $calculatedSign === $expectedSign;
}
?> 