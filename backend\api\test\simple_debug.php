<?php
echo "PHP is working\n";
echo "PHP version: " . phpversion() . "\n";

// 测试数据库连接 - 使用实际配置
try {
    $host = '**************';
    $dbname = 'payment_system';
    $username = 'root';
    $password = 'yak19XTwyDx.8';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    echo "Database connection: SUCCESS\n";
    
    // 测试查询
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM script_versions");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    echo "Script count: $count\n";
    
    // 获取一些示例数据
    $stmt = $pdo->prepare("SELECT id, script_name, app_type FROM script_versions LIMIT 3");
    $stmt->execute();
    $scripts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Sample scripts:\n";
    foreach ($scripts as $script) {
        echo "- ID: {$script['id']}, Name: {$script['script_name']}, Type: {$script['app_type']}\n";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?> 