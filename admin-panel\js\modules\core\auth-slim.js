/**
 * 精简版认证管理器
 * 移除重复功能，专注核心认证逻辑
 * 
 * <AUTHOR>
 * @version 2.0.0 - 精简优化版本
 */

class AuthManager {
    constructor() {
        this.tokenKey = 'auth_token';
        this.userKey = 'user_info';
        this.currentUser = null;
        this.isAuthenticated = false;
        
        this.init();
    }
    
    init() {
        this.loadStoredAuth();
        console.log('🔐 认证管理器初始化完成');
    }
    
    // === 核心认证方法 ===
    async login(username, password) {
        try {
            const response = await this.apiRequest('/admin.php?action=login', {
                method: 'POST',
                body: JSON.stringify({ username, password })
            });
            
            if (response.code === 200 && response.data) {
                this.setAuth(response.data.token, response.data.user);
                return { success: true, user: response.data.user };
            } else {
                throw new Error(response.message || '登录失败');
            }
            
        } catch (error) {
            console.error('登录失败:', error);
            return { success: false, error: error.message };
        }
    }
    
    logout() {
        this.clearAuth();
        window.location.reload();
    }
    
    // === 认证状态管理 ===
    setAuth(token, user) {
        localStorage.setItem(this.tokenKey, token);
        localStorage.setItem(this.userKey, JSON.stringify(user));
        this.currentUser = user;
        this.isAuthenticated = true;
        
        console.log('✅ 用户认证成功:', user.username);
    }
    
    clearAuth() {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.userKey);
        this.currentUser = null;
        this.isAuthenticated = false;
        
        console.log('🚪 用户已退出登录');
    }
    
    loadStoredAuth() {
        try {
            const token = localStorage.getItem(this.tokenKey);
            const userStr = localStorage.getItem(this.userKey);
            
            if (token && userStr) {
                this.currentUser = JSON.parse(userStr);
                this.isAuthenticated = true;
                console.log('📋 已加载存储的认证信息:', this.currentUser.username);
            }
            
        } catch (error) {
            console.error('认证信息加载失败:', error);
            this.clearAuth();
        }
    }
    
    // === 认证检查 ===
    isLoggedIn() {
        return this.isAuthenticated && this.currentUser && localStorage.getItem(this.tokenKey);
    }
    
    getToken() {
        return localStorage.getItem(this.tokenKey);
    }
    
    getUser() {
        return this.currentUser;
    }
    
    getUserId() {
        return this.currentUser?.id;
    }
    
    getUserType() {
        return this.currentUser?.user_type;
    }
    
    // === 权限检查 ===
    hasPermission(permission) {
        if (!this.isAuthenticated) return false;
        
        const userType = this.getUserType();
        
        // 系统管理员拥有所有权限
        if (userType === 'system_admin') return true;
        
        // 基础权限映射
        const permissions = {
            platform_admin: ['merchants', 'providers', 'orders', 'financial', 'products'],
            provider: ['orders', 'financial', 'qr_codes', 'profile'],
            merchant: ['orders', 'financial', 'api', 'profile']
        };
        
        return permissions[userType]?.includes(permission) || false;
    }
    
    requireAuth() {
        if (!this.isLoggedIn()) {
            console.warn('⚠️ 需要登录访问');
            this.redirectToLogin();
            return false;
        }
        return true;
    }
    
    requirePermission(permission) {
        if (!this.requireAuth()) return false;
        
        if (!this.hasPermission(permission)) {
            console.warn('⚠️ 权限不足:', permission);
            this.showPermissionError();
            return false;
        }
        return true;
    }
    
    // === API请求工具 ===
    async apiRequest(url, options = {}) {
        const token = this.getToken();
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        if (finalOptions.headers && options.headers) {
            finalOptions.headers = { ...defaultOptions.headers, ...options.headers };
        }
        
        const apiUrl = url.startsWith('http') ? url : `${window.CONFIG?.API_BASE_URL || '/api'}${url}`;
        
        try {
            const response = await fetch(apiUrl, finalOptions);
            
            if (response.status === 401) {
                console.warn('🔒 认证失效，需要重新登录');
                this.clearAuth();
                this.redirectToLogin();
                throw new Error('认证失效');
            }
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
            
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }
    
    // === 工具方法 ===
    redirectToLogin() {
        if (window.location.hash) {
            window.location.hash = '';
        }
        window.location.reload();
    }
    
    showPermissionError() {
        if (window.Utils) {
            const utils = new window.Utils();
            utils.showError('您没有权限执行此操作');
        } else {
            alert('您没有权限执行此操作');
        }
    }
    
    // === 会话管理 ===
    async refreshToken() {
        try {
            const response = await this.apiRequest('/admin.php?action=refresh_token', {
                method: 'POST'
            });
            
            if (response.code === 200 && response.data.token) {
                localStorage.setItem(this.tokenKey, response.data.token);
                console.log('🔄 Token刷新成功');
                return true;
            }
            
        } catch (error) {
            console.error('Token刷新失败:', error);
            this.clearAuth();
        }
        
        return false;
    }
    
    // === 自动刷新Token ===
    startTokenRefresh() {
        // 每25分钟刷新一次token（假设token有效期30分钟）
        setInterval(async () => {
            if (this.isLoggedIn()) {
                await this.refreshToken();
            }
        }, 25 * 60 * 1000);
    }
    
    // === 密码修改 ===
    async changePassword(oldPassword, newPassword) {
        try {
            const response = await this.apiRequest('/admin.php?action=change_password', {
                method: 'POST',
                body: JSON.stringify({ old_password: oldPassword, new_password: newPassword })
            });
            
            if (response.code === 200) {
                console.log('✅ 密码修改成功');
                return { success: true };
            } else {
                throw new Error(response.message || '密码修改失败');
            }
            
        } catch (error) {
            console.error('密码修改失败:', error);
            return { success: false, error: error.message };
        }
    }
}

// 导出到全局
window.AuthManager = AuthManager;

// 页面加载完成后启动token刷新
document.addEventListener('DOMContentLoaded', () => {
    if (window.authManager) {
        window.authManager.startTokenRefresh();
    }
});

console.log('✅ 精简版认证管理器已加载'); 