<?php
/**
 * API路由中间件
 * 统一处理权限验证、租户过滤、限流等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

require_once __DIR__ . '/ApiRouter.php';

class ApiMiddleware {
    
    private $tenantAuth;
    private $userContext;
    private $routeConfig;
    
    public function __construct($tenantAuth = null) {
        $this->tenantAuth = $tenantAuth;
        $this->userContext = $tenantAuth ? $tenantAuth->getUserContext() : null;
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest($action, $method = 'GET') {
        try {
            // 解析路由
            $routeName = ApiRouter::parseAction($action);
            if (!$routeName) {
                return $this->sendError('未知的API接口: ' . $action, 404);
            }
            
            $this->routeConfig = ApiRouter::getRoute($routeName);
            if (!$this->routeConfig) {
                return $this->sendError('路由配置不存在: ' . $routeName, 404);
            }
            
            // 验证HTTP方法
            if (!$this->validateMethod($method)) {
                return $this->sendError('不支持的HTTP方法', 405);
            }
            
            // 权限验证
            if (!$this->validatePermissions()) {
                return $this->sendError('权限不足', 403);
            }
            
            // 限流检查
            if (!$this->checkRateLimit()) {
                return $this->sendError('请求过于频繁', 429);
            }
            
            // 租户数据过滤
            if ($this->routeConfig['tenant_filter'] && $this->userContext) {
                $this->applyTenantFilter();
            }
            
            // 记录API调用日志
            $this->logApiCall($routeName, $action);
            
            return [
                'success' => true,
                'route_config' => $this->routeConfig,
                'tenant_context' => $this->userContext
            ];
            
        } catch (Exception $e) {
            error_log("API中间件错误: " . $e->getMessage());
            return $this->sendError('API处理失败', 500);
        }
    }
    
    /**
     * 验证HTTP方法
     */
    private function validateMethod($method) {
        $allowedMethods = is_array($this->routeConfig['method']) ? 
            $this->routeConfig['method'] : 
            [$this->routeConfig['method']];
            
        return in_array($method, $allowedMethods);
    }
    
    /**
     * 验证权限
     */
    private function validatePermissions() {
        $permissions = $this->routeConfig['permissions'];
        
        // 公开接口
        if (in_array('public', $permissions)) {
            return true;
        }
        
        // 需要认证但用户未登录
        if (!$this->userContext) {
            return false;
        }
        
        // 已认证用户访问需要认证的接口
        if (in_array('authenticated', $permissions)) {
            return true;
        }
        
        // 检查具体用户类型权限
        return in_array($this->userContext['user_type'], $permissions);
    }
    
    /**
     * 检查限流
     */
    private function checkRateLimit() {
        if (!$this->routeConfig['rate_limit']) {
            return true;
        }
        
        // 简单的基于IP的限流实现
        $ip = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
        $key = 'rate_limit_' . $ip . '_' . md5($this->routeConfig['path']);
        
        // 这里应该使用Redis或Memcached，简化实现使用文件
        $rateLimitFile = sys_get_temp_dir() . '/' . $key;
        $now = time();
        $limit = $this->routeConfig['rate_limit'];
        $window = 60; // 1分钟窗口
        
        if (file_exists($rateLimitFile)) {
            $data = json_decode(file_get_contents($rateLimitFile), true);
            
            // 清理过期记录
            $data['requests'] = array_filter($data['requests'], function($timestamp) use ($now, $window) {
                return ($now - $timestamp) < $window;
            });
            
            // 检查是否超限
            if (count($data['requests']) >= $limit) {
                return false;
            }
            
            $data['requests'][] = $now;
        } else {
            $data = ['requests' => [$now]];
        }
        
        file_put_contents($rateLimitFile, json_encode($data));
        return true;
    }
    
    /**
     * 应用租户数据过滤
     */
    private function applyTenantFilter() {
        if (!$this->userContext) {
            return;
        }
        
        $userType = $this->userContext['user_type'];
        $userId = $this->userContext['user_id'];
        $platformId = isset($this->userContext['platform_id']) ? $this->userContext['platform_id'] : null;
        
        // 设置全局过滤变量供SQL查询使用
        $GLOBALS['tenant_filter'] = [
            'user_type' => $userType,
            'user_id' => $userId,
            'platform_id' => $platformId
        ];
        
        switch ($userType) {
            case 'system_admin':
                // 系统管理员无限制
                break;
                
            case 'platform_admin':
                // 平台管理员只能访问自己平台的数据
                $GLOBALS['tenant_filter']['where_clause'] = "platform_id = {$platformId}";
                break;
                
            case 'provider':
                // 码商只能访问自己的数据
                $GLOBALS['tenant_filter']['where_clause'] = "provider_id = {$userId}";
                break;
                
            case 'merchant':
                // 商户只能访问自己的数据
                $GLOBALS['tenant_filter']['where_clause'] = "merchant_id = {$userId}";
                break;
        }
    }
    
    /**
     * 记录API调用日志
     */
    private function logApiCall($routeName, $action) {
        try {
            $logData = [
                'route_name' => $routeName,
                'action' => $action,
                'user_id' => isset($this->userContext['user_id']) ? $this->userContext['user_id'] : null,
                'user_type' => isset($this->userContext['user_type']) ? $this->userContext['user_type'] : 'anonymous',
                'ip_address' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown',
                'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'unknown',
                'timestamp' => date('Y-m-d H:i:s'),
                'method' => isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'GET'
            ];
            
            // 记录到日志文件
            $logFile = '/data/logs/api_calls.log';
            $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
            
            // 如果有数据库连接，也可以记录到数据库
            if ($this->tenantAuth) {
                $this->logToDatabase($logData);
            }
            
        } catch (Exception $e) {
            error_log("记录API调用日志失败: " . $e->getMessage());
        }
    }
    
    /**
     * 记录到数据库
     */
    private function logToDatabase($logData) {
        try {
            $db = $this->tenantAuth->getDatabase();
            
            $sql = "INSERT INTO api_call_logs (route_name, action, user_id, user_type, ip_address, user_agent, method, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $db->execute($sql, array(
                $logData['route_name'],
                $logData['action'],
                $logData['user_id'],
                $logData['user_type'],
                $logData['ip_address'],
                $logData['user_agent'],
                $logData['method'],
                $logData['timestamp']
            ));
            
        } catch (Exception $e) {
            // 数据库记录失败不影响主流程
            error_log("记录API调用到数据库失败: " . $e->getMessage());
        }
    }
    
    /**
     * 获取API统计信息
     */
    public function getApiStats($timeRange = '1 day') {
        try {
            $logFile = '/data/logs/api_calls.log';
            
            if (!file_exists($logFile)) {
                return ['total_calls' => 0, 'by_route' => [], 'by_user_type' => []];
            }
            
            $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $cutoffTime = strtotime("-{$timeRange}");
            
            $stats = [
                'total_calls' => 0,
                'by_route' => [],
                'by_user_type' => [],
                'by_hour' => [],
                'top_ips' => []
            ];
            
            foreach ($lines as $line) {
                $data = json_decode($line, true);
                if (!$data || strtotime($data['timestamp']) < $cutoffTime) {
                    continue;
                }
                
                $stats['total_calls']++;
                
                // 按路由统计
                $route = $data['route_name'];
                $stats['by_route'][$route] = (isset($stats['by_route'][$route]) ? $stats['by_route'][$route] : 0) + 1;
                
                // 按用户类型统计
                $userType = $data['user_type'];
                $stats['by_user_type'][$userType] = (isset($stats['by_user_type'][$userType]) ? $stats['by_user_type'][$userType] : 0) + 1;
                
                // 按小时统计
                $hour = date('H', strtotime($data['timestamp']));
                $stats['by_hour'][$hour] = (isset($stats['by_hour'][$hour]) ? $stats['by_hour'][$hour] : 0) + 1;
                
                // 按IP统计
                $ip = $data['ip_address'];
                $stats['top_ips'][$ip] = (isset($stats['top_ips'][$ip]) ? $stats['top_ips'][$ip] : 0) + 1;
            }
            
            // 排序
            arsort($stats['by_route']);
            arsort($stats['by_user_type']);
            arsort($stats['top_ips']);
            ksort($stats['by_hour']);
            
            // 只保留前10个IP
            $stats['top_ips'] = array_slice($stats['top_ips'], 0, 10, true);
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("获取API统计失败: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
    
    /**
     * 发送错误响应
     */
    private function sendError($message, $code = 400) {
        return [
            'success' => false,
            'error' => [
                'message' => $message,
                'code' => $code
            ]
        ];
    }
    
    /**
     * 获取路由配置信息（调试用）
     */
    public function getRouteInfo() {
        return [
            'total_routes' => count(ApiRouter::getAllRoutes()),
            'route_stats' => ApiRouter::getRouteStats(),
            'current_user_routes' => $this->userContext ? 
                ApiRouter::getRoutesByPermission($this->userContext['user_type']) : []
        ];
    }
}

error_log("ApiMiddleware 类加载完成");
?> 