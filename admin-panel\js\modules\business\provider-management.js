/**
 * 码商管理模块 - 四层架构版本
 * 负责码商CRUD操作、审核流程、费率配置、业务统计等核心码商业务功能
 * 支持多租户环境下的数据隔离和权限控制
 * 
 * <AUTHOR> Team
 * @version 2.0.0 - 四层架构改造版本
 * @since 2024-12-17
 */

// 确保CONFIG对象可用
const CONFIG = window.CONFIG || {
    TOKEN_KEY: 'admin_token',
    API_BASE_URL: '/api'
};

class ProviderManager {
    constructor() {
        this.apiClient = null;
        this.authManager = null;
        this.utils = null;
        this.tenantInfo = null;  // 新增：租户信息
        this.currentPage = 1;
        this.pageSize = 20;
        this.providers = [];
        this.currentProviders = [];
        this.totalProviders = 0;
        this.searchQuery = '';
        this.filterStatus = 'all';
        this.filters = {
            status: '',
            search: ''
        };
        
        console.log('ProviderManager initialized');
    }

    /**
     * 依赖注入方法 - 自动获取全局依赖
     */
    injectDependencies() {
        try {
            // 从全局对象获取依赖
            this.apiClient = window.apiClient || null;
            this.utils = window.utils || {
                showMessage: (msg, type) => {
                    console.log(`[${type.toUpperCase()}] ${msg}`);
                    // 简单的消息显示
                    if (type === 'error') {
                        alert('错误: ' + msg);
                    } else if (type === 'success') {
                        console.log('成功: ' + msg);
                    }
                },
                formatDate: (dateStr) => {
                    if (!dateStr) return '-';
                    return new Date(dateStr).toLocaleString('zh-CN');
                },
                getInitials: (name) => {
                    if (!name) return 'P';
                    const words = name.trim().split(' ');
                    if (words.length === 1) {
                        return words[0].charAt(0).toUpperCase();
                    }
                    return words.slice(0, 2).map(word => word.charAt(0).toUpperCase()).join('');
                },
                formatNumber: (num) => {
                    if (!num) return '0';
                    return parseFloat(num).toLocaleString('zh-CN');
                }
            };
            this.authManager = window.authManager || {
                getUserType: () => {
                    const user = JSON.parse(localStorage.getItem('user_info') || '{}');
                    return user.user_type || 'unknown';
                }
            };
            this.tenantInfo = window.TENANT_INFO || {
                tenant_type: 'platform_admin' // 默认平台管理员
            };
            
            console.log('✅ 依赖注入完成', {
                apiClient: !!this.apiClient,
                utils: !!this.utils,
                authManager: !!this.authManager,
                tenantInfo: !!this.tenantInfo
            });
        } catch (error) {
            console.error('❌ 依赖注入失败:', error);
            throw error;
        }
    }

    /**
     * 初始化码商管理模块 - 四层架构版本
     * @param {object} dependencies - 依赖项
     */
    init(dependencies) {
        try {
            // 依赖注入
            if (dependencies) {
                this.apiClient = dependencies.apiClient;
                this.utils = dependencies.utils;
                this.authManager = dependencies.authManager;
                this.tenantInfo = dependencies.tenantInfo || window.TENANT_INFO;
            } else {
                this.injectDependencies();
            }
            
            console.log('✅ 码商管理模块初始化完成', {
                tenantType: this.tenantInfo?.tenant_type,
                userType: this.authManager?.getUserType()
            });
        } catch (error) {
            console.error('❌ 码商管理模块初始化失败', error);
            throw error;
        }
    }

    /**
     * 检查是否可以管理码商
     * @returns {boolean} 是否有权限
     */
    canManageProviders() {
        if (!this.tenantInfo || !this.authManager) return false;

        const tenantType = this.tenantInfo.tenant_type;
        const userType = this.authManager.getUserType();

        // 权限矩阵：只有系统管理员和平台管理员可以管理码商
        const permissions = {
            'system_admin': ['system_admin'],
            'platform_admin': ['platform_admin'],
            'provider': [],  // 码商不能管理其他码商
            'merchant': []   // 商户不能管理码商
        };

        return permissions[tenantType]?.includes(userType) || false;
    }

    /**
     * 方案5配置驱动 - 渲染方法
     * @param {HTMLElement} container 容器元素
     * @param {Object} params 参数对象
     */
    async render(container, params = {}) {
        try {
            console.log('🎯 ProviderManager.render 被调用:', params);
            
            // 调用现有的页面加载方法
            this.loadProviderManagementPage(container);
            
            console.log('✅ 码商管理页面渲染完成');
        } catch (error) {
            console.error('❌ 码商管理页面渲染失败:', error);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h4>加载失败</h4>
                    <p>码商管理页面加载失败: ${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }

    /**
     * 加载码商管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadProviderManagementPage(container) {
        // 检查权限
        if (!this.canManageProviders()) {
            container.innerHTML = this.generateNoPermissionHTML();
            return;
        }

        container.innerHTML = this.generateProviderManagementHTML();
        this.initializeProviderManagementEvents();
        this.loadProviders();
    }

    /**
     * 生成无权限访问页面
     * @returns {string} HTML字符串
     */
    generateNoPermissionHTML() {
        return `
            <div class="text-center py-5">
                <i class="bi bi-shield-exclamation text-warning" style="font-size: 4rem;"></i>
                <h3 class="mt-3">权限不足</h3>
                <p class="text-muted">您没有权限管理码商</p>
                <div class="mt-3">
                    <p class="text-muted"><strong>当前租户类型：</strong>${this.getTenantTypeText()}</p>
                    <p class="text-muted"><strong>当前用户类型：</strong>${this.getUserTypeText()}</p>
                </div>
                <div class="mt-4">
                    <button class="btn btn-primary" onclick="window.uiManager.navigateToPage('dashboard')">
                        <i class="bi bi-house me-2"></i>返回首页
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 获取租户类型文本
     * @returns {string} 租户类型文本
     */
    getTenantTypeText() {
        if (!this.tenantInfo) return '未知';
        
        const typeMap = {
            'system_admin': '系统管理员',
            'platform_admin': '平台管理员',
            'provider': '码商',
            'merchant': '商户'
        };
        return typeMap[this.tenantInfo.tenant_type] || '未知';
    }

    /**
     * 获取用户类型文本
     * @returns {string} 用户类型文本
     */
    getUserTypeText() {
        if (!this.authManager) return '未知';
        
        const userType = this.authManager.getUserType();
        const typeMap = {
            'system_admin': '系统管理员',
            'platform_admin': '平台管理员',
            'provider': '码商',
            'merchant': '商户',
            'employee': '员工'
        };
        return typeMap[userType] || '未知';
    }

    /**
     * 生成码商管理页面HTML
     * @returns {string} HTML字符串
     */
    generateProviderManagementHTML() {
        return `
            <div class="provider-management">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-person-badge me-2"></i>码商管理</h2>
                            <p class="text-muted mb-0">管理码商信息、审核状态和费率配置</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="addProviderBtn">
                                <i class="bi bi-plus-circle me-2"></i>添加码商
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="exportProvidersBtn">
                                <i class="bi bi-download me-2"></i>导出数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalProviders">-</div>
                                <div class="stat-label">总码商数</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="pendingProviders">-</div>
                                <div class="stat-label">待审码商</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="approvedProviders">-</div>
                                <div class="stat-label">已通过</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-danger">
                                <i class="bi bi-pause-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="suspendedProviders">-</div>
                                <div class="stat-label">已暂停</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-secondary">
                                <i class="bi bi-x-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="rejectedProviders">-</div>
                                <div class="stat-label">已拒绝</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">搜索码商</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="providerSearchInput" 
                                           placeholder="码商名称、邮箱、手机号...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">码商状态</label>
                                <select class="form-select" id="providerStatusFilter">
                                    <option value="">全部状态</option>
                                    <option value="pending">待审核</option>
                                    <option value="approved">已通过</option>
                                    <option value="suspended">已暂停</option>
                                    <option value="rejected">已拒绝</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">注册时间</label>
                                <input type="date" class="form-control" id="providerDateFilter">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-outline-primary" id="searchProvidersBtn">
                                        <i class="bi bi-funnel me-1"></i>筛选
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-outline-secondary" id="refreshProvidersBtn">
                                        <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 码商列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">码商列表 <span class="badge bg-primary ms-2" id="providerCount">0</span></h5>
                        <div class="d-flex gap-2">
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                        type="button" data-bs-toggle="dropdown">
                                    批量操作
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" id="batchApproveProvidersBtn">
                                        <i class="bi bi-check-circle me-2"></i>批量审核通过
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" id="batchRejectProvidersBtn">
                                        <i class="bi bi-x-circle me-2"></i>批量拒绝
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" id="batchSuspendProvidersBtn">
                                        <i class="bi bi-pause-circle me-2"></i>批量暂停
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="providersTableContainer">
                            <!-- 码商表格将在这里动态生成 -->
                        </div>
                    </div>
                    <div class="card-footer">
                        <div id="providerPagination">
                            <!-- 分页将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 码商模态框 -->
            <div class="modal fade" id="providerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="providerModalTitle">添加码商</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="providerForm">
                                <input type="hidden" id="providerId">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">用户名 *</label>
                                        <input type="text" class="form-control" id="providerUsername" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">码商代码</label>
                                        <input type="text" class="form-control" id="providerCode" placeholder="可选，用于标识码商">
                                    </div>
                                    <div class="col-md-12" id="providerPasswordRow">
                                        <label class="form-label">密码 *</label>
                                        <input type="password" class="form-control" id="providerPassword" required minlength="6">
                                        <small class="text-muted">密码长度不能少于6位</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">最大设备数</label>
                                        <input type="number" class="form-control" id="providerMaxDevices" value="100" min="1" max="1000">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">最大小组数</label>
                                        <input type="number" class="form-control" id="providerMaxGroups" value="10" min="1" max="100">
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="providerDeviceEnabled">
                                            <label class="form-check-label" for="providerDeviceEnabled">
                                                启用设备管理
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">状态</label>
                                        <select class="form-select" id="providerStatus">
                                            <option value="pending">待审核</option>
                                            <option value="approved">已通过</option>
                                            <option value="active">激活</option>
                                            <option value="inactive">未激活</option>
                                            <option value="disabled">禁用</option>
                                            <option value="suspended">已暂停</option>
                                            <option value="rejected">已拒绝</option>
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveProviderBtn">
                                <span class="spinner-border spinner-border-sm d-none" id="providerSaveSpinner"></span>
                                <span id="providerSaveText">保存</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 修改密码模态框 -->
            <div class="modal fade" id="passwordModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="passwordModalTitle">修改密码</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="passwordForm">
                                <input type="hidden" id="passwordProviderId">
                                <div class="mb-3">
                                    <label class="form-label">新密码 *</label>
                                    <input type="password" class="form-control" id="newPassword" required minlength="6">
                                    <small class="text-muted">密码长度不能少于6位</small>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">确认密码 *</label>
                                    <input type="password" class="form-control" id="confirmPassword" required minlength="6">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-warning" id="changePasswordBtn">
                                <span class="spinner-border spinner-border-sm d-none" id="passwordSaveSpinner"></span>
                                <span id="passwordSaveText">修改密码</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 样式 -->
            <style>
                .provider-management .stat-card {
                    background: white;
                    border-radius: 8px;
                    padding: 1.5rem;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    border: 1px solid #e9ecef;
                    transition: transform 0.2s, box-shadow 0.2s;
                }

                .provider-management .stat-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                }

                .provider-management .stat-icon {
                    width: 48px;
                    height: 48px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1.5rem;
                    color: white;
                    margin-bottom: 1rem;
                }

                .provider-management .stat-number {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #333;
                    margin-bottom: 0.25rem;
                }

                .provider-management .stat-label {
                    color: #666;
                    font-size: 0.875rem;
                    font-weight: 500;
                }

                .provider-management .user-info {
                    line-height: 1.4;
                }

                .provider-management .company-info {
                    line-height: 1.4;
                }

                .provider-management .business-stats {
                    line-height: 1.3;
                }

                .provider-management .no-data-icon {
                    font-size: 4rem;
                    margin-bottom: 1rem;
                }

                .provider-management .table th {
                    border-top: none;
                    font-weight: 600;
                    color: #495057;
                    background-color: #f8f9fa;
                }

                .provider-management .btn-group-sm .btn {
                    padding: 0.25rem 0.5rem;
                }
            </style>
        `;
    }

    /**
     * 初始化码商管理事件
     */
    initializeProviderManagementEvents() {
        // 添加码商按钮
        const addBtn = document.getElementById('addProviderBtn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showCreateModal());
        }

        // 搜索框回车事件
        const searchInput = document.getElementById('providerSearchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchProviders();
                }
            });
        }

        // 状态筛选变化事件
        const statusFilter = document.getElementById('providerStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }

        // 搜索按钮
        const searchBtn = document.getElementById('searchProvidersBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.searchProviders());
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshProvidersBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshProviders());
        }

        // 保存码商按钮
        const saveBtn = document.getElementById('saveProviderBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveProvider());
        }

        // 导出按钮
        const exportBtn = document.getElementById('exportProvidersBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportProviders());
        }

        // 修改密码按钮
        const changePasswordBtn = document.getElementById('changePasswordBtn');
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', () => this.changePassword());
        }
    }

    async loadProviders(page = 1) {
        try {
            this.currentPage = page;
            this.showTableLoading();
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                status: this.filters.status,
                search: this.filters.search
            });

            // 统一使用fetch调用API
            const token = localStorage.getItem(CONFIG.TOKEN_KEY);
            if (!token) {
                throw new Error('未找到认证token，请重新登录');
            }
            
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=providers&${params}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('认证失败，请重新登录');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();

            if (result.code === 200) {
                this.providers = result.data.providers || [];
                this.updateStatsCards(result.data.stats || {});
                this.renderProvidersTable(result.data.providers || []);
                this.renderPagination(result.data.pagination || {});
            } else {
                throw new Error(result.message || '加载码商数据失败');
            }
        } catch (error) {
            console.error('加载码商数据失败:', error);
            this.showTableError('加载码商数据失败: ' + error.message);
        }
    }

    updateStatsCards(stats) {
        const elements = {
            'totalProviders': stats.total_providers || 0,
            'pendingProviders': stats.pending_providers || 0,
            'approvedProviders': stats.approved_providers || 0,
            'suspendedProviders': stats.suspended_providers || 0,
            'rejectedProviders': stats.rejected_providers || 0
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    renderProvidersTable(providers) {
        const container = document.getElementById('providersTableContainer');
        const countElement = document.getElementById('providerCount');
        
        if (countElement) {
            countElement.textContent = providers.length;
        }

        if (providers.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="no-data-icon">📋</div>
                    <h5>暂无码商数据</h5>
                    <p class="text-muted">还没有注册的码商，或者当前筛选条件下没有找到匹配的数据。</p>
                    <button class="btn btn-primary" onclick="window.providerManager.showCreateModal()">
                        <i class="bi bi-plus-circle me-2"></i>添加第一个码商
                    </button>
                </div>
            `;
            return;
        }

        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>码商代码</th>
                            <th>设备管理</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${providers.map(provider => this.renderProviderRow(provider)).join('')}
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = tableHtml;
    }

    renderProviderRow(provider) {
        const statusBadges = {
            'pending': 'badge bg-warning',
            'approved': 'badge bg-success',
            'active': 'badge bg-success',
            'disabled': 'badge bg-danger',
            'inactive': 'badge bg-secondary',
            'suspended': 'badge bg-danger',
            'rejected': 'badge bg-secondary'
        };

        const statusTexts = {
            'pending': '待审核',
            'approved': '已通过',
            'active': '激活',
            'disabled': '禁用',
            'inactive': '未激活',
            'suspended': '已暂停',
            'rejected': '已拒绝'
        };

        return `
            <tr>
                <td>#${provider.id}</td>
                <td>
                    <div class="user-info">
                        <div class="fw-bold">@${provider.username || '-'}</div>
                        <div class="text-muted small">${provider.user_type || 'provider'}</div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-info">${provider.provider_code || '未设置'}</span>
                </td>
                <td>
                    <div class="small">
                        <div>设备: ${provider.current_devices || 0}/${provider.max_devices || 100}</div>
                        <div>小组: ${provider.current_groups || 0}/${provider.max_groups || 10}</div>
                        <span class="badge ${provider.device_management_enabled ? 'bg-success' : 'bg-secondary'}">
                            ${provider.device_management_enabled ? '已启用' : '未启用'}
                        </span>
                    </div>
                </td>
                <td>
                    <span class="${statusBadges[provider.status] || 'badge bg-secondary'}">
                        ${statusTexts[provider.status] || provider.status}
                    </span>
                </td>
                <td>
                    <div class="small">${this.utils.formatDate(provider.created_at)}</div>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="window.providerManager.showEditModal(${provider.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="window.providerManager.showChangePasswordModal(${provider.id})" title="修改密码">
                            <i class="bi bi-key"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="window.providerManager.showProviderDetails(${provider.id})" title="详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="window.providerManager.deleteProvider(${provider.id})" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    renderPagination(pagination) {
        const container = document.getElementById('providerPagination');
        
        if (!pagination || pagination.total_pages <= 1) {
            container.innerHTML = '';
            return;
        }

        const currentPage = pagination.current_page;
        const totalPages = pagination.total_pages;
        
        let paginationHtml = `
            <div class="d-flex justify-content-between align-items-center">
                <div class="pagination-info">
                    显示${((currentPage - 1) * this.pageSize) + 1} - ${Math.min(currentPage * this.pageSize, pagination.total_records)}条，共${pagination.total_records}条记录
                </div>
                <nav>
                    <ul class="pagination mb-0">
        `;

        // 上一页
        paginationHtml += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="window.providerManager.loadProviders(${currentPage - 1})">
                    上一页
                </a>
            </li>
        `;

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="window.providerManager.loadProviders(1)">1</a></li>`;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="window.providerManager.loadProviders(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="window.providerManager.loadProviders(${totalPages})">${totalPages}</a></li>`;
        }

        // 下一页
        paginationHtml += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="window.providerManager.loadProviders(${currentPage + 1})">
                    下一页
                </a>
            </li>
        `;

        paginationHtml += `
                    </ul>
                </nav>
            </div>
        `;

        container.innerHTML = paginationHtml;
    }

    showTableLoading() {
        const container = document.getElementById('providersTableContainer');
        container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载码商数据...</p>
            </div>
        `;
    }

    showTableError(message) {
        const container = document.getElementById('providersTableContainer');
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-danger">加载失败</h5>
                <p class="text-muted">${message}</p>
                <button class="btn btn-primary" onclick="window.providerManager.refreshProviders()">重试</button>
            </div>
        `;
    }

    searchProviders() {
        const searchInput = document.getElementById('providerSearchInput');
        this.filters.search = searchInput ? searchInput.value.trim() : '';
        this.applyFilters();
    }

    applyFilters() {
        this.currentPage = 1;
        this.loadProviders();
    }

    refreshProviders() {
        this.loadProviders(this.currentPage);
    }

    showCreateModal() {
        document.getElementById('providerModalTitle').textContent = '添加码商';
        document.getElementById('providerId').value = '';
        document.getElementById('providerForm').reset();
        document.getElementById('providerPasswordRow').style.display = 'block';
        document.getElementById('providerPassword').required = true;
        document.getElementById('providerSaveText').textContent = '创建';

        const modal = new bootstrap.Modal(document.getElementById('providerModal'));
        modal.show();
    }

    showEditModal(providerId) {
        const provider = this.providers.find(p => p.id == providerId);
        if (!provider) {
            this.utils.showMessage('找不到码商信息', 'error');
            return;
        }

        document.getElementById('providerModalTitle').textContent = '编辑码商';
        document.getElementById('providerId').value = provider.id;
        document.getElementById('providerUsername').value = provider.username || '';
        document.getElementById('providerCode').value = provider.provider_code || '';
        document.getElementById('providerMaxDevices').value = provider.max_devices || 100;
        document.getElementById('providerMaxGroups').value = provider.max_groups || 10;
        document.getElementById('providerDeviceEnabled').checked = provider.device_management_enabled == 1;
        document.getElementById('providerStatus').value = provider.status || 'pending';
        
        document.getElementById('providerPasswordRow').style.display = 'none';
        document.getElementById('providerPassword').required = false;
        document.getElementById('providerSaveText').textContent = '保存';

        const modal = new bootstrap.Modal(document.getElementById('providerModal'));
        modal.show();
    }

    async saveProvider() {
        const saveBtn = document.getElementById('providerSaveText');
        const saveSpinner = document.getElementById('providerSaveSpinner');
        
        try {
            const form = document.getElementById('providerForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            saveBtn.textContent = '保存中...';
            saveSpinner.classList.remove('d-none');

            const formData = {
                username: document.getElementById('providerUsername').value.trim(),
                provider_code: document.getElementById('providerCode').value.trim(),
                max_devices: parseInt(document.getElementById('providerMaxDevices').value) || 100,
                max_groups: parseInt(document.getElementById('providerMaxGroups').value) || 10,
                device_management_enabled: document.getElementById('providerDeviceEnabled').checked ? 1 : 0,
                status: document.getElementById('providerStatus').value
            };

            const providerId = document.getElementById('providerId').value;
            const token = localStorage.getItem(CONFIG.TOKEN_KEY);
            if (!token) {
                throw new Error('未找到认证token，请重新登录');
            }

            let response;
            if (providerId) {
                formData.id = parseInt(providerId);
                response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=providers`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
            } else {
                formData.password = document.getElementById('providerPassword').value;
                response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=providers`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
            }

            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('认证失败，请重新登录');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.code === 200) {
                this.utils.showMessage(result.message || (providerId ? '码商更新成功' : '码商创建成功'), 'success');
                bootstrap.Modal.getInstance(document.getElementById('providerModal')).hide();
                this.loadProviders(this.currentPage);
            } else {
                throw new Error(result.message || '操作失败');
            }
        } catch (error) {
            console.error('保存码商失败:', error);
            this.utils.showMessage('保存失败: ' + error.message, 'error');
        } finally {
            saveBtn.textContent = document.getElementById('providerId').value ? '保存' : '创建';
            saveSpinner.classList.add('d-none');
        }
    }

    async deleteProvider(providerId) {
        const provider = this.providers.find(p => p.id == providerId);
        if (!provider) {
            this.utils.showMessage('找不到码商信息', 'error');
            return;
        }

        if (!confirm(`确定要删除码商"${provider.username}" 吗？\n\n注意：删除后无法恢复，请谨慎操作！`)) {
            return;
        }

        try {
            const token = localStorage.getItem(CONFIG.TOKEN_KEY);
            if (!token) {
                throw new Error('未找到认证token，请重新登录');
            }

            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=providers&id=${providerId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('认证失败，请重新登录');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.code === 200) {
                this.utils.showMessage('码商删除成功', 'success');
                this.loadProviders(this.currentPage);
            } else {
                throw new Error(result.message || '删除失败');
            }
        } catch (error) {
            console.error('删除码商失败:', error);
            this.utils.showMessage('删除失败: ' + error.message, 'error');
        }
    }

    showProviderDetails(providerId) {
        const provider = this.providers.find(p => p.id == providerId);
        if (!provider) {
            this.utils.showMessage('找不到码商信息', 'error');
            return;
        }

        // 简单的详情显示，后续可以创建专门的详情模态框
        const details = [
            `码商详情：`,
            ``,
            `基本信息：`,
            `用户名：@${provider.username || '-'}`,
            `码商代码：${provider.provider_code || '未设置'}`,
            `用户类型：${provider.user_type || 'provider'}`,
            ``,
            `设备管理：`,
            `当前设备：${provider.current_devices || 0}`,
            `最大设备：${provider.max_devices || 100}`,
            `当前小组：${provider.current_groups || 0}`,
            `最大小组：${provider.max_groups || 10}`,
            `设备管理状态：${provider.device_management_enabled ? '已启用' : '未启用'}`,
            ``,
            `状态：${provider.status || '-'}`,
            `创建时间：${this.utils.formatDate(provider.created_at)}`,
            `最后活动：${this.utils.formatDate(provider.last_activity) || '无'}`
        ].join('\n');

        alert(details);
    }

    showChangePasswordModal(providerId) {
        const provider = this.providers.find(p => p.id == providerId);
        if (!provider) {
            this.utils.showMessage('找不到码商信息', 'error');
            return;
        }
        
        document.getElementById('passwordModalTitle').textContent = `修改密码 - ${provider.username}`;
        document.getElementById('passwordProviderId').value = provider.id;
        document.getElementById('passwordForm').reset();
        
        const modal = new bootstrap.Modal(document.getElementById('passwordModal'));
        modal.show();
    }

    async changePassword() {
        const saveBtn = document.getElementById('passwordSaveText');
        const saveSpinner = document.getElementById('passwordSaveSpinner');
        
        try {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (newPassword !== confirmPassword) {
                this.utils.showMessage('两次输入的密码不一致', 'error');
                return;
            }
            
            if (newPassword.length < 6) {
                this.utils.showMessage('密码长度不能少于6位', 'error');
                return;
            }
            
            saveBtn.textContent = '修改中...';
            saveSpinner.classList.remove('d-none');
            
            const providerId = document.getElementById('passwordProviderId').value;
            const token = localStorage.getItem(CONFIG.TOKEN_KEY);
            if (!token) {
                throw new Error('未找到认证token，请重新登录');
            }
            
            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=reset_provider_password`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    provider_id: parseInt(providerId),
                    new_password: newPassword
                })
            });
            
            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('认证失败，请重新登录');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            if (result.code === 200) {
                this.utils.showMessage('密码修改成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('passwordModal')).hide();
            } else {
                throw new Error(result.message || '密码修改失败');
            }
        } catch (error) {
            console.error('修改密码失败:', error);
            this.utils.showMessage('修改密码失败: ' + error.message, 'error');
        } finally {
            saveBtn.textContent = '修改密码';
            saveSpinner.classList.add('d-none');
        }
    }

    async exportProviders() {
        try {
            this.utils.showMessage('正在导出码商数据...', 'info');
            
            const token = localStorage.getItem(CONFIG.TOKEN_KEY);
            if (!token) {
                throw new Error('未找到认证token，请重新登录');
            }

            const response = await fetch(`${CONFIG.API_BASE_URL}/admin.php?action=export_providers`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                if (response.status === 401) {
                    throw new Error('认证失败，请重新登录');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.code === 200) {
                // 创建下载链接
                const blob = new Blob([result.data], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `providers_${new Date().getTime()}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                this.utils.showMessage('码商数据导出成功', 'success');
            } else {
                throw new Error(result.message || '导出失败');
            }
        } catch (error) {
            console.error('导出码商数据失败:', error);
            this.utils.showMessage('导出失败: ' + error.message, 'error');
        }
    }
}

// 创建模块实例
const ProviderManagementModule = {
    name: 'provider-management',
    version: '1.0.0',
    instance: null,
    
    async initialize() {
        this.instance = new ProviderManager();
        await this.instance.init();
        return this.instance;
    },
    
    getInstance() {
        return this.instance;
    },
    
    // 直接提供render方法供路由管理器调用
    render: function(container, params = {}) {
        if (this.instance && this.instance.render) {
            return this.instance.render(container, params);
        } else {
            console.error('❌ ProviderManager实例未初始化');
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h4>模块未初始化</h4>
                    <p>码商管理模块尚未完成初始化，请稍后再试。</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }
};

// 全局导出
window.ProviderManagementModule = ProviderManagementModule;

// 创建全局实例
if (!window.providerManager) {
    ProviderManagementModule.initialize().then(instance => {
        window.providerManager = instance;
        console.log('✅ 码商管理模块全局实例创建完成');
    }).catch(error => {
        console.error('❌ 码商管理模块初始化失败:', error);
    });
}

// 模块导出 - 路由管理器期望的格式
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProviderManagementModule;
} else {
    // 浏览器环境下，直接导出到window
    window['provider-management'] = ProviderManagementModule;
}
