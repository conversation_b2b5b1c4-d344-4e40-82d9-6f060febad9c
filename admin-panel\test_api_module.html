<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API模块测试页面</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>API模块测试页面</h1>
        <div id="apiContainer"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 模拟认证管理器 -->
    <script>
        // 模拟认证管理器
        window.authManager = {
            getToken: () => 'test-token',
            getUser: () => ({ user_type: 'merchant' })
        };
        
        // 模拟租户信息
        window.TENANT_INFO = {
            tenant_type: 'merchant'
        };
        
        // 模拟API客户端
        window.apiClient = {
            get: (url) => fetch(url),
            post: (url, data) => fetch(url, { method: 'POST', body: JSON.stringify(data) })
        };
        
        // 模拟工具函数
        window.utils = {
            formatDate: (date) => new Date(date).toLocaleString('zh-CN'),
            formatNumber: (num) => num.toLocaleString()
        };
    </script>
    
    <!-- 加载ApiModule -->
    <script src="admin-panel/js/modules/ApiModule.js"></script>
    
    <script>
        // 初始化API模块
        document.addEventListener('DOMContentLoaded', function() {
            const apiModule = new ApiModule();
            const container = document.getElementById('apiContainer');
            
            // 渲染API模块
            apiModule.render(container, { role: 'merchant' });
        });
    </script>
</body>
</html> 