<?php
require_once dirname(__FILE__) . '/common.php';

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'api_docs':
            handleGetApiDocs();
            break;
        case 'api_test':
            handleApiTest();
            break;
        default:
            http_response_code(404);
            echo json_encode(array('code' => 404, 'message' => '接口不存在'));
    }
} catch (Exception $e) {
    ErrorHandler::handleException($e);
}

// 获取API文档
function handleGetApiDocs() {
    global $currentUser;
    
    // 允许所有已登录用户查看API文档
    if (!$currentUser) {
        ErrorHandler::unauthorized('请先登录');
        return;
    }
    
    $docs = array(
        'info' => array(
            'title' => 'PayPal支付系统API接口文档',
            'version' => '2.0.0',
            'description' => '商户接入支付系统的完整API文档',
            'base_url' => 'https://qrcode.top670.com',
            'notice' => '调用接口前请提供IP，只有添加到白名单的IP才可成功调用。'
        ),
        'authentication' => array(
            'type' => 'SHA-256签名',
            'description' => '为确保接口调用安全可靠，平台对所有开发者请求增加签名校验',
            'signature_steps' => array(
                array(
                    'step' => 1,
                    'title' => '准备业务参数',
                    'description' => '把要POST提交的字段放入数组'
                ),
                array(
                    'step' => 2,
                    'title' => '按字段名升序排序',
                    'description' => 'ksort($post, SORT_STRING);'
                ),
                array(
                    'step' => 3,
                    'title' => '拼接为 key=value 串',
                    'description' => '用 & 连接得到：a=1&b=3&c=55'
                ),
                array(
                    'step' => 4,
                    'title' => '追加三段固定信息',
                    'description' => '拼接字符串 &developer_id&developer_key&timestamp'
                ),
                array(
                    'step' => 5,
                    'title' => '计算 SHA-256 并转大写',
                    'description' => '$sign = strtoupper(hash("sha256", $post_str));'
                ),
                array(
                    'step' => 6,
                    'title' => '在POST请求中携带字段',
                    'description' => 'developer_id、timestamp、sign与业务数据一起提交'
                )
            )
        ),
        'apis' => array(
            'pay_get_qrcode' => array(
                'name' => '获取收款页接口',
                'description' => '接口接收请求后，会返回有可用收款信息的收款页面url，供使用。',
                'url' => 'https://qrcode.top670.com/api.php?do=pay_get_qrcode',
                'method' => 'POST',
                'content_type' => 'application/x-www-form-urlencoded',
                'parameters' => array(
                    'developer_id' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者id，在开发者后台设置中查看'
                    ),
                    'timestamp' => array(
                        'type' => 'integer',
                        'required' => true,
                        'description' => '开发者签名时间戳(五分钟内签名时间有效)'
                    ),
                    'sign' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者sign签名（签名生成方式见签名说明）'
                    ),
                    'amount' => array(
                        'type' => 'decimal',
                        'required' => true,
                        'description' => '交易金额，单位是元'
                    ),
                    'type' => array(
                        'type' => 'integer',
                        'required' => true,
                        'description' => '传入固定数值 2'
                    ),
                    'product_id' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '传入产品id（由开发者在后台添加）'
                    ),
                    'order_no' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '传入开发者的订单号,数字,字母均可以最长50,要保持唯一'
                    ),
                    'notification_url' => array(
                        'type' => 'string',
                        'required' => false,
                        'description' => '订单状态回调地址。传入此值时若产品已有设定回调地址，则优先传入该地址。'
                    )
                ),
                'response' => array(
                    'error_code' => array(
                        'type' => 'integer',
                        'description' => '错误代码，0表示成功，非0表示失败'
                    ),
                    'error_message' => array(
                        'type' => 'string',
                        'description' => '错误信息，成功时为空'
                    ),
                    'url' => array(
                        'type' => 'string',
                        'description' => '收款页面完整url'
                    )
                ),
                'example' => array(
                    'request' => 'developer_id=11&sign=CDFAFE235B6B37AF641FB94F1D4AD31BC6035A294A3B509790EEF0750FC157FB&amount=200&product_id=12&type=2&timestamp=1746809221&order_no=201120339120099200929844892001&notification_url=http://www.baidu.com/',
                    'success_response' => array(
                        'error_code' => 0,
                        'error_message' => '',
                        'url' => 'https://pay.top670.com/?key=12_1746676802_573882'
                    ),
                    'error_response' => array(
                        'error_code' => 22,
                        'error_message' => '无可用账户'
                    )
                ),
                'error_codes' => array(
                    0 => '成功',
                    2 => '收款金额输入错误',
                    6 => '请输入有效的product_id',
                    7 => '请输入有效的order_no',
                    8 => '该order_no已经被使用,请换一个',
                    22 => '该金额不存在可用的账户',
                    23 => '不安全发起,无法使用',
                    212 => 'developer_id错误',
                    213 => 'sign错误',
                    214 => 'ip不在白名单中,请去开发者后台配置白名单',
                    215 => 'sign超时,请重新生成'
                )
            ),
            'pay_get_orders_inquiry' => array(
                'name' => '查询订单信息接口',
                'description' => '可传入订单号id，主动查询订单状态和信息。',
                'url' => 'https://qrcode.top670.com/api.php?do=pay_get_orders_inquiry',
                'method' => 'POST',
                'content_type' => 'application/x-www-form-urlencoded',
                'parameters' => array(
                    'developer_id' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者id，在开发者后台设置中查看'
                    ),
                    'timestamp' => array(
                        'type' => 'integer',
                        'required' => true,
                        'description' => '开发者签名时间戳(五分钟内签名时间有效)'
                    ),
                    'sign' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者sign签名（签名生成方式见签名说明）'
                    ),
                    'order_no' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '传入开发者的订单号,数字,字母均可以最长50,要保持唯一'
                    )
                ),
                'response' => array(
                    'error_code' => array(
                        'type' => 'integer',
                        'description' => '错误代码，0表示成功，非0表示失败'
                    ),
                    'error_message' => array(
                        'type' => 'string',
                        'description' => '错误信息，成功时为空'
                    ),
                    'order_data' => array(
                        'type' => 'object',
                        'description' => '订单详细信息',
                        'fields' => array(
                            'id' => '系统订单id',
                            'amount' => '金额（元）',
                            'order_no' => '开发者订单号',
                            'status' => '订单状态 0 待付款  1已付款 2已超时',
                            'create_time' => '订单生成时间',
                            'pay_time' => '收款时间',
                            'fee' => '手续费金额（元）',
                            'product_name' => '产品名称'
                        )
                    )
                ),
                'example' => array(
                    'request' => 'developer_id=11&sign=CDFAFE235B6B37AF641FB94F1D4AD31BC6035A294A3B509790EEF0750FC157FB&timestamp=1746809221&order_no=201120339120099200929844892001',
                    'success_response' => array(
                        'error_code' => 0,
                        'error_message' => '',
                        'order_data' => array(
                            'id' => 6,
                            'amount' => 30000,
                            'status' => 2,
                            'create_time' => '2025-05-07 23:26:20',
                            'pay_time' => '',
                            'product_id' => 2,
                            'order_no' => '20250507000001',
                            'fee' => '',
                            'product_name' => '测试商品'
                        )
                    ),
                    'error_response' => array(
                        'error_code' => 217,
                        'error_message' => '订单不存在'
                    )
                ),
                'error_codes' => array(
                    0 => '成功',
                    212 => 'developer_id错误',
                    213 => 'sign错误',
                    214 => 'ip不在白名单中,请去开发者后台配置白名单',
                    215 => 'sign超时,请重新生成',
                    217 => '订单不存在'
                )
            ),
            'pay_get_payment_quota' => array(
                'name' => '查询产品可用额度接口',
                'description' => '可传入产品id，查看产品可用额度。',
                'url' => 'https://qrcode.top670.com/api.php?do=pay_get_payment_quota',
                'method' => 'POST',
                'content_type' => 'application/x-www-form-urlencoded',
                'parameters' => array(
                    'developer_id' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者id，在开发者后台设置中查看'
                    ),
                    'timestamp' => array(
                        'type' => 'integer',
                        'required' => true,
                        'description' => '开发者签名时间戳(五分钟内签名时间有效)'
                    ),
                    'sign' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '开发者sign签名（签名生成方式见签名说明）'
                    ),
                    'product_id' => array(
                        'type' => 'string',
                        'required' => true,
                        'description' => '传入产品id（由开发者在后台添加）'
                    )
                ),
                'response' => array(
                    'error_code' => array(
                        'type' => 'integer',
                        'description' => '错误代码，0表示成功，非0表示失败'
                    ),
                    'error_message' => array(
                        'type' => 'string',
                        'description' => '错误信息，成功时为空'
                    ),
                    'product_id' => array(
                        'type' => 'string',
                        'description' => '产品id'
                    ),
                    'total_remain' => array(
                        'type' => 'decimal',
                        'description' => '产品可用额度（单位：元）'
                    )
                ),
                'example' => array(
                    'request' => 'developer_id=11&sign=CDFAFE235B6B37AF641FB94F1D4AD31BC6035A294A3B509790EEF0750FC157FB&timestamp=1746809221&product_id=201',
                    'success_response' => array(
                        'error_code' => 0,
                        'error_message' => '',
                        'product_id' => '32',
                        'total_remain' => '21.02'
                    ),
                    'error_response' => array(
                        'error_code' => 213,
                        'error_message' => 'sign错误'
                    )
                ),
                'error_codes' => array(
                    0 => '成功',
                    212 => 'developer_id错误',
                    213 => 'sign错误',
                    214 => 'ip不在白名单中,请去开发者后台配置白名单',
                    215 => 'sign超时,请重新生成',
                    218 => '产品不存在'
                )
            )
        ),
        'callback' => array(
            'description' => '回调信息说明',
            'format' => 'HTTP POST form格式',
            'success_response' => 'OK',
            'data_structure' => '与获取订单信息接口返回的信息一致',
            'example' => array(
                'received_data' => array(
                    'id' => 2912,
                    'amount' => 101,
                    'pay_amount' => 101,
                    'status' => 1,
                    'create_time' => '2025-06-05 14:22:07',
                    'pay_time' => '2025-06-05 14:23:27',
                    'order_no' => 'Testpay250605142212322511',
                    'fee' => 0,
                    'product_name' => '支付宝100至50000',
                    'timestamp' => 1749106811,
                    'sign' => 'CE3308DEA0BE6171C6CC6704D92C9ED8479D3BCE264E8362726423E975C33E12'
                ),
                'verification_string' => 'amount=101&create_time=2025-06-05 14:22:07&fee=0&id=2912&order_no=Testpay250605142212322511&pay_amount=101&pay_time=2025-06-05 14:23:27&product_id=21&product_name=支付宝100至50000&status=1&123&109e0f798850c73087d89f07fccea3ba&1749106811',
                'calculated_sign' => 'CE3308DEA0BE6171C6CC6704D92C9ED8479D3BCE264E8362726423E975C33E12'
            )
        )
    );
    
    ErrorHandler::success($docs, 'API文档获取成功');
}

// 处理旧的复杂数据结构 - 暂时保留
function handleGetApiDocsOld() {
    $docs_old = array(
        'categories' => array(
            'public' => array(
                'name' => '对商户公开接口',
                'description' => '供商户调用的支付接口',
                'apis' => array(
                    'pay_get_qrcode' => array(
                        'name' => '获取收款页面',
                        'method' => 'POST',
                        'url' => '/api.php?do=pay_get_qrcode',
                        'description' => '创建支付订单并获取收款页面URL',
                        'parameters' => array(
                            'developer_id' => array('type' => 'string', 'required' => true, 'description' => '商户ID'),
                            'timestamp' => array('type' => 'integer', 'required' => true, 'description' => '时间戳'),
                            'sign' => array('type' => 'string', 'required' => true, 'description' => 'SHA-256签名'),
                            'order_id' => array('type' => 'string', 'required' => true, 'description' => '商户订单号'),
                            'amount' => array('type' => 'decimal', 'required' => true, 'description' => '订单金额'),
                            'notify_url' => array('type' => 'string', 'required' => true, 'description' => '回调通知URL'),
                            'return_url' => array('type' => 'string', 'required' => false, 'description' => '支付完成跳转URL')
                        ),
                        'response' => array(
                            'error_code' => array('type' => 'integer', 'description' => '错误代码，0表示成功'),
                            'error_message' => array('type' => 'string', 'description' => '错误消息'),
                            'qrcode_url' => array('type' => 'string', 'description' => '收款页面URL'),
                            'order_id' => array('type' => 'string', 'description' => '系统订单号')
                        ),
                        'example' => array(
                            'request' => array(
                                'developer_id' => '10001',
                                'timestamp' => '1640995200',
                                'order_id' => 'ORDER_20220101_001',
                                'amount' => '100.00',
                                'notify_url' => 'https://merchant.com/notify',
                                'return_url' => 'https://merchant.com/return',
                                'sign' => 'ABC123...'
                            ),
                            'response' => array(
                                'error_code' => 0,
                                'error_message' => '',
                                'qrcode_url' => 'https://pay.paypal.com/qr/ORDER123',
                                'order_id' => 'PAY_20220101_001'
                            )
                        )
                    ),
                    'pay_get_orders_inquiry' => array(
                        'name' => '查询订单信息',
                        'method' => 'POST',
                        'url' => '/api.php?do=pay_get_orders_inquiry',
                        'description' => '查询订单支付状态和详情',
                        'parameters' => array(
                            'developer_id' => array('type' => 'string', 'required' => true, 'description' => '商户ID'),
                            'timestamp' => array('type' => 'integer', 'required' => true, 'description' => '时间戳'),
                            'sign' => array('type' => 'string', 'required' => true, 'description' => 'SHA-256签名'),
                            'order_id' => array('type' => 'string', 'required' => true, 'description' => '商户订单号或系统订单号')
                        ),
                        'response' => array(
                            'error_code' => array('type' => 'integer', 'description' => '错误代码，0表示成功'),
                            'error_message' => array('type' => 'string', 'description' => '错误消息'),
                            'order_status' => array('type' => 'string', 'description' => '订单状态：pending/completed/failed/expired'),
                            'order_amount' => array('type' => 'decimal', 'description' => '订单金额'),
                            'actual_amount' => array('type' => 'decimal', 'description' => '实际收款金额'),
                            'created_at' => array('type' => 'string', 'description' => '订单创建时间'),
                            'completed_at' => array('type' => 'string', 'description' => '订单完成时间')
                        )
                    ),
                    'pay_get_payment_quota' => array(
                        'name' => '查询产品可用额度',
                        'method' => 'POST',
                        'url' => '/api.php?do=pay_get_payment_quota',
                        'description' => '查询指定产品的可用额度',
                        'parameters' => array(
                            'developer_id' => array('type' => 'string', 'required' => true, 'description' => '商户ID'),
                            'timestamp' => array('type' => 'integer', 'required' => true, 'description' => '时间戳'),
                            'sign' => array('type' => 'string', 'required' => true, 'description' => 'SHA-256签名'),
                            'product_id' => array('type' => 'string', 'required' => true, 'description' => '产品ID')
                        ),
                        'response' => array(
                            'error_code' => array('type' => 'integer', 'description' => '错误代码，0表示成功'),
                            'error_message' => array('type' => 'string', 'description' => '错误消息'),
                            'available_quota' => array('type' => 'decimal', 'description' => '可用额度'),
                            'total_quota' => array('type' => 'decimal', 'description' => '总额度'),
                            'used_quota' => array('type' => 'decimal', 'description' => '已用额度')
                        )
                    )
                )
            ),
            'admin' => array(
                'name' => '管理后台接口',
                'description' => '管理后台专用接口，需要管理员权限',
                'apis' => array(
                    'login' => array(
                        'name' => '管理员登录',
                        'method' => 'POST',
                        'url' => '/api/admin/auth.php?action=login',
                        'description' => '管理员登录获取访问令牌',
                        'parameters' => array(
                            'username' => array('type' => 'string', 'required' => true, 'description' => '用户名'),
                            'password' => array('type' => 'string', 'required' => true, 'description' => '密码')
                        ),
                        'response' => array(
                            'code' => array('type' => 'integer', 'description' => '响应代码'),
                            'message' => array('type' => 'string', 'description' => '响应消息'),
                            'data' => array(
                                'token' => array('type' => 'string', 'description' => '访问令牌'),
                                'user' => array('type' => 'object', 'description' => '用户信息')
                            )
                        )
                    ),
                    'dashboard' => array(
                        'name' => '仪表板数据',
                        'method' => 'GET',
                        'url' => '/api/admin/dashboard.php?action=dashboard',
                        'description' => '获取管理后台仪表板统计数据',
                        'headers' => array(
                            'Authorization' => array('type' => 'string', 'required' => true, 'description' => 'Bearer {token}')
                        ),
                        'response' => array(
                            'code' => array('type' => 'integer', 'description' => '响应代码'),
                            'message' => array('type' => 'string', 'description' => '响应消息'),
                            'data' => array(
                                'stats' => array('type' => 'object', 'description' => '统计数据'),
                                'recent_orders' => array('type' => 'array', 'description' => '最近订单'),
                                'system_status' => array('type' => 'object', 'description' => '系统状态')
                            )
                        )
                    )
                )
            ),
            'mobile' => array(
                'name' => '手机端接口',
                'description' => '手机端APP调用的接口',
                'apis' => array(
                    'instruction_get' => array(
                        'name' => '获取收款指令',
                        'method' => 'POST',
                        'url' => '/api/mobile/instruction_get.php',
                        'description' => '手机端获取待处理的收款指令',
                        'parameters' => array(
                            'device_id' => array('type' => 'string', 'required' => true, 'description' => '设备ID'),
                            'device_key' => array('type' => 'string', 'required' => true, 'description' => '设备密钥'),
                            'alipay_account_id' => array('type' => 'integer', 'required' => true, 'description' => '支付宝账户ID')
                        ),
                        'response' => array(
                            'code' => array('type' => 'integer', 'description' => '响应代码'),
                            'message' => array('type' => 'string', 'description' => '响应消息'),
                            'data' => array(
                                'instructions' => array('type' => 'array', 'description' => '收款指令列表')
                            )
                        )
                    )
                )
            ),
            'device' => array(
                'name' => '设备管理接口',
                'description' => '设备注册和管理相关接口',
                'apis' => array(
                    'register' => array(
                        'name' => '设备注册',
                        'method' => 'POST',
                        'url' => '/api/device/register.php',
                        'description' => '注册新设备到系统',
                        'parameters' => array(
                            'device_id' => array('type' => 'string', 'required' => true, 'description' => '设备唯一标识'),
                            'device_name' => array('type' => 'string', 'required' => true, 'description' => '设备名称'),
                            'phone' => array('type' => 'string', 'required' => false, 'description' => '手机号码')
                        ),
                        'response' => array(
                            'code' => array('type' => 'integer', 'description' => '响应代码'),
                            'message' => array('type' => 'string', 'description' => '响应消息'),
                            'data' => array(
                                'device_key' => array('type' => 'string', 'description' => '设备密钥')
                            )
                        )
                    ),
                    'bill_report' => array(
                        'name' => '账单上报',
                        'method' => 'POST',
                        'url' => '/api/device/bill_report.php',
                        'description' => '设备上报支付宝账单数据',
                        'parameters' => array(
                            'device_id' => array('type' => 'string', 'required' => true, 'description' => '设备ID'),
                            'device_key' => array('type' => 'string', 'required' => true, 'description' => '设备密钥'),
                            'alipay_account_id' => array('type' => 'integer', 'required' => true, 'description' => '支付宝账户ID'),
                            'bills' => array('type' => 'array', 'required' => true, 'description' => '账单数据列表')
                        ),
                        'response' => array(
                            'code' => array('type' => 'integer', 'description' => '响应代码'),
                            'message' => array('type' => 'string', 'description' => '响应消息'),
                            'data' => array(
                                'processed_count' => array('type' => 'integer', 'description' => '处理的账单数量'),
                                'matched_count' => array('type' => 'integer', 'description' => '匹配的订单数量')
                            )
                        )
                    )
                )
            )
        ),
        'error_codes' => array(
            '0' => '操作成功',
            '1' => '系统错误',
            '212' => 'developer_id错误',
            '213' => 'sign错误',
            '214' => 'ip不在白名单中',
            '215' => 'sign超时',
            '400' => '请求参数错误',
            '401' => '未授权访问',
            '403' => '权限不足',
            '404' => '资源不存在',
            '500' => '服务器内部错误',
            '1001' => '数据验证失败',
            '1002' => '数据库操作失败',
            '1003' => '权限验证失败',
            '1004' => '请求的资源不存在',
            '1005' => '资源已存在',
            '1006' => '业务逻辑错误'
        ),
        'authentication' => array(
            'public_api' => array(
                'type' => 'signature',
                'description' => '使用SHA-256签名验证',
                'steps' => array(
                    '1. 将所有业务参数按key升序排序',
                    '2. 拼接为key=value格式，用&连接',
                    '3. 在末尾添加&developer_id&developer_key&timestamp',
                    '4. 对整个字符串进行SHA-256加密并转大写',
                    '5. 将结果作为sign参数传递'
                )
            ),
            'admin_api' => array(
                'type' => 'bearer_token',
                'description' => '使用Bearer Token认证',
                'header' => 'Authorization: Bearer {token}'
            ),
            'device_api' => array(
                'type' => 'device_key',
                'description' => '使用设备ID和设备密钥认证',
                'parameters' => array('device_id', 'device_key')
            )
        )
    );
    
    ErrorHandler::success($docs, 'API文档获取成功');
}

// API测试
function handleApiTest() {
    global $currentUser;
    
    if (!checkPermission('system_management')) {
        ErrorHandler::forbidden('权限不足，需要系统管理权限');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['api_url']) || !isset($input['method'])) {
        ErrorHandler::badRequest('缺少必要参数：api_url 和 method');
        return;
    }
    
    $apiUrl = $input['api_url'];
    $method = strtoupper($input['method']);
    $headers = isset($input['headers']) ? $input['headers'] : array();
    $params = isset($input['parameters']) ? $input['parameters'] : array();
    
    // 构建请求
    $ch = curl_init();
    
    // 基础配置
    curl_setopt_array($ch, array(
        CURLOPT_URL => $apiUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT => 'PayPal-API-Tester/1.0'
    ));
    
    // 设置请求方法
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if (!empty($params)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            $headers['Content-Type'] = 'application/json';
        }
    } elseif ($method === 'PUT') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        if (!empty($params)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            $headers['Content-Type'] = 'application/json';
        }
    } elseif ($method === 'DELETE') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
    }
    
    // 设置请求头
    if (!empty($headers)) {
        $curlHeaders = array();
        foreach ($headers as $key => $value) {
            $curlHeaders[] = "{$key}: {$value}";
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $curlHeaders);
    }
    
    // 执行请求
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        ErrorHandler::internalError('API测试失败: ' . $error);
        return;
    }
    
    // 尝试解析JSON响应
    $responseData = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $responseData = $response;
    }
    
    $result = array(
        'request' => array(
            'url' => $apiUrl,
            'method' => $method,
            'headers' => $headers,
            'parameters' => $params
        ),
        'response' => array(
            'http_code' => $httpCode,
            'data' => $responseData
        ),
        'test_time' => date('Y-m-d H:i:s'),
        'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
    );
    
    ErrorHandler::success($result, 'API测试完成');
}
?> 