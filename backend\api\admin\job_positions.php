<?php
/**
 * 职位管理接口 - 统一架构版本
 */

// 调试信息
error_log("Job positions API called - Method: " . $_SERVER['REQUEST_METHOD'] . " - GET: " . print_r($_GET, true) . " - POST: " . file_get_contents('php://input'));

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证
if (!$auth->checkAuth()) {
    $auth->sendUnauthorized('请先登录');
    exit;
}

// 获取当前用户信息和域名信息（统一在头部处理）
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 验证用户信息
if (!$currentUser) {
    $auth->sendError('用户未登录', 401);
    exit;
}

// 获取数据库连接
$db = $auth->getDatabase();

try {
    // 检查用户权限：系统管理员、平台管理员、商户、码商都可以管理职位
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin', 'merchant', 'provider'])) {
        $auth->sendForbidden('无权限访问职位管理');
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        handleGetPositions();
    } elseif ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }
        $action = isset($input['action']) ? $input['action'] : '';
        
        switch ($action) {
            case 'get_permissions':
                handleGetPermissions($input);
                break;
            case 'create':
                handleCreatePosition($input);
                break;
            case 'update':
                handleUpdatePosition($input);
                break;
            case 'delete':
                handleDeletePosition($input);
                break;
            case 'update_permissions':
                handleUpdatePositionPermissions($input);
                break;
            default:
                $auth->sendError('不支持的操作', 400);
        }
    } else {
        $auth->sendError('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    error_log("Job positions API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 获取职位列表
function handleGetPositions() {
    global $db, $auth, $currentUser, $domainInfo;
    
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    
    // 构建查询条件
    $whereConditions = array();
    $params = array();
    
    // 根据用户类型和归属关系过滤可见职位
    switch ($currentUser['user_type']) {
        case 'system_admin':
            // 系统管理员可以看到所有职位
            break;
        case 'platform_admin':
            // 平台管理员只能看到自己平台的职位
            $platformId = isset($domainInfo['platform_id']) ? $domainInfo['platform_id'] : 0;
            $whereConditions[] = "jp.belongs_to_type = 'platform' AND jp.belongs_to_id = ?";
            $params[] = $platformId;
            break;
        case 'merchant':
            // 商户管理员只能看到自己商户的职位
            $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", array($currentUser['id']));
            if (!$merchant) {
                $auth->sendForbidden('商户信息不存在');
                return;
            }
            $whereConditions[] = "jp.belongs_to_type = 'merchant' AND jp.belongs_to_id = ?";
            $params[] = $merchant['id'];
            break;
        case 'provider':
            // 码商管理员只能看到自己码商的职位
            $provider = $db->fetch("SELECT id FROM payment_providers WHERE user_id = ?", array($currentUser['id']));
            if (!$provider) {
                $auth->sendForbidden('码商信息不存在');
                return;
            }
            $whereConditions[] = "jp.belongs_to_type = 'provider' AND jp.belongs_to_id = ?";
            $params[] = $provider['id'];
            break;
        default:
            // 其他用户类型无权查看
            $auth->sendForbidden('无权限查看职位');
            return;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(jp.position_name LIKE ? OR jp.description LIKE ?)";
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if ($status !== '') {
        $whereConditions[] = "jp.status = ?";
        $params[] = $status;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $countSql = "SELECT COUNT(*) as total FROM job_positions jp {$whereClause}";
    $totalResult = $db->fetch($countSql, $params);
    $total = $totalResult['total'];
    
    // 获取职位列表
    $sql = "SELECT 
        jp.id, jp.position_name, jp.description, jp.status, 
        jp.belongs_to_type, jp.belongs_to_id, jp.created_by, jp.created_at, jp.updated_at,
        COUNT(DISTINCT jpp.permission_id) as permission_count,
        COUNT(DISTINCT e.id) as employee_count
        FROM job_positions jp
        LEFT JOIN job_position_permissions jpp ON jp.id = jpp.job_position_id
        LEFT JOIN employees e ON e.job_position_id = jp.id
        {$whereClause}
        GROUP BY jp.id
        ORDER BY jp.created_at DESC 
        LIMIT {$limit} OFFSET {$offset}";
    
    $positions = $db->fetchAll($sql, $params);
    
    // 获取所有权限用于返回给前端
    $permissions = getAllPermissions();
    
    $auth->sendSuccess(array(
        'positions' => $positions,
        'permissions' => $permissions,
        'pagination' => array(
            'current_page' => $page,
            'per_page' => $limit,
            'total_records' => $total,
            'total_pages' => ceil($total / $limit)
        )
    ), '获取成功');
}

// 获取权限列表
function handleGetPermissions($input) {
    global $db, $auth, $currentUser, $domainInfo;
    
    $positionId = isset($input['position_id']) ? intval($input['position_id']) : 0;
    
    // 获取所有权限
    $permissions = getAllPermissions();
    
    $response = array(
        'permissions' => $permissions
    );
    
    // 如果指定了职位ID，获取该职位的权限
    if ($positionId > 0) {
        $positionPermissions = $db->fetchAll(
            "SELECT permission_id FROM job_position_permissions WHERE job_position_id = ?",
            array($positionId)
        );
        $response['position_permissions'] = array_map(function($p) {
            return intval($p['permission_id']);
        }, $positionPermissions);
    }
    
    $auth->sendSuccess($response, '获取成功');
}

// 获取所有权限（按模块分组）
function getAllPermissions() {
    global $db;
    
    // 检查表结构，兼容不同的字段名
    $permissions = $db->fetchAll("SELECT id, permission_key, permission_name, description, module FROM permissions ORDER BY module, permission_name, id");
    
    $grouped = array();
    foreach ($permissions as $permission) {
        $module = isset($permission['module']) && $permission['module'] ? $permission['module'] : '其他';
        if (!isset($grouped[$module])) {
            $grouped[$module] = array();
        }
        $grouped[$module][] = $permission;
    }
    
    return $grouped;
}

// 创建职位
function handleCreatePosition($input) {
    global $db, $auth, $currentUser, $domainInfo;
    
    $error = validatePositionInput($input, array('position_name'));
    if ($error) {
        $auth->sendError($error, 400);
        return;
    }
    
    // 确定职位归属关系
    $belongsToType = '';
    $belongsToId = 0;
    
    switch ($currentUser['user_type']) {
        case 'system_admin':
            // 系统管理员创建的职位归属于系统
            $belongsToType = 'platform';
            $belongsToId = 0; // 系统级别
            break;
        case 'platform_admin':
            // 平台管理员创建的职位归属于平台
            $belongsToType = 'platform';
            $belongsToId = isset($domainInfo['platform_id']) ? $domainInfo['platform_id'] : 0;
            break;
        case 'merchant':
            // 商户管理员创建的职位归属于商户
            $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", array($currentUser['id']));
            if (!$merchant) {
                $auth->sendForbidden('商户信息不存在');
                return;
            }
            $belongsToType = 'merchant';
            $belongsToId = $merchant['id'];
            break;
        case 'provider':
            // 码商管理员创建的职位归属于码商
            $provider = $db->fetch("SELECT id FROM payment_providers WHERE user_id = ?", array($currentUser['id']));
            if (!$provider) {
                $auth->sendForbidden('码商信息不存在');
                return;
            }
            $belongsToType = 'provider';
            $belongsToId = $provider['id'];
            break;
        default:
            $auth->sendForbidden('无权限创建职位');
            return;
    }
    
    // 插入职位
    $insertSql = "
        INSERT INTO job_positions (position_name, description, belongs_to_type, belongs_to_id, created_by, status, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    ";
    
    $params = array(
        $input['position_name'],
        isset($input['description']) ? $input['description'] : '',
        $belongsToType,
        $belongsToId,
        $currentUser['id'],
        isset($input['status']) ? intval($input['status']) : 1
    );
    
    $db->execute($insertSql, $params);
    $positionId = $db->lastInsertId();
    
    // 保存权限
    if (isset($input['permissions']) && is_array($input['permissions'])) {
        savePositionPermissions($positionId, $input['permissions']);
    }
    
    $auth->logUserAction('position_create', 'job_position', $positionId, "创建职位: {$input['position_name']}");
    $auth->sendSuccess(array('id' => $positionId), '职位创建成功');
}

// 更新职位
function handleUpdatePosition($input) {
    global $db, $auth;
    
    if (!$auth->checkPermission('position_update')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $id = isset($input['id']) ? intval($input['id']) : 0;
    if (!$id) {
        $auth->sendError('缺少职位ID', 400);
        return;
    }
    
    $position = $db->fetch("SELECT * FROM job_positions WHERE id = ?", array($id));
    if (!$position) {
        $auth->sendError('职位不存在', 404);
        return;
    }
    
    // 只有创建者才能修改职位
    $currentUser = $auth->getCurrentUser();
    if ($position['created_by'] != $currentUser['id']) {
        $auth->sendForbidden('只能修改自己创建的职位');
        return;
    }
    
    $error = validatePositionInput($input, array('position_name'));
    if ($error) {
        $auth->sendError($error, 400);
        return;
    }
    
    // 检查职位代码唯一性（排除当前记录）
    if (isset($input['name']) && $input['name'] !== $position['name']) {
        $exists = $db->fetch("SELECT COUNT(*) as count FROM job_positions WHERE name = ? AND id != ?", 
                             array($input['name'], $id));
        
        if ($exists['count'] > 0) {
            $auth->sendError('职位代码已存在', 400);
            return;
        }
    }
    
    // 更新职位信息
    $updateFields = array();
    $params = array();
    
    if (isset($input['name'])) {
        $updateFields[] = "name = ?";
        $params[] = $input['name'];
    }
    
    if (isset($input['position_name'])) {
        $updateFields[] = "position_name = ?";
        $params[] = $input['position_name'];
    }
    
    if (isset($input['description'])) {
        $updateFields[] = "description = ?";
        $params[] = $input['description'];
    }
    
    if (isset($input['user_type'])) {
        $updateFields[] = "user_type = ?";
        $params[] = $input['user_type'];
    }
    
    if (isset($input['status'])) {
        $updateFields[] = "status = ?";
        // 状态转换：字符串 "active" -> 1, "inactive" -> 0
        if ($input['status'] === 'active') {
            $params[] = 1;
            error_log("状态转换: active -> 1");
        } elseif ($input['status'] === 'inactive') {
            $params[] = 0;
            error_log("状态转换: inactive -> 0");
        } else {
            $params[] = intval($input['status']);
            error_log("状态转换: {$input['status']} -> " . intval($input['status']));
        }
    }
    
    if (!empty($updateFields)) {
        $updateFields[] = "updated_at = NOW()";
        $params[] = $id;
        
        $updateSql = "UPDATE job_positions SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $db->execute($updateSql, $params);
    }
    
    // 更新权限
    if (isset($input['permissions']) && is_array($input['permissions'])) {
        savePositionPermissions($id, $input['permissions']);
    }
    
    $auth->logUserAction('position_update', 'job_position', $id, "更新职位: {$input['position_name']}");
    $auth->sendSuccess(null, '职位更新成功');
}

// 删除职位
function handleDeletePosition($input) {
    global $db, $auth;
    
    if (!$auth->checkPermission('position_delete')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $id = isset($input['id']) ? intval($input['id']) : 0;
    if (!$id) {
        $auth->sendError('缺少职位ID', 400);
        return;
    }
    
    $position = $db->fetch("SELECT * FROM job_positions WHERE id = ?", array($id));
    if (!$position) {
        $auth->sendError('职位不存在', 404);
        return;
    }
    
    // 只有创建者才能删除职位
    $currentUser = $auth->getCurrentUser();
    if ($position['created_by'] != $currentUser['id']) {
        $auth->sendForbidden('只能删除自己创建的职位');
        return;
    }
    
    // 检查是否有员工使用此职位
    $employeeCount = $db->fetch("SELECT COUNT(*) as count FROM employees WHERE job_position_id = ?", array($id));
    if ($employeeCount['count'] > 0) {
        $auth->sendError('该职位下还有员工，不能删除', 400);
        return;
    }
    
    // 删除职位权限关联
    $db->execute("DELETE FROM job_position_permissions WHERE job_position_id = ?", array($id));
    
    // 删除职位
    $db->execute("DELETE FROM job_positions WHERE id = ?", array($id));
    
    $auth->logUserAction('position_delete', 'job_position', $id, "删除职位: {$position['position_name']}");
    $auth->sendSuccess(null, '职位删除成功');
}

// 更新职位权限（API接口）
function handleUpdatePositionPermissions($input) {
    global $db, $auth, $currentUser, $domainInfo;
    
    $positionId = isset($input['position_id']) ? intval($input['position_id']) : 0;
    $permissions = isset($input['permissions']) ? $input['permissions'] : array();
    
    if (!$positionId) {
        $auth->sendError('缺少职位ID', 400);
        return;
    }
    
    // 验证职位是否存在且有权限操作
    $position = $db->fetch("SELECT * FROM job_positions WHERE id = ?", array($positionId));
    if (!$position) {
        $auth->sendError('职位不存在', 404);
        return;
    }
    
    // 保存权限
    savePositionPermissions($positionId, $permissions);
    
    $auth->sendSuccess(null, '职位权限更新成功');
}

// 保存职位权限
function savePositionPermissions($positionId, $permissions) {
    global $db;
    
    // 删除原有权限
    $db->execute("DELETE FROM job_position_permissions WHERE job_position_id = ?", array($positionId));
    
    // 插入新权限
    if (!empty($permissions)) {
        $insertSql = "INSERT INTO job_position_permissions (job_position_id, permission_id) VALUES (?, ?)";
        foreach ($permissions as $permissionId) {
            $db->execute($insertSql, array($positionId, intval($permissionId)));
        }
    }
}

// 验证职位输入
function validatePositionInput($input, $required = array()) {
    foreach ($required as $field) {
        if (!isset($input[$field]) || trim($input[$field]) === '') {
            return "缺少必填字段: {$field}";
        }
    }
    

    
    if (isset($input['position_name']) && mb_strlen($input['position_name']) > 50) {
        return '职位名称不能超过50个字符';
    }
    
    if (isset($input['description']) && mb_strlen($input['description']) > 200) {
        return '职位描述不能超过200个字符';
    }
    
    return null;
}
?> 