<?php
/**
 * 调试SQL注入逻辑
 */

require_once dirname(__FILE__) . '/config/database.php';

echo "=== 调试SQL注入逻辑 ===\n\n";

// 获取数据库实例
$db = Database::getInstance();

// 设置平台管理员1上下文
$tenantContext1 = array(
    'tenant_type' => 'platform_admin',
    'tenant_id' => 1,
    'platform_id' => 1,
    'user_id' => 1,
    'domain' => 'platform1.top005.com'
);
$db->setTenantContext($tenantContext1);

// 测试不同的SQL语句
$testCases = array(
    // 不带WHERE，不带LIMIT
    "SELECT COUNT(*) as count FROM merchants m JOIN users u ON m.user_id = u.id",
    
    // 不带WHERE，带LIMIT
    "SELECT m.*, u.username FROM merchants m JOIN users u ON m.user_id = u.id ORDER BY m.created_at DESC LIMIT 20 OFFSET 0",
    
    // 带WHERE，不带LIMIT
    "SELECT COUNT(*) as count FROM merchants m JOIN users u ON m.user_id = u.id WHERE u.status = 'active'",
    
    // 带WHERE，带LIMIT
    "SELECT m.*, u.username FROM merchants m JOIN users u ON m.user_id = u.id WHERE u.status = 'active' ORDER BY m.created_at DESC LIMIT 20 OFFSET 0"
);

foreach ($testCases as $i => $sql) {
    echo "测试案例 " . ($i + 1) . ":\n";
    echo "原始SQL: $sql\n";
    
    // 直接调用applyTenantFilter方法（需要通过反射）
    $reflection = new ReflectionClass($db);
    $method = $reflection->getMethod('applyTenantFilter');
    $method->setAccessible(true);
    
    $result = $method->invoke($db, $sql, array());
    echo "过滤后SQL: {$result['sql']}\n";
    echo "附加参数: " . json_encode($result['params']) . "\n";
    
    // 尝试执行查询看是否有语法错误
    try {
        $pdo = $db->query($result['sql'], $result['params']);
        echo "查询状态: ✅ 成功\n";
    } catch (Exception $e) {
        echo "查询状态: ❌ 失败 - " . $e->getMessage() . "\n";
    }
    echo "\n";
}

echo "=== 调试完成 ===\n";
?> 