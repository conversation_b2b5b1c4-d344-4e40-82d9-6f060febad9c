<?php
/**
 * PayPal支付系统 - 对外API统一入口
 * 兼容旧系统API调用方式，使用新数据库结构
 * 
 * 调用方式: api.php?do=接口名
 * 支持的接口:
 * - pay_get_qrcode: 获取收款页面URL
 * - pay_get_orders_inquiry: 查询订单信息
 * - pay_get_payment_quota: 查询产品可用额度
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 引入数据库配置
require_once dirname(__FILE__) . '/config/database.php';

// 初始化数据库连接
$db = new Database();
$pdo = $db->connect();

/**
 * 统一响应格式
 */
function apiResponse($error_code = 0, $error_message = '', $data = []) {
    $response = [
        'error_code' => $error_code,
        'error_message' => $error_message
    ];
    
    if (!empty($data)) {
        $response = array_merge($response, $data);
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 签名验证函数
 * 与旧系统完全一致的SHA-256签名算法
 */
function verifySign($post, $developer_key) {
    if (!isset($post['sign']) || !isset($post['developer_id']) || !isset($post['timestamp'])) {
        return false;
    }
    
    $received_sign = $post['sign'];
    
    // 移除签名相关字段，准备计算签名
    $sign_data = $post;
    unset($sign_data['sign']);
    unset($sign_data['developer_id']);
    unset($sign_data['timestamp']);
    
    // 按键名升序排序
    ksort($sign_data, SORT_STRING);
    
    // 拼接为 key=value 格式
    $post_arr = [];
    foreach ($sign_data as $k => $v) {
        $post_arr[] = $k . '=' . $v;
    }
    
    // 构建签名字符串
    $post_str = implode('&', $post_arr);
    $post_str .= '&' . $post['developer_id'] . '&' . $developer_key . '&' . $post['timestamp'];
    
    // 计算SHA-256签名并转大写
    $calculated_sign = strtoupper(hash('sha256', $post_str));
    
    return $calculated_sign === $received_sign;
}

/**
 * 验证时间戳（5分钟内有效）
 */
function verifyTimestamp($timestamp) {
    $current_time = time();
    $time_diff = abs($current_time - $timestamp);
    return $time_diff <= 300; // 5分钟 = 300秒
}

/**
 * IP白名单验证
 */
function verifyIPWhitelist($developer_id, $client_ip) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT ip_whitelist FROM merchants WHERE id = ? AND status = 'active'");
        $stmt->execute([$developer_id]);
        $merchant = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchant) {
            return false;
        }
        
        // 如果没有设置白名单，则允许所有IP
        if (empty($merchant['ip_whitelist'])) {
            return true;
        }
        
        // 解析IP白名单（支持逗号分隔的多个IP）
        $whitelist = array_map('trim', explode(',', $merchant['ip_whitelist']));
        
        // 检查客户端IP是否在白名单中
        return in_array($client_ip, $whitelist);
        
    } catch (Exception $e) {
        error_log("IP白名单验证失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取商户信息
 */
function getMerchantInfo($developer_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT m.*, u.username, u.real_name 
            FROM merchants m 
            JOIN users u ON m.user_id = u.id 
            WHERE m.id = ? AND m.status = 'active'
        ");
        $stmt->execute([$developer_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("获取商户信息失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取客户端真实IP
 */
function getClientIP() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
}

// 主程序开始
try {
    // 只接受POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        apiResponse(1, '只支持POST请求');
    }
    
    // 获取接口名称
    $do = isset($_GET['do']) ? $_GET['do'] : '';
    if (empty($do)) {
        apiResponse(1, '缺少接口参数');
    }
    
    // 获取POST数据
    $post_data = $_POST;
    if (empty($post_data)) {
        apiResponse(1, '缺少请求数据');
    }
    
    // 基础参数验证
    $required_fields = ['developer_id', 'timestamp', 'sign'];
    foreach ($required_fields as $field) {
        if (!isset($post_data[$field]) || $post_data[$field] === '') {
            apiResponse(212, 'developer_id错误');
        }
    }
    
    $developer_id = $post_data['developer_id'];
    $timestamp = $post_data['timestamp'];
    
    // 验证时间戳
    if (!verifyTimestamp($timestamp)) {
        apiResponse(215, 'sign超时,请重新生成');
    }
    
    // 获取商户信息
    $merchant = getMerchantInfo($developer_id);
    if (!$merchant) {
        apiResponse(212, 'developer_id错误');
    }
    
    // 验证签名
    if (!verifySign($post_data, $merchant['api_key'])) {
        apiResponse(213, 'sign错误');
    }
    
    // 验证IP白名单
    $client_ip = getClientIP();
    if (!verifyIPWhitelist($developer_id, $client_ip)) {
        apiResponse(214, 'ip不在白名单中,请去开发者后台配置白名单');
    }
    
    // 路由到具体的API处理文件
    $api_routes = [
        // 对商户的公开接口
        'pay_get_qrcode' => 'public/pay_get_qrcode.php',
        'pay_get_orders_inquiry' => 'public/pay_get_orders_inquiry.php', 
        'pay_get_payment_quota' => 'public/pay_get_payment_quota.php',
        'pay_qrcode_notify' => 'public/pay_qrcode_notify.php',
        
        // 管理后台接口（需要额外权限验证）
        'admin_accounts' => 'admin/accounts.php',
        'admin_transactions' => 'admin/transactions.php',
        'admin_merchants' => 'admin/merchants.php',
        'admin_devices' => 'admin/devices.php',
        'admin_instructions' => 'admin/instructions.php',
        'admin_dashboard' => 'admin/dashboard.php',
        
        // 手机端指令接口（需要设备认证）
        'mobile_instruction_get' => 'mobile/instruction_get.php',
        'mobile_instruction_update' => 'mobile/instruction_update.php',
        'mobile_instruction_complete' => 'mobile/instruction_complete.php',
        
        // 设备管理接口
        'device_register' => 'device/register.php',
        'device_account_upload' => 'device/account_upload.php',
        'device_account_complete' => 'device/account_complete.php',
        'device_script_match' => 'device/script_match.php',
        'device_bill_report' => 'device/bill_report.php'
    ];
    
    if (!isset($api_routes[$do])) {
        apiResponse(1, '接口不存在');
    }
    
    $api_file = dirname(__FILE__) . '/api/' . $api_routes[$do];
    if (!file_exists($api_file)) {
        apiResponse(1, '接口文件不存在');
    }
    
    // 对于非公开接口，进行额外的权限验证
    if (strpos($do, 'admin_') === 0) {
        // 管理后台接口需要管理员权限
        if ($merchant['role'] !== 'admin') {
            apiResponse(403, '权限不足，需要管理员权限');
        }
    } elseif (strpos($do, 'mobile_') === 0 || strpos($do, 'device_') === 0) {
        // 手机端和设备接口跳过商户签名验证，使用设备认证
        // 这些接口内部会有自己的认证逻辑
    }
    
    // 设置全局变量供API文件使用
    $GLOBALS['merchant_info'] = $merchant;
    $GLOBALS['post_data'] = $post_data;
    $GLOBALS['pdo'] = $pdo;
    
    // 包含并执行API文件
    require_once $api_file;
    
} catch (Exception $e) {
    error_log("API入口异常: " . $e->getMessage());
    apiResponse(1, '系统异常，请稍后重试');
}
?> 