<?php
/**
 * 支付系统清理定时任务
 * 
 * 功能：
 * 1. 清理过期的收款指令
 * 2. 清理过期的用户应付金额占用
 * 3. 释放过期的支付宝账户占用
 * 4. 清理过期的支付请求
 * 
 * 运行频率：建议每分钟执行一次
 * 
 * <AUTHOR> System
 * @version 1.0
 */

require_once dirname(dirname(__FILE__)) . '/config/database.php';
require_once dirname(dirname(__FILE__)) . '/api/core/PaymentInstructionManager.php';
require_once dirname(dirname(__FILE__)) . '/api/core/PayableAmountManager.php';

/**
 * 记录清理日志
 */
function logCleanup($message, $data = []) {
    $logMessage = date('Y-m-d H:i:s') . ' - ' . $message;
    if (!empty($data)) {
        $logMessage .= ' - ' . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    echo $logMessage . PHP_EOL;
    error_log($logMessage);
}

/**
 * 清理过期的支付请求
 */
function cleanupExpiredPaymentRequests($pdo) {
    try {
        // 清理30分钟前创建但仍处于pending状态的订单
        $expireTime = date('Y-m-d H:i:s', time() - (30 * 60));
        
        $stmt = $pdo->prepare("
            UPDATE payment_requests 
            SET status = 'expired',
                account_released_at = NOW()
            WHERE status = 'pending' 
            AND created_at < ?
        ");
        
        $stmt->execute([$expireTime]);
        $cleanedCount = $stmt->rowCount();
        
        if ($cleanedCount > 0) {
            logCleanup("清理过期支付请求", ['count' => $cleanedCount]);
        }
        
        return $cleanedCount;
        
    } catch (Exception $e) {
        logCleanup("清理过期支付请求失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 释放已完成或过期订单的账户占用
 */
function releaseAccountOccupation($pdo) {
    try {
        $stmt = $pdo->prepare("
            UPDATE payment_requests 
            SET account_released_at = NOW()
            WHERE status IN ('completed', 'expired', 'failed')
            AND account_released_at IS NULL
        ");
        
        $stmt->execute();
        $releasedCount = $stmt->rowCount();
        
        if ($releasedCount > 0) {
            logCleanup("释放账户占用", ['count' => $releasedCount]);
        }
        
        return $releasedCount;
        
    } catch (Exception $e) {
        logCleanup("释放账户占用失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 清理过期的账单数据（保留7天）
 */
function cleanupOldBills($pdo) {
    try {
        $expireTime = date('Y-m-d H:i:s', time() - (7 * 24 * 60 * 60));
        
        $stmt = $pdo->prepare("
            DELETE FROM alipay_bills 
            WHERE uploaded_at < ? 
            AND match_status = 'unmatched'
        ");
        
        $stmt->execute([$expireTime]);
        $cleanedCount = $stmt->rowCount();
        
        if ($cleanedCount > 0) {
            logCleanup("清理过期账单数据", ['count' => $cleanedCount]);
        }
        
        return $cleanedCount;
        
    } catch (Exception $e) {
        logCleanup("清理过期账单数据失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 清理过期的指令日志（保留30天）
 */
function cleanupOldInstructionLogs($pdo) {
    try {
        $expireTime = date('Y-m-d H:i:s', time() - (30 * 24 * 60 * 60));
        
        $stmt = $pdo->prepare("
            DELETE FROM payment_instruction_logs 
            WHERE created_at < ?
        ");
        
        $stmt->execute([$expireTime]);
        $cleanedCount = $stmt->rowCount();
        
        if ($cleanedCount > 0) {
            logCleanup("清理过期指令日志", ['count' => $cleanedCount]);
        }
        
        return $cleanedCount;
        
    } catch (Exception $e) {
        logCleanup("清理过期指令日志失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 更新账户使用统计
 */
function updateAccountUsageStats($pdo) {
    try {
        // 重置每日使用统计（如果是新的一天）
        $stmt = $pdo->prepare("
            UPDATE alipay_accounts 
            SET daily_used = 0, daily_count = 0
            WHERE DATE(last_reset_date) < CURDATE()
        ");
        
        $stmt->execute();
        $resetCount = $stmt->rowCount();
        
        if ($resetCount > 0) {
            // 更新重置日期
            $stmt = $pdo->prepare("
                UPDATE alipay_accounts 
                SET last_reset_date = CURDATE()
                WHERE DATE(last_reset_date) < CURDATE()
            ");
            $stmt->execute();
            
            logCleanup("重置账户每日使用统计", ['count' => $resetCount]);
        }
        
        return $resetCount;
        
    } catch (Exception $e) {
        logCleanup("更新账户使用统计失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 生成清理报告
 */
function generateCleanupReport($results) {
    $totalCleaned = array_sum($results);
    
    $report = [
        'timestamp' => date('Y-m-d H:i:s'),
        'total_cleaned' => $totalCleaned,
        'details' => $results
    ];
    
    logCleanup("清理任务完成", $report);
    
    return $report;
}

// 主程序开始
try {
    logCleanup("开始执行支付系统清理任务");
    
    $db = new Database();
    $pdo = $db->connect();
    
    // 初始化管理器
    $instructionManager = new PaymentInstructionManager();
    $payableAmountManager = new PayableAmountManager();
    
    $results = [];
    
    // 1. 清理过期的收款指令
    $results['expired_instructions'] = $instructionManager->cleanupExpiredInstructions();
    
    // 2. 清理过期的用户应付金额占用
    $results['expired_payable_amounts'] = $payableAmountManager->cleanupExpiredPayableAmounts(30);
    
    // 3. 清理过期的支付请求
    $results['expired_payment_requests'] = cleanupExpiredPaymentRequests($pdo);
    
    // 4. 释放账户占用
    $results['released_accounts'] = releaseAccountOccupation($pdo);
    
    // 5. 清理过期的账单数据
    $results['old_bills'] = cleanupOldBills($pdo);
    
    // 6. 清理过期的指令日志
    $results['old_instruction_logs'] = cleanupOldInstructionLogs($pdo);
    
    // 7. 更新账户使用统计
    $results['account_stats_reset'] = updateAccountUsageStats($pdo);
    
    // 生成清理报告
    $report = generateCleanupReport($results);
    
    // 如果有清理操作，发送通知（可选）
    if ($report['total_cleaned'] > 0) {
        // 这里可以添加邮件或其他通知逻辑
        // sendCleanupNotification($report);
    }
    
    logCleanup("支付系统清理任务执行完成");
    
} catch (Exception $e) {
    logCleanup("支付系统清理任务执行失败: " . $e->getMessage());
    exit(1);
}

exit(0);
?> 