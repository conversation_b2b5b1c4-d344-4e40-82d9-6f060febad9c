<?php
/**
 * 交易管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

// 检查权限
if (!$auth->checkPermission('transaction.view')) {
    $auth->sendForbidden('无权限访问交易管理');
}

try {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'list':
            handleGetTransactionList($auth);
            break;
        case 'stats':
            handleGetTransactionStats($auth);
            break;
        case 'detail':
            handleGetTransactionDetail($auth);
            break;
        default:
            handleGetTransactionList($auth);
            break;
    }
} catch (Exception $e) {
    error_log("Transactions API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 获取交易列表
function handleGetTransactionList($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('transaction.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;
        
        $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
        $status = isset($_GET['status']) ? trim($_GET['status']) : '';
        $dateRange = isset($_GET['date_range']) ? trim($_GET['date_range']) : '';
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        
        // 构建查询条件
        $whereConditions = array();
        $params = array();
        
        // 数据隔离：merchant用户只能查看自己的交易
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = 't.merchant_id = ?';
            $params[] = $currentUser['profile_id'];
        } elseif ($merchantId > 0) {
            $whereConditions[] = 't.merchant_id = ?';
            $params[] = $merchantId;
        }
        
        if (!empty($status)) {
            $whereConditions[] = 't.status = ?';
            $params[] = $status;
        }
        
        // 日期范围处理
        if (!empty($dateRange)) {
            switch ($dateRange) {
                case 'today':
                    $whereConditions[] = 'DATE(t.created_at) = CURDATE()';
                    break;
                case 'yesterday':
                    $whereConditions[] = 'DATE(t.created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)';
                    break;
                case 'week':
                    $whereConditions[] = 't.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                    break;
                case 'month':
                    $whereConditions[] = 't.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                    break;
            }
        }
        
        if (!empty($search)) {
            $whereConditions[] = '(t.order_id LIKE ? OR t.transaction_id LIKE ?)';
            $params[] = '%' . $search . '%';
            $params[] = '%' . $search . '%';
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // 获取交易列表
        $transactions = $db->fetchAll(
            "SELECT t.*, 
                    m.company_name as merchant_name,
                    u.real_name as merchant_real_name
             FROM transactions t
             LEFT JOIN merchants m ON t.merchant_id = m.id
             LEFT JOIN users u ON m.user_id = u.id
             $whereClause
             ORDER BY t.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, array($limit, $offset))
        );
        
        // 获取总数
        $totalResult = $db->fetch(
            "SELECT COUNT(*) as count FROM transactions t $whereClause",
            $params
        );
        $total = $totalResult['count'] ?: 0;
        
        // 记录操作日志
        $auth->logUserAction('view_transactions', 'transaction', 0, '查看交易列表');
        
        $response = array(
            'transactions' => $transactions ?: array(),
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        );
        
        $auth->sendSuccess($response, '交易列表获取成功');
        
    } catch (Exception $e) {
        error_log('获取交易列表失败: ' . $e->getMessage());
        $auth->sendError('获取交易列表失败: ' . $e->getMessage(), 500);
    }
}

// 获取交易统计
function handleGetTransactionStats($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('transaction.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        $dateRange = isset($_GET['date_range']) ? trim($_GET['date_range']) : 'today';
        
        // 构建查询条件
        $whereConditions = array();
        $params = array();
        
        // 数据隔离
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = 'merchant_id = ?';
            $params[] = $currentUser['profile_id'];
        }
        
        // 日期范围
        switch ($dateRange) {
            case 'today':
                $whereConditions[] = 'DATE(created_at) = CURDATE()';
                break;
            case 'yesterday':
                $whereConditions[] = 'DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)';
                break;
            case 'week':
                $whereConditions[] = 'created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                break;
            case 'month':
                $whereConditions[] = 'created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                break;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // 获取统计数据
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_transactions,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as success_transactions,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_transactions,
                SUM(CASE WHEN status = 'success' THEN amount ELSE 0 END) as total_amount,
                AVG(CASE WHEN status = 'success' THEN amount ELSE NULL END) as avg_amount
             FROM transactions $whereClause",
            $params
        );
        
        if (!$stats) {
            $stats = array(
                'total_transactions' => 0,
                'success_transactions' => 0,
                'failed_transactions' => 0,
                'pending_transactions' => 0,
                'total_amount' => 0,
                'avg_amount' => 0
            );
        }
        
        // 记录操作日志
        $auth->logUserAction('view_transaction_stats', 'transaction', 0, '查看交易统计');
        
        $auth->sendSuccess($stats, '交易统计获取成功');
        
    } catch (Exception $e) {
        error_log('获取交易统计失败: ' . $e->getMessage());
        $auth->sendError('获取交易统计失败: ' . $e->getMessage(), 500);
    }
}

// 获取交易详情
function handleGetTransactionDetail($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('transaction.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        $transactionId = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if (!$transactionId) {
            $auth->sendError('缺少交易ID', 400);
            return;
        }
        
        // 构建查询条件
        $whereConditions = array('t.id = ?');
        $params = array($transactionId);
        
        // 数据隔离
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = 't.merchant_id = ?';
            $params[] = $currentUser['profile_id'];
        }
        
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        
        // 获取交易详情
        $transaction = $db->fetch(
            "SELECT t.*, 
                    m.company_name as merchant_name,
                    u.real_name as merchant_real_name,
                    u.email as merchant_email
             FROM transactions t
             LEFT JOIN merchants m ON t.merchant_id = m.id
             LEFT JOIN users u ON m.user_id = u.id
             $whereClause",
            $params
        );
        
        if (!$transaction) {
            $auth->sendError('交易不存在或无权限访问', 404);
            return;
        }
        
        // 记录操作日志
        $auth->logUserAction('view_transaction_detail', 'transaction', $transactionId, '查看交易详情');
        
        $auth->sendSuccess($transaction, '交易详情获取成功');
        
    } catch (Exception $e) {
        error_log('获取交易详情失败: ' . $e->getMessage());
        $auth->sendError('获取交易详情失败: ' . $e->getMessage(), 500);
    }
}
?> 