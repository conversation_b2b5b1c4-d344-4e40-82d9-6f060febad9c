<?php
/**
 * API版本管理器
 * 支持多版本API并存、版本控制、向后兼容性管理
 * 
 * <AUTHOR> System Team
 * @version 1.0.0
 * @since Day 14
 */

class ApiVersionManager {
    private $config;
    private $currentVersion;
    private $supportedVersions;
    private $deprecatedVersions;
    private $versionMappings;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->config = $this->loadConfig();
        $this->currentVersion = $this->config['current_version'];
        $this->supportedVersions = $this->config['supported_versions'];
        $this->deprecatedVersions = $this->config['deprecated_versions'];
        $this->versionMappings = $this->config['version_mappings'];
    }
    
    /**
     * 加载配置
     */
    private function loadConfig() {
        return array(
            'current_version' => '1.0',
            'supported_versions' => array('1.0', '1.1', '2.0'),
            'deprecated_versions' => array(
                '0.9' => array(
                    'sunset_date' => '2024-12-31',
                    'message' => 'API v0.9 will be discontinued on December 31, 2024'
                )
            ),
            'version_mappings' => array(
                '1.0' => array(
                    'namespace' => 'V1',
                    'path' => '/v1',
                    'features' => array('basic_auth', 'json_response')
                ),
                '1.1' => array(
                    'namespace' => 'V1_1',
                    'path' => '/v1.1',
                    'features' => array('basic_auth', 'json_response', 'pagination', 'filtering')
                ),
                '2.0' => array(
                    'namespace' => 'V2',
                    'path' => '/v2',
                    'features' => array('oauth2', 'json_response', 'pagination', 'filtering', 'bulk_operations', 'webhooks')
                )
            ),
            'default_version' => '1.0',
            'version_header' => 'X-API-Version',
            'accept_header_pattern' => '/application\/vnd\.paypal\.v(\d+(?:\.\d+)?)(?:\+json)?/'
        );
    }
    
    /**
     * 检测请求的API版本
     */
    public function detectVersion() {
        $version = null;
        
        // 1. 检查URL路径中的版本
        $pathVersion = $this->detectVersionFromPath();
        if ($pathVersion) {
            $version = $pathVersion;
        }
        
        // 2. 检查HTTP头中的版本
        if (!$version) {
            $headerVersion = $this->detectVersionFromHeader();
            if ($headerVersion) {
                $version = $headerVersion;
            }
        }
        
        // 3. 检查Accept头中的版本
        if (!$version) {
            $acceptVersion = $this->detectVersionFromAcceptHeader();
            if ($acceptVersion) {
                $version = $acceptVersion;
            }
        }
        
        // 4. 使用默认版本
        if (!$version) {
            $version = $this->config['default_version'];
        }
        
        // 验证版本
        if (!$this->isVersionSupported($version)) {
            throw new ApiVersionException("Unsupported API version: {$version}", 400);
        }
        
        // 检查是否已弃用
        $this->checkDeprecation($version);
        
        $this->currentVersion = $version;
        return $version;
    }
    
    /**
     * 从URL路径检测版本
     */
    private function detectVersionFromPath() {
        $path = $_SERVER['REQUEST_URI'];
        
        foreach ($this->versionMappings as $version => $mapping) {
            if (strpos($path, $mapping['path']) !== false) {
                return $version;
            }
        }
        
        return null;
    }
    
    /**
     * 从HTTP头检测版本
     */
    private function detectVersionFromHeader() {
        $headers = $this->getAllHeaders();
        $versionHeader = $this->config['version_header'];
        
        if (isset($headers[$versionHeader])) {
            return $headers[$versionHeader];
        }
        
        return null;
    }
    
    /**
     * 从Accept头检测版本
     */
    private function detectVersionFromAcceptHeader() {
        $headers = $this->getAllHeaders();
        
        if (isset($headers['Accept'])) {
            $pattern = $this->config['accept_header_pattern'];
            if (preg_match($pattern, $headers['Accept'], $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }
    
    /**
     * 检查版本是否支持
     */
    public function isVersionSupported($version) {
        return in_array($version, $this->supportedVersions);
    }
    
    /**
     * 检查版本弃用状态
     */
    private function checkDeprecation($version) {
        if (isset($this->deprecatedVersions[$version])) {
            $deprecation = $this->deprecatedVersions[$version];
            
            // 添加弃用警告头
            header('Warning: 299 - "Deprecated API version"');
            header('Sunset: ' . $deprecation['sunset_date']);
            header('X-API-Deprecation-Message: ' . $deprecation['message']);
            
            // 记录弃用访问日志
            $this->logDeprecatedAccess($version);
        }
    }
    
    /**
     * 记录弃用访问
     */
    private function logDeprecatedAccess($version) {
        $logData = array(
            'version' => $version,
            'ip' => $this->getClientIp(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'endpoint' => isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '',
            'timestamp' => date('Y-m-d H:i:s')
        );
        
        // 记录到日志文件
        $logFile = dirname(dirname(__FILE__)) . '/logs/deprecated_api_access.log';
        error_log(json_encode($logData) . "\n", 3, $logFile);
    }
    
    /**
     * 获取版本特性
     */
    public function getVersionFeatures($version = null) {
        $version = $version ?: $this->currentVersion;
        
        if (isset($this->versionMappings[$version])) {
            return $this->versionMappings[$version]['features'];
        }
        
        return array();
    }
    
    /**
     * 检查版本是否支持特性
     */
    public function hasFeature($feature, $version = null) {
        $features = $this->getVersionFeatures($version);
        return in_array($feature, $features);
    }
    
    /**
     * 获取版本命名空间
     */
    public function getVersionNamespace($version = null) {
        $version = $version ?: $this->currentVersion;
        
        if (isset($this->versionMappings[$version])) {
            return $this->versionMappings[$version]['namespace'];
        }
        
        return 'V1'; // 默认命名空间
    }
    
    /**
     * 版本化的响应处理
     */
    public function processResponse($data, $version = null) {
        $version = $version ?: $this->currentVersion;
        
        // 根据版本调整响应格式
        switch ($version) {
            case '1.0':
                return $this->processV1Response($data);
                
            case '1.1':
                return $this->processV1_1Response($data);
                
            case '2.0':
                return $this->processV2Response($data);
                
            default:
                return $data;
        }
    }
    
    /**
     * 处理v1.0响应
     */
    private function processV1Response($data) {
        // v1.0 简单响应格式
        if (is_array($data) && isset($data['data'])) {
            return array(
                'status' => $data['success'] ? 'success' : 'error',
                'result' => $data['data'],
                'message' => isset($data['message']) ? $data['message'] : '');
        }
        
        return $data;
    }
    
    /**
     * 处理v1.1响应
     */
    private function processV1_1Response($data) {
        // v1.1 添加分页和元数据
        if (is_array($data)) {
            $response = array(
                'success' => isset($data['success']) ? $data['success'] : true,
                'data' => isset($data['data']) ? $data['data'] : $data,
                'meta' => array(
                    'version' => '1.1',
                    'timestamp' => time()
                )
            );
            
            // 添加分页信息
            if (isset($data['pagination'])) {
                $response['meta']['pagination'] = $data['pagination'];
            }
            
            return $response;
        }
        
        return $data;
    }
    
    /**
     * 处理v2.0响应
     */
    private function processV2Response($data) {
        // v2.0 完整的REST响应格式
        $response = array(
            'apiVersion' => '2.0',
            'context' => $_SERVER['REQUEST_URI'],
            'id' => $this->generateRequestId(),
            'method' => $_SERVER['REQUEST_METHOD'],
            'data' => isset($data['data']) ? $data['data'] : $data);
        
        // 添加错误信息
        if (isset($data['success']) && !$data['success']) {
            $response['error'] = array(
                'code' => isset($data['error']['code']) ? $data['error']['code'] : 500,
                'message' => isset($data['error']['message']) ? $data['error']['message'] : 'Unknown error',
                'details' => isset($data['error']['details']) ? $data['error']['details'] : null);
            unset($response['data']);
        }
        
        // 添加分页信息
        if (isset($data['pagination'])) {
            $response['pageInfo'] = array(
                'totalResults' => $data['pagination']['total_items'],
                'resultsPerPage' => $data['pagination']['page_size'],
                'startIndex' => ($data['pagination']['current_page'] - 1) * $data['pagination']['page_size']
            );
        }
        
        return $response;
    }
    
    /**
     * 版本化的请求处理
     */
    public function processRequest($input, $version = null) {
        $version = $version ?: $this->currentVersion;
        
        // 根据版本调整请求处理
        switch ($version) {
            case '1.0':
                return $this->processV1Request($input);
                
            case '1.1':
                return $this->processV1_1Request($input);
                
            case '2.0':
                return $this->processV2Request($input);
                
            default:
                return $input;
        }
    }
    
    /**
     * 处理v1.0请求
     */
    private function processV1Request($input) {
        // v1.0 简单处理
        return $input;
    }
    
    /**
     * 处理v1.1请求
     */
    private function processV1_1Request($input) {
        // v1.1 添加过滤和排序支持
        if (is_array($input)) {
            // 标准化分页参数
            if (isset($input['page'])) {
                $input['page'] = max(1, intval($input['page']));
            }
            if (isset($input['limit'])) {
                $input['limit'] = min(100, max(1, intval($input['limit'])));
            }
        }
        
        return $input;
    }
    
    /**
     * 处理v2.0请求
     */
    private function processV2Request($input) {
        // v2.0 高级处理
        if (is_array($input)) {
            // 支持批量操作
            if (isset($input['batch'])) {
                $input['_batch_mode'] = true;
            }
            
            // 支持字段选择
            if (isset($input['fields'])) {
                $input['_selected_fields'] = explode(',', $input['fields']);
            }
            
            // 支持嵌套资源展开
            if (isset($input['expand'])) {
                $input['_expand'] = explode(',', $input['expand']);
            }
        }
        
        return $input;
    }
    
    /**
     * 获取API版本信息
     */
    public function getVersionInfo() {
        return array(
            'current_version' => $this->currentVersion,
            'supported_versions' => $this->supportedVersions,
            'deprecated_versions' => array_keys($this->deprecatedVersions),
            'features' => $this->getVersionFeatures(),
            'namespace' => $this->getVersionNamespace()
        );
    }
    
    /**
     * 版本兼容性检查
     */
    public function checkCompatibility($requiredVersion, $currentVersion = null) {
        $currentVersion = $currentVersion ?: $this->currentVersion;
        
        // 简单的版本比较
        return version_compare($currentVersion, $requiredVersion, '>=');
    }
    
    /**
     * 生成版本化的错误响应
     */
    public function createErrorResponse($code, $message, $details = null) {
        $version = $this->currentVersion;
        
        switch ($version) {
            case '1.0':
                return array(
                    'status' => 'error',
                    'error_code' => $code,
                    'error_message' => $message
                );
                
            case '1.1':
                return array(
                    'success' => false,
                    'error' => array(
                        'code' => $code,
                        'message' => $message
                    ),
                    'meta' => array(
                        'version' => '1.1',
                        'timestamp' => time()
                    )
                );
                
            case '2.0':
                $error = array(
                    'apiVersion' => '2.0',
                    'error' => array(
                        'code' => $code,
                        'message' => $message
                    ),
                    'id' => $this->generateRequestId()
                );
                
                if ($details) {
                    $error['error']['details'] = $details;
                }
                
                return $error;
                
            default:
                return array(
                    'error' => array(
                        'code' => $code,
                        'message' => $message
                    )
                );
        }
    }
    
    /**
     * 获取所有HTTP头
     */
    private function getAllHeaders() {
        $headers = array();
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) == 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }
        return $headers;
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        $headers = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP');
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                return trim($ips[0]);
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
    }
    
    /**
     * 生成请求ID
     */
    private function generateRequestId() {
        return uniqid('req_', true);
    }
    
    /**
     * 获取当前版本
     */
    public function getCurrentVersion() {
        return $this->currentVersion;
    }
    
    /**
     * 设置当前版本
     */
    public function setCurrentVersion($version) {
        if (!$this->isVersionSupported($version)) {
            throw new ApiVersionException("Unsupported API version: {$version}", 400);
        }
        
        $this->currentVersion = $version;
    }
    
    /**
     * 获取版本迁移指南
     */
    public function getMigrationGuide($fromVersion, $toVersion) {
        $guides = array(
            '1.0_to_1.1' => array(
                'breaking_changes' => array(),
                'new_features' => array('pagination', 'filtering'),
                'deprecated_features' => array(),
                'migration_steps' => array(
                    '1. 更新API版本号到1.1',
                    '2. 使用新的分页参数',
                    '3. 利用过滤功能优化查询'
                )
            ),
            '1.1_to_2.0' => array(
                'breaking_changes' => array(
                    'Response format changed',
                    'Authentication method updated to OAuth2'
                ),
                'new_features' => array('bulk_operations', 'webhooks', 'field_selection'),
                'deprecated_features' => array('basic_auth'),
                'migration_steps' => array(
                    '1. 迁移到OAuth2认证',
                    '2. 更新响应处理逻辑',
                    '3. 使用新的批量操作API',
                    '4. 配置Webhook端点'
                )
            )
        );
        
        $key = $fromVersion . '_to_' . $toVersion;
        return isset($guides[$key]) ? $guides[$key] : null;
    }
}

/**
 * API版本异常类
 */
class ApiVersionException extends Exception {
    public function __construct($message, $code = 400, Exception $previous = null) {
        parent::__construct($message, $code, $previous);
    }
}
?> 