<?php
/**
 * 支付流程管理API接口
 * 
 * 提供完整的支付流程操作接口：
 * 1. 创建支付请求
 * 2. 查询支付状态
 * 3. 处理支付通知
 * 4. 取消支付请求
 * 5. 获取支付统计
 * 6. 清理过期请求
 * 
 * <AUTHOR> System
 * @version 1.0
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/PaymentFlowManager.php';
require_once __DIR__ . '/auth.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $paymentFlowManager = new PaymentFlowManager();
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    switch ($action) {
        case 'create_payment':
            handleCreatePayment($paymentFlowManager);
            break;
            
        case 'query_payment':
            handleQueryPayment($paymentFlowManager);
            break;
            
        case 'payment_notify':
            handlePaymentNotify($paymentFlowManager);
            break;
            
        case 'cancel_payment':
            handleCancelPayment($paymentFlowManager);
            break;
            
        case 'payment_statistics':
            handlePaymentStatistics($paymentFlowManager);
            break;
            
        case 'clean_expired':
            handleCleanExpired($paymentFlowManager);
            break;
            
        case 'payment_list':
            handlePaymentList($paymentFlowManager);
            break;
            
        case 'retry_notification':
            handleRetryNotification($paymentFlowManager);
            break;
            
        default:
            throw new Exception('无效的操作类型');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'success' => false,
        'error' => $e->getMessage(),
        'error_code' => 'API_ERROR'
    ));
}

/**
 * 创建支付请求
 */
function handleCreatePayment($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        throw new Exception('无效的JSON数据');
    }
    
    // 验证必需参数
    $required = array('merchant_id', 'amount', 'merchant_order_no');
    foreach ($required as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            throw new Exception("缺少必需参数: {$field}");
        }
    }
    
    // 权限验证（如果需要）
    $currentUser = getCurrentUser();
    if ($currentUser && $currentUser['user_type'] === 'merchant') {
        if ($input['merchant_id'] != $currentUser['profile_id']) {
            throw new Exception('无权为其他商户创建支付请求');
        }
    }
    
    $result = $paymentFlowManager->createPaymentRequest($input);
    
    if ($result['success']) {
        http_response_code(201);
    } else {
        http_response_code(400);
    }
    
    echo json_encode($result);
}

/**
 * 查询支付请求状态
 */
function handleQueryPayment($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('只支持GET请求');
    }
    
    $requestId = isset($_GET['request_id']) ? $_GET['request_id'] : '';
    if (!$requestId) {
        throw new Exception('请求ID不能为空');
    }
    
    // 权限验证
    $merchantId = null;
    $currentUser = getCurrentUser();
    if ($currentUser && $currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    }
    
    $result = $paymentFlowManager->queryPaymentRequest($requestId, $merchantId);
    
    if (!$result['success']) {
        http_response_code(404);
    }
    
    echo json_encode($result);
}

/**
 * 处理支付通知（第三方支付平台回调）
 */
function handlePaymentNotify($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    // 获取通知数据（可能是JSON或表单数据）
    $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';
    
    if (strpos($contentType, 'application/json') !== false) {
        $notifyData = json_decode(file_get_contents('php://input'), true);
    } else {
        $notifyData = $_POST;
    }
    
    if (!$notifyData) {
        throw new Exception('无效的通知数据');
    }
    
    // 记录原始通知数据（用于调试）
    error_log('Payment Notification Received: ' . json_encode($notifyData));
    
    $result = $paymentFlowManager->handlePaymentNotification($notifyData);
    
    if ($result['success']) {
        // 返回成功响应（第三方平台期望的格式）
        echo json_encode(array(
            'code' => 'SUCCESS',
            'message' => 'OK'
        ));
    } else {
        http_response_code(400);
        echo json_encode(array(
            'code' => 'FAIL',
            'message' => $result['error']
        ));
    }
}

/**
 * 取消支付请求
 */
function handleCancelPayment($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        throw new Exception('无效的JSON数据');
    }
    
    $requestId = $input['request_id'] ?? '';
    $reason = $input['reason'] ?? '';
    
    if (!$requestId) {
        throw new Exception('请求ID不能为空');
    }
    
    // 权限验证
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        throw new Exception('未登录');
    }
    
    $merchantId = null;
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        $merchantId = $input['merchant_id'] ?? null;
        if (!$merchantId) {
            throw new Exception('管理员取消支付请求时必须指定商户ID');
        }
    } else {
        throw new Exception('无权限操作');
    }
    
    $result = $paymentFlowManager->cancelPaymentRequest($requestId, $merchantId, $reason);
    
    if (!$result['success']) {
        http_response_code(400);
    }
    
    echo json_encode($result);
}

/**
 * 获取支付统计数据
 */
function handlePaymentStatistics($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('只支持GET请求');
    }
    
    // 权限验证
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        throw new Exception('未登录');
    }
    
    $merchantId = null;
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        $merchantId = $_GET['merchant_id'] ?? null;
    } else {
        throw new Exception('无权限访问');
    }
    
    $filters = array(
        'start_date' => $_GET['start_date'] ?? '',
        'end_date' => $_GET['end_date'] ?? ''
    );
    
    $result = $paymentFlowManager->getPaymentFlowStatistics($merchantId, $filters);
    
    if (!$result['success']) {
        http_response_code(400);
    }
    
    echo json_encode($result);
}

/**
 * 清理过期支付请求（管理员功能）
 */
function handleCleanExpired($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    // 权限验证
    $currentUser = getCurrentUser();
    if (!$currentUser || $currentUser['user_type'] !== 'admin') {
        throw new Exception('只有管理员可以执行此操作');
    }
    
    $result = $paymentFlowManager->cleanExpiredRequests();
    
    if (!$result['success']) {
        http_response_code(400);
    }
    
    echo json_encode($result);
}

/**
 * 获取支付请求列表
 */
function handlePaymentList($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('只支持GET请求');
    }
    
    // 权限验证
    $currentUser = getCurrentUser();
    if (!$currentUser) {
        throw new Exception('未登录');
    }
    
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 20);
    $merchantId = null;
    
    if ($currentUser['user_type'] === 'merchant') {
        $merchantId = $currentUser['profile_id'];
    } elseif ($currentUser['user_type'] === 'admin') {
        $merchantId = $_GET['merchant_id'] ?? null;
    } else {
        throw new Exception('无权限访问');
    }
    
    $filters = array(
        'status' => $_GET['status'] ?? '',
        'start_date' => $_GET['start_date'] ?? '',
        'end_date' => $_GET['end_date'] ?? '',
        'search' => $_GET['search'] ?? ''
    );
    
    $result = getPaymentRequestList($merchantId, $page, $limit, $filters);
    
    if (!$result['success']) {
        http_response_code(400);
    }
    
    echo json_encode($result);
}

/**
 * 重试支付通知
 */
function handleRetryNotification($paymentFlowManager) {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        throw new Exception('无效的JSON数据');
    }
    
    $requestId = $input['request_id'] ?? '';
    if (!$requestId) {
        throw new Exception('请求ID不能为空');
    }
    
    // 权限验证
    $currentUser = getCurrentUser();
    if (!$currentUser || !in_array($currentUser['user_type'], array('admin', 'merchant'))) {
        throw new Exception('无权限操作');
    }
    
    $result = retryPaymentNotification($requestId, $currentUser);
    
    if (!$result['success']) {
        http_response_code(400);
    }
    
    echo json_encode($result);
}

// ==================== 辅助函数 ====================

/**
 * 获取当前登录用户信息
 */
function getCurrentUser() {
    // 从Authorization头获取token
    $headers = getallheaders();
    $token = null;
    
    if (isset($headers['Authorization'])) {
        $auth = $headers['Authorization'];
        if (strpos($auth, 'Bearer ') === 0) {
            $token = substr($auth, 7);
        }
    }
    
    if (!$token) {
        return null;
    }
    
    // 验证token并获取用户信息
    try {
        $auth = new Auth();
        return $auth->validateToken($token);
    } catch (Exception $e) {
        return null;
    }
}

/**
 * 获取支付请求列表
 */
function getPaymentRequestList($merchantId, $page, $limit, $filters) {
    try {
        $db = Database::getInstance();
        
        $offset = ($page - 1) * $limit;
        $whereConditions = array();
        $params = array();
        
        // 商户权限控制
        if ($merchantId !== null) {
            $whereConditions[] = 'pr.merchant_id = ?';
            $params[] = $merchantId;
        }
        
        // 状态筛选
        if (!empty($filters['status'])) {
            $whereConditions[] = 'pr.status = ?';
            $params[] = $filters['status'];
        }
        
        // 时间范围筛选
        if (!empty($filters['start_date'])) {
            $whereConditions[] = 'pr.created_at >= ?';
            $params[] = $filters['start_date'] . ' 00:00:00';
        }
        
        if (!empty($filters['end_date'])) {
            $whereConditions[] = 'pr.created_at <= ?';
            $params[] = $filters['end_date'] . ' 23:59:59';
        }
        
        // 搜索筛选
        if (!empty($filters['search'])) {
            $whereConditions[] = '(pr.request_id LIKE ? OR pr.order_no LIKE ? OR m.company_name LIKE ?)';
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);
        
        // 获取总数
        $totalSql = "SELECT COUNT(*) as total FROM payment_requests pr 
                     LEFT JOIN merchants m ON pr.merchant_id = m.id 
                     {$whereClause}";
        $totalResult = $db->fetch($totalSql, $params);
        $total = $totalResult['total'];
        
        // 获取列表数据
        $listSql = "SELECT pr.*, m.company_name as merchant_name, aa.account_name, aa.account_number
                    FROM payment_requests pr
                    LEFT JOIN merchants m ON pr.merchant_id = m.id
                    LEFT JOIN alipay_accounts aa ON pr.alipay_account_id = aa.id
                    {$whereClause}
                    ORDER BY pr.created_at DESC
                    LIMIT {$limit} OFFSET {$offset}";
        
        $requests = $db->fetchAll($listSql, $params);
        
        // 格式化数据
        $formattedRequests = array();
        foreach ($requests as $request) {
            $formattedRequests[] = formatPaymentRequestForList($request);
        }
        
        return array(
            'success' => true,
            'data' => array(
                'requests' => $formattedRequests,
                'pagination' => array(
                    'current_page' => $page,
                    'total_pages' => ceil($total / $limit),
                    'total_records' => $total,
                    'per_page' => $limit
                )
            )
        );
        
    } catch (Exception $e) {
        return array(
            'success' => false,
            'error' => $e->getMessage(),
            'error_code' => 'GET_LIST_FAILED'
        );
    }
}

/**
 * 重试支付通知
 */
function retryPaymentNotification($requestId, $currentUser) {
    try {
        $db = Database::getInstance();
        
        // 查找支付请求
        $whereConditions = array('request_id = ?');
        $params = array($requestId);
        
        // 权限控制
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = 'merchant_id = ?';
            $params[] = $currentUser['profile_id'];
        }
        
        $request = $db->fetch(
            "SELECT * FROM payment_requests WHERE " . implode(' AND ', $whereConditions),
            $params
        );
        
        if (!$request) {
            throw new Exception('支付请求不存在');
        }
        
        if ($request['status'] !== 'paid') {
            throw new Exception('只能重试已支付的订单通知');
        }
        
        if (empty($request['notification_url'])) {
            throw new Exception('该订单没有配置通知地址');
        }
        
        // 构建通知数据
        $notificationData = array(
            'request_id' => $request['request_id'],
            'merchant_order_no' => $request['order_no'],
            'amount' => $request['amount'],
            'actual_amount' => $request['amount'] - $request['fee'],
            'fee' => $request['fee'],
            'status' => 'paid',
            'paid_at' => $request['paid_at'],
            'timestamp' => time()
        );
        
        // 生成签名
        $apiKey = $db->fetch(
            "SELECT secret_key FROM api_keys WHERE merchant_id = ? AND status = 'active' LIMIT 1",
            array($request['merchant_id'])
        );
        
        if ($apiKey) {
            ksort($notificationData);
            $signString = '';
            foreach ($notificationData as $key => $value) {
                if ($value !== '') {
                    $signString .= $key . '=' . $value . '&';
                }
            }
            $signString .= 'key=' . $apiKey['secret_key'];
            $notificationData['sign'] = strtoupper(hash('sha256', $signString));
        }
        
        // 发送通知
        $response = sendHttpRequest($request['notification_url'], $notificationData);
        
        // 记录重试日志
        $db->insert('system_logs', array(
            'user_id' => $currentUser['id'],
            'action' => 'retry_payment_notification',
            'target_type' => 'payment_request',
            'target_id' => $request['id'],
            'description' => '手动重试支付通知',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'extra_data' => json_encode(array(
                'notification_url' => $request['notification_url'],
                'response' => $response
            ))
        ));
        
        return array(
            'success' => true,
            'message' => '通知已重新发送',
            'data' => array(
                'response' => $response
            )
        );
        
    } catch (Exception $e) {
        return array(
            'success' => false,
            'error' => $e->getMessage(),
            'error_code' => 'RETRY_NOTIFICATION_FAILED'
        );
    }
}

/**
 * 格式化支付请求列表数据
 */
function formatPaymentRequestForList($request) {
    return array(
        'id' => $request['id'],
        'request_id' => $request['request_id'],
        'merchant_id' => $request['merchant_id'],
        'merchant_name' => $request['merchant_name'] ?? '',
        'order_no' => $request['order_no'],
        'amount' => floatval($request['amount']),
        'actual_amount' => floatval($request['actual_amount'] ?? 0),
        'fee' => floatval($request['fee'] ?? 0),
        'status' => $request['status'],
        'status_text' => getStatusText($request['status']),
        'qr_code_url' => $request['qr_code_url'] ?? '',
        'subject' => $request['subject'] ?? '',
        'client_ip' => $request['client_ip'] ?? '',
        'created_at' => $request['created_at'],
        'paid_at' => $request['paid_at'],
        'expired_at' => $request['expired_at'],
        'payment_account' => array(
            'account_name' => $request['account_name'] ?? '',
            'account_number' => $request['account_number'] ?? ''
        )
    );
}

/**
 * 获取状态文本
 */
function getStatusText($status) {
    $statusMap = array(
        'pending' => '待支付',
        'paid' => '已支付',
        'expired' => '已过期',
        'cancelled' => '已取消'
    );
    
    return $statusMap[$status] ?? $status;
}

/**
 * 发送HTTP请求
 */
function sendHttpRequest($url, $data) {
    $options = array(
        'http' => array(
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data),
            'timeout' => 30
        )
    );
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    if ($result === false) {
        throw new Exception('发送HTTP请求失败');
    }
    
    return $result;
} 