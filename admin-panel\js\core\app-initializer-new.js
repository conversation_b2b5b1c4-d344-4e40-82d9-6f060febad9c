/**
 * 新版应用初始化器 - 配合全新UI设计
 */
class AppInitializer {
    static async initialize(domainInfo) {
        console.log('🚀 新版应用初始化器启动:', domainInfo.tenant_type);
        
        try {
            // 设置全局租户信息
            window.TENANT_INFO = domainInfo;
            
            // 检查认证状态
            const isAuthenticated = await this.checkAuthentication();
            
            if (isAuthenticated) {
                await this.showMainApp(domainInfo);
            } else {
                this.showLoginForm();
            }
            
            console.log('✅ 应用初始化完成');
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this.showError('系统初始化失败，请刷新页面重试');
        }
    }
    
    static async checkAuthentication() {
        // 检查本地存储的认证信息
        const authToken = localStorage.getItem('auth_token');
        const userInfo = localStorage.getItem('user_info');
        
        if (authToken && userInfo) {
            try {
                // 验证token是否有效
                const response = await fetch('./api/auth.php?action=verify_token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const result = await response.json();
                return result.code === 200;
            } catch (error) {
                console.log('Token验证失败:', error);
                return false;
            }
        }
        
        return false;
    }
    
    static showLoginForm() {
        console.log('🔐 显示登录界面');
        
        document.getElementById('loginContainer').classList.remove('d-none');
        document.getElementById('appContainer').classList.add('d-none');
        
        // 绑定登录表单事件
        const loginForm = document.getElementById('loginForm');
        loginForm.addEventListener('submit', this.handleLogin.bind(this));
    }
    
    static async handleLogin(event) {
        event.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const errorDiv = document.getElementById('loginError');
        
        try {
            const response = await fetch('./api/auth.php?action=login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    tenant_type: window.TENANT_INFO.tenant_type
                })
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                // 保存认证信息
                localStorage.setItem('auth_token', result.data.token);
                localStorage.setItem('user_info', JSON.stringify(result.data.user));
                
                // 显示主应用
                await this.showMainApp(window.TENANT_INFO);
            } else {
                errorDiv.textContent = result.message || '登录失败';
                errorDiv.classList.remove('d-none');
            }
        } catch (error) {
            console.error('登录请求失败:', error);
            errorDiv.textContent = '网络错误，请重试';
            errorDiv.classList.remove('d-none');
        }
    }
    
    static async showMainApp(domainInfo) {
        console.log('🏠 显示主应用界面');
        
        document.getElementById('loginContainer').classList.add('d-none');
        document.getElementById('appContainer').classList.remove('d-none');
        
        // 生成菜单
        this.generateMenu(domainInfo.tenant_type);
        
        // 设置用户信息
        this.setUserInfo();
        
        // 绑定事件
        this.bindEvents();
        
        // 加载默认页面
        await this.loadPage('dashboard');
    }
    
    static generateMenu(tenantType) {
        const menuContainer = document.getElementById('sidebarMenu');
        
        // 根据租户类型生成不同的菜单
        const menus = {
            platform_admin: [
                { id: 'dashboard', icon: 'bi-speedometer2', text: '仪表板', active: true },
                { id: 'merchants', icon: 'bi-shop', text: '商户管理' },
                { id: 'transactions', icon: 'bi-credit-card', text: '交易记录' },
                { id: 'users', icon: 'bi-people', text: '用户管理' },
                { id: 'settings', icon: 'bi-gear', text: '系统设置' }
            ],
            system_admin: [
                { id: 'dashboard', icon: 'bi-speedometer2', text: '系统概览', active: true },
                { id: 'platforms', icon: 'bi-building', text: '平台管理' },
                { id: 'system-config', icon: 'bi-gear-fill', text: '系统配置' },
                { id: 'logs', icon: 'bi-file-text', text: '系统日志' }
            ],
            provider: [
                { id: 'dashboard', icon: 'bi-speedometer2', text: '数据概览', active: true },
                { id: 'channels', icon: 'bi-diagram-3', text: '通道管理' },
                { id: 'settlements', icon: 'bi-cash-stack', text: '结算管理' },
                { id: 'reports', icon: 'bi-graph-up', text: '报表统计' }
            ],
            merchant: [
                { id: 'dashboard', icon: 'bi-speedometer2', text: '交易概览', active: true },
                { id: 'orders', icon: 'bi-receipt', text: '订单管理' },
                { id: 'payments', icon: 'bi-credit-card-2-front', text: '支付记录' },
                { id: 'account', icon: 'bi-person-gear', text: '账户设置' }
            ]
        };
        
        const menuItems = menus[tenantType] || menus.platform_admin;
        
        menuContainer.innerHTML = menuItems.map(item => `
            <a href="#${item.id}" class="menu-item ${item.active ? 'active' : ''}" data-page="${item.id}">
                <i class="${item.icon}"></i> ${item.text}
            </a>
        `).join('');
        
        // 绑定菜单点击事件
        menuContainer.addEventListener('click', (e) => {
            if (e.target.classList.contains('menu-item')) {
                e.preventDefault();
                const page = e.target.dataset.page;
                this.navigateTo(page);
            }
        });
    }
    
    static setUserInfo() {
        const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
        const displayName = userInfo.username || '管理员';
        document.getElementById('userDisplayName').textContent = displayName;
    }
    
    static bindEvents() {
        // 侧边栏切换
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                document.getElementById('sidebar').classList.toggle('show');
            });
        }
        
        // 退出登录
        document.getElementById('logoutBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.logout();
        });
    }
    
    static async navigateTo(page) {
        // 更新菜单状态
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-page="${page}"]`).classList.add('active');
        
        // 更新页面标题
        const titles = {
            dashboard: '仪表板',
            merchants: '商户管理',
            transactions: '交易记录',
            users: '用户管理',
            settings: '系统设置',
            platforms: '平台管理',
            'system-config': '系统配置',
            logs: '系统日志',
            channels: '通道管理',
            settlements: '结算管理',
            reports: '报表统计',
            orders: '订单管理',
            payments: '支付记录',
            account: '账户设置'
        };
        
        document.getElementById('pageTitle').textContent = titles[page] || page;
        
        // 加载页面内容
        await this.loadPage(page);
    }
    
    static async loadPage(page) {
        const contentArea = document.getElementById('contentArea');
        
        // 显示加载动画
        contentArea.innerHTML = `
            <div class="loading">
                <div class="spinner"></div>
            </div>
        `;
        
        try {
            // 根据页面类型加载不同内容
            let content = '';
            
            switch (page) {
                case 'dashboard':
                    content = await this.getDashboardContent();
                    break;
                case 'merchants':
                    content = await this.getMerchantsContent();
                    break;
                case 'transactions':
                    content = await this.getTransactionsContent();
                    break;
                default:
                    content = `
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">${page}</h5>
                            </div>
                            <div class="card-body">
                                <p>页面内容开发中...</p>
                            </div>
                        </div>
                    `;
            }
            
            contentArea.innerHTML = content;
        } catch (error) {
            console.error('页面加载失败:', error);
            contentArea.innerHTML = `
                <div class="alert alert-danger">
                    <h5>页面加载失败</h5>
                    <p>${error.message}</p>
                </div>
            `;
        }
    }
    
    static async getDashboardContent() {
        return `
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="stats-number">156</div>
                        <div class="stats-label">总商户数</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="stats-number">2,847</div>
                        <div class="stats-label">今日交易</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="stats-number">¥1.2M</div>
                        <div class="stats-label">交易金额</div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stats-card">
                        <div class="stats-number">98.5%</div>
                        <div class="stats-label">成功率</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">最近交易</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>交易ID</th>
                                    <th>商户</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#TXN001</td>
                                    <td>测试商户A</td>
                                    <td>¥299.00</td>
                                    <td><span class="badge bg-success">成功</span></td>
                                    <td>2024-06-28 14:30</td>
                                </tr>
                                <tr>
                                    <td>#TXN002</td>
                                    <td>测试商户B</td>
                                    <td>¥156.50</td>
                                    <td><span class="badge bg-success">成功</span></td>
                                    <td>2024-06-28 14:25</td>
                                </tr>
                                <tr>
                                    <td>#TXN003</td>
                                    <td>测试商户C</td>
                                    <td>¥89.99</td>
                                    <td><span class="badge bg-warning">处理中</span></td>
                                    <td>2024-06-28 14:20</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }
    
    static async getMerchantsContent() {
        // 尝试从API获取商户数据
        try {
            const response = await fetch('./api/admin.php?action=get_merchants&page=1&limit=20&status=all', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                }
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                const merchants = result.data.merchants || [];
                const merchantRows = merchants.map(merchant => `
                    <tr>
                        <td>${merchant.id}</td>
                        <td>${merchant.name}</td>
                        <td><span class="badge bg-${merchant.status === 'active' ? 'success' : 'secondary'}">${merchant.status}</span></td>
                        <td>${merchant.created_at}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary">编辑</button>
                            <button class="btn btn-sm btn-outline-danger">禁用</button>
                        </td>
                    </tr>
                `).join('');
                
                return `
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">商户管理</h5>
                            <button class="btn btn-primary btn-sm">
                                <i class="bi-plus"></i> 添加商户
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>商户ID</th>
                                            <th>商户名称</th>
                                            <th>状态</th>
                                            <th>注册时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${merchantRows}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                throw new Error(result.message || '获取商户数据失败');
            }
        } catch (error) {
            console.error('获取商户数据失败:', error);
            return `
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">商户管理</h5>
                        <button class="btn btn-primary btn-sm">
                            <i class="bi-plus"></i> 添加商户
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6>数据加载失败</h6>
                            <p>${error.message}</p>
                            <button class="btn btn-sm btn-outline-primary" onclick="location.reload()">重试</button>
                        </div>
                    </div>
                </div>
            `;
        }
    }
    
    static async getTransactionsContent() {
        return `
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">交易记录</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="date" class="form-control" placeholder="开始日期">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" placeholder="结束日期">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select">
                                <option>全部状态</option>
                                <option>成功</option>
                                <option>失败</option>
                                <option>处理中</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary">查询</button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>交易ID</th>
                                    <th>商户</th>
                                    <th>金额</th>
                                    <th>手续费</th>
                                    <th>状态</th>
                                    <th>时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#TXN001</td>
                                    <td>测试商户A</td>
                                    <td>¥299.00</td>
                                    <td>¥8.97</td>
                                    <td><span class="badge bg-success">成功</span></td>
                                    <td>2024-06-28 14:30</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }
    
    static logout() {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_info');
        location.reload();
    }
    
    static showError(message) {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="alert alert-danger">
                <h5>系统错误</h5>
                <p>${message}</p>
                <button class="btn btn-outline-danger" onclick="location.reload()">刷新页面</button>
            </div>
        `;
    }
}

// 导出到全局
window.AppInitializer = AppInitializer;
