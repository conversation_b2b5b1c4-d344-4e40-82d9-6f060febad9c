<?php
/**
 * 收款指令管理器
 * 
 * 功能：
 * 1. 生成收款指令
 * 2. 分发指令给手机端
 * 3. 管理指令状态
 * 4. 处理指令回复
 * 
 * <AUTHOR> System
 * @version 1.0
 */

require_once dirname(dirname(__FILE__)) . '/config/database.php';

class PaymentInstructionManager {
    
    private $db;
    
    // 指令状态
    const STATUS_PENDING = 'pending';
    const STATUS_EXECUTING = 'executing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_EXPIRED = 'expired';
    const STATUS_FAILED = 'failed';
    
    // 指令类型
    const TYPE_FETCH_BILLS = 'fetch_bills';
    
    // 执行结果
    const RESULT_FOUND = 'found';
    const RESULT_NOT_FOUND = 'not_found';
    const RESULT_TIMEOUT = 'timeout';
    const RESULT_ERROR = 'error';
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * 创建收款指令
     * 
     * @param int $paymentRequestId 支付请求ID
     * @param int $alipayAccountId 支付宝账户ID
     * @param int $merchantId 商户ID
     * @param float $targetAmount 订单金额
     * @param float $payableAmount 用户应付金额
     * @param int $expireMinutes 过期时间（分钟）
     * @return array 创建结果
     */
    public function createInstruction($paymentRequestId, $alipayAccountId, $merchantId, $targetAmount, $payableAmount = null, $expireMinutes = 3) {
        try {
            // 生成唯一指令ID
            $instructionId = $this->generateInstructionId();
            
            // 计算过期时间
            $expiresAt = date('Y-m-d H:i:s', time() + ($expireMinutes * 60));
            
            // 如果没有指定用户应付金额，则等于订单金额
            if ($payableAmount === null) {
                $payableAmount = $targetAmount;
            }
            
            // 插入指令记录
            $stmt = $this->db->prepare("
                INSERT INTO payment_instructions (
                    instruction_id,
                    payment_request_id,
                    alipay_account_id,
                    merchant_id,
                    instruction_type,
                    target_amount,
                    payable_amount,
                    amount_tolerance,
                    expires_at,
                    status,
                    priority,
                    created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $instructionId,
                $paymentRequestId,
                $alipayAccountId,
                $merchantId,
                self::TYPE_FETCH_BILLS,
                $targetAmount,
                $payableAmount,
                0.00, // 金额容错范围
                $expiresAt,
                self::STATUS_PENDING,
                1 // 普通优先级
            ]);
            
            $dbId = $this->db->lastInsertId();
            
            // 记录日志
            $this->logInstruction($instructionId, 'created', '指令创建成功', [
                'payment_request_id' => $paymentRequestId,
                'target_amount' => $targetAmount,
                'payable_amount' => $payableAmount,
                'expires_at' => $expiresAt
            ]);
            
            return [
                'success' => true,
                'instruction_id' => $instructionId,
                'db_id' => $dbId,
                'expires_at' => $expiresAt,
                'message' => '指令创建成功'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取设备待执行指令
     * 
     * @param int $alipayAccountId 支付宝账户ID
     * @param string $deviceId 设备ID（可选）
     * @param int $limit 获取数量限制
     * @return array 指令列表
     */
    public function getPendingInstructions($alipayAccountId, $deviceId = null, $limit = 10) {
        try {
            $whereClause = "WHERE pi.alipay_account_id = ? AND pi.status = ? AND pi.expires_at > NOW()";
            $params = [$alipayAccountId, self::STATUS_PENDING];
            
            $stmt = $this->db->prepare("
                SELECT 
                    pi.*,
                    pr.order_no,
                    pr.amount as order_amount,
                    aa.account_name,
                    aa.account_number
                FROM payment_instructions pi
                LEFT JOIN payment_requests pr ON pi.payment_request_id = pr.id
                LEFT JOIN alipay_accounts aa ON pi.alipay_account_id = aa.id
                {$whereClause}
                ORDER BY pi.priority DESC, pi.created_at ASC
                LIMIT ?
            ");
            
            $params[] = $limit;
            $stmt->execute($params);
            $instructions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 格式化指令数据
            $formattedInstructions = [];
            foreach ($instructions as $instruction) {
                $formattedInstructions[] = [
                    'instruction_id' => $instruction['instruction_id'],
                    'payment_request_id' => $instruction['payment_request_id'],
                    'order_no' => $instruction['order_no'],
                    'instruction_type' => $instruction['instruction_type'],
                    'target_amount' => floatval($instruction['target_amount']),
                    'payable_amount' => floatval($instruction['payable_amount']),
                    'amount_tolerance' => floatval($instruction['amount_tolerance']),
                    'priority' => intval($instruction['priority']),
                    'created_at' => $instruction['created_at'],
                    'expires_at' => $instruction['expires_at'],
                    'account_info' => [
                        'account_name' => $instruction['account_name'],
                        'account_number' => $instruction['account_number']
                    ]
                ];
            }
            
            return [
                'success' => true,
                'instructions' => $formattedInstructions,
                'count' => count($formattedInstructions)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'instructions' => [],
                'count' => 0
            ];
        }
    }
    
    /**
     * 更新指令状态
     * 
     * @param string $instructionId 指令ID
     * @param string $status 新状态
     * @param string $executionResult 执行结果（可选）
     * @param array $extraData 额外数据（可选）
     * @return array 更新结果
     */
    public function updateInstructionStatus($instructionId, $status, $executionResult = null, $extraData = []) {
        try {
            $updateFields = ['status = ?'];
            $params = [$status];
            
            // 根据状态添加相应的时间戳
            switch ($status) {
                case self::STATUS_EXECUTING:
                    $updateFields[] = 'executed_at = NOW()';
                    break;
                case self::STATUS_COMPLETED:
                case self::STATUS_FAILED:
                case self::STATUS_EXPIRED:
                    $updateFields[] = 'completed_at = NOW()';
                    break;
            }
            
            // 添加执行结果
            if ($executionResult) {
                $updateFields[] = 'execution_result = ?';
                $params[] = $executionResult;
            }
            
            // 添加额外数据
            if (isset($extraData['found_amount'])) {
                $updateFields[] = 'found_amount = ?';
                $params[] = $extraData['found_amount'];
                $updateFields[] = 'found_at = NOW()';
            }
            
            if (isset($extraData['bills_count'])) {
                $updateFields[] = 'bills_count = ?';
                $params[] = $extraData['bills_count'];
            }
            
            if (isset($extraData['bills_data'])) {
                $updateFields[] = 'bills_data = ?';
                $params[] = json_encode($extraData['bills_data']);
            }
            
            if (isset($extraData['error_message'])) {
                $updateFields[] = 'error_message = ?';
                $params[] = $extraData['error_message'];
            }
            
            if (isset($extraData['device_response'])) {
                $updateFields[] = 'device_response = ?';
                $params[] = json_encode($extraData['device_response']);
                $updateFields[] = 'response_time = NOW()';
            }
            
            $params[] = $instructionId;
            
            $stmt = $this->db->prepare("
                UPDATE payment_instructions 
                SET " . implode(', ', $updateFields) . "
                WHERE instruction_id = ?
            ");
            
            $stmt->execute($params);
            
            if ($stmt->rowCount() > 0) {
                // 记录日志
                $this->logInstruction($instructionId, 'status_updated', "状态更新为: {$status}", $extraData);
                
                return [
                    'success' => true,
                    'message' => '指令状态更新成功'
                ];
            } else {
                return [
                    'success' => false,
                    'error' => '指令不存在或状态未变更'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取指令详情
     * 
     * @param string $instructionId 指令ID
     * @return array 指令详情
     */
    public function getInstructionDetails($instructionId) {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    pi.*,
                    pr.order_no,
                    pr.amount as order_amount,
                    pr.status as payment_status,
                    aa.account_name,
                    aa.account_number,
                    m.company_name as merchant_name
                FROM payment_instructions pi
                LEFT JOIN payment_requests pr ON pi.payment_request_id = pr.id
                LEFT JOIN alipay_accounts aa ON pi.alipay_account_id = aa.id
                LEFT JOIN merchants m ON pi.merchant_id = m.id
                WHERE pi.instruction_id = ?
            ");
            
            $stmt->execute([$instructionId]);
            $instruction = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$instruction) {
                return [
                    'success' => false,
                    'error' => '指令不存在'
                ];
            }
            
            // 格式化返回数据
            $formattedInstruction = [
                'instruction_id' => $instruction['instruction_id'],
                'payment_request_id' => $instruction['payment_request_id'],
                'order_no' => $instruction['order_no'],
                'merchant_name' => $instruction['merchant_name'],
                'instruction_type' => $instruction['instruction_type'],
                'target_amount' => floatval($instruction['target_amount']),
                'payable_amount' => floatval($instruction['payable_amount']),
                'amount_tolerance' => floatval($instruction['amount_tolerance']),
                'status' => $instruction['status'],
                'priority' => intval($instruction['priority']),
                'execution_result' => $instruction['execution_result'],
                'found_amount' => $instruction['found_amount'] ? floatval($instruction['found_amount']) : null,
                'bills_count' => intval($instruction['bills_count']),
                'error_message' => $instruction['error_message'],
                'created_at' => $instruction['created_at'],
                'expires_at' => $instruction['expires_at'],
                'executed_at' => $instruction['executed_at'],
                'completed_at' => $instruction['completed_at'],
                'account_info' => [
                    'account_name' => $instruction['account_name'],
                    'account_number' => $instruction['account_number']
                ],
                'bills_data' => $instruction['bills_data'] ? json_decode($instruction['bills_data'], true) : null,
                'device_response' => $instruction['device_response'] ? json_decode($instruction['device_response'], true) : null
            ];
            
            return [
                'success' => true,
                'instruction' => $formattedInstruction
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 清理过期指令
     * 
     * @return int 清理的指令数量
     */
    public function cleanupExpiredInstructions() {
        try {
            $stmt = $this->db->prepare("
                UPDATE payment_instructions 
                SET status = ?, completed_at = NOW()
                WHERE status IN (?, ?) 
                AND expires_at < NOW()
            ");
            
            $stmt->execute([
                self::STATUS_EXPIRED,
                self::STATUS_PENDING,
                self::STATUS_EXECUTING
            ]);
            
            $cleanedCount = $stmt->rowCount();
            
            if ($cleanedCount > 0) {
                error_log("Cleaned up {$cleanedCount} expired payment instructions");
            }
            
            return $cleanedCount;
            
        } catch (Exception $e) {
            error_log("Failed to cleanup expired instructions: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * 生成唯一指令ID
     * 
     * @return string 指令ID
     */
    private function generateInstructionId() {
        return 'INST_' . date('YmdHis') . '_' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * 记录指令日志
     * 
     * @param string $instructionId 指令ID
     * @param string $logType 日志类型
     * @param string $logContent 日志内容
     * @param array $extraData 额外数据
     */
    private function logInstruction($instructionId, $logType, $logContent, $extraData = []) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO payment_instruction_logs (
                    instruction_id,
                    log_type,
                    log_content,
                    created_at
                ) VALUES (?, ?, ?, NOW())
            ");
            
            $logData = [
                'content' => $logContent,
                'extra_data' => $extraData,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            
            $stmt->execute([
                $instructionId,
                $logType,
                json_encode($logData, JSON_UNESCAPED_UNICODE)
            ]);
            
        } catch (Exception $e) {
            error_log("Failed to log instruction: " . $e->getMessage());
        }
    }
    
    /**
     * 获取指令统计信息
     * 
     * @param int $alipayAccountId 支付宝账户ID（可选）
     * @param string $dateRange 日期范围
     * @return array 统计结果
     */
    public function getInstructionStats($alipayAccountId = null, $dateRange = '1 day') {
        try {
            $whereClause = "WHERE created_at >= ?";
            $params = [date('Y-m-d H:i:s', strtotime("-{$dateRange}"))];
            
            if ($alipayAccountId) {
                $whereClause .= " AND alipay_account_id = ?";
                $params[] = $alipayAccountId;
            }
            
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(*) as total_instructions,
                    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
                    COUNT(CASE WHEN status = 'executing' THEN 1 END) as executing_count,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
                    COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_count,
                    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
                    COUNT(CASE WHEN execution_result = 'found' THEN 1 END) as found_count,
                    AVG(bills_count) as avg_bills_count
                FROM payment_instructions 
                {$whereClause}
            ");
            
            $stmt->execute($params);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // 计算成功率
            $stats['success_rate'] = $stats['total_instructions'] > 0 
                ? round($stats['completed_count'] / $stats['total_instructions'] * 100, 2) 
                : 0;
                
            // 计算找到率
            $stats['found_rate'] = $stats['completed_count'] > 0 
                ? round($stats['found_count'] / $stats['completed_count'] * 100, 2) 
                : 0;
            
            return $stats;
            
        } catch (Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }
}
?> 