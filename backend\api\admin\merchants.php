<?php
/**
 * 商户管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

try {
    // 检查用户权限：系统管理员、平台管理员可以管理所有商户，商户和码商只能查看自己的信息
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin', 'merchant', 'provider'])) {
        $auth->sendForbidden('无权限访问商户管理');
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($action) {
        case 'merchants':
            handleMerchants($auth);
            break;
        case 'create_merchant':
            handleCreateMerchant($auth);
            break;
        case 'update_merchant':
            handleUpdateMerchant($auth);
            break;
        case 'approve_merchant':
            handleApproveMerchant($auth);
            break;
        case 'reject_merchant':
            handleRejectMerchant($auth);
            break;
        case 'merchant_api_keys':
            handleMerchantApiKeys($auth);
            break;
        case 'reset_merchant_password':
            handleResetMerchantPassword($auth);
            break;
        case 'update_merchant_user_status':
            handleUpdateMerchantUserStatus($auth);
            break;
        case 'get_merchant_login_logs':
            handleGetMerchantLoginLogs($auth);
            break;
        case 'get_merchant_user_detail':
            handleGetMerchantUserDetail($auth);
            break;
        case 'get_merchants':
            handleGetMerchants($auth);
            break;
        default:
            // 默认商户管理
            handleMerchants($auth);
    }
} catch (Exception $e) {
    error_log("Merchants API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

function handleMerchants($auth) {
    global $method;
    
    switch ($method) {
        case 'GET':
            handleGetMerchants($auth);
            break;
        case 'POST':
            handleCreateMerchant($auth);
            break;
        case 'PUT':
            handleUpdateMerchant($auth);
            break;
        case 'DELETE':
            handleDeleteMerchant($auth);
            break;
        default:
            $auth->sendError('不支持的请求方法', 405);
    }
}

// 获取商户列表
function handleGetMerchants($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('merchant.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $simple = isset($_GET['simple']) ? $_GET['simple'] : '';
    
    $whereConditions = array();
    $params = array();
    
    // 注意：数据隔离现在由Database类自动处理，无需手动添加platform_id过滤
    // 商户权限特殊处理：商户只能查看自己的信息
    if ($currentUser['user_type'] === 'merchant') {
        $whereConditions[] = "u.id = ?";
        $params[] = $currentUser['id'];
    }
    
    // 更新状态选项以匹配新表结构
    if (!empty($status) && in_array($status, array('pending', 'approved', 'rejected', 'disabled', 'active', 'inactive'))) {
        $whereConditions[] = "m.status = ?";
        $params[] = $status;
    }
    
    if (!empty($search)) {
        $whereConditions[] = "(u.username LIKE ? OR m.merchant_code LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    try {
        // 如果是simple请求，只返回基本的商户信息
        if ($simple === '1') {
            $merchants = $db->fetchAll(
                "SELECT m.id, u.username, m.merchant_code
                 FROM merchants m
                 JOIN users u ON m.user_id = u.id
                 $whereClause
                 ORDER BY m.created_at DESC",
                $params
            );
            
            // 格式化为前端期望的格式
            $formattedMerchants = array();
            foreach ($merchants as $merchant) {
                $formattedMerchants[] = array(
                    'id' => $merchant['id'],
                    'name' => $merchant['merchant_code'] ?: $merchant['username'],
                    'username' => $merchant['username']
                );
            }
            
            $auth->sendSuccess(array('merchants' => $formattedMerchants), '商户列表获取成功');
            return;
        }
        
        // 获取总数
        $totalResult = $db->fetch(
            "SELECT COUNT(*) as count FROM merchants m 
             JOIN users u ON m.user_id = u.id 
             $whereClause",
            $params
        );
        $total = $totalResult['count'];
        
        // 获取商户列表
        $merchants = $db->fetchAll(
            "SELECT m.*, 
                    u.username, u.user_type, u.status as user_status,
                    u.created_at as user_created_at, u.last_login
             FROM merchants m
             JOIN users u ON m.user_id = u.id
             $whereClause
             ORDER BY m.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, array($limit, $offset))
        );
        
        // 获取统计信息（基于新表结构的状态）
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_merchants,
                COUNT(CASE WHEN m.status = 'pending' THEN 1 END) as pending_merchants,
                COUNT(CASE WHEN m.status = 'approved' THEN 1 END) as approved_merchants,
                COUNT(CASE WHEN m.status = 'active' THEN 1 END) as active_merchants,
                COUNT(CASE WHEN m.status = 'inactive' THEN 1 END) as inactive_merchants,
                COUNT(CASE WHEN m.status = 'disabled' THEN 1 END) as disabled_merchants,
                COUNT(CASE WHEN m.status = 'rejected' THEN 1 END) as rejected_merchants
             FROM merchants m 
             JOIN users u ON m.user_id = u.id 
             $whereClause",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_merchants', 'merchant', 0, '查看商户列表');
        
        $response = array(
            'merchants' => $merchants,
            'stats' => $stats,
            'pagination' => array(
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            )
        );
        
        $auth->sendSuccess($response, '商户列表获取成功');
        
    } catch (Exception $e) {
        error_log("获取商户列表失败: " . $e->getMessage());
        $auth->sendError('获取商户列表失败: ' . $e->getMessage(), 500);
    }
}

// 创建商户
function handleCreateMerchant($auth) {
    global $db;
    
    // 检查权限
    if (!$auth->checkPermission('merchant.create')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证必填字段（基于实际表结构）
    $requiredFields = array('username', 'password');
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            $auth->sendError("字段 {$field} 不能为空", 400);
            return;
        }
    }
    
    $username = trim($input['username']);
    $password = $input['password'];
    $merchantCode = isset($input['merchant_code']) ? trim($input['merchant_code']) : '';
    $platformId = isset($input['platform_id']) ? intval($input['platform_id']) : 1; // 默认平台ID为1
    $callbackUrl = isset($input['callback_url']) ? trim($input['callback_url']) : '';
    $serviceRate = isset($input['service_rate']) ? floatval($input['service_rate']) : 0.0050; // 默认费率
    
    // 验证回调URL格式（如果提供了）
    if (!empty($callbackUrl) && !filter_var($callbackUrl, FILTER_VALIDATE_URL)) {
        $auth->sendError('回调URL格式不正确', 400);
        return;
    }
    
    // 检查用户名是否已存在
    $existingUser = $db->fetch("SELECT id FROM users WHERE username = ?", array($username));
    if ($existingUser) {
        $auth->sendError('用户名已存在', 400);
        return;
    }
    
    // 检查商户代码是否已存在（如果提供了）
    if (!empty($merchantCode)) {
        $existingCode = $db->fetch("SELECT id FROM merchants WHERE merchant_code = ?", array($merchantCode));
        if ($existingCode) {
            $auth->sendError('商户代码已存在', 400);
            return;
        }
    }
    
    try {
        $db->execute("START TRANSACTION");
        
        // 创建用户（基于实际表结构）
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $db->execute(
            "INSERT INTO users (username, password, user_type, status, created_at, updated_at) 
             VALUES (?, ?, 'merchant', 'active', NOW(), NOW())",
            array($username, $hashedPassword)
        );
        
        $userId = $db->lastInsertId();
        
        // 创建商户记录（基于新表结构）
        $apiKey = generateApiKey();
        $salt = generateSalt();
        
        $db->execute(
            "INSERT INTO merchants (platform_id, merchant_code, user_id, status, api_key, callback_url, service_rate, salt, created_at, updated_at) 
             VALUES (?, ?, ?, 'pending', ?, ?, ?, ?, NOW(), NOW())",
            array($platformId, $merchantCode, $userId, $apiKey, $callbackUrl, $serviceRate, $salt)
        );
        
        $merchantId = $db->lastInsertId();
        
        $db->execute("COMMIT");
        
        // 记录操作日志
        $auth->logUserAction('create_merchant', 'merchant', $merchantId, "创建商户: {$username}");
        
        $auth->sendSuccess(array('merchant_id' => $merchantId), '商户创建成功');
        
    } catch (Exception $e) {
        $db->execute("ROLLBACK");
        error_log("创建商户失败: " . $e->getMessage());
        $auth->sendError('商户创建失败: ' . $e->getMessage(), 500);
    }
}

// 其他处理函数（暂时简化）
function handleUpdateMerchant($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 记录开始处理
    error_log("handleUpdateMerchant - 开始处理更新商户请求");
    
    // 只有系统管理员和平台管理员可以更新商户
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin'])) {
        error_log("handleUpdateMerchant - 权限检查失败，用户类型: " . $currentUser['user_type']);
        $auth->sendForbidden('无权限更新商户信息');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    error_log("handleUpdateMerchant - 接收到的输入数据: " . json_encode($input));
    
    $merchantId = isset($input['merchant_id']) ? intval($input['merchant_id']) : 0;
    $username = isset($input['username']) ? trim($input['username']) : '';
    $password = isset($input['password']) ? trim($input['password']) : '';
    $merchantCode = isset($input['merchant_code']) ? trim($input['merchant_code']) : '';
    $callbackUrl = isset($input['callback_url']) ? trim($input['callback_url']) : '';
    $serviceRate = isset($input['service_rate']) ? floatval($input['service_rate']) : 0.0050;
    $platformId = isset($input['platform_id']) ? intval($input['platform_id']) : 1;
    
    error_log("handleUpdateMerchant - 解析后的数据: merchantId=$merchantId, username=$username, merchantCode=$merchantCode, callbackUrl=$callbackUrl, serviceRate=$serviceRate, platformId=$platformId");
    
    if (!$merchantId || !$username) {
        error_log("handleUpdateMerchant - 数据验证失败: merchantId=$merchantId, username=$username");
        $auth->sendError('商户ID和用户名不能为空');
        return;
    }
    
    try {
        // 验证商户是否存在且有权限操作
        $whereClause = "WHERE m.id = ?";
        $params = array($merchantId);
        
        // 注意：数据隔离现在由Database类自动处理
        
        $merchant = $db->fetch(
            "SELECT m.*, u.username, u.id as user_id 
             FROM merchants m 
             JOIN users u ON m.user_id = u.id 
             $whereClause",
            $params
        );
        
        if (!$merchant) {
            $auth->sendError('商户不存在或无权限操作');
            return;
        }
        
        // 检查用户名是否已被其他用户使用
        if ($username !== $merchant['username']) {
            $existingUser = $db->fetch(
                "SELECT id FROM users WHERE username = ? AND id != ?",
                array($username, $merchant['user_id'])
            );
            
            if ($existingUser) {
                $auth->sendError('用户名已被使用');
                return;
            }
        }
        
        // 开始事务
        $db->beginTransaction();
        
        // 更新用户信息
        $userUpdateFields = array();
        $userParams = array();
        
        if ($username !== $merchant['username']) {
            $userUpdateFields[] = "username = ?";
            $userParams[] = $username;
        }
        
        if (!empty($password)) {
            $userUpdateFields[] = "password = ?";
            $userParams[] = password_hash($password, PASSWORD_BCRYPT);
        }
        
        if (!empty($userUpdateFields)) {
            $userParams[] = $merchant['user_id'];
            $db->execute(
                "UPDATE users SET " . implode(', ', $userUpdateFields) . " WHERE id = ?",
                $userParams
            );
        }
        
        // 更新商户信息
        $merchantUpdateFields = array();
        $merchantParams = array();
        
        if ($merchantCode !== $merchant['merchant_code']) {
            $merchantUpdateFields[] = "merchant_code = ?";
            $merchantParams[] = $merchantCode;
        }
        
        if ($callbackUrl !== $merchant['callback_url']) {
            $merchantUpdateFields[] = "callback_url = ?";
            $merchantParams[] = $callbackUrl;
        }
        
        if ($serviceRate !== floatval($merchant['service_rate'])) {
            $merchantUpdateFields[] = "service_rate = ?";
            $merchantParams[] = $serviceRate;
        }
        
        // 平台ID通常不允许修改，除非是系统管理员
        if ($currentUser['user_type'] === 'system_admin' && $platformId !== intval($merchant['platform_id'])) {
            $merchantUpdateFields[] = "platform_id = ?";
            $merchantParams[] = $platformId;
        }
        
        if (!empty($merchantUpdateFields)) {
            $merchantUpdateFields[] = "updated_at = NOW()";
            $merchantParams[] = $merchantId;
            $db->execute(
                "UPDATE merchants SET " . implode(', ', $merchantUpdateFields) . " WHERE id = ?",
                $merchantParams
            );
        }
        
        $db->commit();
        
        // 记录操作日志
        $auth->logUserAction(
            'update_merchant', 
            'merchant', 
            $merchantId, 
            "更新商户信息: $username"
        );
        
        $auth->sendSuccess(null, '商户信息更新成功');
        
    } catch (Exception $e) {
        if ($db->inTransaction()) {
            $db->rollback();
        }
        error_log("handleUpdateMerchant - 异常: " . $e->getMessage());
        error_log("handleUpdateMerchant - 异常堆栈: " . $e->getTraceAsString());
        $auth->sendError('更新商户失败: ' . $e->getMessage());
    }
}

function handleDeleteMerchant($auth) {
    $auth->sendError('功能开发中', 501);
}

function handleApproveMerchant($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 只有系统管理员和平台管理员可以审核商户
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限审核商户');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $merchantId = isset($input['merchant_id']) ? intval($input['merchant_id']) : 0;
    
    if (!$merchantId) {
        $auth->sendError('商户ID不能为空');
        return;
    }
    
    try {
        // 验证商户是否存在且有权限操作
        $whereClause = "WHERE m.id = ?";
        $params = array($merchantId);
        
        // 注意：数据隔离现在由Database类自动处理
        
        $merchant = $db->fetch(
            "SELECT m.id, u.username FROM merchants m 
             JOIN users u ON m.user_id = u.id 
             $whereClause",
            $params
        );
        
        if (!$merchant) {
            $auth->sendError('商户不存在或无权限操作');
            return;
        }
        
        // 更新商户状态为approved
        $db->execute(
            "UPDATE merchants SET status = 'approved', approved_by = ?, approved_at = NOW() WHERE id = ?",
            array($currentUser['id'], $merchantId)
        );
        
        // 记录操作日志
        $auth->logUserAction(
            'approve_merchant', 
            'merchant', 
            $merchantId, 
            "审核通过商户: {$merchant['username']}"
        );
        
        $auth->sendSuccess(null, '商户审核通过');
        
    } catch (Exception $e) {
        error_log("审核商户失败: " . $e->getMessage());
        $auth->sendError('审核商户失败: ' . $e->getMessage());
    }
}

function handleRejectMerchant($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 只有系统管理员和平台管理员可以拒绝商户
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限拒绝商户');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $merchantId = isset($input['merchant_id']) ? intval($input['merchant_id']) : 0;
    
    if (!$merchantId) {
        $auth->sendError('商户ID不能为空');
        return;
    }
    
    try {
        // 验证商户是否存在且有权限操作
        $whereClause = "WHERE m.id = ?";
        $params = array($merchantId);
        
        // 注意：数据隔离现在由Database类自动处理
        
        $merchant = $db->fetch(
            "SELECT m.id, u.username FROM merchants m 
             JOIN users u ON m.user_id = u.id 
             $whereClause",
            $params
        );
        
        if (!$merchant) {
            $auth->sendError('商户不存在或无权限操作');
            return;
        }
        
        // 更新商户状态为rejected
        $db->execute(
            "UPDATE merchants SET status = 'rejected' WHERE id = ?",
            array($merchantId)
        );
        
        // 记录操作日志
        $auth->logUserAction(
            'reject_merchant', 
            'merchant', 
            $merchantId, 
            "拒绝商户: {$merchant['username']}"
        );
        
        $auth->sendSuccess(null, '商户已拒绝');
        
    } catch (Exception $e) {
        error_log("拒绝商户失败: " . $e->getMessage());
        $auth->sendError('拒绝商户失败: ' . $e->getMessage());
    }
}

function handleMerchantApiKeys($auth) {
    // 返回默认API密钥信息
    $apiKeys = array(
        'api_key' => 'test_api_key_123456',
        'secret_key' => 'test_secret_key_123456',
        'callback_url' => '',
        'ip_whitelist' => array(),
        'status' => 'active'
    );
    
    $auth->sendSuccess($apiKeys, 'API密钥信息获取成功');
}

// 生成API密钥
function generateApiKey() {
    return md5(uniqid(mt_rand(), true));
}

// 生成密码盐值
function generateSalt() {
    return substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 10);
}

// ===============================
// 新增用户操作功能
// ===============================

/**
 * 重置商户用户密码
 */
function handleResetMerchantPassword($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 只有系统管理员和平台管理员可以重置密码
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限重置商户密码');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $merchantId = isset($input['merchant_id']) ? intval($input['merchant_id']) : 0;
    $newPassword = isset($input['new_password']) ? trim($input['new_password']) : '';
    
    if (!$merchantId || !$newPassword) {
        $auth->sendError('商户ID和新密码不能为空');
        return;
    }
    
    if (strlen($newPassword) < 6) {
        $auth->sendError('密码长度不能少于6位');
        return;
    }
    
    try {
        // 验证商户是否存在且有权限操作
        $whereClause = "WHERE m.id = ?";
        $params = array($merchantId);
        
        // 注意：数据隔离现在由Database类自动处理
        
        $merchant = $db->fetch(
            "SELECT m.id, m.user_id, u.username 
             FROM merchants m 
             JOIN users u ON m.user_id = u.id 
             $whereClause",
            $params
        );
        
        if (!$merchant) {
            $auth->sendError('商户不存在或无权限操作');
            return;
        }
        
        // 更新用户密码
        $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT);
        $db->execute(
            "UPDATE users SET password = ? WHERE id = ?",
            array($hashedPassword, $merchant['user_id'])
        );
        
        // 记录操作日志
        $auth->logUserAction(
            'reset_merchant_password', 
            'merchant', 
            $merchantId, 
            "重置商户 {$merchant['username']} 的密码"
        );
        
        $auth->sendSuccess(null, '商户密码重置成功');
        
    } catch (Exception $e) {
        error_log("重置商户密码失败: " . $e->getMessage());
        $auth->sendError('重置密码失败: ' . $e->getMessage());
    }
}

/**
 * 更新商户用户状态
 */
function handleUpdateMerchantUserStatus($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 只有系统管理员和平台管理员可以修改状态
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限修改商户状态');
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $merchantId = isset($input['merchant_id']) ? intval($input['merchant_id']) : 0;
    $userStatus = isset($input['user_status']) ? trim($input['user_status']) : '';
    $merchantStatus = isset($input['merchant_status']) ? trim($input['merchant_status']) : '';
    
    if (!$merchantId) {
        $auth->sendError('商户ID不能为空');
        return;
    }
    
    // 验证状态值
    $validUserStatuses = array('active', 'inactive', 'suspended');
    $validMerchantStatuses = array('pending', 'active', 'suspended', 'rejected');
    
    if ($userStatus && !in_array($userStatus, $validUserStatuses)) {
        $auth->sendError('无效的用户状态');
        return;
    }
    
    if ($merchantStatus && !in_array($merchantStatus, $validMerchantStatuses)) {
        $auth->sendError('无效的商户状态');
        return;
    }
    
    try {
        // 验证商户是否存在且有权限操作
        $whereClause = "WHERE m.id = ?";
        $params = array($merchantId);
        
        // 注意：数据隔离现在由Database类自动处理
        
        $merchant = $db->fetch(
            "SELECT m.id, m.user_id, u.username 
             FROM merchants m 
             JOIN users u ON m.user_id = u.id 
             $whereClause",
            $params
        );
        
        if (!$merchant) {
            $auth->sendError('商户不存在或无权限操作');
            return;
        }
        
        // 开始事务
        $db->beginTransaction();
        
        // 更新用户状态
        if ($userStatus) {
            $db->execute(
                "UPDATE users SET status = ? WHERE id = ?",
                array($userStatus, $merchant['user_id'])
            );
        }
        
        // 更新商户状态
        if ($merchantStatus) {
            $db->execute(
                "UPDATE merchants SET status = ? WHERE id = ?",
                array($merchantStatus, $merchantId)
            );
        }
        
        $db->commit();
        
        // 记录操作日志
        $logDesc = "更新商户 {$merchant['username']} 状态";
        if ($userStatus) $logDesc .= " - 用户状态: $userStatus";
        if ($merchantStatus) $logDesc .= " - 商户状态: $merchantStatus";
        
        $auth->logUserAction(
            'update_merchant_status', 
            'merchant', 
            $merchantId, 
            $logDesc
        );
        
        $auth->sendSuccess(null, '商户状态更新成功');
        
    } catch (Exception $e) {
        $db->rollback();
        error_log("更新商户状态失败: " . $e->getMessage());
        $auth->sendError('更新状态失败: ' . $e->getMessage());
    }
}

/**
 * 获取商户登录日志
 */
function handleGetMerchantLoginLogs($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 检查权限
    if (!in_array($currentUser['user_type'], ['admin', 'platform_admin'])) {
        $auth->sendForbidden('无权限查看商户登录日志');
        return;
    }
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
    $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
    $offset = ($page - 1) * $limit;
    
    if (!$merchantId) {
        $auth->sendError('商户ID不能为空');
        return;
    }
    
    try {
        // 验证商户是否存在且有权限操作
        $whereClause = "WHERE m.id = ?";
        $params = array($merchantId);
        
        // 注意：数据隔离现在由Database类自动处理
        
        $merchant = $db->fetch(
            "SELECT m.id, m.user_id, u.username 
             FROM merchants m 
             JOIN users u ON m.user_id = u.id 
             $whereClause",
            $params
        );
        
        if (!$merchant) {
            $auth->sendError('商户不存在或无权限操作');
            return;
        }
        
        // 获取登录日志
        $logs = $db->fetchAll(
            "SELECT 
                action, 
                description, 
                ip_address, 
                user_agent, 
                created_at
             FROM system_logs 
             WHERE user_id = ? AND action IN ('login', 'logout', 'tenant_login', 'tenant_logout')
             ORDER BY created_at DESC 
             LIMIT ? OFFSET ?",
            array($merchant['user_id'], $limit, $offset)
        );
        
        // 获取总数
        $totalResult = $db->fetch(
            "SELECT COUNT(*) as count 
             FROM system_logs 
             WHERE user_id = ? AND action IN ('login', 'logout', 'tenant_login', 'tenant_logout')",
            array($merchant['user_id'])
        );
        
        $response = array(
            'logs' => $logs,
            'merchant' => array(
                'id' => $merchant['id'],
                'username' => $merchant['username']
            ),
            'pagination' => array(
                'total' => $totalResult['count'],
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($totalResult['count'] / $limit)
            )
        );
        
        $auth->sendSuccess($response, '获取商户登录日志成功');
        
    } catch (Exception $e) {
        error_log("获取商户登录日志失败: " . $e->getMessage());
        $auth->sendError('获取登录日志失败: ' . $e->getMessage());
    }
}

/**
 * 获取商户用户详情
 */
function handleGetMerchantUserDetail($auth) {
    global $db, $currentUser, $domainInfo;
    
    // 检查权限
    if (!in_array($currentUser['user_type'], ['admin', 'platform_admin', 'merchant'])) {
        $auth->sendForbidden('无权限查看商户详情');
        return;
    }
    
    $merchantId = isset($_GET['merchant_id']) ? intval($_GET['merchant_id']) : 0;
    
    if (!$merchantId) {
        $auth->sendError('商户ID不能为空');
        return;
    }
    
    try {
        // 验证商户是否存在且有权限操作
        $whereClause = "WHERE m.id = ?";
        $params = array($merchantId);
        
        // 注意：数据隔离现在由Database类自动处理
        // 商户权限特殊处理：商户只能查看自己的信息
        if ($currentUser['user_type'] === 'merchant') {
            $whereClause .= " AND m.user_id = ?";
            $params[] = $currentUser['id'];
        }
        
        $merchant = $db->fetch(
            "SELECT 
                m.*,
                u.username, u.status as user_status,
                u.created_at as user_created_at, u.last_login,
                p.platform_name
             FROM merchants m 
             JOIN users u ON m.user_id = u.id 
             LEFT JOIN platforms p ON m.platform_id = p.id
             $whereClause",
            $params
        );
        
        if (!$merchant) {
            $auth->sendError('商户不存在或无权限查看');
            return;
        }
        
        // 获取最近登录记录
        $recentLogins = $db->fetchAll(
            "SELECT ip_address, user_agent, created_at 
             FROM system_logs 
             WHERE user_id = ? AND action IN ('login', 'tenant_login')
             ORDER BY created_at DESC 
             LIMIT 5",
            array($merchant['user_id'])
        );
        
        $merchant['recent_logins'] = $recentLogins;
        
        $auth->sendSuccess($merchant, '获取商户详情成功');
        
    } catch (Exception $e) {
        error_log("获取商户详情失败: " . $e->getMessage());
        $auth->sendError('获取商户详情失败: ' . $e->getMessage());
    }
}
?> 