/**
 * 支付管理模块
 * 负责支付方式配置、支付渠道管理、费率设置、支付统计等核心支付业务功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-15
 */

class PaymentManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.currentChannels = [];
        this.currentMethods = [];
        
        console.log('PaymentManager initialized');
    }

    /**
     * 初始化支付管理模块
     */
    async init() {
        try {
            // 注册页面处理器
            if (window.uiManager) {
                window.uiManager.registerPageHandler('payments', (container) => {
                    this.loadPaymentManagementPage(container);
                });
            }
            
            console.log('✅ 支付管理模块初始化完成');
        } catch (error) {
            console.error('❌ 支付管理模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载支付管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadPaymentManagementPage(container) {
        container.innerHTML = this.generatePaymentManagementHTML();
        this.initializePaymentManagementEvents();
        this.loadPaymentData();
    }

    /**
     * 生成支付管理页面HTML
     * @returns {string} HTML字符串
     */
    generatePaymentManagementHTML() {
        return `
            <div class="payment-management">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-credit-card-2-front me-2"></i>支付管理</h2>
                            <p class="text-muted mb-0">管理支付方式、渠道配置和费率设置</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="addChannelBtn">
                                <i class="bi bi-plus-circle me-2"></i>添加支付渠道
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="paymentSettingsBtn">
                                <i class="bi bi-gear me-2"></i>支付设置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-credit-card"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalChannelsCount">-</div>
                                <div class="stat-label">支付渠道</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="activeChannelsCount">-</div>
                                <div class="stat-label">活跃渠道</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="todayPaymentsCount">-</div>
                                <div class="stat-label">今日支付</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-percent"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="avgSuccessRateCount">-</div>
                                <div class="stat-label">平均成功率</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="paymentTabs">
                            <li class="nav-item">
                                <a class="nav-link active" id="channels-tab" data-bs-toggle="tab" href="#channels">
                                    <i class="bi bi-credit-card me-2"></i>支付渠道
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="methods-tab" data-bs-toggle="tab" href="#methods">
                                    <i class="bi bi-wallet2 me-2"></i>支付方式
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="rates-tab" data-bs-toggle="tab" href="#rates">
                                    <i class="bi bi-percent me-2"></i>费率管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="statistics-tab" data-bs-toggle="tab" href="#statistics">
                                    <i class="bi bi-graph-up me-2"></i>支付统计
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="paymentTabContent">
                            <!-- 支付渠道 -->
                            <div class="tab-pane fade show active" id="channels">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>支付渠道列表</h6>
                                    <div>
                                        <button class="btn btn-outline-primary btn-sm" id="refreshChannelsBtn">
                                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm ms-2" id="testAllChannelsBtn">
                                            <i class="bi bi-lightning me-1"></i>批量测试
                                        </button>
                                    </div>
                                </div>
                                <div class="row" id="channelsContainer">
                                    <!-- 支付渠道卡片将动态生成 -->
                                    <div class="col-12 text-center py-4">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <div class="mt-2">正在加载支付渠道...</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 支付方式 -->
                            <div class="tab-pane fade" id="methods">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>支付方式配置</h6>
                                    <button class="btn btn-primary btn-sm" id="addMethodBtn">
                                        <i class="bi bi-plus-circle me-1"></i>添加方式
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>支付方式</th>
                                                <th>图标</th>
                                                <th>状态</th>
                                                <th>支持渠道</th>
                                                <th>排序</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="methodsTableBody">
                                            <tr>
                                                <td colspan="6" class="text-center py-4">
                                                    <div class="spinner-border text-primary" role="status"></div>
                                                    <div class="mt-2">正在加载支付方式...</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 费率管理 -->
                            <div class="tab-pane fade" id="rates">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>费率配置</h6>
                                    <button class="btn btn-primary btn-sm" id="addRateBtn">
                                        <i class="bi bi-plus-circle me-1"></i>添加费率
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>渠道</th>
                                                <th>支付方式</th>
                                                <th>费率类型</th>
                                                <th>费率值</th>
                                                <th>最低费用</th>
                                                <th>最高费用</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="ratesTableBody">
                                            <tr>
                                                <td colspan="8" class="text-center py-4">
                                                    <div class="spinner-border text-primary" role="status"></div>
                                                    <div class="mt-2">正在加载费率配置...</div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 支付统计 -->
                            <div class="tab-pane fade" id="statistics">
                                <div class="row g-4">
                                    <div class="col-md-8">
                                        <div class="chart-container">
                                            <h6 class="mb-3">支付趋势图</h6>
                                            <canvas id="paymentChart" width="400" height="200"></canvas>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="stats-summary">
                                            <h6 class="mb-3">渠道统计</h6>
                                            <div id="channelStatsContainer">
                                                <!-- 渠道统计将动态生成 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 添加/编辑支付渠道模态框 -->
            <div class="modal fade" id="channelModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="channelModalTitle">添加支付渠道</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="channelForm">
                                <input type="hidden" id="channelId">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">渠道名称 *</label>
                                        <input type="text" class="form-control" id="channelName" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">渠道代码 *</label>
                                        <input type="text" class="form-control" id="channelCode" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">渠道类型 *</label>
                                        <select class="form-select" id="channelType" required>
                                            <option value="">请选择类型</option>
                                            <option value="alipay">支付宝</option>
                                            <option value="wechat">微信支付</option>
                                            <option value="unionpay">银联支付</option>
                                            <option value="bank">网银支付</option>
                                            <option value="other">其他</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">状态</label>
                                        <select class="form-select" id="channelStatus">
                                            <option value="active">启用</option>
                                            <option value="inactive">禁用</option>
                                            <option value="testing">测试中</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">商户号</label>
                                        <input type="text" class="form-control" id="merchantId">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">应用ID</label>
                                        <input type="text" class="form-control" id="appId">
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">API密钥</label>
                                        <textarea class="form-control" id="apiKey" rows="3"></textarea>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">回调地址</label>
                                        <input type="url" class="form-control" id="notifyUrl">
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">备注</label>
                                        <textarea class="form-control" id="channelRemarks" rows="2"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-outline-warning" id="testChannelBtn">测试连接</button>
                            <button type="button" class="btn btn-primary" id="saveChannelBtn">保存</button>
                        </div>
                    </div>
                </div>
            </div>

            ${this.generatePaymentManagementStyles()}
        `;
    }

    /**
     * 生成支付管理样式
     * @returns {string} CSS样式
     */
    generatePaymentManagementStyles() {
        return `
            <style>
                .payment-management .stat-card {
                    background: white;
                    border-radius: 12px;
                    padding: 20px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }

                .payment-management .stat-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                }

                .payment-management .stat-icon {
                    width: 50px;
                    height: 50px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .payment-management .stat-content {
                    flex: 1;
                }

                .payment-management .stat-number {
                    font-size: 1.8rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin-bottom: 4px;
                }

                .payment-management .stat-label {
                    color: #6b7280;
                    font-size: 14px;
                }

                .payment-management .channel-card {
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                    padding: 20px;
                    margin-bottom: 20px;
                    transition: all 0.3s ease;
                    background: white;
                }

                .payment-management .channel-card:hover {
                    border-color: #3b82f6;
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
                }

                .payment-management .channel-icon {
                    width: 60px;
                    height: 60px;
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.8rem;
                    margin-bottom: 15px;
                }

                .payment-management .channel-alipay {
                    background: linear-gradient(135deg, #1677ff 0%, #69c0ff 100%);
                }

                .payment-management .channel-wechat {
                    background: linear-gradient(135deg, #52c41a 0%, #95de64 100%);
                }

                .payment-management .channel-unionpay {
                    background: linear-gradient(135deg, #fa541c 0%, #ff7a45 100%);
                }

                .payment-management .channel-bank {
                    background: linear-gradient(135deg, #722ed1 0%, #b37feb 100%);
                }

                .payment-management .channel-other {
                    background: linear-gradient(135deg, #8c8c8c 0%, #bfbfbf 100%);
                }

                .payment-management .status-badge {
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .payment-management .status-active {
                    background: #dcfce7;
                    color: #166534;
                }

                .payment-management .status-inactive {
                    background: #fef3c7;
                    color: #92400e;
                }

                .payment-management .status-testing {
                    background: #dbeafe;
                    color: #1e40af;
                }

                .payment-management .chart-container {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 20px;
                }

                .payment-management .stats-summary {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 20px;
                }

                .payment-management .channel-stat-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 0;
                    border-bottom: 1px solid #e9ecef;
                }

                .payment-management .channel-stat-item:last-child {
                    border-bottom: none;
                }

                .payment-management .table th {
                    font-weight: 600;
                    color: #374151;
                    border-bottom: 2px solid #e5e7eb;
                }

                .payment-management .table td {
                    vertical-align: middle;
                    border-bottom: 1px solid #f3f4f6;
                }

                .payment-management .nav-tabs .nav-link {
                    color: #6b7280;
                    border: none;
                    padding: 12px 20px;
                }

                .payment-management .nav-tabs .nav-link.active {
                    color: #3b82f6;
                    background: white;
                    border-bottom: 2px solid #3b82f6;
                }

                .payment-management .form-label {
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 6px;
                }

                .payment-management .form-control:focus,
                .payment-management .form-select:focus {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
            </style>
        `;
    }

    /**
     * 初始化支付管理事件
     */
    initializePaymentManagementEvents() {
        // 添加支付渠道按钮
        document.getElementById('addChannelBtn')?.addEventListener('click', () => {
            this.showChannelModal();
        });

        // 支付设置按钮
        document.getElementById('paymentSettingsBtn')?.addEventListener('click', () => {
            this.showPaymentSettings();
        });

        // 刷新渠道按钮
        document.getElementById('refreshChannelsBtn')?.addEventListener('click', () => {
            this.loadPaymentChannels();
        });

        // 批量测试按钮
        document.getElementById('testAllChannelsBtn')?.addEventListener('click', () => {
            this.testAllChannels();
        });

        // 保存渠道按钮
        document.getElementById('saveChannelBtn')?.addEventListener('click', () => {
            this.handleSaveChannel();
        });

        // 测试渠道按钮
        document.getElementById('testChannelBtn')?.addEventListener('click', () => {
            this.testChannel();
        });

        // 选项卡切换事件
        document.querySelectorAll('#paymentTabs .nav-link').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const target = e.target.getAttribute('href').substring(1);
                this.handleTabChange(target);
            });
        });
    }

    /**
     * 加载支付数据
     */
    async loadPaymentData() {
        try {
            await this.updatePaymentStats();
            await this.loadPaymentChannels();
        } catch (error) {
            console.error('加载支付数据失败:', error);
            this.utils.showMessage('加载支付数据失败', 'error');
        }
    }

    /**
     * 更新支付统计数据
     */
    async updatePaymentStats() {
        try {
            const response = await this.apiClient.get('/admin/payments/stats');
            
            if (response.success) {
                const stats = response.data;
                document.getElementById('totalChannelsCount').textContent = stats.total_channels || 0;
                document.getElementById('activeChannelsCount').textContent = stats.active_channels || 0;
                document.getElementById('todayPaymentsCount').textContent = stats.today_payments || 0;
                document.getElementById('avgSuccessRateCount').textContent = 
                    (stats.avg_success_rate || 0) + '%';
            }
        } catch (error) {
            console.error('加载支付统计失败:', error);
        }
    }

    /**
     * 加载支付渠道
     */
    async loadPaymentChannels() {
        try {
            const response = await this.apiClient.get('/admin/payments/channels');
            
            if (response.success) {
                this.currentChannels = response.data.channels || [];
                this.renderChannels();
            } else {
                throw new Error(response.message || '加载支付渠道失败');
            }
        } catch (error) {
            console.error('加载支付渠道失败:', error);
            this.showChannelsError('加载支付渠道失败');
        }
    }

    /**
     * 渲染支付渠道
     */
    renderChannels() {
        const container = document.getElementById('channelsContainer');
        if (!container) return;

        if (this.currentChannels.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center py-4">
                    <i class="bi bi-credit-card" style="font-size: 3rem; color: #6b7280;"></i>
                    <h5 class="mt-3 text-muted">暂无支付渠道</h5>
                    <p class="text-muted">点击上方"添加支付渠道"按钮开始配置</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.currentChannels.map(channel => `
            <div class="col-md-6 col-lg-4">
                <div class="channel-card">
                    <div class="channel-icon channel-${channel.type}">
                        <i class="bi bi-${this.getChannelIcon(channel.type)}"></i>
                    </div>
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <h6 class="mb-1">${channel.name}</h6>
                            <small class="text-muted">${channel.code}</small>
                        </div>
                        <span class="status-badge status-${channel.status}">
                            ${this.getStatusText(channel.status)}
                        </span>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between text-sm">
                            <span>今日交易:</span>
                            <span class="fw-bold">${channel.today_count || 0}</span>
                        </div>
                        <div class="d-flex justify-content-between text-sm">
                            <span>成功率:</span>
                            <span class="fw-bold text-success">${channel.success_rate || 0}%</span>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm flex-fill" 
                                onclick="paymentManager.showChannelModal(${channel.id})">
                            <i class="bi bi-pencil me-1"></i>编辑
                        </button>
                        <button class="btn btn-outline-warning btn-sm" 
                                onclick="paymentManager.testSingleChannel(${channel.id})" title="测试">
                            <i class="bi bi-lightning"></i>
                        </button>
                        <button class="btn btn-outline-${channel.status === 'active' ? 'secondary' : 'success'} btn-sm" 
                                onclick="paymentManager.toggleChannelStatus(${channel.id})" 
                                title="${channel.status === 'active' ? '禁用' : '启用'}">
                            <i class="bi bi-${channel.status === 'active' ? 'pause' : 'play'}"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 显示渠道错误
     * @param {string} message 错误信息
     */
    showChannelsError(message) {
        const container = document.getElementById('channelsContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="col-12 text-center py-4">
                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-danger">${message}</h5>
                <button class="btn btn-outline-primary mt-2" onclick="paymentManager.loadPaymentChannels()">
                    重新加载
                </button>
            </div>
        `;
    }

    /**
     * 显示支付渠道模态框
     * @param {number} channelId 渠道ID（编辑时）
     */
    async showChannelModal(channelId = null) {
        const modal = new bootstrap.Modal(document.getElementById('channelModal'));
        const title = document.getElementById('channelModalTitle');
        const form = document.getElementById('channelForm');

        // 重置表单
        form.reset();

        if (channelId) {
            // 编辑模式
            title.textContent = '编辑支付渠道';
            
            try {
                const response = await this.apiClient.get(`/admin/payments/channels/${channelId}`);
                if (response.success) {
                    const channel = response.data;
                    document.getElementById('channelId').value = channel.id;
                    document.getElementById('channelName').value = channel.name;
                    document.getElementById('channelCode').value = channel.code;
                    document.getElementById('channelType').value = channel.type;
                    document.getElementById('channelStatus').value = channel.status;
                    document.getElementById('merchantId').value = channel.merchant_id || '';
                    document.getElementById('appId').value = channel.app_id || '';
                    document.getElementById('apiKey').value = channel.api_key || '';
                    document.getElementById('notifyUrl').value = channel.notify_url || '';
                    document.getElementById('channelRemarks').value = channel.remarks || '';
                }
            } catch (error) {
                console.error('加载渠道信息失败:', error);
                this.utils.showMessage('加载渠道信息失败', 'error');
                return;
            }
        } else {
            // 添加模式
            title.textContent = '添加支付渠道';
        }

        modal.show();
    }

    /**
     * 处理保存渠道
     */
    async handleSaveChannel() {
        const form = document.getElementById('channelForm');
        const channelId = document.getElementById('channelId').value;

        // 表单验证
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const channelData = {
            name: document.getElementById('channelName').value,
            code: document.getElementById('channelCode').value,
            type: document.getElementById('channelType').value,
            status: document.getElementById('channelStatus').value,
            merchant_id: document.getElementById('merchantId').value,
            app_id: document.getElementById('appId').value,
            api_key: document.getElementById('apiKey').value,
            notify_url: document.getElementById('notifyUrl').value,
            remarks: document.getElementById('channelRemarks').value
        };

        try {
            const saveBtn = document.getElementById('saveChannelBtn');
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';

            let response;
            if (channelId) {
                response = await this.apiClient.put(`/admin/payments/channels/${channelId}`, channelData);
            } else {
                response = await this.apiClient.post('/admin/payments/channels', channelData);
            }

            if (response.success) {
                this.utils.showMessage(channelId ? '渠道更新成功' : '渠道添加成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('channelModal')).hide();
                this.loadPaymentChannels();
            } else {
                throw new Error(response.message || '保存失败');
            }
        } catch (error) {
            console.error('保存渠道失败:', error);
            this.utils.showMessage(error.message || '保存渠道失败', 'error');
        } finally {
            const saveBtn = document.getElementById('saveChannelBtn');
            saveBtn.disabled = false;
            saveBtn.innerHTML = '保存';
        }
    }

    /**
     * 测试渠道连接
     */
    async testChannel() {
        const channelId = document.getElementById('channelId').value;
        
        if (!channelId) {
            this.utils.showMessage('请先保存渠道后再测试', 'warning');
            return;
        }

        try {
            const testBtn = document.getElementById('testChannelBtn');
            testBtn.disabled = true;
            testBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>测试中...';

            const response = await this.apiClient.post(`/admin/payments/channels/${channelId}/test`);

            if (response.success) {
                this.utils.showMessage('渠道连接测试成功', 'success');
            } else {
                throw new Error(response.message || '测试失败');
            }
        } catch (error) {
            console.error('测试渠道失败:', error);
            this.utils.showMessage(error.message || '测试渠道失败', 'error');
        } finally {
            const testBtn = document.getElementById('testChannelBtn');
            testBtn.disabled = false;
            testBtn.innerHTML = '测试连接';
        }
    }

    /**
     * 测试单个渠道
     * @param {number} channelId 渠道ID
     */
    async testSingleChannel(channelId) {
        try {
            const response = await this.apiClient.post(`/admin/payments/channels/${channelId}/test`);

            if (response.success) {
                this.utils.showMessage('渠道测试成功', 'success');
            } else {
                throw new Error(response.message || '测试失败');
            }
        } catch (error) {
            console.error('测试渠道失败:', error);
            this.utils.showMessage(error.message || '测试渠道失败', 'error');
        }
    }

    /**
     * 批量测试所有渠道
     */
    async testAllChannels() {
        if (!confirm('确定要测试所有支付渠道吗？')) {
            return;
        }

        try {
            const testBtn = document.getElementById('testAllChannelsBtn');
            testBtn.disabled = true;
            testBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>测试中...';

            const response = await this.apiClient.post('/admin/payments/channels/test-all');

            if (response.success) {
                this.utils.showMessage('批量测试完成', 'success');
                this.loadPaymentChannels();
            } else {
                throw new Error(response.message || '批量测试失败');
            }
        } catch (error) {
            console.error('批量测试失败:', error);
            this.utils.showMessage(error.message || '批量测试失败', 'error');
        } finally {
            const testBtn = document.getElementById('testAllChannelsBtn');
            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="bi bi-lightning me-1"></i>批量测试';
        }
    }

    /**
     * 切换渠道状态
     * @param {number} channelId 渠道ID
     */
    async toggleChannelStatus(channelId) {
        try {
            const response = await this.apiClient.post(`/admin/payments/channels/${channelId}/toggle`);

            if (response.success) {
                this.utils.showMessage('渠道状态更新成功', 'success');
                this.loadPaymentChannels();
            } else {
                throw new Error(response.message || '状态更新失败');
            }
        } catch (error) {
            console.error('更新渠道状态失败:', error);
            this.utils.showMessage(error.message || '更新渠道状态失败', 'error');
        }
    }

    /**
     * 处理选项卡切换
     * @param {string} target 目标选项卡
     */
    handleTabChange(target) {
        switch (target) {
            case 'channels':
                this.loadPaymentChannels();
                break;
            case 'methods':
                this.loadPaymentMethods();
                break;
            case 'rates':
                this.loadPaymentRates();
                break;
            case 'statistics':
                this.loadPaymentStatistics();
                break;
        }
    }

    /**
     * 加载支付方式
     */
    async loadPaymentMethods() {
        // 这里可以加载支付方式数据
        console.log('加载支付方式数据');
    }

    /**
     * 加载费率配置
     */
    async loadPaymentRates() {
        // 这里可以加载费率配置数据
        console.log('加载费率配置数据');
    }

    /**
     * 加载支付统计
     */
    async loadPaymentStatistics() {
        // 这里可以加载支付统计数据
        console.log('加载支付统计数据');
    }

    /**
     * 显示支付设置
     */
    showPaymentSettings() {
        this.utils.showMessage('支付设置功能开发中', 'info');
    }

    /**
     * 获取渠道图标
     * @param {string} type 渠道类型
     * @returns {string} 图标类名
     */
    getChannelIcon(type) {
        const iconMap = {
            'alipay': 'alipay',
            'wechat': 'wechat',
            'unionpay': 'credit-card',
            'bank': 'bank',
            'other': 'credit-card-2-front'
        };
        return iconMap[type] || 'credit-card';
    }

    /**
     * 获取状态文本
     * @param {string} status 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'active': '启用',
            'inactive': '禁用',
            'testing': '测试中'
        };
        return statusMap[status] || status;
    }
}

// 创建全局实例
const paymentManager = new PaymentManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PaymentManager };
} else {
    // 浏览器环境
    window.PaymentManager = PaymentManager;
    window.paymentManager = paymentManager;
} 