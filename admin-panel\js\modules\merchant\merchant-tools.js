/**
 * 商户工具模块
 * 负责商户自助服务、工具集合、数据分析等核心商户服务功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-15
 */

class MerchantToolsManager {
    constructor() {

        this.authManager = window.authManager;
        this.utils = window.utils;
        this.currentTools = [];
        this.currentMerchants = [];
        
        console.log('MerchantToolsManager initialized');
    }

    /**
     * 初始化商户工具模块
     */
    async init() {
        try {
            // 注册页面处理器
            if (window.uiManager) {
                window.uiManager.registerPageHandler('merchant-tools', (container) => {
                    this.loadMerchantToolsPage(container);
                });
            }
            
            console.log('✅ 商户工具模块初始化完成');
        } catch (error) {
            console.error('❌ 商户工具模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载商户工具页面
     * @param {HTMLElement} container 容器元素
     */
    loadMerchantToolsPage(container) {
        container.innerHTML = this.generateMerchantToolsHTML();
        this.initializeMerchantToolsEvents();
        this.loadMerchantToolsData();
    }

    /**
     * 生成商户工具页面HTML
     * @returns {string} HTML字符串
     */
    generateMerchantToolsHTML() {
        return `
            <div class="merchant-tools">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-tools me-2"></i>商户工具</h2>
                            <p class="text-muted mb-0">为商户提供自助服务工具和数据分析</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="addToolBtn">
                                <i class="bi bi-plus-circle me-2"></i>添加工具
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="toolSettingsBtn">
                                <i class="bi bi-gear me-2"></i>工具设置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-tools"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalToolsCount">-</div>
                                <div class="stat-label">可用工具</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="activeMerchantsCount">-</div>
                                <div class="stat-label">活跃商户</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="todayUsageCount">-</div>
                                <div class="stat-label">今日使用</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-star"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="avgRatingCount">-</div>
                                <div class="stat-label">平均评分</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能选项卡 -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="toolsTabs">
                            <li class="nav-item">
                                <a class="nav-link active" id="tools-tab" data-bs-toggle="tab" href="#tools">
                                    <i class="bi bi-collection me-2"></i>工具集合
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="analytics-tab" data-bs-toggle="tab" href="#analytics">
                                    <i class="bi bi-graph-up me-2"></i>数据分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="self-service-tab" data-bs-toggle="tab" href="#self-service">
                                    <i class="bi bi-person-gear me-2"></i>自助服务
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="feedback-tab" data-bs-toggle="tab" href="#feedback">
                                    <i class="bi bi-chat-square-text me-2"></i>反馈建议
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="toolsTabContent">
                            <!-- 工具集合 -->
                            <div class="tab-pane fade show active" id="tools">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6>商户工具列表</h6>
                                    <div>
                                        <select class="form-select form-select-sm" id="toolCategoryFilter" style="width: 150px; display: inline-block;">
                                            <option value="">全部分类</option>
                                            <option value="payment">支付工具</option>
                                            <option value="analysis">分析工具</option>
                                            <option value="management">管理工具</option>
                                            <option value="integration">集成工具</option>
                                        </select>
                                        <button class="btn btn-outline-primary btn-sm ms-2" id="refreshToolsBtn">
                                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                        </button>
                                    </div>
                                </div>
                                <div class="row" id="toolsContainer">
                                    <div class="col-12 text-center py-4">
                                        <div class="spinner-border text-primary" role="status"></div>
                                        <div class="mt-2">正在加载工具数据...</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据分析 -->
                            <div class="tab-pane fade" id="analytics">
                                <div class="row g-4">
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">工具使用趋势</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="usageChart" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">热门工具</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="popularToolsContainer">
                                                    <!-- 热门工具将动态生成 -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">商户活跃度分析</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-hover">
                                                        <thead class="table-light">
                                                            <tr>
                                                                <th>商户名称</th>
                                                                <th>使用工具数</th>
                                                                <th>今日使用次数</th>
                                                                <th>活跃度</th>
                                                                <th>最后使用</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody id="merchantActivityTableBody">
                                                            <!-- 商户活跃度数据将动态生成 -->
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 自助服务 -->
                            <div class="tab-pane fade" id="self-service">
                                <div class="row g-4">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">快速操作</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="d-grid gap-2">
                                                    <button class="btn btn-outline-primary" id="generateAPIKeyBtn">
                                                        <i class="bi bi-key me-2"></i>生成API密钥
                                                    </button>
                                                    <button class="btn btn-outline-success" id="downloadSDKBtn">
                                                        <i class="bi bi-download me-2"></i>下载SDK
                                                    </button>
                                                    <button class="btn btn-outline-info" id="viewDocsBtn">
                                                        <i class="bi bi-book me-2"></i>查看文档
                                                    </button>
                                                    <button class="btn btn-outline-warning" id="contactSupportBtn">
                                                        <i class="bi bi-headset me-2"></i>联系客服
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">账户信息</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row g-3">
                                                    <div class="col-12">
                                                        <label class="form-label">商户ID</label>
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" value="MCH_123456789" readonly>
                                                            <button class="btn btn-outline-secondary" type="button">
                                                                <i class="bi bi-clipboard"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <label class="form-label">API密钥</label>
                                                        <div class="input-group">
                                                            <input type="password" class="form-control" value="sk_live_123456789abcdef" readonly>
                                                            <button class="btn btn-outline-secondary" type="button">
                                                                <i class="bi bi-eye"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <label class="form-label">回调地址</label>
                                                        <input type="url" class="form-control" placeholder="https://your-domain.com/notify">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">服务状态</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row g-3">
                                                    <div class="col-md-3">
                                                        <div class="text-center">
                                                            <div class="status-indicator status-success mb-2"></div>
                                                            <div class="fw-semibold">支付服务</div>
                                                            <small class="text-success">正常</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="text-center">
                                                            <div class="status-indicator status-success mb-2"></div>
                                                            <div class="fw-semibold">API服务</div>
                                                            <small class="text-success">正常</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="text-center">
                                                            <div class="status-indicator status-warning mb-2"></div>
                                                            <div class="fw-semibold">回调服务</div>
                                                            <small class="text-warning">延迟</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="text-center">
                                                            <div class="status-indicator status-success mb-2"></div>
                                                            <div class="fw-semibold">结算服务</div>
                                                            <small class="text-success">正常</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 反馈建议 -->
                            <div class="tab-pane fade" id="feedback">
                                <div class="row g-4">
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">提交反馈</h6>
                                            </div>
                                            <div class="card-body">
                                                <form id="feedbackForm">
                                                    <div class="row g-3">
                                                        <div class="col-md-6">
                                                            <label class="form-label">反馈类型</label>
                                                            <select class="form-select" id="feedbackType" required>
                                                                <option value="">请选择类型</option>
                                                                <option value="bug">问题报告</option>
                                                                <option value="feature">功能建议</option>
                                                                <option value="improvement">改进建议</option>
                                                                <option value="other">其他</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label class="form-label">优先级</label>
                                                            <select class="form-select" id="feedbackPriority">
                                                                <option value="low">低</option>
                                                                <option value="medium" selected>中</option>
                                                                <option value="high">高</option>
                                                                <option value="urgent">紧急</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-12">
                                                            <label class="form-label">标题</label>
                                                            <input type="text" class="form-control" id="feedbackTitle" required>
                                                        </div>
                                                        <div class="col-12">
                                                            <label class="form-label">详细描述</label>
                                                            <textarea class="form-control" id="feedbackContent" rows="5" required></textarea>
                                                        </div>
                                                        <div class="col-12">
                                                            <label class="form-label">附件</label>
                                                            <input type="file" class="form-control" id="feedbackAttachment" multiple>
                                                            <div class="form-text">支持图片、文档等文件，最大10MB</div>
                                                        </div>
                                                        <div class="col-12">
                                                            <button type="submit" class="btn btn-primary" id="submitFeedbackBtn">
                                                                <i class="bi bi-send me-2"></i>提交反馈
                                                            </button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">我的反馈</h6>
                                            </div>
                                            <div class="card-body">
                                                <div id="myFeedbackContainer">
                                                    <!-- 我的反馈将动态生成 -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            ${this.generateMerchantToolsStyles()}
        `;
    }

    /**
     * 生成商户工具样式
     * @returns {string} CSS样式
     */
    generateMerchantToolsStyles() {
        return `
            <style>
                .merchant-tools .stat-card {
                    background: white;
                    border-radius: 12px;
                    padding: 20px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }

                .merchant-tools .stat-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                }

                .merchant-tools .stat-icon {
                    width: 50px;
                    height: 50px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .merchant-tools .stat-content {
                    flex: 1;
                }

                .merchant-tools .stat-number {
                    font-size: 1.8rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin-bottom: 4px;
                }

                .merchant-tools .stat-label {
                    color: #6b7280;
                    font-size: 14px;
                }

                .merchant-tools .tool-card {
                    border: 1px solid #e5e7eb;
                    border-radius: 12px;
                    padding: 20px;
                    margin-bottom: 20px;
                    transition: all 0.3s ease;
                    background: white;
                    height: 100%;
                }

                .merchant-tools .tool-card:hover {
                    border-color: #3b82f6;
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
                    transform: translateY(-2px);
                }

                .merchant-tools .tool-icon {
                    width: 60px;
                    height: 60px;
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.8rem;
                    margin-bottom: 15px;
                }

                .merchant-tools .tool-payment {
                    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
                }

                .merchant-tools .tool-analysis {
                    background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
                }

                .merchant-tools .tool-management {
                    background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
                }

                .merchant-tools .tool-integration {
                    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
                }

                .merchant-tools .tool-rating {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    margin-top: 10px;
                }

                .merchant-tools .rating-stars {
                    color: #fbbf24;
                }

                .merchant-tools .status-indicator {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    display: inline-block;
                }

                .merchant-tools .status-success {
                    background: #10b981;
                }

                .merchant-tools .status-warning {
                    background: #f59e0b;
                }

                .merchant-tools .status-error {
                    background: #ef4444;
                }

                .merchant-tools .popular-tool-item {
                    display: flex;
                    align-items: center;
                    padding: 10px 0;
                    border-bottom: 1px solid #f3f4f6;
                }

                .merchant-tools .popular-tool-item:last-child {
                    border-bottom: none;
                }

                .merchant-tools .popular-tool-icon {
                    width: 40px;
                    height: 40px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 12px;
                    font-size: 16px;
                    color: white;
                }

                .merchant-tools .feedback-item {
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                    padding: 12px;
                    margin-bottom: 12px;
                }

                .merchant-tools .feedback-status {
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 11px;
                    font-weight: 500;
                }

                .merchant-tools .feedback-pending {
                    background: #fef3c7;
                    color: #92400e;
                }

                .merchant-tools .feedback-processing {
                    background: #dbeafe;
                    color: #1e40af;
                }

                .merchant-tools .feedback-completed {
                    background: #dcfce7;
                    color: #166534;
                }

                .merchant-tools .nav-tabs .nav-link {
                    color: #6b7280;
                    border: none;
                    padding: 12px 20px;
                }

                .merchant-tools .nav-tabs .nav-link.active {
                    color: #3b82f6;
                    background: white;
                    border-bottom: 2px solid #3b82f6;
                }

                .merchant-tools .form-label {
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 6px;
                }

                .merchant-tools .form-control:focus,
                .merchant-tools .form-select:focus {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
            </style>
        `;
    }

    /**
     * 初始化商户工具事件
     */
    initializeMerchantToolsEvents() {
        // 添加工具按钮
        document.getElementById('addToolBtn')?.addEventListener('click', () => {
            this.showAddToolModal();
        });

        // 工具设置按钮
        document.getElementById('toolSettingsBtn')?.addEventListener('click', () => {
            this.showToolSettings();
        });

        // 刷新工具按钮
        document.getElementById('refreshToolsBtn')?.addEventListener('click', () => {
            this.loadToolsList();
        });

        // 自助服务按钮
        document.getElementById('generateAPIKeyBtn')?.addEventListener('click', () => {
            this.generateAPIKey();
        });

        document.getElementById('downloadSDKBtn')?.addEventListener('click', () => {
            this.downloadSDK();
        });

        document.getElementById('viewDocsBtn')?.addEventListener('click', () => {
            this.viewDocumentation();
        });

        document.getElementById('contactSupportBtn')?.addEventListener('click', () => {
            this.contactSupport();
        });

        // 反馈表单提交
        document.getElementById('feedbackForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmitFeedback();
        });

        // 选项卡切换事件
        document.querySelectorAll('#toolsTabs .nav-link').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const target = e.target.getAttribute('href').substring(1);
                this.handleTabChange(target);
            });
        });
    }

    /**
     * 加载商户工具数据
     */
    async loadMerchantToolsData() {
        try {
            await this.updateToolsStats();
            await this.loadToolsList();
        } catch (error) {
            console.error('加载商户工具数据失败:', error);
            this.utils.showMessage('加载商户工具数据失败', 'error');
        }
    }

    /**
     * 更新工具统计数据
     */
    async updateToolsStats() {
        try {
            // 模拟统计数据
            document.getElementById('totalToolsCount').textContent = '18';
            document.getElementById('activeMerchantsCount').textContent = '245';
            document.getElementById('todayUsageCount').textContent = '1,567';
            document.getElementById('avgRatingCount').textContent = '4.8';
        } catch (error) {
            console.error('加载工具统计失败:', error);
        }
    }

    /**
     * 加载工具列表
     */
    async loadToolsList() {
        try {
            // 模拟工具数据
            this.currentTools = [
                {
                    id: 1,
                    name: '支付接口',
                    description: '快速集成支付功能到您的应用',
                    category: 'payment',
                    icon: 'credit-card',
                    usage_count: 1234,
                    rating: 4.8,
                    status: 'active'
                },
                {
                    id: 2,
                    name: '数据分析',
                    description: '实时查看交易数据和趋势分析',
                    category: 'analysis',
                    icon: 'graph-up',
                    usage_count: 856,
                    rating: 4.6,
                    status: 'active'
                },
                {
                    id: 3,
                    name: '账户管理',
                    description: '管理商户账户信息和设置',
                    category: 'management',
                    icon: 'person-gear',
                    usage_count: 642,
                    rating: 4.5,
                    status: 'active'
                },
                {
                    id: 4,
                    name: 'SDK集成',
                    description: '多语言SDK快速集成工具',
                    category: 'integration',
                    icon: 'code-slash',
                    usage_count: 423,
                    rating: 4.7,
                    status: 'active'
                }
            ];
            
            this.renderToolsList();
        } catch (error) {
            console.error('加载工具列表失败:', error);
            this.showToolsError('加载工具列表失败');
        }
    }

    /**
     * 渲染工具列表
     */
    renderToolsList() {
        const container = document.getElementById('toolsContainer');
        if (!container) return;

        if (this.currentTools.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center py-4">
                    <i class="bi bi-tools" style="font-size: 3rem; color: #6b7280;"></i>
                    <h5 class="mt-3 text-muted">暂无工具数据</h5>
                    <p class="text-muted">点击上方"添加工具"按钮开始配置</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.currentTools.map(tool => `
            <div class="col-md-6 col-lg-4">
                <div class="tool-card">
                    <div class="tool-icon tool-${tool.category}">
                        <i class="bi bi-${tool.icon}"></i>
                    </div>
                    <div class="mb-3">
                        <h6 class="mb-1">${tool.name}</h6>
                        <p class="text-muted mb-0" style="font-size: 13px;">${tool.description}</p>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between text-sm">
                            <span>使用次数:</span>
                            <span class="fw-bold">${this.utils.formatNumber(tool.usage_count)}</span>
                        </div>
                        <div class="tool-rating">
                            <div class="rating-stars">
                                ${this.generateStars(tool.rating)}
                            </div>
                            <span class="fw-bold">${tool.rating}</span>
                        </div>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-sm flex-fill" 
                                onclick="merchantToolsManager.useTool(${tool.id})">
                            <i class="bi bi-play-circle me-1"></i>使用
                        </button>
                        <button class="btn btn-outline-info btn-sm" 
                                onclick="merchantToolsManager.showToolDetails(${tool.id})" title="详情">
                            <i class="bi bi-info-circle"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 显示工具错误
     * @param {string} message 错误信息
     */
    showToolsError(message) {
        const container = document.getElementById('toolsContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="col-12 text-center py-4">
                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-danger">${message}</h5>
                <button class="btn btn-outline-primary mt-2" onclick="merchantToolsManager.loadToolsList()">
                    重新加载
                </button>
            </div>
        `;
    }

    /**
     * 处理选项卡切换
     * @param {string} target 目标选项卡
     */
    handleTabChange(target) {
        switch (target) {
            case 'tools':
                this.loadToolsList();
                break;
            case 'analytics':
                this.loadAnalyticsData();
                break;
            case 'self-service':
                this.loadSelfServiceData();
                break;
            case 'feedback':
                this.loadFeedbackData();
                break;
        }
    }

    /**
     * 加载分析数据
     */
    loadAnalyticsData() {
        this.loadPopularTools();
        this.loadMerchantActivity();
    }

    /**
     * 加载热门工具
     */
    loadPopularTools() {
        const container = document.getElementById('popularToolsContainer');
        if (!container) return;

        // 模拟热门工具数据
        const popularTools = [
            { name: '支付接口', icon: 'credit-card', category: 'payment', usage: 1234 },
            { name: '数据分析', icon: 'graph-up', category: 'analysis', usage: 856 },
            { name: 'SDK集成', icon: 'code-slash', category: 'integration', usage: 642 }
        ];

        container.innerHTML = popularTools.map(tool => `
            <div class="popular-tool-item">
                <div class="popular-tool-icon tool-${tool.category}">
                    <i class="bi bi-${tool.icon}"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold">${tool.name}</div>
                    <small class="text-muted">使用 ${this.utils.formatNumber(tool.usage)} 次</small>
                </div>
            </div>
        `).join('');
    }

    /**
     * 加载商户活跃度
     */
    loadMerchantActivity() {
        const tbody = document.getElementById('merchantActivityTableBody');
        if (!tbody) return;

        // 模拟商户活跃度数据
        const merchants = [
            { name: '商户A', tools: 8, usage: 156, activity: 'high', lastUsed: '2小时前' },
            { name: '商户B', tools: 5, usage: 89, activity: 'medium', lastUsed: '1天前' },
            { name: '商户C', tools: 12, usage: 234, activity: 'high', lastUsed: '30分钟前' }
        ];

        tbody.innerHTML = merchants.map(merchant => `
            <tr>
                <td>${merchant.name}</td>
                <td>${merchant.tools}</td>
                <td>${merchant.usage}</td>
                <td>
                    <span class="badge bg-${merchant.activity === 'high' ? 'success' : 'warning'}">
                        ${merchant.activity === 'high' ? '高' : '中'}
                    </span>
                </td>
                <td>${merchant.lastUsed}</td>
            </tr>
        `).join('');
    }

    /**
     * 加载自助服务数据
     */
    loadSelfServiceData() {
        // 自助服务数据已在HTML中静态定义
        console.log('自助服务数据加载完成');
    }

    /**
     * 加载反馈数据
     */
    loadFeedbackData() {
        const container = document.getElementById('myFeedbackContainer');
        if (!container) return;

        // 模拟反馈数据
        const feedbacks = [
            {
                id: 1,
                title: '支付接口优化建议',
                type: 'feature',
                status: 'processing',
                date: '2024-01-15'
            },
            {
                id: 2,
                title: '数据统计问题',
                type: 'bug',
                status: 'completed',
                date: '2024-01-12'
            }
        ];

        container.innerHTML = feedbacks.map(feedback => `
            <div class="feedback-item">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">${feedback.title}</h6>
                    <span class="feedback-status feedback-${feedback.status}">
                        ${this.getFeedbackStatusText(feedback.status)}
                    </span>
                </div>
                <div class="text-muted" style="font-size: 12px;">
                    ${feedback.type} • ${feedback.date}
                </div>
            </div>
        `).join('');
    }

    /**
     * 生成星级评分
     * @param {number} rating 评分
     * @returns {string} 星级HTML
     */
    generateStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;
        let stars = '';

        for (let i = 0; i < fullStars; i++) {
            stars += '<i class="bi bi-star-fill"></i>';
        }

        if (hasHalfStar) {
            stars += '<i class="bi bi-star-half"></i>';
        }

        const emptyStars = 5 - Math.ceil(rating);
        for (let i = 0; i < emptyStars; i++) {
            stars += '<i class="bi bi-star"></i>';
        }

        return stars;
    }

    /**
     * 获取反馈状态文本
     * @param {string} status 状态
     * @returns {string} 状态文本
     */
    getFeedbackStatusText(status) {
        const statusMap = {
            'pending': '待处理',
            'processing': '处理中',
            'completed': '已完成'
        };
        return statusMap[status] || status;
    }

    /**
     * 使用工具
     * @param {number} toolId 工具ID
     */
    useTool(toolId) {
        this.utils.showMessage('正在启动工具...', 'info');
        
        // 模拟工具启动
        setTimeout(() => {
            this.utils.showMessage('工具启动成功', 'success');
        }, 1000);
    }

    /**
     * 显示工具详情
     * @param {number} toolId 工具ID
     */
    showToolDetails(toolId) {
        this.utils.showMessage('工具详情功能开发中', 'info');
    }

    /**
     * 显示添加工具模态框
     */
    showAddToolModal() {
        this.utils.showMessage('添加工具功能开发中', 'info');
    }

    /**
     * 显示工具设置
     */
    showToolSettings() {
        this.utils.showMessage('工具设置功能开发中', 'info');
    }

    /**
     * 生成API密钥
     */
    generateAPIKey() {
        this.utils.showMessage('API密钥生成成功', 'success');
    }

    /**
     * 下载SDK
     */
    downloadSDK() {
        this.utils.showMessage('SDK下载功能开发中', 'info');
    }

    /**
     * 查看文档
     */
    viewDocumentation() {
        this.utils.showMessage('文档查看功能开发中', 'info');
    }

    /**
     * 联系客服
     */
    contactSupport() {
        this.utils.showMessage('客服联系功能开发中', 'info');
    }

    /**
     * 处理提交反馈
     */
    handleSubmitFeedback() {
        const form = document.getElementById('feedbackForm');
        
        // 表单验证
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // 模拟提交
        this.utils.showMessage('反馈提交成功，我们会尽快处理', 'success');
        form.reset();
        this.loadFeedbackData();
    }

    /**
     * 加载商户仪表板
     */
    loadMerchantDashboard(container) {
        container.innerHTML = `
            <div class="merchant-dashboard">
                <div class="page-header">
                    <h2><i class="bi bi-speedometer2 me-2"></i>商户仪表板</h2>
                    <p class="text-muted">实时监控商户业务数据和关键指标</p>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">¥125,680</div>
                                <div class="stat-label">今日交易额</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">1,234</div>
                                <div class="stat-label">今日订单数</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">89</div>
                                <div class="stat-label">活跃商户</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number">3</div>
                                <div class="stat-label">待处理问题</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-4 mt-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">交易趋势</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="transactionChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">热门产品</h6>
                            </div>
                            <div class="card-body">
                                <div class="popular-products">
                                    <div class="product-item">
                                        <div class="product-name">支付宝收款</div>
                                        <div class="product-stats">¥45,230 / 156单</div>
                                    </div>
                                    <div class="product-item">
                                        <div class="product-name">微信支付</div>
                                        <div class="product-stats">¥38,450 / 134单</div>
                                    </div>
                                    <div class="product-item">
                                        <div class="product-name">银行卡支付</div>
                                        <div class="product-stats">¥32,000 / 89单</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载商户订单页面
     */
    loadMerchantOrders(container) {
        container.innerHTML = `
            <div class="merchant-orders">
                <div class="page-header">
                    <h2><i class="bi bi-list-ul me-2"></i>商户订单</h2>
                    <p class="text-muted">管理和查看所有商户订单信息</p>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">订单列表</h6>
                            <div>
                                <select class="form-select form-select-sm me-2" style="width: 120px; display: inline-block;">
                                    <option>全部状态</option>
                                    <option>待支付</option>
                                    <option>已支付</option>
                                    <option>已退款</option>
                                </select>
                                <button class="btn btn-primary btn-sm">
                                    <i class="bi bi-search me-1"></i>搜索
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>商户</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>ORD20240115001</td>
                                        <td>商户A</td>
                                        <td>¥1,000.00</td>
                                        <td><span class="badge bg-success">已支付</span></td>
                                        <td>2024-01-15 10:30</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">查看</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>ORD20240115002</td>
                                        <td>商户B</td>
                                        <td>¥2,500.00</td>
                                        <td><span class="badge bg-warning">待支付</span></td>
                                        <td>2024-01-15 11:15</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">查看</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载商户统计页面
     */
    loadMerchantStatistics(container) {
        container.innerHTML = `
            <div class="merchant-statistics">
                <div class="page-header">
                    <h2><i class="bi bi-graph-up me-2"></i>商户统计</h2>
                    <p class="text-muted">详细的商户业务数据分析和报表</p>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">商户分布</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="merchantChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">交易统计</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="transactionStatsChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载商户产品页面
     */
    loadMerchantProducts(container) {
        container.innerHTML = `
            <div class="merchant-products">
                <div class="page-header">
                    <h2><i class="bi bi-box me-2"></i>商户产品</h2>
                    <p class="text-muted">管理商户可用的产品和服务</p>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">产品列表</h6>
                            <button class="btn btn-primary btn-sm">
                                <i class="bi bi-plus me-1"></i>添加产品
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="product-card">
                                    <div class="product-icon bg-primary">
                                        <i class="bi bi-credit-card"></i>
                                    </div>
                                    <div class="product-info">
                                        <h6>支付宝收款</h6>
                                        <p class="text-muted">支持支付宝扫码支付</p>
                                        <div class="product-status">
                                            <span class="badge bg-success">已启用</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="product-card">
                                    <div class="product-icon bg-success">
                                        <i class="bi bi-chat-dots"></i>
                                    </div>
                                    <div class="product-info">
                                        <h6>微信支付</h6>
                                        <p class="text-muted">支持微信扫码支付</p>
                                        <div class="product-status">
                                            <span class="badge bg-success">已启用</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载API密钥页面
     */
    loadAPIKeys(container) {
        container.innerHTML = `
            <div class="api-keys">
                <div class="page-header">
                    <h2><i class="bi bi-key me-2"></i>API密钥</h2>
                    <p class="text-muted">管理商户的API访问密钥</p>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">密钥列表</h6>
                            <button class="btn btn-primary btn-sm" onclick="merchantToolsManager.generateAPIKey()">
                                <i class="bi bi-plus me-1"></i>生成新密钥
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>密钥名称</th>
                                        <th>密钥ID</th>
                                        <th>创建时间</th>
                                        <th>最后使用</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>生产环境密钥</td>
                                        <td>ak_1234567890abcdef</td>
                                        <td>2024-01-01</td>
                                        <td>2024-01-15 10:30</td>
                                        <td><span class="badge bg-success">活跃</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-danger">撤销</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载代码生成器页面
     */
    loadCodeGenerator(container) {
        container.innerHTML = `
            <div class="code-generator">
                <div class="page-header">
                    <h2><i class="bi bi-code-slash me-2"></i>代码生成器</h2>
                    <p class="text-muted">生成各种编程语言的API集成代码</p>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">选择语言</h6>
                            </div>
                            <div class="card-body">
                                <select class="form-select mb-3">
                                    <option>PHP</option>
                                    <option>JavaScript</option>
                                    <option>Python</option>
                                    <option>Java</option>
                                    <option>C#</option>
                                </select>
                                <select class="form-select mb-3">
                                    <option>支付宝收款</option>
                                    <option>微信支付</option>
                                    <option>银行卡支付</option>
                                </select>
                                <button class="btn btn-primary w-100">
                                    <i class="bi bi-download me-1"></i>生成代码
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">生成的代码</h6>
                            </div>
                            <div class="card-body">
                                <pre><code class="language-php">
// 支付宝收款示例代码
$apiKey = 'your_api_key';
$apiSecret = 'your_api_secret';
$timestamp = time();

$params = [
    'amount' => 100.00,
    'order_id' => 'ORDER_' . $timestamp,
    'notify_url' => 'https://your-domain.com/notify'
];

$sign = hash_hmac('sha256', json_encode($params) . $apiKey . $apiSecret . $timestamp, $apiSecret);
                                </code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载签名工具页面
     */
    loadSignatureTool(container) {
        container.innerHTML = `
            <div class="signature-tool">
                <div class="page-header">
                    <h2><i class="bi bi-shield-check me-2"></i>签名工具</h2>
                    <p class="text-muted">生成和验证API请求签名</p>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">生成签名</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">API密钥</label>
                                    <input type="text" class="form-control" placeholder="输入API密钥">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">请求参数</label>
                                    <textarea class="form-control" rows="5" placeholder="输入JSON格式的请求参数"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">时间戳</label>
                                    <input type="text" class="form-control" value="${Date.now()}" readonly>
                                </div>
                                <button class="btn btn-primary">
                                    <i class="bi bi-shield-check me-1"></i>生成签名
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">验证签名</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">签名</label>
                                    <input type="text" class="form-control" placeholder="输入要验证的签名">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">原始数据</label>
                                    <textarea class="form-control" rows="5" placeholder="输入原始请求数据"></textarea>
                                </div>
                                <button class="btn btn-success">
                                    <i class="bi bi-check-circle me-1"></i>验证签名
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载API文档页面
     */
    loadAPIDocumentation(container) {
        container.innerHTML = `
            <div class="api-documentation">
                <div class="page-header">
                    <h2><i class="bi bi-book me-2"></i>API文档</h2>
                    <p class="text-muted">完整的API接口文档和使用指南</p>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">接口分类</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <a href="#" class="list-group-item list-group-item-action active">
                                        <i class="bi bi-credit-card me-2"></i>支付接口
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action">
                                        <i class="bi bi-list-ul me-2"></i>订单接口
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action">
                                        <i class="bi bi-people me-2"></i>商户接口
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action">
                                        <i class="bi bi-graph-up me-2"></i>统计接口
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-9">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">支付接口文档</h6>
                            </div>
                            <div class="card-body">
                                <h5>创建支付订单</h5>
                                <p>用于创建新的支付订单，支持多种支付方式。</p>
                                
                                <h6>请求地址</h6>
                                <code>POST /api/payment/create</code>
                                
                                <h6>请求参数</h6>
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>参数名</th>
                                            <th>类型</th>
                                            <th>必填</th>
                                            <th>说明</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>amount</td>
                                            <td>decimal</td>
                                            <td>是</td>
                                            <td>支付金额</td>
                                        </tr>
                                        <tr>
                                            <td>order_id</td>
                                            <td>string</td>
                                            <td>是</td>
                                            <td>商户订单号</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载商户配置页面
     */
    loadMerchantConfiguration(container) {
        container.innerHTML = `
            <div class="merchant-configuration">
                <div class="page-header">
                    <h2><i class="bi bi-gear me-2"></i>商户配置</h2>
                    <p class="text-muted">配置商户的基本信息和业务参数</p>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">基本信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">商户名称</label>
                                    <input type="text" class="form-control" value="示例商户">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">联系人</label>
                                    <input type="text" class="form-control" value="张三">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">联系电话</label>
                                    <input type="text" class="form-control" value="13800138000">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">邮箱地址</label>
                                    <input type="email" class="form-control" value="<EMAIL>">
                                </div>
                                <button class="btn btn-primary">
                                    <i class="bi bi-check me-1"></i>保存基本信息
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">业务配置</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">单笔限额</label>
                                    <input type="number" class="form-control" value="10000">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">日限额</label>
                                    <input type="number" class="form-control" value="100000">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">手续费率</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" value="0.6" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">结算周期</label>
                                    <select class="form-select">
                                        <option>T+1</option>
                                        <option>T+3</option>
                                        <option>T+7</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary">
                                    <i class="bi bi-check me-1"></i>保存业务配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

// 创建全局实例
const merchantToolsManager = new MerchantToolsManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MerchantToolsManager };
} else {
    // 浏览器环境
    window.MerchantToolsManager = MerchantToolsManager;
    window.merchantToolsManager = merchantToolsManager;
} 