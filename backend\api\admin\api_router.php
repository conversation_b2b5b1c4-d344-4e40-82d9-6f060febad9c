<?php
/**
 * API路由管理接口
 * 提供路由配置信息、统计数据和管理功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

// 验证是否在管理面板中调用
if (!defined('IN_ADMIN_PANEL')) {
    http_response_code(403);
    exit('Access denied');
}

// 引入API路由系统
require_once dirname(__FILE__) . '/../core/ApiRouter.php';
require_once dirname(__FILE__) . '/../core/ApiMiddleware.php';

// 获取全局变量
$tenantAuth = $GLOBALS['tenant_auth'] ?? null;
$currentUser = $GLOBALS['current_user'] ?? null;
$domainInfo = $GLOBALS['domain_info'] ?? null;

// 权限检查 - 只有系统管理员可以访问
if (!$currentUser || $currentUser['user_type'] !== 'system_admin') {
    http_response_code(403);
    echo json_encode([
        'code' => 403,
        'message' => '权限不足，只有系统管理员可以访问API路由管理'
    ]);
    exit;
}

// 获取请求参数
$input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
$action = isset($_GET['action']) ? $_GET['action'] : (isset($input['action']) ? $input['action'] : '');

try {
    switch ($action) {
        case 'get_api_routes':
            // 获取所有API路由配置
            handleGetApiRoutes();
            break;
            
        case 'get_route_stats':
            // 获取路由统计信息
            handleGetRouteStats();
            break;
            
        case 'get_api_call_logs':
            // 获取API调用日志
            handleGetApiCallLogs($input);
            break;
            
        case 'test_route':
            // 测试指定路由
            handleTestRoute($input);
            break;
            
        case 'export_routes':
            // 导出路由配置
            handleExportRoutes();
            break;
            
        case 'get_route_performance':
            // 获取路由性能数据
            handleGetRoutePerformance($input);
            break;
            
        default:
            throw new Exception('未知的操作类型: ' . $action);
    }
    
} catch (Exception $e) {
    error_log("API路由管理错误: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'code' => 500,
        'message' => '操作失败: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * 获取所有API路由配置
 */
function handleGetApiRoutes() {
    try {
        // 获取所有路由配置
        $routes = ApiRouter::getAllRoutes();
        $stats = ApiRouter::getRouteStats();
        
        // 格式化路由数据
        $formattedRoutes = [];
        foreach ($routes as $name => $config) {
            $formattedRoutes[] = [
                'name' => $name,
                'method' => $config['method'],
                'path' => $config['path'],
                'permissions' => $config['permissions'],
                'description' => $config['description'],
                'tenant_filter' => $config['tenant_filter'],
                'rate_limit' => $config['rate_limit'],
                'cache_ttl' => $config['cache_ttl']
            ];
        }
        
        echo json_encode([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'routes' => $formattedRoutes,
                'stats' => $stats,
                'total_count' => count($formattedRoutes)
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取路由配置失败: ' . $e->getMessage());
    }
}

/**
 * 获取路由统计信息
 */
function handleGetRouteStats() {
    try {
        $stats = ApiRouter::getRouteStats();
        
        // 获取API中间件统计
        if (isset($GLOBALS['api_middleware'])) {
            $apiStats = $GLOBALS['api_middleware']->getApiStats('7 days');
            $stats['api_calls'] = $apiStats;
        }
        
        echo json_encode([
            'code' => 0,
            'message' => '获取成功',
            'data' => $stats
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取统计信息失败: ' . $e->getMessage());
    }
}

/**
 * 获取API调用日志
 */
function handleGetApiCallLogs($input) {
    try {
        $page = isset($input['page']) ? (int)$input['page'] : 1;
        $pageSize = isset($input['pageSize']) ? (int)$input['pageSize'] : 50;
        $timeRange = isset($input['timeRange']) ? $input['timeRange'] : '1 day';
        
        $logFile = '/data/logs/api_calls.log';
        
        if (!file_exists($logFile)) {
            echo json_encode([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'logs' => [],
                    'pagination' => [
                        'current_page' => 1,
                        'total_pages' => 0,
                        'total_records' => 0
                    ]
                ]
            ]);
            return;
        }
        
        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $cutoffTime = strtotime("-{$timeRange}");
        
        // 过滤时间范围内的日志
        $filteredLogs = [];
        foreach (array_reverse($lines) as $line) {
            $data = json_decode($line, true);
            if ($data && strtotime($data['timestamp']) >= $cutoffTime) {
                $filteredLogs[] = $data;
            }
        }
        
        // 分页处理
        $totalRecords = count($filteredLogs);
        $totalPages = ceil($totalRecords / $pageSize);
        $offset = ($page - 1) * $pageSize;
        $pagedLogs = array_slice($filteredLogs, $offset, $pageSize);
        
        echo json_encode([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'logs' => $pagedLogs,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_records' => $totalRecords,
                    'page_size' => $pageSize
                ]
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取API调用日志失败: ' . $e->getMessage());
    }
}

/**
 * 测试指定路由
 */
function handleTestRoute($input) {
    try {
        $routeName = isset($input['route_name']) ? $input['route_name'] : '';
        $method = isset($input['method']) ? $input['method'] : 'GET';
        $params = isset($input['params']) ? $input['params'] : [];
        
        if (empty($routeName)) {
            throw new Exception('路由名称不能为空');
        }
        
        // 获取路由配置
        $route = ApiRouter::getRoute($routeName);
        if (!$route) {
            throw new Exception('路由不存在: ' . $routeName);
        }
        
        // 创建API中间件进行测试
        $middleware = new ApiMiddleware($GLOBALS['tenant_auth']);
        $result = $middleware->handleRequest($routeName, $method);
        
        if ($result['success']) {
            echo json_encode([
                'code' => 0,
                'message' => '路由测试成功',
                'data' => [
                    'route_name' => $routeName,
                    'method' => $method,
                    'route_config' => $route,
                    'test_result' => $result,
                    'test_time' => date('Y-m-d H:i:s')
                ]
            ]);
        } else {
            echo json_encode([
                'code' => 400,
                'message' => '路由测试失败: ' . $result['error']['message'],
                'data' => [
                    'route_name' => $routeName,
                    'error' => $result['error']
                ]
            ]);
        }
        
    } catch (Exception $e) {
        throw new Exception('测试路由失败: ' . $e->getMessage());
    }
}

/**
 * 导出路由配置
 */
function handleExportRoutes() {
    try {
        $routes = ApiRouter::getAllRoutes();
        $stats = ApiRouter::getRouteStats();
        
        $exportData = [
            'export_info' => [
                'exported_at' => date('Y-m-d H:i:s'),
                'exported_by' => $GLOBALS['current_user']['username'] ?? 'unknown',
                'version' => '1.0.0',
                'total_routes' => count($routes)
            ],
            'statistics' => $stats,
            'routes' => $routes
        ];
        
        // 设置下载头
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="api-routes-' . date('Y-m-d-H-i-s') . '.json"');
        header('Content-Length: ' . strlen(json_encode($exportData, JSON_PRETTY_PRINT)));
        
        echo json_encode($exportData, JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        throw new Exception('导出路由配置失败: ' . $e->getMessage());
    }
}

/**
 * 获取路由性能数据
 */
function handleGetRoutePerformance($input) {
    try {
        $timeRange = isset($input['timeRange']) ? $input['timeRange'] : '1 day';
        
        // 获取API中间件统计
        $apiStats = [];
        if (isset($GLOBALS['api_middleware'])) {
            $apiStats = $GLOBALS['api_middleware']->getApiStats($timeRange);
        }
        
        // 模拟性能数据（实际项目中应该从监控系统获取）
        $performanceData = [
            'response_times' => [
                'avg' => 150,
                'p50' => 120,
                'p90' => 250,
                'p99' => 500
            ],
            'error_rates' => [
                'total_requests' => $apiStats['total_calls'] ?? 0,
                'error_count' => 0,
                'error_rate' => 0
            ],
            'throughput' => [
                'requests_per_minute' => round(($apiStats['total_calls'] ?? 0) / (24 * 60), 2),
                'peak_rps' => 10
            ],
            'top_slow_routes' => [],
            'top_error_routes' => []
        ];
        
        echo json_encode([
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'performance' => $performanceData,
                'api_stats' => $apiStats,
                'time_range' => $timeRange
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception('获取性能数据失败: ' . $e->getMessage());
    }
}

?> 