/**
 * 脚本管理器
 * 从admin.js第4448-5232行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class ScriptManager {
    constructor() {
        this.auth = new AuthManager();
        this.currentPage = 1;
        this.pageSize = 20;
        this.deviceBrands = [];
        this.scriptMatrix = {
        };
    }
    async loadScripts(page = 1) {
        this.currentPage = page;
        try {
            // 并行加载设备品牌和脚本矩阵
            const [brandsResponse,
            matrixResponse] = await Promise.all([
            this.loadDeviceBrands(),
            this.loadScriptMatrix()
            ]);
            if (brandsResponse && matrixResponse) {
                this.renderScriptMatrix();
            }
        } catch (error) {
            console.error('Load scripts error:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async loadDeviceBrands() {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=get_device_brands`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.success) {
                // 转换数据格式，添加brand_code字段（使用device_brand作为code）
                this.deviceBrands = data.data.map(brand => ({
                    brand_code: brand.device_brand,
                    brand_name: this.getBrandDisplayName(brand.device_brand),
                    device_count: brand.device_count,
                    alipay_devices: brand.alipay_devices,
                    wechat_devices: brand.wechat_devices,
                    alipay_versions: brand.alipay_versions,
                    wechat_versions: brand.wechat_versions
                }));
                return true;
            } else {
                this.showError('加载设备品牌失败: ' + data.message);
                return false;
            }
        } catch (error) {
            console.error('Load device brands error:',
            error);
            return false;
        }
    }
    // 获取品牌显示名称
    getBrandDisplayName(brandCode) {
        const brandNames = {
            'xiaomi': '小米',
            'huawei': '华为',
            'oppo': 'OPPO',
            'vivo': 'vivo',
            'samsung': '三星',
            'oneplus': '一加',
            'realme': 'realme',
            'honor': '荣耀',
            'meizu': '魅族'
        };
        return brandNames[brandCode] || brandCode.toUpperCase();
    }
    async loadScriptMatrix() {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=get_script_matrix`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.success) {
                this.scriptMatrix = data.data;
                return true;
            } else {
                this.showError('加载脚本矩阵失败: ' + data.message);
                return false;
            }
        } catch (error) {
            console.error('Load script matrix error:',
            error);
            return false;
        }
    }
    renderScriptMatrix() {
        const container = document.getElementById('scriptMatrixContainer');
        if (!container) return;
        if (this.deviceBrands.length === 0) {
            container.innerHTML = `
            <div class="text-center text-muted py-5">
            <i class="bi bi-inbox" style="font-size: 3rem;
            "></i>
            <div class="mt-3">暂无已注册的设备品牌</div>
            <small>设备注册后会自动显示对应品牌</small>
            </div>
            `;
            return;
        }
        let matrixHtml = `
        <div class="row">
        <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
        <i class="bi bi-grid-3x3-gap-fill me-2"></i>
        脚本管理矩阵
        </h5>
        <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary btn-sm" onclick="window.scriptManager.refreshMatrix()">
        <i class="bi bi-arrow-clockwise"></i> 刷新
        </button>
        </div>
        </div>
        </div>
        </div>
        <div class="row">
        `;
        // 按品牌分组显示
        this.deviceBrands.forEach(brand => {
            const brandScripts = this.scriptMatrix[brand.brand_code] || {
            };
            matrixHtml += `
            <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card h-100">
            <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
            <i class="bi bi-phone me-2"></i>
            ${
                brand.brand_name
            }
            </h6>
            <span class="badge bg-secondary">${
                brand.device_count || 0
            } 台设备</span>
            </div>
            </div>
            <div class="card-body">
            ${
                this.renderBrandScripts(brand.brand_code,
                brandScripts)
            }
            </div>
            </div>
            </div>
            `;
        });
        matrixHtml += '</div>';
        container.innerHTML = matrixHtml;
    }
    renderBrandScripts(brandCode,
    scripts) {
        const appTypes = ['alipay', 'wechat'];
        const environments = ['release', 'debug'];
        let html = '';
        appTypes.forEach(appType => {
            const appName = appType === 'alipay' ? '支付宝' : '微信';
            const appIcon = appType === 'alipay' ? 'bi-alipay' : 'bi-wechat';
            html += `
            <div class="mb-3">
            <h6 class="text-primary mb-2">
            <i class="bi ${
                appIcon
            } me-1"></i>
            ${
                appName
            }
            </h6>
            <div class="row g-2">
            `;
            environments.forEach(env => {
                const envName = env === 'release' ? '正式版' : '调试版';
                const envClass = env === 'release' ? 'success' : 'warning';
                const scriptKey = `${
                    appType
                }_${
                    env
                }`;
                const hasScript = scripts[scriptKey];
                html += `
                <div class="col-6">
                <div class="d-grid">
                ${
                    hasScript ?
                    `<button class="btn btn-outline-${
                        envClass
                    } btn-sm" onclick="window.scriptManager.viewBrandScript('${
                        brandCode
                    }', '${
                        appType
                    }', '${
                        env
                    }')" title="查看脚本">
                    <i class="bi bi-check-circle me-1"></i>
                    ${
                        envName
                    }
                    </button>` :
                    `<button class="btn btn-outline-secondary btn-sm" onclick="window.scriptManager.uploadBrandScript('${
                        brandCode
                    }', '${
                        appType
                    }', '${
                        env
                    }')" title="上传脚本">
                    <i class="bi bi-plus-circle me-1"></i>
                    ${
                        envName
                    }
                    </button>`
                }
                </div>
                </div>
                `;
            });
            html += `
            </div>
            </div>
            `;
        });
        return html;
    }
    renderPagination(pagination) {
        const nav = document.getElementById('scriptPagination');
        if (!nav || pagination.pages <= 1) {
            nav.style.display = 'none';
            return;
        }
        nav.style.display = 'block';
        const ul = nav.querySelector('ul');
        let paginationHtml = '';
        // 上一页
        if (pagination.page > 1) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.scriptManager.loadScripts(${
                pagination.page - 1
            })">上一页</a>
            </li>
            `;
        }
        // 页码
        const startPage = Math.max(1,
        pagination.page - 2);
        const endPage = Math.min(pagination.pages,
        pagination.page + 2);
        for (let i = startPage;
        i <= endPage;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === pagination.page ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.scriptManager.loadScripts(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        // 下一页
        if (pagination.page < pagination.pages) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.scriptManager.loadScripts(${
                pagination.page + 1
            })">下一页</a>
            </li>
            `;
        }
        ul.innerHTML = paginationHtml;
    }
    refreshMatrix() {
        this.loadScripts();
    }
    async viewBrandScript(brandCode,
    appType,
    environment) {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=get_brand_script`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    brand_code: brandCode,
                    app_type: appType,
                    environment: environment
                })
            });
            const data = await response.json();
            if (data.success) {
                this.showScriptModal(data.data.script_name,
                data.data.content,
                true,
                data.data);
            } else {
                this.showError('获取脚本内容失败: ' + data.message);
            }
        } catch (error) {
            console.error('View brand script error:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    uploadBrandScript(brandCode,
    appType,
    environment) {
        this.showUploadModal(brandCode,
        appType,
        environment);
    }
    showUploadModal(brandCode = '',
    appType = '',
    environment = '') {
        const modalHtml = `
        <div class="modal fade" id="uploadScriptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">上传脚本</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="uploadScriptForm">
        <div class="row">
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">设备品牌 *</label>
        <select class="form-select" id="deviceBrand" required ${
            brandCode ? 'disabled' : ''
        }>
        <option value="">请选择品牌</option>
        ${
            this.deviceBrands.map(brand =>
            `<option value="${
                brand.brand_code
            }" ${
                brand.brand_code === brandCode ? 'selected' : ''
            }>${
                brand.brand_name
            }</option>`
            ).join('')
        }
        </select>
        </div>
        </div>
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">应用类型 *</label>
        <select class="form-select" id="appType" required ${
            appType ? 'disabled' : ''
        }>
        <option value="">请选择应用</option>
        <option value="alipay" ${
            appType === 'alipay' ? 'selected' : ''
        }>支付宝</option>
        <option value="wechat" ${
            appType === 'wechat' ? 'selected' : ''
        }>微信</option>
        </select>
        </div>
        </div>
        </div>
        <div class="row">
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">环境类型 *</label>
        <select class="form-select" id="environment" required ${
            environment ? 'disabled' : ''
        }>
        <option value="">请选择环境</option>
        <option value="release" ${
            environment === 'release' ? 'selected' : ''
        }>正式版</option>
        <option value="debug" ${
            environment === 'debug' ? 'selected' : ''
        }>调试版</option>
        </select>
        </div>
        </div>
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">应用版本</label>
        <input type="text" class="form-control" id="appVersion" placeholder="如: 10.3.96.8100" value="*">
        <div class="form-text">* 表示通用版本，适配所有版本</div>
        </div>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">脚本描述</label>
        <input type="text" class="form-control" id="description" placeholder="简要描述脚本功能">
        </div>
        <div class="mb-3">
        <label class="form-label">选择脚本文件 *</label>
        <div class="file-upload-area" id="fileUploadArea">
        <input type="file" class="form-control" id="scriptFile" accept=".js,.txt" required>
        <div class="upload-hint">
        <i class="bi bi-cloud-upload" style="font-size: 2rem;
        color: #6c757d;
        "></i>
        <div class="mt-2">点击选择文件或拖拽文件到此处</div>
        <small class="text-muted">支持 .js 和 .txt 文件，文件大小限制 1MB</small>
        </div>
        </div>
        </div>
        <div class="mb-3" id="filePreview" style="display: none;
        ">
        <label class="form-label">文件预览</label>
        <textarea class="form-control" id="scriptContent" rows="10" readonly></textarea>
        </div>
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.scriptManager.uploadScript()">
        <span class="upload-text">上传脚本</span>
        <span class="upload-loading d-none">
        <span class="spinner-border spinner-border-sm me-2"></span>
        上传中...
        </span>
        </button>
        </div>
        </div>
        </div>
        </div>
        `;
        // 移除已存在的模态框
        const existingModal = document.getElementById('uploadScriptModal');
        if (existingModal) {
            existingModal.remove();
        }
        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend',
        modalHtml);
        // 添加文件选择事件监听
        const fileInput = document.getElementById('scriptFile');
        const filePreview = document.getElementById('filePreview');
        const scriptContent = document.getElementById('scriptContent');
        const fileUploadArea = document.getElementById('fileUploadArea');
        // 添加拖拽上传样式
        const uploadAreaStyle = `
        <style>
        .file-upload-area {
            position: relative;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        .file-upload-area:hover {
            border-color: #0d6efd;
            background: #e7f3ff;
        }
        .file-upload-area.dragover {
            border-color: #0d6efd;
            background: #e7f3ff;
            transform: scale(1.02);
        }
        .file-upload-area input[type="file"] {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }
        .upload-hint {
            pointer-events: none;
        }
        </style>
        `;
        document.head.insertAdjacentHTML('beforeend',
        uploadAreaStyle);
        // 文件处理函数
        const handleFile = (file) => {
            if (!file) {
                filePreview.style.display = 'none';
                return;
            }
            // 检查文件大小（1MB限制）
            if (file.size > 1024 * 1024) {
                this.showError('文件大小不能超过 1MB');
                fileInput.value = '';
                filePreview.style.display = 'none';
                return;
            }
            // 检查文件类型
            const allowedTypes = ['.js', '.txt'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExtension)) {
                this.showError('只支持 .js 和 .txt 文件');
                fileInput.value = '';
                filePreview.style.display = 'none';
                return;
            }
            // 读取文件内容
            const reader = new FileReader();
            reader.onload = (e) => {
                scriptContent.value = e.target.result;
                filePreview.style.display = 'block';
            };
            reader.readAsText(file);
        };
        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            handleFile(e.target.files[0]);
        });
        // 拖拽事件
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            fileUploadArea.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });
        ['dragenter', 'dragover'].forEach(eventName => {
            fileUploadArea.addEventListener(eventName, () => {
                fileUploadArea.classList.add('dragover');
            });
        });
        ['dragleave', 'drop'].forEach(eventName => {
            fileUploadArea.addEventListener(eventName, () => {
                fileUploadArea.classList.remove('dragover');
            });
        });
        fileUploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFile(files[0]);
            }
        });
        const modal = new bootstrap.Modal(document.getElementById('uploadScriptModal'));
        modal.show();
    }
    async uploadScript() {
        const form = document.getElementById('uploadScriptForm');
        const fileInput = document.getElementById('scriptFile');
        const scriptContent = document.getElementById('scriptContent').value;
        // 验证表单
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        // 验证文件是否选择
        if (!fileInput.files[0]) {
            this.showError('请选择要上传的脚本文件');
            return;
        }
        // 验证脚本内容
        if (!scriptContent.trim()) {
            this.showError('脚本文件内容为空');
            return;
        }
        const appType = document.getElementById('appType').value;
        const appVersion = document.getElementById('appVersion').value;
        const deviceBrand = document.getElementById('deviceBrand').value;
        const environment = document.getElementById('environment').value;
        const description = document.getElementById('description').value;
        const fileName = fileInput.files[0].name;
        this.setUploadLoading(true);
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=upload_brand_script`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    app_type: appType,
                    app_version: appVersion,
                    device_brand: deviceBrand,
                    environment: environment,
                    description: description,
                    script_content: scriptContent,
                    original_filename: fileName
                })
            });
            const data = await response.json();
            if (data.success) {
                this.showSuccess(data.message);
                bootstrap.Modal.getInstance(document.getElementById('uploadScriptModal')).hide();
                this.loadScripts();
                // 重新加载矩阵
            } else {
                this.showError('上传失败: ' + data.message);
            }
        } catch (error) {
            console.error('Upload script error:',
            error);
            this.showError('网络错误，请重试');
        }
        this.setUploadLoading(false);
    }
    async viewScript(scriptName) {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/script.php?action=download&script_name=${
                encodeURIComponent(scriptName)
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.success) {
                this.showScriptModal(scriptName,
                data.data.content,
                true);
            } else {
                this.showError('获取脚本内容失败: ' + data.message);
            }
        } catch (error) {
            console.error('View script error:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async editScript(scriptName) {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/script.php?action=download&script_name=${
                encodeURIComponent(scriptName)
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.success) {
                this.showScriptModal(scriptName,
                data.data.content,
                false);
            } else {
                this.showError('获取脚本内容失败: ' + data.message);
            }
        } catch (error) {
            console.error('Edit script error:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    showScriptModal(scriptName,
    content,
    readonly = false,
    scriptInfo = null) {
        const modalHtml = `
        <div class="modal fade" id="scriptViewModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">${
            readonly ? '查看' : '编辑'
        }脚本: ${
            scriptName
        }</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        ${
            scriptInfo ? `
            <div class="modal-body">
            <div class="row mb-3">
            <div class="col-md-3">
            <strong>版本:</strong> ${
                scriptInfo.version || 'N/A'
            }
            </div>
            <div class="col-md-3">
            <strong>文件大小:</strong> ${
                this.formatFileSize(scriptInfo.file_size || 0)
            }
            </div>
            <div class="col-md-6">
            <strong>更新时间:</strong> ${
                this.formatDateTime(scriptInfo.updated_at || '')
            }
            </div>
            </div>
            ${
                scriptInfo.description ? `
                <div class="mb-3">
                <strong>描述:</strong> ${
                    scriptInfo.description
                }
                </div>
                ` : ''
            }
            <textarea class="form-control" id="scriptViewContent" rows="20" ${
                readonly ? 'readonly' : ''
            }>${
                content
            }</textarea>
            </div>
            ` : `
            <div class="modal-body">
            <textarea class="form-control" id="scriptViewContent" rows="25" ${
                readonly ? 'readonly' : ''
            }>${
                content
            }</textarea>
            </div>
            `
        }
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        ${
            !readonly ? `<button type="button" class="btn btn-primary" onclick="window.scriptManager.saveScript('${
                scriptName
            }')">保存修改</button>` : ''
        }
        </div>
        </div>
        </div>
        </div>
        `;
        // 移除已存在的模态框
        const existingModal = document.getElementById('scriptViewModal');
        if (existingModal) {
            existingModal.remove();
        }
        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend',
        modalHtml);
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('scriptViewModal'));
        modal.show();
        // 设置代码编辑器样式
        const textarea = document.getElementById('scriptViewContent');
        textarea.style.fontFamily = "'Courier New',
        monospace";
        textarea.style.fontSize = '13px';
    }
    async saveScript(scriptName) {
        const content = document.getElementById('scriptViewContent').value;
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/script.php?action=update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    script_name: scriptName,
                    script_content: content,
                    description: '通过管理后台编辑'
                })
            });
            const data = await response.json();
            if (data.success) {
                this.showSuccess(data.message);
                bootstrap.Modal.getInstance(document.getElementById('scriptViewModal')).hide();
                this.loadScripts();
                // 重新加载列表
            } else {
                this.showError('保存失败: ' + data.message);
            }
        } catch (error) {
            console.error('Save script error:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async deleteScript(scriptName) {
        if (!confirm(`确定要删除脚本 "${
            scriptName
        }" 吗？此操作不可撤销。`)) {
            return;
        }
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/script.php?action=delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    script_name: scriptName
                })
            });
            const data = await response.json();
            if (data.success) {
                this.showSuccess(data.message);
                this.loadScripts();
                // 重新加载列表
            } else {
                this.showError('删除失败: ' + data.message);
            }
        } catch (error) {
            console.error('Delete script error:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    setUploadLoading(loading) {
        const textSpan = document.querySelector('.upload-btn-text');
        const loadingSpan = document.querySelector('.upload-btn-loading');
        const button = textSpan?.closest('button');
        if (textSpan) textSpan.style.display = loading ? 'none' : 'inline';
        if (loadingSpan) loadingSpan.style.display = loading ? 'inline' : 'none';
        if (button) button.disabled = loading;
    }
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k,
        i)).toFixed(2)) + ' ' + sizes[i];
    }
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    showSuccess(message) {
        // 简单的成功提示，可以后续改为更好的UI组件
        alert('✅ ' + message);
    }
    showError(message) {
        // 简单的错误提示，可以后续改为更好的UI组件
        alert('❌ ' + message);
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = ScriptManager;
} else if (typeof window !== "undefined") {
    window.ScriptManager = ScriptManager;
}

console.log('📦 ScriptManager 模块加载完成');
