<?php
/**
 * 查询产品可用额度接口
 * 兼容旧系统API，使用新数据库结构
 */

// 引入通用函数
require_once dirname(__FILE__) . '/common.php';

// 获取全局变量
$merchant_info = $GLOBALS['merchant_info'];
$post_data = $GLOBALS['post_data'];
$pdo = $GLOBALS['pdo'];

try {
    // 验证必需参数
    if (!isset($post_data['product_id']) || $post_data['product_id'] === '') {
        apiResponse(6, '请输入有效的product_id');
    }
    
    $product_id = $post_data['product_id'];
    
    // 获取产品信息
    $product = getProductInfo($product_id, $merchant_info['id']);
    if (!$product) {
        // 记录API调用日志
        $error_response = [
            'error_code' => 218,
            'error_message' => '产品不存在'
        ];
        
        logApiCall($merchant_info['id'], 'pay_get_payment_quota', $post_data, $error_response, 'error');
        
        apiResponse(218, '产品不存在');
    }
    
    // 计算产品可用额度
    // 这里需要根据关联的支付宝账户来计算总的可用额度
    $stmt = $pdo->prepare("
        SELECT 
            SUM(daily_limit - daily_used) as total_remain
        FROM alipay_accounts 
        WHERE status = 'active' 
        AND daily_limit > daily_used
        AND (product_ids LIKE ? OR product_ids = '' OR product_ids IS NULL)
    ");
    
    $product_pattern = '%,' . $product_id . ',%';
    $stmt->execute([$product_pattern]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $total_remain = isset($result['total_remain']) ? $result['total_remain'] : 0;
    
    // 确保额度不为负数
    if ($total_remain < 0) {
        $total_remain = 0;
    }
    
    // 格式化额度（保留2位小数）
    $total_remain = number_format($total_remain, 2, '.', '');
    
    // 构建响应数据（与接口文档格式完全一致）
    $response_data = [
        'error_code' => 0,
        'error_message' => '',
        'product_id' => (string)$product_id,
        'total_remain' => $total_remain
    ];
    
    // 记录API调用日志
    logApiCall($merchant_info['id'], 'pay_get_payment_quota', $post_data, $response_data, 'success');
    
    // 返回成功响应
    apiResponse(0, '', [
        'product_id' => $response_data['product_id'],
        'total_remain' => $response_data['total_remain']
    ]);
    
} catch (Exception $e) {
    error_log("pay_get_payment_quota异常: " . $e->getMessage());
    
    // 记录错误日志
    $error_response = [
        'error_code' => 1,
        'error_message' => '系统异常，请稍后重试'
    ];
    
    logApiCall($merchant_info['id'], 'pay_get_payment_quota', $post_data, $error_response, 'error');
    
    apiResponse(1, '系统异常，请稍后重试');
}
?> 