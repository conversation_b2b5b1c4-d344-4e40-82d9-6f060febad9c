<?php
/**
 * 获取收款页面接口
 * 兼容旧系统API，使用新数据库结构
 */

// 引入通用函数
require_once dirname(__FILE__) . '/common.php';

// 获取全局变量
$merchant_info = $GLOBALS['merchant_info'];
$post_data = $GLOBALS['post_data'];
$pdo = $GLOBALS['pdo'];

try {
    // 验证必需参数
    $required_params = ['amount', 'type', 'product_id', 'order_no'];
    foreach ($required_params as $param) {
        if (!isset($post_data[$param]) || $post_data[$param] === '') {
            switch ($param) {
                case 'amount':
                    apiResponse(2, '收款金额输入错误');
                    break;
                case 'product_id':
                    apiResponse(6, '请输入有效的product_id');
                    break;
                case 'order_no':
                    apiResponse(7, '请输入有效的order_no');
                    break;
                default:
                    apiResponse(1, '缺少必需参数: ' . $param);
            }
        }
    }
    
    $amount = $post_data['amount'];
    $type = $post_data['type'];
    $product_id = $post_data['product_id'];
    $order_no = $post_data['order_no'];
    $notification_url = isset($post_data['notification_url']) ? $post_data['notification_url'] : '';
    
    // 验证type参数（必须为2）
    if ($type != '2') {
        apiResponse(1, 'type参数错误');
    }
    
    // 验证金额格式
    if (!validateAmount($amount)) {
        apiResponse(2, '收款金额输入错误');
    }
    
    // 验证订单号格式
    if (!validateOrderNo($order_no)) {
        apiResponse(7, '请输入有效的order_no');
    }
    
    // 检查订单号是否已存在
    if (checkOrderExists($order_no, $merchant_info['id'])) {
        apiResponse(8, '该order_no已经被使用,请换一个');
    }
    
    // 获取产品信息
    $product = getProductInfo($product_id, $merchant_info['id']);
    if (!$product) {
        apiResponse(6, '请输入有效的product_id');
    }
    
    // 检查金额是否在产品允许范围内
    if ($amount < $product['min_amount'] || $amount > $product['max_amount']) {
        apiResponse(2, '收款金额输入错误');
    }
    
    // 获取可用的支付宝账户
    $alipay_account = getAvailableAlipayAccount($amount, $product_id);
    if (!$alipay_account) {
        apiResponse(22, '该金额不存在可用的账户');
    }
    
    // 引入用户应付金额管理器
    require_once dirname(dirname(__FILE__)) . '/core/PayableAmountManager.php';
    require_once dirname(dirname(__FILE__)) . '/core/PaymentInstructionManager.php';
    
    // 生成用户应付金额
    $payableAmountManager = new PayableAmountManager();
    $payableResult = $payableAmountManager->generatePayableAmount($amount, $alipay_account['id'], $merchant_info['id']);
    
    $payableAmount = $payableResult['payable_amount'];
    $amountDiff = $payableResult['amount_diff'];
    $conflictDetected = $payableResult['conflict_detected'];
    
    // 检查环境安全性（简单的反爬虫检测）
    $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
    if (empty($user_agent) || strpos($user_agent, 'curl') !== false || strpos($user_agent, 'wget') !== false) {
        // 这里可以根据需要调整检测逻辑
        // apiResponse(23, '不安全发起,无法使用');
    }
    
    // 创建支付订单
    $order_data = [
        'merchant_id' => $merchant_info['id'],
        'product_id' => $product_id,
        'order_no' => $order_no,
        'amount' => $amount,
        'payable_amount' => $payableAmount,
        'actual_amount' => $amount, // 商户实收金额
        'amount_diff' => $amountDiff,
        'alipay_account_id' => $alipay_account['id'],
        'notification_url' => $notification_url,
        'fee_rate' => $merchant_info['service_rate'],
        'product_name' => $product['name'],
        'client_ip' => getClientIP(),
        'user_agent' => $user_agent,
        'conflict_detected' => $conflictDetected
    ];
    
    $transaction_id = createPaymentOrder($order_data);
    if (!$transaction_id) {
        apiResponse(1, '创建订单失败');
    }
    
    // 创建收款指令
    $instructionManager = new PaymentInstructionManager();
    $instructionResult = $instructionManager->createInstruction(
        $transaction_id,
        $alipay_account['id'],
        $merchant_info['id'],
        $amount,
        $payableAmount,
        3 // 3分钟过期
    );
    
    if (!$instructionResult['success']) {
        error_log("Failed to create payment instruction: " . $instructionResult['error']);
        // 不影响主流程，继续执行
    }
    
    // 生成支付页面URL（使用用户应付金额）
    $payment_url = generatePaymentUrl($transaction_id, $alipay_account['id'], $payableAmount);
    
    // 更新支付宝账户使用情况
    try {
        $stmt = $pdo->prepare("
            UPDATE alipay_accounts 
            SET daily_used = daily_used + ?, last_used_time = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$amount, $alipay_account['id']]);
    } catch (Exception $e) {
        error_log("更新支付宝账户使用情况失败: " . $e->getMessage());
    }
    
    // 记录API调用日志
    $response_data = [
        'error_code' => 0,
        'error_message' => '',
        'url' => $payment_url,
        'amount' => $amount,
        'payable_amount' => $payableAmount,
        'amount_diff' => $amountDiff,
        'conflict_detected' => $conflictDetected,
        'instruction_id' => isset($instructionResult['instruction_id']) ? $instructionResult['instruction_id'] : null
    ];
    
    logApiCall($merchant_info['id'], 'pay_get_qrcode', $post_data, $response_data, 'success');
    
    // 返回成功响应
    apiResponse(0, '', ['url' => $payment_url]);
    
} catch (Exception $e) {
    error_log("pay_get_qrcode异常: " . $e->getMessage());
    
    // 记录错误日志
    $error_response = [
        'error_code' => 1,
        'error_message' => '系统异常，请稍后重试'
    ];
    
    logApiCall($merchant_info['id'], 'pay_get_qrcode', $post_data, $error_response, 'error');
    
    apiResponse(1, '系统异常，请稍后重试');
}
?> 