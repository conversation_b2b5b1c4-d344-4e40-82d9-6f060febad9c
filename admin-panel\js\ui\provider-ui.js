/**
 * 码商UI配置
 * 专门为provider租户类型定制的界面配置
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-12-28
 */

class ProviderUI {
    constructor() {
        this.config = {
            theme: {
                primaryColor: '#fa8c16',
                sidebarStyle: 'light',
                brandName: '码商管理后台'
            },
            layout: {
                sidebarCollapsed: false,
                headerFixed: true,
                footerVisible: true
            },
            features: {
                darkMode: false,
                notifications: true,
                quickActions: true,
                searchGlobal: false,
                qrCodeTools: true
            }
        };
    }

    init() {
        console.log('🎨 初始化码商UI配置...');
        this.applyTheme();
        this.setupLayout();
        this.initializeComponents();
        console.log('✅ 码商UI初始化完成');
    }

    applyTheme() {
        const theme = this.config.theme;
        document.documentElement.style.setProperty('--primary-color', theme.primaryColor);
        document.documentElement.style.setProperty('--sidebar-style', theme.sidebarStyle);
        if (theme.brandName) {
            document.title = theme.brandName;
        }
    }

    setupLayout() {
        const layout = this.config.layout;
        if (layout.sidebarCollapsed) {
            document.body.classList.add('sidebar-collapsed');
        }
        if (layout.headerFixed) {
            document.body.classList.add('header-fixed');
        }
        if (!layout.footerVisible) {
            document.body.classList.add('footer-hidden');
        }
    }

    initializeComponents() {
        if (this.config.features.notifications) {
            this.initNotifications();
        }
        if (this.config.features.quickActions) {
            this.initQuickActions();
        }
        if (this.config.features.qrCodeTools) {
            this.initQrCodeTools();
        }
    }

    initNotifications() {
        console.log('📢 码商通知已初始化');
    }

    initQuickActions() {
        console.log('⚡ 码商快速操作已初始化');
    }

    initQrCodeTools() {
        console.log('📱 二维码工具已初始化');
    }

    getConfig() {
        return this.config;
    }

    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.applyTheme();
        this.setupLayout();
    }
}

const providerUI = new ProviderUI();
window.ProviderUI = ProviderUI;
window.providerUI = providerUI;

if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProviderUI;
}

console.log('✅ 码商UI配置已加载'); 