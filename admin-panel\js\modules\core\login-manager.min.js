class LoginManager{constructor(){this.auth=new AuthManager,this.initializeLoginForm()}initializeLoginForm(){var a=document.getElementById("loginForm");a&&a.addEventListener("submit",a=>this.handleLogin(a))}async handleLogin(a){a.preventDefault();var a=document.getElementById("username").value,i=document.getElementById("password").value;if(a&&i){this.showLoading(!0);try{var e=await this.auth.login(a,i);e.success?(this.showAlert("登录成功！正在跳转...","success"),setTimeout(()=>{this.initializeMainApp(),this.showLoading(!1)},1e3)):(this.showAlert(e.message,"danger"),this.showLoading(!1))}catch(a){console.error("Login error:",a),this.showAlert("登录失败，请重试","danger"),this.showLoading(!1)}}else this.showAlert("请输入用户名和密码","danger")}showAlert(a,i="info"){let e=document.getElementById("alertContainer");e&&(e.innerHTML=`
        <div class="alert alert-${i} alert-dismissible fade show" role="alert">
        ${a}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        `,setTimeout(()=>{e.innerHTML=""},5e3))}showLoading(a){var i=document.querySelector(".loading"),e=document.querySelector(".btn-login");i&&(i.style.display=a?"flex":"none"),e&&(e.disabled=a)}initializeMainApp(){var a;console.log("🏠 正在初始化主应用界面..."),window.appInitializer?(console.log("✅ 使用统一的AppInitializer启动主应用..."),window.appInitializer.authManager&&window.appInitializer.authManager.refreshFromStorage&&(console.log("🔄 刷新AppInitializer的AuthManager状态..."),a=window.appInitializer.authManager.refreshFromStorage(),console.log("✅ AuthManager状态刷新完成，认证状态:",a)),window.appInitializer._checkAuthAndStart()):console.error("❌ AppInitializer不存在，无法启动主应用")}generateMenuItems(a){var i=this.auth.getUser(),i=i&&1==i.is_staff,e={admin:[{id:"dashboard",icon:"bi-speedometer2",text:"仪表板"},{id:"users",icon:"bi-people",text:"用户管理"},{id:"employees",icon:"bi-person-badge",text:"员工管理"},{id:"job-positions",icon:"bi-diagram-3",text:"职位管理"},{id:"providers",icon:"bi-building",text:"码商管理"},{id:"merchants",icon:"bi-shop",text:"商户管理"},{id:"products",icon:"bi-box-seam",text:"产品管理"},{id:"financial",icon:"bi-graph-up",text:"财务管理"},{id:"risk-control",icon:"bi-shield-check",text:"风控管理"},{id:"notifications",icon:"bi-bell",text:"通知管理"},{id:"blacklist",icon:"bi-shield-exclamation",text:"黑名单管理"},{id:"daily-reports",icon:"bi-calendar2-week",text:"日报管理"},{id:"totp",icon:"bi-shield-lock",text:"TOTP管理"},{id:"security-logs",icon:"bi-shield-exclamation",text:"安全日志"},{id:"performance-monitor",icon:"bi-speedometer2",text:"性能监控"},{id:"payment-requests",icon:"bi-credit-card-2-front",text:"支付请求"},{id:"devices",icon:"bi-phone",text:"设备管理"},{id:"alipay",icon:"bi-credit-card",text:"支付宝账户"},{id:"transactions",icon:"bi-receipt",text:"交易记录"},{id:"scripts",icon:"bi-code-square",text:"脚本管理"}],provider:[{id:"dashboard",icon:"bi-speedometer2",text:"仪表板"},{id:"employees",icon:"bi-person-badge",text:"员工管理"},{id:"job-positions",icon:"bi-diagram-3",text:"职位管理"},{id:"devices",icon:"bi-phone",text:"我的设备"},{id:"totp",icon:"bi-shield-lock",text:"TOTP管理"},{id:"alipay",icon:"bi-credit-card",text:"支付宝账户"},{id:"transactions",icon:"bi-receipt",text:"交易记录"}],merchant:[{id:"merchant-dashboard",icon:"bi-speedometer2",text:"仪表板"},{id:"merchant-orders",icon:"bi-receipt",text:"我的订单"},{id:"merchant-stats",icon:"bi-graph-up",text:"数据统计"},{id:"merchant-products",icon:"bi-box-seam",text:"产品管理"},{type:"header",text:"开发者工具"},{id:"api-keys",icon:"bi-key",text:"API密钥"},{id:"code-generator",icon:"bi-code-square",text:"代码生成器"},{id:"signature-tool",icon:"bi-shield-check",text:"签名工具"},{id:"api-docs",icon:"bi-book",text:"API文档"},{type:"header",text:"系统设置"},{id:"merchant-config",icon:"bi-gear",text:"系统配置"},{id:"employees",icon:"bi-person-badge",text:"员工管理"},{id:"job-positions",icon:"bi-diagram-3",text:"职位管理"}]};let s=e[a]||e.provider;return(s=i?s.filter(a=>"employees"!==a.id&&"job-positions"!==a.id):s).map(a=>"header"===a.type?`<div class="menu-header">${a.text}</div>`:`
            <a href="#" class="menu-item" data-page="${a.id}">
            <i class="${a.icon} me-3"></i>${a.text}
            </a>
            `).join("")}getUserTypeText(a){return{admin:"系统管理员",provider:"支付码商",merchant:"商户用户"}[a]||"未知用户"}initializeMenuEvents(){let s=document.querySelectorAll(".menu-item[data-page]");s.forEach(e=>{e.addEventListener("click",a=>{a.preventDefault();var a=e.getAttribute("data-page"),i=(s.forEach(a=>a.classList.remove("active")),e.classList.add("active"),document.getElementById("pageTitle"));i&&(i.textContent=e.textContent.trim()),this.loadPageContent(a),console.log("切换到页面: "+a)})})}loadPageContent(a){var i=document.querySelector(".main-content");if(i){var e=i.querySelector(".top-bar"),s=(i.querySelectorAll(".content-area, .welcome-card, .stats-grid, .device-management, .script-management, .management-page").forEach(a=>a.remove()),document.createElement("div"));switch(s.className="content-area",e&&e.nextSibling?i.insertBefore(s,e.nextSibling):i.appendChild(s),a){case"dashboard":this.loadDashboard(s);break;case"scripts":this.loadScriptManagement(s);break;case"devices":this.loadDeviceManagement(s);break;case"users":this.loadUserManagement(s);break;case"employees":this.loadEmployeeManagement(s);break;case"job-positions":this.loadJobPositionManagement(s);break;case"providers":this.loadProviderManagement(s);break;case"merchants":this.loadMerchantManagement(s);break;case"products":this.loadProductManagement(s);break;case"financial":this.loadFinancialManagement(s);break;case"risk-control":this.loadRiskControlManagement(s);break;case"notifications":loadNotificationManagement(s);break;case"blacklist":menuLoadBlacklistManagement(s);break;case"daily-reports":loadDailyReportManagement(s);break;case"totp":loadTOTPManagement(s);break;case"security-logs":loadSecurityLogManagement(s);break;case"payment-requests":this.loadPaymentRequestManagement(s);break;case"transactions":this.loadTransactionManagement(s);break;case"alipay":this.loadAlipayManagement(s);break;case"api":this.loadApiManagement(s);break;case"api-docs":this.loadApiDocumentation(s);break;case"merchant-dashboard":this.loadMerchantDashboard(s);break;case"merchant-orders":this.loadMerchantOrders(s);break;case"merchant-stats":this.loadMerchantStats(s);break;case"merchant-products":this.loadMerchantProducts(s);break;case"code-generator":this.loadCodeGenerator(s);break;case"signature-tool":this.loadSignatureTool(s);break;case"api-keys":this.loadMerchantApiKeyManagement(s);break;case"merchant-config":this.loadMerchantConfig(s);break;case"merchant-profile":this.loadMerchantProfile(s);break;default:this.loadDashboard(s)}}}loadScriptManagement(a){a.innerHTML="",a.innerHTML=`
        <div class="script-management">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-file-earmark-code me-2"></i>脚本管理</h4>
        <p class="text-muted mb-0">基于设备品牌的智能脚本管理</p>
        </div>
        <button class="btn btn-primary" onclick="window.scriptManager.showUploadModal()">
        <i class="bi bi-upload me-2"></i>上传脚本
        </button>
        </div>
        <!-- 说明卡片 -->
        <div class="alert alert-info mb-4">
        <div class="d-flex">
        <i class="bi bi-info-circle me-3 mt-1"></i>
        <div>
        <h6 class="alert-heading mb-2">脚本管理说明</h6>
        <ul class="mb-0 small">
        <li>系统根据已注册设备的品牌自动显示对应的脚本管理卡片</li>
        <li>每个品牌支持支付宝和微信两种应用类型，每种应用支持正式版和调试版</li>
        <li>绿色按钮表示已有脚本，灰色按钮表示需要上传脚本</li>
        <li>点击按钮可以查看现有脚本或上传新脚本</li>
        </ul>
        </div>
        </div>
        </div>
        <!-- 脚本矩阵容器 -->
        <div id="scriptMatrixContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载脚本管理矩阵...</div>
        </div>
        </div>
        </div>
        <!-- 上传脚本模态框 -->
        <div class="modal fade" id="uploadScriptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">上传脚本</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="uploadScriptForm">
        <div class="row g-3">
        <div class="col-md-6">
        <label for="appType" class="form-label">应用类型 *</label>
        <select class="form-select" id="appType" required>
        <option value="">请选择应用类型</option>
        <option value="alipay">支付宝</option>
        <option value="wechat">微信</option>
        </select>
        </div>
        <div class="col-md-6">
        <label for="appVersion" class="form-label">应用版本 *</label>
        <input type="text" class="form-control" id="appVersion" placeholder="如: 10.5.20" required>
        </div>
        <div class="col-md-6">
        <label for="deviceBrand" class="form-label">设备品牌 *</label>
        <select class="form-select" id="deviceBrand" required>
        <option value="">请选择设备品牌</option>
        <option value="xiaomi">小米</option>
        <option value="huawei">华为</option>
        <option value="oppo">OPPO</option>
        <option value="vivo">VIVO</option>
        <option value="generic">通用</option>
        <option value="default">默认</option>
        </select>
        </div>
        <div class="col-md-6">
        <label for="environment" class="form-label">环境 *</label>
        <select class="form-select" id="environment" required>
        <option value="release">正式环境</option>
        <option value="test">测试环境</option>
        </select>
        </div>
        <div class="col-12">
        <label for="description" class="form-label">描述</label>
        <textarea class="form-control" id="description" rows="2" placeholder="脚本功能描述（可选）"></textarea>
        </div>
        <div class="col-12">
        <label for="scriptContent" class="form-label">脚本内容 *</label>
        <textarea class="form-control" id="scriptContent" rows="15" placeholder="请输入JavaScript脚本内容..." required></textarea>
        </div>
        </div>
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.scriptManager.uploadScript()">
        <span class="upload-btn-text">上传脚本</span>
        <span class="upload-btn-loading" style="display: none;
        ">
        <span class="spinner-border spinner-border-sm me-2"></span>上传中...
        </span>
        </button>
        </div>
        </div>
        </div>
        </div>
        <style>
        .script-management {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .filter-bar {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .table th {
            background: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .btn-action {
            padding: 4px 8px;
            font-size: 12px;
            margin: 0 2px;
        }
        .modal-lg {
            max-width: 800px;
        }
        #scriptContent {
            font-family: 'Courier New',
            monospace;
            font-size: 13px;
        }
        .management-page {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .page-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f8f9fa;
        }
        .page-header h4 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        .content-placeholder {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .placeholder-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.7;
        }
        .content-placeholder h5 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit,
            minmax(200px, 1fr));
            gap: 10px;
            margin-top: 30px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        .feature-item {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 14px;
            color: var(--success-color);
            font-weight: 500;
        }
        </style>
        `,window.scriptManager||(window.scriptManager=new ScriptManager),window.scriptManager.loadScripts()}loadDashboard(a){a.innerHTML=`
        <div class="content-area">
        <!-- 欢迎信息 -->
        <div class="welcome-card">
        <h2>欢迎回来，${this.auth.getUser()?.display_name||this.auth.getUser()?.real_name||this.auth.getUser()?.username}！</h2>
        <p>您的身份：${this.getUserTypeText(this.auth.getUser()?.user_type)}</p>
        </div>
        <!-- 统计数据 -->
        <div class="stats-grid">
        <div class="stat-card">
        <i class="bi bi-people" style="font-size: 2rem;
        color: #3b82f6;
        "></i>
        <div class="stat-number">156</div>
        <div class="stat-label">总用户数</div>
        </div>
        <div class="stat-card">
        <i class="bi bi-phone" style="font-size: 2rem;
        color: #10b981;
        "></i>
        <div class="stat-number">42</div>
        <div class="stat-label">活跃设备</div>
        </div>
        <div class="stat-card">
        <i class="bi bi-building" style="font-size: 2rem;
        color: #f59e0b;
        "></i>
        <div class="stat-number">8</div>
        <div class="stat-label">码商数量</div>
        </div>
        <div class="stat-card">
        <i class="bi bi-graph-up" style="font-size: 2rem;
        color: #ef4444;
        "></i>
        <div class="stat-number">¥12,345</div>
        <div class="stat-label">今日交易额</div>
        </div>
        </div>
        </div>
        `}loadDeviceManagement(a){this.loadModularDeviceManagement(a)}async loadModularDeviceManagement(i){i.innerHTML="";var a=document.createElement("div");a.id="device-management-container",a.className="device-management-app",a.innerHTML=`
        <div class="container-fluid">
        <div class="row">
        <div class="col-12">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h2 class="mb-1">
        <i class="bi bi-phone-vibrate me-2"></i>设备管理系统
        </h2>
        <p class="text-muted mb-0">管理和监控所有设备的状态、审核和任务分配</p>
        </div>
        <div class="d-flex gap-2">
        <button class="btn btn-outline-secondary" onclick="location.reload()">
        <i class="bi bi-arrow-clockwise me-1"></i>刷新
        </button>
        <button class="btn btn-primary" id="exportDeviceData">
        <i class="bi bi-download me-1"></i>导出数据
        </button>
        </div>
        </div>
        <!-- 模块导航标签 -->
        <ul class="nav nav-tabs nav-fill mb-4" id="deviceManagementTabs" role="tablist">
        <li class="nav-item" role="presentation">
        <button class="nav-link active" id="device-tab" data-bs-toggle="tab"
        data-bs-target="#device-content" type="button" role="tab">
        <i class="bi bi-phone me-2"></i>设备管理
        <span class="badge bg-primary ms-2" id="device-count">0</span>
        </button>
        </li>
        <li class="nav-item" role="presentation">
        <button class="nav-link" id="group-tab" data-bs-toggle="tab"
        data-bs-target="#group-content" type="button" role="tab">
        <i class="bi bi-people me-2"></i>小组管理
        <span class="badge bg-info ms-2" id="group-count">0</span>
        </button>
        </li>
        <li class="nav-item" role="presentation">
        <button class="nav-link" id="checkin-tab" data-bs-toggle="tab"
        data-bs-target="#checkin-content" type="button" role="tab">
        <i class="bi bi-clock-history me-2"></i>签到监控
        <span class="badge bg-warning ms-2" id="overdue-count">0</span>
        </button>
        </li>
        </ul>
        <!-- 标签页内容 -->
        <div class="tab-content" id="deviceManagementTabContent">
        <!-- 设备管理内容 -->
        <div class="tab-pane fade show active" id="device-content" role="tabpanel">
        <div id="device-management-module">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">正在加载设备管理模块...</p>
        </div>
        </div>
        </div>
        <!-- 小组管理内容 -->
        <div class="tab-pane fade" id="group-content" role="tabpanel">
        <div id="group-management-module">
        <div class="text-center py-5">
        <div class="spinner-border text-info" role="status">
        <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">正在加载小组管理模块...</p>
        </div>
        </div>
        </div>
        <!-- 签到监控内容 -->
        <div class="tab-pane fade" id="checkin-content" role="tabpanel">
        <div id="checkin-monitor-module">
        <div class="text-center py-5">
        <div class="spinner-border text-warning" role="status">
        <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-muted">正在加载签到监控模块...</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        `,i.appendChild(a);try{await this.loadDeviceModuleAssets(),await this.initializeDeviceModules()}catch(a){console.error("设备管理模块加载失败:",a),this.showModuleLoadError(i)}}async loadDeviceModuleAssets(){var a,i;document.getElementById("device-modules-css")||((a=document.createElement("link")).id="device-modules-css",a.rel="stylesheet",a.href="css/device-modules.css",document.head.appendChild(a));for(i of["js/modules/device-module-manager.js","js/modules/device-management.js","js/modules/group-management.js","js/modules/checkin-monitor.js"])await this.loadScript(i)}async initializeDeviceModules(){if(await new Promise(a=>setTimeout(a,100)),!window.DeviceModuleManager)throw new Error("设备模块管理器未找到");window.deviceModuleManager=new DeviceModuleManager,await window.deviceModuleManager.init(),this.setupDeviceTabSwitching(),console.log("设备管理模块系统初始化完成")}loadScript(s){return new Promise((a,i)=>{var e;document.querySelector(`script[src="${s}"]`)?a():((e=document.createElement("script")).src=s,e.type="module",e.onload=a,e.onerror=i,document.head.appendChild(e))})}setupDeviceTabSwitching(){document.querySelectorAll('#deviceManagementTabs button[data-bs-toggle="tab"]').forEach(a=>{a.addEventListener("shown.bs.tab",a=>{a=a.target.getAttribute("data-bs-target").replace("#","").replace("-content","");window.deviceModuleManager&&window.deviceModuleManager.switchModule(a)})})}showModuleLoadError(a){var i=document.getElementById("device-management-container");i&&(i.innerHTML=`
            <div class="container-fluid">
            <div class="row justify-content-center">
            <div class="col-md-8">
            <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">
            <i class="bi bi-exclamation-triangle me-2"></i>模块加载失败
            </h4>
            <p>设备管理模块无法正常加载，可能的原因：</p>
            <ul class="mb-3">
            <li>模块文件不存在或路径错误</li>
            <li>网络连接问题</li>
            <li>JavaScript执行错误</li>
            </ul>
            <hr>
            <div class="d-flex gap-2">
            <button class="btn btn-outline-danger" onclick="location.reload()">
            <i class="bi bi-arrow-clockwise me-1"></i>重新加载
            </button>
            <button class="btn btn-danger" onclick="console.log('设备管理模块加载错误',
            arguments)">
            <i class="bi bi-bug me-1"></i>查看控制台
            </button>
            </div>
            </div>
            <!-- 降级方案：显示基础信息 -->
            <div class="card">
            <div class="card-header">
            <h5 class="mb-0">
            <i class="bi bi-phone me-2"></i>设备管理（降级模式）
            </h5>
            </div>
            <div class="card-body">
            <p class="text-muted">模块化系统暂不可用，请联系技术支持。</p>
            <button class="btn btn-primary" onclick="location.reload()">
            <i class="bi bi-arrow-clockwise me-1"></i>重试加载
            </button>
            </div>
            </div>
            </div>
            </div>
            </div>
            `)}loadUserManagement(a){var i=this.auth.getUser();i&&["admin"].includes(i.user_type)?(document.getElementById("pageTitle").textContent="用户管理",a.innerHTML=`
        <div class="user-management">
        <!-- 页面头部 -->
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h2 class="mb-1">用户管理</h2>
        <p class="text-muted mb-0">管理系统用户账户、权限和状态</p>
        </div>
        <div>
        <button class="btn btn-primary" onclick="window.userManager.showCreateModal()">
        <i class="bi bi-person-plus me-2"></i>添加用户
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.userManager.refreshUsers()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 统计卡片 -->
        <div class="stats-section" id="userStats">
        <div class="row">
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number" id="totalUsers">-</div>
        <div class="stat-label">总用户数</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-primary" id="adminUsers">-</div>
        <div class="stat-label">管理员</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-info" id="providerUsers">-</div>
        <div class="stat-label">码商</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-warning" id="merchantUsers">-</div>
        <div class="stat-label">商户</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-success" id="activeUsers">-</div>
        <div class="stat-label">活跃用户</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-secondary" id="dailyActiveUsers">-</div>
        <div class="stat-label">日活跃</div>
        </div>
        </div>
        </div>
        </div>
        <!-- 筛选控制栏 -->
        <div class="controls-section">
        <div class="card">
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-2">
        <label class="form-label">用户类型</label>
        <select class="form-select" id="userTypeFilter">
        <option value="">全部类型</option>
        <option value="admin">管理员</option>
        <option value="provider">码商</option>
        <option value="merchant">商户</option>
        </select>
        </div>
        <div class="col-md-2">
        <label class="form-label">状态筛选</label>
        <select class="form-select" id="userStatusFilter">
        <option value="">全部状态</option>
        <option value="active">活跃</option>
        <option value="inactive">非活跃</option>
        <option value="suspended">已暂停</option>
        </select>
        </div>
        <div class="col-md-4">
        <label class="form-label">搜索</label>
        <div class="input-group">
        <input type="text" class="form-control" id="userSearchInput" placeholder="搜索用户名、姓名、邮箱...">
        <button class="btn btn-outline-secondary" onclick="window.userManager.searchUsers()">
        <i class="bi bi-search"></i>
        </button>
        </div>
        </div>
        <div class="col-md-2">
        <label class="form-label">&nbsp;
        </label>
        <div class="d-grid">
        <button class="btn btn-primary" onclick="window.userManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>筛选
        </button>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 用户列表 -->
        <div class="users-section">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-people me-2"></i>用户列表
        <span class="badge bg-secondary ms-2" id="userCount">0</span>
        </h5>
        </div>
        <div class="card-body">
        <div id="usersTableContainer">
        <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载用户数据...</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 分页 -->
        <div class="pagination-section" id="userPagination">
        <!-- 分页控件将在这里动态生成 -->
        </div>
        </div>
        <!-- 用户详情/编辑模态框 -->
        <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title" id="userModalTitle">用户详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="userForm">
        <input type="hidden" id="userId">
        <!-- 基本信息 -->
        <h6>基本信息</h6>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">用户名 *</label>
        <input type="text" class="form-control" id="userUsername" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">真实姓名 *</label>
        <input type="text" class="form-control" id="userRealName" required>
        </div>
        </div>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">邮箱 *</label>
        <input type="email" class="form-control" id="userEmail" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">手机号</label>
        <input type="text" class="form-control" id="userPhone">
        </div>
        </div>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">用户类型 *</label>
        <select class="form-select" id="userType" required>
        <option value="">请选择</option>
        <option value="admin">管理员</option>
        <option value="provider">码商</option>
        <option value="merchant">商户</option>
        </select>
        </div>
        <div class="col-md-6">
        <label class="form-label">状态</label>
        <select class="form-select" id="userStatus">
        <option value="active">活跃</option>
        <option value="inactive">非活跃</option>
        <option value="suspended">已暂停</option>
        </select>
        </div>
        </div>
        <div class="row mb-3" id="userPasswordRow">
        <div class="col-md-6">
        <label class="form-label">密码 *</label>
        <input type="password" class="form-control" id="userPassword" required>
        </div>
        </div>
        <!-- 扩展信息 - 根据用户类型显示 -->
        <div id="extendedInfo" style="display: none;
        ">
        <hr>
        <h6 id="extendedInfoTitle">扩展信息</h6>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">公司名称</label>
        <input type="text" class="form-control" id="userCompanyName">
        </div>
        <div class="col-md-6">
        <label class="form-label">业务类型</label>
        <select class="form-select" id="userBusinessType">
        <option value="">请选择</option>
        <option value="个人">个人</option>
        <option value="企业">企业</option>
        <option value="个体工商户">个体工商户</option>
        </select>
        </div>
        </div>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">营业执照号</label>
        <input type="text" class="form-control" id="userBusinessLicense">
        </div>
        <div class="col-md-6" id="websiteField" style="display: none;
        ">
        <label class="form-label">网站</label>
        <input type="url" class="form-control" id="userWebsite">
        </div>
        <div class="col-md-6" id="settlementRateField" style="display: none;
        ">
        <label class="form-label">结算费率 (%)</label>
        <input type="number" class="form-control" id="userSettlementRate" step="0.01" min="0" max="100" value="95">
        </div>
        </div>
        </div>
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.userManager.saveUser()">
        <span id="userSaveText">保存</span>
        <span class="spinner-border spinner-border-sm ms-2 d-none" id="userSaveSpinner"></span>
        </button>
        </div>
        </div>
        </div>
        </div>
        `,window.userManager||(window.userManager=new UserManager),window.userManager.initialize()):a.innerHTML=`
            <div class="alert alert-danger">
            <h4>未授权访问</h4>
            <p>您没有权限访问用户管理功能。</p>
            </div>
            `}loadEmployeeManagement(a){var i=this.auth.getUser();i&&1!=i.is_staff?(document.getElementById("pageTitle").textContent="员工管理",a.innerHTML=`
        <div class="employee-management">
        <!-- 页面头部 -->
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h2 class="mb-1">员工管理</h2>
        <p class="text-muted mb-0">管理我的员工账户、职位和权限</p>
        </div>
        <div>
        <button class="btn btn-primary" onclick="window.employeeManager.showCreateModal()">
        <i class="bi bi-person-plus me-2"></i>添加员工
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.employeeManager.refreshEmployees()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 统计卡片 -->
        <div class="stats-section" id="employeeStats">
        <div class="row">
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number" id="totalEmployees">-</div>
        <div class="stat-label">总员工数</div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number text-success" id="activeEmployees">-</div>
        <div class="stat-label">活跃员工</div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number text-warning" id="inactiveEmployees">-</div>
        <div class="stat-label">非活跃员工</div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number text-danger" id="suspendedEmployees">-</div>
        <div class="stat-label">已暂停</div>
        </div>
        </div>
        </div>
        </div>
        <!-- 筛选控制栏 -->
        <div class="controls-section">
        <div class="card">
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-2">
        <label class="form-label">职位筛选</label>
        <select class="form-select" id="employeeJobFilter">
        <option value="">全部职位</option>
        </select>
        </div>
        <div class="col-md-2">
        <label class="form-label">状态筛选</label>
        <select class="form-select" id="employeeStatusFilter">
        <option value="">全部状态</option>
        <option value="active">活跃</option>
        <option value="inactive">非活跃</option>
        <option value="suspended">已暂停</option>
        </select>
        </div>
        <div class="col-md-4">
        <label class="form-label">搜索</label>
        <div class="input-group">
        <input type="text" class="form-control" id="employeeSearchInput" placeholder="搜索员工姓名、用户名、邮箱...">
        <button class="btn btn-outline-secondary" onclick="window.employeeManager.searchEmployees()">
        <i class="bi bi-search"></i>
        </button>
        </div>
        </div>
        <div class="col-md-2">
        <label class="form-label">&nbsp;
        </label>
        <div class="d-grid">
        <button class="btn btn-primary" onclick="window.employeeManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>筛选
        </button>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 员工列表 -->
        <div class="employees-section">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-person-badge me-2"></i>员工列表
        <span class="badge bg-secondary ms-2" id="employeeCount">0</span>
        </h5>
        </div>
        <div class="card-body">
        <div id="employeesTableContainer">
        <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载员工数据...</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 分页 -->
        <div class="pagination-section" id="employeePagination">
        <!-- 分页控件将在这里动态生成 -->
        </div>
        </div>
        <!-- 员工详情/编辑模态框 -->
        <div class="modal fade" id="employeeModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title" id="employeeModalTitle">员工详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="employeeForm">
        <input type="hidden" id="employeeId">
        <!-- 基本信息 -->
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">用户名 *</label>
        <input type="text" class="form-control" id="employeeUsername" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">真实姓名 *</label>
        <input type="text" class="form-control" id="employeeFullName" required>
        </div>
        </div>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">邮箱 *</label>
        <input type="email" class="form-control" id="employeeEmail" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">职位 *</label>
        <select class="form-select" id="employeeJobPosition" required>
        <option value="">请选择职位</option>
        </select>
        </div>
        </div>
        <div class="row mb-3" id="employeePasswordRow">
        <div class="col-md-6">
        <label class="form-label">密码 *</label>
        <input type="password" class="form-control" id="employeePassword" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">状态</label>
        <select class="form-select" id="employeeStatus">
        <option value="active">活跃</option>
        <option value="inactive">非活跃</option>
        <option value="suspended">已暂停</option>
        </select>
        </div>
        </div>
        <!-- 权限设置 -->
        <hr>
        <h6 class="mb-3">权限设置</h6>
        <div id="employeePermissions">
        <div class="text-center py-3">
        <div class="spinner-border spinner-border-sm" role="status">
        <span class="visually-hidden">加载权限...</span>
        </div>
        </div>
        </div>
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.employeeManager.saveEmployee()">
        <span id="employeeSaveText">保存</span>
        <span class="spinner-border spinner-border-sm ms-2 d-none" id="employeeSaveSpinner"></span>
        </button>
        </div>
        </div>
        </div>
        </div>
        <!-- 权限管理模态框 -->
        <div class="modal fade" id="permissionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">权限管理</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <input type="hidden" id="permissionEmployeeId">
        <div id="permissionsList">
        <div class="text-center py-3">
        <div class="spinner-border" role="status">
        <span class="visually-hidden">加载权限...</span>
        </div>
        </div>
        </div>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.employeeManager.savePermissions()">
        <span id="permissionSaveText">保存权限</span>
        <span class="spinner-border spinner-border-sm ms-2 d-none" id="permissionSaveSpinner"></span>
        </button>
        </div>
        </div>
        </div>
        </div>
        `,window.employeeManager||(window.employeeManager=new EmployeeManager),window.employeeManager.initialize()):a.innerHTML=`
            <div class="alert alert-danger">
            <h4>未授权访问</h4>
            <p>员工账户无权限管理其他员工。</p>
            </div>
            `}loadJobPositionManagement(a){document.getElementById("pageTitle").textContent="职位管理",a.innerHTML=`
        <div class="management-page">
        <div class="page-header">
        <h2 class="mb-1">职位管理</h2>
        <p class="text-muted mb-0">管理和配置员工职位类型</p>
        </div>
        <!-- 统计卡片 -->
        <div class="row g-3 mb-4" id="positionStats">
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="avatar avatar-md bg-primary bg-opacity-10 text-primary rounded-circle">
        <i class="bi bi-diagram-3"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="fw-semibold text-dark">总职位数</div>
        <div class="h5 mb-0" id="totalPositions">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="avatar avatar-md bg-success bg-opacity-10 text-success rounded-circle">
        <i class="bi bi-check-circle"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="fw-semibold text-dark">启用职位</div>
        <div class="h5 mb-0" id="activePositions">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="avatar avatar-md bg-info bg-opacity-10 text-info rounded-circle">
        <i class="bi bi-person-plus"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="fw-semibold text-dark">自定义职位</div>
        <div class="h5 mb-0" id="customPositions">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="avatar avatar-md bg-warning bg-opacity-10 text-warning rounded-circle">
        <i class="bi bi-people"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="fw-semibold text-dark">在职员工</div>
        <div class="h5 mb-0" id="employeesInPositions">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 控制面板 -->
        <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
        <div class="row g-3 align-items-center">
        <div class="col-md-4">
        <div class="input-group">
        <span class="input-group-text">
        <i class="bi bi-search"></i>
        </span>
        <input type="text" class="form-control" id="positionSearch" placeholder="搜索职位名称或描述">
        </div>
        </div>
        <div class="col-md-2">
        <select class="form-select" id="positionStatusFilter">
        <option value="">所有状态</option>
        <option value="active">启用</option>
        <option value="inactive">禁用</option>
        </select>
        </div>
        <div class="col-md-2">
        <select class="form-select" id="positionTypeFilter">
        <option value="">所有类型</option>
        <option value="system">系统预设</option>
        <option value="custom">自定义</option>
        </select>
        </div>
        <div class="col-md-4 text-end">
        <button type="button" class="btn btn-outline-secondary me-2" id="refreshPositions">
        <i class="bi bi-arrow-clockwise me-1"></i>刷新
        </button>
        <button type="button" class="btn btn-primary" id="createPositionBtn">
        <i class="bi bi-plus-lg me-1"></i>添加职位
        </button>
        </div>
        </div>
        </div>
        </div>
        <!-- 职位列表 -->
        <div class="card border-0 shadow-sm">
        <div class="card-header bg-white">
        <div class="d-flex justify-content-between align-items-center">
        <h6 class="mb-0">职位列表</h6>
        <small class="text-muted">共 <span id="positionCount">0</span> 个职位</small>
        </div>
        </div>
        <div class="card-body p-0">
        <div class="table-responsive">
        <table class="table table-hover mb-0">
        <thead class="table-light">
        <tr>
        <th>职位信息</th>
        <th>类型</th>
        <th>状态</th>
        <th>权限数量</th>
        <th>员工数量</th>
        <th>创建时间</th>
        <th width="180">操作</th>
        </tr>
        </thead>
        <tbody id="positionTableBody">
        <tr>
        <td colspan="6" class="text-center py-4">
        <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-2 text-muted">正在加载职位数据...</div>
        </td>
        </tr>
        </tbody>
        </table>
        </div>
        </div>
        <div class="card-footer bg-white">
        <nav id="positionPagination"></nav>
        </div>
        </div>
        </div>
        `,window.jobPositionManager||(window.jobPositionManager=new JobPositionManager),window.jobPositionManager.initialize()}loadProviderManagement(a){var i=this.auth.getUser();i&&["admin"].includes(i.user_type)?(document.getElementById("pageTitle").textContent="码商管理",a.innerHTML=`
        <div class="provider-management">
        <!-- 页面头部 -->
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h2 class="mb-1">码商管理</h2>
        <p class="text-muted mb-0">管理支付码商信息、结算费率和业务数据</p>
        </div>
        <div>
        <button class="btn btn-primary" onclick="window.providerManager.showCreateModal()">
        <i class="bi bi-plus-circle me-2"></i>添加码商
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.providerManager.refreshProviders()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 统计卡片 -->
        <div class="stats-section" id="providerStats">
        <div class="row">
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number" id="totalProviders">-</div>
        <div class="stat-label">总码商数</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-warning" id="pendingProviders">-</div>
        <div class="stat-label">待审核</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-success" id="approvedProviders">-</div>
        <div class="stat-label">已通过</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-danger" id="suspendedProviders">-</div>
        <div class="stat-label">已暂停</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-secondary" id="rejectedProviders">-</div>
        <div class="stat-label">已拒绝</div>
        </div>
        </div>
        </div>
        </div>
        <!-- 筛选控制栏 -->
        <div class="controls-section">
        <div class="card">
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-2">
        <label class="form-label">状态筛选</label>
        <select class="form-select" id="providerStatusFilter">
        <option value="">全部状态</option>
        <option value="pending">待审核</option>
        <option value="approved">已通过</option>
        <option value="suspended">已暂停</option>
        <option value="rejected">已拒绝</option>
        </select>
        </div>
        <div class="col-md-4">
        <label class="form-label">搜索</label>
        <div class="input-group">
        <input type="text" class="form-control" id="providerSearchInput" placeholder="搜索码商名称、邮箱、公司名...">
        <button class="btn btn-outline-secondary" onclick="window.providerManager.searchProviders()">
        <i class="bi bi-search"></i>
        </button>
        </div>
        </div>
        <div class="col-md-2">
        <label class="form-label">&nbsp;
        </label>
        <div class="d-grid">
        <button class="btn btn-primary" onclick="window.providerManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>筛选
        </button>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 码商列表 -->
        <div class="providers-section">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-building me-2"></i>码商列表
        <span class="badge bg-secondary ms-2" id="providerCount">0</span>
        </h5>
        </div>
        <div class="card-body">
        <div id="providersTableContainer">
        <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载码商数据...</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 分页 -->
        <div class="pagination-section" id="providerPagination">
        <!-- 分页控件将在这里动态生成 -->
        </div>
        </div>
        <!-- 码商详情/编辑模态框 -->
        <div class="modal fade" id="providerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title" id="providerModalTitle">码商详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="providerForm">
        <input type="hidden" id="providerId">
        <!-- 基本信息 -->
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">用户名 *</label>
        <input type="text" class="form-control" id="providerUsername" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">真实姓名 *</label>
        <input type="text" class="form-control" id="providerRealName" required>
        </div>
        </div>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">邮箱 *</label>
        <input type="email" class="form-control" id="providerEmail" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">手机号</label>
        <input type="text" class="form-control" id="providerPhone">
        </div>
        </div>
        <div class="row mb-3" id="providerPasswordRow">
        <div class="col-md-6">
        <label class="form-label">密码 *</label>
        <input type="password" class="form-control" id="providerPassword" required>
        </div>
        </div>
        <!-- 公司信息 -->
        <hr>
        <h6>公司信息</h6>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">公司名称 *</label>
        <input type="text" class="form-control" id="providerCompanyName" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">业务类型</label>
        <select class="form-select" id="providerBusinessType">
        <option value="">请选择</option>
        <option value="个人">个人</option>
        <option value="企业">企业</option>
        <option value="个体工商户">个体工商户</option>
        </select>
        </div>
        </div>
        <div class="row mb-3">
        <div class="col-md-12">
        <label class="form-label">营业执照号</label>
        <input type="text" class="form-control" id="providerBusinessLicense">
        </div>
        </div>
        <!-- 费率设置 -->
        <hr>
        <h6>费率设置</h6>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">结算费率 (%)</label>
        <input type="number" class="form-control" id="providerSettlementRate" step="0.01" min="0" max="100" value="95">
        <small class="text-muted">码商的结算费率，默认95%</small>
        </div>
        <div class="col-md-6">
        <label class="form-label">状态</label>
        <select class="form-select" id="providerStatus">
        <option value="pending">待审核</option>
        <option value="approved">已通过</option>
        <option value="suspended">已暂停</option>
        <option value="rejected">已拒绝</option>
        </select>
        </div>
        </div>
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.providerManager.saveProvider()">
        <span id="providerSaveText">保存</span>
        <span class="spinner-border spinner-border-sm ms-2 d-none" id="providerSaveSpinner"></span>
        </button>
        </div>
        </div>
        </div>
        </div>
        `,window.providerManager||(window.providerManager=new ProviderManager),window.providerManager.initialize()):a.innerHTML=`
            <div class="alert alert-danger">
            <h4>未授权访问</h4>
            <p>您没有权限访问码商管理功能。</p>
            </div>
            `}loadMerchantManagement(a){var i=this.auth.getUser();i&&["admin"].includes(i.user_type)?(document.getElementById("pageTitle").textContent="商户管理",a.innerHTML=`
        <div class="merchant-management">
        <!-- 页面头部 -->
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h2 class="mb-1">商户管理</h2>
        <p class="text-muted mb-0">管理商户账户、API密钥和交易配置</p>
        </div>
        <div>
        <button class="btn btn-primary" onclick="window.merchantManager.showCreateModal()">
        <i class="bi bi-plus-circle me-2"></i>添加商户
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.merchantManager.refreshMerchants()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 统计卡片 -->
        <div class="stats-section" id="merchantStats">
        <div class="row">
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number" id="totalMerchants">-</div>
        <div class="stat-label">总商户数</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-warning" id="pendingMerchants">-</div>
        <div class="stat-label">待审核</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-success" id="activeMerchants">-</div>
        <div class="stat-label">已激活</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-danger" id="suspendedMerchants">-</div>
        <div class="stat-label">已暂停</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-secondary" id="rejectedMerchants">-</div>
        <div class="stat-label">已拒绝</div>
        </div>
        </div>
        </div>
        </div>
        <!-- 筛选控制栏 -->
        <div class="controls-section">
        <div class="card">
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-2">
        <label class="form-label">状态筛选</label>
        <select class="form-select" id="statusFilter">
        <option value="">全部状态</option>
        <option value="pending">待审核</option>
        <option value="active">已激活</option>
        <option value="suspended">已暂停</option>
        <option value="rejected">已拒绝</option>
        </select>
        </div>
        <div class="col-md-4">
        <label class="form-label">搜索</label>
        <div class="input-group">
        <input type="text" class="form-control" id="searchInput" placeholder="搜索商户名称、邮箱、公司名...">
        <button class="btn btn-outline-secondary" onclick="window.merchantManager.searchMerchants()">
        <i class="bi bi-search"></i>
        </button>
        </div>
        </div>
        <div class="col-md-2">
        <label class="form-label">&nbsp;
        </label>
        <div class="d-grid">
        <button class="btn btn-primary" onclick="window.merchantManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>筛选
        </button>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 商户列表 -->
        <div class="merchants-section">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-shop me-2"></i>商户列表
        <span class="badge bg-secondary ms-2" id="merchantCount">0</span>
        </h5>
        </div>
        <div class="card-body">
        <div id="merchantsTableContainer">
        <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载商户数据...</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 分页 -->
        <div class="pagination-section" id="merchantPagination">
        <!-- 分页控件将在这里动态生成 -->
        </div>
        </div>
        <!-- 商户详情/编辑模态框 -->
        <div class="modal fade" id="merchantModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title" id="merchantModalTitle">商户详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="merchantForm">
        <input type="hidden" id="merchantId">
        <!-- 基本信息 -->
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">用户名 *</label>
        <input type="text" class="form-control" id="username" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">真实姓名 *</label>
        <input type="text" class="form-control" id="realName" required>
        </div>
        </div>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">邮箱 *</label>
        <input type="email" class="form-control" id="email" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">手机号</label>
        <input type="text" class="form-control" id="phone">
        </div>
        </div>
        <div class="row mb-3" id="passwordRow">
        <div class="col-md-6">
        <label class="form-label">密码 *</label>
        <input type="password" class="form-control" id="password" required>
        </div>
        </div>
        <!-- 商户信息 -->
        <hr>
        <h6>商户信息</h6>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">公司名称</label>
        <input type="text" class="form-control" id="companyName">
        </div>
        <div class="col-md-6">
        <label class="form-label">营业执照号</label>
        <input type="text" class="form-control" id="businessLicense">
        </div>
        </div>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">服务费率 (%)</label>
        <input type="number" class="form-control" id="serviceRate" step="0.01" min="0" max="100">
        </div>
        <div class="col-md-6">
        <label class="form-label">状态</label>
        <select class="form-select" id="status">
        <option value="pending">待审核</option>
        <option value="active">已激活</option>
        <option value="suspended">已暂停</option>
        <option value="rejected">已拒绝</option>
        </select>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">IP白名单</label>
        <textarea class="form-control" id="ipWhitelist" rows="3" placeholder="多个IP用逗号分隔，留空表示不限制"></textarea>
        <div class="form-text">例如：***********,********</div>
        </div>
        <!-- API信息 -->
        <div id="apiInfoSection" style="display: none;
        ">
        <hr>
        <h6>API信息</h6>
        <div class="mb-3">
        <label class="form-label">API密钥</label>
        <div class="input-group">
        <input type="text" class="form-control" id="apiKey" readonly>
        <button type="button" class="btn btn-outline-secondary" onclick="window.merchantManager.regenerateApiKey()">
        <i class="bi bi-arrow-clockwise me-1"></i>重新生成
        </button>
        </div>
        </div>
        </div>
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.merchantManager.saveMerchant()" id="saveMerchantBtn">
        <span class="spinner-border spinner-border-sm me-2" style="display: none;
        "></span>
        保存
        </button>
        </div>
        </div>
        </div>
        </div>
        `,window.merchantManager||(window.merchantManager=new MerchantManager),window.merchantManager.initialize()):a.innerHTML=`
            <div class="alert alert-danger">
            <h4>未授权访问</h4>
            <p>您没有权限访问商户管理功能。</p>
            </div>
            `}loadTransactionManagement(a){var i,e,s=this.auth.getUser();s?(i="admin"===s.user_type?"交易记录管理":"我的交易记录",e="admin"===s.user_type?"查看和管理所有交易记录、统计分析":"provider"===s.user_type?"查看您名下的交易记录和统计数据":"查看您的交易记录",document.getElementById("pageTitle").textContent=i,a.innerHTML=`
        <div class="transaction-management">
        <!-- 页面头部 -->
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h2 class="mb-1">${i}</h2>
        <p class="text-muted mb-0">${e}</p>
        ${"provider"===s.user_type?`<small class="text-info">当前码商：${s.display_name||s.real_name}</small>`:""}
        </div>
        <div>
        <button class="btn btn-outline-success" onclick="window.transactionManager.exportTransactions()">
        <i class="bi bi-download me-2"></i>导出数据
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.transactionManager.refreshTransactions()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 统计卡片 -->
        <div class="stats-section" id="transactionStats">
        <div class="row">
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number" id="totalTransactions">-</div>
        <div class="stat-label">总交易数</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-success" id="successTransactions">-</div>
        <div class="stat-label">成功交易</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-warning" id="pendingTransactions">-</div>
        <div class="stat-label">待处理</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-danger" id="failedTransactions">-</div>
        <div class="stat-label">失败交易</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-info" id="totalAmount">-</div>
        <div class="stat-label">交易总额</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-primary" id="successRate">-</div>
        <div class="stat-label">成功率</div>
        </div>
        </div>
        </div>
        </div>
        <!-- 筛选控制栏 -->
        <div class="controls-section">
        <div class="card">
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-2">
        <label class="form-label">状态筛选</label>
        <select class="form-select" id="statusFilter">
        <option value="">全部状态</option>
        <option value="pending">待处理</option>
        <option value="success">成功</option>
        <option value="failed">失败</option>
        <option value="cancelled">已取消</option>
        </select>
        </div>
        <div class="col-md-2">
        <label class="form-label">时间范围</label>
        <select class="form-select" id="dateRangeFilter">
        <option value="today">今天</option>
        <option value="yesterday">昨天</option>
        <option value="week">最近7天</option>
        <option value="month">最近30天</option>
        <option value="custom">自定义</option>
        </select>
        </div>
        <div class="col-md-2" id="customDateRange" style="display: none;
        ">
        <label class="form-label">开始日期</label>
        <input type="date" class="form-control" id="startDate">
        </div>
        <div class="col-md-2" id="customDateRange2" style="display: none;
        ">
        <label class="form-label">结束日期</label>
        <input type="date" class="form-control" id="endDate">
        </div>
        ${"admin"===s.user_type?`
            <div class="col-md-2">
            <label class="form-label">商户筛选</label>
            <select class="form-select" id="merchantFilter">
            <option value="">全部商户</option>
            </select>
            </div>
            <div class="col-md-2">
            <label class="form-label">码商筛选</label>
            <select class="form-select" id="providerFilter">
            <option value="">全部码商</option>
            </select>
            </div>
            `:""}
        <div class="col-md-3">
        <label class="form-label">搜索</label>
        <div class="input-group">
        <input type="text" class="form-control" id="searchInput" placeholder="搜索订单号、备注...">
        <button class="btn btn-outline-secondary" onclick="window.transactionManager.searchTransactions()">
        <i class="bi bi-search"></i>
        </button>
        </div>
        </div>
        <div class="col-md-1">
        <label class="form-label">&nbsp;
        </label>
        <div class="d-grid">
        <button class="btn btn-primary" onclick="window.transactionManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>筛选
        </button>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 数据透视图（可选） -->
        <div class="chart-section" id="transactionChart" style="display: none;
        ">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-bar-chart me-2"></i>趋势分析
        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="window.transactionManager.toggleChart()">
        <i class="bi bi-eye-slash"></i> 隐藏图表
        </button>
        </h5>
        </div>
        <div class="card-body">
        <canvas id="transactionTrendChart" height="300"></canvas>
        </div>
        </div>
        </div>
        <!-- 交易列表表格 -->
        <div class="table-section">
        <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
        <i class="bi bi-list-ul me-2"></i>交易列表
        <span class="badge bg-secondary ms-2" id="transactionCount">0</span>
        </h5>
        <div class="btn-group" role="group">
        <button class="btn btn-sm btn-outline-info" onclick="window.transactionManager.toggleChart()">
        <i class="bi bi-bar-chart me-1"></i>图表
        </button>
        <button class="btn btn-sm btn-outline-primary" onclick="window.transactionManager.exportCurrentData()">
        <i class="bi bi-download me-1"></i>导出当前
        </button>
        </div>
        </div>
        <div class="card-body p-0">
        <div id="transactionsTableContainer">
        <div class="text-center p-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载交易数据...</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 分页控件 -->
        <div class="pagination-section" id="paginationContainer">
        <!-- 分页内容将通过JavaScript动态生成 -->
        </div>
        </div>
        <!-- 交易详情模态框 -->
        <div class="modal fade" id="transactionDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">
        <i class="bi bi-receipt me-2"></i>交易详情
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body" id="transactionDetailContent">
        <!-- 详情内容 -->
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        ${["admin","provider"].includes(s.user_type)?`
            <button type="button" class="btn btn-primary" id="updateStatusBtn" style="display: none;
            ">
            更新状态
            </button>
            `:""}
        </div>
        </div>
        </div>
        </div>
        <!-- 状态更新模态框 -->
        ${["admin","provider"].includes(s.user_type)?`
            <div class="modal fade" id="updateStatusModal" tabindex="-1">
            <div class="modal-dialog">
            <div class="modal-content">
            <div class="modal-header">
            <h5 class="modal-title">更新交易状态</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
            <form id="updateStatusForm">
            <input type="hidden" id="updateTransactionId">
            <div class="mb-3">
            <label class="form-label">新状态</label>
            <select class="form-select" id="newStatus" required>
            <option value="">请选择状态</option>
            <option value="pending">待处理</option>
            <option value="success">成功</option>
            <option value="failed">失败</option>
            <option value="cancelled">已取消</option>
            </select>
            </div>
            <div class="mb-3">
            <label class="form-label">备注（可选）</label>
            <textarea class="form-control" id="updateRemark" rows="3" placeholder="更新原因或备注信息..."></textarea>
            </div>
            </form>
            </div>
            <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" onclick="window.transactionManager.confirmUpdateStatus()">
            <span id="updateStatusSpinner" class="spinner-border spinner-border-sm d-none me-2"></span>
            确认更新
            </button>
            </div>
            </div>
            </div>
            </div>
            `:""}
        `,window.transactionManager||(window.transactionManager=new TransactionManager),window.transactionManager.initialize()):a.innerHTML=`
            <div class="alert alert-danger">
            <h4>未授权访问</h4>
            <p>请先登录系统。</p>
            </div>
            `}loadAlipayManagement(a){let i=this.auth.getUser();var e,s;i&&["admin","provider"].includes(i.user_type)?(e="admin"===i.user_type?"支付宝账户管理":"我的支付宝账户",s="admin"===i.user_type?"管理所有码商的支付宝账户信息":"管理您的支付宝账户信息",document.getElementById("pageTitle").textContent=e,a.innerHTML=`
        <div class="alipay-management">
        <!-- 页面头部 -->
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h2 class="mb-1">${e}</h2>
        <p class="text-muted mb-0">${s}</p>
        ${"provider"===i.user_type?`<small class="text-info">当前码商：${i.display_name||i.real_name}</small>`:""}
        </div>
        <div>
        <button class="btn btn-primary" onclick="window.accountManager.showAddAccountModal()">
        <i class="bi bi-plus-circle me-2"></i>添加账户
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.accountManager.refreshAccounts()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 筛选控制栏 -->
        <div class="controls-section">
        <div class="card">
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-3">
        <label class="form-label">状态筛选</label>
        <select class="form-select" id="statusFilter">
        <option value="">全部状态</option>
        <option value="pending">待审核</option>
        <option value="approved">已审核</option>
        <option value="rejected">已拒绝</option>
        <option value="disabled">已禁用</option>
        </select>
        </div>
        ${"admin"===i.user_type?`
            <div class="col-md-3">
            <label class="form-label">码商筛选</label>
            <select class="form-select" id="providerFilter">
            <option value="">全部码商</option>
            </select>
            </div>
            `:""}
        <div class="col-md-4">
        <label class="form-label">搜索</label>
        <div class="input-group">
        <input type="text" class="form-control" id="searchInput" placeholder="搜索账户名称、支付宝账号、手机号...">
        <button class="btn btn-outline-secondary" onclick="window.accountManager.searchAccounts()">
        <i class="bi bi-search"></i>
        </button>
        </div>
        </div>
        <div class="col-md-2">
        <label class="form-label">&nbsp;
        </label>
        <div class="d-grid">
        <button class="btn btn-primary" onclick="window.accountManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>应用筛选
        </button>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 统计信息 -->
        <div class="stats-section" id="accountStats">
        <div class="row">
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number" id="totalAccounts">-</div>
        <div class="stat-label">总账户数</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-warning" id="pendingAccounts">-</div>
        <div class="stat-label">待审核</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-success" id="approvedAccounts">-</div>
        <div class="stat-label">已审核</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-danger" id="rejectedAccounts">-</div>
        <div class="stat-label">已拒绝</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-secondary" id="disabledAccounts">-</div>
        <div class="stat-label">已禁用</div>
        </div>
        </div>
        <div class="col-md-2">
        <div class="stat-card">
        <div class="stat-number text-info" id="totalDailyLimit">-</div>
        <div class="stat-label">总日限额</div>
        </div>
        </div>
        </div>
        </div>
        <!-- 账户列表表格 -->
        <div class="table-section">
        <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
        <i class="bi bi-credit-card me-2"></i>账户列表
        <span class="badge bg-secondary ms-2" id="accountCount">0</span>
        </h5>
        <div class="btn-group" role="group">
        <button class="btn btn-sm btn-outline-primary" onclick="window.accountManager.exportAccounts()">
        <i class="bi bi-download me-1"></i>导出
        </button>
        <button class="btn btn-sm btn-outline-secondary" onclick="window.accountManager.toggleView()">
        <i class="bi bi-grid-3x3-gap me-1"></i>切换视图
        </button>
        </div>
        </div>
        <div class="card-body p-0">
        <div id="accountsTableContainer">
        <div class="text-center p-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载账户数据...</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 分页控件 -->
        <div class="pagination-section" id="paginationContainer">
        <!-- 分页内容将通过JavaScript动态生成 -->
        </div>
        <!-- 批量操作确认模态框 -->
        <div class="modal fade" id="batchConfirmModal" tabindex="-1">
        <div class="modal-dialog">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">确认批量操作</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <p>您确定要对选中的 <span id="batchCount">0</span> 个账户执行 <strong id="batchAction"></strong> 操作吗？</p>
        <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle me-2"></i>
        此操作不可撤销，请谨慎操作。
        </div>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-danger" onclick="window.accountManager.executeBatchOperation()">确认执行</button>
        </div>
        </div>
        </div>
        </div>
        </div>
        `,window.accountManager||(console.log("创建新的AccountManager实例..."),window.accountManager=new AccountManager),setTimeout(()=>{window.accountManager?(console.log("开始加载支付宝账户数据..."),console.log("当前用户信息:",i),window.accountManager.loadAccountsForCurrentUser()):console.error("AccountManager未初始化")},500)):a.innerHTML=`
            <div class="alert alert-danger">
            <h4>权限不足</h4>
            <p>您没有权限访问支付宝账户管理功能。</p>
            </div>
            `}loadProductManagement(a){var i=this.auth.getUser();i&&["admin","merchant"].includes(i.user_type)?(document.getElementById("pageTitle").textContent="产品管理",a.innerHTML=`
        <div class="product-management">
        <!-- 页面头部 -->
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h2 class="mb-1">产品管理</h2>
        <p class="text-muted mb-0">管理商户产品配置、费率设置和使用统计</p>
        </div>
        <div>
        <button class="btn btn-primary" onclick="window.productManager.showCreateModal()">
        <i class="bi bi-plus-circle me-2"></i>添加产品
        </button>
        <button class="btn btn-success ms-2" onclick="window.productManager.exportProducts()">
        <i class="bi bi-download me-2"></i>导出数据
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.productManager.refreshProducts()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 统计卡片 -->
        <div class="stats-section" id="productStats">
        <div class="row">
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number" id="totalProducts">-</div>
        <div class="stat-label">总产品数</div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number text-success" id="activeProducts">-</div>
        <div class="stat-label">启用产品</div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number text-warning" id="inactiveProducts">-</div>
        <div class="stat-label">禁用产品</div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="stat-card">
        <div class="stat-number text-info" id="todayTransactions">-</div>
        <div class="stat-label">今日交易</div>
        </div>
        </div>
        </div>
        </div>
        <!-- 筛选控制栏 -->
        <div class="controls-section">
        <div class="card">
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-2">
        <label class="form-label">状态筛选</label>
        <select class="form-select" id="productStatusFilter">
        <option value="">全部状态</option>
        <option value="active">启用</option>
        <option value="inactive">禁用</option>
        <option value="suspended">暂停</option>
        </select>
        </div>
        ${"admin"===i.user_type?`
            <div class="col-md-2">
            <label class="form-label">商户筛选</label>
            <select class="form-select" id="productMerchantFilter">
            <option value="">全部商户</option>
            </select>
            </div>
            `:""}
        <div class="col-md-4">
        <label class="form-label">搜索</label>
        <div class="input-group">
        <input type="text" class="form-control" id="productSearch" placeholder="搜索产品名称...">
        <button class="btn btn-outline-secondary" onclick="window.productManager.searchProducts()">
        <i class="bi bi-search"></i>
        </button>
        </div>
        </div>
        <div class="col-md-2">
        <label class="form-label">&nbsp;
        </label>
        <div class="d-grid">
        <button class="btn btn-primary" onclick="window.productManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>筛选
        </button>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 产品列表 -->
        <div class="products-section">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-box-seam me-2"></i>产品列表
        <span class="badge bg-secondary ms-2" id="productCount">0</span>
        </h5>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-hover">
        <thead class="table-light">
        <tr>
        <th>产品信息</th>
        ${"admin"===i.user_type?"<th>所属商户</th>":""}
        <th>金额范围</th>
        <th>服务费率</th>
        <th>状态</th>
        <th>交易统计</th>
        <th>创建时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody id="productTableBody">
        <tr>
        <td colspan="${"admin"===i.user_type?"8":"7"}" class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载产品数据...</p>
        </td>
        </tr>
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        <!-- 分页 -->
        <div class="pagination-section" id="productPagination">
        <!-- 分页控件将在这里动态生成 -->
        </div>
        </div>
        <!-- 产品创建/编辑模态框 -->
        <div class="modal fade" id="productModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title" id="productModalTitle">产品详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="productForm">
        <input type="hidden" id="productId">
        <!-- 基本信息 -->
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">产品名称 *</label>
        <input type="text" class="form-control" id="productName" required>
        </div>
        ${"admin"===i.user_type?`
            <div class="col-md-6">
            <label class="form-label">所属商户 *</label>
            <select class="form-select" id="productMerchant" required>
            <option value="">请选择商户</option>
            </select>
            </div>
            `:""}
        </div>
        <div class="mb-3">
        <label class="form-label">产品描述</label>
        <textarea class="form-control" id="productDescription" rows="3" placeholder="产品功能描述（可选）"></textarea>
        </div>
        <!-- 金额配置 -->
        <hr>
        <h6>金额配置</h6>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">最小金额 (元) *</label>
        <input type="number" class="form-control" id="productMinAmount" step="0.01" min="0.01" required>
        </div>
        <div class="col-md-6">
        <label class="form-label">最大金额 (元) *</label>
        <input type="number" class="form-control" id="productMaxAmount" step="0.01" min="0.01" required>
        </div>
        </div>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">服务费率 (%)</label>
        <input type="number" class="form-control" id="productServiceRate" step="0.01" min="0" max="100">
        <div class="form-text">留空使用商户默认费率</div>
        </div>
        <div class="col-md-6">
        <label class="form-label">状态</label>
        <select class="form-select" id="productStatus">
        <option value="active">启用</option>
        <option value="inactive">禁用</option>
        <option value="suspended">暂停</option>
        </select>
        </div>
        </div>
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.productManager.saveProduct()" id="saveProductBtn">
        <span class="spinner-border spinner-border-sm me-2" style="display: none;
        "></span>
        保存
        </button>
        </div>
        </div>
        </div>
        </div>
        `,window.productManager||(window.productManager=new ProductManager),window.productManager.initialize()):a.innerHTML=`
            <div class="alert alert-danger">
            <h4>未授权访问</h4>
            <p>您没有权限访问产品管理功能。</p>
            </div>
            `}loadFinancialManagement(a){var i=this.auth.getUser();i&&["admin","merchant"].includes(i.user_type)?(document.getElementById("pageTitle").textContent="财务管理",a.innerHTML=`
        <div class="financial-management">
        <!-- 页面头部 -->
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h2 class="mb-1">财务管理</h2>
        <p class="text-muted mb-0">查看财务概览、流水记录和统计分析</p>
        </div>
        <div>
        <button class="btn btn-outline-primary" onclick="window.financialManager.exportData()">
        <i class="bi bi-download me-2"></i>导出数据
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.financialManager.refreshFinancial()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 筛选控制栏 -->
        <div class="controls-section">
        <div class="card">
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-3">
        <label class="form-label">时间范围</label>
        <select class="form-select" id="financialDateRange">
        <option value="today">今日</option>
        <option value="week">本周</option>
        <option value="month" selected>本月</option>
        <option value="quarter">本季度</option>
        <option value="year">本年</option>
        <option value="custom">自定义</option>
        </select>
        </div>
        ${"admin"===i.user_type?`
            <div class="col-md-3">
            <label class="form-label">商户筛选</label>
            <select class="form-select" id="financialMerchantFilter">
            <option value="">全部商户</option>
            </select>
            </div>
            `:""}
        <div class="col-md-3">
        <label class="form-label">数据类型</label>
        <select class="form-select" id="financialDataType">
        <option value="overview">财务概览</option>
        <option value="transactions">交易流水</option>
        <option value="settlement">结算数据</option>
        <option value="statistics">统计分析</option>
        </select>
        </div>
        <div class="col-md-3">
        <label class="form-label">&nbsp;
        </label>
        <div class="d-grid">
        <button class="btn btn-primary" onclick="window.financialManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>应用筛选
        </button>
        </div>
        </div>
        </div>
        <!-- 自定义日期范围 -->
        <div class="row g-3 mt-2" id="customDateRange" style="display: none;
        ">
        <div class="col-md-3">
        <label class="form-label">开始日期</label>
        <input type="date" class="form-control" id="startDate">
        </div>
        <div class="col-md-3">
        <label class="form-label">结束日期</label>
        <input type="date" class="form-control" id="endDate">
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 财务概览卡片 -->
        <div class="overview-section" id="financialOverview">
        <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载财务数据...</p>
        </div>
        </div>
        <!-- 图表展示区域 -->
        <div class="charts-section">
        <div class="row">
        <div class="col-md-8">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-bar-chart me-2"></i>收入趋势图
        </h5>
        </div>
        <div class="card-body">
        <canvas id="revenueChart" height="300"></canvas>
        </div>
        </div>
        </div>
        <div class="col-md-4">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-pie-chart me-2"></i>收入构成
        </h5>
        </div>
        <div class="card-body">
        <canvas id="revenueCompositionChart" height="300"></canvas>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 详细数据表格 -->
        <div class="data-section">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-table me-2"></i>详细数据
        <span class="badge bg-secondary ms-2" id="dataCount">0</span>
        </h5>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-hover">
        <thead class="table-light" id="dataTableHeader">
        <!-- 表头将根据数据类型动态生成 -->
        </thead>
        <tbody id="dataTableBody">
        <tr>
        <td colspan="8" class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载数据...</p>
        </td>
        </tr>
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        <!-- 分页 -->
        <div class="pagination-section" id="financialPagination">
        <!-- 分页控件将在这里动态生成 -->
        </div>
        </div>
        `,window.financialManager||(window.financialManager=new FinancialManager),window.financialManager.initialize()):a.innerHTML=`
            <div class="alert alert-danger">
            <h4>未授权访问</h4>
            <p>您没有权限访问财务管理功能。</p>
            </div>
            `}loadRiskControlManagement(a){var i=this.auth.getUser();i&&["admin","merchant"].includes(i.user_type)?(document.getElementById("pageTitle").textContent="风控管理",a.innerHTML=`
        <div class="risk-control-management">
        <!-- 页面头部 -->
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h2 class="mb-1">风控管理</h2>
        <p class="text-muted mb-0">配置和管理风险控制规则，保障交易安全</p>
        </div>
        <div>
        <button class="btn btn-primary" onclick="window.riskControlManager.showCreateModal()">
        <i class="bi bi-plus-circle me-2"></i>添加风控规则
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="window.riskControlManager.refreshRiskConfigs()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 统计卡片 -->
        <div class="stats-section" id="riskStats">
        <div class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载统计数据...</p>
        </div>
        </div>
        <!-- 筛选控制栏 -->
        <div class="controls-section">
        <div class="card">
        <div class="card-body">
        <div class="row g-3">
        ${"admin"===i.user_type?`
            <div class="col-md-3">
            <label class="form-label">商户筛选</label>
            <select class="form-select" id="riskMerchantFilter">
            <option value="">全部商户</option>
            </select>
            </div>
            `:""}
        <div class="col-md-3">
        <label class="form-label">规则状态</label>
        <select class="form-select" id="riskStatusFilter">
        <option value="">全部状态</option>
        <option value="active">启用</option>
        <option value="inactive">禁用</option>
        </select>
        </div>
        <div class="col-md-3">
        <label class="form-label">规则类型</label>
        <select class="form-select" id="riskTypeFilter">
        <option value="">全部类型</option>
        <option value="amount_limit">金额限制</option>
        <option value="frequency_limit">频率限制</option>
        <option value="ip_control">IP控制</option>
        <option value="device_control">设备控制</option>
        </select>
        </div>
        <div class="col-md-3">
        <label class="form-label">&nbsp;
        </label>
        <div class="d-grid">
        <button class="btn btn-primary" onclick="window.riskControlManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>筛选
        </button>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 风控规则列表 -->
        <div class="risk-configs-section">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-shield-check me-2"></i>风控规则列表
        <span class="badge bg-secondary ms-2" id="riskConfigCount">0</span>
        </h5>
        </div>
        <div class="card-body">
        <div class="table-responsive">
        <table class="table table-hover">
        <thead class="table-light">
        <tr>
        <th>规则信息</th>
        ${"admin"===i.user_type?"<th>所属商户</th>":""}
        <th>日限额</th>
        <th>单笔限额</th>
        <th>状态</th>
        <th>创建时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody id="riskConfigsTableBody">
        <tr>
        <td colspan="${"admin"===i.user_type?"7":"6"}" class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载风控规则...</p>
        </td>
        </tr>
        </tbody>
        </table>
        </div>
        </div>
        </div>
        </div>
        <!-- 风控事件监控 -->
        <div class="risk-events-section">
        <div class="card">
        <div class="card-header">
        <h5 class="mb-0">
        <i class="bi bi-exclamation-triangle me-2"></i>风控事件监控
        </h5>
        </div>
        <div class="card-body">
        <div class="row">
        <div class="col-md-6">
        <div class="alert alert-warning">
        <h6><i class="bi bi-clock me-2"></i>最近24小时</h6>
        <p class="mb-0">触发风控规则 <strong id="recentRiskEvents">0</strong> 次</p>
        </div>
        </div>
        <div class="col-md-6">
        <div class="alert alert-info">
        <h6><i class="bi bi-shield-fill-check me-2"></i>拦截统计</h6>
        <p class="mb-0">累计拦截可疑交易 <strong id="totalBlockedTransactions">0</strong> 笔</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 风控规则创建/编辑模态框 -->
        <div class="modal fade" id="riskConfigModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title" id="riskConfigModalTitle">风控规则详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="riskConfigForm">
        <input type="hidden" id="riskConfigId">
        <!-- 基本信息 -->
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">规则名称 *</label>
        <input type="text" class="form-control" id="riskRuleName" required>
        </div>
        ${"admin"===i.user_type?`
            <div class="col-md-6">
            <label class="form-label">所属商户 *</label>
            <select class="form-select" id="riskMerchant" required>
            <option value="">请选择商户</option>
            </select>
            </div>
            `:""}
        </div>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">规则类型 *</label>
        <select class="form-select" id="riskRuleType" required>
        <option value="">请选择类型</option>
        <option value="amount_limit">金额限制</option>
        <option value="frequency_limit">频率限制</option>
        <option value="ip_control">IP控制</option>
        <option value="device_control">设备控制</option>
        </select>
        </div>
        <div class="col-md-6">
        <label class="form-label">状态</label>
        <select class="form-select" id="riskConfigStatus">
        <option value="active">启用</option>
        <option value="inactive">禁用</option>
        </select>
        </div>
        </div>
        <!-- 限额配置 -->
        <hr>
        <h6>限额配置</h6>
        <div class="row mb-3">
        <div class="col-md-6">
        <label class="form-label">日限额 (元)</label>
        <input type="number" class="form-control" id="riskDailyLimit" step="0.01" min="0">
        <div class="form-text">留空表示无限制</div>
        </div>
        <div class="col-md-6">
        <label class="form-label">单笔限额 (元)</label>
        <input type="number" class="form-control" id="riskSingleLimit" step="0.01" min="0">
        <div class="form-text">留空表示无限制</div>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">规则描述</label>
        <textarea class="form-control" id="riskRuleDescription" rows="3" placeholder="详细描述风控规则的作用和触发条件"></textarea>
        </div>
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.riskControlManager.saveRiskConfig()" id="saveRiskConfigBtn">
        <span class="spinner-border spinner-border-sm me-2" style="display: none;
        "></span>
        保存
        </button>
        </div>
        </div>
        </div>
        </div>
        `,window.riskControlManager||(window.riskControlManager=new RiskControlManager),window.riskControlManager.initialize()):a.innerHTML=`
            <div class="alert alert-danger">
            <h4>未授权访问</h4>
            <p>您没有权限访问风控管理功能。</p>
            </div>
            `}loadPaymentRequestManagement(a){a.innerHTML=`
        <div class="management-page">
        <div class="page-header">
        <div class="d-flex justify-content-between align-items-center">
        <div>
        <h4><i class="bi bi-credit-card-2-front me-2"></i>支付请求管理</h4>
        <p class="text-muted mb-0">管理和监控所有支付请求</p>
        </div>
        <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="window.paymentRequestManager.exportData()">
        <i class="bi bi-download me-2"></i>导出数据
        </button>
        <button class="btn btn-primary" onclick="window.paymentRequestManager.refreshPaymentRequests()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        </div>
        <!-- 统计卡片 -->
        <div class="row mb-4" id="paymentRequestStats">
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="stat-icon bg-primary text-white">
        <i class="bi bi-credit-card"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="stat-label">总请求数</div>
        <div class="stat-value" id="totalRequests">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="stat-icon bg-success text-white">
        <i class="bi bi-check-circle"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="stat-label">成功请求</div>
        <div class="stat-value" id="successRequests">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="stat-icon bg-warning text-white">
        <i class="bi bi-clock"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="stat-label">处理中</div>
        <div class="stat-value" id="pendingRequests">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-3">
        <div class="card border-0 shadow-sm">
        <div class="card-body">
        <div class="d-flex align-items-center">
        <div class="flex-shrink-0">
        <div class="stat-icon bg-danger text-white">
        <i class="bi bi-x-circle"></i>
        </div>
        </div>
        <div class="flex-grow-1 ms-3">
        <div class="stat-label">失败请求</div>
        <div class="stat-value" id="failedRequests">-</div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 筛选和搜索 -->
        <div class="card mb-4">
        <div class="card-body">
        <div class="row g-3">
        <div class="col-md-3">
        <label class="form-label">商户筛选</label>
        <select class="form-select" id="merchantFilter">
        <option value="">所有商户</option>
        </select>
        </div>
        <div class="col-md-3">
        <label class="form-label">状态筛选</label>
        <select class="form-select" id="statusFilter">
        <option value="">所有状态</option>
        <option value="pending">待处理</option>
        <option value="processing">处理中</option>
        <option value="success">成功</option>
        <option value="failed">失败</option>
        <option value="cancelled">已取消</option>
        </select>
        </div>
        <div class="col-md-3">
        <label class="form-label">时间范围</label>
        <select class="form-select" id="dateRangeFilter">
        <option value="today">今天</option>
        <option value="week">本周</option>
        <option value="month">本月</option>
        <option value="quarter">本季度</option>
        <option value="year">本年</option>
        <option value="custom">自定义</option>
        </select>
        </div>
        <div class="col-md-3">
        <label class="form-label">搜索</label>
        <div class="input-group">
        <input type="text" class="form-control" id="searchInput" placeholder="订单号/商户名称">
        <button class="btn btn-outline-secondary" type="button" onclick="window.paymentRequestManager.searchPaymentRequests()">
        <i class="bi bi-search"></i>
        </button>
        </div>
        </div>
        </div>
        <div class="row mt-3" id="customDateRange" style="display: none;
        ">
        <div class="col-md-3">
        <label class="form-label">开始日期</label>
        <input type="date" class="form-control" id="startDate">
        </div>
        <div class="col-md-3">
        <label class="form-label">结束日期</label>
        <input type="date" class="form-control" id="endDate">
        </div>
        <div class="col-md-3 d-flex align-items-end">
        <button class="btn btn-primary" onclick="window.paymentRequestManager.applyFilters()">
        <i class="bi bi-funnel me-2"></i>应用筛选
        </button>
        </div>
        </div>
        </div>
        </div>
        <!-- 支付请求列表 -->
        <div class="card">
        <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
        <h6 class="mb-0">支付请求列表</h6>
        <div class="d-flex align-items-center gap-2">
        <small class="text-muted">共 <span id="paymentRequestCount">0</span> 条记录</small>
        <div class="btn-group" role="group">
        <button class="btn btn-outline-primary btn-sm" onclick="window.paymentRequestManager.batchUpdateStatus()">
        <i class="bi bi-arrow-repeat me-1"></i>批量操作
        </button>
        </div>
        </div>
        </div>
        </div>
        <div class="card-body p-0">
        <div id="paymentRequestTableContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载支付请求数据...</div>
        </div>
        </div>
        </div>
        </div>
        <!-- 分页 -->
        <div id="paymentRequestPagination" class="d-flex justify-content-center mt-4"></div>
        </div>
        <!-- 支付请求详情模态框 -->
        <div class="modal fade" id="paymentRequestDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">支付请求详情</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body" id="paymentRequestDetailContent">
        <!-- 详情内容 -->
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        <button type="button" class="btn btn-primary" onclick="window.paymentRequestManager.retryPaymentRequest()">
        <i class="bi bi-arrow-clockwise me-2"></i>重试请求
        </button>
        </div>
        </div>
        </div>
        </div>
        <!-- 状态更新模态框 -->
        <div class="modal fade" id="updateStatusModal" tabindex="-1">
        <div class="modal-dialog">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">更新支付状态</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <form id="updateStatusForm">
        <div class="mb-3">
        <label class="form-label">新状态</label>
        <select class="form-select" id="newStatus" required>
        <option value="">请选择状态</option>
        <option value="success">成功</option>
        <option value="failed">失败</option>
        <option value="cancelled">取消</option>
        </select>
        </div>
        <div class="mb-3">
        <label class="form-label">备注</label>
        <textarea class="form-control" id="statusRemark" rows="3" placeholder="请输入状态更新备注"></textarea>
        </div>
        <input type="hidden" id="updateRequestId">
        </form>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick="window.paymentRequestManager.confirmUpdateStatus()">
        <span class="update-btn-text">确认更新</span>
        <span class="update-btn-loading" style="display: none;
        ">
        <span class="spinner-border spinner-border-sm me-2"></span>更新中...
        </span>
        </button>
        </div>
        </div>
        </div>
        </div>
        `,window.paymentRequestManager||(window.paymentRequestManager=new PaymentRequestManager),window.paymentRequestManager.initialize()}loadApiManagement(a){var i=this.auth.getUser();i&&"merchant"===i.user_type?this.loadMerchantApiKeyManagement(a):a.innerHTML=`
        <div class="management-page">
        <div class="page-header">
        <h4><i class="bi bi-code-slash me-2"></i>API接口</h4>
        <p class="text-muted">API接口文档和配置</p>
        </div>
        <div class="content-placeholder">
        <div class="placeholder-icon">💻</div>
        <h5>API接口管理</h5>
        <p class="text-muted">此功能正在开发中，敬请期待...</p>
        <div class="feature-list">
        <div class="feature-item">✓ API文档查看</div>
        <div class="feature-item">✓ 接口测试工具</div>
        <div class="feature-item">✓ 密钥管理</div>
        <div class="feature-item">✓ 调用统计</div>
        </div>
        </div>
        </div>
        `}loadMerchantApiKeyManagement(a){a.innerHTML=`
        <div class="api-key-management">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-key me-2"></i>API密钥管理</h4>
        <p class="text-muted mb-0">管理您的API密钥和访问权限</p>
        </div>
        <div class="btn-group">
        <button class="btn btn-outline-warning" onclick="window.merchantApiKeyManager.regenerateKey()">
        <i class="bi bi-arrow-clockwise me-2"></i>重新生成密钥
        </button>
        <button class="btn btn-primary" onclick="window.merchantApiKeyManager.refreshData()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        <!-- API密钥管理容器 -->
        <div id="apiKeyManagementContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载API密钥信息...</div>
        </div>
        </div>
        </div>
        `,window.merchantApiKeyManager||(window.merchantApiKeyManager=new MerchantApiKeyManager),window.merchantApiKeyManager.initialize()}loadApiDocumentation(a){var i=this.auth.getUser();i&&"merchant"===i.user_type?this.loadMerchantApiDocumentation(a):(a.innerHTML=`
        <div class="api-documentation">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-book me-2"></i>API文档</h4>
        <p class="text-muted mb-0">PayPal支付系统完整API接口文档</p>
        </div>
        <div class="btn-group">
        <button class="btn btn-outline-primary" onclick="window.apiDocManager.refreshDocs()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        <button class="btn btn-primary" onclick="window.apiDocManager.downloadDocs()">
        <i class="bi bi-download me-2"></i>下载文档
        </button>
        </div>
        </div>
        <!-- 文档加载容器 -->
        <div id="apiDocsContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载API文档...</div>
        </div>
        </div>
        </div>
        <style>
        .api-documentation {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .api-section {
            margin-bottom: 40px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        .api-section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }
        .api-section-content {
            padding: 20px;
        }
        .api-endpoint {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .method-badge {
            font-size: 0.75rem;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
        }
        .method-post {
            background: #28a745;
            color: white;
        }
        .param-table th {
            background: #f8f9fa;
            border-color: #dee2e6;
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            font-family: 'Courier New',
            monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
        .error-code-table td {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
        }
        .error-code-table th {
            background: #f8f9fa;
            padding: 10px 12px;
            border: 1px solid #dee2e6;
        }
        </style>
        `,window.apiDocManager||(window.apiDocManager=new ApiDocumentationManager),window.apiDocManager.initialize())}loadMerchantDashboard(a){a.innerHTML=`
        <div class="merchant-dashboard">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-speedometer2 me-2"></i>商户仪表板</h4>
        <p class="text-muted mb-0">查看您的业务数据和统计信息</p>
        </div>
        <button class="btn btn-primary" onclick="window.merchantDashboardManager.refreshData()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
        </button>
        </div>
        <!-- 仪表板内容容器 -->
        <div id="merchantDashboardContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载商户数据...</div>
        </div>
        </div>
        </div>
        `,window.merchantDashboardManager||(window.merchantDashboardManager=new MerchantDashboardManager),window.merchantDashboardManager.initialize()}loadCodeGenerator(a){a.innerHTML=`
        <div class="code-generator">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-code-square me-2"></i>代码生成器</h4>
        <p class="text-muted mb-0">生成各种编程语言的API调用示例代码</p>
        </div>
        <div class="btn-group">
        <button class="btn btn-outline-primary" onclick="window.codeGeneratorManager.resetForm()">
        <i class="bi bi-arrow-clockwise me-2"></i>重置
        </button>
        <button class="btn btn-primary" onclick="window.codeGeneratorManager.showLanguageInfo()">
        <i class="bi bi-info-circle me-2"></i>语言说明
        </button>
        </div>
        </div>
        <!-- 代码生成器内容容器 -->
        <div id="codeGeneratorContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载代码生成器...</div>
        </div>
        </div>
        </div>
        `,window.codeGeneratorManager||(window.codeGeneratorManager=new MerchantCodeGeneratorManager),window.codeGeneratorManager.initialize()}loadSignatureTool(a){a.innerHTML=`
        <div class="signature-tool">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-shield-check me-2"></i>签名测试工具</h4>
        <p class="text-muted mb-0">生成和验证API调用签名，测试接口连通性</p>
        </div>
        <div class="btn-group">
        <button class="btn btn-outline-primary" onclick="window.signatureToolManager.clearAll()">
        <i class="bi bi-x-circle me-2"></i>清空
        </button>
        <button class="btn btn-primary" onclick="window.signatureToolManager.showHelp()">
        <i class="bi bi-question-circle me-2"></i>帮助
        </button>
        </div>
        </div>
        <!-- 签名工具内容容器 -->
        <div id="signatureToolContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载签名工具...</div>
        </div>
        </div>
        </div>
        `,window.signatureToolManager||(window.signatureToolManager=new MerchantSignatureToolManager),window.signatureToolManager.initialize()}loadMerchantApiDocumentation(a){a.innerHTML=`
        <div class="merchant-api-docs">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-book me-2"></i>API接口文档</h4>
        <p class="text-muted mb-0">PayPal支付系统商户API完整接口文档</p>
        </div>
        <div class="btn-group">
        <button class="btn btn-outline-primary" onclick="window.merchantApiDocManager.refreshDocs()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        <button class="btn btn-primary" onclick="window.merchantApiDocManager.downloadDocs()">
        <i class="bi bi-download me-2"></i>下载文档
        </button>
        </div>
        </div>
        <!-- 商户API文档容器 -->
        <div id="merchantApiDocsContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载商户API文档...</div>
        </div>
        </div>
        </div>
        `,window.merchantApiDocManager||(window.merchantApiDocManager=new MerchantApiDocumentationManager),window.merchantApiDocManager.initialize()}loadMerchantOrders(a){a.innerHTML=`
        <div class="merchant-orders">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-receipt me-2"></i>我的订单</h4>
        <p class="text-muted mb-0">查看和管理您的所有订单</p>
        </div>
        <div class="btn-group">
        <button class="btn btn-outline-primary" onclick="window.merchantOrderManager.exportOrders()">
        <i class="bi bi-download me-2"></i>导出订单
        </button>
        <button class="btn btn-primary" onclick="window.merchantOrderManager.refreshOrders()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        <!-- 订单管理容器 -->
        <div id="merchantOrdersContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载订单数据...</div>
        </div>
        </div>
        </div>
        `,window.merchantOrderManager||(window.merchantOrderManager=new MerchantOrderManager),window.merchantOrderManager.initialize()}loadMerchantStats(a){a.innerHTML=`
        <div class="merchant-stats">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-graph-up me-2"></i>数据统计</h4>
        <p class="text-muted mb-0">查看详细的业务统计和分析报表</p>
        </div>
        <div class="btn-group">
        <button class="btn btn-outline-primary" onclick="window.merchantStatsManager.exportStats()">
        <i class="bi bi-download me-2"></i>导出报表
        </button>
        <button class="btn btn-primary" onclick="window.merchantStatsManager.refreshStats()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        <!-- 统计数据容器 -->
        <div id="merchantStatsContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载统计数据...</div>
        </div>
        </div>
        </div>
        `,window.merchantStatsManager||(window.merchantStatsManager=new MerchantStatsManager),window.merchantStatsManager.initialize()}loadMerchantProducts(a){a.innerHTML=`
        <div class="merchant-products">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-box-seam me-2"></i>产品管理</h4>
        <p class="text-muted mb-0">管理您的支付产品和配额设置</p>
        </div>
        <div class="btn-group">
        <button class="btn btn-outline-primary" onclick="window.merchantProductManager.exportProducts()">
        <i class="bi bi-download me-2"></i>导出
        </button>
        <button class="btn btn-primary" onclick="window.merchantProductManager.refreshProducts()">
        <i class="bi bi-arrow-clockwise me-2"></i>刷新
        </button>
        </div>
        </div>
        <div id="merchantProductsContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载产品数据...</div>
        </div>
        </div>
        </div>
        `,window.merchantProductManager||(window.merchantProductManager=new MerchantProductManager),window.merchantProductManager.initialize()}loadMerchantConfig(a){a.innerHTML=`
        <div class="merchant-config">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-gear me-2"></i>系统配置</h4>
        <p class="text-muted mb-0">配置API回调地址、IP白名单等系统参数</p>
        </div>
        <button class="btn btn-primary" onclick="window.merchantConfigManager.saveAllConfig()">
        <i class="bi bi-check-circle me-2"></i>保存所有配置
        </button>
        </div>
        <!-- 系统配置容器 -->
        <div id="merchantConfigContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载系统配置...</div>
        </div>
        </div>
        </div>
        `,window.merchantConfigManager||(window.merchantConfigManager=new MerchantConfigManager),window.merchantConfigManager.initialize(),window.merchantConfigManager||(window.merchantConfigManager=new MerchantConfigManager),window.merchantConfigManager.initialize()}loadMerchantProfile(a){a.innerHTML=`
        <div class="merchant-profile">
        <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
        <h4><i class="bi bi-person me-2"></i>个人中心</h4>
        <p class="text-muted mb-0">管理您的账户信息和安全设置</p>
        </div>
        <button class="btn btn-primary" onclick="window.merchantProfileManager.saveProfile()">
        <i class="bi bi-check-circle me-2"></i>保存资料
        </button>
        </div>
        <!-- 个人中心容器 -->
        <div id="merchantProfileContainer">
        <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <div class="mt-3">正在加载个人资料...</div>
        </div>
        </div>
        </div>
        `,window.merchantProfileManager||(window.merchantProfileManager=new MerchantProfileManager),window.merchantProfileManager.initialize()}}"undefined"!=typeof module&&module.exports?module.exports=LoginManager:"undefined"!=typeof window&&(window.LoginManager=LoginManager),console.log("📦 LoginManager 模块加载完成");