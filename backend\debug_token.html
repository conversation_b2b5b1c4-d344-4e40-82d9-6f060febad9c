<!DOCTYPE html>
<html>
<head>
    <title>Token Debug</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Token 调试工具</h1>
    
    <div id="debug-info"></div>
    
    <h3>操作</h3>
    <button onclick="checkToken()">检查Token状态</button>
    <button onclick="setValidToken()">设置有效Token</button>
    <button onclick="testMerchants()">测试商户接口</button>
    <button onclick="clearToken()">清除Token</button>
    
    <h3>结果</h3>
    <pre id="result"></pre>
    
    <script>
        const validToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxLCJleHAiOjE3NTAyMjkzMjR9.n29uq8qLdEmHOSQBYmclet8KzUzJ2WDJrUaM6_J-XT4';
        
        function checkToken() {
            const info = {
                'localStorage.admin_token': localStorage.getItem('admin_token'),
                'localStorage.token': localStorage.getItem('token'),
                'sessionStorage.admin_token': sessionStorage.getItem('admin_token'),
                'sessionStorage.token': sessionStorage.getItem('token'),
                'all_localStorage_keys': Object.keys(localStorage),
                'all_sessionStorage_keys': Object.keys(sessionStorage)
            };
            
            document.getElementById('debug-info').innerHTML = '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
        }
        
        function setValidToken() {
            localStorage.setItem('admin_token', validToken);
            alert('已设置有效token到 localStorage.admin_token');
            checkToken();
        }
        
        function clearToken() {
            localStorage.removeItem('admin_token');
            localStorage.removeItem('token');
            sessionStorage.removeItem('admin_token');
            sessionStorage.removeItem('token');
            alert('已清除所有token');
            checkToken();
        }
        
        async function testMerchants() {
            const token = localStorage.getItem('admin_token');
            
            document.getElementById('result').textContent = '测试中...';
            
            try {
                const response = await fetch('/api/admin.php?action=merchants&page=1&limit=5', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                document.getElementById('result').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('result').textContent = 'Error: ' + error.message;
            }
        }
        
        // 页面加载时检查token状态
        window.onload = function() {
            checkToken();
        }
    </script>
</body>
</html> 