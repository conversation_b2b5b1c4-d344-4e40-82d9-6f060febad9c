<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';

// AES加密工具类
class CryptoUtils {
    private static $key;
    
    public static function init() {
        // 使用与Android相同的密钥生成方式
        $baseKey = "PayPal_Script_2025" . "UNIFIED_KEY";
        self::$key = hash('sha256', $baseKey, true);
    }
    
    public static function encrypt($plaintext) {
        self::init();
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($plaintext, 'AES-256-CBC', self::$key, OPENSSL_RAW_DATA, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    public static function decrypt($ciphertext) {
        self::init();
        $data = base64_decode($ciphertext);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', self::$key, OPENSSL_RAW_DATA, $iv);
    }
}

class ScriptManager {
    private $db;
    private $auth;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->connect();
        $this->auth = new Auth();
    }
    
    public function handleRequest() {
        try {
            $action = isset($_GET['action']) ? $_GET['action'] : '';
            
            switch ($action) {
                case 'upload':
                    return $this->uploadScript();
                case 'list':
                    return $this->listScripts();
                case 'download':
                    return $this->downloadScript();
                case 'delete':
                    return $this->deleteScript();
                case 'update':
                    return $this->updateScript();
                default:
                    return $this->error('无效的操作');
            }
        } catch (Exception $e) {
            return $this->error('服务器错误: ' . $e->getMessage());
        }
    }
    
    private function uploadScript() {
        // 验证管理员权限
        if (!$this->auth->verifyAdminToken()) {
            return $this->error('权限不足');
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        $appType = isset($input['app_type']) ? $input['app_type'] : '';
        $appVersion = isset($input['app_version']) ? $input['app_version'] : '';
        $deviceBrand = isset($input['device_brand']) ? $input['device_brand'] : '';
        $environment = isset($input['environment']) ? $input['environment'] : 'release';
        $scriptContent = isset($input['script_content']) ? $input['script_content'] : '';
        $description = isset($input['description']) ? $input['description'] : '';
        
        if (empty($appType) || empty($appVersion) || empty($deviceBrand) || empty($scriptContent)) {
            return $this->error('缺少必要参数');
        }
        
        // 加密脚本内容
        $encryptedContent = CryptoUtils::encrypt($scriptContent);
        
        // 生成脚本文件名
        $scriptName = "{$appType}_{$appVersion}_{$deviceBrand}_{$environment}.js";
        
        // 检查是否已存在
        $stmt = $this->db->prepare("SELECT id FROM script_versions WHERE filename = ? AND app_type = ? AND app_version = ? AND device_brand = ? AND environment = ?");
        $stmt->execute([$scriptName, $appType, $appVersion, $deviceBrand, $environment]);
        
        if ($stmt->fetch()) {
            // 更新现有脚本
            $stmt = $this->db->prepare("
                UPDATE script_versions 
                SET encrypted_content = ?, description = ?, updated_at = NOW() 
                WHERE filename = ? AND app_type = ? AND app_version = ? AND device_brand = ? AND environment = ?
            ");
            $stmt->execute([$encryptedContent, $description, $scriptName, $appType, $appVersion, $deviceBrand, $environment]);
            $message = '脚本更新成功';
        } else {
            // 插入新脚本
            $stmt = $this->db->prepare("
                INSERT INTO script_versions 
                (filename, app_type, app_version, device_brand, environment, encrypted_content, description, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([$scriptName, $appType, $appVersion, $deviceBrand, $environment, $encryptedContent, $description]);
            $message = '脚本上传成功';
        }
        
        return $this->success($message, ['script_name' => $scriptName]);
    }
    
    private function listScripts() {
        // 验证管理员权限
        if (!$this->auth->verifyAdminToken()) {
            return $this->error('权限不足');
        }
        
        $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
        $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
        $offset = ($page - 1) * $limit;
        
        $appType = isset($_GET['app_type']) ? $_GET['app_type'] : '';
        $deviceBrand = isset($_GET['device_brand']) ? $_GET['device_brand'] : '';
        $environment = isset($_GET['environment']) ? $_GET['environment'] : '';
        
        $whereClause = "WHERE 1=1";
        $params = [];
        
        if (!empty($appType)) {
            $whereClause .= " AND app_type = ?";
            $params[] = $appType;
        }
        
        if (!empty($deviceBrand)) {
            $whereClause .= " AND device_brand = ?";
            $params[] = $deviceBrand;
        }
        
        if (!empty($environment)) {
            $whereClause .= " AND environment = ?";
            $params[] = $environment;
        }
        
        // 获取总数
        $countStmt = $this->db->prepare("SELECT COUNT(*) FROM script_versions $whereClause");
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();
        
        // 获取脚本列表
        $stmt = $this->db->prepare("
            SELECT id, filename, app_type, app_version, device_brand, environment, 
                   description, created_at, updated_at,
                   LENGTH(encrypted_content) as content_size
            FROM script_versions 
            $whereClause 
            ORDER BY created_at DESC 
            LIMIT $limit OFFSET $offset
        ");
        $stmt->execute($params);
        $scripts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 转换字段名以兼容前端
        foreach ($scripts as &$script) {
            $script['script_name'] = $script['filename'];
        }
        
        return $this->success('获取成功', [
            'scripts' => $scripts,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    }
    
    private function downloadScript() {
        // 验证管理员权限
        if (!$this->auth->verifyAdminToken()) {
            return $this->error('权限不足');
        }
        
        $scriptName = isset($_GET['script_name']) ? $_GET['script_name'] : '';
        if (empty($scriptName)) {
            return $this->error('缺少脚本名称参数');
        }
        
        $stmt = $this->db->prepare("SELECT encrypted_content FROM script_versions WHERE filename = ?");
        $stmt->execute([$scriptName]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            return $this->error('脚本不存在');
        }
        
        // 解密脚本内容
        $decryptedContent = CryptoUtils::decrypt($result['encrypted_content']);
        
        return $this->success('获取成功', [
            'script_name' => $scriptName,
            'content' => $decryptedContent
        ]);
    }
    
    private function deleteScript() {
        // 验证管理员权限
        if (!$this->auth->verifyAdminToken()) {
            return $this->error('权限不足');
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $scriptName = isset($input['script_name']) ? $input['script_name'] : '';
        
        if (empty($scriptName)) {
            return $this->error('缺少脚本名称参数');
        }
        
        $stmt = $this->db->prepare("DELETE FROM script_versions WHERE filename = ?");
        $result = $stmt->execute([$scriptName]);
        
        if ($stmt->rowCount() > 0) {
            return $this->success('脚本删除成功');
        } else {
            return $this->error('脚本不存在或删除失败');
        }
    }
    
    private function updateScript() {
        // 验证管理员权限
        if (!$this->auth->verifyAdminToken()) {
            return $this->error('权限不足');
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        
        $scriptName = isset($input['script_name']) ? $input['script_name'] : '';
        $scriptContent = isset($input['script_content']) ? $input['script_content'] : '';
        $description = isset($input['description']) ? $input['description'] : '';
        
        if (empty($scriptName) || empty($scriptContent)) {
            return $this->error('缺少必要参数');
        }
        
        // 加密脚本内容
        $encryptedContent = CryptoUtils::encrypt($scriptContent);
        
        $stmt = $this->db->prepare("
            UPDATE script_versions 
            SET encrypted_content = ?, description = ?, updated_at = NOW() 
            WHERE filename = ?
        ");
        $result = $stmt->execute([$encryptedContent, $description, $scriptName]);
        
        if ($stmt->rowCount() > 0) {
            return $this->success('脚本更新成功');
        } else {
            return $this->error('脚本不存在或更新失败');
        }
    }
    
    private function success($message, $data = null) {
        $response = ['success' => true, 'message' => $message];
        if ($data !== null) {
            $response['data'] = $data;
        }
        return json_encode($response);
    }
    
    private function error($message) {
        return json_encode(array(
            'success' => false,
            'message' => $message
        ));
    }
}

// 处理请求
$manager = new ScriptManager();
echo $manager->handleRequest();
?> 