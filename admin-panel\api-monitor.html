<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API性能监控面板 - PayPal管理系统</title>
    <link rel="stylesheet" href="css/performance-optimized.css">
    <style>
        .monitor-dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        
        .monitor-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .monitor-card h3 {
            margin: 0 0 15px 0;
            color: var(--text-primary);
            font-size: 1.2em;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: var(--primary-color);
            margin: 10px 0;
        }
        
        .metric-label {
            color: var(--text-secondary);
            font-size: 0.9em;
        }
        
        .metric-trend {
            display: flex;
            align-items: center;
            margin-top: 10px;
            font-size: 0.9em;
        }
        
        .trend-up {
            color: #28a745;
        }
        
        .trend-down {
            color: #dc3545;
        }
        
        .trend-stable {
            color: #6c757d;
        }
        
        .chart-container {
            height: 200px;
            margin: 15px 0;
        }
        
        .endpoint-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .endpoint-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .endpoint-method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            color: white;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .method-get { background: #007bff; }
        .method-post { background: #28a745; }
        .method-put { background: #ffc107; color: #000; }
        .method-delete { background: #dc3545; }
        
        .error-list {
            max-height: 250px;
            overflow-y: auto;
        }
        
        .error-item {
            padding: 10px;
            margin: 5px 0;
            border-left: 3px solid #dc3545;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .error-time {
            font-size: 0.8em;
            color: #6c757d;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-healthy { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-critical { background: #dc3545; }
        
        .filter-controls {
            margin: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .filter-row {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .filter-group label {
            font-size: 0.9em;
            color: var(--text-secondary);
        }
        
        .refresh-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-left: auto;
        }
        
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 12px 16px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .alert-danger {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .performance-score {
            text-align: center;
            padding: 20px;
        }
        
        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 2em;
            font-weight: bold;
            color: white;
        }
        
        .score-excellent { background: #28a745; }
        .score-good { background: #17a2b8; }
        .score-fair { background: #ffc107; color: #000; }
        .score-poor { background: #dc3545; }
        
        @media (max-width: 768px) {
            .monitor-dashboard {
                grid-template-columns: 1fr;
                padding: 10px;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .refresh-controls {
                margin-left: 0;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <header class="header">
        <div class="header-content">
            <h1>API性能监控面板</h1>
            <div class="header-actions">
                <span id="lastUpdate">最后更新: --</span>
                <button class="btn-primary" onclick="exportReport()">导出报告</button>
                <button class="btn-secondary" onclick="goBack()">返回</button>
            </div>
        </div>
    </header>

    <div class="filter-controls">
        <div class="filter-row">
            <div class="filter-group">
                <label>时间范围</label>
                <select id="timeRange">
                    <option value="1h">最近1小时</option>
                    <option value="6h">最近6小时</option>
                    <option value="24h" selected>最近24小时</option>
                    <option value="7d">最近7天</option>
                    <option value="30d">最近30天</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label>端点筛选</label>
                <select id="endpointFilter">
                    <option value="">所有端点</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label>状态码</label>
                <select id="statusFilter">
                    <option value="">所有状态</option>
                    <option value="2xx">成功 (2xx)</option>
                    <option value="4xx">客户端错误 (4xx)</option>
                    <option value="5xx">服务器错误 (5xx)</option>
                </select>
            </div>
            
            <div class="refresh-controls">
                <div class="auto-refresh">
                    <input type="checkbox" id="autoRefresh" checked>
                    <label for="autoRefresh">自动刷新</label>
                </div>
                <button class="btn-primary" onclick="refreshData()">立即刷新</button>
            </div>
        </div>
    </div>

    <div class="monitor-dashboard">
        <!-- 系统健康状态 -->
        <div class="monitor-card">
            <h3>系统健康状态</h3>
            <div class="performance-score">
                <div class="score-circle" id="healthScore">--</div>
                <div class="metric-label">综合健康评分</div>
                <div id="healthStatus" class="metric-trend">
                    <span class="status-indicator status-healthy"></span>
                    系统运行正常
                </div>
            </div>
        </div>

        <!-- API请求统计 -->
        <div class="monitor-card">
            <h3>API请求统计</h3>
            <div class="metric-value" id="totalRequests">--</div>
            <div class="metric-label">总请求数</div>
            <div class="metric-trend" id="requestsTrend">
                <span>📈</span> 与昨日相比
            </div>
            <div class="chart-container">
                <canvas id="requestsChart"></canvas>
            </div>
        </div>

        <!-- 响应时间 -->
        <div class="monitor-card">
            <h3>平均响应时间</h3>
            <div class="metric-value" id="avgResponseTime">--</div>
            <div class="metric-label">毫秒</div>
            <div class="metric-trend" id="responseTimeTrend">
                <span>⏱️</span> 性能指标
            </div>
            <div class="chart-container">
                <canvas id="responseTimeChart"></canvas>
            </div>
        </div>

        <!-- 错误率 -->
        <div class="monitor-card">
            <h3>错误率</h3>
            <div class="metric-value" id="errorRate">--%</div>
            <div class="metric-label">错误请求比例</div>
            <div class="metric-trend" id="errorTrend">
                <span>🚨</span> 错误统计
            </div>
            <div class="chart-container">
                <canvas id="errorChart"></canvas>
            </div>
        </div>

        <!-- 热门端点 -->
        <div class="monitor-card">
            <h3>热门API端点</h3>
            <div class="endpoint-list" id="popularEndpoints">
                <div class="endpoint-item">
                    <span>
                        <span class="endpoint-method method-get">GET</span>
                        /admin.php?action=dashboard
                    </span>
                    <span>1,234次</span>
                </div>
            </div>
        </div>

        <!-- 慢查询 -->
        <div class="monitor-card">
            <h3>慢查询监控</h3>
            <div class="metric-value" id="slowQueries">--</div>
            <div class="metric-label">响应时间>1秒的请求</div>
            <div class="endpoint-list" id="slowEndpoints">
                <!-- 动态加载 -->
            </div>
        </div>

        <!-- 缓存命中率 -->
        <div class="monitor-card">
            <h3>缓存性能</h3>
            <div class="metric-value" id="cacheHitRate">--%</div>
            <div class="metric-label">缓存命中率</div>
            <div class="metric-trend" id="cacheTrend">
                <span>💾</span> 缓存效率
            </div>
            <div class="chart-container">
                <canvas id="cacheChart"></canvas>
            </div>
        </div>

        <!-- 并发用户 -->
        <div class="monitor-card">
            <h3>并发统计</h3>
            <div class="metric-value" id="concurrentUsers">--</div>
            <div class="metric-label">当前活跃用户</div>
            <div class="metric-trend" id="usersTrend">
                <span>👥</span> 用户活跃度
            </div>
        </div>

        <!-- 最近错误 -->
        <div class="monitor-card">
            <h3>最近错误</h3>
            <div class="error-list" id="recentErrors">
                <!-- 动态加载 -->
            </div>
        </div>

        <!-- 系统资源 -->
        <div class="monitor-card">
            <h3>系统资源</h3>
            <div style="margin-bottom: 15px;">
                <div class="metric-label">内存使用</div>
                <div class="metric-value" style="font-size: 1.5em;" id="memoryUsage">--MB</div>
            </div>
            <div style="margin-bottom: 15px;">
                <div class="metric-label">CPU使用率</div>
                <div class="metric-value" style="font-size: 1.5em;" id="cpuUsage">--%</div>
            </div>
            <div>
                <div class="metric-label">磁盘使用</div>
                <div class="metric-value" style="font-size: 1.5em;" id="diskUsage">--%</div>
            </div>
        </div>

        <!-- API版本统计 -->
        <div class="monitor-card">
            <h3>API版本使用</h3>
            <div class="chart-container">
                <canvas id="versionChart"></canvas>
            </div>
            <div id="versionStats">
                <!-- 动态加载 -->
            </div>
        </div>

        <!-- 地理分布 -->
        <div class="monitor-card">
            <h3>请求地理分布</h3>
            <div class="chart-container">
                <canvas id="geoChart"></canvas>
            </div>
            <div id="geoStats">
                <!-- 动态加载 -->
            </div>
        </div>
    </div>

    <!-- Chart.js 加载器 -->
    <script src="js/libs/chart-loader.js"></script>
    <script src="js/modules/core.js"></script>
    
    <script>
        class ApiMonitor {
            constructor() {
                this.charts = {};
                this.refreshInterval = null;
                this.isAutoRefresh = true;
                
                this.init();
            }
            
            init() {
                this.setupEventListeners();
                this.initCharts();
                this.loadData();
                this.startAutoRefresh();
            }
            
            setupEventListeners() {
                // 筛选控件事件
                document.getElementById('timeRange').addEventListener('change', () => {
                    this.loadData();
                });
                
                document.getElementById('endpointFilter').addEventListener('change', () => {
                    this.loadData();
                });
                
                document.getElementById('statusFilter').addEventListener('change', () => {
                    this.loadData();
                });
                
                // 自动刷新控制
                document.getElementById('autoRefresh').addEventListener('change', (e) => {
                    this.isAutoRefresh = e.target.checked;
                    if (this.isAutoRefresh) {
                        this.startAutoRefresh();
                    } else {
                        this.stopAutoRefresh();
                    }
                });
            }
            
            initCharts() {
                // 请求量图表
                this.charts.requests = new Chart(document.getElementById('requestsChart'), {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '请求数',
                            data: [],
                            borderColor: '#007bff',
                            backgroundColor: 'rgba(0, 123, 255, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                
                // 响应时间图表
                this.charts.responseTime = new Chart(document.getElementById('responseTimeChart'), {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '平均响应时间(ms)',
                            data: [],
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                
                // 错误率图表
                this.charts.error = new Chart(document.getElementById('errorChart'), {
                    type: 'bar',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '错误数',
                            data: [],
                            backgroundColor: '#dc3545'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                
                // 缓存命中率图表
                this.charts.cache = new Chart(document.getElementById('cacheChart'), {
                    type: 'doughnut',
                    data: {
                        labels: ['命中', '未命中'],
                        datasets: [{
                            data: [0, 0],
                            backgroundColor: ['#28a745', '#dc3545']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                
                // API版本使用图表
                this.charts.version = new Chart(document.getElementById('versionChart'), {
                    type: 'pie',
                    data: {
                        labels: [],
                        datasets: [{
                            data: [],
                            backgroundColor: [
                                '#007bff',
                                '#28a745',
                                '#ffc107',
                                '#dc3545',
                                '#6f42c1'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                
                // 地理分布图表
                this.charts.geo = new Chart(document.getElementById('geoChart'), {
                    type: 'bar',
                    data: {
                        labels: [],
                        datasets: [{
                            label: '请求数',
                            data: [],
                            backgroundColor: '#17a2b8'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y'
                    }
                });
            }
            
            async loadData() {
                this.showLoading();
                
                try {
                    const timeRange = document.getElementById('timeRange').value;
                    const endpoint = document.getElementById('endpointFilter').value;
                    const status = document.getElementById('statusFilter').value;
                    
                    const params = new URLSearchParams({
                        action: 'performance_monitor',
                        time_range: timeRange,
                        endpoint: endpoint,
                        status: status
                    });
                    
                    const response = await fetch(`/api/admin.php?${params}`, {
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem(CONFIG.TOKEN_KEY)}`
                        }
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        this.updateDashboard(data.data);
                        this.updateLastUpdateTime();
                    } else {
                        this.showError('加载数据失败: ' + data.message);
                    }
                    
                } catch (error) {
                    console.error('加载数据错误:', error);
                    this.showError('网络错误，请稍后重试');
                } finally {
                    this.hideLoading();
                }
            }
            
            updateDashboard(data) {
                // 更新基本指标
                this.updateMetrics(data.metrics);
                
                // 更新图表
                this.updateCharts(data.charts);
                
                // 更新列表
                this.updateLists(data.lists);
                
                // 更新健康状态
                this.updateHealthStatus(data.health);
            }
            
            updateMetrics(metrics) {
                document.getElementById('totalRequests').textContent = 
                    this.formatNumber(metrics.total_requests || 0);
                    
                document.getElementById('avgResponseTime').textContent = 
                    Math.round(metrics.avg_response_time || 0);
                    
                document.getElementById('errorRate').textContent = 
                    (metrics.error_rate || 0).toFixed(1) + '%';
                    
                document.getElementById('slowQueries').textContent = 
                    metrics.slow_queries || 0;
                    
                document.getElementById('cacheHitRate').textContent = 
                    (metrics.cache_hit_rate || 0).toFixed(1) + '%';
                    
                document.getElementById('concurrentUsers').textContent = 
                    metrics.concurrent_users || 0;
                    
                document.getElementById('memoryUsage').textContent = 
                    Math.round(metrics.memory_usage_mb || 0) + 'MB';
                    
                document.getElementById('cpuUsage').textContent = 
                    (metrics.cpu_usage || 0).toFixed(1) + '%';
                    
                document.getElementById('diskUsage').textContent = 
                    (metrics.disk_usage || 0).toFixed(1) + '%';
                
                // 更新趋势指标
                this.updateTrends(metrics.trends || {});
            }
            
            updateTrends(trends) {
                const trendElements = {
                    'requestsTrend': trends.requests,
                    'responseTimeTrend': trends.response_time,
                    'errorTrend': trends.errors,
                    'cacheTrend': trends.cache,
                    'usersTrend': trends.users
                };
                
                Object.entries(trendElements).forEach(([id, trend]) => {
                    const element = document.getElementById(id);
                    if (element && trend) {
                        const direction = trend.direction || 'stable';
                        const value = trend.value || 0;
                        const icon = this.getTrendIcon(direction);
                        
                        element.innerHTML = `
                            <span class="trend-${direction}">${icon}</span>
                            ${direction === 'up' ? '增长' : direction === 'down' ? '下降' : '稳定'} ${Math.abs(value)}%
                        `;
                    }
                });
            }
            
            getTrendIcon(direction) {
                const icons = {
                    'up': '📈',
                    'down': '📉',
                    'stable': '➡️'
                };
                return icons[direction] || '➡️';
            }
            
            updateCharts(charts) {
                // 更新请求量图表
                if (charts.requests) {
                    this.charts.requests.data.labels = charts.requests.labels;
                    this.charts.requests.data.datasets[0].data = charts.requests.data;
                    this.charts.requests.update();
                }
                
                // 更新响应时间图表
                if (charts.response_time) {
                    this.charts.responseTime.data.labels = charts.response_time.labels;
                    this.charts.responseTime.data.datasets[0].data = charts.response_time.data;
                    this.charts.responseTime.update();
                }
                
                // 更新错误图表
                if (charts.errors) {
                    this.charts.error.data.labels = charts.errors.labels;
                    this.charts.error.data.datasets[0].data = charts.errors.data;
                    this.charts.error.update();
                }
                
                // 更新缓存图表
                if (charts.cache) {
                    this.charts.cache.data.datasets[0].data = [
                        charts.cache.hits,
                        charts.cache.misses
                    ];
                    this.charts.cache.update();
                }
                
                // 更新版本图表
                if (charts.versions) {
                    this.charts.version.data.labels = charts.versions.labels;
                    this.charts.version.data.datasets[0].data = charts.versions.data;
                    this.charts.version.update();
                }
                
                // 更新地理分布图表
                if (charts.geo) {
                    this.charts.geo.data.labels = charts.geo.labels;
                    this.charts.geo.data.datasets[0].data = charts.geo.data;
                    this.charts.geo.update();
                }
            }
            
            updateLists(lists) {
                // 更新热门端点
                if (lists.popular_endpoints) {
                    this.updatePopularEndpoints(lists.popular_endpoints);
                }
                
                // 更新慢查询
                if (lists.slow_endpoints) {
                    this.updateSlowEndpoints(lists.slow_endpoints);
                }
                
                // 更新最近错误
                if (lists.recent_errors) {
                    this.updateRecentErrors(lists.recent_errors);
                }
                
                // 更新版本统计
                if (lists.version_stats) {
                    this.updateVersionStats(lists.version_stats);
                }
                
                // 更新地理统计
                if (lists.geo_stats) {
                    this.updateGeoStats(lists.geo_stats);
                }
            }
            
            updatePopularEndpoints(endpoints) {
                const container = document.getElementById('popularEndpoints');
                container.innerHTML = '';
                
                endpoints.forEach(endpoint => {
                    const item = document.createElement('div');
                    item.className = 'endpoint-item';
                    item.innerHTML = `
                        <span>
                            <span class="endpoint-method method-${endpoint.method.toLowerCase()}">${endpoint.method}</span>
                            ${endpoint.endpoint}
                        </span>
                        <span>${this.formatNumber(endpoint.count)}次</span>
                    `;
                    container.appendChild(item);
                });
            }
            
            updateSlowEndpoints(endpoints) {
                const container = document.getElementById('slowEndpoints');
                container.innerHTML = '';
                
                endpoints.forEach(endpoint => {
                    const item = document.createElement('div');
                    item.className = 'endpoint-item';
                    item.innerHTML = `
                        <span>
                            <span class="endpoint-method method-${endpoint.method.toLowerCase()}">${endpoint.method}</span>
                            ${endpoint.endpoint}
                        </span>
                        <span>${Math.round(endpoint.avg_time)}ms</span>
                    `;
                    container.appendChild(item);
                });
            }
            
            updateRecentErrors(errors) {
                const container = document.getElementById('recentErrors');
                container.innerHTML = '';
                
                errors.forEach(error => {
                    const item = document.createElement('div');
                    item.className = 'error-item';
                    item.innerHTML = `
                        <div><strong>${error.message}</strong></div>
                        <div>端点: ${error.endpoint}</div>
                        <div class="error-time">${error.time}</div>
                    `;
                    container.appendChild(item);
                });
            }
            
            updateVersionStats(stats) {
                const container = document.getElementById('versionStats');
                container.innerHTML = '';
                
                stats.forEach(stat => {
                    const item = document.createElement('div');
                    item.className = 'endpoint-item';
                    item.innerHTML = `
                        <span>版本 ${stat.version}</span>
                        <span>${this.formatNumber(stat.requests)}次 (${stat.percentage}%)</span>
                    `;
                    container.appendChild(item);
                });
            }
            
            updateGeoStats(stats) {
                const container = document.getElementById('geoStats');
                container.innerHTML = '';
                
                stats.forEach(stat => {
                    const item = document.createElement('div');
                    item.className = 'endpoint-item';
                    item.innerHTML = `
                        <span>${stat.country || stat.region}</span>
                        <span>${this.formatNumber(stat.requests)}次</span>
                    `;
                    container.appendChild(item);
                });
            }
            
            updateHealthStatus(health) {
                const scoreElement = document.getElementById('healthScore');
                const statusElement = document.getElementById('healthStatus');
                
                const score = health.score || 0;
                scoreElement.textContent = score;
                
                // 根据分数设置颜色
                scoreElement.className = 'score-circle ';
                if (score >= 90) {
                    scoreElement.className += 'score-excellent';
                } else if (score >= 75) {
                    scoreElement.className += 'score-good';
                } else if (score >= 60) {
                    scoreElement.className += 'score-fair';
                } else {
                    scoreElement.className += 'score-poor';
                }
                
                // 更新状态文本
                const statusText = health.status || '未知状态';
                const statusClass = this.getStatusClass(health.level || 'unknown');
                
                statusElement.innerHTML = `
                    <span class="status-indicator ${statusClass}"></span>
                    ${statusText}
                `;
            }
            
            getStatusClass(level) {
                const classes = {
                    'healthy': 'status-healthy',
                    'warning': 'status-warning',
                    'critical': 'status-critical'
                };
                return classes[level] || 'status-warning';
            }
            
            startAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                }
                
                this.refreshInterval = setInterval(() => {
                    if (this.isAutoRefresh) {
                        this.loadData();
                    }
                }, 30000); // 30秒刷新一次
            }
            
            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                }
            }
            
            updateLastUpdateTime() {
                document.getElementById('lastUpdate').textContent = 
                    '最后更新: ' + new Date().toLocaleTimeString();
            }
            
            formatNumber(num) {
                if (num >= 1000000) {
                    return (num / 1000000).toFixed(1) + 'M';
                } else if (num >= 1000) {
                    return (num / 1000).toFixed(1) + 'K';
                }
                return num.toString();
            }
            
            showLoading() {
                document.getElementById('loadingOverlay').style.display = 'flex';
            }
            
            hideLoading() {
                document.getElementById('loadingOverlay').style.display = 'none';
            }
            
            showError(message) {
                // 创建错误提示
                const alert = document.createElement('div');
                alert.className = 'alert alert-danger';
                alert.textContent = message;
                
                document.body.insertBefore(alert, document.body.firstChild);
                
                // 3秒后自动移除
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 3000);
            }
        }
        
        // 全局函数
        function refreshData() {
            window.apiMonitor.loadData();
        }
        
        function exportReport() {
            // 导出性能报告
            const params = new URLSearchParams({
                action: 'export_performance_report',
                time_range: document.getElementById('timeRange').value,
                format: 'pdf'
            });
            
                            window.open(`/api/admin.php?${params}`, '_blank');
        }
        
        function goBack() {
            window.history.back();
        }
        
        // 初始化监控面板
        document.addEventListener('DOMContentLoaded', () => {
            window.apiMonitor = new ApiMonitor();
        });
        
        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            if (window.apiMonitor) {
                window.apiMonitor.stopAutoRefresh();
            }
        });
    </script>
</body>
</html> 