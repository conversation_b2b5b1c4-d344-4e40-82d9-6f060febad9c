<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal管理系统 - 模块化测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 配置常量 -->
    <script>
        // 全局配置
        const CONFIG = {
            API_BASE_URL: '/api',
            TOKEN_KEY: 'admin_token',
            USER_KEY: 'admin_user',
            DEBUG: true
        };
        
        console.log('🔧 配置加载完成:', CONFIG);
        
        // 清理错误的localStorage数据
        if (localStorage.getItem('paypal_admin_token')) {
            console.log('🧹 清理错误的localStorage数据...');
            localStorage.removeItem('paypal_admin_token');
            localStorage.removeItem('paypal_admin_user');
            console.log('✅ 错误数据已清理');
        }
    </script>
    
    <style>
        /* 基础样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 0;
        }
        
        .app-container {
            display: none !important;
            visibility: hidden !important;
        }
        
        /* 登录界面样式 */
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-title {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .login-subtitle {
            color: #6b7280;
            font-size: 16px;
        }
        
        .form-control {
            border-radius: 12px;
            padding: 15px;
            border: 2px solid #e5e7eb;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            z-index: 9999;
            align-items: center;
            justify-content: center;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f4f6;
            border-top: 5px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 调试面板样式 */
        .debug-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-width: 300px;
            z-index: 1001;
            font-size: 12px;
            border-left: 4px solid #3b82f6;
        }
        
        .debug-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .debug-item {
            margin-bottom: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .debug-label {
            color: #6b7280;
        }
        
        .debug-value {
            color: #059669;
            font-weight: 500;
        }
        
        .debug-error {
            color: #dc2626;
        }
    </style>
</head>
<body>
    <!-- 调试面板 -->
    <div class="debug-panel" id="debugPanel">
        <div class="debug-title">
            <i class="bi bi-bug"></i>
            系统状态
        </div>
        <div class="debug-item">
            <span class="debug-label">初始化状态:</span>
            <span class="debug-value" id="debugInitStatus">加载中...</span>
        </div>
        <div class="debug-item">
            <span class="debug-label">认证状态:</span>
            <span class="debug-value" id="debugAuthStatus">检查中...</span>
        </div>
        <div class="debug-item">
            <span class="debug-label">已加载模块:</span>
            <span class="debug-value" id="debugModuleCount">0</span>
        </div>
        <div class="debug-item">
            <span class="debug-label">当前页面:</span>
            <span class="debug-value" id="debugCurrentPage">-</span>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading">
        <div class="loading-spinner"></div>
    </div>

    <!-- 登录界面 -->
    <div id="loginContainer" class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1 class="login-title">PayPal管理系统</h1>
                <p class="login-subtitle">模块化架构测试版</p>
            </div>
            
            <div id="alertContainer"></div>
            
            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="password" required>
                </div>
                <button type="submit" class="btn btn-login">
                    <i class="bi bi-box-arrow-in-right me-2"></i>登录
                </button>
            </form>
            
            <div class="mt-3 text-center">
                <small class="text-muted">
                    测试账号: admin / password
                </small>
            </div>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="appContainer" class="app-container">
        <!-- 主界面内容将由UIManager动态生成 -->
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 加载应用初始化器 -->
    <script src="/js/modules/core/app-initializer.js"></script>
    
    <script>
        // 调试面板更新函数
        function updateDebugPanel() {
            if (!window.appInitializer) return;
            
            const status = window.appInitializer.getAppStatus();
            
            document.getElementById('debugInitStatus').textContent = 
                status.initialized ? '已完成' : '进行中';
            document.getElementById('debugInitStatus').className = 
                status.initialized ? 'debug-value' : 'debug-error';
                
            document.getElementById('debugAuthStatus').textContent = 
                status.authenticated ? '已登录' : '未登录';
            document.getElementById('debugAuthStatus').className = 
                status.authenticated ? 'debug-value' : 'debug-error';
                
            document.getElementById('debugModuleCount').textContent = 
                status.loadedModules.length;
                
            document.getElementById('debugCurrentPage').textContent = 
                status.currentPage || '-';
        }
        
        // 每秒更新调试面板
        setInterval(updateDebugPanel, 1000);
        
        // 监听应用初始化完成事件
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎯 DOM加载完成，等待应用初始化...');
            
            // 延迟启动调试面板更新
            setTimeout(updateDebugPanel, 500);
        });
        
        // 全局错误监听
        window.addEventListener('error', (event) => {
            console.error('🚨 页面错误:', event.error);
            
            // 更新调试面板显示错误
            const debugPanel = document.getElementById('debugPanel');
            if (debugPanel) {
                debugPanel.innerHTML += `
                    <div class="debug-item">
                        <span class="debug-label">错误:</span>
                        <span class="debug-error">${event.error.message}</span>
                    </div>
                `;
            }
        });
        
        // 测试函数
        window.testModularSystem = function() {
            console.log('🧪 开始测试模块化系统...');
            
            if (!window.appInitializer) {
                console.error('❌ 应用初始化器未找到');
                return;
            }
            
            const status = window.appInitializer.getAppStatus();
            console.log('📊 应用状态:', status);
            
            // 测试模块加载
            if (window.moduleLoader) {
                console.log('📦 模块加载器可用');
                console.log('已加载模块:', window.moduleLoader.getLoadedModules());
                console.log('性能指标:', window.moduleLoader.getPerformanceMetrics());
            }
            
            // 测试认证系统
            if (window.authManager) {
                console.log('🔐 认证管理器可用');
                console.log('认证状态:', window.authManager.isAuthenticated());
                console.log('当前用户:', window.authManager.getUser());
            }
            
            // 测试UI管理器
            if (window.uiManager) {
                console.log('🎨 UI管理器可用');
                console.log('当前页面:', window.uiManager.getCurrentPage());
                console.log('界面初始化状态:', window.uiManager.isInterfaceInitialized());
            }
            
            console.log('✅ 模块化系统测试完成');
        };
        
        // 在控制台提供测试命令
        console.log('🎮 测试命令可用:');
        console.log('  testModularSystem() - 测试模块化系统');
        console.log('  appInitializer.getAppStatus() - 获取应用状态');
        console.log('  appInitializer.restart() - 重启应用');
    </script>
</body>
</html> 