/**
 * API密钥管理器
 * 从ApiModule.js提取的API密钥相关功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class ApiKeyManager {
    constructor() {
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.apiKeys = {};
        
        console.log('✅ ApiKeyManager initialized');
    }

    /**
     * 统一渲染方法 - 路由系统调用
     */
    async render(container, params = {}) {
        try {
            console.log('🎯 ApiKeyManager渲染 - 参数:', params);
            
            // 显示加载状态
            container.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><div class="mt-2">正在加载API设置...</div></div>';
            
            // 生成简单的API设置页面
            container.innerHTML = this.generateSimpleApiSettingsContent();
            
            // 设置全局引用以便HTML中的onclick调用
            window.apiModule = this;
            
            // 加载数据
            setTimeout(() => {
                this.loadMerchantApiKeyData();
                this.loadIpWhitelist();
            }, 100);
            
        } catch (error) {
            console.error('❌ ApiKeyManager渲染失败:', error);
            container.innerHTML = `<div class="alert alert-danger">API设置加载失败: ${error.message}</div>`;
        }
    }

    /**
     * 生成简单的API设置页面内容
     */
    generateSimpleApiSettingsContent() {
        return `
            <div class="api-settings-container">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h4><i class="bi bi-gear me-2"></i>API设置</h4>
                        <p class="text-muted mb-0">管理您的API密钥、IP白名单和回调配置</p>
                    </div>
                </div>

                <div class="row">
                    <!-- 左侧：API密钥管理 -->
                    <div class="col-lg-8">
                        <!-- API密钥信息 -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-key me-2"></i>API密钥管理
                                </h6>
                                <button class="btn btn-sm btn-outline-primary" onclick="apiModule.regenerateApiKey()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>重新生成
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">商户ID</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="merchantId" readonly>
                                            <button class="btn btn-outline-secondary" type="button" onclick="apiModule.copyToClipboard('merchantId')">
                                                <i class="bi bi-clipboard"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">API密钥</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="apiKey" readonly>
                                            <button class="btn btn-outline-secondary" type="button" onclick="apiModule.toggleKeyVisibility('apiKey', 'toggleApiKey')">
                                                <i class="bi bi-eye" id="toggleApiKey"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary" type="button" onclick="apiModule.copyToClipboard('apiKey')">
                                                <i class="bi bi-clipboard"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>安全提示：</strong>请妥善保管您的API密钥，不要在前端代码中暴露密钥信息。
                                </div>
                            </div>
                        </div>

                        <!-- 回调地址设置 -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-arrow-left-right me-2"></i>回调地址设置
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">回调地址</label>
                                    <div class="input-group">
                                        <input type="url" class="form-control" id="callbackUrl" placeholder="https://your-domain.com/callback">
                                        <button class="btn btn-outline-primary" onclick="apiModule.updateCallbackUrl()">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">订单状态变化时将向此地址发送通知</small>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-info btn-sm" onclick="apiModule.testCallback()">
                                        <i class="bi bi-play me-1"></i>测试回调
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="apiModule.viewCallbackLogs()">
                                        <i class="bi bi-list me-1"></i>回调日志
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- IP白名单管理 -->
                        <div class="card shadow">
                            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-shield-check me-2"></i>IP白名单管理
                                </h6>
                                <button class="btn btn-sm btn-outline-primary" onclick="apiModule.addIpWhitelist()">
                                    <i class="bi bi-plus me-1"></i>添加IP
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="ipWhitelistContainer">
                                    <div class="text-center py-3">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                        <div class="mt-2 text-muted">正在加载IP白名单...</div>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        支持单个IP地址（如：***********）或CIDR网段（如：***********/24）
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：统计信息和快速操作 -->
                    <div class="col-lg-4">
                        <!-- 调用统计 -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-graph-up me-2"></i>调用统计
                                </h6>
                            </div>
                            <div class="card-body">
                                <style>
                                    .stat-item {
                                        padding: 10px 0;
                                    }
                                    .stat-value {
                                        font-size: 1.5rem;
                                        font-weight: bold;
                                        color: #495057;
                                    }
                                    .stat-label {
                                        font-size: 0.875rem;
                                        color: #6c757d;
                                        margin-top: 5px;
                                    }
                                </style>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value" id="todayCallsCount">-</div>
                                            <div class="stat-label">今日调用</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value" id="monthCallsCount">-</div>
                                            <div class="stat-label">本月调用</div>
                                        </div>
                                    </div>
                                    <div class="col-6 mt-3">
                                        <div class="stat-item">
                                            <div class="stat-value" id="totalCallsCount">-</div>
                                            <div class="stat-label">总调用量</div>
                                        </div>
                                    </div>
                                    <div class="col-6 mt-3">
                                        <div class="stat-item">
                                            <div class="stat-value text-success" id="successRateCount">-</div>
                                            <div class="stat-label">成功率</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 快速操作 -->
                        <div class="card shadow">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-lightning me-2"></i>快速操作
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary btn-sm" onclick="apiModule.downloadPostman()">
                                        <i class="bi bi-download me-1"></i>下载Postman集合
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="apiModule.openSignatureTool()">
                                        <i class="bi bi-shield-check me-1"></i>签名工具
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="apiModule.openApiTester()">
                                        <i class="bi bi-play-circle me-1"></i>接口测试
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="apiModule.openCodeGenerator()">
                                        <i class="bi bi-code me-1"></i>代码生成器
                                    </button>
                                </div>
                                
                                <hr class="my-3">
                                
                                <div class="text-center">
                                    <h6 class="text-muted">开发文档</h6>
                                    <div class="d-grid gap-1">
                                        <a href="#" class="btn btn-sm btn-outline-secondary">快速入门</a>
                                        <a href="#" class="btn btn-sm btn-outline-secondary">API参考</a>
                                        <a href="#" class="btn btn-sm btn-outline-secondary">SDK下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 加载IP白名单数据
     */
    async loadIpWhitelist() {
        try {
            console.log('🔄 开始加载IP白名单...');
            
            const response = await fetch('/api/merchant/tools.php?action=get_ip_whitelist', {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                const ipList = data.data || [];
                this.updateIpWhitelistDisplay(ipList);
            } else {
                console.error('❌ IP白名单加载失败:', data.message);
                this.showError('IP白名单加载失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 加载IP白名单异常:', error);
            this.showError('加载IP白名单时发生错误: ' + error.message);
        }
    }

    /**
     * 更新IP白名单显示
     */
    updateIpWhitelistDisplay(ipList) {
        const container = document.getElementById('ipWhitelistContainer');
        if (!container) return;

        if (!ipList || ipList.length === 0) {
            container.innerHTML = `
                <div class="text-center py-3 text-muted">
                    <i class="bi bi-shield-x" style="font-size: 2rem;"></i>
                    <p class="mt-2 mb-0">暂无IP白名单</p>
                    <small>点击"添加IP"按钮添加允许访问的IP地址</small>
                </div>
            `;
            return;
        }

        const ipListHtml = ipList.map((ip, index) => `
            <div class="d-flex justify-content-between align-items-center py-2 ${index > 0 ? 'border-top' : ''}">
                <div>
                    <span class="fw-bold">${ip.ip_address || ip}</span>
                    <small class="text-muted d-block">添加时间: ${ip.created_at || '未知'}</small>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="apiModule.removeIpFromWhitelist(${index}, '${ip.ip_address || ip}')">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `).join('');

        container.innerHTML = ipListHtml;
    }

    /**
     * 添加IP到白名单
     */
    async addIpWhitelist() {
        const ip = prompt('请输入IP地址（支持单个IP或CIDR网段）：');
        if (!ip) return;

        // 简单的IP格式验证
        const ipPattern = /^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/;
        if (!ipPattern.test(ip)) {
            this.showError('IP地址格式不正确');
            return;
        }

        try {
            const response = await fetch('/api/merchant/tools.php?action=add_ip_whitelist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify({ ip_address: ip })
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('IP地址已添加到白名单');
                this.loadIpWhitelist(); // 重新加载列表
            } else {
                this.showError('添加失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 添加IP白名单失败:', error);
            this.showError('添加IP失败: ' + error.message);
        }
    }

    /**
     * 从白名单移除IP
     */
    async removeIpFromWhitelist(index, ip) {
        if (!confirm(`确定要从白名单中移除 ${ip} 吗？`)) return;

        try {
            const response = await fetch('/api/merchant/tools.php?action=remove_ip_whitelist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify({ index: index })
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('IP地址已从白名单移除');
                this.loadIpWhitelist(); // 重新加载列表
            } else {
                this.showError('移除失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 移除IP白名单失败:', error);
            this.showError('移除IP失败: ' + error.message);
        }
    }

    /**
     * 更新回调地址
     */
    async updateCallbackUrl() {
        const callbackUrl = document.getElementById('callbackUrl')?.value;
        if (!callbackUrl) {
            this.showError('请输入回调地址');
            return;
        }

        // 简单的URL格式验证
        try {
            new URL(callbackUrl);
        } catch {
            this.showError('回调地址格式不正确');
            return;
        }

        try {
            const response = await fetch('/api/merchant/tools.php?action=update_callback_url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify({ callback_url: callbackUrl })
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('回调地址更新成功');
            } else {
                this.showError('更新失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 更新回调地址失败:', error);
            this.showError('更新回调地址失败: ' + error.message);
        }
    }

    /**
     * 测试回调
     */
    async testCallback() {
        const callbackUrl = document.getElementById('callbackUrl')?.value;
        if (!callbackUrl) {
            this.showError('请先设置回调地址');
            return;
        }

        this.showSuccess('正在发送测试回调，请检查您的服务器日志...');
        // 这里可以实现实际的回调测试逻辑
    }

    /**
     * 查看回调日志
     */
    viewCallbackLogs() {
        this.showSuccess('回调日志功能开发中...');
        // 这里可以实现回调日志查看功能
    }

    /**
     * 打开代码生成器
     */
    openCodeGenerator() {
        if (window.routerManager) {
            window.routerManager.navigateToRoute('/code-generator');
        } else {
            this.showError('路由管理器未初始化');
        }
    }

    /**
     * 加载商户API密钥数据
     */
    async loadMerchantApiKeyData() {
        try {
            console.log('🔄 开始加载商户API密钥数据...');
            
            const response = await fetch('/api/merchant/tools.php?action=get_key_info', {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                const keyInfo = data.data || data;
                console.log('✅ API密钥数据加载成功:', keyInfo);
                this.updateMerchantApiKeyDisplay(keyInfo);
            } else {
                console.error('❌ API密钥数据加载失败:', data.message);
                this.showError('API密钥数据加载失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 加载API密钥数据异常:', error);
            this.showError('加载API密钥数据时发生错误: ' + error.message);
        }
    }

    /**
     * 更新商户API密钥显示
     */
    updateMerchantApiKeyDisplay(keyInfo) {
        try {
            // 更新商户ID (使用developer_id字段)
            const merchantIdInput = document.getElementById('merchantId');
            if (merchantIdInput && keyInfo.developer_id) {
                merchantIdInput.value = keyInfo.developer_id;
            }
            
            // 更新API密钥
            const apiKeyInput = document.getElementById('apiKey');
            if (apiKeyInput && keyInfo.api_key) {
                apiKeyInput.value = keyInfo.api_key;
            }
            
            // 更新回调地址
            const callbackUrlInput = document.getElementById('callbackUrl');
            if (callbackUrlInput && keyInfo.callback_url) {
                callbackUrlInput.value = keyInfo.callback_url;
            }
            
            // 更新统计数据
            this.updateStatsDisplay(keyInfo);
            
            console.log('✅ API密钥显示更新完成');
        } catch (error) {
            console.error('❌ 更新API密钥显示失败:', error);
        }
    }

    /**
     * 更新统计数据显示
     */
    updateStatsDisplay(keyInfo) {
        try {
            // 更新今日调用量
            const todayCallsElement = document.getElementById('todayCallsCount');
            if (todayCallsElement) {
                todayCallsElement.textContent = keyInfo.today_calls || '0';
            }
            
            // 更新本月调用量
            const monthCallsElement = document.getElementById('monthCallsCount');
            if (monthCallsElement) {
                monthCallsElement.textContent = keyInfo.month_calls || '0';
            }
            
            // 更新总调用量
            const totalCallsElement = document.getElementById('totalCallsCount');
            if (totalCallsElement) {
                totalCallsElement.textContent = keyInfo.total_calls || '0';
            }
            
            // 更新成功率
            const successRateElement = document.getElementById('successRateCount');
            if (successRateElement) {
                const successRate = keyInfo.success_rate || '0%';
                successRateElement.textContent = successRate;
            }
            
            console.log('✅ 统计数据更新完成');
        } catch (error) {
            console.error('❌ 更新统计数据失败:', error);
        }
    }

    /**
     * 切换密钥可见性
     */
    toggleKeyVisibility(inputId, buttonId) {
        const input = document.getElementById(inputId);
        const button = document.getElementById(buttonId);
        
        if (input && button) {
            if (input.type === 'password') {
                input.type = 'text';
                button.className = 'bi bi-eye-slash';
            } else {
                input.type = 'password';
                button.className = 'bi bi-eye';
            }
        }
    }

    /**
     * 复制到剪贴板
     */
    copyToClipboard(inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            input.select();
            input.setSelectionRange(0, 99999);
            
            try {
                document.execCommand('copy');
                this.showSuccess('已复制到剪贴板');
            } catch (err) {
                console.error('复制失败:', err);
                this.showError('复制失败，请手动选择复制');
            }
        }
    }

    /**
     * 重新生成API密钥
     */
    async regenerateApiKey() {
        if (!confirm('确定要重新生成API密钥吗？这将使旧密钥失效。')) {
            return;
        }

        try {
            console.log('🔄 开始重新生成API密钥...');
            
            const response = await fetch('/api/merchant/tools.php?action=regenerate_api_key', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                const keyInfo = data.data || data;
                console.log('✅ API密钥重新生成成功');
                this.updateMerchantApiKeyDisplay(keyInfo);
                this.showSuccess('API密钥重新生成成功');
            } else {
                console.error('❌ API密钥重新生成失败:', data.message);
                this.showError('API密钥重新生成失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 重新生成API密钥异常:', error);
            this.showError('重新生成API密钥时发生错误: ' + error.message);
        }
    }

    /**
     * 更新权限设置
     */
    async updatePermissions() {
        try {
            const permissions = {
                payment: document.getElementById('perm_payment')?.checked || false,
                refund: document.getElementById('perm_refund')?.checked || false,
                query: document.getElementById('perm_query')?.checked || false,
                webhook: document.getElementById('perm_webhook')?.checked || false,
                report: document.getElementById('perm_report')?.checked || false,
                admin: document.getElementById('perm_admin')?.checked || false,
                daily_limit: document.getElementById('dailyLimit')?.value || 10000,
                concurrent_limit: document.getElementById('concurrentLimit')?.value || 100
            };

            console.log('🔄 更新API权限设置:', permissions);
            
            const response = await fetch('/api/merchant/tools.php?action=update_permissions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify(permissions)
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                console.log('✅ API权限更新成功');
                this.showSuccess('API权限设置已更新');
            } else {
                console.error('❌ API权限更新失败:', data.message);
                this.showError('API权限更新失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 更新API权限异常:', error);
            this.showError('更新API权限时发生错误: ' + error.message);
        }
    }

    /**
     * 下载Postman集合
     */
    downloadPostman() {
        // 生成Postman集合文件
        const postmanCollection = {
            info: {
                name: "PayPal API Collection",
                description: "PayPal支付系统API接口集合",
                version: "1.0.0"
            },
            item: [
                {
                    name: "支付接口",
                    request: {
                        method: "POST",
                        header: [
                            {
                                key: "Content-Type",
                                value: "application/json"
                            },
                            {
                                key: "X-API-Key",
                                value: "{{api_key}}"
                            }
                        ],
                        url: {
                            raw: "{{base_url}}/api/payment",
                            host: ["{{base_url}}"],
                            path: ["api", "payment"]
                        },
                        body: {
                            mode: "raw",
                            raw: JSON.stringify({
                                amount: 100.00,
                                currency: "USD",
                                description: "测试支付"
                            }, null, 2)
                        }
                    }
                }
            ],
            variable: [
                {
                    key: "base_url",
                    value: window.location.origin
                },
                {
                    key: "api_key",
                    value: document.getElementById('apiKey')?.value || ""
                }
            ]
        };

        // 下载文件
        const blob = new Blob([JSON.stringify(postmanCollection, null, 2)], {
            type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'paypal-api-collection.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess('Postman集合文件已下载');
    }

    /**
     * 打开签名生成工具
     */
    openSignatureTool() {
        // 这里可以打开一个模态框或新页面来展示签名生成工具
        alert('签名生成工具功能开发中...');
    }

    /**
     * 打开API测试工具
     */
    openApiTester() {
        // 切换到测试选项卡
        const testTab = document.querySelector('#testing-tab');
        if (testTab) {
            testTab.click();
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        // 可以调用全局的消息提示组件
        if (window.apiModule && window.apiModule.showSuccess && window.apiModule.showSuccess !== this.showSuccess) {
            window.apiModule.showSuccess(message);
        } else {
            console.log('✅ 成功:', message);
            // 简单的成功提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-check-circle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        // 可以调用全局的消息提示组件
        if (window.apiModule && window.apiModule.showError && window.apiModule.showError !== this.showError) {
            window.apiModule.showError(message);
        } else {
            console.error('❌ 错误:', message);
            // 简单的错误提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }
    }
}

// 导出类
window.ApiKeyManager = ApiKeyManager;