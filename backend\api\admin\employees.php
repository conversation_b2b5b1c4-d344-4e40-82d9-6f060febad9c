<?php
/**
 * 员工管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证
if (!$auth->checkAuth()) {
    $auth->sendUnauthorized('请先登录');
    exit;
}

// 获取当前用户信息和域名信息（统一在头部处理）
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 验证用户信息
if (!$currentUser) {
    $auth->sendError('用户未登录', 401);
    exit;
}

// 获取数据库连接
$db = $auth->getDatabase();

try {
    // 检查用户权限：系统管理员、平台管理员、商户、码商都可以管理员工
    if (!in_array($currentUser['user_type'], ['system_admin', 'platform_admin', 'merchant', 'provider'])) {
        $auth->sendForbidden('无权限访问员工管理');
    }
    
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        handleGetEmployees();
    } elseif ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }
        $action = isset($input['action']) ? $input['action'] : '';
        
        switch ($action) {
            case 'create':
                handleCreateEmployee();
                break;
            case 'update':
                handleUpdateEmployee();
                break;
            case 'delete':
                handleDeleteEmployee();
                break;
            case 'update_permissions':
                handleUpdateEmployeePermissions($input);
                break;
            default:
                $auth->sendError('不支持的操作', 400);
        }
    } else {
        $auth->sendError('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    error_log("Employees API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 获取员工列表
function handleGetEmployees() {
    global $db, $auth, $currentUser, $domainInfo;
    
    // 获取分页参数
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    // 获取搜索和筛选参数
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $jobPositionId = isset($_GET['job_position_id']) ? intval($_GET['job_position_id']) : 0;
    $status = isset($_GET['status']) ? trim($_GET['status']) : '';
    
    try {
        // 根据用户类型构建不同的查询条件
        $whereCondition = "u.user_type = 'employee'";
        $params = array();
        
        if ($currentUser['user_type'] === 'system_admin') {
            // 系统管理员可以看到所有员工
            // 不需要额外条件
        } elseif ($currentUser['user_type'] === 'platform_admin') {
            // 平台管理员只能看到自己平台下的员工
            $platformId = isset($domainInfo['platform_id']) ? $domainInfo['platform_id'] : 0;
            $whereCondition .= " AND (
                (e.belongs_to_type = 'platform' AND e.belongs_to_id = ?) OR
                (e.belongs_to_type = 'merchant' AND e.belongs_to_id IN (
                    SELECT m.id FROM merchants m WHERE m.platform_id = ?
                )) OR
                (e.belongs_to_type = 'provider' AND e.belongs_to_id IN (
                    SELECT p.id FROM payment_providers p WHERE p.platform_id = ?
                ))
            )";
            $params = array($platformId, $platformId, $platformId);
        } else {
            // 商户和码商只能看到自己的员工
            $belongsToType = '';
            $belongsToId = 0;
            
            if ($currentUser['user_type'] === 'merchant') {
                $belongsToType = 'merchant';
                // 获取商户ID
                $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", array($currentUser['id']));
                $belongsToId = $merchant ? $merchant['id'] : 0;
            } elseif ($currentUser['user_type'] === 'provider') {
                $belongsToType = 'provider';
                // 获取码商ID
                $provider = $db->fetch("SELECT id FROM payment_providers WHERE user_id = ?", array($currentUser['id']));
                $belongsToId = $provider ? $provider['id'] : 0;
            }
            
            $whereCondition .= " AND e.belongs_to_type = ? AND e.belongs_to_id = ?";
            $params = array($belongsToType, $belongsToId);
        }
        
        // 添加搜索条件
        if (!empty($search)) {
            $whereCondition .= " AND u.username LIKE ?";
            $searchParam = '%' . $search . '%';
            $params[] = $searchParam;
        }
        
        // 添加职位筛选
        if ($jobPositionId > 0) {
            $whereCondition .= " AND e.job_position_id = ?";
            $params[] = $jobPositionId;
        }
        
        // 添加状态筛选
        if (!empty($status)) {
            $whereCondition .= " AND e.status = ?";
            $params[] = $status;
        }
        
        // 获取总数（用于分页）
        $countParams = $params;
        $countSql = "
            SELECT COUNT(DISTINCT u.id) as total
            FROM users u 
            INNER JOIN employees e ON u.id = e.user_id
            LEFT JOIN job_positions jp ON e.job_position_id = jp.id 
            WHERE {$whereCondition}
        ";
        $totalResult = $db->fetch($countSql, $countParams);
        $total = $totalResult['total'];
        $totalPages = ceil($total / $limit);
        
        // 获取员工列表 - 联合查询users和employees表（带分页）
        $listParams = $params;
        $listParams[] = $limit;
        $listParams[] = $offset;
        
        $employees = $db->fetchAll("
            SELECT u.id as user_id, u.username, u.status as user_status, u.created_at as user_created_at, u.last_login,
                   e.id as employee_id, e.job_position_id, e.status as employee_status, e.hire_date,
                   e.belongs_to_type, e.belongs_to_id,
                   jp.position_name as job_position_name,
                   COUNT(up.permission_id) as permission_count
            FROM users u 
            INNER JOIN employees e ON u.id = e.user_id
            LEFT JOIN job_positions jp ON e.job_position_id = jp.id 
            LEFT JOIN user_permissions up ON u.id = up.user_id
            WHERE {$whereCondition}
            GROUP BY u.id, u.username, u.status, u.created_at, u.last_login, e.id, e.job_position_id, e.status, e.hire_date, jp.position_name, e.belongs_to_type, e.belongs_to_id
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?", 
            $listParams
        );
        
        // 获取职位列表用于前端显示（根据用户类型过滤）
        $jobPositionWhere = "status = 1";
        $jobPositionParams = array();
        
        if ($currentUser['user_type'] === 'platform_admin') {
            // 平台管理员只能看到自己平台的职位
            $platformId = isset($domainInfo['platform_id']) ? $domainInfo['platform_id'] : 0;
            $jobPositionWhere .= " AND belongs_to_type = 'platform' AND belongs_to_id = ?";
            $jobPositionParams[] = $platformId;
        } elseif ($currentUser['user_type'] === 'merchant') {
            // 商户管理员只能看到自己商户的职位
            $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", array($currentUser['id']));
            $merchantId = $merchant ? $merchant['id'] : 0;
            $jobPositionWhere .= " AND belongs_to_type = 'merchant' AND belongs_to_id = ?";
            $jobPositionParams[] = $merchantId;
        } elseif ($currentUser['user_type'] === 'provider') {
            // 码商管理员只能看到自己码商的职位
            $provider = $db->fetch("SELECT id FROM payment_providers WHERE user_id = ?", array($currentUser['id']));
            $providerId = $provider ? $provider['id'] : 0;
            $jobPositionWhere .= " AND belongs_to_type = 'provider' AND belongs_to_id = ?";
            $jobPositionParams[] = $providerId;
        }
        // 系统管理员可以看到所有职位，不需要额外条件
        
        $jobPositions = $db->fetchAll("
            SELECT id, position_name, description, status 
            FROM job_positions 
            WHERE {$jobPositionWhere}
            ORDER BY position_name ASC", 
            $jobPositionParams
        );
        
        $auth->sendSuccess('员工列表获取成功', array(
            'employees' => $employees,
            'job_positions' => $jobPositions,
            'pagination' => array(
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => $totalPages
            )
        ));
        
    } catch (Exception $e) {
        $auth->sendError('获取员工列表失败: ' . $e->getMessage(), 500);
    }
}

// 创建员工
function handleCreateEmployee() {
    global $db, $auth, $currentUser, $domainInfo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证必填字段
    $requiredFields = array('username', 'password');
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            $auth->sendError("字段 {$field} 不能为空", 400);
            return;
        }
    }
    
    $username = trim($input['username']);
    $password = $input['password'];
    $status = isset($input['status']) ? $input['status'] : 'active';
    $jobPositionId = isset($input['job_position_id']) ? intval($input['job_position_id']) : 0;
    
    // 检查用户名是否已存在
    $existingUser = $db->fetch("SELECT id FROM users WHERE username = ?", array($username));
    if ($existingUser) {
        $auth->sendError('用户名已存在', 400);
        return;
    }
    
    try {
        $db->execute("START TRANSACTION");
        
        // 1. 创建users记录（登录信息）
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, user_type, status, created_at, updated_at) 
                VALUES (?, ?, 'employee', ?, NOW(), NOW())";
        
        $params = array($username, $hashedPassword, $status);
        $db->execute($sql, $params);
        $userId = $db->lastInsertId();
        
        // 2. 确定员工所属关系
        $belongsToType = '';
        $belongsToId = 0;
        
        if ($currentUser['user_type'] === 'platform_admin') {
            $belongsToType = 'platform';
            $belongsToId = isset($domainInfo['platform_id']) ? $domainInfo['platform_id'] : 0;
        } elseif ($currentUser['user_type'] === 'merchant') {
            $belongsToType = 'merchant';
            $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", array($currentUser['id']));
            $belongsToId = $merchant ? $merchant['id'] : 0;
        } elseif ($currentUser['user_type'] === 'provider') {
            $belongsToType = 'provider';
            $provider = $db->fetch("SELECT id FROM payment_providers WHERE user_id = ?", array($currentUser['id']));
            $belongsToId = $provider ? $provider['id'] : 0;
        }
        
        // 验证职位是否属于当前用户的实体
        if ($jobPositionId > 0) {
            $jobPosition = $db->fetch("
                SELECT id FROM job_positions 
                WHERE id = ? AND belongs_to_type = ? AND belongs_to_id = ? AND status = 1
            ", array($jobPositionId, $belongsToType, $belongsToId));
            
            if (!$jobPosition) {
                $auth->sendError('选择的职位不存在或不属于当前实体', 400);
                return;
            }
        }
        
        // 3. 创建employees记录（详细信息）
        $employeeSql = "INSERT INTO employees (user_id, belongs_to_type, belongs_to_id, job_position_id, status, created_at, updated_at) 
                        VALUES (?, ?, ?, ?, ?, NOW(), NOW())";
        
        $employeeParams = array(
            $userId,
            $belongsToType,
            $belongsToId,
            $jobPositionId > 0 ? $jobPositionId : null,
            $status
        );
        
        $db->execute($employeeSql, $employeeParams);
        $employeeId = $db->lastInsertId();
        
        // 3. 如果提供了权限列表，设置员工权限
        if (isset($input['permissions']) && is_array($input['permissions'])) {
            foreach ($input['permissions'] as $permissionId) {
                $permissionId = intval($permissionId);
                if ($permissionId > 0) {
                    // 验证权限是否存在
                    $permission = $db->fetch("SELECT id FROM permissions WHERE id = ?", array($permissionId));
                    if ($permission) {
                        $db->execute(
                            "INSERT INTO user_permissions (user_id, permission_id, created_at) VALUES (?, ?, NOW())",
                            array($userId, $permissionId)
                        );
                    }
                }
            }
        }
        
        $db->execute("COMMIT");
        
        // 返回创建的员工信息（联合查询）
        $employee = $db->fetch("
            SELECT u.id as user_id, u.username, u.status as user_status, u.created_at as user_created_at,
                   e.id as employee_id, e.job_position_id, e.status as employee_status,
                   jp.position_name as job_position_name
            FROM users u 
            INNER JOIN employees e ON u.id = e.user_id
            LEFT JOIN job_positions jp ON e.job_position_id = jp.id 
            WHERE u.id = ?", array($userId));
        
        $auth->sendSuccess('员工创建成功', $employee);
        
    } catch (Exception $e) {
        $db->execute("ROLLBACK");
        $auth->sendError('创建员工失败: ' . $e->getMessage(), 500);
    }
}

// 更新员工
function handleUpdateEmployee() {
    global $db, $auth, $currentUser, $domainInfo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $userId = isset($input['user_id']) ? intval($input['user_id']) : 0;
    if (!$userId) {
        $auth->sendError('用户ID不能为空', 400);
        return;
    }
    
    // 检查员工是否存在且有权限访问
    $whereCondition = "u.id = ? AND u.user_type = 'employee'";
    $params = array($userId);
    
    if ($currentUser['user_type'] === 'platform_admin') {
        // 平台管理员只能操作自己平台下的员工
        $platformId = isset($domainInfo['platform_id']) ? $domainInfo['platform_id'] : 0;
        $whereCondition .= " AND (
            (e.belongs_to_type = 'platform' AND e.belongs_to_id = ?) OR
            (e.belongs_to_type = 'merchant' AND e.belongs_to_id IN (
                SELECT m.id FROM merchants m WHERE m.platform_id = ?
            )) OR
            (e.belongs_to_type = 'provider' AND e.belongs_to_id IN (
                SELECT p.id FROM payment_providers p WHERE p.platform_id = ?
            ))
        )";
        $params[] = $platformId;
        $params[] = $platformId;
        $params[] = $platformId;
    } elseif ($currentUser['user_type'] === 'merchant') {
        // 商户只能操作自己的员工
        $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", array($currentUser['id']));
        $merchantId = $merchant ? $merchant['id'] : 0;
        $whereCondition .= " AND e.belongs_to_type = 'merchant' AND e.belongs_to_id = ?";
        $params[] = $merchantId;
    } elseif ($currentUser['user_type'] === 'provider') {
        // 码商只能操作自己的员工
        $provider = $db->fetch("SELECT id FROM payment_providers WHERE user_id = ?", array($currentUser['id']));
        $providerId = $provider ? $provider['id'] : 0;
        $whereCondition .= " AND e.belongs_to_type = 'provider' AND e.belongs_to_id = ?";
        $params[] = $providerId;
    }
    
    $employee = $db->fetch("
        SELECT u.*, e.id as employee_id, e.job_position_id, e.status as employee_status,
               e.belongs_to_type, e.belongs_to_id
        FROM users u 
        INNER JOIN employees e ON u.id = e.user_id
        WHERE {$whereCondition}", 
        $params
    );
    
    if (!$employee) {
        $auth->sendError('员工不存在或无权限访问', 404);
        return;
    }
    
    try {
        $db->execute("START TRANSACTION");
        
        // 更新users表
        $userUpdateFields = array();
        $userUpdateValues = array();
        
        // 更新用户名
        if (isset($input['username']) && !empty(trim($input['username']))) {
            $newUsername = trim($input['username']);
            if ($newUsername !== $employee['username']) {
                // 检查新用户名是否已存在
                $existingUser = $db->fetch("SELECT id FROM users WHERE username = ? AND id != ?", 
                                         array($newUsername, $userId));
                if ($existingUser) {
                    $auth->sendError('用户名已存在', 400);
                    return;
                }
                $userUpdateFields[] = "username = ?";
                $userUpdateValues[] = $newUsername;
            }
        }
        
        // 更新密码
        if (isset($input['password']) && !empty($input['password'])) {
            $userUpdateFields[] = "password = ?";
            $userUpdateValues[] = password_hash($input['password'], PASSWORD_DEFAULT);
        }
        
        // 更新用户状态
        if (isset($input['user_status']) && in_array($input['user_status'], ['active', 'disabled'])) {
            $userUpdateFields[] = "status = ?";
            $userUpdateValues[] = $input['user_status'];
        }
        
        if (!empty($userUpdateFields)) {
            $userUpdateFields[] = "updated_at = NOW()";
            $userUpdateValues[] = $userId;
            
            $sql = "UPDATE users SET " . implode(', ', $userUpdateFields) . " WHERE id = ?";
            $db->execute($sql, $userUpdateValues);
        }
        
        // 更新employees表
        $employeeUpdateFields = array();
        $employeeUpdateValues = array();
        
        // 更新职位
        if (isset($input['job_position_id'])) {
            $jobPositionId = intval($input['job_position_id']);
            if ($jobPositionId > 0) {
                // 验证职位是否属于当前员工的实体
                $jobPosition = $db->fetch("
                    SELECT id FROM job_positions 
                    WHERE id = ? AND belongs_to_type = ? AND belongs_to_id = ? AND status = 1
                ", array($jobPositionId, $employee['belongs_to_type'], $employee['belongs_to_id']));
                
                if (!$jobPosition) {
                    $auth->sendError('指定的职位不存在或不属于当前员工的实体', 400);
                    return;
                }
                $employeeUpdateFields[] = "job_position_id = ?";
                $employeeUpdateValues[] = $jobPositionId;
            } else {
                $employeeUpdateFields[] = "job_position_id = NULL";
            }
        }
        
        // 更新员工状态
        if (isset($input['employee_status']) && in_array($input['employee_status'], ['active', 'inactive', 'resigned'])) {
            $employeeUpdateFields[] = "status = ?";
            $employeeUpdateValues[] = $input['employee_status'];
        }
        
        if (!empty($employeeUpdateFields)) {
            $employeeUpdateFields[] = "updated_at = NOW()";
            $employeeUpdateValues[] = $employee['employee_id'];
            
            $sql = "UPDATE employees SET " . implode(', ', $employeeUpdateFields) . " WHERE id = ?";
            $db->execute($sql, $employeeUpdateValues);
        }
        
        // 更新权限（如果提供）
        if (isset($input['permissions']) && is_array($input['permissions'])) {
            // 删除现有权限
            $db->execute("DELETE FROM user_permissions WHERE user_id = ?", array($userId));
            
            // 添加新权限
            foreach ($input['permissions'] as $permissionId) {
                $permissionId = intval($permissionId);
                if ($permissionId > 0) {
                    $permission = $db->fetch("SELECT id FROM permissions WHERE id = ?", array($permissionId));
                    if ($permission) {
                        $db->execute(
                            "INSERT INTO user_permissions (user_id, permission_id, created_at) VALUES (?, ?, NOW())",
                            array($userId, $permissionId)
                        );
                    }
                }
            }
        }
        
        $db->execute("COMMIT");
        
        // 返回更新后的员工信息
        $updatedEmployee = $db->fetch("
            SELECT u.id as user_id, u.username, u.status as user_status, u.created_at as user_created_at, u.last_login,
                   e.id as employee_id, e.job_position_id, e.status as employee_status, e.hire_date,
                   jp.position_name as job_position_name
            FROM users u 
            INNER JOIN employees e ON u.id = e.user_id
            LEFT JOIN job_positions jp ON e.job_position_id = jp.id 
            WHERE u.id = ?", array($userId));
        
        $auth->sendSuccess('员工信息更新成功', $updatedEmployee);
        
    } catch (Exception $e) {
        $db->execute("ROLLBACK");
        $auth->sendError('更新员工信息失败: ' . $e->getMessage(), 500);
    }
}

// 删除员工
function handleDeleteEmployee() {
    global $db, $auth, $currentUser, $domainInfo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $userId = isset($input['user_id']) ? intval($input['user_id']) : 0;
    if (!$userId) {
        $auth->sendError('用户ID不能为空', 400);
        return;
    }
    
    // 检查员工是否存在且有权限访问
    $whereCondition = "u.id = ? AND u.user_type = 'employee'";
    $params = array($userId);
    
    if ($currentUser['user_type'] === 'platform_admin') {
        // 平台管理员只能删除自己平台下的员工
        $platformId = isset($domainInfo['platform_id']) ? $domainInfo['platform_id'] : 0;
        $whereCondition .= " AND (
            (e.belongs_to_type = 'platform' AND e.belongs_to_id = ?) OR
            (e.belongs_to_type = 'merchant' AND e.belongs_to_id IN (
                SELECT m.id FROM merchants m WHERE m.platform_id = ?
            )) OR
            (e.belongs_to_type = 'provider' AND e.belongs_to_id IN (
                SELECT p.id FROM payment_providers p WHERE p.platform_id = ?
            ))
        )";
        $params[] = $platformId;
        $params[] = $platformId;
        $params[] = $platformId;
    } elseif ($currentUser['user_type'] === 'merchant') {
        // 商户只能删除自己的员工
        $merchant = $db->fetch("SELECT id FROM merchants WHERE user_id = ?", array($currentUser['id']));
        $merchantId = $merchant ? $merchant['id'] : 0;
        $whereCondition .= " AND e.belongs_to_type = 'merchant' AND e.belongs_to_id = ?";
        $params[] = $merchantId;
    } elseif ($currentUser['user_type'] === 'provider') {
        // 码商只能删除自己的员工
        $provider = $db->fetch("SELECT id FROM payment_providers WHERE user_id = ?", array($currentUser['id']));
        $providerId = $provider ? $provider['id'] : 0;
        $whereCondition .= " AND e.belongs_to_type = 'provider' AND e.belongs_to_id = ?";
        $params[] = $providerId;
    }
    
    $employee = $db->fetch("
        SELECT u.*, e.id as employee_id, e.belongs_to_type, e.belongs_to_id
        FROM users u 
        INNER JOIN employees e ON u.id = e.user_id
        WHERE {$whereCondition}", 
        $params
    );
    
    if (!$employee) {
        $auth->sendError('员工不存在或无权限访问', 404);
        return;
    }
    
    try {
        $db->execute("START TRANSACTION");
        
        // 1. 删除用户权限
        $db->execute("DELETE FROM user_permissions WHERE user_id = ?", array($userId));
        
        // 2. 删除员工记录
        $db->execute("DELETE FROM employees WHERE user_id = ?", array($userId));
        
        // 3. 删除用户记录
        $db->execute("DELETE FROM users WHERE id = ?", array($userId));
        
        $db->execute("COMMIT");
        
        $auth->sendSuccess('员工删除成功');
        
    } catch (Exception $e) {
        $db->execute("ROLLBACK");
        $auth->sendError('删除员工失败: ' . $e->getMessage(), 500);
    }
}

// 更新员工权限（暂时简化）
function handleUpdateEmployeePermissions($input) {
    global $auth, $currentUser;
    
    // 检查权限 - 简化权限检查，只要是认证用户就可以管理员工权限
    if (!$currentUser) {
        $auth->sendForbidden('未认证用户');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}
?> 