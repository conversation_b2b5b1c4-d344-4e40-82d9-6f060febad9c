/**
 * 核心工具模块 - admin.js拆分重构项目
 * 包含通用工具函数、消息提示系统、格式化工具等
 * 合并了原core.js中的配置、性能监控、缓存管理功能
 * 
 * <AUTHOR>
 * @version 1.1.0
 * @created 2024年12月
 * @updated 2024年12月22日 - 合并core.js功能
 */

// 首先定义全局配置 (防止在其他地方使用时未定义)
if (typeof window.CONFIG === 'undefined') {
    window.CONFIG = {
        API_BASE_URL: '/api',
        TOKEN_KEY: 'admin_token',
        USER_KEY: 'admin_user',
        PERFORMANCE_ENABLED: true,
        LAZY_LOAD_DELAY: 100,
        CACHE_DURATION: 5 * 60 * 1000, // 5分钟
        MAX_RETRY_ATTEMPTS: 3,
        DEBUG: false  // 设置为false，不使用模拟登录
    };
}

// 性能监控模块 (防止重复声明)
const PerformanceMonitor = window.PerformanceMonitor || {
    metrics: {
        loadTime: 0,
        domContentLoaded: 0,
        apiCalls: [],
        memoryUsage: 0,
        renderTime: {}
    },

    // 记录性能指标
    recordMetric(name, value, type = 'timing') {
        const metric = {
            name,
            value,
            type,
            timestamp: Date.now()
        };
        
        if (type === 'api') {
            this.metrics.apiCalls.push(metric);
            // 保持最近100次API调用记录
            if (this.metrics.apiCalls.length > 100) {
                this.metrics.apiCalls.shift();
            }
        } else {
            this.metrics[name] = value;
        }

        // 发送性能数据到后端（可选）
        if (window.CONFIG && window.CONFIG.PERFORMANCE_ENABLED) {
            this.sendPerformanceData(metric);
        }
    },

    // 发送性能数据
    async sendPerformanceData(metric) {
        try {
            // 批量发送，减少请求次数
            if (!this.pendingMetrics) {
                this.pendingMetrics = [];
            }
            this.pendingMetrics.push(metric);

            // 每10个指标或5秒后发送一次
            if (this.pendingMetrics.length >= 10 || !this.sendTimer) {
                this.sendTimer = setTimeout(() => {
                    this.flushMetrics();
                }, 5000);
            }
        } catch (error) {
            console.warn('性能数据发送失败:', error);
        }
    },

    // 批量发送指标
    async flushMetrics() {
        if (!this.pendingMetrics || this.pendingMetrics.length === 0) return;

        try {
            const metrics = this.pendingMetrics.splice(0);
            await utils.apiRequest('/admin.php?action=performance_log', {
                method: 'POST',
                body: JSON.stringify({ metrics })
            });
        } catch (error) {
            console.warn('性能指标发送失败:', error);
        } finally {
            this.sendTimer = null;
        }
    },

    // 获取内存使用情况
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1048576), // MB
                total: Math.round(performance.memory.totalJSHeapSize / 1048576),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576)
            };
        }
        return null;
    },

    // 页面加载性能
    measurePageLoad() {
        if (performance.timing) {
            const timing = performance.timing;
            this.metrics.loadTime = timing.loadEventEnd - timing.navigationStart;
            this.metrics.domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart;
            
            this.recordMetric('pageLoad', this.metrics.loadTime);
            this.recordMetric('domReady', this.metrics.domContentLoaded);
        }
    }
};

// 缓存管理模块 (防止重复声明)
const CacheManager = window.CacheManager || {
    storage: new Map(),
    
    // 设置缓存
    set(key, value, ttl = (window.CONFIG ? window.CONFIG.CACHE_DURATION : 5 * 60 * 1000)) {
        const item = {
            value,
            expiry: Date.now() + ttl
        };
        this.storage.set(key, item);
    },

    // 获取缓存
    get(key) {
        const item = this.storage.get(key);
        if (!item) return null;

        if (Date.now() > item.expiry) {
            this.storage.delete(key);
            return null;
        }

        return item.value;
    },

    // 清除缓存
    clear(key) {
        if (key) {
            this.storage.delete(key);
        } else {
            this.storage.clear();
        }
    },

    // 清理过期缓存
    cleanup() {
        const now = Date.now();
        for (const [key, item] of this.storage.entries()) {
            if (now > item.expiry) {
                this.storage.delete(key);
            }
        }
    }
};

// AdminUtils类 (防止重复声明)
const AdminUtils = window.AdminUtils || class AdminUtils {
    constructor() {
        this.messageContainer = null;
        this.loadingElements = new Set();
        this.debounceTimers = new Map();
        
        this.init();
    }
    
    /**
     * 初始化工具模块
     */
    init() {
        this.createMessageContainer();
        this.setupGlobalErrorHandler();
        console.log('AdminUtils initialized');
    }
    
    /**
     * 创建消息容器
     */
    createMessageContainer() {
        if (!document.getElementById('admin-message-container')) {
            const container = document.createElement('div');
            container.id = 'admin-message-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
            this.messageContainer = container;
        } else {
            this.messageContainer = document.getElementById('admin-message-container');
        }
    }
    
    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandler() {
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.showMessage('系统发生未处理的错误', 'error');
        });
    }
    
    // ==================== 消息提示系统 ====================
    
    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     * @param {number} duration - 显示时长(毫秒)
     */
    showMessage(message, type = 'info', duration = 3000) {
        const messageElement = document.createElement('div');
        messageElement.className = `alert alert-${this.getBootstrapAlertType(type)} alert-dismissible fade show`;
        messageElement.style.cssText = `
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border: none;
            border-radius: 8px;
        `;
        
        messageElement.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${this.getMessageIcon(type)} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        this.messageContainer.appendChild(messageElement);
        
        // 自动消失
        if (duration > 0) {
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.classList.remove('show');
                    setTimeout(() => {
                        if (messageElement.parentNode) {
                            messageElement.parentNode.removeChild(messageElement);
                        }
                    }, 150);
                }
            }, duration);
        }
        
        return messageElement;
    }
    
    /**
     * 获取Bootstrap警告类型
     */
    getBootstrapAlertType(type) {
        const typeMap = {
            success: 'success',
            error: 'danger',
            warning: 'warning',
            info: 'info'
        };
        return typeMap[type] || 'info';
    }
    
    /**
     * 获取消息图标
     */
    getMessageIcon(type) {
        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return iconMap[type] || 'info-circle';
    }
    
    /**
     * 显示成功消息
     */
    showSuccess(message, duration = 3000) {
        return this.showMessage(message, 'success', duration);
    }
    
    /**
     * 显示错误消息
     */
    showError(message, duration = 5000) {
        return this.showMessage(message, 'error', duration);
    }
    
    /**
     * 显示警告消息
     */
    showWarning(message, duration = 4000) {
        return this.showMessage(message, 'warning', duration);
    }
    
    /**
     * 显示信息消息
     */
    showInfo(message, duration = 3000) {
        return this.showMessage(message, 'info', duration);
    }
    
    // ==================== 加载状态管理 ====================
    
    /**
     * 显示加载状态
     * @param {HTMLElement} element - 目标元素
     * @param {string} text - 加载文本
     */
    showLoading(element, text = '加载中...') {
        if (!element) return;
        
        element.style.position = 'relative';
        
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'admin-loading-overlay';
        loadingOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            border-radius: inherit;
        `;
        
        loadingOverlay.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary mb-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="small text-muted">${text}</div>
            </div>
        `;
        
        element.appendChild(loadingOverlay);
        this.loadingElements.add(element);
        
        return loadingOverlay;
    }
    
    /**
     * 隐藏加载状态
     * @param {HTMLElement} element - 目标元素
     */
    hideLoading(element) {
        if (!element) return;
        
        const overlay = element.querySelector('.admin-loading-overlay');
        if (overlay) {
            overlay.remove();
        }
        
        this.loadingElements.delete(element);
    }
    
    /**
     * 清理所有加载状态
     */
    clearAllLoading() {
        this.loadingElements.forEach(element => {
            this.hideLoading(element);
        });
    }
    
    // ==================== 格式化工具 ====================
    
    /**
     * 格式化金额
     * @param {number} amount - 金额
     * @param {string} currency - 货币符号
     * @returns {string}
     */
    formatMoney(amount, currency = '¥') {
        if (amount === null || amount === undefined) return '-';
        
        const num = parseFloat(amount);
        if (isNaN(num)) return '-';
        
        return currency + num.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    /**
     * 格式化日期时间
     * @param {string|Date} datetime - 日期时间
     * @param {string} format - 格式类型
     * @returns {string}
     */
    formatDateTime(datetime, format = 'full') {
        if (!datetime) return '-';
        
        const date = new Date(datetime);
        if (isNaN(date.getTime())) return '-';
        
        const options = {
            full: {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            },
            date: {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            },
            time: {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            },
            short: {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            }
        };
        
        return date.toLocaleString('zh-CN', options[format] || options.full);
    }
    
    /**
     * 格式化日期（只显示日期部分）
     * @param {string|Date} datetime - 日期时间
     * @returns {string}
     */
    formatDate(datetime) {
        return this.formatDateTime(datetime, 'date');
    }
    
    /**
     * 获取姓名首字母
     * @param {string} name - 姓名
     * @returns {string}
     */
    getInitials(name) {
        if (!name) return '?';
        
        // 去除空格并转为大写
        const cleanName = name.trim();
        if (!cleanName) return '?';
        
        // 如果是中文名，取前两个字符
        if (/[\u4e00-\u9fa5]/.test(cleanName)) {
            return cleanName.substring(0, 2);
        }
        
        // 如果是英文名，取每个单词的首字母
        const words = cleanName.split(/\s+/);
        if (words.length === 1) {
            return words[0].substring(0, 2).toUpperCase();
        }
        
        return words.slice(0, 2).map(word => word.charAt(0).toUpperCase()).join('');
    }
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string}
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        if (!bytes) return '-';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 格式化数字
     * @param {number} num - 数字
     * @param {number} decimals - 小数位数
     * @returns {string}
     */
    formatNumber(num, decimals = 0) {
        if (num === null || num === undefined) return '-';
        
        const number = parseFloat(num);
        if (isNaN(number)) return '-';
        
        return number.toLocaleString('zh-CN', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }
    
    /**
     * 格式化百分比
     * @param {number} value - 数值
     * @param {number} decimals - 小数位数
     * @returns {string}
     */
    formatPercent(value, decimals = 2) {
        if (value === null || value === undefined) return '-';
        
        const num = parseFloat(value);
        if (isNaN(num)) return '-';
        
        return (num * 100).toFixed(decimals) + '%';
    }
    
    // ==================== 字符串工具 ====================
    
    /**
     * 截断文本
     * @param {string} text - 文本
     * @param {number} length - 最大长度
     * @param {string} suffix - 后缀
     * @returns {string}
     */
    truncateText(text, length = 50, suffix = '...') {
        if (!text) return '';
        if (text.length <= length) return text;
        return text.substring(0, length) + suffix;
    }
    
    /**
     * 转义HTML
     * @param {string} html - HTML字符串
     * @returns {string}
     */
    escapeHtml(html) {
        if (!html) return '';
        
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    }
    
    /**
     * 生成随机字符串
     * @param {number} length - 长度
     * @param {string} chars - 字符集
     * @returns {string}
     */
    randomString(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    // ==================== 验证工具 ====================
    
    /**
     * 验证邮箱
     * @param {string} email - 邮箱地址
     * @returns {boolean}
     */
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
    /**
     * 验证手机号
     * @param {string} phone - 手机号
     * @returns {boolean}
     */
    validatePhone(phone) {
        const re = /^1[3-9]\d{9}$/;
        return re.test(phone);
    }
    
    /**
     * 验证身份证号
     * @param {string} idCard - 身份证号
     * @returns {boolean}
     */
    validateIdCard(idCard) {
        const re = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        return re.test(idCard);
    }
    
    /**
     * 验证URL
     * @param {string} url - URL地址
     * @returns {boolean}
     */
    validateUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }
    
    // ==================== 性能工具 ====================
    
    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间(毫秒)
     * @param {string} key - 防抖键值(可选)
     * @returns {Function}
     */
    debounce(func, delay = 300, key = 'default') {
        return (...args) => {
            if (this.debounceTimers.has(key)) {
                clearTimeout(this.debounceTimers.get(key));
            }
            
            const timer = setTimeout(() => {
                func.apply(this, args);
                this.debounceTimers.delete(key);
            }, delay);
            
            this.debounceTimers.set(key, timer);
        };
    }
    
    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} delay - 延迟时间(毫秒)
     * @returns {Function}
     */
    throttle(func, delay = 300) {
        let lastTime = 0;
        return (...args) => {
            const now = Date.now();
            if (now - lastTime >= delay) {
                lastTime = now;
                func.apply(this, args);
            }
        };
    }
    
    /**
     * 延迟执行
     * @param {number} ms - 延迟时间(毫秒)
     * @returns {Promise}
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // ==================== DOM工具 ====================
    
    /**
     * 查询元素
     * @param {string} selector - 选择器
     * @param {HTMLElement} parent - 父元素
     * @returns {HTMLElement|null}
     */
    $(selector, parent = document) {
        return parent.querySelector(selector);
    }
    
    /**
     * 查询多个元素
     * @param {string} selector - 选择器
     * @param {HTMLElement} parent - 父元素
     * @returns {NodeList}
     */
    $$(selector, parent = document) {
        return parent.querySelectorAll(selector);
    }
    
    /**
     * 创建元素
     * @param {string} tag - 标签名
     * @param {Object} attrs - 属性对象
     * @param {string} html - 内部HTML
     * @returns {HTMLElement}
     */
    createElement(tag, attrs = {}, html = '') {
        const element = document.createElement(tag);
        
        Object.keys(attrs).forEach(key => {
            if (key === 'class') {
                element.className = attrs[key];
            } else if (key === 'style' && typeof attrs[key] === 'object') {
                Object.assign(element.style, attrs[key]);
            } else {
                element.setAttribute(key, attrs[key]);
            }
        });
        
        if (html) {
            element.innerHTML = html;
        }
        
        return element;
    }
    
    /**
     * 检查元素是否在视口中
     * @param {HTMLElement} element - 元素
     * @returns {boolean}
     */
    isElementInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
    
    // ==================== 数据工具 ====================
    
    /**
     * 深拷贝对象
     * @param {any} obj - 要拷贝的对象
     * @returns {any}
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const cloned = {};
            Object.keys(obj).forEach(key => {
                cloned[key] = this.deepClone(obj[key]);
            });
            return cloned;
        }
    }
    
    /**
     * 合并对象
     * @param {Object} target - 目标对象
     * @param {...Object} sources - 源对象
     * @returns {Object}
     */
    merge(target, ...sources) {
        if (!sources.length) return target;
        const source = sources.shift();
        
        if (this.isObject(target) && this.isObject(source)) {
            for (const key in source) {
                if (this.isObject(source[key])) {
                    if (!target[key]) Object.assign(target, { [key]: {} });
                    this.merge(target[key], source[key]);
                } else {
                    Object.assign(target, { [key]: source[key] });
                }
            }
        }
        
        return this.merge(target, ...sources);
    }
    
    /**
     * 检查是否为对象
     * @param {any} item - 要检查的项
     * @returns {boolean}
     */
    isObject(item) {
        return item && typeof item === 'object' && !Array.isArray(item);
    }
    
    /**
     * 获取对象属性值
     * @param {Object} obj - 对象
     * @param {string} path - 属性路径 (如: 'user.profile.name')
     * @param {any} defaultValue - 默认值
     * @returns {any}
     */
    get(obj, path, defaultValue = undefined) {
        const keys = path.split('.');
        let result = obj;
        
        for (const key of keys) {
            if (result === null || result === undefined) {
                return defaultValue;
            }
            result = result[key];
        }
        
        return result !== undefined ? result : defaultValue;
    }
    
    // ==================== 清理方法 ====================
    
    /**
     * 销毁工具模块
     */
    destroy() {
        // 清理所有防抖定时器
        this.debounceTimers.forEach(timer => clearTimeout(timer));
        this.debounceTimers.clear();
        
        // 清理所有加载状态
        this.clearAllLoading();
        
        // 移除消息容器
        if (this.messageContainer && this.messageContainer.parentNode) {
            this.messageContainer.parentNode.removeChild(this.messageContainer);
        }
        
        console.log('AdminUtils destroyed');
    }
    
    /**
     * 清理浏览器缓存（开发环境使用）
     */
    clearBrowserCache() {
        if ('caches' in window) {
            caches.keys().then(names => {
                names.forEach(name => {
                    caches.delete(name);
                });
                console.log('✅ 浏览器缓存已清理');
            });
        }
        
        // 清理localStorage和sessionStorage
        if (window.localStorage) {
            localStorage.clear();
            console.log('✅ localStorage已清理');
        }
        
        if (window.sessionStorage) {
            sessionStorage.clear();
            console.log('✅ sessionStorage已清理');
        }
        
        // 强制刷新页面
        window.location.reload(true);
    }
    
    /**
     * 添加缓存破坏参数到URL
     * @param {string} url - 原始URL
     * @returns {string} 带时间戳的URL
     */
    addCacheBuster(url) {
        const separator = url.includes('?') ? '&' : '?';
        return `${url}${separator}v=${Date.now()}`;
    }
};

// 创建全局工具实例
window.AdminUtilsInstance = window.AdminUtilsInstance || new AdminUtils();

// 全局导出 (合并了原core.js的功能，防止重复声明)
// CONFIG已在文件开头定义
if (typeof window.PerformanceMonitor === 'undefined') {
    window.PerformanceMonitor = PerformanceMonitor;
}
if (typeof window.CacheManager === 'undefined') {
    window.CacheManager = CacheManager;
}
if (typeof window.AdminUtils === 'undefined') {
    window.AdminUtils = AdminUtils;
}

// 导出工具模块
window.UtilsModule = {
    CONFIG: window.CONFIG,
    PerformanceMonitor: window.PerformanceMonitor,
    CacheManager: window.CacheManager,
    AdminUtils: window.AdminUtils,
    instance: window.AdminUtilsInstance,
    
    // 便捷方法
    showMessage: (message, type, duration) => window.AdminUtilsInstance.showMessage(message, type, duration),
    showSuccess: (message, duration) => window.AdminUtilsInstance.showSuccess(message, duration),
    showError: (message, duration) => window.AdminUtilsInstance.showError(message, duration),
    showWarning: (message, duration) => window.AdminUtilsInstance.showWarning(message, duration),
    showInfo: (message, duration) => window.AdminUtilsInstance.showInfo(message, duration),
    showLoading: (element, text) => window.AdminUtilsInstance.showLoading(element, text),
    hideLoading: (element) => window.AdminUtilsInstance.hideLoading(element),
    formatMoney: (amount, currency) => window.AdminUtilsInstance.formatMoney(amount, currency),
    formatDateTime: (datetime, format) => window.AdminUtilsInstance.formatDateTime(datetime, format),
    formatDate: (datetime) => window.AdminUtilsInstance.formatDate(datetime),
    getInitials: (name) => window.AdminUtilsInstance.getInitials(name),
    formatFileSize: (bytes) => window.AdminUtilsInstance.formatFileSize(bytes),
    formatNumber: (num, decimals) => window.AdminUtilsInstance.formatNumber(num, decimals),
    formatPercent: (value, decimals) => window.AdminUtilsInstance.formatPercent(value, decimals),
    truncateText: (text, length, suffix) => window.AdminUtilsInstance.truncateText(text, length, suffix),
    escapeHtml: (html) => window.AdminUtilsInstance.escapeHtml(html),
    randomString: (length, chars) => window.AdminUtilsInstance.randomString(length, chars),
    validateEmail: (email) => window.AdminUtilsInstance.validateEmail(email),
    validatePhone: (phone) => window.AdminUtilsInstance.validatePhone(phone),
    validateIdCard: (idCard) => window.AdminUtilsInstance.validateIdCard(idCard),
    validateUrl: (url) => window.AdminUtilsInstance.validateUrl(url),
    debounce: (func, delay, key) => window.AdminUtilsInstance.debounce(func, delay, key),
    throttle: (func, delay) => window.AdminUtilsInstance.throttle(func, delay),
    delay: (ms) => window.AdminUtilsInstance.delay(ms),
    $: (selector, parent) => window.AdminUtilsInstance.$(selector, parent),
    $$: (selector, parent) => window.AdminUtilsInstance.$$(selector, parent),
    createElement: (tag, attrs, html) => window.AdminUtilsInstance.createElement(tag, attrs, html),
    isElementInViewport: (element) => window.AdminUtilsInstance.isElementInViewport(element),
    deepClone: (obj) => window.AdminUtilsInstance.deepClone(obj),
    merge: (target, ...sources) => window.AdminUtilsInstance.merge(target, ...sources),
    isObject: (item) => window.AdminUtilsInstance.isObject(item),
    get: (obj, path, defaultValue) => window.AdminUtilsInstance.get(obj, path, defaultValue)
};

// ES6模块导出 (如果支持)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        CONFIG: window.CONFIG, 
        PerformanceMonitor: window.PerformanceMonitor, 
        CacheManager: window.CacheManager, 
        AdminUtils: window.AdminUtils 
    };
}

console.log('✅ 核心工具模块加载完成 (已合并core.js功能)'); 