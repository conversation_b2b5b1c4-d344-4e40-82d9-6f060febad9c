<?php
/**
 * 支付回调通知处理接口
 * 处理支付成功后的订单状态更新和商户通知
 */

// 引入通用函数
require_once dirname(__FILE__) . '/common.php';

// 获取全局变量
$merchant_info = $GLOBALS['merchant_info'];
$post_data = $GLOBALS['post_data'];
$pdo = $GLOBALS['pdo'];

try {
    // 验证必需参数
    $required_params = ['transaction_id', 'pay_amount', 'alipay_trade_no'];
    foreach ($required_params as $param) {
        if (!isset($post_data[$param]) || $post_data[$param] === '') {
            apiResponse(1, '缺少必需参数: ' . $param);
        }
    }
    
    $transaction_id = $post_data['transaction_id'];
    $pay_amount = $post_data['pay_amount'];
    $alipay_trade_no = $post_data['alipay_trade_no'];
    
    // 查询订单信息
    $stmt = $pdo->prepare("
        SELECT * FROM transactions 
        WHERE id = ? AND merchant_id = ? AND status = 'pending'
    ");
    $stmt->execute([$transaction_id, $merchant_info['id']]);
    $transaction = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$transaction) {
        apiResponse(1, '订单不存在或状态异常');
    }
    
    // 验证支付金额
    if (abs($pay_amount - $transaction['amount']) > 0.01) {
        apiResponse(1, '支付金额不匹配');
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        // 更新订单状态
        $stmt = $pdo->prepare("
            UPDATE transactions 
            SET 
                status = 'paid',
                pay_time = NOW(),
                pay_amount = ?,
                alipay_trade_no = ?,
                merchant_fee = amount * merchant_fee_rate,
                update_time = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$pay_amount, $alipay_trade_no, $transaction_id]);
        
        // 更新商户统计
        $stmt = $pdo->prepare("
            UPDATE merchants 
            SET 
                total_amount = total_amount + ?,
                total_transactions = total_transactions + 1,
                update_time = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$pay_amount, $merchant_info['id']]);
        
        // 提交事务
        $pdo->commit();
        
        // 发送回调通知给商户
        if (!empty($transaction['notification_url'])) {
            sendMerchantNotification($transaction, $pay_amount, $alipay_trade_no);
        }
        
        // 记录API调用日志
        $response_data = [
            'error_code' => 0,
            'error_message' => 'success',
            'transaction_id' => $transaction_id
        ];
        
        logApiCall($merchant_info['id'], 'pay_qrcode_notify', $post_data, $response_data, 'success');
        
        // 返回成功响应
        apiResponse(0, 'success');
        
    } catch (Exception $e) {
        // 回滚事务
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("pay_qrcode_notify异常: " . $e->getMessage());
    
    // 记录错误日志
    $error_response = [
        'error_code' => 1,
        'error_message' => '处理失败'
    ];
    
    logApiCall($merchant_info['id'], 'pay_qrcode_notify', $post_data, $error_response, 'error');
    
    apiResponse(1, '处理失败');
}

/**
 * 发送商户回调通知
 */
function sendMerchantNotification($transaction, $pay_amount, $alipay_trade_no) {
    global $merchant_info, $pdo;
    
    try {
        // 构建回调数据（与接口文档格式一致）
        $notify_data = [
            'id' => $transaction['id'],
            'amount' => $transaction['amount'],
            'pay_amount' => $pay_amount,
            'status' => 1, // 已付款
            'create_time' => date('Y-m-d H:i:s', strtotime($transaction['create_time'])),
            'pay_time' => date('Y-m-d H:i:s'),
            'order_no' => $transaction['merchant_order_no'],
            'fee' => $transaction['amount'] * $transaction['merchant_fee_rate'],
            'product_name' => $transaction['merchant_product_name'],
            'timestamp' => time()
        ];
        
        // 生成签名
        $sign_data = $notify_data;
        unset($sign_data['timestamp']);
        ksort($sign_data, SORT_STRING);
        
        $sign_str = http_build_query($sign_data, '', '&');
        $sign_str .= '&' . $merchant_info['id'] . '&' . $merchant_info['api_key'] . '&' . $notify_data['timestamp'];
        
        $notify_data['sign'] = strtoupper(hash('sha256', $sign_str));
        
        // 发送HTTP POST请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $transaction['notification_url']);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($notify_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'User-Agent: PayPal-Notify/1.0'
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        // 记录通知结果
        $notify_status = ($http_code == 200 && trim($response) == 'OK') ? 'success' : 'failed';
        
        $stmt = $pdo->prepare("
            INSERT INTO merchant_notification_tasks (
                merchant_id, transaction_id, notification_url, 
                request_data, response_data, status, http_code, 
                create_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $merchant_info['id'],
            $transaction['id'],
            $transaction['notification_url'],
            json_encode($notify_data, JSON_UNESCAPED_UNICODE),
            $response,
            $notify_status,
            $http_code
        ]);
        
        // 如果通知失败，可以考虑重试机制
        if ($notify_status == 'failed') {
            error_log("商户通知失败: URL={$transaction['notification_url']}, HTTP_CODE={$http_code}, RESPONSE={$response}");
        }
        
    } catch (Exception $e) {
        error_log("发送商户通知异常: " . $e->getMessage());
    }
}
?> 