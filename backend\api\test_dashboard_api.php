<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== Dashboard API 测试 ===\n";

try {
    // 模拟dashboard API调用
    $_POST['action'] = 'provider_dashboard_stats';
    $_POST['token'] = 'test_token'; // 模拟token
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    echo "1. 模拟POST数据设置完成\n";
    echo "   Action: " . $_POST['action'] . "\n";
    
    // 包含必要文件
    require_once dirname(__FILE__) . '/../utils/TenantAuth.php';
    require_once dirname(__FILE__) . '/../core/TenantManager.php';
    
    echo "2. 基础文件包含成功\n";
    
    // 创建对象
    $tenantAuth = new TenantAuth();
    $tenantManager = new TenantManager();
    $currentTenant = $tenantManager->getCurrentTenant();
    
    echo "3. 对象创建成功\n";
    echo "   当前租户类型: " . $currentTenant['tenant_type'] . "\n";
    
    // 测试业务处理器加载
    if ($currentTenant['tenant_type'] === 'provider') {
        echo "4. 尝试加载 ProviderAPI...\n";
        require_once dirname(__FILE__) . '/tenant/provider_api.php';
        echo "   ProviderAPI 文件加载成功\n";
        
        echo "5. 尝试创建 ProviderAPI 对象...\n";
        $handler = new ProviderAPI($tenantAuth, $tenantManager);
        echo "   ProviderAPI 对象创建成功\n";
        
        // 创建一个模拟用户
        $mockUser = array(
            'id' => 1,
            'username' => 'test_provider',
            'user_type' => 'provider',
            'profile_id' => 1
        );
        
        echo "6. 尝试调用 handleAction...\n";
        $result = $handler->handleAction('provider_dashboard_stats', 'POST', $mockUser);
        
        echo "7. API 调用成功！\n";
        echo "   结果: " . json_encode($result) . "\n";
    } else {
        echo "4. 当前租户不是provider类型: " . $currentTenant['tenant_type'] . "\n";
    }
    
} catch (ParseError $e) {
    echo "PHP语法错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "PHP错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 现在测试完整的API流程 ===\n";

try {
    // 直接模拟tenant_api.php的处理逻辑
    echo "8. 开始完整API流程测试...\n";
    
    // 验证租户
    if (!$currentTenant) {
        throw new Exception('无效的访问域名', 404);
    }
    echo "   租户验证通过\n";
    
    // 获取请求参数
    $action = $_POST['action'];
    $method = $_SERVER['REQUEST_METHOD'];
    echo "   Action: $action, Method: $method\n";
    
    // 这个action不需要认证，直接路由到业务处理器
    if ($action === 'provider_dashboard_stats') {
        echo "   路由到业务处理器...\n";
        
        // 根据租户类型路由
        switch ($currentTenant['tenant_type']) {
            case 'provider':
                require_once dirname(__FILE__) . '/tenant/provider_api.php';
                $handler = new ProviderAPI($tenantAuth, $tenantManager);
                
                // 模拟用户（通常从token获取）
                $user = array(
                    'id' => 1,
                    'username' => 'test_provider',
                    'user_type' => 'provider',
                    'profile_id' => 1
                );
                
                $result = $handler->handleAction($action, $method, $user);
                echo "9. 完整API流程成功！\n";
                echo "   结果: " . json_encode($result) . "\n";
                break;
                
            default:
                throw new Exception('不支持的租户类型', 400);
        }
    }
    
} catch (Exception $e) {
    echo "完整API流程错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
?> 