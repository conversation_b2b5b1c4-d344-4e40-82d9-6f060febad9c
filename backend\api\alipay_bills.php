<?php
/**
 * 支付宝账单管理API
 * 用于码商管理支付宝账单数据
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

require_once '../includes/TenantAuth.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

class AlipayBillsAPI {
    private $tenantAuth;
    private $userContext;
    
    public function __construct() {
        $this->tenantAuth = new TenantAuth();
        $this->userContext = $this->tenantAuth->getUserContext();
        
        // 验证用户权限 - 只有码商可以访问
        if (!$this->userContext || $this->userContext['user_type'] !== 'provider') {
            $this->sendError('无权访问支付宝账单功能', 403);
        }
    }
    
    /**
     * 处理API请求
     */
    public function handleRequest() {
        try {
            $method = $_SERVER['REQUEST_METHOD'];
            $path = $_GET['path'] ?? '';
            
            switch ($method) {
                case 'GET':
                    $this->handleGet($path);
                    break;
                case 'POST':
                    $this->handlePost($path);
                    break;
                case 'PUT':
                    $this->handlePut($path);
                    break;
                case 'DELETE':
                    $this->handleDelete($path);
                    break;
                default:
                    $this->sendError('不支持的请求方法', 405);
            }
        } catch (Exception $e) {
            error_log("支付宝账单API错误: " . $e->getMessage());
            $this->sendError('服务器内部错误: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 处理GET请求
     */
    private function handleGet($path) {
        switch ($path) {
            case 'list':
                $this->getBillsList();
                break;
            case 'stats':
                $this->getBillsStats();
                break;
            case 'detail':
                $this->getBillDetail();
                break;
            case 'unmatched':
                $this->getUnmatchedBills();
                break;
            case 'export':
                $this->exportBills();
                break;
            default:
                $this->sendError('无效的路径', 404);
        }
    }
    
    /**
     * 处理POST请求
     */
    private function handlePost($path) {
        switch ($path) {
            case 'manual_match':
                $this->manualMatchBill();
                break;
            case 'batch_process':
                $this->batchProcessBills();
                break;
            case 'ignore':
                $this->ignoreBill();
                break;
            default:
                $this->sendError('无效的路径', 404);
        }
    }
    
    /**
     * 获取账单列表
     */
    private function getBillsList() {
        $page = max(1, intval($_GET['page'] ?? 1));
        $pageSize = min(100, max(10, intval($_GET['pageSize'] ?? 20)));
        $offset = ($page - 1) * $pageSize;
        
        // 构建筛选条件
        $conditions = ['1=1'];
        $params = [];
        
        // 用户权限过滤 - 只能看到自己平台的账单
        $conditions[] = 'ab.alipay_account_id IN (SELECT id FROM alipay_accounts WHERE provider_id = ?)';
        $params[] = $this->userContext['user_id'];
        
        // 匹配状态筛选
        if (!empty($_GET['match_status'])) {
            $conditions[] = 'ab.match_status = ?';
            $params[] = $_GET['match_status'];
        }
        
        // 支付宝账户筛选
        if (!empty($_GET['alipay_account_id'])) {
            $conditions[] = 'ab.alipay_account_id = ?';
            $params[] = $_GET['alipay_account_id'];
        }
        
        // 设备筛选
        if (!empty($_GET['device_id'])) {
            $conditions[] = 'ab.device_id = ?';
            $params[] = $_GET['device_id'];
        }
        
        // 日期范围筛选
        if (!empty($_GET['start_date'])) {
            $conditions[] = 'DATE(ab.transaction_time) >= ?';
            $params[] = $_GET['start_date'];
        }
        
        if (!empty($_GET['end_date'])) {
            $conditions[] = 'DATE(ab.transaction_time) <= ?';
            $params[] = $_GET['end_date'];
        }
        
        $whereClause = implode(' AND ', $conditions);
        
        try {
            $db = $this->tenantAuth->getDB();
            
            // 获取总数
            $countSql = "SELECT COUNT(*) as total 
                        FROM alipay_bills ab 
                        LEFT JOIN alipay_accounts aa ON ab.alipay_account_id = aa.id 
                        WHERE {$whereClause}";
            
            $countStmt = $db->prepare($countSql);
            $countStmt->execute($params);
            $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // 获取账单列表
            $sql = "SELECT ab.*, 
                           aa.account_name as alipay_account_name,
                           aa.account_number as alipay_account_number,
                           o.order_no as matched_order_no
                    FROM alipay_bills ab 
                    LEFT JOIN alipay_accounts aa ON ab.alipay_account_id = aa.id
                    LEFT JOIN orders o ON ab.matched_order_id = o.id
                    WHERE {$whereClause}
                    ORDER BY ab.transaction_time DESC, ab.id DESC
                    LIMIT {$pageSize} OFFSET {$offset}";
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $bills = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 计算分页信息
            $totalPages = ceil($total / $pageSize);
            
            $this->sendSuccess([
                'bills' => $bills,
                'pagination' => [
                    'current_page' => $page,
                    'page_size' => $pageSize,
                    'total' => intval($total),
                    'total_pages' => $totalPages,
                    'has_next' => $page < $totalPages,
                    'has_prev' => $page > 1
                ]
            ]);
            
        } catch (Exception $e) {
            error_log("获取账单列表错误: " . $e->getMessage());
            $this->sendError('获取账单列表失败', 500);
        }
    }
    
    /**
     * 获取账单统计
     */
    private function getBillsStats() {
        try {
            $db = $this->tenantAuth->getDB();
            
            // 用户权限过滤
            $userCondition = 'ab.alipay_account_id IN (SELECT id FROM alipay_accounts WHERE provider_id = ?)';
            $params = [$this->userContext['user_id']];
            
            // 添加筛选条件
            $conditions = [$userCondition];
            
            if (!empty($_GET['start_date'])) {
                $conditions[] = 'DATE(ab.transaction_time) >= ?';
                $params[] = $_GET['start_date'];
            }
            
            if (!empty($_GET['end_date'])) {
                $conditions[] = 'DATE(ab.transaction_time) <= ?';
                $params[] = $_GET['end_date'];
            }
            
            $whereClause = implode(' AND ', $conditions);
            
            $sql = "SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN match_status = 'matched' THEN 1 ELSE 0 END) as matched,
                        SUM(CASE WHEN match_status = 'unmatched' THEN 1 ELSE 0 END) as unmatched,
                        SUM(CASE WHEN match_status = 'manual' THEN 1 ELSE 0 END) as manual,
                        SUM(CASE WHEN match_status = 'ignored' THEN 1 ELSE 0 END) as ignored,
                        SUM(CASE WHEN transaction_type = '收入' THEN amount ELSE 0 END) as total_income,
                        SUM(CASE WHEN transaction_type = '支出' THEN amount ELSE 0 END) as total_expense,
                        SUM(amount) as total_amount
                    FROM alipay_bills ab 
                    WHERE {$whereClause}";
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // 转换数据类型
            foreach ($stats as $key => $value) {
                $stats[$key] = is_numeric($value) ? floatval($value) : $value;
            }
            
            $this->sendSuccess($stats);
            
        } catch (Exception $e) {
            error_log("获取账单统计错误: " . $e->getMessage());
            $this->sendError('获取账单统计失败', 500);
        }
    }
    
    /**
     * 获取账单详情
     */
    private function getBillDetail() {
        $billId = intval($_GET['id'] ?? 0);
        
        if ($billId <= 0) {
            $this->sendError('无效的账单ID', 400);
        }
        
        try {
            $db = $this->tenantAuth->getDB();
            
            $sql = "SELECT ab.*, 
                           aa.account_name as alipay_account_name,
                           aa.account_number as alipay_account_number,
                           o.order_no as matched_order_no,
                           o.amount as order_amount,
                           o.status as order_status
                    FROM alipay_bills ab 
                    LEFT JOIN alipay_accounts aa ON ab.alipay_account_id = aa.id
                    LEFT JOIN orders o ON ab.matched_order_id = o.id
                    WHERE ab.id = ? 
                    AND ab.alipay_account_id IN (SELECT id FROM alipay_accounts WHERE provider_id = ?)";
            
            $stmt = $db->prepare($sql);
            $stmt->execute([$billId, $this->userContext['user_id']]);
            $bill = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$bill) {
                $this->sendError('账单不存在或无权访问', 404);
            }
            
            $this->sendSuccess($bill);
            
        } catch (Exception $e) {
            error_log("获取账单详情错误: " . $e->getMessage());
            $this->sendError('获取账单详情失败', 500);
        }
    }
    
    /**
     * 手动匹配账单
     */
    private function manualMatchBill() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $billId = intval($input['bill_id'] ?? 0);
        $orderId = intval($input['order_id'] ?? 0);
        $notes = trim($input['notes'] ?? '');
        
        if ($billId <= 0) {
            $this->sendError('无效的账单ID', 400);
        }
        
        if ($orderId <= 0) {
            $this->sendError('无效的订单ID', 400);
        }
        
        try {
            $db = $this->tenantAuth->getDB();
            $db->beginTransaction();
            
            // 验证账单权限
            $billCheckSql = "SELECT ab.id 
                            FROM alipay_bills ab 
                            LEFT JOIN alipay_accounts aa ON ab.alipay_account_id = aa.id
                            WHERE ab.id = ? AND aa.provider_id = ? AND ab.match_status IN ('unmatched', 'manual')";
            
            $billCheckStmt = $db->prepare($billCheckSql);
            $billCheckStmt->execute([$billId, $this->userContext['user_id']]);
            
            if (!$billCheckStmt->fetch()) {
                $db->rollBack();
                $this->sendError('账单不存在或无法匹配', 404);
            }
            
            // 验证订单存在
            $orderCheckSql = "SELECT id FROM orders WHERE id = ?";
            $orderCheckStmt = $db->prepare($orderCheckSql);
            $orderCheckStmt->execute([$orderId]);
            
            if (!$orderCheckStmt->fetch()) {
                $db->rollBack();
                $this->sendError('订单不存在', 404);
            }
            
            // 更新账单匹配信息
            $updateSql = "UPDATE alipay_bills 
                         SET match_status = 'manual',
                             matched_order_id = ?,
                             matched_at = NOW(),
                             matched_by = 'manual',
                             process_notes = ?,
                             is_processed = 1
                         WHERE id = ?";
            
            $updateStmt = $db->prepare($updateSql);
            $updateStmt->execute([$orderId, $notes, $billId]);
            
            $db->commit();
            
            // 记录操作日志
            $this->logOperation('manual_match_bill', [
                'bill_id' => $billId,
                'order_id' => $orderId,
                'notes' => $notes
            ]);
            
            $this->sendSuccess(['message' => '账单匹配成功']);
            
        } catch (Exception $e) {
            if ($db->inTransaction()) {
                $db->rollBack();
            }
            error_log("手动匹配账单错误: " . $e->getMessage());
            $this->sendError('手动匹配失败', 500);
        }
    }
    
    /**
     * 忽略账单
     */
    private function ignoreBill() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $billId = intval($input['bill_id'] ?? 0);
        $reason = trim($input['reason'] ?? '');
        
        if ($billId <= 0) {
            $this->sendError('无效的账单ID', 400);
        }
        
        try {
            $db = $this->tenantAuth->getDB();
            
            // 验证账单权限
            $checkSql = "SELECT ab.id 
                        FROM alipay_bills ab 
                        LEFT JOIN alipay_accounts aa ON ab.alipay_account_id = aa.id
                        WHERE ab.id = ? AND aa.provider_id = ?";
            
            $checkStmt = $db->prepare($checkSql);
            $checkStmt->execute([$billId, $this->userContext['user_id']]);
            
            if (!$checkStmt->fetch()) {
                $this->sendError('账单不存在或无权访问', 404);
            }
            
            // 更新账单状态
            $updateSql = "UPDATE alipay_bills 
                         SET match_status = 'ignored',
                             process_notes = ?,
                             is_processed = 1
                         WHERE id = ?";
            
            $updateStmt = $db->prepare($updateSql);
            $updateStmt->execute([$reason, $billId]);
            
            // 记录操作日志
            $this->logOperation('ignore_bill', [
                'bill_id' => $billId,
                'reason' => $reason
            ]);
            
            $this->sendSuccess(['message' => '账单已忽略']);
            
        } catch (Exception $e) {
            error_log("忽略账单错误: " . $e->getMessage());
            $this->sendError('忽略账单失败', 500);
        }
    }
    
    /**
     * 记录操作日志
     */
    private function logOperation($action, $data) {
        try {
            $db = $this->tenantAuth->getDB();
            
            $sql = "INSERT INTO operation_logs (user_id, user_type, action, data, ip_address, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW())";
            
            $stmt = $db->prepare($sql);
            $stmt->execute([
                $this->userContext['user_id'],
                $this->userContext['user_type'],
                $action,
                json_encode($data),
                $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            
        } catch (Exception $e) {
            error_log("记录操作日志失败: " . $e->getMessage());
        }
    }
    
    /**
     * 发送成功响应
     */
    private function sendSuccess($data) {
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    /**
     * 发送错误响应
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message,
            'code' => $code,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

// 执行API
try {
    $api = new AlipayBillsAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?> 