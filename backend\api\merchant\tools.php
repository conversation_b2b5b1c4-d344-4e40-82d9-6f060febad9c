<?php
// 使用TenantAuth多租户认证系统
require_once dirname(__FILE__) . '/../../utils/TenantAuth.php';

// 初始化多租户认证
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();

// 验证是否为商户用户
if ($currentUser['user_type'] !== 'merchant') {
    $auth->sendForbidden('只有商户用户可以访问此接口');
}

// 获取数据库连接
$db = $auth->getDatabase();

// 直接从数据库获取商户信息
try {
    $stmt = $db->query("
        SELECT * FROM merchants 
        WHERE user_id = ? AND status IN ('active', 'approved')
    ", array($currentUser['id']));
    
    $currentMerchant = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$currentMerchant) {
        $auth->sendError('商户信息不存在或状态异常', 403);
    }
} catch (Exception $e) {
    error_log("获取商户信息失败: " . $e->getMessage());
    $auth->sendError('获取商户信息失败', 500);
}

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'get_dashboard':
            handleGetDashboard($auth);
            break;
        case 'get_orders':
            handleGetOrders($auth);
            break;
        case 'get_statistics':
            handleGetStatistics($auth);
            break;
        case 'get_products':
            handleGetProducts($auth);
            break;
        case 'get_key_info':
            handleGetKeyInfo($auth);
            break;
        case 'get_call_stats':
            handleGetCallStats($auth);
            break;
        case 'get_config':
            handleGetConfig($auth);
            break;
        case 'get_ip_whitelist':
            handleGetIpWhitelist($auth);
            break;
        case 'add_ip_whitelist':
            handleAddIpWhitelist($auth);
            break;
        case 'remove_ip_whitelist':
            handleRemoveIpWhitelist($auth);
            break;
        case 'update_profile':
            handleUpdateProfile($auth);
            break;
        case 'update_callback_url':
            handleUpdateCallbackUrl($auth);
            break;
        case 'update_ip_whitelist':
            handleUpdateIpWhitelist($auth);
            break;
        case 'regenerate_api_key':
            handleRegenerateApiKey($auth);
            break;
        default:
            $auth->sendError('接口不存在', 404);
    }
} catch (Exception $e) {
    error_log("Merchant tools error: " . $e->getMessage());
    $auth->sendError('系统错误: ' . $e->getMessage(), 500);
}

// 获取仪表板数据
function handleGetDashboard($auth) {
    global $currentMerchant;
    
    // 记录操作日志
    $auth->logUserAction('view_dashboard', 'merchant', $currentMerchant['id'], '查看仪表板');
    
    $merchantId = $currentMerchant['id'];
    
    // 获取今日数据
    $todayStats = getTodayStatistics($merchantId);
    
    // 获取本月数据
    $monthStats = getMonthStatistics($merchantId);
    
    // 获取最近订单
    $recentOrders = getRecentOrders($merchantId, 10);
    
    // 获取产品状态
    $productStats = getProductStatistics($merchantId);
    
    $dashboard = array(
        'merchant_info' => array(
            'id' => $currentMerchant['id'],
            'name' => $currentMerchant['name'],
            'status' => $currentMerchant['status'],
            'balance' => $currentMerchant['balance'],
            'created_at' => $currentMerchant['created_at']
        ),
        'today_stats' => $todayStats,
        'month_stats' => $monthStats,
        'recent_orders' => $recentOrders,
        'product_stats' => $productStats,
        'api_info' => array(
            'developer_id' => $currentMerchant['id'],
            'api_key_preview' => substr($currentMerchant['api_key'], 0, 8) . '...',
            'callback_url' => $currentMerchant['callback_url'],
            'ip_whitelist_count' => count(explode(',', $currentMerchant['ip_whitelist']))
        )
    );
    
    $auth->sendSuccess($dashboard, '仪表板数据获取成功');
}

// 获取订单列表
function handleGetOrders($auth) {
    global $currentMerchant;
    
    $pagination = getPaginationParams();
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    $startDate = isset($_GET['start_date']) ? $_GET['start_date'] : '';
    $endDate = isset($_GET['end_date']) ? $_GET['end_date'] : '';
    $orderNo = isset($_GET['order_no']) ? $_GET['order_no'] : '';
    
    // 记录操作日志
    $auth->logUserAction('view_orders', 'merchant', $currentMerchant['id'], '查看订单列表');
    
    $orders = getOrdersList($currentMerchant['id'], $pagination, $status, $startDate, $endDate, $orderNo);
    
    $auth->sendSuccess($orders, '订单列表获取成功');
}

// 获取统计数据
function handleGetStatistics($auth) {
    global $currentMerchant;
    
    $type = isset($_GET['type']) ? $_GET['type'] : 'daily';
    $days = isset($_GET['days']) ? intval($_GET['days']) : 30;
    
    // 记录操作日志
    $auth->logUserAction('view_statistics', 'merchant', $currentMerchant['id'], '查看统计数据');
    
    $statistics = getDetailedStatistics($currentMerchant['id'], $type, $days);
    
    $auth->sendSuccess($statistics, '统计数据获取成功');
}

// 获取产品列表
function handleGetProducts($auth) {
    global $currentMerchant;
    
    // 记录操作日志
    $auth->logUserAction('view_products', 'merchant', $currentMerchant['id'], '查看产品列表');
    
    $products = getMerchantProducts($currentMerchant['id']);
    
    // 返回符合前端期望的数据格式
    $response = array(
        'products' => $products,
        'total' => count($products)
    );
    
    $auth->sendSuccess($response, '产品列表获取成功');
}

// 获取商户产品列表（从api_docs.php复制过来避免依赖）
function getMerchantProducts($merchantId) {
    global $db;
    
    try {
        $stmt = $db->query("
            SELECT id, name, min_amount, max_amount, fee_rate, status, available_quota, created_at
            FROM products 
            WHERE merchant_id = ? 
            ORDER BY created_at DESC
        ", array($merchantId));
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("获取商户产品失败: " . $e->getMessage());
        // 如果products表不存在，返回空数组
        return array();
    }
}

// 获取API密钥信息
function handleGetKeyInfo($auth) {
    global $currentMerchant, $db;
    
    // 记录操作日志
    $auth->logUserAction('view_api_key', 'merchant', $currentMerchant['id'], '查看API密钥信息');
    
    // 从数据库获取完整的商户信息，包括API密钥
    try {
        $stmt = $db->query("
            SELECT id, api_key, callback_url, ip_whitelist, status, created_at, 
                   merchant_code, balance, service_rate, platform_id
            FROM merchants 
            WHERE id = ?
        ", array($currentMerchant['id']));
        
        $merchantData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$merchantData) {
            $auth->sendError('商户信息不存在', 404);
            return;
        }
        
        $keyInfo = array(
            'developer_id' => $merchantData['id'],
            'api_key' => isset($merchantData['api_key']) ? $merchantData['api_key'] : '',
            'callback_url' => isset($merchantData['callback_url']) ? $merchantData['callback_url'] : '',
            'ip_whitelist' => isset($merchantData['ip_whitelist']) ? $merchantData['ip_whitelist'] : '',
            'status' => isset($merchantData['status']) ? $merchantData['status'] : 'pending',
            'created_at' => isset($merchantData['created_at']) ? $merchantData['created_at'] : '',
            'merchant_code' => isset($merchantData['merchant_code']) ? $merchantData['merchant_code'] : '',
            'platform_id' => isset($merchantData['platform_id']) ? $merchantData['platform_id'] : 1,
            'balance' => isset($merchantData['balance']) ? $merchantData['balance'] : '0.00',
            'service_rate' => isset($merchantData['service_rate']) ? $merchantData['service_rate'] : '0.0050');
        
        $auth->sendSuccess($keyInfo, 'API密钥信息获取成功');
        
    } catch (Exception $e) {
        error_log("获取API密钥信息失败: " . $e->getMessage());
        $auth->sendError('获取API密钥信息失败: ' . $e->getMessage(), 500);
    }
}

// 获取调用统计
function handleGetCallStats($auth) {
    global $currentMerchant, $db;
    
    // 记录操作日志
    $auth->logUserAction('view_call_stats', 'merchant', $currentMerchant['id'], '查看调用统计');
    
    $merchantId = $currentMerchant['id'];
    $days = isset($_GET['days']) ? intval($_GET['days']) : 7;
    
    try {
        // 获取API调用统计
        $stmt = $db->query("
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as total_calls,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as success_calls,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_calls
            FROM api_logs 
            WHERE merchant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        ", array($merchantId, $days));
        
        $dailyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取总体统计
        $stmt = $db->query("
            SELECT 
                COUNT(*) as total_calls,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as success_calls,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_calls,
                AVG(response_time) as avg_response_time
            FROM api_logs 
            WHERE merchant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        ", array($merchantId, $days));
        
        $totalStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stats = array(
            'daily_stats' => $dailyStats,
            'total_stats' => $totalStats,
            'period' => $days . '天'
        );
        
        $auth->sendSuccess($stats, '调用统计获取成功');
        
    } catch (Exception $e) {
        error_log("获取调用统计失败: " . $e->getMessage());
        // 如果api_logs表不存在，返回空统计
        $stats = array(
            'daily_stats' => array(),
            'total_stats' => array(
                'total_calls' => 0,
                'success_calls' => 0,
                'failed_calls' => 0,
                'avg_response_time' => 0
            ),
            'period' => $days . '天'
        );
        $auth->sendSuccess($stats, '调用统计获取成功');
    }
}

// 更新回调地址
function handleUpdateCallbackUrl($auth) {
    global $currentMerchant, $db;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    if (empty($input['callback_url'])) {
        $auth->sendError('回调地址不能为空', 400);
    }
    
    if (!filter_var($input['callback_url'], FILTER_VALIDATE_URL)) {
        $auth->sendError('回调地址格式不正确', 400);
    }
    
    $callbackUrl = $input['callback_url'];
    
    try {
        $db->query("UPDATE merchants SET callback_url = ?, updated_at = NOW() WHERE id = ?", 
                  array($callbackUrl, $currentMerchant['id']));
        
        // 记录操作日志
        $auth->logUserAction('update_callback_url', 'merchant', $currentMerchant['id'], 
                           '更新回调地址: ' . $callbackUrl);
        
        $auth->sendSuccess(array('callback_url' => $callbackUrl), '回调地址更新成功');
        
    } catch (Exception $e) {
        $auth->sendError('回调地址更新失败: ' . $e->getMessage(), 500);
    }
}

// 重新生成API密钥
function handleRegenerateApiKey($auth) {
    global $currentMerchant, $db;
    
    try {
        $newApiKey = generateApiKey();
        
        $db->query("UPDATE merchants SET api_key = ?, updated_at = NOW() WHERE id = ?", 
                  array($newApiKey, $currentMerchant['id']));
        
        // 记录操作日志
        $auth->logUserAction('regenerate_api_key', 'merchant', $currentMerchant['id'], '重新生成API密钥');
        
        $auth->sendSuccess(array(
            'api_key' => $newApiKey
        ), 'API密钥重新生成成功');
        
    } catch (Exception $e) {
        $auth->sendError('API密钥重新生成失败: ' . $e->getMessage(), 500);
    }
}

// 获取今日统计
function getTodayStatistics($merchantId) {
    global $db;
    
    try {
        $stmt = $db->query("
            SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 1 THEN 1 END) as paid_orders,
                COUNT(CASE WHEN status = 0 THEN 1 END) as pending_orders,
                COUNT(CASE WHEN status = 2 THEN 1 END) as timeout_orders,
                COALESCE(SUM(CASE WHEN status = 1 THEN amount ELSE 0 END), 0) as total_amount,
                COALESCE(SUM(CASE WHEN status = 1 THEN fee ELSE 0 END), 0) as total_fee
            FROM orders 
            WHERE merchant_id = ? AND DATE(created_at) = CURDATE()
        ", array($merchantId));
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("获取今日统计失败: " . $e->getMessage());
        return array(
            'total_orders' => 0,
            'paid_orders' => 0,
            'pending_orders' => 0,
            'timeout_orders' => 0,
            'total_amount' => 0,
            'total_fee' => 0
        );
    }
}

// 获取本月统计
function getMonthStatistics($merchantId) {
    global $db;
    
    try {
        $stmt = $db->query("
            SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 1 THEN 1 END) as paid_orders,
                COALESCE(SUM(CASE WHEN status = 1 THEN amount ELSE 0 END), 0) as total_amount,
                COALESCE(SUM(CASE WHEN status = 1 THEN fee ELSE 0 END), 0) as total_fee
            FROM orders 
            WHERE merchant_id = ? AND YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())
        ", array($merchantId));
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("获取本月统计失败: " . $e->getMessage());
        return array(
            'total_orders' => 0,
            'paid_orders' => 0,
            'total_amount' => 0,
            'total_fee' => 0
        );
    }
}

// 获取最近订单
function getRecentOrders($merchantId, $limit = 10) {
    global $db;
    
    try {
        $stmt = $db->query("
            SELECT o.*, p.name as product_name
            FROM orders o
            LEFT JOIN products p ON o.product_id = p.id
            WHERE o.merchant_id = ?
            ORDER BY o.created_at DESC
            LIMIT ?
        ", array($merchantId, $limit));
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("获取最近订单失败: " . $e->getMessage());
        return array();
    }
}

// 获取产品统计
function getProductStatistics($merchantId) {
    global $db;
    
    try {
        $stmt = $db->query("
            SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_products,
                COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_products,
                COALESCE(SUM(available_quota), 0) as total_quota
            FROM products 
            WHERE merchant_id = ?
        ", array($merchantId));
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("获取产品统计失败: " . $e->getMessage());
        return array(
            'total_products' => 0,
            'active_products' => 0,
            'inactive_products' => 0,
            'total_quota' => 0
        );
    }
}

// 获取订单列表
function getOrdersList($merchantId, $pagination, $status = '', $startDate = '', $endDate = '', $orderNo = '') {
    global $db;
    
    try {
        $where = "WHERE o.merchant_id = ?";
        $params = array($merchantId);
        
        if ($status !== '') {
            $where .= " AND o.status = ?";
            $params[] = $status;
        }
        
        if ($startDate) {
            $where .= " AND DATE(o.created_at) >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $where .= " AND DATE(o.created_at) <= ?";
            $params[] = $endDate;
        }
        
        if ($orderNo) {
            $where .= " AND o.order_no LIKE ?";
            $params[] = "%{$orderNo}%";
        }
        
        // 获取总数
        $countStmt = $db->query("SELECT COUNT(*) FROM orders o {$where}", $params);
        $total = $countStmt->fetchColumn();
        
        // 获取数据
        $sql = "
            SELECT o.*, p.name as product_name
            FROM orders o
            LEFT JOIN products p ON o.product_id = p.id
            {$where}
            ORDER BY o.created_at DESC
            LIMIT {$pagination['limit']} OFFSET {$pagination['offset']}
        ";
        
        $stmt = $db->query($sql, $params);
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return buildPaginatedResponse($orders, $total, $pagination['page'], $pagination['limit']);
        
    } catch (Exception $e) {
        error_log("获取订单列表失败: " . $e->getMessage());
        return buildPaginatedResponse(array(), 0, $pagination['page'], $pagination['limit']);
    }
}

// 获取详细统计
function getDetailedStatistics($merchantId, $type = 'daily', $days = 30) {
    global $db;
    
    try {
        $dateFormat = $type === 'daily' ? '%Y-%m-%d' : '%Y-%m';
        
        $stmt = $db->query("
            SELECT 
                DATE_FORMAT(created_at, '{$dateFormat}') as date_key,
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 1 THEN 1 END) as paid_orders,
                COALESCE(SUM(CASE WHEN status = 1 THEN amount ELSE 0 END), 0) as total_amount,
                COALESCE(SUM(CASE WHEN status = 1 THEN fee ELSE 0 END), 0) as total_fee
            FROM orders 
            WHERE merchant_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE_FORMAT(created_at, '{$dateFormat}')
            ORDER BY date_key DESC
        ", array($merchantId, $days));
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return array(
            'type' => $type,
            'days' => $days,
            'data' => $data
        );
        
    } catch (Exception $e) {
        error_log("获取详细统计失败: " . $e->getMessage());
        return array(
            'type' => $type,
            'days' => $days,
            'data' => array()
        );
    }
}

// 获取IP白名单
function handleGetIpWhitelist($auth) {
    global $currentMerchant, $db;
    
    // 记录操作日志
    $auth->logUserAction('view_ip_whitelist', 'merchant', $currentMerchant['id'], '查看IP白名单');
    
    try {
        // 从数据库获取当前商户的IP白名单
        $stmt = $db->query("
            SELECT ip_whitelist 
            FROM merchants 
            WHERE id = ?
        ", array($currentMerchant['id']));
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            $auth->sendError('商户信息不存在', 404);
            return;
        }
        
        // 解析IP白名单字符串为数组
        $ipWhitelistStr = isset($result['ip_whitelist']) ? $result['ip_whitelist'] : '';
        $ipList = array();
        
        if (!empty($ipWhitelistStr)) {
            // 按逗号分割IP地址
            $ips = explode(',', $ipWhitelistStr);
            foreach ($ips as $index => $ip) {
                $ip = trim($ip);
                if (!empty($ip)) {
                    $ipList[] = array(
                        'index' => $index,
                        'ip_address' => $ip,
                        'description' => '', // 暂时没有描述字段
                        'status' => 'active',
                        'created_at' => '' // 暂时没有时间字段
                    );
                }
            }
        }
        
        // 获取当前用户IP
        $currentIp = getClientIP();
        
        // 前端期望data.data直接是IP数组
        $auth->sendSuccess($ipList, 'IP白名单获取成功');
        
    } catch (Exception $e) {
        error_log("获取IP白名单失败: " . $e->getMessage());
        $auth->sendError('获取IP白名单失败: ' . $e->getMessage(), 500);
    }
}

// 获取客户端IP地址
function getClientIP() {
    $ip_keys = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR');
    
    foreach ($ip_keys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
}

// 添加IP到白名单
function handleAddIpWhitelist($auth) {
    global $currentMerchant, $db;
    
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $auth->sendError('无效的请求数据', 400);
        return;
    }
    
    $ipAddress = isset($input['ip_address']) ? trim($input['ip_address']) : 
                 (isset($input['ip']) ? trim($input['ip']) : '');
    
    if (empty($ipAddress)) {
        $auth->sendError('IP地址不能为空', 400);
        return;
    }
    
    // 验证IP格式
    if (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
        $auth->sendError('IP地址格式不正确', 400);
        return;
    }
    
    try {
        // 获取当前IP白名单
        $stmt = $db->query("SELECT ip_whitelist FROM merchants WHERE id = ?", array($currentMerchant['id']));
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            $auth->sendError('商户信息不存在', 404);
            return;
        }
        
        $currentWhitelist = isset($result['ip_whitelist']) ? $result['ip_whitelist'] : '';
        $ipList = array();
        
        // 解析现有IP列表
        if (!empty($currentWhitelist)) {
            $ipList = array_map('trim', explode(',', $currentWhitelist));
            $ipList = array_filter($ipList); // 移除空值
        }
        
        // 检查IP是否已存在
        if (in_array($ipAddress, $ipList)) {
            $auth->sendError('IP地址已存在于白名单中', 400);
            return;
        }
        
        // 检查白名单数量限制
        if (count($ipList) >= 50) {
            $auth->sendError('IP白名单数量已达上限(50个)', 400);
            return;
        }
        
        // 添加新IP
        $ipList[] = $ipAddress;
        $newWhitelist = implode(',', $ipList);
        
        // 更新数据库
        $updateStmt = $db->query(
            "UPDATE merchants SET ip_whitelist = ?, updated_at = NOW() WHERE id = ?",
            array($newWhitelist, $currentMerchant['id'])
        );
        
        if ($updateStmt) {
            // 记录操作日志
            $auth->logUserAction('add_ip_whitelist', 'merchant', $currentMerchant['id'], '添加IP白名单: ' . $ipAddress);
            
            $auth->sendSuccess(array('ip_address' => $ipAddress), 'IP地址添加成功');
        } else {
            $auth->sendError('添加IP地址失败', 500);
        }
        
    } catch (Exception $e) {
        error_log("添加IP白名单失败: " . $e->getMessage());
        $auth->sendError('添加IP地址失败: ' . $e->getMessage(), 500);
    }
}

// 从白名单移除IP
function handleRemoveIpWhitelist($auth) {
    global $currentMerchant, $db;
    
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $auth->sendError('无效的请求数据', 400);
        return;
    }
    
    $index = isset($input['index']) ? intval($input['index']) : -1;
    
    if ($index < 0) {
        $auth->sendError('索引参数无效', 400);
        return;
    }
    
    try {
        // 获取当前IP白名单
        $stmt = $db->query("SELECT ip_whitelist FROM merchants WHERE id = ?", array($currentMerchant['id']));
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$result) {
            $auth->sendError('商户信息不存在', 404);
            return;
        }
        
        $currentWhitelist = isset($result['ip_whitelist']) ? $result['ip_whitelist'] : '';
        
        if (empty($currentWhitelist)) {
            $auth->sendError('IP白名单为空', 400);
            return;
        }
        
        $ipList = array_map('trim', explode(',', $currentWhitelist));
        $ipList = array_filter($ipList); // 移除空值
        $ipList = array_values($ipList); // 重新索引
        
        // 检查索引是否有效
        if ($index >= count($ipList)) {
            $auth->sendError('索引超出范围', 400);
            return;
        }
        
        $removedIp = $ipList[$index];
        
        // 移除指定索引的IP
        unset($ipList[$index]);
        $ipList = array_values($ipList); // 重新索引
        
        $newWhitelist = implode(',', $ipList);
        
        // 更新数据库
        $updateStmt = $db->query(
            "UPDATE merchants SET ip_whitelist = ?, updated_at = NOW() WHERE id = ?",
            array($newWhitelist, $currentMerchant['id'])
        );
        
        if ($updateStmt) {
            // 记录操作日志
            $auth->logUserAction('remove_ip_whitelist', 'merchant', $currentMerchant['id'], '移除IP白名单: ' . $removedIp);
            
            $auth->sendSuccess(array('removed_ip' => $removedIp), 'IP地址移除成功');
        } else {
            $auth->sendError('移除IP地址失败', 500);
        }
        
    } catch (Exception $e) {
        error_log("移除IP白名单失败: " . $e->getMessage());
        $auth->sendError('移除IP地址失败: ' . $e->getMessage(), 500);
    }
}

// 获取商户配置信息 - 临时简化版本排00错误
function handleGetConfig($auth) {
    // 直接返回简单的测试数据，不使用任何复杂逻辑
    try {
        $testConfig = array(
            'merchant_info' => array(
                'id' => 1,
                'name' => '测试商户',
                'company_name' => '测试公司',
                'description' => '测试描述',
                'contact_person' => '测试联系人',
                'contact_phone' => '***********',
                'contact_email' => '<EMAIL>',
                'business_license' => '测试执照',
                'status' => 'active'
            ),
            'api_config' => array(
                'developer_id' => 1,
                'api_key' => 'test_api_key_123456',
                'callback_url' => 'https://example.com/callback',
                'ip_whitelist' => array()
            ),
            'account_info' => array(
                'balance' => '1000.00',
                'service_rate' => '0.05',
                'created_at' => '2024-01-01 00:00:00',
                'updated_at' => '2024-01-01 00:00:00'
            )
        );
        
        // 直接输出JSON，不使用ErrorHandler
        header('Content-Type: application/json');
        echo json_encode(array(
            'code' => 200,
            'message' => '配置信息获取成功',
            'data' => $testConfig
        ));
        exit;
        
    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode(array(
            'code' => 500,
            'message' => '错误: ' . $e->getMessage()
        ));
        exit;
    }
}

// 更新商户个人资料
function handleUpdateProfile($auth) {
    global $db, $currentMerchant;
    
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $auth->sendError('无效的请求数据', 400);
        return;
    }
    
    // 验证必填字段
    $contactPerson = isset($input['contact_person']) ? trim($input['contact_person']) : '';
    $contactPhone = isset($input['contact_phone']) ? trim($input['contact_phone']) : '';
    $contactEmail = isset($input['contact_email']) ? trim($input['contact_email']) : '';
    $description = isset($input['description']) ? trim($input['description']) : '';
    $callbackUrl = isset($input['callback_url']) ? trim($input['callback_url']) : '';
    
    // 验证邮箱格式
    if (!empty($contactEmail) && !filter_var($contactEmail, FILTER_VALIDATE_EMAIL)) {
        $auth->sendError('邮箱格式不正确', 400);
        return;
    }
    
    // 验证回调URL格式
    if (!empty($callbackUrl) && !filter_var($callbackUrl, FILTER_VALIDATE_URL)) {
        $auth->sendError('回调URL格式不正确', 400);
        return;
    }
    
    try {
        // 更新商户信息（基于新表结构）
        $sql = "UPDATE merchants SET 
                merchant_code = ?, 
                callback_url = ?,
                service_rate = ?,
                updated_at = NOW()
                WHERE id = ?";
        
        $merchantCode = isset($input['merchant_code']) ? trim($input['merchant_code']) : '';
        $serviceRate = isset($input['service_rate']) ? floatval($input['service_rate']) : 0.0050;
        
        $result = $db->query($sql, array(
            $merchantCode,
            $callbackUrl,
            $serviceRate,
            $currentMerchant['id']
        ));
        
        if ($result) {
            // 记录操作日志
            $auth->logUserAction('update_profile', 'merchant', $currentMerchant['id'], '更新个人资料');
            
            $auth->sendSuccess(array(), '个人资料更新成功');
        } else {
            $auth->sendError('更新失败，请重试', 500);
        }
        
    } catch (Exception $e) {
        error_log("更新商户个人资料失败: " . $e->getMessage());
        $auth->sendError('系统错误，请稍后重试', 500);
    }
}

// 生成API密钥 - PHP 5.6兼容版本
function generateApiKey() {
    // PHP 5.6兼容的随机字符串生成
    if (function_exists('random_bytes')) {
        // PHP 7.0+
        return bin2hex(random_bytes(32));
    } else {
        // PHP 5.6兼容方式
        return hash('sha256', uniqid(mt_rand(), true) . microtime(true));
    }
}

// 构建分页响应
function buildPaginatedResponse($data, $total, $page, $limit) {
    return array(
        'data' => $data,
        'pagination' => array(
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        )
    );
}

// 分页处理
function getPaginationParams() {
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    return array(
        'page' => $page,
        'limit' => $limit,
        'offset' => $offset
    );
}
?> 
