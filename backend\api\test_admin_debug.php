<?php
// 简化版admin.php测试文件 - 逐步排查500错误
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义管理面板常量，用于子模块验证
define('IN_ADMIN_PANEL', true);

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    echo json_encode(array(
        'code' => 200,
        'message' => '测试文件运行正常',
        'data' => array(
            'test_stage' => 'basic_test',
            'timestamp' => date('Y-m-d H:i:s')
        )
    ));
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(array(
        'code' => 500,
        'message' => '测试失败: ' . $e->getMessage()
    ));
}
?> 