<?php
/**
 * 安全日志管理接口 - 简化版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

// 检查安全权限
if (!$auth->checkPermission('security.view')) {
    $auth->sendForbidden('无权限访问安全日志管理');
}

try {
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($action) {
        case 'security_logs':
            handleSecurityLogs($auth);
            break;
        case 'totp':
            handleTOTP($auth);
            break;
        case 'totp_status':
            handleGetTOTPStatus($auth);
            break;
        case 'setup_totp':
            handleSetupTOTP($auth);
            break;
        case 'stats':
            handleSecurityStats($auth);
            break;
        case 'recent':
            handleRecentEvents($auth);
            break;
        default:
            // 默认安全日志管理
            handleSecurityLogs($auth);
    }
} catch (Exception $e) {
    error_log("Security Logs API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 安全日志管理
function handleSecurityLogs($auth) {
    global $method;
    
    switch ($method) {
        case 'GET':
            handleGetSecurityLogs($auth);
            break;
        default:
            $auth->sendError('不支持的请求方法', 405);
    }
}

// 获取安全日志
function handleGetSecurityLogs($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('security.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        $page = intval(isset($_GET['page']) ? $_GET['page'] : 1);
        $limit = intval(isset($_GET['limit']) ? $_GET['limit'] : 20);
        $offset = ($page - 1) * $limit;
        
        $userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
        $eventType = isset($_GET['event_type']) ? $_GET['event_type'] : '';
        $dateRange = isset($_GET['date_range']) ? $_GET['date_range'] : '';
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        
        $whereConditions = array();
        $params = array();
        
        // 数据隔离：非管理员只能查看自己的日志
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = 'sl.user_id = ?';
            $params[] = $currentUser['id'];
        } elseif ($currentUser['user_type'] === 'admin' && $userId > 0) {
            $whereConditions[] = 'sl.user_id = ?';
            $params[] = $userId;
        }
        
        if (!empty($eventType)) {
            $whereConditions[] = 'sl.event_type = ?';
            $params[] = $eventType;
        }
        
        // 日期范围筛选
        if (!empty($dateRange)) {
            switch ($dateRange) {
                case 'today':
                    $whereConditions[] = "DATE(sl.created_at) = CURDATE()";
                    break;
                case 'yesterday':
                    $whereConditions[] = "DATE(sl.created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
                    break;
                case 'week':
                    $whereConditions[] = "sl.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                    break;
                case 'month':
                    $whereConditions[] = "sl.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                    break;
            }
        }
        
        if (!empty($search)) {
            $whereConditions[] = '(sl.event_type LIKE ? OR sl.ip_address LIKE ? OR sl.event_data LIKE ?)';
            $searchTerm = "%$search%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // 获取总数
        $totalResult = $db->fetch(
            "SELECT COUNT(*) as count FROM security_logs sl $whereClause",
            $params
        );
        $total = $totalResult['count'];
        
        // 获取安全日志列表
        $logs = $db->fetchAll(
            "SELECT sl.*, 
                    u.real_name as user_name,
                    u.email as user_email,
                    u.username as username,
                    CASE 
                        WHEN sl.event_type = 'login' THEN '用户登录'
                        WHEN sl.event_type = 'logout' THEN '用户登出'
                        WHEN sl.event_type = 'login_failed' THEN '登录失败'
                        WHEN sl.event_type = 'password_change' THEN '密码修改'
                        WHEN sl.event_type = 'totp_setup' THEN 'TOTP双因子认证设置'
                        WHEN sl.event_type = 'totp_verified' THEN 'TOTP双因子认证验证'
                        WHEN sl.event_type = 'totp_disabled' THEN 'TOTP双因子认证禁用'
                        ELSE sl.event_type
                    END as description
             FROM security_logs sl
             LEFT JOIN users u ON sl.user_id = u.id
             $whereClause
             ORDER BY sl.created_at DESC
             LIMIT ? OFFSET ?",
            array_merge($params, array($limit, $offset))
        );
        
        // 获取统计信息
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_logs,
                COUNT(CASE WHEN sl.event_type = 'login' THEN 1 END) as login_count,
                COUNT(CASE WHEN sl.event_type = 'logout' THEN 1 END) as logout_count,
                COUNT(CASE WHEN sl.event_type = 'login_failed' THEN 1 END) as failed_login_count,
                COUNT(CASE WHEN sl.event_type = 'password_change' THEN 1 END) as password_change_count,
                COUNT(CASE WHEN sl.event_type = 'totp_setup' THEN 1 END) as totp_setup_count
             FROM security_logs sl $whereClause",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_security_logs', 'security', 0, '查看安全日志');
        
        $response = array(
            'logs' => $logs ?: array(),
            'stats' => $stats ?: array(),
            'pagination' => array(
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total,
                'per_page' => $limit
            )
        );
        
        $auth->sendSuccess($response, '安全日志获取成功');
        
    } catch (Exception $e) {
        error_log("获取安全日志失败: " . $e->getMessage());
        $auth->sendError('获取安全日志失败: ' . $e->getMessage(), 500);
    }
}

// TOTP管理（暂时简化）
function handleTOTP($auth) {
    global $method;
    
    switch ($method) {
        case 'GET':
            handleGetTOTPStatus($auth);
            break;
        case 'POST':
            handleSetupTOTP($auth);
            break;
        default:
            $auth->sendError('不支持的请求方法', 405);
    }
}

// 获取TOTP状态（暂时简化）
function handleGetTOTPStatus($auth) {
    // 检查权限
    if (!$auth->checkPermission('totp.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

// 设置TOTP（暂时简化）
function handleSetupTOTP($auth) {
    // 检查权限
    if (!$auth->checkPermission('totp.setup')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    $auth->sendError('功能开发中', 501);
}

// 安全统计
function handleSecurityStats($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('security.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        // 构建查询条件
        $whereConditions = array();
        $params = array();
        
        // 数据隔离
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = 'user_id = ?';
            $params[] = $currentUser['id'];
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // 获取今日统计
        $todayStats = $db->fetch(
            "SELECT 
                COUNT(*) as total_events,
                COUNT(CASE WHEN event_type = 'login' THEN 1 END) as login_events,
                COUNT(CASE WHEN event_type = 'login_failed' THEN 1 END) as failed_login_events,
                COUNT(CASE WHEN event_type = 'password_change' THEN 1 END) as password_changes
             FROM security_logs 
             $whereClause AND DATE(created_at) = CURDATE()",
            $params
        );
        
        // 获取本周统计
        $weekStats = $db->fetch(
            "SELECT 
                COUNT(*) as total_events,
                COUNT(CASE WHEN event_type = 'login' THEN 1 END) as login_events,
                COUNT(CASE WHEN event_type = 'login_failed' THEN 1 END) as failed_login_events
             FROM security_logs 
             $whereClause AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)",
            $params
        );
        
        // 记录操作日志
        $auth->logUserAction('view_security_stats', 'security', 0, '查看安全统计');
        
        $response = array(
            'today_stats' => $todayStats ?: array(),
            'week_stats' => $weekStats ?: array()
        );
        
        $auth->sendSuccess($response, '安全统计获取成功');
        
    } catch (Exception $e) {
        error_log("获取安全统计失败: " . $e->getMessage());
        $auth->sendError('获取安全统计失败: ' . $e->getMessage(), 500);
    }
}

// 最近事件
function handleRecentEvents($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('security.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        $limit = isset($_GET['limit']) ? min(50, intval($_GET['limit'])) : 10;
        
        // 构建查询条件
        $whereConditions = array();
        $params = array();
        
        // 数据隔离
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = 'sl.user_id = ?';
            $params[] = $currentUser['id'];
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // 获取最近事件
        $events = $db->fetchAll(
            "SELECT sl.*, 
                    u.real_name as user_name,
                    u.username as username,
                    CASE 
                        WHEN sl.event_type = 'login' THEN '用户登录'
                        WHEN sl.event_type = 'logout' THEN '用户登出'
                        WHEN sl.event_type = 'login_failed' THEN '登录失败'
                        WHEN sl.event_type = 'password_change' THEN '密码修改'
                        ELSE sl.event_type
                    END as description
             FROM security_logs sl
             LEFT JOIN users u ON sl.user_id = u.id
             $whereClause
             ORDER BY sl.created_at DESC
             LIMIT ?",
            array_merge($params, array($limit))
        );
        
        // 记录操作日志
        $auth->logUserAction('view_recent_events', 'security', 0, '查看最近安全事件');
        
        $auth->sendSuccess(array('events' => $events ?: array()), '最近事件获取成功');
        
    } catch (Exception $e) {
        error_log("获取最近事件失败: " . $e->getMessage());
        $auth->sendError('获取最近事件失败: ' . $e->getMessage(), 500);
    }
}
?>
