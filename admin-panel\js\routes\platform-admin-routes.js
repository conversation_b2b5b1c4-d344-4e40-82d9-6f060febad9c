/**
 * 平台管理员路由配置
 * 优化版本 - 只加载平台管理员需要的功能
 * 
 * <AUTHOR>
 * @version 2.0.0 - 架构优化版本
 */

const PlatformAdminRoutes = {
    // 租户类型标识
    tenantType: 'platform_admin',
    
    // 平台管理员菜单配置（11个核心功能）
    menuConfig: [
        {
            id: 'dashboard',
            name: '平台概览',
            icon: 'bi-speedometer2',
            path: '/dashboard',
            module: 'PlatformDashboardModule',
            permission: 'platform_dashboard',
            description: '平台运营数据概览'
        },
        {
            id: 'merchants',
            name: '商户管理',
            icon: 'bi-shop',
            path: '/merchants',
            module: 'MerchantManagementModule',
            permission: 'merchant_management',
            description: '管理平台商户'
        },
        {
            id: 'providers',
            name: '码商管理',
            icon: 'bi-qr-code',
            path: '/providers',
            module: 'ProviderManagementModule',
            permission: 'provider_management',
            description: '管理平台码商'
        },
        {
            id: 'orders',
            name: '订单管理',
            icon: 'bi-receipt',
            path: '/orders',
            module: 'OrderManagementModule',
            permission: 'order_management',
            description: '订单查询和管理'
        },
        {
            id: 'financial',
            name: '财务管理',
            icon: 'bi-currency-dollar',
            path: '/financial',
            module: 'FinancialManagementModule',
            permission: 'financial_management',
            description: '财务数据和结算'
        },
        {
            id: 'reports',
            name: '数据报表',
            icon: 'bi-graph-up',
            path: '/reports',
            module: 'ReportsModule',
            permission: 'reports_view',
            description: '业务数据报表'
        },
        {
            id: 'products',
            name: '产品管理',
            icon: 'bi-box',
            path: '/products',
            module: 'ProductManagementModule',
            permission: 'product_management',
            description: '支付产品配置'
        },
        {
            id: 'settings',
            name: '平台设置',
            icon: 'bi-gear',
            path: '/settings',
            module: 'PlatformSettingsModule',
            permission: 'platform_settings',
            description: '平台参数配置'
        },
        {
            id: 'notifications',
            name: '消息通知',
            icon: 'bi-bell',
            path: '/notifications',
            module: 'NotificationModule',
            permission: 'notification_management',
            description: '系统消息管理'
        },
        {
            id: 'logs',
            name: '操作日志',
            icon: 'bi-journal-text',
            path: '/logs',
            module: 'PlatformLogsModule',
            permission: 'platform_logs',
            description: '平台操作日志'
        },
        {
            id: 'profile',
            name: '个人资料',
            icon: 'bi-person-circle',
            path: '/profile',
            module: 'PlatformProfileModule',
            permission: 'profile_management',
            description: '管理个人账户信息'
        }
    ],

    // 路由配置
    routes: {
        '/dashboard': {
            component: 'PlatformDashboardModule',
            title: '平台概览',
            requireAuth: true,
            permission: 'platform_dashboard'
        },
        '/merchants': {
            component: 'MerchantManagementModule',
            title: '商户管理',
            requireAuth: true,
            permission: 'merchant_management'
        },
        '/providers': {
            component: 'ProviderManagementModule',
            title: '码商管理',
            requireAuth: true,
            permission: 'provider_management'
        },
        '/orders': {
            component: 'OrderManagementModule',
            title: '订单管理',
            requireAuth: true,
            permission: 'order_management'
        },
        '/financial': {
            component: 'FinancialManagementModule',
            title: '财务管理',
            requireAuth: true,
            permission: 'financial_management'
        },
        '/reports': {
            component: 'ReportsModule',
            title: '数据报表',
            requireAuth: true,
            permission: 'reports_view'
        },
        '/products': {
            component: 'ProductManagementModule',
            title: '产品管理',
            requireAuth: true,
            permission: 'product_management'
        },
        '/settings': {
            component: 'PlatformSettingsModule',
            title: '平台设置',
            requireAuth: true,
            permission: 'platform_settings'
        },
        '/notifications': {
            component: 'NotificationModule',
            title: '消息通知',
            requireAuth: true,
            permission: 'notification_management'
        },
        '/logs': {
            component: 'PlatformLogsModule',
            title: '操作日志',
            requireAuth: true,
            permission: 'platform_logs'
        },
        '/profile': {
            component: 'PlatformProfileModule',
            title: '个人资料',
            requireAuth: true,
            permission: 'profile_management'
        }
    },

    // 权限配置
    permissions: [
        'platform_dashboard',
        'merchant_management',
        'provider_management',
        'order_management',
        'financial_management',
        'reports_view',
        'product_management',
        'platform_settings',
        'notification_management',
        'platform_logs',
        'profile_management'
    ],

    // 默认路由
    defaultRoute: '/dashboard',

    // 主题配置
    theme: {
        primaryColor: '#722ed1',
        brandName: '平台管理后台',
        sidebarStyle: 'light',
        headerStyle: 'colored'
    },

    // API端点配置
    apiEndpoints: {
        base: '/api/admin.php',
        auth: '/api/admin.php?action=login',
        logout: '/api/admin.php?action=logout',
        profile: '/api/admin.php?action=get_profile'
    },

    // 快速操作配置
    quickActions: [
        {
            name: '查看平台数据',
            icon: 'bi-speedometer2',
            action: 'dashboard',
            color: 'primary'
        },
        {
            name: '添加商户',
            icon: 'bi-plus-circle',
            action: 'add_merchant',
            color: 'success'
        },
        {
            name: '订单查询',
            icon: 'bi-search',
            action: 'search_orders',
            color: 'info'
        },
        {
            name: '财务报表',
            icon: 'bi-graph-up',
            action: 'financial_reports',
            color: 'warning'
        }
    ],

    // 统计信息配置
    dashboardWidgets: [
        'merchant_count',
        'provider_count',
        'today_orders',
        'today_amount',
        'success_rate',
        'platform_balance'
    ]
};

// 导出配置
window.PlatformAdminRoutes = PlatformAdminRoutes;
console.log('✅ 平台管理员路由配置已加载 (11个功能)'); 