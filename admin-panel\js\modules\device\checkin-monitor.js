/**
 * 签到监控模块
 * 监控设备签到状态，提供签到统计和超时提醒功能
 */
class CheckinMonitor {
    constructor() {
        this.apiBase = '/api/device';
        this.currentPage = 1;
        this.pageSize = 20;
        this.checkinData = [];
        this.isInitialized = false;
        this.autoRefresh = true;
        this.refreshInterval = null;
        
        console.log('CheckinMonitor initialized');
        this.refreshInterval = 30000; // 30秒刷新一次
        this.refreshTimer = null;
        this.overdueDevices = [];
        this.checkinStats = {};
    }

    /**
     * 初始化签到监控模块
     */
    async init(container) {
        try {
            this.renderCheckinMonitor(container);
            await this.loadCheckinData();
            this.bindEvents();
            this.startAutoRefresh();
        } catch (error) {
            console.error('签到监控模块初始化失败:', error);
            this.showError('签到监控模块初始化失败');
        }
    }

    /**
     * 渲染签到监控界面
     */
    renderCheckinMonitor(container) {
        container.innerHTML = `
            <div class="checkin-monitor-module">
                <!-- 页面头部 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="bi bi-clock-history me-2"></i>签到监控</h4>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" id="refreshCheckinBtn">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                        <button class="btn btn-outline-success" id="exportCheckinBtn">
                            <i class="bi bi-download"></i> 导出
                        </button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary bg-opacity-10 border-primary">
                            <div class="card-body text-center">
                                <div class="display-6 fw-bold text-primary" id="totalDevicesCount">0</div>
                                <div class="text-muted">总设备数</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success bg-opacity-10 border-success">
                            <div class="card-body text-center">
                                <div class="display-6 fw-bold text-success" id="normalDevicesCount">0</div>
                                <div class="text-muted">正常签到</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning bg-opacity-10 border-warning">
                            <div class="card-body text-center">
                                <div class="display-6 fw-bold text-warning" id="overdueDevicesCount">0</div>
                                <div class="text-muted">超时设备</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger bg-opacity-10 border-danger">
                            <div class="card-body text-center">
                                <div class="display-6 fw-bold text-danger" id="criticalDevicesCount">0</div>
                                <div class="text-muted">严重超时</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作面板 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-lightning me-2"></i>快速操作</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <button class="btn btn-outline-warning w-100" id="batchRemindBtn">
                                    <i class="bi bi-bell"></i> 批量提醒超时设备
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-info w-100" id="viewCheckinHistoryBtn">
                                    <i class="bi bi-clock-history"></i> 查看签到历史
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-outline-success w-100" id="generateReportBtn">
                                    <i class="bi bi-file-earmark-bar-graph"></i> 生成统计报表
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 超时设备列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">超时设备列表</h6>
                        <div class="text-muted small">
                            <span id="lastUpdateTime">最后更新: --</span>
                            <span class="mx-2">|</span>
                            <label class="form-check-label">
                                <input type="checkbox" class="form-check-input" id="autoRefreshCheckbox" checked>
                                自动刷新
                            </label>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" id="selectAllOverdue" class="form-check-input">
                                        </th>
                                        <th>设备ID</th>
                                        <th>设备信息</th>
                                        <th>所属小组</th>
                                        <th>最后签到</th>
                                        <th>超时时长</th>
                                        <th>状态</th>
                                        <th width="120">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="overdueTableBody">
                                    <!-- 超时设备列表将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                        <div id="checkinLoadingIndicator" class="text-center p-4" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在加载签到数据...</p>
                        </div>
                        <div id="checkinNoDataIndicator" class="text-center p-4" style="display: none;">
                            <i class="bi bi-check-circle text-success" style="font-size: 3rem;"></i>
                            <p class="mt-2 text-success">所有设备签到状态正常</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 签到历史模态框 -->
            <div class="modal fade" id="checkinHistoryModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">签到历史</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label">设备ID</label>
                                        <input type="text" class="form-control" id="historyDeviceIdInput" 
                                               placeholder="输入设备ID">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">开始日期</label>
                                        <input type="date" class="form-control" id="historyStartDate">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">结束日期</label>
                                        <input type="date" class="form-control" id="historyEndDate">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary w-100" id="queryHistoryBtn">查询</button>
                                    </div>
                                </div>
                            </div>
                            <div id="checkinHistoryContent">
                                <!-- 签到历史内容 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计报表模态框 -->
            <div class="modal fade" id="checkinReportModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">签到统计报表</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">统计类型</label>
                                        <select class="form-select" id="reportTypeSelect">
                                            <option value="daily">日统计</option>
                                            <option value="weekly">周统计</option>
                                            <option value="monthly">月统计</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">开始日期</label>
                                        <input type="date" class="form-control" id="reportStartDate">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">结束日期</label>
                                        <input type="date" class="form-control" id="reportEndDate">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary w-100" id="generateReportDataBtn">生成报表</button>
                                    </div>
                                </div>
                            </div>
                            <div id="checkinReportContent">
                                <!-- 统计报表内容 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 刷新按钮
        document.getElementById('refreshCheckinBtn').addEventListener('click', () => {
            this.loadCheckinData();
        });

        // 自动刷新复选框
        document.getElementById('autoRefreshCheckbox').addEventListener('change', (e) => {
            if (e.target.checked) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });

        // 全选复选框
        document.getElementById('selectAllOverdue').addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('.overdue-device-checkbox');
            checkboxes.forEach(cb => cb.checked = e.target.checked);
        });

        // 批量提醒按钮
        document.getElementById('batchRemindBtn').addEventListener('click', () => {
            this.batchRemindDevices();
        });

        // 查看签到历史
        document.getElementById('viewCheckinHistoryBtn').addEventListener('click', () => {
            this.showCheckinHistory();
        });

        // 生成统计报表
        document.getElementById('generateReportBtn').addEventListener('click', () => {
            this.showCheckinReport();
        });

        // 查询历史按钮
        document.getElementById('queryHistoryBtn').addEventListener('click', () => {
            this.queryCheckinHistory();
        });

        // 生成报表按钮
        document.getElementById('generateReportDataBtn').addEventListener('click', () => {
            this.generateReportData();
        });

        // 导出按钮
        document.getElementById('exportCheckinBtn').addEventListener('click', () => {
            this.exportCheckinData();
        });
    }

    /**
     * 加载签到数据
     */
    async loadCheckinData() {
        try {
            this.showCheckinLoading(true);
            
            // 并行加载统计数据和超时设备列表
            const [statsResponse, overdueResponse] = await Promise.all([
                this.makeApiRequest('/checkin_management.php?action=stats', 'GET'),
                this.makeApiRequest('/checkin_management.php?action=overdue_devices', 'GET')
            ]);
            
            if (statsResponse.error_code === 0) {
                this.checkinStats = statsResponse.data;
                this.updateStatsCards();
            }
            
            if (overdueResponse.error_code === 0) {
                this.overdueDevices = overdueResponse.data.overdue_devices;
                this.renderOverdueDevices();
            }
            
            this.updateLastUpdateTime();
            
        } catch (error) {
            console.error('加载签到数据失败:', error);
            this.showError('加载签到数据失败');
        } finally {
            this.showCheckinLoading(false);
        }
    }

    /**
     * 更新统计卡片
     */
    updateStatsCards() {
        document.getElementById('totalDevicesCount').textContent = this.checkinStats.total_devices || 0;
        document.getElementById('normalDevicesCount').textContent = this.checkinStats.normal_devices || 0;
        document.getElementById('overdueDevicesCount').textContent = this.checkinStats.overdue_devices || 0;
        document.getElementById('criticalDevicesCount').textContent = this.checkinStats.critical_devices || 0;
    }

    /**
     * 渲染超时设备列表
     */
    renderOverdueDevices() {
        const tbody = document.getElementById('overdueTableBody');
        
        if (this.overdueDevices.length === 0) {
            tbody.innerHTML = '';
            document.getElementById('checkinNoDataIndicator').style.display = 'block';
            return;
        }
        
        document.getElementById('checkinNoDataIndicator').style.display = 'none';
        
        tbody.innerHTML = this.overdueDevices.map(device => `
            <tr class="${device.is_critical ? 'table-danger' : 'table-warning'}">
                <td>
                    <input type="checkbox" class="form-check-input overdue-device-checkbox" 
                           value="${device.device_id}">
                </td>
                <td>
                    <div class="fw-bold">${device.device_id}</div>
                    <small class="text-muted">${device.device_fingerprint || '无指纹'}</small>
                </td>
                <td>
                    <div>${device.device_name || '未命名'}</div>
                    <small class="text-muted">${device.device_brand} ${device.device_model}</small>
                </td>
                <td>
                    ${device.group_code ? 
                      `<span class="badge bg-info">${device.group_code}</span>` : 
                      '<span class="text-muted">未分配</span>'}
                </td>
                <td>
                    ${device.last_checkin_time ? 
                      `<small>${new Date(device.last_checkin_time).toLocaleString()}</small>` : 
                      '<span class="text-muted">从未签到</span>'}
                </td>
                <td>
                    <div class="${device.is_critical ? 'text-danger fw-bold' : 'text-warning'}">
                        ${this.formatOverdueTime(device.overdue_minutes)}
                    </div>
                </td>
                <td>
                    ${device.is_critical ? 
                      '<span class="badge bg-danger">严重超时</span>' : 
                      '<span class="badge bg-warning">超时</span>'}
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="checkinMonitor.sendReminder('${device.device_id}')" 
                                title="发送提醒">
                            <i class="bi bi-bell"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="checkinMonitor.viewDeviceHistory('${device.device_id}')" 
                                title="查看历史">
                            <i class="bi bi-clock-history"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 格式化超时时间
     */
    formatOverdueTime(minutes) {
        if (minutes < 60) {
            return `${minutes}分钟`;
        } else if (minutes < 1440) {
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;
            return `${hours}小时${remainingMinutes}分钟`;
        } else {
            const days = Math.floor(minutes / 1440);
            const remainingHours = Math.floor((minutes % 1440) / 60);
            return `${days}天${remainingHours}小时`;
        }
    }

    /**
     * 批量提醒设备
     */
    async batchRemindDevices() {
        const selectedDevices = Array.from(document.querySelectorAll('.overdue-device-checkbox:checked'))
            .map(cb => cb.value);
        
        if (selectedDevices.length === 0) {
            // 如果没有选中设备，则提醒所有超时设备
            if (this.overdueDevices.length === 0) {
                this.showError('没有超时设备需要提醒');
                return;
            }
            selectedDevices.push(...this.overdueDevices.map(device => device.device_id));
        }
        
        if (!confirm(`确认向选中的 ${selectedDevices.length} 台设备发送签到提醒？`)) {
            return;
        }
        
        try {
            const data = {
                device_ids: selectedDevices,
                message: '您的设备签到已超时，请及时签到以确保正常使用'
            };
            
            const response = await this.makeApiRequest('/checkin_management.php?action=batch_remind', 'POST', data);
            if (response.error_code === 0) {
                this.showSuccess(`签到提醒发送成功，共处理 ${response.data.success_count} 台设备`);
                this.loadCheckinData(); // 刷新数据
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('批量提醒失败:', error);
            this.showError('批量提醒失败');
        }
    }

    /**
     * 发送单个设备提醒
     */
    async sendReminder(deviceId) {
        try {
            const data = {
                device_ids: [deviceId],
                message: '您的设备签到已超时，请及时签到'
            };
            
            const response = await this.makeApiRequest('/checkin_management.php?action=batch_remind', 'POST', data);
            if (response.error_code === 0) {
                this.showSuccess('签到提醒发送成功');
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('发送提醒失败:', error);
            this.showError('发送提醒失败');
        }
    }

    /**
     * 显示签到历史
     */
    showCheckinHistory() {
        // 设置默认日期范围（最近7天）
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 7);
        
        document.getElementById('historyStartDate').value = startDate.toISOString().split('T')[0];
        document.getElementById('historyEndDate').value = endDate.toISOString().split('T')[0];
        
        new bootstrap.Modal(document.getElementById('checkinHistoryModal')).show();
    }

    /**
     * 查询签到历史
     */
    async queryCheckinHistory() {
        try {
            const deviceId = document.getElementById('historyDeviceIdInput').value.trim();
            const startDate = document.getElementById('historyStartDate').value;
            const endDate = document.getElementById('historyEndDate').value;
            
            if (!startDate || !endDate) {
                this.showError('请选择查询日期范围');
                return;
            }
            
            const params = new URLSearchParams({
                start_date: startDate,
                end_date: endDate
            });
            
            if (deviceId) {
                params.append('device_id', deviceId);
            }
            
            const response = await this.makeApiRequest(`/checkin_management.php?action=history&${params}`, 'GET');
            if (response.error_code === 0) {
                this.renderCheckinHistory(response.data);
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('查询签到历史失败:', error);
            this.showError('查询签到历史失败');
        }
    }

    /**
     * 渲染签到历史
     */
    renderCheckinHistory(data) {
        const content = document.getElementById('checkinHistoryContent');
        
        if (data.history.length === 0) {
            content.innerHTML = '<div class="text-center text-muted">暂无签到历史记录</div>';
            return;
        }
        
        content.innerHTML = `
            <div class="table-responsive" style="max-height: 400px;">
                <table class="table table-sm">
                    <thead class="table-light">
                        <tr>
                            <th>设备ID</th>
                            <th>签到时间</th>
                            <th>签到类型</th>
                            <th>状态</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.history.map(record => `
                            <tr>
                                <td>${record.device_id}</td>
                                <td><small>${new Date(record.checkin_time).toLocaleString()}</small></td>
                                <td>
                                    <span class="badge bg-${record.is_manual ? 'primary' : 'success'}">
                                        ${record.is_manual ? '手动签到' : '自动签到'}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-${record.is_overdue ? 'warning' : 'success'}">
                                        ${record.is_overdue ? '超时签到' : '正常签到'}
                                    </span>
                                </td>
                                <td><small>${record.remarks || '-'}</small></td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            <div class="mt-3">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            共查询到 ${data.history.length} 条记录
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-outline-primary btn-sm" onclick="checkinMonitor.exportHistory()">
                            <i class="bi bi-download"></i> 导出历史
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 显示统计报表
     */
    showCheckinReport() {
        // 设置默认日期范围（最近30天）
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 30);
        
        document.getElementById('reportStartDate').value = startDate.toISOString().split('T')[0];
        document.getElementById('reportEndDate').value = endDate.toISOString().split('T')[0];
        
        new bootstrap.Modal(document.getElementById('checkinReportModal')).show();
    }

    /**
     * 生成报表数据
     */
    async generateReportData() {
        try {
            const reportType = document.getElementById('reportTypeSelect').value;
            const startDate = document.getElementById('reportStartDate').value;
            const endDate = document.getElementById('reportEndDate').value;
            
            if (!startDate || !endDate) {
                this.showError('请选择统计日期范围');
                return;
            }
            
            const params = new URLSearchParams({
                type: reportType,
                start_date: startDate,
                end_date: endDate
            });
            
            const response = await this.makeApiRequest(`/checkin_management.php?action=report&${params}`, 'GET');
            if (response.error_code === 0) {
                this.renderReportData(response.data);
            } else {
                this.showError(response.error_message);
            }
        } catch (error) {
            console.error('生成报表失败:', error);
            this.showError('生成报表失败');
        }
    }

    /**
     * 渲染报表数据
     */
    renderReportData(data) {
        const content = document.getElementById('checkinReportContent');
        
        content.innerHTML = `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card bg-primary bg-opacity-10 border-primary">
                        <div class="stat-value">${data.summary.total_checkins}</div>
                        <div class="stat-label">总签到次数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card bg-success bg-opacity-10 border-success">
                        <div class="stat-value">${data.summary.normal_checkins}</div>
                        <div class="stat-label">正常签到</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card bg-warning bg-opacity-10 border-warning">
                        <div class="stat-value">${data.summary.overdue_checkins}</div>
                        <div class="stat-label">超时签到</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card bg-info bg-opacity-10 border-info">
                        <div class="stat-value">${data.summary.avg_interval}</div>
                        <div class="stat-label">平均间隔(小时)</div>
                    </div>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead class="table-light">
                        <tr>
                            <th>日期</th>
                            <th>设备数</th>
                            <th>签到次数</th>
                            <th>正常签到</th>
                            <th>超时签到</th>
                            <th>签到率</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.details.map(row => `
                            <tr>
                                <td>${row.date}</td>
                                <td>${row.device_count}</td>
                                <td>${row.total_checkins}</td>
                                <td class="text-success">${row.normal_checkins}</td>
                                <td class="text-warning">${row.overdue_checkins}</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" style="width: ${row.checkin_rate}%">
                                            ${row.checkin_rate}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * 查看设备历史
     */
    viewDeviceHistory(deviceId) {
        document.getElementById('historyDeviceIdInput').value = deviceId;
        this.showCheckinHistory();
    }

    /**
     * 导出签到数据
     */
    exportCheckinData() {
        const data = this.overdueDevices.map(device => ({
            '设备ID': device.device_id,
            '设备名称': device.device_name || '未命名',
            '设备品牌': device.device_brand,
            '所属小组': device.group_code || '未分配',
            '最后签到': device.last_checkin_time || '从未签到',
            '超时时长': this.formatOverdueTime(device.overdue_minutes),
            '状态': device.is_critical ? '严重超时' : '超时'
        }));
        
        this.downloadCSV(data, `超时设备列表_${new Date().toISOString().split('T')[0]}.csv`);
    }

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        this.refreshTimer = setInterval(() => {
            this.loadCheckinData();
        }, this.refreshInterval);
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    /**
     * 更新最后更新时间
     */
    updateLastUpdateTime() {
        const now = new Date();
        document.getElementById('lastUpdateTime').textContent = 
            `最后更新: ${now.toLocaleTimeString()}`;
    }

    /**
     * 工具方法
     */
    async makeApiRequest(endpoint, method = 'GET', data = null) {
        const url = `${this.apiBase}${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.getAuthToken()}`
            }
        };
        
        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        return await response.json();
    }

    getAuthToken() {
        return localStorage.getItem('admin_token') || sessionStorage.getItem('admin_token') || '';
    }

    showCheckinLoading(show) {
        document.getElementById('checkinLoadingIndicator').style.display = show ? 'block' : 'none';
    }

    showError(message) {
        alert(message);
    }

    showSuccess(message) {
        alert(message);
    }

    downloadCSV(data, filename) {
        if (data.length === 0) return;
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
        ].join('\n');
        
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    }

    /**
     * 销毁模块
     */
    destroy() {
        this.stopAutoRefresh();
    }
}

// 创建全局实例
window.checkinMonitor = new CheckinMonitor();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CheckinMonitor };
} else {
    // 浏览器环境 - 导出类到全局作用域
    window.CheckinMonitor = CheckinMonitor;
    
    // 为模块加载器导出整个模块
    window.CheckinMonitorModule = {
        CheckinMonitor,
        version: '1.0.0',
        initialized: true
    };
    
    console.log('✅ 签到监控模块已导出到全局作用域');
}

// export default CheckinMonitor; // 注释掉ES6导出，使用全局变量
window.CheckinMonitor = CheckinMonitor; 