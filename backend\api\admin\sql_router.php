<?php
/**
 * SQL路由管理API
 * 用于系统管理员管理和监控SQL查询路由
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

// 引入必要的文件
require_once __DIR__ . '/../core/TenantAuth.php';
require_once __DIR__ . '/../core/SqlRouter.php';
require_once __DIR__ . '/../core/SqlMiddleware.php';
require_once __DIR__ . '/../core/DataAccessLayer.php';

// 认证和权限检查
$tenantAuth = new TenantAuth();
$userContext = $tenantAuth->getUserContext();

if (!$userContext) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未认证']);
    exit;
}

// 只有系统管理员可以访问SQL路由管理
if ($userContext['user_type'] !== 'system_admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => '权限不足']);
    exit;
}

// 获取请求参数
$action = isset(\$_GET['action']) ? \$_GET['action'] : '';
$method = $_SERVER['REQUEST_METHOD'];

// 初始化数据访问层
try {
    $db = new PDO(
        "mysql:host=**************;dbname=payment_system;charset=utf8",
        "root",
        "yak19XTwyDx.8",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    $dal = DataAccessLayer::getInstance($db, $tenantAuth);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}

// 处理请求
try {
    switch ($action) {
        case 'get_queries':
            handleGetQueries();
            break;
            
        case 'get_query_detail':
            handleGetQueryDetail();
            break;
            
        case 'get_query_stats':
            handleGetQueryStats();
            break;
            
        case 'get_performance_report':
            handleGetPerformanceReport();
            break;
            
        case 'test_query':
            handleTestQuery();
            break;
            
        case 'export_queries':
            handleExportQueries();
            break;
            
        case 'clear_cache':
            handleClearCache();
            break;
            
        case 'get_cache_stats':
            handleGetCacheStats();
            break;
            
        case 'get_sql_logs':
            handleGetSqlLogs();
            break;
            
        case 'execute_custom_query':
            handleExecuteCustomQuery();
            break;
            
        default:
            throw new Exception('未知操作');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 获取所有查询配置
 */
function handleGetQueries() {
    $queries = SqlRouter::getAllQueries();
    $queryList = [];
    
    foreach ($queries as $name => $config) {
        $queryList[] = [
            'name' => $name,
            'description' => isset(\$config['description']) ? \$config['description'] : '',
            'type' => isset(\$config['type']) ? \$config['type'] : 'select',
            'cache_ttl' => isset(\$config['cache_ttl']) ? \$config['cache_ttl'] : 0,
            'tenant_filter' => isset(\$config['tenant_filter']) ? \$config['tenant_filter'] : false,
            'permission_required' => isset(\$config['permission_required']) ? \$config['permission_required'] : null,
            'param_count' => count(isset(\$config['params']) ? \$config['params'] : []),
            'optional_param_count' => count(isset(\$config['optional_params']) ? \$config['optional_params'] : [])
        ];
    }
    
    // 按类别分组
    $grouped = [];
    foreach ($queryList as $query) {
        $category = explode('.', $query['name'])[0];
        if (!isset($grouped[$category])) {
            $grouped[$category] = [];
        }
        $grouped[$category][] = $query;
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'queries' => $queryList,
            'grouped' => $grouped,
            'total' => count($queryList),
            'categories' => array_keys($grouped)
        ]
    ]);
}

/**
 * 获取查询详情
 */
function handleGetQueryDetail() {
    $queryName = isset(\$_GET['query_name']) ? \$_GET['query_name'] : '';
    
    if (empty($queryName)) {
        throw new Exception('查询名称不能为空');
    }
    
    $query = SqlRouter::getQuery($queryName);
    if (!$query) {
        throw new Exception('查询不存在');
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'name' => $queryName,
            'config' => $query
        ]
    ]);
}

/**
 * 获取查询统计
 */
function handleGetQueryStats() {
    global $dal;
    
    $stats = $dal->getQueryStats();
    
    // 计算总体统计
    $totalCalls = array_sum(array_column($stats, 'total_calls'));
    $totalTime = array_sum(array_column($stats, 'total_time'));
    $avgTime = $totalCalls > 0 ? $totalTime / $totalCalls : 0;
    
    echo json_encode([
        'success' => true,
        'data' => [
            'queries' => $stats,
            'summary' => [
                'total_queries' => count($stats),
                'total_calls' => $totalCalls,
                'total_time' => round($totalTime, 2),
                'avg_time' => round($avgTime, 2)
            ]
        ]
    ]);
}

/**
 * 获取性能报告
 */
function handleGetPerformanceReport() {
    global $dal;
    
    $timeRange = isset(\$_GET['time_range']) ? \$_GET['time_range'] : '1 day';
    $report = $dal->getPerformanceReport($timeRange);
    
    echo json_encode([
        'success' => true,
        'data' => $report
    ]);
}

/**
 * 测试查询
 */
function handleTestQuery() {
    global $dal;
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $queryName = isset(\$input['query_name']) ? \$input['query_name'] : '';
    $params = isset(\$input['params']) ? \$input['params'] : [];
    
    if (empty($queryName)) {
        throw new Exception('查询名称不能为空');
    }
    
    // 限制测试查询的类型，只允许安全的查询
    $allowedQueries = [
        'user.by_id', 'merchant.count', 'provider.list', 
        'order.stats', 'platform.list', 'domain.list'
    ];
    
    if (!in_array($queryName, $allowedQueries)) {
        throw new Exception('该查询不允许测试');
    }
    
    $startTime = microtime(true);
    $result = SqlMiddleware::executeQuery($queryName, $params);
    $executeTime = (microtime(true) - $startTime) * 1000;
    
    echo json_encode([
        'success' => true,
        'data' => [
            'result' => $result,
            'execute_time' => round($executeTime, 2),
            'query_name' => $queryName,
            'params' => $params
        ]
    ]);
}

/**
 * 导出查询配置
 */
function handleExportQueries() {
    $queries = SqlRouter::getAllQueries();
    $format = isset(\$_GET['format']) ? \$_GET['format'] : 'json';
    
    switch ($format) {
        case 'json':
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="sql_queries_' . date('Y-m-d') . '.json"');
            echo json_encode($queries, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            break;
            
        case 'csv':
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="sql_queries_' . date('Y-m-d') . '.csv"');
            
            $output = fopen('php://output', 'w');
            fputcsv($output, ['Name', 'Type', 'Description', 'Cache TTL', 'Tenant Filter', 'Permission Required']);
            
            foreach ($queries as $name => $config) {
                fputcsv($output, [
                    $name,
                    isset(\$config['type']) ? \$config['type'] : 'select',
                    isset(\$config['description']) ? \$config['description'] : '',
                    isset(\$config['cache_ttl']) ? \$config['cache_ttl'] : 0,
                    $config['tenant_filter'] ? 'Yes' : 'No',
                    isset(\$config['permission_required']) ? \$config['permission_required'] : 'None'
                ]);
            }
            
            fclose($output);
            break;
            
        default:
            throw new Exception('不支持的导出格式');
    }
}

/**
 * 清理缓存
 */
function handleClearCache() {
    global $dal;
    
    $pattern = isset(\$_POST['pattern']) ? \$_POST['pattern'] : null;
    $result = $dal->clearCache($pattern);
    
    echo json_encode([
        'success' => true,
        'data' => $result
    ]);
}

/**
 * 获取缓存统计
 */
function handleGetCacheStats() {
    global $dal;
    
    $stats = $dal->getCacheStats();
    
    echo json_encode([
        'success' => true,
        'data' => $stats
    ]);
}

/**
 * 获取SQL日志
 */
function handleGetSqlLogs() {
    $limit = (int)(isset(\$_GET['limit']) ? \$_GET['limit'] : 100);
    $offset = (int)(isset(\$_GET['offset']) ? \$_GET['offset'] : 0);
    $timeRange = isset(\$_GET['time_range']) ? \$_GET['time_range'] : '1 hour';
    
    // 读取SQL查询日志文件
    $logFile = '/data/logs/sql_queries.log';
    $logs = [];
    
    if (file_exists($logFile)) {
        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $lines = array_reverse($lines); // 最新的在前面
        
        // 解析日志
        $currentTime = time();
        $timeRangeSeconds = [
            '1 hour' => 3600,
            '6 hours' => 21600,
            '1 day' => 86400,
            '7 days' => 604800
        ];
        $timeRangeSeconds = isset($timeRangeSeconds[$timeRange]) ? $timeRangeSeconds[$timeRange] : 3600;
        
        foreach (array_slice($lines, $offset, $limit) as $line) {
            $data = json_decode($line, true);
            if ($data && isset($data['timestamp'])) {
                $logTime = strtotime($data['timestamp']);
                if (($currentTime - $logTime) <= $timeRangeSeconds) {
                    $logs[] = $data;
                }
            }
        }
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'logs' => $logs,
            'total' => count($logs),
            'time_range' => $timeRange
        ]
    ]);
}

/**
 * 执行自定义查询（危险功能，仅供调试）
 */
function handleExecuteCustomQuery() {
    // 这个功能非常危险，只在开发环境启用
    if (!defined('DEVELOPMENT_MODE') || !DEVELOPMENT_MODE) {
        throw new Exception('此功能仅在开发环境可用');
    }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $sql = isset(\$input['sql']) ? \$input['sql'] : '';
    
    if (empty($sql)) {
        throw new Exception('SQL语句不能为空');
    }
    
    // 基本安全检查
    $dangerousKeywords = ['DROP', 'DELETE', 'TRUNCATE', 'UPDATE', 'INSERT'];
    foreach ($dangerousKeywords as $keyword) {
        if (stripos($sql, $keyword) !== false) {
            throw new Exception('不允许执行修改数据的SQL语句');
        }
    }
    
    global $db;
    
    try {
        $startTime = microtime(true);
        $stmt = $db->query($sql);
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $executeTime = (microtime(true) - $startTime) * 1000;
        
        echo json_encode([
            'success' => true,
            'data' => [
                'result' => $result,
                'execute_time' => round($executeTime, 2),
                'row_count' => count($result)
            ]
        ]);
        
    } catch (PDOException $e) {
        throw new Exception('SQL执行失败: ' . $e->getMessage());
    }
}

/**
 * 记录管理员操作日志
 */
function logAdminAction($action, $details = []) {
    global $userContext;
    
    $logData = [
        'user_id' => $userContext['user_id'],
        'user_type' => $userContext['user_type'],
        'action' => $action,
        'details' => $details,
        'ip_address' => isset(\$_SERVER['REMOTE_ADDR']) ? \$_SERVER['REMOTE_ADDR'] : 'unknown',
        'user_agent' => isset(\$_SERVER['HTTP_USER_AGENT']) ? \$_SERVER['HTTP_USER_AGENT'] : 'unknown',
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    $logFile = '/data/logs/admin_actions.log';
    file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
}

error_log("SQL路由管理API处理完成");
?> 