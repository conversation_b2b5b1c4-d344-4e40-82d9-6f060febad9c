<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 真实登录流程测试 ===\n";

try {
    // 包含必要的文件
    require_once dirname(__FILE__) . '/../utils/TenantAuth.php';
    require_once dirname(__FILE__) . '/../core/TenantManager.php';
    
    echo "1. 文件包含成功\n";
    
    // 创建对象
    $tenantAuth = new TenantAuth();
    $tenantManager = new TenantManager();
    
    echo "2. 对象创建成功\n";
    
    // 获取当前租户
    $currentTenant = $tenantManager->getCurrentTenant();
    echo "3. 当前租户获取成功\n";
    
    // 模拟登录参数
    $testUsername = 'provider_user';  // 使用一个可能存在的用户名
    $testPassword = 'test123';
    
    echo "4. 开始测试登录流程...\n";
    echo "   用户名: $testUsername\n";
    echo "   密码: $testPassword\n";
    
    // 测试tenantLogin方法
    $loginResult = $tenantAuth->tenantLogin($testUsername, $testPassword);
    
    echo "5. 登录结果:\n";
    echo "   " . print_r($loginResult, true) . "\n";
    
    if ($loginResult['success']) {
        echo "6. 登录成功测试完成\n";
    } else {
        echo "6. 登录失败，但没有产生500错误\n";
        echo "   失败原因: " . $loginResult['message'] . "\n";
    }
    
} catch (ParseError $e) {
    echo "PHP语法错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "PHP错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 现在测试完整的API路由器 ===\n";

try {
    // 模拟POST请求数据
    $_POST['action'] = 'login';
    $_POST['username'] = 'provider_user';
    $_POST['password'] = 'test123';
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    echo "7. 模拟POST数据设置完成\n";
    
    // 不直接包含tenant_api.php，而是创建路由器对象
    class TestTenantAPIRouter {
        private $tenantAuth;
        private $tenantManager;
        private $currentTenant;
        
        public function __construct() {
            $this->tenantAuth = new TenantAuth();
            $this->tenantManager = new TenantManager();
            $this->currentTenant = $this->tenantManager->getCurrentTenant();
        }
        
        public function testHandleLogin() {
            // 验证租户
            if (!$this->currentTenant) {
                throw new Exception('无效的访问域名', 404);
            }
            
            // 获取请求参数
            $action = isset($_POST['action']) ? $_POST['action'] : '';
            
            if (!$action) {
                throw new Exception('缺少action参数', 400);
            }
            
            if ($action === 'login') {
                return $this->handleLogin();
            }
            
            throw new Exception('不支持的action', 400);
        }
        
        private function handleLogin() {
            // 尝试从POST参数获取
            $input = $_POST;
            
            // 验证必要参数
            if (!$input || !isset($input['username']) || !isset($input['password'])) {
                throw new Exception('用户名和密码不能为空', 400);
            }
            
            $result = $this->tenantAuth->tenantLogin($input['username'], $input['password']);
            
            if (!$result['success']) {
                throw new Exception($result['message'], 401);
            }
            
            return array(
                'code' => 200,
                'message' => '登录成功',
                'data' => array(
                    'token' => $result['token'],
                    'user' => $result['user'],
                    'tenant' => $result['tenant']
                )
            );
        }
    }
    
    $router = new TestTenantAPIRouter();
    $result = $router->testHandleLogin();
    
    echo "8. API路由器测试完成\n";
    echo "   结果: " . json_encode($result) . "\n";
    
} catch (Exception $e) {
    echo "API路由器错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== 测试完成 ===\n";
?> 