/**
 * 财务风险控制管理器 - 四层架构风险控制系统
 * 负责财务风险监控、预警和处理
 */

class FinanceRiskControlManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        
        this.currentPage = 1;
        this.pageSize = 20;
        this.riskEvents = [];
        this.riskRules = [];
        this.monitorTimer = null;
        
        console.log('⚠️ FinanceRiskControlManager initialized for tenant:', this.tenantInfo?.tenant_type);
    }

    /**
     * 初始化风险控制管理器
     */
    async init(container) {
        try {
            this.container = container;
            await this.renderRiskControlPage();
            await this.loadRiskEvents();
            await this.loadRiskRules();
            await this.loadRiskStats();
            this.bindEvents();
            this.startRiskMonitor();
            
            console.log('✅ 财务风险控制管理器初始化完成');
        } catch (error) {
            console.error('❌ 财务风险控制管理器初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    /**
     * 渲染风险控制页面
     */
    async renderRiskControlPage() {
        this.container.innerHTML = `
            <div class="finance-risk-control-manager">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="bi bi-shield-exclamation me-2"></i>财务风险控制</h2>
                            <p class="text-muted mb-0">实时监控财务风险，预警异常交易和资金流动</p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-danger" onclick="financeRiskControlManager.showCreateRuleModal()">
                                <i class="bi bi-plus me-2"></i>新增规则
                            </button>
                            <button class="btn btn-warning" onclick="financeRiskControlManager.showRiskAnalysisModal()">
                                <i class="bi bi-graph-up me-2"></i>风险分析
                            </button>
                            <button class="btn btn-outline-info" onclick="financeRiskControlManager.exportRiskReport()">
                                <i class="bi bi-download me-2"></i>导出报告
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 风险概览统计 -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-danger">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number text-danger" id="highRiskCount">0</div>
                                <div class="stat-label">高风险事件</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-exclamation-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number text-warning" id="mediumRiskCount">0</div>
                                <div class="stat-label">中风险事件</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-info-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number text-info" id="lowRiskCount">0</div>
                                <div class="stat-label">低风险事件</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number text-primary" id="activeRulesCount">0</div>
                                <div class="stat-label">活跃规则</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number text-success" id="blockedTransactions">0</div>
                                <div class="stat-label">拦截交易</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-secondary">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number text-secondary" id="protectedAmount">¥0</div>
                                <div class="stat-label">保护金额</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时监控面板 -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-activity me-2"></i>实时风险监控
                                    <span class="badge bg-success ms-2" id="monitorStatus">监控中</span>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-primary" id="todayRiskEvents">0</div>
                                            <div class="metric-label">今日风险事件</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-warning" id="pendingReview">0</div>
                                            <div class="metric-label">待审核事件</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-info" id="avgResponseTime">0ms</div>
                                            <div class="metric-label">平均响应时间</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-success" id="riskScore">0</div>
                                            <div class="metric-label">风险评分</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <canvas id="riskTrendChart" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-bell me-2"></i>最新风险预警
                                </h6>
                            </div>
                            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                <div id="recentAlerts">
                                    <div class="text-center py-3">
                                        <div class="spinner-border spinner-border-sm" role="status"></div>
                                        <div class="mt-2">加载中...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 风险事件和规则tabs -->
                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#riskEvents" role="tab">
                                    <i class="bi bi-exclamation-triangle me-2"></i>风险事件
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#riskRules" role="tab">
                                    <i class="bi bi-shield-check me-2"></i>风险规则
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#riskAnalysis" role="tab">
                                    <i class="bi bi-graph-up me-2"></i>风险分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#whitelist" role="tab">
                                    <i class="bi bi-check-circle me-2"></i>白名单管理
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <!-- 风险事件 -->
                            <div class="tab-pane fade show active" id="riskEvents" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="d-flex gap-3">
                                        <select class="form-select form-select-sm" id="filterRiskLevel">
                                            <option value="">全部风险等级</option>
                                            <option value="high">高风险</option>
                                            <option value="medium">中风险</option>
                                            <option value="low">低风险</option>
                                        </select>
                                        <select class="form-select form-select-sm" id="filterEventType">
                                            <option value="">全部事件类型</option>
                                            <option value="abnormal_amount">异常金额</option>
                                            <option value="frequent_transaction">频繁交易</option>
                                            <option value="suspicious_account">可疑账户</option>
                                            <option value="unusual_time">异常时间</option>
                                        </select>
                                        <select class="form-select form-select-sm" id="filterStatus">
                                            <option value="">全部状态</option>
                                            <option value="pending">待处理</option>
                                            <option value="processing">处理中</option>
                                            <option value="resolved">已解决</option>
                                            <option value="ignored">已忽略</option>
                                        </select>
                                        <input type="date" class="form-control form-control-sm" id="filterDate">
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="financeRiskControlManager.applyFilters()">
                                            <i class="bi bi-search"></i> 搜索
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="financeRiskControlManager.resetFilters()">
                                            <i class="bi bi-arrow-clockwise"></i> 重置
                                        </button>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <input type="checkbox" id="selectAllEvents" onchange="financeRiskControlManager.toggleSelectAll('events')">
                                                </th>
                                                <th>事件ID</th>
                                                <th>风险等级</th>
                                                <th>事件类型</th>
                                                <th>相关交易</th>
                                                <th>风险金额</th>
                                                <th>触发时间</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="riskEventsTableBody">
                                            <tr>
                                                <td colspan="9" class="text-center py-4">
                                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                    加载中...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分页 -->
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div class="text-muted">
                                        显示 <span id="eventsPageInfo">0-0</span> 条，共 <span id="eventsTotalItems">0</span> 条
                                    </div>
                                    <nav>
                                        <ul class="pagination pagination-sm mb-0" id="eventsPagination">
                                        </ul>
                                    </nav>
                                </div>
                            </div>

                            <!-- 风险规则 -->
                            <div class="tab-pane fade" id="riskRules" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">风险控制规则管理</h6>
                                    <button class="btn btn-primary btn-sm" onclick="financeRiskControlManager.showCreateRuleModal()">
                                        <i class="bi bi-plus me-2"></i>新增规则
                                    </button>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>规则ID</th>
                                                <th>规则名称</th>
                                                <th>规则类型</th>
                                                <th>风险等级</th>
                                                <th>触发条件</th>
                                                <th>状态</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="riskRulesTableBody">
                                            <tr>
                                                <td colspan="8" class="text-center py-4">
                                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                    加载中...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 风险分析 -->
                            <div class="tab-pane fade" id="riskAnalysis" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">风险趋势分析</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="riskTrendAnalysisChart" height="300"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">风险类型分布</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="riskTypeDistributionChart" height="300"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 白名单管理 -->
                            <div class="tab-pane fade" id="whitelist" role="tabpanel">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">白名单管理</h6>
                                    <button class="btn btn-success btn-sm" onclick="financeRiskControlManager.showAddWhitelistModal()">
                                        <i class="bi bi-plus me-2"></i>添加白名单
                                    </button>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>类型</th>
                                                <th>值</th>
                                                <th>描述</th>
                                                <th>添加时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="whitelistTableBody">
                                            <tr>
                                                <td colspan="5" class="text-center py-4">
                                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                                    加载中...
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 处理风险事件模态框 -->
            <div class="modal fade" id="processRiskEventModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle me-2"></i>处理风险事件
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="riskEventDetails">
                                <!-- 风险事件详情将动态加载 -->
                            </div>
                            <form id="processRiskEventForm">
                                <input type="hidden" id="riskEventId">
                                <div class="mb-3">
                                    <label class="form-label">处理动作</label>
                                    <select class="form-select" id="processAction" required>
                                        <option value="">请选择处理动作</option>
                                        <option value="block">阻止交易</option>
                                        <option value="review">人工审核</option>
                                        <option value="ignore">忽略警告</option>
                                        <option value="whitelist">加入白名单</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">处理说明</label>
                                    <textarea class="form-control" id="processNote" rows="3" placeholder="请说明处理原因..."></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="financeRiskControlManager.processRiskEvent()">
                                <i class="bi bi-check me-2"></i>确认处理
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // Tab切换事件
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const target = e.target.getAttribute('href').substring(1);
                this.loadTabData(target);
            });
        });
    }

    /**
     * 加载风险事件
     */
    async loadRiskEvents() {
        try {
            const params = {
                page: this.currentPage,
                pageSize: this.pageSize,
                risk_level: document.getElementById('filterRiskLevel')?.value || '',
                event_type: document.getElementById('filterEventType')?.value || '',
                status: document.getElementById('filterStatus')?.value || '',
                date: document.getElementById('filterDate')?.value || ''
            };

            const response = await this.apiClient.get('/admin/finance/risk-events', { params });

            if (response.success) {
                this.riskEvents = response.data.events;
                this.renderRiskEventsTable();
                this.renderPagination(response.data.pagination);
            } else {
                throw new Error(response.message || '加载风险事件失败');
            }

        } catch (error) {
            console.error('加载风险事件失败:', error);
            this.showError('加载事件失败: ' + error.message);
        }
    }

    /**
     * 渲染风险事件表格
     */
    renderRiskEventsTable() {
        const tbody = document.getElementById('riskEventsTableBody');
        
        if (this.riskEvents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <i class="bi bi-shield-check text-success" style="font-size: 2rem;"></i>
                        <div class="mt-2 text-muted">暂无风险事件</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.riskEvents.map(event => `
            <tr>
                <td>
                    <input type="checkbox" class="risk-event-checkbox" value="${event.id}">
                </td>
                <td>
                    <div class="fw-medium">#${event.id}</div>
                    <small class="text-muted">${event.rule_name}</small>
                </td>
                <td>
                    <span class="badge bg-${this.getRiskLevelBadgeColor(event.risk_level)}">
                        ${this.getRiskLevelText(event.risk_level)}
                    </span>
                </td>
                <td>${this.getEventTypeText(event.event_type)}</td>
                <td>
                    <div class="fw-medium">${event.transaction_no}</div>
                    <small class="text-muted">¥${this.formatAmount(event.amount)}</small>
                </td>
                <td class="text-danger fw-medium">¥${this.formatAmount(event.risk_amount)}</td>
                <td>${this.formatDateTime(event.created_at)}</td>
                <td>
                    <span class="badge bg-${this.getStatusBadgeColor(event.status)}">
                        ${this.getStatusText(event.status)}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="financeRiskControlManager.viewRiskEventDetail(${event.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${event.status === 'pending' ? `
                            <button class="btn btn-outline-warning" onclick="financeRiskControlManager.showProcessRiskEventModal(${event.id})" title="处理事件">
                                <i class="bi bi-tools"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 启动风险监控
     */
    startRiskMonitor() {
        // 每30秒检查一次风险状态
        this.monitorTimer = setInterval(() => {
            this.loadRiskStats();
            this.loadRecentAlerts();
        }, 30000);
    }

    /**
     * 加载风险统计
     */
    async loadRiskStats() {
        try {
            const response = await this.apiClient.get('/admin/finance/risk-stats');

            if (response.success) {
                const stats = response.data;
                this.updateStatsDisplay(stats);
                this.updateMonitorStatus(stats.monitor);
            }

        } catch (error) {
            console.error('加载风险统计失败:', error);
        }
    }

    /**
     * 更新统计显示
     */
    updateStatsDisplay(stats) {
        document.getElementById('highRiskCount').textContent = stats.high_risk_count || 0;
        document.getElementById('mediumRiskCount').textContent = stats.medium_risk_count || 0;
        document.getElementById('lowRiskCount').textContent = stats.low_risk_count || 0;
        document.getElementById('activeRulesCount').textContent = stats.active_rules_count || 0;
        document.getElementById('blockedTransactions').textContent = stats.blocked_transactions || 0;
        document.getElementById('protectedAmount').textContent = '¥' + this.formatAmount(stats.protected_amount || 0);
    }

    /**
     * 获取风险等级文本
     */
    getRiskLevelText(level) {
        const levelMap = {
            'high': '高风险',
            'medium': '中风险',
            'low': '低风险'
        };
        return levelMap[level] || level;
    }

    /**
     * 获取风险等级徽章颜色
     */
    getRiskLevelBadgeColor(level) {
        const colorMap = {
            'high': 'danger',
            'medium': 'warning',
            'low': 'info'
        };
        return colorMap[level] || 'secondary';
    }

    /**
     * 获取事件类型文本
     */
    getEventTypeText(type) {
        const typeMap = {
            'abnormal_amount': '异常金额',
            'frequent_transaction': '频繁交易',
            'suspicious_account': '可疑账户',
            'unusual_time': '异常时间'
        };
        return typeMap[type] || type;
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'pending': '待处理',
            'processing': '处理中',
            'resolved': '已解决',
            'ignored': '已忽略'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取状态徽章颜色
     */
    getStatusBadgeColor(status) {
        const colorMap = {
            'pending': 'warning',
            'processing': 'info',
            'resolved': 'success',
            'ignored': 'secondary'
        };
        return colorMap[status] || 'secondary';
    }

    /**
     * 格式化金额
     */
    formatAmount(amount) {
        return parseFloat(amount || 0).toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(datetime) {
        return new Date(datetime).toLocaleString('zh-CN');
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        console.log('✅', message);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error('❌', message);
    }

    /**
     * 销毁管理器
     */
    destroy() {
        if (this.monitorTimer) {
            clearInterval(this.monitorTimer);
        }
    }
}

// 导出模块
window.FinanceRiskControlManager = FinanceRiskControlManager;

// 创建全局实例
if (typeof window.financeRiskControlManager === 'undefined') {
    window.financeRiskControlManager = new FinanceRiskControlManager();
}

console.log('⚠️ FinanceRiskControlManager 模块加载完成'); 