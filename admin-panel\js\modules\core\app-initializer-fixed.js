/**
 * 应用初始化器 - 修复版本
 * 负责整个PayPal管理系统的启动，不再重复加载静态模块
 * 
 * <AUTHOR> Team
 * @version 2.0.0 - Fixed
 * @since 2024-01-15
 */

class AppInitializerFixed {
    constructor() {
        this.moduleLoader = null;
        this.authManager = null;
        this.uiManager = null;
        this.isInitialized = false;
        this.initializationPromise = null;
        
        // 绑定DOM加载事件
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initialize());
        } else {
            this.initialize();
        }
    }

    /**
     * 初始化应用
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.initializationPromise) {
            return this.initializationPromise;
        }

        this.initializationPromise = this._performInitialization();
        return this.initializationPromise;
    }

    /**
     * 执行实际的初始化过程
     * @private
     * @returns {Promise<void>}
     */
    async _performInitialization() {
        try {
            console.log('🚀 PayPal管理系统启动中... (Fixed Version)');
            
            // 第一步：获取模块加载器实例
            this._getModuleLoader();
            
            // 第二步：检查核心模块（不重新加载）
            this._checkCoreModules();
            
            // 第三步：初始化认证系统
            this._initializeAuth();
            
            // 第四步：检查登录状态并启动相应流程
            this._checkAuthAndStart();
            
            this.isInitialized = true;
            console.log('✅ PayPal管理系统启动完成 (Fixed Version)');
            
        } catch (error) {
            console.error('❌ 应用初始化失败:', error);
            this._showInitializationError(error);
            throw error;
        }
    }

    /**
     * 获取模块加载器实例
     * @private
     */
    _getModuleLoader() {
        console.log('📦 获取模块加载器...');
        
        // 检查ModuleLoader类是否可用
        if (typeof ModuleLoader === 'undefined') {
            throw new Error('ModuleLoader类未找到，请确保module-loader.js已正确加载');
        }
        
        // 优先使用现有的全局实例
        if (window.AdminModuleLoader) {
            console.log('✅ 使用现有的AdminModuleLoader实例');
            this.moduleLoader = window.AdminModuleLoader;
        } else {
            console.log('⚠️ AdminModuleLoader实例未找到，创建新实例');
            this.moduleLoader = new ModuleLoader();
            window.AdminModuleLoader = this.moduleLoader;
        }
        
        // 设置便捷的全局引用
        window.moduleLoader = this.moduleLoader;
        
        console.log('✅ 模块加载器获取完成');
    }

    /**
     * 检查核心模块（不重新加载）
     * @private
     */
    _checkCoreModules() {
        console.log('🔧 检查核心模块状态...');
        
        try {
            // 检查核心模块是否已经静态加载
            const coreModules = [
                { name: 'CONFIG', check: () => typeof window.CONFIG !== 'undefined' },
                { name: 'AdminUtils', check: () => typeof window.AdminUtils !== 'undefined' || typeof window.AdminUtilsInstance !== 'undefined' || typeof window.UtilsModule !== 'undefined' },
                { name: 'AuthManager', check: () => typeof window.AuthManager !== 'undefined' },
                { name: 'UIManager', check: () => typeof window.UIManager !== 'undefined' },
                { name: 'ModuleLoader', check: () => typeof window.ModuleLoader !== 'undefined' || typeof window.AdminModuleLoader !== 'undefined' }
            ];
            
            let allLoaded = true;
            for (const module of coreModules) {
                if (module.check()) {
                    console.log(`✅ ${module.name} 已加载`);
                } else {
                    console.warn(`⚠️ ${module.name} 未找到`);
                    allLoaded = false;
                }
            }
            
            if (allLoaded) {
                console.log('✅ 所有核心模块检查完成');
            } else {
                console.warn('⚠️ 部分核心模块缺失，但继续初始化');
            }
            
        } catch (error) {
            console.error('❌ 核心模块检查失败:', error);
            // 不抛出错误，允许继续初始化
        }
    }

    /**
     * 初始化认证系统
     * @private
     */
    _initializeAuth() {
        console.log('🔐 初始化认证系统...');
        
        // 获取认证管理器类
        const AuthManager = window.AuthManager;
        if (!AuthManager) {
            throw new Error('AuthManager类未找到');
        }
        
        // 创建认证管理器实例
        this.authManager = new AuthManager();
        
        // 设置全局引用
        window.authManager = this.authManager;
        
        console.log('✅ 认证系统初始化完成');
    }

    /**
     * 检查认证状态并启动相应流程
     */
    _checkAuthAndStart() {
        console.log('🔍 检查认证状态...');
        
        if (this.authManager.isAuthenticated()) {
            console.log('👤 用户已登录，启动主应用界面');
            this._startMainApplication();
        } else {
            console.log('🔑 用户未登录，启动登录流程');
            this._startLoginFlow();
        }
    }

    /**
     * 启动主应用界面
     * @private
     */
    _startMainApplication() {
        console.log('🏠 启动主应用界面...');
        
        try {
            // 获取UI管理器类
            const UIManager = window.UIManager;
            if (!UIManager) {
                throw new Error('UIManager类未找到');
            }
            
            // 创建UI管理器实例
            this.uiManager = new UIManager(this.authManager);
            
            // 设置全局引用
            window.uiManager = this.uiManager;
            
            // 隐藏登录界面，显示主应用界面
            this._switchToMainInterface();
            
            console.log('✅ 主应用界面启动完成');
            
        } catch (error) {
            console.error('❌ 主应用界面启动失败:', error);
            throw error;
        }
    }

    /**
     * 启动登录流程
     * @private
     */
    _startLoginFlow() {
        console.log('🔑 启动登录流程...');
        
        try {
            // 获取登录管理器类
            const LoginManager = window.LoginManager;
            if (!LoginManager) {
                console.warn('LoginManager类未找到，跳过登录流程初始化');
                return;
            }
            
            // 创建登录管理器实例
            this.loginManager = new LoginManager();
            
            // 设置全局引用
            window.loginManager = this.loginManager;
            
            // 显示登录界面
            this._switchToLoginInterface();
            
            console.log('✅ 登录流程启动完成');
            
        } catch (error) {
            console.error('❌ 登录流程启动失败:', error);
            // 不抛出错误，允许继续运行
        }
    }

    /**
     * 切换到主界面
     * @private
     */
    _switchToMainInterface() {
        const loginContainer = document.getElementById('loginContainer');
        const appContainer = document.getElementById('appContainer');

        if (loginContainer) {
            loginContainer.style.display = 'none';
            loginContainer.style.visibility = 'hidden';
        }

        if (appContainer) {
            appContainer.classList.remove('app-container');
            appContainer.style.display = 'block';
            appContainer.style.visibility = 'visible';
            
            // 触发主界面创建事件
            if (this.authManager) {
                const user = this.authManager.getUser();
                const event = new CustomEvent('mainInterfaceCreate', {
                    detail: { user, container: appContainer }
                });
                document.dispatchEvent(event);
            }
        }
    }

    /**
     * 切换到登录界面
     * @private
     */
    _switchToLoginInterface() {
        const loginContainer = document.getElementById('loginContainer');
        const appContainer = document.getElementById('appContainer');

        if (appContainer) {
            appContainer.style.display = 'none';
            appContainer.style.visibility = 'hidden';
        }

        if (loginContainer) {
            loginContainer.style.display = 'block';
            loginContainer.style.visibility = 'visible';
        }
    }

    /**
     * 显示初始化错误
     * @private
     * @param {Error} error 错误对象
     */
    _showInitializationError(error) {
        const errorHtml = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <div style="
                    background: white;
                    padding: 40px;
                    border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    text-align: center;
                    max-width: 500px;
                    margin: 20px;
                ">
                    <div style="
                        color: #ef4444;
                        font-size: 48px;
                        margin-bottom: 20px;
                    ">⚠️</div>
                    <h2 style="
                        color: #1f2937;
                        margin-bottom: 15px;
                        font-size: 24px;
                    ">系统初始化失败</h2>
                    <p style="
                        color: #6b7280;
                        margin-bottom: 20px;
                        line-height: 1.6;
                    ">系统启动过程中遇到错误，请检查网络连接或联系技术支持。</p>
                    <details style="
                        text-align: left;
                        margin-bottom: 20px;
                        padding: 10px;
                        background: #f9fafb;
                        border-radius: 8px;
                        font-size: 14px;
                    ">
                        <summary style="cursor: pointer; font-weight: 600;">错误详情</summary>
                        <pre style="
                            margin-top: 10px;
                            color: #ef4444;
                            font-size: 12px;
                            white-space: pre-wrap;
                        ">${error.message}\n${error.stack || ''}</pre>
                    </details>
                    <button onclick="location.reload()" style="
                        background: #3b82f6;
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 16px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: background-color 0.2s;
                    " onmouseover="this.style.backgroundColor='#2563eb'" 
                       onmouseout="this.style.backgroundColor='#3b82f6'">
                        重新加载
                    </button>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', errorHtml);
    }

    /**
     * 获取应用状态
     * @returns {Object} 应用状态信息
     */
    getAppStatus() {
        return {
            initialized: this.isInitialized,
            authenticated: this.authManager ? this.authManager.isAuthenticated() : false,
            currentUser: this.authManager ? this.authManager.getUser() : null,
            loadedModules: this.moduleLoader ? this.moduleLoader.getLoadedModules() : [],
            currentPage: this.uiManager ? this.uiManager.getCurrentPage() : null
        };
    }
}

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
});

// 创建应用初始化器实例
const appInitializerFixed = new AppInitializerFixed();

// 设置全局引用
window.appInitializerFixed = appInitializerFixed;
window.AppInitializerFixed = AppInitializerFixed;

console.log('✅ 修复版应用初始化器已加载'); 