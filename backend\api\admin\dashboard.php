<?php
/**
 * 仪表板接口 - TenantAuth版本
 */

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';

// 初始化租户认证系统
$auth = TenantAuth::init();

// 检查认证并要求管理员权限
if (!$auth->requireAdmin()) {
    exit; // requireAdmin()已经发送了响应
}

// 获取当前用户信息
$currentUser = $auth->getCurrentUser();
$domainInfo = $auth->getDomainInfo();

// 获取数据库连接
$db = $auth->getDatabase();

$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch ($action) {
        case 'dashboard':
            handleDashboard($auth);
            break;
        default:
            $auth->sendError('接口不存在', 404);
    }
} catch (Exception $e) {
    error_log("Dashboard API error: " . $e->getMessage());
    $auth->sendError('服务器错误: ' . $e->getMessage(), 500);
}

// 仪表板数据
function handleDashboard($auth) {
    global $db, $currentUser;
    
    // 检查权限
    if (!$auth->checkPermission('dashboard.view')) {
        $auth->sendForbidden('权限不足');
        return;
    }
    
    try {
        // 获取基础统计数据
        $stats = array();
        
        // 今日订单统计
        $todayOrders = $db->fetch("SELECT COUNT(*) as count, SUM(amount) as total FROM transactions WHERE DATE(created_at) = CURDATE()");
        $stats['today_orders'] = $todayOrders['count'];
        $stats['today_amount'] = isset($todayOrders['total']) ? $todayOrders['total'] : 0;
        
        // 本月订单统计
        $monthOrders = $db->fetch("SELECT COUNT(*) as count, SUM(amount) as total FROM transactions WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())");
        $stats['month_orders'] = $monthOrders['count'];
        $stats['month_amount'] = isset($monthOrders['total']) ? $monthOrders['total'] : 0;
        
        // 活跃设备数
        $activeDevices = $db->fetch("SELECT COUNT(*) as count FROM devices WHERE status = 'active' AND last_checkin_time > DATE_SUB(NOW(), INTERVAL 5 MINUTE)");
        $stats['active_devices'] = $activeDevices['count'];
        
        // 支付成功率
        $successRate = $db->fetch("SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as completed
            FROM transactions 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
        $stats['success_rate'] = $successRate['total'] > 0 ? round(($successRate['completed'] / $successRate['total']) * 100, 2) : 0;
        
        // 最近订单
        $recentOrders = $db->fetchAll("SELECT 
            t.id, t.order_id, t.amount, t.status, t.created_at,
            m.company_name as merchant_name
            FROM transactions t
            LEFT JOIN merchants m ON t.merchant_id = m.id
            ORDER BY t.created_at DESC
            LIMIT 10");
        
        // 系统状态
        $systemStatus = array(
            'database' => checkDatabaseStatus($db),
            'cache' => checkCacheStatus(),
            'storage' => checkStorageStatus()
        );
        
        // 记录操作日志
        $auth->logUserAction('view_dashboard', 'dashboard', 0, '查看仪表板');
        
        $response = array(
            'stats' => $stats,
            'recent_orders' => $recentOrders,
            'system_status' => $systemStatus
        );
        
        $auth->sendSuccess($response, '仪表板数据获取成功');
        
    } catch (Exception $e) {
        error_log("获取仪表板数据失败: " . $e->getMessage());
        $auth->sendError('获取仪表板数据失败: ' . $e->getMessage(), 500);
    }
}

// 检查数据库状态
function checkDatabaseStatus($db) {
    try {
        $db->fetch("SELECT 1");
        return array('status' => 'healthy', 'message' => '数据库连接正常');
    } catch (Exception $e) {
        return array('status' => 'error', 'message' => '数据库连接异常: ' . $e->getMessage());
    }
}

// 检查缓存状态
function checkCacheStatus() {
    // 这里可以检查Redis或其他缓存系统
    return array('status' => 'healthy', 'message' => '缓存系统正常');
}

// 检查存储状态
function checkStorageStatus() {
    try {
        $diskFree = disk_free_space('/');
        $diskTotal = disk_total_space('/');
        
        if ($diskFree === false || $diskTotal === false) {
            return array('status' => 'warning', 'message' => '无法获取磁盘信息');
        }
        
        $usagePercent = round((($diskTotal - $diskFree) / $diskTotal) * 100, 2);
        
        $status = $usagePercent > 90 ? 'warning' : 'healthy';
        $message = "磁盘使用率: {$usagePercent}%";
        
        return array('status' => $status, 'message' => $message);
    } catch (Exception $e) {
        return array('status' => 'error', 'message' => '磁盘检查失败: ' . $e->getMessage());
    }
}
?> 