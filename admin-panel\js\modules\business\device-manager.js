/**
 * 设备管理器
 * 从admin.js第5235-5993行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class DeviceManager {
    constructor() {
        this.auth = new AuthManager();
        this.currentPage = 1;
        this.pageSize = 20;
    }
    async loadDevices(page = 1) {
        this.currentPage = page;
        const status = document.getElementById('filterStatus')?.value || '';
        const brand = document.getElementById('filterBrand')?.value || '';
        const providerId = document.getElementById('filterProvider')?.value || '';
        const search = document.getElementById('searchInput')?.value || '';
        const params = new URLSearchParams({
            action: 'devices',
            page: page,
            limit: this.pageSize
        });
        if (status) params.append('status',
        status);
        if (brand) params.append('brand',
        brand);
        if (providerId) params.append('provider_id',
        providerId);
        if (search) params.append('search',
        search);
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?${
                params
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200) {
                this.renderDeviceList(data.data.devices);
                this.renderStats(data.data.stats);
                this.renderFilters(data.data.brands,
                data.data.providers);
                this.renderPagination(data.data.pagination);
            } else {
                this.showError('加载设备列表失败: ' + data.message);
            }
        } catch (error) {
            console.error('Load devices error:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    renderDeviceList(devices) {
        const tbody = document.getElementById('deviceTableBody');
        if (!tbody) return;
        if (devices.length === 0) {
            tbody.innerHTML = `
            <tr>
            <td colspan="9" class="text-center text-muted">
            <i class="bi bi-inbox" style="font-size: 2rem;
            "></i>
            <div>暂无设备数据</div>
            </td>
            </tr>
            `;
            return;
        }
        tbody.innerHTML = devices.map(device => `
        <tr>
        <td>
        <span class="device-id">${
            device.device_id
        }</span>
        </td>
        <td>${
            device.device_name || '-'
        }</td>
        <td>
        <div>${
            device.device_brand || '-'
        }</div>
        <small class="text-muted">${
            device.device_model || '-'
        }</small>
        </td>
        <td>
        <span class="status-badge status-${
            device.status
        }">${
            this.getStatusText(device.status)
        }</span>
        </td>
        <td>
        <span class="online-badge online-${
            this.getOnlineClass(device.online_status)
        }">${
            device.online_status
        }</span>
        </td>
        <td>
        <span class="badge ${
            device.password_status === '已设置' ? 'bg-success' : 'bg-warning'
        }">${
            device.password_status
        }</span>
        </td>
        <td>${
            device.provider_name || '-'
        }</td>
        <td>${
            this.formatDateTime(device.last_online)
        }</td>
        <td>
        <button class="btn btn-outline-info btn-action" onclick="window.deviceManager.viewDevice('${
            device.device_id
        }')" title="查看详情">
        <i class="bi bi-eye"></i>
        </button>
        ${
            this.renderActionButtons(device)
        }
        </td>
        </tr>
        `).join('');
    }
    renderActionButtons(device) {
        const user = this.auth.getUser();
        let buttons = '';
        // 分配码商按钮（仅管理员，且设备状态为pending）
        if (user.user_type === 'admin' && device.status === 'pending') {
            buttons += `
            <button class="btn btn-outline-primary btn-action" onclick="window.deviceManager.showAssignProvider('${
                device.device_id
            }')" title="分配码商">
            <i class="bi bi-person-plus"></i>
            </button>
            `;
        }
        // 状态控制按钮
        if (device.status === 'active') {
            buttons += `
            <button class="btn btn-outline-warning btn-action" onclick="window.deviceManager.updateStatus('${
                device.device_id
            }', 'disabled')" title="禁用设备">
            <i class="bi bi-pause-circle"></i>
            </button>
            `;
        } else if (device.status === 'disabled') {
            buttons += `
            <button class="btn btn-outline-success btn-action" onclick="window.deviceManager.updateStatus('${
                device.device_id
            }', 'active')" title="启用设备">
            <i class="bi bi-play-circle"></i>
            </button>
            `;
        }
        // 重置密码按钮
        if (device.password_status === '已设置') {
            buttons += `
            <button class="btn btn-outline-danger btn-action" onclick="window.deviceManager.resetPassword('${
                device.device_id
            }')" title="重置密码">
            <i class="bi bi-key"></i>
            </button>
            `;
        }
        // 删除按钮（仅管理员）
        if (user.user_type === 'admin') {
            buttons += `
            <button class="btn btn-outline-danger btn-action" onclick="window.deviceManager.deleteDevice('${
                device.device_id
            }')" title="删除设备">
            <i class="bi bi-trash"></i>
            </button>
            `;
        }
        return buttons;
    }
    renderStats(stats) {
        document.getElementById('totalDevices').textContent = stats.total;
        document.getElementById('activeDevices').textContent = stats.active;
        document.getElementById('pendingDevices').textContent = stats.pending;
        document.getElementById('onlineDevices').textContent = stats.online;
    }
    renderFilters(brands,
    providers) {
        // 渲染品牌筛选
        const brandSelect = document.getElementById('filterBrand');
        if (brandSelect) {
            const currentValue = brandSelect.value;
            brandSelect.innerHTML = '<option value="">所有品牌</option>' +
            brands.map(brand => `<option value="${
                brand
            }" ${
                brand === currentValue ? 'selected' : ''
            }>${
                brand
            }</option>`).join('');
        }
        // 渲染码商筛选（仅管理员可见）
        const providerSelect = document.getElementById('filterProvider');
        const providerContainer = document.getElementById('providerFilterContainer');
        if (providerSelect && providerContainer) {
            const user = this.auth.getUser();
            if (user.user_type === 'admin') {
                providerContainer.style.display = 'block';
                const currentValue = providerSelect.value;
                providerSelect.innerHTML = '<option value="">所有码商</option>' +
                providers.map(provider => `<option value="${
                    provider.id
                }" ${
                    provider.id == currentValue ? 'selected' : ''
                }>${
                    provider.company_name
                }</option>`).join('');
            } else {
                providerContainer.style.display = 'none';
            }
        }
    }
    renderPagination(pagination) {
        const nav = document.getElementById('devicePagination');
        if (!nav || pagination.total_pages <= 1) {
            nav.style.display = 'none';
            return;
        }
        nav.style.display = 'block';
        const ul = nav.querySelector('ul');
        let paginationHtml = '';
        // 上一页
        if (pagination.current_page > 1) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.deviceManager.loadDevices(${
                pagination.current_page - 1
            })">上一页</a>
            </li>
            `;
        }
        // 页码
        const startPage = Math.max(1,
        pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages,
        pagination.current_page + 2);
        for (let i = startPage;
        i <= endPage;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === pagination.current_page ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.deviceManager.loadDevices(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        // 下一页
        if (pagination.current_page < pagination.total_pages) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.deviceManager.loadDevices(${
                pagination.current_page + 1
            })">下一页</a>
            </li>
            `;
        }
        ul.innerHTML = paginationHtml;
    }
    async showAssignProvider(deviceId) {
        // 获取码商列表
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=providers`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200) {
                const select = document.getElementById('assignProviderId');
                select.innerHTML = '<option value="">请选择码商</option>' +
                data.data.filter(p => p.status === 'approved').map(provider =>
                `<option value="${
                    provider.id
                }">${
                    provider.company_name
                }</option>`
                ).join('');
                document.getElementById('assignDeviceId').value = deviceId;
                const modal = new bootstrap.Modal(document.getElementById('assignProviderModal'));
                modal.show();
            }
        } catch (error) {
            this.showError('获取码商列表失败');
        }
    }
    async confirmAssignProvider() {
        const deviceId = document.getElementById('assignDeviceId').value;
        const providerId = document.getElementById('assignProviderId').value;
        if (!providerId) {
            this.showError('请选择码商');
            return;
        }
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=devices`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    action: 'assign_provider',
                    device_id: deviceId,
                    provider_id: providerId
                })
            });
            const data = await response.json();
            if (data.code === 200) {
                this.showSuccess(data.message);
                bootstrap.Modal.getInstance(document.getElementById('assignProviderModal')).hide();
                this.loadDevices();
            } else {
                this.showError('分配失败: ' + data.message);
            }
        } catch (error) {
            this.showError('网络错误，请重试');
        }
    }
    async updateStatus(deviceId,
    status) {
        const statusText = status === 'active' ? '启用' : '禁用';
        if (!confirm(`确定要${
            statusText
        }设备 "${
            deviceId
        }" 吗？`)) {
            return;
        }
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=devices`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    action: 'update_status',
                    device_id: deviceId,
                    status: status
                })
            });
            const data = await response.json();
            if (data.code === 200) {
                this.showSuccess(data.message);
                this.loadDevices();
            } else {
                this.showError('操作失败: ' + data.message);
            }
        } catch (error) {
            this.showError('网络错误，请重试');
        }
    }
    async resetPassword(deviceId) {
        if (!confirm(`确定要重置设备 "${
            deviceId
        }" 的密码吗？用户需要重新设置密码。`)) {
            return;
        }
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=devices`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    action: 'reset_password',
                    device_id: deviceId
                })
            });
            const data = await response.json();
            if (data.code === 200) {
                this.showSuccess(data.message);
                this.loadDevices();
            } else {
                this.showError('重置失败: ' + data.message);
            }
        } catch (error) {
            this.showError('网络错误，请重试');
        }
    }
    async deleteDevice(deviceId) {
        if (!confirm(`确定要删除设备 "${
            deviceId
        }" 吗？此操作不可撤销。`)) {
            return;
        }
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=devices&device_id=${
                deviceId
            }`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200) {
                this.showSuccess(data.message);
                this.loadDevices();
            } else {
                this.showError('删除失败: ' + data.message);
            }
        } catch (error) {
            this.showError('网络错误，请重试');
        }
    }
    async viewDevice(deviceId) {
        // 这里可以实现设备详情查看功能
        this.showInfo('设备详情功能开发中...');
    }
    refreshDevices() {
        this.loadDevices(this.currentPage);
    }
    getStatusText(status) {
        const statusMap = {
            'active': '已激活',
            'pending': '待分配',
            'disabled': '已禁用'
        };
        return statusMap[status] || status;
    }
    getOnlineClass(onlineStatus) {
        const classMap = {
            '在线': 'online',
            '不活跃': 'inactive',
            '离线': 'offline',
            '从未在线': 'never'
        };
        return classMap[onlineStatus] || 'never';
    }
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    // 显示加载状态
    showLoadingState() {
        const container = document.getElementById('accountsTableContainer');
        if (container) {
            container.innerHTML = `
            <div class="text-center p-4">
            <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载账户数据...</p>
            </div>
            `;
        }
    }
    // 显示错误状态
    showErrorState(message) {
        const container = document.getElementById('accountsTableContainer');
        if (container) {
            container.innerHTML = `
            <div class="text-center p-4">
            <div class="text-danger">
            <i class="bi bi-exclamation-triangle" style="font-size: 2rem;
            "></i>
            <p class="mt-2 mb-0">${
                message
            }</p>
            </div>
            </div>
            `;
        }
    }
    // 更新账户数量显示
    updateAccountCount(count) {
        const countElement = document.getElementById('accountCount');
        if (countElement) {
            countElement.textContent = count || 0;
        }
    }
    // 新的渲染方法适配新的API响应格式
    renderAccountListNew(accounts) {
        const container = document.getElementById('accountsTableContainer');
        if (!container) return;
        if (!accounts || accounts.length === 0) {
            container.innerHTML = `
            <div class="text-center p-5">
            <div class="text-muted">
            <i class="bi bi-inbox" style="font-size: 3rem;
            opacity: 0.5;
            "></i>
            <div class="mt-3">暂无账户数据</div>
            <small>请尝试调整筛选条件或添加新账户</small>
            </div>
            </div>
            `;
            return;
        }
        container.innerHTML = `
        <div class="table-responsive">
        <table class="table table-hover">
        <thead class="table-light">
        <tr>
        <th>ID</th>
        <th>账户名称</th>
        <th>支付宝账号</th>
        <th>实名</th>
        <th>手机号</th>
        <th>状态</th>
        <th>日限额</th>
        <th>创建时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        ${
            accounts.map(account => `
            <tr>
            <td><span class="badge bg-secondary">${
                account.id
            }</span></td>
            <td>
            <div class="fw-medium">${
                account.account_name || '-'
            }</div>
            ${
                account.provider_name ? `<small class="text-muted">${
                    account.provider_name
                }</small>` : ''
            }
            </td>
            <td><code class="small">${
                account.account_number || '-'
            }</code></td>
            <td>${
                account.real_name || '<span class="text-muted">-</span>'
            }</td>
            <td>${
                account.phone || '<span class="text-muted">-</span>'
            }</td>
            <td>${
                this.renderStatusBadge(account.status)
            }</td>
            <td>
            <span class="fw-medium text-success">¥${
                parseFloat(account.daily_limit || 0).toLocaleString()
            }</span>
            </td>
            <td>
            <small class="text-muted">${
                this.formatDateTime(account.created_at)
            }</small>
            </td>
            <td>
            <div class="btn-group btn-group-sm" role="group">
            ${
                this.renderActionButtonsNew(account)
            }
            </div>
            </td>
            </tr>
            `).join('')
        }
        </tbody>
        </table>
        </div>
        `;
    }
    // 渲染状态徽章
    renderStatusBadge(status) {
        const statusConfig = {
            'pending': {
                class: 'bg-warning text-dark',
                text: '待审核'
            },
            'approved': {
                class: 'bg-success',
                text: '已审核'
            },
            'rejected': {
                class: 'bg-danger',
                text: '已拒绝'
            },
            'disabled': {
                class: 'bg-secondary',
                text: '已禁用'
            }
        };
        const config = statusConfig[status] || {
            class: 'bg-secondary',
            text: status
        };
        return `<span class="badge ${
            config.class
        }">${
            config.text
        }</span>`;
    }
    // 新的操作按钮渲染
    renderActionButtonsNew(account) {
        const user = this.auth.getUser();
        let buttons = '';
        // 编辑按钮
        buttons += `
        <button class="btn btn-outline-primary btn-sm" onclick="window.accountManager.editAccount(${
            account.id
        })" title="编辑" style="color: #3b82f6 !important;
        ">
        <i class="bi bi-pencil"></i>
        </button>
        `;
        // 根据状态显示不同的操作按钮
        if (account.status === 'pending' && ['admin', 'provider'].includes(user?.user_type)) {
            buttons += `
            <button class="btn btn-outline-success btn-sm" onclick="window.accountManager.approveAccount(${
                account.id
            })" title="通过" style="color: #10b981 !important;
            ">
            <i class="bi bi-check-circle"></i>
            </button>
            <button class="btn btn-outline-warning btn-sm" onclick="window.accountManager.rejectAccount(${
                account.id
            })" title="拒绝" style="color: #f59e0b !important;
            ">
            <i class="bi bi-x-circle"></i>
            </button>
            `;
        } else if (account.status === 'approved') {
            buttons += `
            <button class="btn btn-outline-warning btn-sm" onclick="window.accountManager.disableAccount(${
                account.id
            })" title="禁用" style="color: #f59e0b !important;
            ">
            <i class="bi bi-pause-circle"></i>
            </button>
            `;
        } else if (account.status === 'disabled') {
            buttons += `
            <button class="btn btn-outline-success btn-sm" onclick="window.accountManager.enableAccount(${
                account.id
            })" title="启用" style="color: #10b981 !important;
            ">
            <i class="bi bi-play-circle"></i>
            </button>
            `;
        }
        // 删除按钮
        if (['admin', 'provider'].includes(user?.user_type)) {
            buttons += `
            <button class="btn btn-outline-danger btn-sm" onclick="window.accountManager.deleteAccount(${
                account.id
            })" title="删除" style="color: #ef4444 !important;
            ">
            <i class="bi bi-trash"></i>
            </button>
            `;
        }
        return buttons;
    }
    // 渲染统计信息
    renderStatsNew(stats) {
        if (!stats) return;
        document.getElementById('totalAccounts').textContent = stats.total_accounts || 0;
        document.getElementById('pendingAccounts').textContent = stats.pending_accounts || 0;
        document.getElementById('approvedAccounts').textContent = stats.approved_accounts || 0;
        document.getElementById('rejectedAccounts').textContent = stats.rejected_accounts || 0;
        document.getElementById('disabledAccounts').textContent = stats.disabled_accounts || 0;
        const totalLimit = stats.total_daily_limit || 0;
        document.getElementById('totalDailyLimit').textContent = '¥' + parseFloat(totalLimit).toLocaleString();
    }
    // 新的分页渲染方法
    renderPaginationNew(pagination) {
        const container = document.getElementById('paginationContainer');
        if (!container || !pagination || pagination.total_pages <= 1) {
            if (container) container.innerHTML = '';
            return;
        }
        const currentPage = pagination.current_page;
        const totalPages = pagination.total_pages;
        let paginationHtml = '<nav aria-label="账户列表分页"><ul class="pagination justify-content-center">';
        // 上一页
        if (currentPage > 1) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.accountManager.loadAccountsForCurrentUser(${
                currentPage - 1
            })">
            <i class="bi bi-chevron-left"></i>
            </a>
            </li>
            `;
        }
        // 页码
        const startPage = Math.max(1,
        currentPage - 2);
        const endPage = Math.min(totalPages,
        currentPage + 2);
        if (startPage > 1) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.accountManager.loadAccountsForCurrentUser(1)">1</a>
            </li>
            `;
            if (startPage > 2) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        for (let i = startPage;
        i <= endPage;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === currentPage ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.accountManager.loadAccountsForCurrentUser(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.accountManager.loadAccountsForCurrentUser(${
                totalPages
            })">${
                totalPages
            }</a>
            </li>
            `;
        }
        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.accountManager.loadAccountsForCurrentUser(${
                currentPage + 1
            })">
            <i class="bi bi-chevron-right"></i>
            </a>
            </li>
            `;
        }
        paginationHtml += '</ul></nav>';
        container.innerHTML = paginationHtml;
    }
    // 刷新账户列表
    refreshAccounts() {
        this.loadAccountsForCurrentUser(1);
    }
    // 搜索账户
    searchAccounts() {
        this.loadAccountsForCurrentUser(1);
    }
    // 应用筛选
    applyFilters() {
        this.loadAccountsForCurrentUser(1);
    }
    // 导出账户
    exportAccounts() {
        this.showInfo('导出功能开发中...');
    }
    // 切换视图
    toggleView() {
        this.showInfo('视图切换功能开发中...');
    }
    // 显示添加账户模态框
    showAddAccountModal() {
        this.showInfo('添加账户功能开发中...');
    }
    showSuccess(message) {
        // 创建并显示成功提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        ';
        alert.innerHTML = `
        <i class="bi bi-check-circle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);
        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
    showError(message) {
        // 创建并显示错误提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        ';
        alert.innerHTML = `
        <i class="bi bi-exclamation-triangle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);
        // 5秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);
    }
    showInfo(message) {
        // 创建并显示信息提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-info alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        ';
        alert.innerHTML = `
        <i class="bi bi-info-circle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);
        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
    showInfo(message) {
        alert('ℹ️ ' + message);
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = DeviceManager;
} else if (typeof window !== "undefined") {
    window.DeviceManager = DeviceManager;
}

console.log('📦 DeviceManager 模块加载完成');
