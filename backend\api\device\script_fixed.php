<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(dirname(__FILE__))) . '/utils/Auth.php';

// 加密工具类
class CryptoUtils {
    private static $key = 'your-secret-key-32-characters!!';
    
    public static function encrypt($data) {
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', self::$key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    public static function decrypt($data) {
        $data = base64_decode($data);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', self::$key, 0, $iv);
    }
}

// 获取认证token
function getAuthToken() {
    $headers = array();
    if (function_exists('getallheaders')) {
        $headers = getallheaders();
    } else {
        // PHP 5.6 fallback
        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $headers['Authorization'] = $_SERVER['HTTP_AUTHORIZATION'];
        }
    }
    
    if (isset($headers['Authorization'])) {
        return str_replace('Bearer ', '', $headers['Authorization']);
    }
    return null;
}

// 验证管理员权限
function verifyAdminToken($token) {
    if (!$token) return false;
    
    try {
        $db = new Database();
        $pdo = $db->connect();
        
        $stmt = $pdo->prepare("SELECT * FROM admin_sessions WHERE token = ? AND expires_at > NOW()");
        $stmt->execute([$token]);
        $session = $stmt->fetch();
        
        if ($session) {
            $stmt = $pdo->prepare("SELECT * FROM admins WHERE id = ? AND status = 'active'");
            $stmt->execute([$session['admin_id']]);
            return $stmt->fetch() !== false;
        }
        
        return false;
    } catch (Exception $e) {
        return false;
    }
}

try {
    $db = new Database();
    $pdo = $db->connect();
    
    $action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');
    
    // 验证管理员权限
    $token = getAuthToken();
    if (!verifyAdminToken($token)) {
        echo json_encode(array(
            'success' => false,
            'message' => '需要管理员权限'
        ));
        exit;
    }
    
    switch ($action) {
        case 'upload':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('只支持POST请求');
            }
            
            $app_type = isset($_POST['app_type']) ? $_POST['app_type'] : '';
            $app_version = isset($_POST['app_version']) ? $_POST['app_version'] : '';
            $device_brand = isset($_POST['device_brand']) ? $_POST['device_brand'] : '';
            $environment = isset($_POST['environment']) ? $_POST['environment'] : '';
            $description = isset($_POST['description']) ? $_POST['description'] : '';
            $script_content = isset($_POST['script_content']) ? $_POST['script_content'] : '';
            
            if (empty($app_type) || empty($app_version) || empty($device_brand) || empty($environment) || empty($script_content)) {
                throw new Exception('缺少必要参数');
            }
            
            // 生成脚本名称
            $script_name = $app_type . '_' . $app_version . '_' . $device_brand . '_' . $environment . '.js';
            
            // 加密脚本内容
            $encrypted_content = CryptoUtils::encrypt($script_content);
            
            // 插入数据库
            $stmt = $pdo->prepare("INSERT INTO script_versions (script_name, app_type, app_version, device_brand, environment, description, script_content, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$script_name, $app_type, $app_version, $device_brand, $environment, $description, $encrypted_content]);
            
            echo json_encode(array(
                'success' => true,
                'message' => '脚本上传成功',
                'data' => array('id' => $pdo->lastInsertId())
            ));
            break;
            
        case 'list':
            $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
            $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
            $app_type = isset($_GET['app_type']) ? $_GET['app_type'] : '';
            $device_brand = isset($_GET['device_brand']) ? $_GET['device_brand'] : '';
            $environment = isset($_GET['environment']) ? $_GET['environment'] : '';
            
            $offset = ($page - 1) * $limit;
            
            $where_conditions = array();
            $params = array();
            
            if (!empty($app_type)) {
                $where_conditions[] = "app_type = ?";
                $params[] = $app_type;
            }
            
            if (!empty($device_brand)) {
                $where_conditions[] = "device_brand = ?";
                $params[] = $device_brand;
            }
            
            if (!empty($environment)) {
                $where_conditions[] = "environment = ?";
                $params[] = $environment;
            }
            
            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            
            // 获取总数
            $count_sql = "SELECT COUNT(*) FROM script_versions $where_clause";
            $stmt = $pdo->prepare($count_sql);
            $stmt->execute($params);
            $total = $stmt->fetchColumn();
            
            // 获取数据
            $params[] = $limit;
            $params[] = $offset;
            $sql = "SELECT id, script_name, app_type, app_version, device_brand, environment, description, created_at FROM script_versions $where_clause ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $scripts = $stmt->fetchAll();
            
            echo json_encode(array(
                'success' => true,
                'data' => array(
                    'scripts' => $scripts,
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit
                )
            ));
            break;
            
        case 'download':
            $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
            if (!$id) {
                throw new Exception('缺少脚本ID');
            }
            
            $stmt = $pdo->prepare("SELECT script_name, script_content FROM script_versions WHERE id = ?");
            $stmt->execute([$id]);
            $script = $stmt->fetch();
            
            if (!$script) {
                throw new Exception('脚本不存在');
            }
            
            // 解密内容
            $decrypted_content = CryptoUtils::decrypt($script['script_content']);
            
            echo json_encode(array(
                'success' => true,
                'data' => array(
                    'filename' => $script['script_name'],
                    'content' => $decrypted_content
                )
            ));
            break;
            
        case 'delete':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('只支持POST请求');
            }
            
            $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
            if (!$id) {
                throw new Exception('缺少脚本ID');
            }
            
            $stmt = $pdo->prepare("DELETE FROM script_versions WHERE id = ?");
            $stmt->execute([$id]);
            
            if ($stmt->rowCount() > 0) {
                echo json_encode(array(
                    'success' => true,
                    'message' => '脚本删除成功'
                ));
            } else {
                throw new Exception('脚本不存在或删除失败');
            }
            break;
            
        case 'update':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('只支持POST请求');
            }
            
            $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
            $description = isset($_POST['description']) ? $_POST['description'] : '';
            $script_content = isset($_POST['script_content']) ? $_POST['script_content'] : '';
            
            if (!$id) {
                throw new Exception('缺少脚本ID');
            }
            
            $update_fields = array();
            $params = array();
            
            if (!empty($description)) {
                $update_fields[] = "description = ?";
                $params[] = $description;
            }
            
            if (!empty($script_content)) {
                $update_fields[] = "script_content = ?";
                $params[] = CryptoUtils::encrypt($script_content);
            }
            
            if (empty($update_fields)) {
                throw new Exception('没有要更新的内容');
            }
            
            $params[] = $id;
            $sql = "UPDATE script_versions SET " . implode(', ', $update_fields) . " WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            echo json_encode(array(
                'success' => true,
                'message' => '脚本更新成功'
            ));
            break;
            
        default:
            throw new Exception('不支持的操作');
    }
    
} catch (Exception $e) {
    echo json_encode(array(
        'success' => false,
        'message' => $e->getMessage()
    ));
}
?> 