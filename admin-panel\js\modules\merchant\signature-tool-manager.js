/**
 * 商户签名工具管理器
 * 从admin.js第16562-17197行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class MerchantSignatureToolManager {
    constructor() {
        this.auth = new AuthManager();
        this.merchantKeys = null;
        this.currentTool = 'generator';
        // generator,
        verifier,
        tester
    }
    async initialize() {
        try {
            await this.loadMerchantKeys();
            this.renderSignatureTool();
        } catch (error) {
            console.error('初始化签名工具失败:',
            error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }
    async loadMerchantKeys() {
        try {
            const response = await fetch('/api/merchant/index.php?module=signature_tool&action=get_merchant_keys', {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.merchantKeys = data.data || data;
                return true;
            } else {
                this.showError('加载商户密钥失败: ' + (data.message || '未知错误'));
                return false;
            }
        } catch (error) {
            console.error('加载商户密钥失败:',
            error);
            this.showError('网络错误，请重试');
            return false;
        }
    }
    renderSignatureTool() {
        const container = document.getElementById('signatureToolContainer');
        if (!container) return;
        container.innerHTML = `
        <div class="row">
        <!-- 左侧：工具选择 -->
        <div class="col-md-3">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-tools me-2"></i>工具选择</h6>
        </div>
        <div class="card-body p-0">
        <div class="list-group list-group-flush">
        <a href="#" class="list-group-item list-group-item-action ${
            this.currentTool === 'generator' ? 'active' : ''
        }"
        onclick="window.signatureToolManager.switchTool('generator')">
        <i class="bi bi-key me-2"></i>签名生成器
        </a>
        <a href="#" class="list-group-item list-group-item-action ${
            this.currentTool === 'verifier' ? 'active' : ''
        }"
        onclick="window.signatureToolManager.switchTool('verifier')">
        <i class="bi bi-shield-check me-2"></i>签名验证器
        </a>
        <a href="#" class="list-group-item list-group-item-action ${
            this.currentTool === 'tester' ? 'active' : ''
        }"
        onclick="window.signatureToolManager.switchTool('tester')">
        <i class="bi bi-cloud-arrow-up me-2"></i>API测试器
        </a>
        </div>
        </div>
        </div>
        <!-- 商户信息卡片 -->
        <div class="card mt-3">
        <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>商户信息</h6>
        </div>
        <div class="card-body">
        ${
            this.merchantKeys ? `
            <p><strong>开发者ID:</strong> ${
                this.merchantKeys.developer_id
            }</p>
            <p><strong>API密钥:</strong> ${
                this.merchantKeys.api_key_preview
            }</p>
            <p class="text-muted small">完整密钥已自动填入，请勿在页面上显示完整密钥</p>
            ` : '<p class="text-muted">加载中...</p>'
        }
        </div>
        </div>
        </div>
        <!-- 右侧：工具内容 -->
        <div class="col-md-9">
        <div id="toolContent">
        <!-- 工具内容将在这里动态加载 -->
        </div>
        </div>
        </div>
        `;
        this.renderCurrentTool();
    }
    switchTool(tool) {
        this.currentTool = tool;
        // 更新左侧菜单状态
        document.querySelectorAll('.list-group-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[onclick*="'${
            tool
        }'"]`).classList.add('active');
        this.renderCurrentTool();
    }
    renderCurrentTool() {
        const toolContent = document.getElementById('toolContent');
        if (!toolContent) return;
        switch (this.currentTool) {
            case 'generator':
            this.renderSignatureGenerator(toolContent);
            break;
            case 'verifier':
            this.renderSignatureVerifier(toolContent);
            break;
            case 'tester':
            this.renderApiTester(toolContent);
            break;
        }
    }
    renderSignatureGenerator(container) {
        container.innerHTML = `
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-key me-2"></i>签名生成器</h6>
        </div>
        <div class="card-body">
        <div class="row">
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">时间戳</label>
        <div class="input-group">
        <input type="number" class="form-control" id="genTimestamp" value="${
            Math.floor(Date.now() / 1000)
        }">
        <button class="btn btn-outline-secondary" onclick="document.getElementById('genTimestamp').value = Math.floor(Date.now() / 1000)">
        <i class="bi bi-arrow-clockwise"></i>
        </button>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">业务参数 (JSON格式)</label>
        <textarea class="form-control" id="genParams" rows="8" placeholder='{
            "amount": 100, "type": 2, "product_id": 1, "order_no": "ORDER123"
        }'></textarea>
        </div>
        <div class="d-grid gap-2">
        <button class="btn btn-primary" onclick="window.signatureToolManager.generateSignature()">
        <i class="bi bi-key me-2"></i>生成签名
        </button>
        <button class="btn btn-outline-secondary" onclick="window.signatureToolManager.clearGenerator()">
        <i class="bi bi-x-circle me-2"></i>清空
        </button>
        </div>
        </div>
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">生成的签名</label>
        <div class="input-group">
        <input type="text" class="form-control" id="generatedSignature" readonly placeholder="点击生成签名按钮">
        <button class="btn btn-outline-primary" onclick="window.signatureToolManager.copySignature('generatedSignature')">
        <i class="bi bi-clipboard"></i>
        </button>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">签名调试信息</label>
        <textarea class="form-control" id="signatureDebugInfo" rows="8" readonly placeholder="签名调试信息将在这里显示"></textarea>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    renderSignatureVerifier(container) {
        container.innerHTML = `
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-shield-check me-2"></i>签名验证器</h6>
        </div>
        <div class="card-body">
        <div class="row">
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">时间戳</label>
        <input type="number" class="form-control" id="verifyTimestamp" value="${
            Math.floor(Date.now() / 1000)
        }">
        </div>
        <div class="mb-3">
        <label class="form-label">业务参数 (JSON格式)</label>
        <textarea class="form-control" id="verifyParams" rows="6" placeholder='{
            "amount": 100, "type": 2, "product_id": 1, "order_no": "ORDER123"
        }'></textarea>
        </div>
        <div class="mb-3">
        <label class="form-label">待验证的签名</label>
        <input type="text" class="form-control" id="expectedSignature" placeholder="输入要验证的签名">
        </div>
        <div class="d-grid gap-2">
        <button class="btn btn-warning" onclick="window.signatureToolManager.verifySignature()">
        <i class="bi bi-shield-check me-2"></i>验证签名
        </button>
        <button class="btn btn-outline-secondary" onclick="window.signatureToolManager.clearVerifier()">
        <i class="bi bi-x-circle me-2"></i>清空
        </button>
        </div>
        </div>
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">验证结果</label>
        <div id="verificationResult" class="border rounded p-3" style="min-height: 100px;
        background: #f8f9fa;
        ">
        <p class="text-muted mb-0">点击验证按钮查看结果</p>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">计算得到的签名</label>
        <div class="input-group">
        <input type="text" class="form-control" id="calculatedSignature" readonly placeholder="验证时会显示计算结果">
        <button class="btn btn-outline-primary" onclick="window.signatureToolManager.copySignature('calculatedSignature')">
        <i class="bi bi-clipboard"></i>
        </button>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">验证调试信息</label>
        <textarea class="form-control" id="verifyDebugInfo" rows="4" readonly placeholder="验证调试信息将在这里显示"></textarea>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    renderApiTester(container) {
        container.innerHTML = `
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-cloud-arrow-up me-2"></i>API测试器</h6>
        </div>
        <div class="card-body">
        <div class="row">
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">API接口类型</label>
        <select class="form-select" id="apiTestType">
        <option value="pay_get_qrcode">获取收款页面</option>
        <option value="pay_get_orders_inquiry">查询订单信息</option>
        <option value="pay_get_payment_quota">查询产品可用额度</option>
        </select>
        </div>
        <div class="mb-3">
        <label class="form-label">请求参数 (JSON格式)</label>
        <textarea class="form-control" id="apiTestParams" rows="8" placeholder='{
            "amount": 100, "type": 2, "product_id": 1, "order_no": "ORDER123"
        }'></textarea>
        </div>
        <div class="d-grid gap-2">
        <button class="btn btn-success" onclick="window.signatureToolManager.testApi()">
        <i class="bi bi-cloud-arrow-up me-2"></i>测试API
        </button>
        <button class="btn btn-outline-secondary" onclick="window.signatureToolManager.clearTester()">
        <i class="bi bi-x-circle me-2"></i>清空
        </button>
        </div>
        </div>
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">请求信息</label>
        <div id="requestInfo" class="border rounded p-3" style="min-height: 150px;
        background: #f8f9fa;
        ">
        <p class="text-muted mb-0">点击测试按钮查看请求详情</p>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">响应结果</label>
        <div id="responseResult" class="border rounded p-3" style="min-height: 200px;
        background: #f8f9fa;
        overflow-y: auto;
        ">
        <p class="text-muted mb-0">API响应将在这里显示</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    async generateSignature() {
        try {
            const timestamp = document.getElementById('genTimestamp').value;
            const paramsText = document.getElementById('genParams').value;
            if (!timestamp || !paramsText) {
                this.showError('请填写时间戳和业务参数');
                return;
            }
            let params;
            try {
                params = JSON.parse(paramsText);
            } catch (e) {
                this.showError('业务参数不是有效的JSON格式');
                return;
            }
            const response = await fetch('/api/merchant/index.php?module=signature_tool&action=generate_signature', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    params: params,
                    developer_key: this.merchantKeys.api_key,
                    timestamp: parseInt(timestamp)
                })
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                document.getElementById('generatedSignature').value = data.data.signature || data.signature;
                document.getElementById('signatureDebugInfo').value = this.formatDebugInfo(data.data.debug_info || data.debug_info);
                this.showSuccess('签名生成成功！');
            } else {
                this.showError('签名生成失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('生成签名失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async verifySignature() {
        try {
            const timestamp = document.getElementById('verifyTimestamp').value;
            const paramsText = document.getElementById('verifyParams').value;
            const expectedSignature = document.getElementById('expectedSignature').value;
            if (!timestamp || !paramsText || !expectedSignature) {
                this.showError('请填写所有必要字段');
                return;
            }
            let params;
            try {
                params = JSON.parse(paramsText);
            } catch (e) {
                this.showError('业务参数不是有效的JSON格式');
                return;
            }
            const response = await fetch('/api/merchant/index.php?module=signature_tool&action=verify_signature', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    params: params,
                    developer_key: this.merchantKeys.api_key,
                    timestamp: parseInt(timestamp),
                    expected_signature: expectedSignature
                })
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                const result = data.data || data;
                document.getElementById('calculatedSignature').value = result.calculated_signature;
                document.getElementById('verifyDebugInfo').value = this.formatDebugInfo(result.debug_info);
                const resultDiv = document.getElementById('verificationResult');
                if (result.is_valid) {
                    resultDiv.innerHTML = `
                    <div class="alert alert-success mb-0">
                    <i class="bi bi-check-circle me-2"></i>
                    <strong>验证成功！</strong><br>
                    签名匹配，验证通过。
                    </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                    <div class="alert alert-danger mb-0">
                    <i class="bi bi-x-circle me-2"></i>
                    <strong>验证失败！</strong><br>
                    签名不匹配，请检查参数。
                    </div>
                    `;
                }
                this.showSuccess('签名验证完成！');
            } else {
                this.showError('签名验证失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('验证签名失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async testApi() {
        try {
            const apiType = document.getElementById('apiTestType').value;
            const paramsText = document.getElementById('apiTestParams').value;
            if (!paramsText) {
                this.showError('请填写请求参数');
                return;
            }
            let params;
            try {
                params = JSON.parse(paramsText);
            } catch (e) {
                this.showError('请求参数不是有效的JSON格式');
                return;
            }
            // 显示请求信息
            const requestInfo = document.getElementById('requestInfo');
            requestInfo.innerHTML = `
            <div class="small">
            <strong>API类型:</strong> ${
                apiType
            }<br>
            <strong>请求时间:</strong> ${
                new Date().toLocaleString()
            }<br>
            <strong>状态:</strong> <span class="text-warning">发送中...</span>
            </div>
            `;
            const response = await fetch('/api/merchant/index.php?module=signature_tool&action=test_api', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    api_type: apiType,
                    params: params,
                    developer_key: this.merchantKeys.api_key
                })
            });
            const data = await response.json();
            // 更新请求信息
            requestInfo.innerHTML = `
            <div class="small">
            <strong>API类型:</strong> ${
                apiType
            }<br>
            <strong>请求时间:</strong> ${
                new Date().toLocaleString()
            }<br>
            <strong>状态:</strong> <span class="text-success">已完成</span><br>
            <strong>请求URL:</strong> ${
                data.data?.request_url || '未知'
            }
            </div>
            `;
            // 显示响应结果
            const responseResult = document.getElementById('responseResult');
            responseResult.innerHTML = `
            <pre style="margin: 0;
            white-space: pre-wrap;
            font-size: 0.85rem;
            ">${
                JSON.stringify(data.data?.response || data,
                null, 2)
            }</pre>
            `;
            this.showSuccess('API测试完成！');
        } catch (error) {
            console.error('API测试失败:',
            error);
            this.showError('网络错误，请重试');
            // 更新请求状态
            const requestInfo = document.getElementById('requestInfo');
            if (requestInfo) {
                requestInfo.innerHTML = `
                <div class="small">
                <strong>状态:</strong> <span class="text-danger">请求失败</span><br>
                <strong>错误:</strong> ${
                    error.message
                }
                </div>
                `;
            }
        }
    }
    formatDebugInfo(debugInfo) {
        if (!debugInfo) return '';
        if (typeof debugInfo === 'string') return debugInfo;
        let formatted = '';
        if (debugInfo.sorted_params) {
            formatted += '排序后的参数:\n' + JSON.stringify(debugInfo.sorted_params,
            null, 2) + '\n\n';
        }
        if (debugInfo.signature_string) {
            formatted += '签名字符串:\n' + debugInfo.signature_string + '\n\n';
        }
        return formatted;
    }
    copySignature(elementId) {
        const element = document.getElementById(elementId);
        if (!element || !element.value) {
            this.showError('没有可复制的签名');
            return;
        }
        navigator.clipboard.writeText(element.value).then(() => {
            this.showSuccess('签名已复制到剪贴板');
        }).catch(err => {
            console.error('复制失败:',
            err);
            this.showError('复制失败，请手动复制');
        });
    }
    clearGenerator() {
        document.getElementById('genTimestamp').value = Math.floor(Date.now() / 1000);
        document.getElementById('genParams').value = '';
        document.getElementById('generatedSignature').value = '';
        document.getElementById('signatureDebugInfo').value = '';
    }
    clearVerifier() {
        document.getElementById('verifyTimestamp').value = Math.floor(Date.now() / 1000);
        document.getElementById('verifyParams').value = '';
        document.getElementById('expectedSignature').value = '';
        document.getElementById('calculatedSignature').value = '';
        document.getElementById('verifyDebugInfo').value = '';
        document.getElementById('verificationResult').innerHTML = '<p class="text-muted mb-0">点击验证按钮查看结果</p>';
    }
    clearTester() {
        document.getElementById('apiTestParams').value = '';
        document.getElementById('requestInfo').innerHTML = '<p class="text-muted mb-0">点击测试按钮查看请求详情</p>';
        document.getElementById('responseResult').innerHTML = '<p class="text-muted mb-0">API响应将在这里显示</p>';
    }
    clearAll() {
        this.clearGenerator();
        this.clearVerifier();
        this.clearTester();
        this.showSuccess('已清空所有工具');
    }
    showHelp() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">签名工具使用帮助</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <div class="accordion" id="helpAccordion">
        <div class="accordion-item">
        <h2 class="accordion-header">
        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#help1">
        签名生成器使用说明
        </button>
        </h2>
        <div id="help1" class="accordion-collapse collapse show" data-bs-parent="#helpAccordion">
        <div class="accordion-body">
        <p>1. 填入当前时间戳（自动生成）</p>
        <p>2. 输入业务参数，格式为JSON对象</p>
        <p>3. 点击"生成签名"按钮</p>
        <p>4. 复制生成的签名用于API调用</p>
        </div>
        </div>
        </div>
        <div class="accordion-item">
        <h2 class="accordion-header">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help2">
        签名验证器使用说明
        </button>
        </h2>
        <div id="help2" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
        <div class="accordion-body">
        <p>1. 填入时间戳和业务参数</p>
        <p>2. 输入要验证的签名</p>
        <p>3. 点击"验证签名"按钮</p>
        <p>4. 查看验证结果和调试信息</p>
        </div>
        </div>
        </div>
        <div class="accordion-item">
        <h2 class="accordion-header">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help3">
        API测试器使用说明
        </button>
        </h2>
        <div id="help3" class="accordion-collapse collapse" data-bs-parent="#helpAccordion">
        <div class="accordion-body">
        <p>1. 选择要测试的API接口类型</p>
        <p>2. 输入请求参数（JSON格式）</p>
        <p>3. 点击"测试API"按钮</p>
        <p>4. 查看请求信息和响应结果</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
        </div>
        </div>
        `;
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }
    showSuccess(message) {
        console.log('Success:',
        message);
        // 这里可以集成toast通知组件
    }
    showError(message) {
        console.error('Error:',
        message);
        // 这里可以集成toast通知组件
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = MerchantSignatureToolManager;
} else if (typeof window !== "undefined") {
    window.MerchantSignatureToolManager = MerchantSignatureToolManager;
}

console.log('📦 MerchantSignatureToolManager 模块加载完成');
