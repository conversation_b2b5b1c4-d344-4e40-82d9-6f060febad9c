/**
 * API客户端模块 - admin.js拆分重构项目
 * 封装所有HTTP请求，包含认证、错误处理、请求拦截等功能
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

class ApiClient {
    constructor() {
        this.baseURL = '/api';
        this.timeout = 30000; // 30秒超时
        this.requestInterceptors = [];
        this.responseInterceptors = [];
        this.pendingRequests = new Map();
        this.retryConfig = {
            maxRetries: 3,
            retryDelay: 1000,
            retryCondition: (error) => {
                return error.code === 'NETWORK_ERROR' || 
                       (error.status >= 500 && error.status < 600);
            }
        };
        
        this.init();
    }
    
    /**
     * 初始化API客户端
     */
    init() {
        this.setupDefaultInterceptors();
        console.log('ApiClient initialized');
    }
    
    /**
     * 设置默认拦截器
     */
    setupDefaultInterceptors() {
        // 请求拦截器 - 添加认证信息
        this.addRequestInterceptor((config) => {
            // 添加认证token
            const token = this.getAuthToken();
            if (token) {
                config.headers = config.headers || {};
                config.headers['Authorization'] = `Bearer ${token}`;
            }
            
            // 添加CSRF token
            const csrfToken = this.getCSRFToken();
            if (csrfToken) {
                config.headers = config.headers || {};
                config.headers['X-CSRF-Token'] = csrfToken;
            }
            
            // 添加请求ID用于追踪
            config.headers = config.headers || {};
            config.headers['X-Request-ID'] = this.generateRequestId();
            
            return config;
        });
        
        // 响应拦截器 - 统一错误处理
        this.addResponseInterceptor(
            (response) => {
                return response;
            },
            (error) => {
                return this.handleResponseError(error);
            }
        );
    }
    
    /**
     * 获取认证token
     */
    getAuthToken() {
        // 从localStorage或sessionStorage获取token
        return localStorage.getItem('admin_token') || 
               sessionStorage.getItem('admin_token');
    }
    
    /**
     * 获取CSRF token
     */
    getCSRFToken() {
        // 从meta标签或cookie获取CSRF token
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            return metaToken.getAttribute('content');
        }
        
        // 从cookie获取
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrf_token') {
                return decodeURIComponent(value);
            }
        }
        
        return null;
    }
    
    /**
     * 生成请求ID
     */
    generateRequestId() {
        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 添加请求拦截器
     * @param {Function} interceptor - 拦截器函数
     */
    addRequestInterceptor(interceptor) {
        this.requestInterceptors.push(interceptor);
    }
    
    /**
     * 添加响应拦截器
     * @param {Function} successInterceptor - 成功拦截器
     * @param {Function} errorInterceptor - 错误拦截器
     */
    addResponseInterceptor(successInterceptor, errorInterceptor) {
        this.responseInterceptors.push({
            success: successInterceptor,
            error: errorInterceptor
        });
    }
    
    /**
     * 处理响应错误
     * @param {Error} error - 错误对象
     */
    async handleResponseError(error) {
        console.error('API Error:', error);
        
        // 根据错误类型进行不同处理
        switch (error.status) {
            case 401:
                // 未授权，可能需要重新登录
                this.handleUnauthorizedError();
                break;
            case 403:
                // 禁止访问
                this.handleForbiddenError();
                break;
            case 404:
                // 资源未找到
                this.handleNotFoundError();
                break;
            case 422:
                // 验证错误
                this.handleValidationError(error);
                break;
            case 429:
                // 请求过多
                this.handleRateLimitError();
                break;
            case 500:
            case 502:
            case 503:
            case 504:
                // 服务器错误
                this.handleServerError(error);
                break;
            default:
                this.handleGenericError(error);
        }
        
        throw error;
    }
    
    /**
     * 处理未授权错误
     */
    handleUnauthorizedError() {
        // 清除本地token
        localStorage.removeItem('admin_token');
        sessionStorage.removeItem('admin_token');
        
        // 显示错误消息
        if (window.UtilsModule) {
            window.UtilsModule.showError('登录已过期，请重新登录');
        }
        
        // 重定向到登录页面
        window.location.href = '/admin/login.html';
    }
    
    /**
     * 处理禁止访问错误
     */
    handleForbiddenError() {
        if (window.UtilsModule) {
            window.UtilsModule.showError('您没有权限执行此操作');
        }
    }
    
    /**
     * 处理资源未找到错误
     */
    handleNotFoundError() {
        if (window.UtilsModule) {
            window.UtilsModule.showError('请求的资源不存在');
        }
    }
    
    /**
     * 处理验证错误
     */
    handleValidationError(error) {
        let message = '数据验证失败';
        
        if (error.data && error.data.errors) {
            const errors = Object.values(error.data.errors).flat();
            message = errors.join(', ');
        } else if (error.data && error.data.message) {
            message = error.data.message;
        }
        
        if (window.UtilsModule) {
            window.UtilsModule.showError(message);
        }
    }
    
    /**
     * 处理请求过多错误
     */
    handleRateLimitError() {
        if (window.UtilsModule) {
            window.UtilsModule.showWarning('请求过于频繁，请稍后再试');
        }
    }
    
    /**
     * 处理服务器错误
     */
    handleServerError(error) {
        if (window.UtilsModule) {
            window.UtilsModule.showError('服务器错误，请稍后重试');
        }
    }
    
    /**
     * 处理通用错误
     */
    handleGenericError(error) {
        let message = '请求失败';
        
        if (error.message) {
            message = error.message;
        } else if (error.data && error.data.message) {
            message = error.data.message;
        }
        
        if (window.UtilsModule) {
            window.UtilsModule.showError(message);
        }
    }
    
    /**
     * 执行HTTP请求
     * @param {Object} config - 请求配置
     * @returns {Promise}
     */
    async request(config) {
        // 应用请求拦截器
        let processedConfig = { ...config };
        for (const interceptor of this.requestInterceptors) {
            processedConfig = await interceptor(processedConfig);
        }
        
        // 生成请求key用于去重
        const requestKey = this.generateRequestKey(processedConfig);
        
        // 检查是否有相同的请求正在进行
        if (this.pendingRequests.has(requestKey)) {
            console.log('Duplicate request detected, returning existing promise');
            return this.pendingRequests.get(requestKey);
        }
        
        // 创建请求Promise
        const requestPromise = this.executeRequest(processedConfig);
        this.pendingRequests.set(requestKey, requestPromise);
        
        try {
            const response = await requestPromise;
            
            // 应用响应拦截器
            let processedResponse = response;
            for (const interceptor of this.responseInterceptors) {
                if (interceptor.success) {
                    processedResponse = await interceptor.success(processedResponse);
                }
            }
            
            return processedResponse;
        } catch (error) {
            // 应用错误拦截器
            for (const interceptor of this.responseInterceptors) {
                if (interceptor.error) {
                    await interceptor.error(error);
                }
            }
            throw error;
        } finally {
            // 清理pending请求
            this.pendingRequests.delete(requestKey);
        }
    }
    
    /**
     * 执行实际的HTTP请求
     * @param {Object} config - 请求配置
     */
    async executeRequest(config) {
        const {
            url,
            method = 'GET',
            data,
            params,
            headers = {},
            timeout = this.timeout,
            responseType = 'json'
        } = config;
        
        // 构建完整URL
        const fullUrl = this.buildURL(url, params);
        
        // 构建fetch选项
        const fetchOptions = {
            method: method.toUpperCase(),
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        };
        
        // 添加请求体
        if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
            if (data instanceof FormData) {
                fetchOptions.body = data;
                // FormData会自动设置Content-Type
                delete fetchOptions.headers['Content-Type'];
            } else {
                fetchOptions.body = JSON.stringify(data);
            }
        }
        
        // 创建AbortController用于超时控制
        const controller = new AbortController();
        fetchOptions.signal = controller.signal;
        
        // 设置超时
        const timeoutId = setTimeout(() => {
            controller.abort();
        }, timeout);
        
        try {
            console.log(`API Request: ${method.toUpperCase()} ${fullUrl}`, {
                headers: fetchOptions.headers,
                body: fetchOptions.body
            });
            
            const response = await fetch(fullUrl, fetchOptions);
            clearTimeout(timeoutId);
            
            // 检查响应状态
            if (!response.ok) {
                const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
                error.status = response.status;
                error.statusText = response.statusText;
                
                // 尝试解析错误响应体
                try {
                    error.data = await response.json();
                } catch {
                    error.data = { message: response.statusText };
                }
                
                throw error;
            }
            
            // 解析响应体
            let responseData;
            if (responseType === 'json') {
                responseData = await response.json();
            } else if (responseType === 'text') {
                responseData = await response.text();
            } else if (responseType === 'blob') {
                responseData = await response.blob();
            } else {
                responseData = response;
            }
            
            console.log(`API Response: ${method.toUpperCase()} ${fullUrl}`, responseData);
            
            return {
                data: responseData,
                status: response.status,
                statusText: response.statusText,
                headers: response.headers
            };
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                const timeoutError = new Error('Request timeout');
                timeoutError.code = 'TIMEOUT';
                throw timeoutError;
            }
            
            if (!navigator.onLine) {
                const networkError = new Error('Network error');
                networkError.code = 'NETWORK_ERROR';
                throw networkError;
            }
            
            throw error;
        }
    }
    
    /**
     * 生成请求key用于去重
     * @param {Object} config - 请求配置
     */
    generateRequestKey(config) {
        const { url, method = 'GET', data, params } = config;
        const key = `${method.toUpperCase()}_${url}_${JSON.stringify(params || {})}_${JSON.stringify(data || {})}`;
        return btoa(key).replace(/[^a-zA-Z0-9]/g, '');
    }
    
    /**
     * 构建URL
     * @param {string} url - 基础URL
     * @param {Object} params - 查询参数
     */
    buildURL(url, params) {
        let fullUrl = url.startsWith('http') ? url : this.baseURL + url;
        
        if (params && Object.keys(params).length > 0) {
            const searchParams = new URLSearchParams();
            Object.keys(params).forEach(key => {
                if (params[key] !== null && params[key] !== undefined) {
                    searchParams.append(key, params[key]);
                }
            });
            
            const queryString = searchParams.toString();
            if (queryString) {
                fullUrl += (fullUrl.includes('?') ? '&' : '?') + queryString;
            }
        }
        
        return fullUrl;
    }
    
    // ==================== HTTP方法快捷方式 ====================
    
    /**
     * GET请求
     * @param {string} url - 请求URL
     * @param {Object} config - 请求配置
     */
    async get(url, config = {}) {
        return this.request({
            url,
            method: 'GET',
            ...config
        });
    }
    
    /**
     * POST请求
     * @param {string} url - 请求URL
     * @param {any} data - 请求数据
     * @param {Object} config - 请求配置
     */
    async post(url, data, config = {}) {
        return this.request({
            url,
            method: 'POST',
            data,
            ...config
        });
    }
    
    /**
     * PUT请求
     * @param {string} url - 请求URL
     * @param {any} data - 请求数据
     * @param {Object} config - 请求配置
     */
    async put(url, data, config = {}) {
        return this.request({
            url,
            method: 'PUT',
            data,
            ...config
        });
    }
    
    /**
     * PATCH请求
     * @param {string} url - 请求URL
     * @param {any} data - 请求数据
     * @param {Object} config - 请求配置
     */
    async patch(url, data, config = {}) {
        return this.request({
            url,
            method: 'PATCH',
            data,
            ...config
        });
    }
    
    /**
     * DELETE请求
     * @param {string} url - 请求URL
     * @param {Object} config - 请求配置
     */
    async delete(url, config = {}) {
        return this.request({
            url,
            method: 'DELETE',
            ...config
        });
    }
    
    // ==================== 文件上传 ====================
    
    /**
     * 上传文件
     * @param {string} url - 上传URL
     * @param {File|FileList} files - 文件
     * @param {Object} config - 配置选项
     */
    async upload(url, files, config = {}) {
        const formData = new FormData();
        
        if (files instanceof FileList) {
            for (let i = 0; i < files.length; i++) {
                formData.append('files[]', files[i]);
            }
        } else if (files instanceof File) {
            formData.append('file', files);
        } else {
            throw new Error('Invalid file parameter');
        }
        
        // 添加额外的表单数据
        if (config.data) {
            Object.keys(config.data).forEach(key => {
                formData.append(key, config.data[key]);
            });
        }
        
        return this.request({
            url,
            method: 'POST',
            data: formData,
            ...config,
            headers: {
                // 不设置Content-Type，让浏览器自动设置
                ...config.headers
            }
        });
    }
    
    /**
     * 下载文件
     * @param {string} url - 下载URL
     * @param {string} filename - 文件名
     * @param {Object} config - 配置选项
     */
    async download(url, filename, config = {}) {
        const response = await this.request({
            url,
            method: 'GET',
            responseType: 'blob',
            ...config
        });
        
        // 创建下载链接
        const blob = response.data;
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename || 'download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
        
        return response;
    }
    
    // ==================== 重试机制 ====================
    
    /**
     * 带重试的请求
     * @param {Object} config - 请求配置
     * @param {number} retryCount - 当前重试次数
     */
    async requestWithRetry(config, retryCount = 0) {
        try {
            return await this.request(config);
        } catch (error) {
            const shouldRetry = retryCount < this.retryConfig.maxRetries && 
                              this.retryConfig.retryCondition(error);
            
            if (shouldRetry) {
                console.log(`Retrying request (${retryCount + 1}/${this.retryConfig.maxRetries}):`, config.url);
                
                // 等待一段时间后重试
                await new Promise(resolve => 
                    setTimeout(resolve, this.retryConfig.retryDelay * Math.pow(2, retryCount))
                );
                
                return this.requestWithRetry(config, retryCount + 1);
            }
            
            throw error;
        }
    }
    
    // ==================== 批量请求 ====================
    
    /**
     * 批量请求
     * @param {Array} requests - 请求配置数组
     * @param {Object} options - 选项
     */
    async batchRequest(requests, options = {}) {
        const { concurrent = 5, failFast = false } = options;
        
        if (concurrent >= requests.length) {
            // 并发数大于等于请求数，直接并行执行
            if (failFast) {
                return Promise.all(requests.map(config => this.request(config)));
            } else {
                return Promise.allSettled(requests.map(config => this.request(config)));
            }
        }
        
        // 分批执行
        const results = [];
        for (let i = 0; i < requests.length; i += concurrent) {
            const batch = requests.slice(i, i + concurrent);
            const batchPromises = batch.map(config => this.request(config));
            
            if (failFast) {
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            } else {
                const batchResults = await Promise.allSettled(batchPromises);
                results.push(...batchResults);
            }
        }
        
        return results;
    }
    
    // ==================== 取消请求 ====================
    
    /**
     * 取消所有pending请求
     */
    cancelAllRequests() {
        this.pendingRequests.clear();
        console.log('All pending requests cancelled');
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 设置基础URL
     * @param {string} baseURL - 基础URL
     */
    setBaseURL(baseURL) {
        this.baseURL = baseURL;
    }
    
    /**
     * 设置超时时间
     * @param {number} timeout - 超时时间(毫秒)
     */
    setTimeout(timeout) {
        this.timeout = timeout;
    }
    
    /**
     * 获取pending请求数量
     */
    getPendingRequestsCount() {
        return this.pendingRequests.size;
    }
    
    /**
     * 销毁API客户端
     */
    destroy() {
        this.cancelAllRequests();
        this.requestInterceptors.length = 0;
        this.responseInterceptors.length = 0;
        console.log('ApiClient destroyed');
    }
}

// 创建全局API客户端实例
window.AdminApiClient = window.AdminApiClient || new ApiClient();

// 导出API客户端模块
window.ApiClientModule = {
    ApiClient,
    instance: window.AdminApiClient,
    
    // 便捷方法
    request: (config) => window.AdminApiClient.request(config),
    get: (url, config) => window.AdminApiClient.get(url, config),
    post: (url, data, config) => window.AdminApiClient.post(url, data, config),
    put: (url, data, config) => window.AdminApiClient.put(url, data, config),
    patch: (url, data, config) => window.AdminApiClient.patch(url, data, config),
    delete: (url, config) => window.AdminApiClient.delete(url, config),
    upload: (url, files, config) => window.AdminApiClient.upload(url, files, config),
    download: (url, filename, config) => window.AdminApiClient.download(url, filename, config),
    requestWithRetry: (config) => window.AdminApiClient.requestWithRetry(config),
    batchRequest: (requests, options) => window.AdminApiClient.batchRequest(requests, options),
    cancelAllRequests: () => window.AdminApiClient.cancelAllRequests(),
    setBaseURL: (baseURL) => window.AdminApiClient.setBaseURL(baseURL),
    setTimeout: (timeout) => window.AdminApiClient.setTimeout(timeout),
    getPendingRequestsCount: () => window.AdminApiClient.getPendingRequestsCount()
};

console.log('ApiClient module loaded successfully'); 