<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PayPal管理系统 - 修复版本测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 30px;
        }
        
        h1 {
            color: #1f2937;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        .status-panel {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 600;
            color: #374151;
        }
        
        .status-value {
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .status-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .console-output {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background: #2563eb;
        }
        
        .btn-success {
            background: #10b981;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-warning {
            background: #f59e0b;
        }
        
        .btn-warning:hover {
            background: #d97706;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 PayPal管理系统修复版本测试</h1>
        
        <div class="status-panel">
            <h3>📊 系统状态监控</h3>
            <div class="status-item">
                <span class="status-label">核心模块加载</span>
                <span id="core-status" class="status-value status-warning">检查中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">应用初始化器</span>
                <span id="app-status" class="status-value status-warning">检查中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">认证系统</span>
                <span id="auth-status" class="status-value status-warning">检查中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">模块重复声明</span>
                <span id="duplicate-status" class="status-value status-warning">检查中...</span>
            </div>
        </div>
        
        <div style="text-align: center;">
            <button class="btn" onclick="runFullTest()">🚀 运行完整测试</button>
            <button class="btn btn-success" onclick="checkModuleStatus()">📋 检查模块状态</button>
            <button class="btn btn-warning" onclick="clearConsole()">🧹 清空控制台</button>
        </div>
        
        <div class="console-output" id="console-output">
            <div>🔍 等待测试开始...</div>
        </div>
    </div>

    <!-- 静态加载核心模块 -->
    <script src="js/modules/core/utils.js"></script>
    <script src="js/modules/core/module-loader.js"></script>
    <script src="js/modules/core/auth.js"></script>
    <script src="js/modules/core/ui-manager.js"></script>
    <!-- 使用修复版本的app-initializer -->
    <script src="js/modules/core/app-initializer-fixed.js"></script>

    <script>
        // 重写console.log来显示在页面上
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.style.marginBottom = '5px';
            
            let prefix = '';
            let color = '#f9fafb';
            
            switch(type) {
                case 'error':
                    prefix = '❌ ';
                    color = '#fca5a5';
                    break;
                case 'warn':
                    prefix = '⚠️ ';
                    color = '#fcd34d';
                    break;
                case 'success':
                    prefix = '✅ ';
                    color = '#86efac';
                    break;
                default:
                    prefix = '📋 ';
            }
            
            div.innerHTML = `<span style="color: #9ca3af;">[${timestamp}]</span> <span style="color: ${color};">${prefix}${message}</span>`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        // 监听全局错误
        window.addEventListener('error', (event) => {
            addToConsole(`全局错误: ${event.error.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            addToConsole(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
        
        // 测试函数
        function runFullTest() {
            addToConsole('🚀 开始运行完整测试...', 'success');
            
            // 检查核心模块
            checkCoreModules();
            
            // 检查应用初始化器
            checkAppInitializer();
            
            // 检查认证系统
            checkAuthSystem();
            
            // 检查重复声明
            checkDuplicateDeclarations();
            
            addToConsole('✅ 完整测试完成！', 'success');
        }
        
        function checkCoreModules() {
            addToConsole('🔧 检查核心模块...', 'log');
            
            const modules = [
                { name: 'CONFIG', obj: window.CONFIG },
                { name: 'AdminUtils', obj: window.AdminUtils },
                { name: 'AuthManager', obj: window.AuthManager },
                { name: 'UIManager', obj: window.UIManager },
                { name: 'ModuleLoader', obj: window.ModuleLoader }
            ];
            
            let allGood = true;
            
            modules.forEach(module => {
                if (module.obj) {
                    addToConsole(`✅ ${module.name} 已加载`, 'success');
                } else {
                    addToConsole(`❌ ${module.name} 未找到`, 'error');
                    allGood = false;
                }
            });
            
            const statusEl = document.getElementById('core-status');
            if (allGood) {
                statusEl.textContent = '正常';
                statusEl.className = 'status-value status-success';
            } else {
                statusEl.textContent = '异常';
                statusEl.className = 'status-value status-error';
            }
        }
        
        function checkAppInitializer() {
            addToConsole('🚀 检查应用初始化器...', 'log');
            
            const statusEl = document.getElementById('app-status');
            
            if (window.appInitializerFixed) {
                addToConsole('✅ 修复版应用初始化器已加载', 'success');
                
                const status = window.appInitializerFixed.getAppStatus();
                addToConsole(`📊 应用状态: ${JSON.stringify(status, null, 2)}`, 'log');
                
                statusEl.textContent = '正常';
                statusEl.className = 'status-value status-success';
            } else {
                addToConsole('❌ 修复版应用初始化器未找到', 'error');
                statusEl.textContent = '异常';
                statusEl.className = 'status-value status-error';
            }
        }
        
        function checkAuthSystem() {
            addToConsole('🔐 检查认证系统...', 'log');
            
            const statusEl = document.getElementById('auth-status');
            
            if (window.authManager) {
                addToConsole('✅ 认证管理器实例已创建', 'success');
                statusEl.textContent = '正常';
                statusEl.className = 'status-value status-success';
            } else if (window.AuthManager) {
                addToConsole('⚠️ 认证管理器类存在但实例未创建', 'warn');
                statusEl.textContent = '部分正常';
                statusEl.className = 'status-value status-warning';
            } else {
                addToConsole('❌ 认证系统未找到', 'error');
                statusEl.textContent = '异常';
                statusEl.className = 'status-value status-error';
            }
        }
        
        function checkDuplicateDeclarations() {
            addToConsole('🔍 检查重复声明...', 'log');
            
            const statusEl = document.getElementById('duplicate-status');
            
            try {
                // 尝试检测是否有重复声明的迹象
                const performanceMonitorCount = document.querySelectorAll('script').length;
                addToConsole(`📊 已加载脚本数量: ${performanceMonitorCount}`, 'log');
                
                // 检查是否有重复的全局变量
                let duplicates = [];
                
                if (window.PerformanceMonitor && typeof window.PerformanceMonitor === 'object') {
                    addToConsole('✅ PerformanceMonitor 正常', 'success');
                } else {
                    addToConsole('⚠️ PerformanceMonitor 状态异常', 'warn');
                }
                
                if (duplicates.length === 0) {
                    addToConsole('✅ 未发现重复声明问题', 'success');
                    statusEl.textContent = '正常';
                    statusEl.className = 'status-value status-success';
                } else {
                    addToConsole(`⚠️ 发现重复声明: ${duplicates.join(', ')}`, 'warn');
                    statusEl.textContent = '有警告';
                    statusEl.className = 'status-value status-warning';
                }
                
            } catch (error) {
                addToConsole(`❌ 重复声明检查失败: ${error.message}`, 'error');
                statusEl.textContent = '检查失败';
                statusEl.className = 'status-value status-error';
            }
        }
        
        function checkModuleStatus() {
            addToConsole('📋 检查模块状态...', 'log');
            
            // 检查所有全局对象
            const globalObjects = Object.keys(window).filter(key => 
                key.includes('Manager') || 
                key.includes('Utils') || 
                key.includes('CONFIG') ||
                key.includes('Module')
            );
            
            addToConsole(`🌐 相关全局对象: ${globalObjects.join(', ')}`, 'log');
            
            // 检查模块加载器状态
            if (window.AdminModuleLoader) {
                try {
                    const loadedModules = window.AdminModuleLoader.getLoadedModules();
                    addToConsole(`📦 已加载模块: ${loadedModules.join(', ')}`, 'log');
                } catch (error) {
                    addToConsole(`⚠️ 无法获取已加载模块列表: ${error.message}`, 'warn');
                }
            }
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '<div>🧹 控制台已清空</div>';
        }
        
        // 页面加载完成后自动运行检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                addToConsole('🔍 页面加载完成，开始自动检查...', 'success');
                checkModuleStatus();
            }, 1000);
        });
        
        addToConsole('🚀 修复版本测试页面已加载', 'success');
    </script>
</body>
</html> 