/**
 * 轻量级认证管理器
 * 处理用户认证相关功能
 * 
 * <AUTHOR>
 * @version 2.0.0 - 架构优化版本
 */

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.tokenKey = 'auth_token';
        this.userKey = 'user_info';
        
        // 初始化时检查本地存储
        this._loadFromStorage();
        
        console.log('🔐 认证管理器初始化完成');
    }

    /**
     * 用户登录
     * @param {string} username 用户名
     * @param {string} password 密码
     * @param {string} apiUrl API地址
     * @returns {Promise<Object>} 登录结果
     */
    async login(username, password, apiUrl) {
        try {
            console.log('🔑 开始用户登录:', username);
            
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.error_code === 0) {
                // 登录成功
                this.currentUser = result.data.user;
                this.isAuthenticated = true;
                
                // 保存到本地存储
                localStorage.setItem(this.tokenKey, result.data.token);
                localStorage.setItem(this.userKey, JSON.stringify(this.currentUser));
                
                console.log('✅ 登录成功:', this.currentUser.username);
                
                return {
                    success: true,
                    user: this.currentUser,
                    token: result.data.token
                };
                
            } else {
                throw new Error(result.message || '登录失败');
            }
            
        } catch (error) {
            console.error('❌ 登录失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 验证Token有效性
     * @param {string} apiUrl API地址
     * @returns {Promise<boolean>} 是否有效
     */
    async verifyToken(apiUrl) {
        const token = this.getToken();
        if (!token) {
            console.log('❌ 未找到Token');
            return false;
        }

        try {
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.error_code === 0) {
                    // Token有效，更新用户信息
                    this.currentUser = result.data;
                    this.isAuthenticated = true;
                    localStorage.setItem(this.userKey, JSON.stringify(this.currentUser));
                    
                    console.log('✅ Token验证成功');
                    return true;
                }
            }
            
            // Token无效
            this.logout();
            return false;
            
        } catch (error) {
            console.error('❌ Token验证失败:', error);
            this.logout();
            return false;
        }
    }

    /**
     * 用户退出
     * @param {string} apiUrl 退出API地址（可选）
     */
    async logout(apiUrl = null) {
        console.log('👋 用户退出登录');
        
        // 如果提供了API地址，调用服务器退出接口
        if (apiUrl) {
            try {
                const token = this.getToken();
                if (token) {
                    await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                }
            } catch (error) {
                console.warn('⚠️ 服务器退出请求失败:', error);
            }
        }
        
        // 清理本地状态
        this.currentUser = null;
        this.isAuthenticated = false;
        
        // 清理本地存储
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.userKey);
        
        console.log('✅ 退出登录完成');
    }

    /**
     * 获取当前用户
     * @returns {Object|null} 用户信息
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 获取认证状态
     * @returns {boolean} 是否已认证
     */
    isLoggedIn() {
        return this.isAuthenticated && this.currentUser !== null;
    }

    /**
     * 获取Token
     * @returns {string|null} Token
     */
    getToken() {
        return localStorage.getItem(this.tokenKey);
    }

    /**
     * 获取用户权限
     * @returns {Array} 权限列表
     */
    getUserPermissions() {
        if (!this.currentUser) return [];
        return this.currentUser.permissions || [];
    }

    /**
     * 检查用户权限
     * @param {string} permission 权限名称
     * @returns {boolean} 是否有权限
     */
    hasPermission(permission) {
        const permissions = this.getUserPermissions();
        return permissions.includes(permission) || permissions.includes('*');
    }

    /**
     * 获取用户角色
     * @returns {string} 用户角色
     */
    getUserRole() {
        if (!this.currentUser) return '';
        return this.currentUser.user_type || '';
    }

    /**
     * 检查用户角色
     * @param {string} role 角色名称
     * @returns {boolean} 是否匹配
     */
    hasRole(role) {
        return this.getUserRole() === role;
    }

    /**
     * 获取用户ID
     * @returns {number|null} 用户ID
     */
    getUserId() {
        if (!this.currentUser) return null;
        return this.currentUser.id || null;
    }

    /**
     * 获取用户名
     * @returns {string} 用户名
     */
    getUsername() {
        if (!this.currentUser) return '';
        return this.currentUser.username || '';
    }

    /**
     * 更新用户信息
     * @param {Object} userInfo 用户信息
     */
    updateUserInfo(userInfo) {
        if (this.isAuthenticated) {
            this.currentUser = Object.assign({}, this.currentUser, userInfo);
            localStorage.setItem(this.userKey, JSON.stringify(this.currentUser));
            console.log('✅ 用户信息已更新');
        }
    }

    /**
     * 检查Token是否即将过期
     * @param {number} threshold 阈值（分钟）
     * @returns {boolean} 是否即将过期
     */
    isTokenExpiringSoon(threshold = 30) {
        if (!this.currentUser || !this.currentUser.token_expires_at) {
            return false;
        }
        
        const expiresAt = new Date(this.currentUser.token_expires_at);
        const now = new Date();
        const diffMinutes = (expiresAt - now) / (1000 * 60);
        
        return diffMinutes <= threshold;
    }

    /**
     * 刷新Token
     * @param {string} apiUrl 刷新API地址
     * @returns {Promise<boolean>} 是否成功
     */
    async refreshToken(apiUrl) {
        const token = this.getToken();
        if (!token) return false;
        
        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const result = await response.json();
                if (result.error_code === 0) {
                    // 更新Token
                    localStorage.setItem(this.tokenKey, result.data.token);
                    this.updateUserInfo(result.data.user);
                    
                    console.log('✅ Token刷新成功');
                    return true;
                }
            }
            
            return false;
            
        } catch (error) {
            console.error('❌ Token刷新失败:', error);
            return false;
        }
    }

    /**
     * 从本地存储加载用户信息
     * @private
     */
    _loadFromStorage() {
        try {
            const token = localStorage.getItem(this.tokenKey);
            const userInfo = localStorage.getItem(this.userKey);
            
            if (token && userInfo) {
                this.currentUser = JSON.parse(userInfo);
                this.isAuthenticated = true;
                console.log('📂 从本地存储加载用户信息:', this.currentUser.username);
            }
            
        } catch (error) {
            console.warn('⚠️ 本地用户信息加载失败:', error);
            this.logout();
        }
    }

    /**
     * 设置自动Token刷新
     * @param {string} refreshUrl 刷新API地址
     * @param {number} interval 检查间隔（分钟）
     */
    setupAutoRefresh(refreshUrl, interval = 10) {
        setInterval(async () => {
            if (this.isLoggedIn() && this.isTokenExpiringSoon()) {
                console.log('🔄 Token即将过期，尝试自动刷新...');
                const success = await this.refreshToken(refreshUrl);
                if (!success) {
                    console.warn('⚠️ Token自动刷新失败，需要重新登录');
                    this.logout();
                    // 可以在这里触发重新登录提示
                }
            }
        }, interval * 60 * 1000);
    }

    /**
     * 获取认证状态摘要
     * @returns {Object} 状态摘要
     */
    getAuthSummary() {
        return {
            isAuthenticated: this.isAuthenticated,
            username: this.getUsername(),
            role: this.getUserRole(),
            userId: this.getUserId(),
            hasToken: !!this.getToken(),
            tokenExpiringSoon: this.isTokenExpiringSoon()
        };
    }
}

// 导出到全局作用域
window.AuthManager = AuthManager;
console.log('✅ 轻量级认证管理器已加载'); 