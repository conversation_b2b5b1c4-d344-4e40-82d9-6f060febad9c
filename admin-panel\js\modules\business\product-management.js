/**
 * 产品管理模块
 * 负责产品CRUD、分类管理、价格配置、库存管理等核心产品业务功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-15
 */

class ProductManager {
    constructor() {

        this.authManager = window.authManager;
        this.utils = window.utils;
        this.currentProducts = [];
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalProducts = 0;
        this.searchQuery = '';
        this.filterCategory = 'all';
        this.filterStatus = 'all';
        
        console.log('ProductManager initialized');
    }

    /**
     * 初始化函数，由 app-initializer 调用
     * @param {object} dependencies - 依赖项（可选）
     */
    async init(dependencies) {
        try {
            // 设置依赖项 - 支持两种初始化方式
            if (dependencies) {
                // 方式1：通过UI管理器传递依赖项
                this.apiClient = dependencies.apiClient;
                this.utils = dependencies.utils;
                this.authManager = dependencies.authManager;
                this.tenantInfo = dependencies.tenantInfo || window.TENANT_INFO;
            } else {
                // 方式2：从全局变量获取依赖项（模块加载器方式）
                this.apiClient = window.AdminApiClient || window.apiClient || 
                               (window.ApiClientModule && window.ApiClientModule.instance);
                this.utils = window.AdminUtils || window.utils || 
                           (window.UtilsModule && window.UtilsModule.instance) ||
                           (window.uiManager && window.uiManager.utils);
                this.authManager = window.authManager || window.AdminAuthManager ||
                                 (window.AuthModule && window.AuthModule.instance);
                this.tenantInfo = window.TENANT_INFO;
            }
            
            // 注册页面处理器
            if (window.uiManager) {
                window.uiManager.registerPageHandler('products', (container) => {
                    this.loadProductManagementPage(container);
                });
            }
            
            console.log('✅ 产品管理模块初始化完成', {
                tenantType: this.tenantInfo?.tenant_type,
                userType: this.authManager?.getUserType(),
                hasApiClient: !!this.apiClient,
                hasUtils: !!this.utils
            });
        } catch (error) {
            console.error('❌ 产品管理模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 方案5配置驱动 - 渲染方法
     * @param {HTMLElement} container 容器元素
     * @param {Object} params 参数对象
     */
    async render(container, params = {}) {
        try {
            console.log('🎯 ProductManager.render 被调用:', params);
            
            // 检查依赖项是否已初始化
            if (!this.utils) {
                throw new Error('模块依赖项未正确初始化，请检查模块加载顺序');
            }
            
            // 设置角色和API路径
            this.currentRole = params.role || 'platform_admin';
            this.setApiPaths(this.currentRole);
            
            // 调用现有的页面加载方法
            this.loadProductManagementPage(container);
            
            console.log('✅ 产品管理页面渲染完成');
        } catch (error) {
            console.error('❌ 产品管理页面渲染失败:', error);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h4>加载失败</h4>
                    <p>产品管理页面加载失败: ${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }

    /**
     * 根据角色设置API路径
     * @param {string} role 用户角色
     */
    setApiPaths(role) {
        // 所有API都通过admin.php主路由，使用不同的action参数
        switch (role) {
            case 'merchant':
                this.apiBasePath = `${CONFIG.API_BASE_URL}/admin.php?action=products`; // 通过admin.php主路由
                this.apiStatsPath = `${CONFIG.API_BASE_URL}/admin.php?action=product_stats`; // 统计数据
                this.apiCategoriesPath = `${CONFIG.API_BASE_URL}/admin.php?action=product_categories`; // 分类数据
                break;
            case 'platform_admin':
            case 'provider':
            default:
                this.apiBasePath = `${CONFIG.API_BASE_URL}/admin.php?action=products`;
                this.apiStatsPath = `${CONFIG.API_BASE_URL}/admin.php?action=product_stats`;
                this.apiCategoriesPath = `${CONFIG.API_BASE_URL}/admin.php?action=product_categories`;
                break;
        }
        console.log('🔧 API路径已设置:', { role, basePath: this.apiBasePath });
    }

    /**
     * 加载产品管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadProductManagementPage(container) {
        container.innerHTML = this.generateProductManagementHTML();
        this.initializeProductManagementEvents();
        this.loadProductList();
    }

    /**
     * 生成产品管理页面HTML
     * @returns {string} HTML字符串
     */
    generateProductManagementHTML() {
        return `
            <div class="product-management">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-box-seam me-2"></i>产品管理</h2>
                            <p class="text-muted mb-0">管理产品信息、分类和库存</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="addProductBtn">
                                <i class="bi bi-plus-circle me-2"></i>添加产品
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="manageCategoriesBtn">
                                <i class="bi bi-tags me-2"></i>分类管理
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-box"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalProductsCount">-</div>
                                <div class="stat-label">总产品数</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="activeProductsCount">-</div>
                                <div class="stat-label">上架产品</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="lowStockCount">-</div>
                                <div class="stat-label">库存不足</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-tags"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="categoriesCount">-</div>
                                <div class="stat-label">产品分类</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">搜索产品</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="productSearchInput" 
                                           placeholder="产品名称、SKU、描述...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">产品分类</label>
                                <select class="form-select" id="categoryFilter">
                                    <option value="all">全部分类</option>
                                    <!-- 分类选项将动态加载 -->
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">产品状态</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="all">全部状态</option>
                                    <option value="active">上架</option>
                                    <option value="inactive">下架</option>
                                    <option value="draft">草稿</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">价格范围</label>
                                <select class="form-select" id="priceFilter">
                                    <option value="all">全部价格</option>
                                    <option value="0-100">¥0-100</option>
                                    <option value="100-500">¥100-500</option>
                                    <option value="500-1000">¥500-1000</option>
                                    <option value="1000+">¥1000+</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-outline-primary" id="searchBtn">
                                        <i class="bi bi-funnel me-1"></i>筛选
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 产品列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">产品列表</h5>
                        <div class="d-flex gap-2">
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary active" id="gridViewBtn">
                                    <i class="bi bi-grid"></i>
                                </button>
                                <button class="btn btn-outline-secondary" id="listViewBtn">
                                    <i class="bi bi-list"></i>
                                </button>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" id="refreshProductsBtn">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body" id="productListContainer">
                        <!-- 产品列表将动态生成 -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载产品数据...</div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                显示第 <span id="currentPageInfo">1-20</span> 条，共 <span id="totalProductsInfo">0</span> 条记录
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="productPagination">
                                    <!-- 分页将动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 添加/编辑产品模态框 -->
            <div class="modal fade" id="productModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="productModalTitle">添加产品</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="productForm">
                                <input type="hidden" id="productId">
                                <div class="row g-3">
                                    <!-- 基本信息 -->
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="bi bi-info-circle me-2"></i>基本信息
                                        </h6>
                                    </div>
                                    <div class="col-md-8">
                                        <label class="form-label">产品名称 *</label>
                                        <input type="text" class="form-control" id="productName" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">产品SKU *</label>
                                        <input type="text" class="form-control" id="productSku" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">产品分类 *</label>
                                        <select class="form-select" id="productCategory" required>
                                            <option value="">请选择分类</option>
                                            <!-- 分类选项将动态加载 -->
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">产品状态</label>
                                        <select class="form-select" id="productStatus">
                                            <option value="draft">草稿</option>
                                            <option value="active">上架</option>
                                            <option value="inactive">下架</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">产品描述</label>
                                        <textarea class="form-control" id="productDescription" rows="3"></textarea>
                                    </div>
                                    
                                    <!-- 价格和库存 -->
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3 mt-4">
                                            <i class="bi bi-currency-dollar me-2"></i>价格和库存
                                        </h6>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">售价 *</label>
                                        <div class="input-group">
                                            <span class="input-group-text">¥</span>
                                            <input type="number" class="form-control" id="productPrice" 
                                                   min="0" step="0.01" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">成本价</label>
                                        <div class="input-group">
                                            <span class="input-group-text">¥</span>
                                            <input type="number" class="form-control" id="productCost" 
                                                   min="0" step="0.01">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">库存数量</label>
                                        <input type="number" class="form-control" id="productStock" 
                                               min="0" value="0">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">库存预警</label>
                                        <input type="number" class="form-control" id="stockAlert" 
                                               min="0" value="10">
                                    </div>
                                    
                                    <!-- 产品图片 -->
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3 mt-4">
                                            <i class="bi bi-image me-2"></i>产品图片
                                        </h6>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">主图片</label>
                                        <input type="file" class="form-control" id="productImage" accept="image/*">
                                        <div class="form-text">支持 JPG、PNG 格式，建议尺寸 800x600px</div>
                                    </div>
                                    
                                    <!-- 其他设置 -->
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3 mt-4">
                                            <i class="bi bi-gear me-2"></i>其他设置
                                        </h6>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">产品重量 (kg)</label>
                                        <input type="number" class="form-control" id="productWeight" 
                                               min="0" step="0.01">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">产品尺寸</label>
                                        <input type="text" class="form-control" id="productDimensions" 
                                               placeholder="长x宽x高 (cm)">
                                    </div>
                                    <div class="col-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="trackInventory" checked>
                                            <label class="form-check-label" for="trackInventory">
                                                启用库存跟踪
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="allowBackorder">
                                            <label class="form-check-label" for="allowBackorder">
                                                允许缺货销售
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveProductBtn">保存</button>
                        </div>
                    </div>
                </div>
            </div>

            ${this.generateProductManagementStyles()}
        `;
    }

    /**
     * 生成产品管理样式
     * @returns {string} CSS样式
     */
    generateProductManagementStyles() {
        return `
            <style>
                .product-management .stat-card {
                    background: white;
                    border-radius: 12px;
                    padding: 20px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }

                .product-management .stat-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                }

                .product-management .stat-icon {
                    width: 50px;
                    height: 50px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .product-management .stat-content {
                    flex: 1;
                }

                .product-management .stat-number {
                    font-size: 1.8rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin-bottom: 4px;
                }

                .product-management .stat-label {
                    color: #6b7280;
                    font-size: 14px;
                }

                .product-management .product-card {
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                    padding: 15px;
                    margin-bottom: 15px;
                    transition: all 0.3s ease;
                    background: white;
                }

                .product-management .product-card:hover {
                    border-color: #3b82f6;
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
                }

                .product-management .product-image {
                    width: 80px;
                    height: 80px;
                    border-radius: 8px;
                    object-fit: cover;
                    background: #f3f4f6;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #9ca3af;
                    font-size: 2rem;
                }

                .product-management .product-info h6 {
                    margin: 0;
                    font-weight: 600;
                    color: #1f2937;
                }

                .product-management .product-info .sku {
                    color: #6b7280;
                    font-size: 12px;
                    font-family: 'Courier New', monospace;
                }

                .product-management .price {
                    font-size: 1.2rem;
                    font-weight: 700;
                    color: #059669;
                }

                .product-management .stock {
                    font-size: 14px;
                    color: #6b7280;
                }

                .product-management .stock.low {
                    color: #dc2626;
                    font-weight: 600;
                }

                .product-management .status-badge {
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .product-management .status-active {
                    background: #dcfce7;
                    color: #166534;
                }

                .product-management .status-inactive {
                    background: #fef3c7;
                    color: #92400e;
                }

                .product-management .status-draft {
                    background: #f1f5f9;
                    color: #475569;
                }

                .product-management .category-badge {
                    background: #e0e7ff;
                    color: #3730a3;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 11px;
                    font-weight: 500;
                }

                .product-management .grid-view {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                    gap: 20px;
                }

                .product-management .list-view .product-card {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }

                .product-management .form-label {
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 6px;
                }

                .product-management .form-control:focus,
                .product-management .form-select:focus {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
            </style>
        `;
    }

    /**
     * 初始化产品管理事件
     */
    initializeProductManagementEvents() {
        // 添加产品按钮
        document.getElementById('addProductBtn')?.addEventListener('click', () => {
            this.showProductModal();
        });

        // 分类管理按钮
        document.getElementById('manageCategoriesBtn')?.addEventListener('click', () => {
            this.showCategoryManagement();
        });

        // 搜索按钮
        document.getElementById('searchBtn')?.addEventListener('click', () => {
            this.handleSearch();
        });

        // 刷新按钮
        document.getElementById('refreshProductsBtn')?.addEventListener('click', () => {
            this.loadProductList();
        });

        // 保存产品按钮
        document.getElementById('saveProductBtn')?.addEventListener('click', () => {
            this.handleSaveProduct();
        });

        // 视图切换
        document.getElementById('gridViewBtn')?.addEventListener('click', () => {
            this.switchView('grid');
        });

        document.getElementById('listViewBtn')?.addEventListener('click', () => {
            this.switchView('list');
        });

        // 搜索输入框回车事件
        document.getElementById('productSearchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch();
            }
        });

        // 筛选器变化事件
        ['categoryFilter', 'statusFilter', 'priceFilter'].forEach(id => {
            document.getElementById(id)?.addEventListener('change', () => {
                this.handleSearch();
            });
        });
    }

    /**
     * 加载产品列表
     * @param {number} page 页码
     */
    async loadProductList(page = 1) {
        try {
            this.showListLoading();
            
            const params = new URLSearchParams({
                page: page,
                pageSize: this.pageSize,
                search: this.searchQuery,
                category: this.filterCategory,
                status: this.filterStatus
            });

            const response = await fetch(`${this.apiBasePath}&${params}`, {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken() || localStorage.getItem('admin_token')}`
                }
            });

            const data = await response.json();
            
            if (data.success || data.code === 200) {
                this.currentProducts = data.data.products || [];
                this.totalProducts = data.data.pagination ? data.data.pagination.total_records : data.data.total || 0;
                this.currentPage = page;
                
                this.renderProductList();
                this.renderPagination();
                this.updateProductStats();
                this.loadCategories();
            } else {
                throw new Error(data.message || '加载产品列表失败');
            }
        } catch (error) {
            console.error('加载产品列表失败:', error);
            this.showListError('加载产品列表失败，请重试');
            
            // 兼容性处理：确保showMessage方法存在
            if (this.utils && typeof this.utils.showMessage === 'function') {
                this.utils.showMessage('加载产品列表失败', 'error');
            } else if (window.UtilsModule && typeof window.UtilsModule.showMessage === 'function') {
                window.UtilsModule.showMessage('加载产品列表失败', 'error');
            } else {
                console.error('Utils.showMessage方法不可用:', this.utils);
            }
        }
    }

    /**
     * 渲染产品列表
     */
    renderProductList() {
        const container = document.getElementById('productListContainer');
        if (!container) return;

        if (this.currentProducts.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-inbox" style="font-size: 3rem; color: #6b7280;"></i>
                    <h5 class="mt-3 text-muted">暂无产品数据</h5>
                    <p class="text-muted">点击上方"添加产品"按钮开始添加产品</p>
                </div>
            `;
            return;
        }

        const viewMode = document.getElementById('gridViewBtn').classList.contains('active') ? 'grid' : 'list';
        container.className = `card-body ${viewMode}-view`;

        container.innerHTML = this.currentProducts.map(product => `
            <div class="product-card">
                <div class="product-image">
                    ${product.image_url ? 
                        `<img src="${product.image_url}" alt="${product.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">` :
                        `<i class="bi bi-image"></i>`
                    }
                </div>
                <div class="product-info flex-grow-1">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <h6>${product.name}</h6>
                            <div class="sku">SKU: ${product.sku}</div>
                        </div>
                        <div class="text-end">
                            <span class="status-badge status-${product.status}">
                                ${this.getStatusText(product.status)}
                            </span>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="price">¥${this.formatPrice(product.price)}</div>
                        <div class="stock ${product.stock <= product.stock_alert ? 'low' : ''}">
                            库存: ${product.stock}
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="category-badge">${product.category_name || '未分类'}</span>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" 
                                    onclick="productManager.showProductModal(${product.id})" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-secondary" 
                                    onclick="productManager.duplicateProduct(${product.id})" title="复制">
                                <i class="bi bi-files"></i>
                            </button>
                            <button class="btn btn-outline-danger" 
                                    onclick="productManager.deleteProduct(${product.id})" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 显示列表加载状态
     */
    showListLoading() {
        const container = document.getElementById('productListContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在加载产品数据...</div>
            </div>
        `;
    }

    /**
     * 显示列表错误状态
     * @param {string} message 错误信息
     */
    showListError(message) {
        const container = document.getElementById('productListContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-danger">${message}</h5>
                <button class="btn btn-outline-primary mt-2" onclick="productManager.loadProductList()">
                    重新加载
                </button>
            </div>
        `;
    }

    /**
     * 更新产品统计数据
     */
    async updateProductStats() {
        try {
            const response = await fetch(this.apiStatsPath, {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken() || localStorage.getItem('admin_token')}`
                }
            });

            const data = await response.json();
            
            if (data.success || data.code === 200) {
                const stats = data.data;
                document.getElementById('totalProductsCount').textContent = stats.total || 0;
                document.getElementById('activeProductsCount').textContent = stats.active || 0;
                document.getElementById('lowStockCount').textContent = stats.low_stock || 0;
                document.getElementById('categoriesCount').textContent = stats.categories || 0;
            }
        } catch (error) {
            console.error('加载产品统计失败:', error);
        }
    }

    /**
     * 加载分类列表
     */
    async loadCategories() {
        try {
            const response = await fetch(this.apiCategoriesPath, {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken() || localStorage.getItem('admin_token')}`
                }
            });

            const data = await response.json();
            
            if (data.success || data.code === 200) {
                const categories = data.data.categories || [];
                
                // 更新筛选器选项
                const categoryFilter = document.getElementById('categoryFilter');
                const productCategory = document.getElementById('productCategory');
                
                const categoryOptions = categories.map(cat => 
                    `<option value="${cat.id}">${cat.name}</option>`
                ).join('');
                
                if (categoryFilter) {
                    categoryFilter.innerHTML = '<option value="all">全部分类</option>' + categoryOptions;
                }
                
                if (productCategory) {
                    productCategory.innerHTML = '<option value="">请选择分类</option>' + categoryOptions;
                }
            }
        } catch (error) {
            console.error('加载分类失败:', error);
        }
    }

    /**
     * 显示产品模态框
     * @param {number} productId 产品ID（编辑时）
     */
    async showProductModal(productId = null) {
        const modal = new bootstrap.Modal(document.getElementById('productModal'));
        const title = document.getElementById('productModalTitle');
        const form = document.getElementById('productForm');

        // 重置表单
        form.reset();
        await this.loadCategories();

        if (productId) {
            // 编辑模式
            title.textContent = '编辑产品';
            
            try {
                const response = await fetch(`${this.apiBasePath}&id=${productId}`, {
                    headers: {
                        'Authorization': `Bearer ${this.authManager?.getToken() || localStorage.getItem('admin_token')}`
                    }
                });
                const data = await response.json();
                if (data.success || data.code === 200) {
                    const product = data.data.products?.[0] || data.data;
                    document.getElementById('productId').value = product.id;
                    document.getElementById('productName').value = product.name;
                    document.getElementById('productSku').value = product.sku;
                    document.getElementById('productCategory').value = product.category_id || '';
                    document.getElementById('productStatus').value = product.status;
                    document.getElementById('productDescription').value = product.description || '';
                    document.getElementById('productPrice').value = product.price;
                    document.getElementById('productCost').value = product.cost_price || '';
                    document.getElementById('productStock').value = product.stock || 0;
                    document.getElementById('stockAlert').value = product.stock_alert || 10;
                    document.getElementById('productWeight').value = product.weight || '';
                    document.getElementById('productDimensions').value = product.dimensions || '';
                    document.getElementById('trackInventory').checked = product.track_inventory;
                    document.getElementById('allowBackorder').checked = product.allow_backorder;
                }
            } catch (error) {
                console.error('加载产品信息失败:', error);
                this.utils.showMessage('加载产品信息失败', 'error');
                return;
            }
        } else {
            // 添加模式
            title.textContent = '添加产品';
        }

        modal.show();
    }

    /**
     * 处理保存产品
     */
    async handleSaveProduct() {
        const form = document.getElementById('productForm');
        const formData = new FormData(form);
        const productId = document.getElementById('productId').value;

        // 表单验证
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // 构建产品数据
        const productData = {
            name: document.getElementById('productName').value,
            sku: document.getElementById('productSku').value,
            category_id: document.getElementById('productCategory').value || null,
            status: document.getElementById('productStatus').value,
            description: document.getElementById('productDescription').value,
            price: parseFloat(document.getElementById('productPrice').value),
            cost_price: parseFloat(document.getElementById('productCost').value) || null,
            stock: parseInt(document.getElementById('productStock').value) || 0,
            stock_alert: parseInt(document.getElementById('stockAlert').value) || 10,
            weight: parseFloat(document.getElementById('productWeight').value) || null,
            dimensions: document.getElementById('productDimensions').value || null,
            track_inventory: document.getElementById('trackInventory').checked,
            allow_backorder: document.getElementById('allowBackorder').checked
        };

        // 处理图片上传
        const imageFile = document.getElementById('productImage').files[0];
        if (imageFile) {
            formData.append('image', imageFile);
        }

        try {
            const saveBtn = document.getElementById('saveProductBtn');
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';

            let response;
            if (productId) {
                // 编辑产品
                Object.keys(productData).forEach(key => {
                    formData.append(key, productData[key]);
                });
                response = await fetch(`${this.apiBasePath}&id=${productId}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${this.authManager?.getToken() || localStorage.getItem('admin_token')}`
                    },
                    body: formData
                });
            } else {
                // 添加产品
                Object.keys(productData).forEach(key => {
                    formData.append(key, productData[key]);
                });
                response = await fetch(this.apiBasePath, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.authManager?.getToken() || localStorage.getItem('admin_token')}`
                    },
                    body: formData
                });
            }

            const data = await response.json();
            if (data.success || data.code === 200) {
                this.utils.showMessage(productId ? '产品更新成功' : '产品添加成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('productModal')).hide();
                this.loadProductList(this.currentPage);
            } else {
                throw new Error(data.message || '保存失败');
            }
        } catch (error) {
            console.error('保存产品失败:', error);
            this.utils.showMessage(error.message || '保存产品失败', 'error');
        } finally {
            const saveBtn = document.getElementById('saveProductBtn');
            saveBtn.disabled = false;
            saveBtn.innerHTML = '保存';
        }
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        this.searchQuery = document.getElementById('productSearchInput').value.trim();
        this.filterCategory = document.getElementById('categoryFilter').value;
        this.filterStatus = document.getElementById('statusFilter').value;
        
        this.currentPage = 1;
        this.loadProductList(1);
    }

    /**
     * 切换视图模式
     * @param {string} mode 视图模式 grid/list
     */
    switchView(mode) {
        const gridBtn = document.getElementById('gridViewBtn');
        const listBtn = document.getElementById('listViewBtn');
        
        if (mode === 'grid') {
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
        } else {
            listBtn.classList.add('active');
            gridBtn.classList.remove('active');
        }
        
        this.renderProductList();
    }

    /**
     * 复制产品
     * @param {number} productId 产品ID
     */
    async duplicateProduct(productId) {
        try {
            const response = await fetch(`${this.apiBasePath}&action=duplicate&id=${productId}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken() || localStorage.getItem('admin_token')}`
                }
            });
            
            const data = await response.json();
            if (data.success || data.code === 200) {
                this.utils.showMessage('产品复制成功', 'success');
                this.loadProductList(this.currentPage);
            } else {
                throw new Error(data.message || '复制失败');
            }
        } catch (error) {
            console.error('复制产品失败:', error);
            this.utils.showMessage(error.message || '复制产品失败', 'error');
        }
    }

    /**
     * 删除产品
     * @param {number} productId 产品ID
     */
    async deleteProduct(productId) {
        if (!confirm('确定要删除这个产品吗？此操作不可恢复！')) {
            return;
        }

        try {
            const response = await fetch(`${this.apiBasePath}&id=${productId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken() || localStorage.getItem('admin_token')}`
                }
            });
            
            const data = await response.json();
            if (data.success || data.code === 200) {
                this.utils.showMessage('产品删除成功', 'success');
                this.loadProductList(this.currentPage);
            } else {
                throw new Error(data.message || '删除失败');
            }
        } catch (error) {
            console.error('删除产品失败:', error);
            this.utils.showMessage(error.message || '删除产品失败', 'error');
        }
    }

    /**
     * 显示分类管理
     */
    showCategoryManagement() {
        this.utils.showMessage('分类管理功能开发中', 'info');
    }

    /**
     * 获取状态文本
     * @param {string} status 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'active': '上架',
            'inactive': '下架',
            'draft': '草稿'
        };
        return statusMap[status] || status;
    }

    /**
     * 格式化价格 - 兼容性方法
     * @param {number} price 价格
     * @returns {string} 格式化后的价格
     */
    formatPrice(price) {
        try {
            // 优先使用utils.formatNumber
            if (this.utils && typeof this.utils.formatNumber === 'function') {
                return this.utils.formatNumber(price, 2);
            }
            
            // 回退到window.AdminUtils实例
            if (window.AdminUtils) {
                const utils = new window.AdminUtils();
                if (typeof utils.formatNumber === 'function') {
                    return utils.formatNumber(price, 2);
                }
            }
            
            // 最后的回退方案：简单格式化
            return parseFloat(price || 0).toFixed(2);
        } catch (error) {
            console.warn('价格格式化失败:', error);
            return parseFloat(price || 0).toFixed(2);
        }
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        const totalPages = Math.ceil(this.totalProducts / this.pageSize);
        const pagination = document.getElementById('productPagination');
        const currentPageInfo = document.getElementById('currentPageInfo');
        const totalProductsInfo = document.getElementById('totalProductsInfo');

        if (!pagination) return;

        // 更新信息显示
        const startItem = (this.currentPage - 1) * this.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.pageSize, this.totalProducts);
        currentPageInfo.textContent = `${startItem}-${endItem}`;
        totalProductsInfo.textContent = this.totalProducts;

        // 生成分页HTML
        let paginationHTML = '';

        // 上一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="productManager.loadProductList(${this.currentPage - 1})">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码逻辑
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="productManager.loadProductList(1)">1</a>
                </li>
            `;
            if (startPage > 2) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="productManager.loadProductList(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="productManager.loadProductList(${totalPages})">${totalPages}</a>
                </li>
            `;
        }

        // 下一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="productManager.loadProductList(${this.currentPage + 1})">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }
}

// 创建全局实例
const productManager = new ProductManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ProductManager };
} else {
    // 浏览器环境 - 模块加载器期望的是实例，不是类
    window.ProductManager = productManager; // 导出实例，让模块加载器可以直接调用render方法
    window.productManager = productManager; // 保持向后兼容
} 