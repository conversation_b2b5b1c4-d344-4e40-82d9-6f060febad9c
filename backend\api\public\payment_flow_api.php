<?php
/**
 * 支付流程管理API接口 - PHP 5.6兼容版本
 * 提供基础的支付流程操作接口
 */

require_once dirname(dirname(__FILE__)) . '/config/database.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 初始化数据库连接
$database = new Database();
$pdo = $database->connect();

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // 支持两种路由方式：?action=xxx 或 ?do=xxx
    $action = '';
    if (isset($_GET['action'])) {
        $action = $_GET['action'];
    } elseif (isset($_GET['do'])) {
        $action = $_GET['do'];
    } elseif (isset($_POST['do'])) {
        $action = $_POST['do'];
    }
    
    switch ($action) {
        case 'create_payment':
            handleCreatePayment();
            break;
            
        case 'query_payment':
            handleQueryPayment();
            break;
            
        case 'payment_statistics':
            handlePaymentStatistics();
            break;
            
        case 'clean_expired':
            handleCleanExpired();
            break;
            
        case 'payment_list':
            handlePaymentList();
            break;
            
        default:
            // 返回API信息
            echo json_encode(array(
                'error_code' => 0,
                'error_message' => 'success',
                'data' => array(
                    'message' => '支付流程管理API',
                    'version' => '1.0',
                    'endpoints' => array(
                        'create_payment' => '创建支付请求',
                        'query_payment' => '查询支付状态', 
                        'payment_statistics' => '获取支付统计',
                        'clean_expired' => '清理过期请求',
                        'payment_list' => '获取支付列表'
                    )
                )
            ));
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(array(
        'error_code' => 1,
        'error_message' => $e->getMessage(),
        'data' => null
    ));
}

/**
 * 创建支付请求
 */
function handleCreatePayment() {
    global $pdo;
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    // 获取输入数据
    $input = $_POST;
    if (empty($input)) {
        $json = file_get_contents('php://input');
        if ($json) {
            $input = json_decode($json, true);
        }
    }
    
    if (!$input) {
        throw new Exception('无效的请求数据');
    }
    
    // 验证必需参数
    $required = array('merchant_id', 'amount', 'order_no');
    foreach ($required as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            throw new Exception("缺少必需参数: {$field}");
        }
    }
    
    // 生成请求ID
    $requestId = 'PAY_' . date('YmdHis') . '_' . mt_rand(100000, 999999);
    
    // 设置过期时间（30分钟后）
    $expiredAt = date('Y-m-d H:i:s', time() + 30 * 60);
    
    // 插入数据库
    $stmt = $pdo->prepare("
        INSERT INTO payment_requests 
        (request_id, merchant_id, amount, merchant_order_no, subject, body, status, created_at, expired_at, client_ip) 
        VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW(), ?, ?)
    ");
    
    $subject = isset($input['subject']) ? $input['subject'] : '';
    $body = isset($input['body']) ? $input['body'] : '';
    $clientIp = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
    
    $stmt->execute(array(
        $requestId,
        $input['merchant_id'],
        $input['amount'],
        $input['order_no'],
        $subject,
        $body,
        $expiredAt,
        $clientIp
    ));
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'request_id' => $requestId,
            'amount' => floatval($input['amount']),
            'status' => 'pending',
            'expired_at' => $expiredAt,
            'payment_url' => 'https://top005.com/pay?id=' . $requestId
        )
    ));
}

/**
 * 查询支付请求状态
 */
function handleQueryPayment() {
    global $pdo;
    
    // 获取请求ID
    $requestId = '';
    if (isset($_GET['request_id'])) {
        $requestId = $_GET['request_id'];
    } elseif (isset($_POST['request_id'])) {
        $requestId = $_POST['request_id'];
    }
    
    if (!$requestId) {
        throw new Exception('请求ID不能为空');
    }
    
    // 查询数据库
    $stmt = $pdo->prepare("SELECT * FROM payment_requests WHERE request_id = ?");
    $stmt->execute(array($requestId));
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$request) {
        throw new Exception('支付请求不存在');
    }
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'request_id' => $request['request_id'],
            'merchant_id' => intval($request['merchant_id']),
            'amount' => floatval($request['amount']),
            'merchant_order_no' => $request['merchant_order_no'],
            'status' => $request['status'],
            'created_at' => $request['created_at'],
            'paid_at' => $request['paid_at'],
            'expired_at' => $request['expired_at']
        )
    ));
}

/**
 * 获取支付统计数据
 */
function handlePaymentStatistics() {
    global $pdo;
    
    // 获取参数
    $merchantId = isset($_GET['merchant_id']) ? $_GET['merchant_id'] : null;
    $startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-7 days'));
    $endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
    
    // 构建查询条件
    $where = "created_at >= ? AND created_at <= ?";
    $params = array($startDate . ' 00:00:00', $endDate . ' 23:59:59');
    
    if ($merchantId) {
        $where .= " AND merchant_id = ?";
        $params[] = $merchantId;
    }
    
    // 查询统计数据
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_requests,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
            COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_requests,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_requests,
            SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as total_amount,
            AVG(CASE WHEN status = 'paid' THEN amount ELSE NULL END) as avg_amount
        FROM payment_requests 
        WHERE {$where}
    ");
    
    $stmt->execute($params);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 计算成功率
    $successRate = 0;
    if ($stats['total_requests'] > 0) {
        $successRate = round(($stats['paid_requests'] / $stats['total_requests']) * 100, 2);
    }
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'total_requests' => intval($stats['total_requests']),
            'paid_requests' => intval($stats['paid_requests']),
            'pending_requests' => intval($stats['pending_requests']),
            'expired_requests' => intval($stats['expired_requests']),
            'cancelled_requests' => intval($stats['cancelled_requests']),
            'total_amount' => floatval($stats['total_amount']),
            'avg_amount' => floatval($stats['avg_amount']),
            'success_rate' => $successRate,
            'date_range' => array(
                'start_date' => $startDate,
                'end_date' => $endDate
            )
        )
    ));
}

/**
 * 清理过期支付请求
 */
function handleCleanExpired() {
    global $pdo;
    
    // 清理过期的pending状态请求
    $stmt = $pdo->prepare("
        UPDATE payment_requests 
        SET status = 'expired' 
        WHERE status = 'pending' AND expired_at < NOW()
    ");
    
    $stmt->execute();
    $cleanedCount = $stmt->rowCount();
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'cleaned_count' => $cleanedCount,
            'message' => "已清理 {$cleanedCount} 个过期请求"
        )
    ));
}

/**
 * 获取支付请求列表
 */
function handlePaymentList() {
    global $pdo;
    
    // 获取分页参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
    $merchantId = isset($_GET['merchant_id']) ? $_GET['merchant_id'] : null;
    $status = isset($_GET['status']) ? $_GET['status'] : '';
    
    if ($page < 1) $page = 1;
    if ($limit < 1 || $limit > 100) $limit = 20;
    
    // 构建查询条件
    $where = array('1=1');
    $params = array();
    
    if ($merchantId) {
        $where[] = 'merchant_id = ?';
        $params[] = $merchantId;
    }
    
    if ($status) {
        $where[] = 'status = ?';
        $params[] = $status;
    }
    
    $whereClause = implode(' AND ', $where);
    
    // 获取总数
    $countStmt = $pdo->prepare("SELECT COUNT(*) FROM payment_requests WHERE {$whereClause}");
    $countStmt->execute($params);
    $total = $countStmt->fetchColumn();
    
    // 获取列表数据
    $offset = ($page - 1) * $limit;
    $listStmt = $pdo->prepare("
        SELECT * FROM payment_requests 
        WHERE {$whereClause} 
        ORDER BY created_at DESC 
        LIMIT {$limit} OFFSET {$offset}
    ");
    $listStmt->execute($params);
    $requests = $listStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 格式化数据
    $formattedRequests = array();
    foreach ($requests as $request) {
        $formattedRequests[] = array(
            'id' => intval($request['id']),
            'request_id' => $request['request_id'],
            'merchant_id' => intval($request['merchant_id']),
            'merchant_order_no' => $request['merchant_order_no'],
            'amount' => floatval($request['amount']),
            'status' => $request['status'],
            'subject' => isset($request['subject']) ? $request['subject'] : '',
            'created_at' => $request['created_at'],
            'paid_at' => $request['paid_at'],
            'expired_at' => $request['expired_at']
        );
    }
    
    echo json_encode(array(
        'error_code' => 0,
        'error_message' => 'success',
        'data' => array(
            'list' => $formattedRequests,
            'pagination' => array(
                'page' => $page,
                'limit' => $limit,
                'total' => intval($total),
                'pages' => ceil($total / $limit)
            )
        )
    ));
}
?> 