<?php
/**
 * SQL路由管理系统
 * 实现三层路由架构中的SQL路由层
 * 统一管理数据库查询，提供缓存、安全防护和性能监控
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月
 */

class SqlRouter {
    
    private static $queries = [];
    private static $initialized = false;
    private static $db = null;
    private static $cache = [];
    private static $queryStats = [];
    private static $tenantFilters = [];
    
    /**
     * 初始化SQL路由系统
     */
    public static function init($dbConnection = null) {
        if (self::$initialized) {
            return;
        }
        
        self::$initialized = true;
        self::$db = $dbConnection;
        self::initializeQueries();
        
        error_log("SqlRouter 初始化完成");
    }
    
    /**
     * 配置所有SQL查询路由
     */
    private static function initializeQueries() {
        
        // ================================
        // 用户认证相关查询
        // ================================
        self::addQuery('user.login', [
            'sql' => 'SELECT u.*, d.tenant_type, d.tenant_id 
                      FROM users u 
                      LEFT JOIN domain_configs d ON d.tenant_id = u.platform_id 
                      WHERE u.username = :username AND u.status = "active"',
            'params' => ['username'],
            'cache_ttl' => 300,
            'description' => '用户登录查询'
        ]);
        
        self::addQuery('user.by_id', [
            'sql' => 'SELECT * FROM users WHERE id = :user_id AND status != "deleted"',
            'params' => ['user_id'],
            'cache_ttl' => 600,
            'tenant_filter' => false,
            'description' => '根据ID查询用户'
        ]);
        
        self::addQuery('user.update_last_login', [
            'sql' => 'UPDATE users SET last_login = NOW() WHERE id = :user_id',
            'params' => ['user_id'],
            'type' => 'update',
            'description' => '更新用户最后登录时间'
        ]);
        
        // ================================
        // 商户管理查询
        // ================================
        self::addQuery('merchant.list', [
            'sql' => 'SELECT m.*, u.username, u.status as user_status, u.created_at as user_created_at
                      FROM merchants m 
                      LEFT JOIN users u ON m.user_id = u.id 
                      WHERE 1=1 {tenant_filter} {search_filter} {status_filter}
                      ORDER BY m.created_at DESC 
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['search', 'status', 'platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 60,
            'description' => '获取商户列表'
        ]);
        
        self::addQuery('merchant.detail', [
            'sql' => 'SELECT m.*, u.username, u.status as user_status, u.created_at as user_created_at,
                             p.name as platform_name
                      FROM merchants m 
                      LEFT JOIN users u ON m.user_id = u.id
                      LEFT JOIN platforms p ON m.platform_id = p.id
                      WHERE m.id = :merchant_id {tenant_filter}',
            'params' => ['merchant_id'],
            'tenant_filter' => true,
            'cache_ttl' => 300,
            'description' => '获取商户详情'
        ]);
        
        self::addQuery('merchant.create', [
            'sql' => 'INSERT INTO merchants (platform_id, merchant_code, user_id, balance, status, 
                                           api_key, callback_url, service_rate, salt, created_at) 
                      VALUES (:platform_id, :merchant_code, :user_id, :balance, :status, 
                              :api_key, :callback_url, :service_rate, :salt, NOW())',
            'params' => ['platform_id', 'merchant_code', 'user_id', 'balance', 'status', 
                        'api_key', 'callback_url', 'service_rate', 'salt'],
            'type' => 'insert',
            'description' => '创建新商户'
        ]);
        
        self::addQuery('merchant.update', [
            'sql' => 'UPDATE merchants SET 
                             merchant_code = :merchant_code,
                             callback_url = :callback_url,
                             service_rate = :service_rate,
                             updated_at = NOW()
                      WHERE id = :merchant_id {tenant_filter}',
            'params' => ['merchant_id', 'merchant_code', 'callback_url', 'service_rate'],
            'tenant_filter' => true,
            'type' => 'update',
            'description' => '更新商户信息'
        ]);
        
        self::addQuery('merchant.count', [
            'sql' => 'SELECT COUNT(*) as count FROM merchants m
                      JOIN users u ON m.user_id = u.id
                      WHERE 1=1 {tenant_filter} {search_filter} {status_filter}',
            'params' => [],
            'optional_params' => ['search', 'status', 'platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 120,
            'description' => '统计商户数量'
        ]);

        self::addQuery('merchant.stats', [
            'sql' => 'SELECT
                        COUNT(*) as total_merchants,
                        COUNT(CASE WHEN m.status = "pending" THEN 1 END) as pending_merchants,
                        COUNT(CASE WHEN m.status = "approved" THEN 1 END) as approved_merchants,
                        COUNT(CASE WHEN m.status = "active" THEN 1 END) as active_merchants,
                        COUNT(CASE WHEN m.status = "inactive" THEN 1 END) as inactive_merchants,
                        COUNT(CASE WHEN m.status = "disabled" THEN 1 END) as disabled_merchants,
                        COUNT(CASE WHEN m.status = "rejected" THEN 1 END) as rejected_merchants
                      FROM merchants m
                      JOIN users u ON m.user_id = u.id
                      WHERE 1=1 {tenant_filter}',
            'params' => [],
            'optional_params' => ['platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 300,
            'description' => '获取商户统计信息'
        ]);

        self::addQuery('merchant.simple_list', [
            'sql' => 'SELECT m.id, u.username, m.merchant_code
                      FROM merchants m
                      JOIN users u ON m.user_id = u.id
                      WHERE 1=1 {tenant_filter} {search_filter} {status_filter}
                      ORDER BY m.created_at DESC',
            'params' => [],
            'optional_params' => ['search', 'status', 'platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 300,
            'description' => '获取简化商户列表'
        ]);

        self::addQuery('merchant.update_status', [
            'sql' => 'UPDATE merchants SET status = :status, updated_at = NOW()
                      WHERE id = :merchant_id {tenant_filter}',
            'params' => ['merchant_id', 'status'],
            'tenant_filter' => true,
            'type' => 'update',
            'description' => '更新商户状态'
        ]);
        
        // ================================
        // 码商管理查询 - 平台管理员专用
        // ================================
        self::addQuery('provider.list', [
            'sql' => 'SELECT p.*, u.username, u.status as user_status, u.created_at as user_created_at,
                             u.last_login, u.user_type
                      FROM payment_providers p
                      LEFT JOIN users u ON p.user_id = u.id
                      WHERE 1=1 {tenant_filter} {search_filter} {status_filter}
                      ORDER BY p.created_at DESC
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['search', 'status', 'platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 60,
            'description' => '获取码商列表'
        ]);

        self::addQuery('provider.detail', [
            'sql' => 'SELECT p.*, u.username, u.status as user_status, u.created_at as user_created_at,
                             u.last_login, u.user_type
                      FROM payment_providers p
                      LEFT JOIN users u ON p.user_id = u.id
                      WHERE p.id = :provider_id {tenant_filter}',
            'params' => ['provider_id'],
            'tenant_filter' => true,
            'cache_ttl' => 300,
            'description' => '获取码商详情'
        ]);
        
        self::addQuery('provider.count', [
            'sql' => 'SELECT COUNT(*) as count FROM payment_providers p
                      JOIN users u ON p.user_id = u.id
                      WHERE 1=1 {tenant_filter} {search_filter} {status_filter}',
            'params' => [],
            'optional_params' => ['search', 'status', 'platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 120,
            'description' => '统计码商数量'
        ]);

        self::addQuery('provider.stats', [
            'sql' => 'SELECT
                        COUNT(*) as total_providers,
                        COUNT(CASE WHEN p.status = "pending" THEN 1 END) as pending_providers,
                        COUNT(CASE WHEN p.status = "approved" THEN 1 END) as approved_providers,
                        COUNT(CASE WHEN p.status = "active" THEN 1 END) as active_providers,
                        COUNT(CASE WHEN p.status = "inactive" THEN 1 END) as inactive_providers,
                        COUNT(CASE WHEN p.status = "disabled" THEN 1 END) as disabled_providers,
                        COUNT(CASE WHEN p.status = "rejected" THEN 1 END) as rejected_providers
                      FROM payment_providers p
                      JOIN users u ON p.user_id = u.id
                      WHERE 1=1 {tenant_filter}',
            'params' => [],
            'optional_params' => ['platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 300,
            'description' => '获取码商统计信息'
        ]);

        self::addQuery('provider.create', [
            'sql' => 'INSERT INTO payment_providers (platform_id, user_id, status, created_at)
                      VALUES (:platform_id, :user_id, :status, NOW())',
            'params' => ['platform_id', 'user_id', 'status'],
            'type' => 'insert',
            'description' => '创建码商'
        ]);

        self::addQuery('provider.update', [
            'sql' => 'UPDATE payment_providers SET
                             provider_code = :provider_code,
                             dns_domain = :dns_domain,
                             max_devices = :max_devices,
                             max_groups = :max_groups
                      WHERE id = :provider_id {tenant_filter}',
            'params' => ['provider_id', 'provider_code', 'dns_domain', 'max_devices', 'max_groups'],
            'tenant_filter' => true,
            'type' => 'update',
            'description' => '更新码商信息'
        ]);

        self::addQuery('provider.update_status', [
            'sql' => 'UPDATE payment_providers SET status = :status
                      WHERE id = :provider_id {tenant_filter}',
            'params' => ['provider_id', 'status'],
            'tenant_filter' => true,
            'type' => 'update',
            'description' => '更新码商状态'
        ]);

        // ================================
        // 员工管理查询 - 平台管理员专用
        // ================================
        self::addQuery('employee.list', [
            'sql' => 'SELECT e.*, u.username, u.status as user_status, u.created_at as user_created_at,
                             u.last_login, jp.position_name
                      FROM employees e
                      LEFT JOIN users u ON e.user_id = u.id
                      LEFT JOIN job_positions jp ON e.position_id = jp.id
                      WHERE 1=1 {tenant_filter} {search_filter} {status_filter}
                      ORDER BY e.created_at DESC
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['search', 'status', 'platform_id', 'position_id'],
            'tenant_filter' => true,
            'cache_ttl' => 60,
            'description' => '获取员工列表'
        ]);

        self::addQuery('employee.count', [
            'sql' => 'SELECT COUNT(*) as count FROM employees e
                      JOIN users u ON e.user_id = u.id
                      WHERE 1=1 {tenant_filter} {search_filter} {status_filter}',
            'params' => [],
            'optional_params' => ['search', 'status', 'platform_id', 'position_id'],
            'tenant_filter' => true,
            'cache_ttl' => 120,
            'description' => '统计员工数量'
        ]);

        self::addQuery('job_position.list', [
            'sql' => 'SELECT * FROM job_positions
                      WHERE 1=1 {tenant_filter} {search_filter}
                      ORDER BY created_at DESC',
            'params' => [],
            'optional_params' => ['search', 'platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 300,
            'description' => '获取职位列表'
        ]);

        // ================================
        // 财务管理查询 - 平台管理员专用
        // ================================
        self::addQuery('finance.flow_list', [
            'sql' => 'SELECT f.*, m.merchant_code, p.company_name as provider_name
                      FROM finance_flows f
                      LEFT JOIN merchants m ON f.merchant_id = m.id
                      LEFT JOIN providers p ON f.provider_id = p.id
                      WHERE 1=1 {tenant_filter} {date_filter} {type_filter} {search_filter}
                      ORDER BY f.created_at DESC
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['start_date', 'end_date', 'flow_type', 'search', 'platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 60,
            'description' => '获取资金流水列表'
        ]);

        self::addQuery('finance.settlement_list', [
            'sql' => 'SELECT s.*, m.merchant_code, p.company_name as provider_name
                      FROM settlements s
                      LEFT JOIN merchants m ON s.merchant_id = m.id
                      LEFT JOIN providers p ON s.provider_id = p.id
                      WHERE 1=1 {tenant_filter} {date_filter} {status_filter} {search_filter}
                      ORDER BY s.created_at DESC
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['start_date', 'end_date', 'status', 'search', 'platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 60,
            'description' => '获取结算管理列表'
        ]);

        self::addQuery('finance.reports_summary', [
            'sql' => 'SELECT
                        DATE(created_at) as report_date,
                        SUM(CASE WHEN flow_type = "income" THEN amount ELSE 0 END) as total_income,
                        SUM(CASE WHEN flow_type = "expense" THEN amount ELSE 0 END) as total_expense,
                        COUNT(*) as total_transactions
                      FROM finance_flows
                      WHERE 1=1 {tenant_filter} {date_filter}
                      GROUP BY DATE(created_at)
                      ORDER BY report_date DESC',
            'params' => [],
            'optional_params' => ['start_date', 'end_date', 'platform_id'],
            'tenant_filter' => true,
            'cache_ttl' => 300,
            'description' => '获取财务报表汇总'
        ]);

        // ================================
        // 员工管理查询 - 平台管理员专用
        // ================================
        self::addQuery('employee.list', [
            'sql' => 'SELECT e.*, u.username, u.status as user_status, u.created_at as user_created_at,
                             u.last_login, u.user_type, jp.position_name
                      FROM employees e
                      LEFT JOIN users u ON e.user_id = u.id
                      LEFT JOIN job_positions jp ON e.job_position_id = jp.id
                      WHERE e.belongs_to_type = "platform" {tenant_filter} {search_filter} {status_filter}
                      ORDER BY e.created_at DESC
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['search', 'status', 'platform_id', 'job_position_id'],
            'tenant_filter' => true,
            'cache_ttl' => 60,
            'description' => '获取平台员工列表'
        ]);

        self::addQuery('employee.count', [
            'sql' => 'SELECT COUNT(*) as count FROM employees e
                      JOIN users u ON e.user_id = u.id
                      WHERE e.belongs_to_type = "platform" {tenant_filter} {search_filter} {status_filter}',
            'params' => [],
            'optional_params' => ['search', 'status', 'platform_id', 'job_position_id'],
            'tenant_filter' => true,
            'cache_ttl' => 120,
            'description' => '统计平台员工数量'
        ]);

        self::addQuery('job_position.list', [
            'sql' => 'SELECT * FROM job_positions
                      WHERE 1=1 {search_filter}
                      ORDER BY created_at DESC',
            'params' => [],
            'optional_params' => ['search'],
            'tenant_filter' => false,
            'cache_ttl' => 300,
            'description' => '获取职位列表'
        ]);

        self::addQuery('employee.create', [
            'sql' => 'INSERT INTO employees (user_id, belongs_to_type, belongs_to_id, job_position_id,
                                           hire_date, status, created_at)
                      VALUES (:user_id, "platform", :platform_id, :job_position_id,
                              :hire_date, :status, NOW())',
            'params' => ['user_id', 'platform_id', 'job_position_id', 'hire_date', 'status'],
            'type' => 'insert',
            'description' => '创建平台员工'
        ]);

        self::addQuery('employee.update_status', [
            'sql' => 'UPDATE employees SET status = :status, updated_at = NOW()
                      WHERE id = :employee_id AND belongs_to_type = "platform" {tenant_filter}',
            'params' => ['employee_id', 'status'],
            'tenant_filter' => true,
            'type' => 'update',
            'description' => '更新员工状态'
        ]);

        // ================================
        // 设备管理查询
        // ================================
        self::addQuery('device.list', [
            'sql' => 'SELECT d.*, p.company_name as provider_name
                      FROM devices d 
                      LEFT JOIN providers p ON d.provider_id = p.id
                      WHERE 1=1 {tenant_filter} {search_filter} {status_filter}
                      ORDER BY d.created_at DESC 
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['search', 'status', 'provider_id'],
            'tenant_filter' => true,
            'cache_ttl' => 60,
            'description' => '获取设备列表'
        ]);
        
        self::addQuery('device.by_provider', [
            'sql' => 'SELECT * FROM devices WHERE provider_id = :provider_id {tenant_filter} {status_filter}',
            'params' => ['provider_id'],
            'optional_params' => ['status'],
            'tenant_filter' => true,
            'cache_ttl' => 120,
            'description' => '获取指定码商的设备'
        ]);
        
        // ================================
        // 订单管理查询
        // ================================
        self::addQuery('order.list', [
            'sql' => 'SELECT o.*, m.merchant_code, p.company_name as provider_name, d.device_name
                      FROM orders o
                      LEFT JOIN merchants m ON o.merchant_id = m.id
                      LEFT JOIN providers p ON o.provider_id = p.id
                      LEFT JOIN devices d ON o.device_id = d.device_id
                      WHERE 1=1 {tenant_filter} {date_filter} {status_filter} {search_filter}
                      ORDER BY o.created_at DESC 
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['start_date', 'end_date', 'status', 'search', 'merchant_id', 'provider_id'],
            'tenant_filter' => true,
            'cache_ttl' => 30,
            'description' => '获取订单列表'
        ]);
        
        self::addQuery('order.stats', [
            'sql' => 'SELECT 
                        COUNT(*) as total_orders,
                        SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_orders,
                        SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_orders,
                        SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_orders,
                        SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as total_amount
                      FROM orders 
                      WHERE 1=1 {tenant_filter} {date_filter}',
            'params' => [],
            'optional_params' => ['start_date', 'end_date', 'merchant_id', 'provider_id'],
            'tenant_filter' => true,
            'cache_ttl' => 300,
            'description' => '获取订单统计数据'
        ]);
        
        self::addQuery('order.detail', [
            'sql' => 'SELECT o.*, m.merchant_code, p.company_name as provider_name, d.device_name
                      FROM orders o
                      LEFT JOIN merchants m ON o.merchant_id = m.id
                      LEFT JOIN providers p ON o.provider_id = p.id
                      LEFT JOIN devices d ON o.device_id = d.device_id
                      WHERE o.id = :order_id {tenant_filter}',
            'params' => ['order_id'],
            'tenant_filter' => true,
            'cache_ttl' => 600,
            'description' => '获取订单详情'
        ]);
        
        // ================================
        // 财务管理查询
        // ================================
        self::addQuery('finance.flow_list', [
            'sql' => 'SELECT f.*, 
                        CASE 
                            WHEN f.user_type = "merchant" THEN m.merchant_code
                            WHEN f.user_type = "provider" THEN p.company_name
                            ELSE "系统"
                        END as user_name
                      FROM financial_flows f
                      LEFT JOIN merchants m ON f.user_id = m.user_id AND f.user_type = "merchant"
                      LEFT JOIN providers p ON f.user_id = p.user_id AND f.user_type = "provider"
                      WHERE 1=1 {tenant_filter} {date_filter} {type_filter}
                      ORDER BY f.created_at DESC 
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['start_date', 'end_date', 'flow_type', 'user_id', 'user_type'],
            'tenant_filter' => true,
            'cache_ttl' => 60,
            'description' => '获取资金流水列表'
        ]);
        
        self::addQuery('finance.balance', [
            'sql' => 'SELECT 
                        SUM(CASE WHEN flow_type = "income" THEN amount ELSE -amount END) as balance
                      FROM financial_flows 
                      WHERE user_id = :user_id AND user_type = :user_type {tenant_filter}',
            'params' => ['user_id', 'user_type'],
            'tenant_filter' => true,
            'cache_ttl' => 120,
            'description' => '计算用户余额'
        ]);
        
        // ================================
        // 支付宝账单查询
        // ================================
        self::addQuery('alipay_bills.list', [
            'sql' => 'SELECT ab.*, d.device_name, o.order_no as matched_order_no
                      FROM alipay_bills ab
                      LEFT JOIN devices d ON ab.device_id = d.device_id
                      LEFT JOIN orders o ON ab.matched_order_id = o.id
                      WHERE 1=1 {tenant_filter} {match_status_filter} {date_filter}
                      ORDER BY ab.transaction_time DESC 
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['match_status', 'start_date', 'end_date', 'device_id'],
            'tenant_filter' => true,
            'cache_ttl' => 30,
            'description' => '获取支付宝账单列表'
        ]);
        
        self::addQuery('alipay_bills.stats', [
            'sql' => 'SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN match_status = "matched" THEN 1 ELSE 0 END) as matched,
                        SUM(CASE WHEN match_status = "unmatched" THEN 1 ELSE 0 END) as unmatched,
                        SUM(CASE WHEN match_status = "ignored" THEN 1 ELSE 0 END) as ignored,
                        SUM(amount) as total_amount
                      FROM alipay_bills 
                      WHERE 1=1 {tenant_filter} {date_filter}',
            'params' => [],
            'optional_params' => ['start_date', 'end_date', 'device_id'],
            'tenant_filter' => true,
            'cache_ttl' => 180,
            'description' => '获取支付宝账单统计'
        ]);
        
        // ================================
        // 平台管理查询（系统管理员）
        // ================================
        self::addQuery('platform.list', [
            'sql' => 'SELECT p.*, u.username as admin_username
                      FROM platforms p
                      LEFT JOIN users u ON p.user_id = u.id
                      ORDER BY p.created_at DESC',
            'params' => [],
            'tenant_filter' => false,
            'permission_required' => 'system_admin',
            'cache_ttl' => 300,
            'description' => '获取平台列表'
        ]);
        
        self::addQuery('domain.list', [
            'sql' => 'SELECT * FROM domain_configs ORDER BY created_at DESC',
            'params' => [],
            'tenant_filter' => false,
            'permission_required' => 'system_admin',
            'cache_ttl' => 600,
            'description' => '获取域名配置列表'
        ]);
        
        // ================================
        // 日志和审计查询
        // ================================
        self::addQuery('logs.user_actions', [
            'sql' => 'SELECT * FROM user_action_logs 
                      WHERE 1=1 {tenant_filter} {date_filter} {user_filter}
                      ORDER BY created_at DESC 
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['start_date', 'end_date', 'user_id', 'action_type'],
            'tenant_filter' => true,
            'cache_ttl' => 0,
            'description' => '获取用户操作日志'
        ]);
        
        self::addQuery('logs.api_calls', [
            'sql' => 'SELECT * FROM api_call_logs 
                      WHERE 1=1 {tenant_filter} {date_filter}
                      ORDER BY created_at DESC 
                      LIMIT :limit OFFSET :offset',
            'params' => ['limit', 'offset'],
            'optional_params' => ['start_date', 'end_date', 'user_id', 'route_name'],
            'tenant_filter' => true,
            'cache_ttl' => 0,
            'description' => '获取API调用日志'
        ]);
        
        error_log("SQL查询路由配置完成，共配置 " . count(self::$queries) . " 个查询");
    }
    
    /**
     * 添加查询配置
     */
    private static function addQuery($name, $config) {
        self::$queries[$name] = array_merge([
            'sql' => '',
            'params' => [],
            'optional_params' => [],
            'type' => 'select',
            'cache_ttl' => 0,
            'tenant_filter' => false,
            'permission_required' => null,
            'description' => ''
        ], $config);
    }
    
    /**
     * 执行查询
     */
    public static function execute($queryName, $params = [], $userContext = null) {
        if (!isset(self::$queries[$queryName])) {
            throw new Exception("查询 {$queryName} 不存在");
        }
        
        $query = self::$queries[$queryName];
        
        // 权限检查
        if ($query['permission_required'] && $userContext) {
            if ($userContext['user_type'] !== $query['permission_required']) {
                throw new Exception("权限不足，需要 {$query['permission_required']} 权限");
            }
        }
        
        // 生成缓存键
        $cacheKey = self::generateCacheKey($queryName, $params, $userContext);
        
        // 检查缓存
        if ($query['cache_ttl'] > 0 && isset(self::$cache[$cacheKey])) {
            $cached = self::$cache[$cacheKey];
            if (time() - $cached['timestamp'] < $query['cache_ttl']) {
                self::recordQueryStats($queryName, 'cache_hit', 0);
                return $cached['data'];
            } else {
                unset(self::$cache[$cacheKey]);
            }
        }
        
        // 构建SQL
        $sql = self::buildSql($query, $params, $userContext);
        
        // 执行查询
        $startTime = microtime(true);
        try {
            $result = self::executeQuery($sql, $params, $query['type']);
            $executeTime = (microtime(true) - $startTime) * 1000;
            
            // 记录统计
            self::recordQueryStats($queryName, 'success', $executeTime);
            
            // 缓存结果
            if ($query['cache_ttl'] > 0 && $query['type'] === 'select') {
                self::$cache[$cacheKey] = [
                    'data' => $result,
                    'timestamp' => time()
                ];
            }
            
            // 记录查询日志
            self::logQuery($queryName, $sql, $params, $userContext, $executeTime, true);
            
            return $result;
            
        } catch (Exception $e) {
            $executeTime = (microtime(true) - $startTime) * 1000;
            
            // 记录错误统计
            self::recordQueryStats($queryName, 'error', $executeTime);
            
            // 记录错误日志
            self::logQuery($queryName, $sql, $params, $userContext, $executeTime, false, $e->getMessage());
            
            throw $e;
        }
    }
    
    /**
     * 构建SQL语句
     */
    private static function buildSql($query, $params, $userContext) {
        $sql = $query['sql'];
        
        // 应用租户过滤
        if ($query['tenant_filter'] && $userContext) {
            $tenantFilter = self::buildTenantFilter($userContext);
            $sql = str_replace('{tenant_filter}', $tenantFilter, $sql);
        } else {
            $sql = str_replace('{tenant_filter}', '', $sql);
        }
        
        // 应用搜索过滤
        $searchFilter = '';
        if (isset($params['search']) && !empty($params['search'])) {
            $searchFilter = self::buildSearchFilter($params['search']);
        }
        $sql = str_replace('{search_filter}', $searchFilter, $sql);
        
        // 应用状态过滤
        $statusFilter = '';
        if (isset($params['status']) && !empty($params['status'])) {
            $statusFilter = " AND status = '" . self::escapeString($params['status']) . "'";
        }
        $sql = str_replace('{status_filter}', $statusFilter, $sql);
        
        // 应用日期过滤
        $dateFilter = '';
        if (isset($params['start_date']) && !empty($params['start_date'])) {
            $dateFilter .= " AND created_at >= '" . self::escapeString($params['start_date']) . "'";
        }
        if (isset($params['end_date']) && !empty($params['end_date'])) {
            $dateFilter .= " AND created_at <= '" . self::escapeString($params['end_date']) . " 23:59:59'";
        }
        $sql = str_replace('{date_filter}', $dateFilter, $sql);
        
        // 应用匹配状态过滤（支付宝账单专用）
        $matchStatusFilter = '';
        if (isset($params['match_status']) && !empty($params['match_status'])) {
            $matchStatusFilter = " AND match_status = '" . self::escapeString($params['match_status']) . "'";
        }
        $sql = str_replace('{match_status_filter}', $matchStatusFilter, $sql);
        
        // 应用类型过滤
        $typeFilter = '';
        if (isset($params['flow_type']) && !empty($params['flow_type'])) {
            $typeFilter = " AND flow_type = '" . self::escapeString($params['flow_type']) . "'";
        }
        $sql = str_replace('{type_filter}', $typeFilter, $sql);
        
        // 应用用户过滤
        $userFilter = '';
        if (isset($params['user_id']) && !empty($params['user_id'])) {
            $userFilter = " AND user_id = " . (int)$params['user_id'];
        }
        $sql = str_replace('{user_filter}', $userFilter, $sql);
        
        return $sql;
    }
    
    /**
     * 构建租户过滤条件
     */
    private static function buildTenantFilter($userContext) {
        if (!$userContext) {
            return '';
        }
        
        $userType = $userContext['user_type'];
        $userId = $userContext['user_id'];
        $platformId = isset($userContext['platform_id']) ? $userContext['platform_id'] : null;
        
        switch ($userType) {
            case 'system_admin':
                return ''; // 系统管理员无限制
                
            case 'platform_admin':
                return $platformId ? " AND platform_id = {$platformId}" : '';
                
            case 'provider':
                return " AND provider_id = {$userId}";
                
            case 'merchant':
                return " AND merchant_id = {$userId}";
                
            default:
                return ' AND 1=0'; // 未知用户类型，拒绝访问
        }
    }
    
    /**
     * 构建搜索过滤条件
     */
    private static function buildSearchFilter($search) {
        $search = self::escapeString($search);
        return " AND (username LIKE '%{$search}%' OR merchant_code LIKE '%{$search}%' OR company_name LIKE '%{$search}%')";
    }
    
    /**
     * 执行数据库查询
     */
    private static function executeQuery($sql, $params, $type) {
        if (!self::$db) {
            throw new Exception("数据库连接未初始化");
        }

        try {
            // 转换命名参数为位置参数（适配Database类）
            $convertedParams = array();
            $convertedSql = $sql;

            foreach ($params as $key => $value) {
                $placeholder = ":{$key}";
                if (strpos($convertedSql, $placeholder) !== false) {
                    $convertedSql = str_replace($placeholder, '?', $convertedSql);
                    $convertedParams[] = $value;
                }
            }

            switch ($type) {
                case 'select':
                    return self::$db->fetchAll($convertedSql, $convertedParams);

                case 'insert':
                case 'update':
                case 'delete':
                    $affectedRows = self::$db->execute($convertedSql, $convertedParams);
                    $result = ['affected_rows' => $affectedRows];

                    if ($type === 'insert') {
                        $result['insert_id'] = self::$db->lastInsertId();
                    }

                    return $result;

                default:
                    return self::$db->fetchAll($convertedSql, $convertedParams);
            }

        } catch (Exception $e) {
            throw new Exception("数据库查询失败: " . $e->getMessage());
        }
    }
    
    /**
     * 生成缓存键
     */
    private static function generateCacheKey($queryName, $params, $userContext) {
        $keyData = [
            'query' => $queryName,
            'params' => $params,
            'user' => $userContext ? $userContext['user_id'] : 0,
            'type' => $userContext ? $userContext['user_type'] : 'anonymous'
        ];
        return 'sql_' . md5(json_encode($keyData));
    }
    
    /**
     * 记录查询统计
     */
    private static function recordQueryStats($queryName, $status, $executeTime) {
        if (!isset(self::$queryStats[$queryName])) {
            self::$queryStats[$queryName] = [
                'total_calls' => 0,
                'success_calls' => 0,
                'error_calls' => 0,
                'cache_hits' => 0,
                'total_time' => 0,
                'avg_time' => 0,
                'max_time' => 0
            ];
        }
        
        $stats = &self::$queryStats[$queryName];
        $stats['total_calls']++;
        
        switch ($status) {
            case 'success':
                $stats['success_calls']++;
                $stats['total_time'] += $executeTime;
                $stats['avg_time'] = $stats['total_time'] / $stats['success_calls'];
                $stats['max_time'] = max($stats['max_time'], $executeTime);
                break;
                
            case 'error':
                $stats['error_calls']++;
                break;
                
            case 'cache_hit':
                $stats['cache_hits']++;
                break;
        }
    }
    
    /**
     * 记录查询日志
     */
    private static function logQuery($queryName, $sql, $params, $userContext, $executeTime, $success, $error = null) {
        try {
            $logData = [
                'query_name' => $queryName,
                'sql' => $sql,
                'params' => $params,
                'user_id' => isset($userContext['user_id']) ? $userContext['user_id'] : null,
                'user_type' => isset($userContext['user_type']) ? $userContext['user_type'] : 'anonymous',
                'execute_time' => round($executeTime, 2),
                'success' => $success,
                'error' => $error,
                'timestamp' => date('Y-m-d H:i:s'),
                'ip_address' => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown'
            ];
            
            // 记录到日志文件
            $logFile = '/data/logs/sql_queries.log';
            $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
            
        } catch (Exception $e) {
            error_log("记录SQL查询日志失败: " . $e->getMessage());
        }
    }
    
    /**
     * 字符串转义
     */
    private static function escapeString($string) {
        return addslashes($string);
    }
    
    /**
     * 获取查询配置
     */
    public static function getQuery($queryName) {
        return isset(self::$queries[$queryName]) ? self::$queries[$queryName] : null;
    }
    
    /**
     * 获取所有查询配置
     */
    public static function getAllQueries() {
        return self::$queries;
    }
    
    /**
     * 获取查询统计
     */
    public static function getQueryStats() {
        return self::$queryStats;
    }
    
    /**
     * 清理缓存
     */
    public static function clearCache($pattern = null) {
        if ($pattern) {
            foreach (self::$cache as $key => $value) {
                if (strpos($key, $pattern) !== false) {
                    unset(self::$cache[$key]);
                }
            }
        } else {
            self::$cache = [];
        }
    }
    
    /**
     * 获取缓存统计
     */
    public static function getCacheStats() {
        return [
            'total_cached' => count(self::$cache),
            'memory_usage' => memory_get_usage(true),
            'cache_keys' => array_keys(self::$cache)
        ];
    }
    
    /**
     * 设置数据库连接
     */
    public static function setDatabase($db) {
        self::$db = $db;
    }
}

error_log("SqlRouter 类加载完成");
?> 