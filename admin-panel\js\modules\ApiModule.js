/**
 * API接口模块 - 统一API管理
 * 整合api-management.js、API密钥管理、接口测试等功能
 * 实现界面共用+数据差异化的设计模式
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2024-01-20
 */

class ApiModule {
    constructor() {

        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        this.currentRole = null;
        this.apiList = [];
        this.apiKeys = {};
        
        // 初始化子模块系统
        this.subModules = {};
        this.subModulesLoaded = false;
        
        console.log('✅ ApiModule initialized');
        
        // 设置全局引用以便在HTML中调用
        window.apiModule = this;
    }

    /**
     * 统一渲染方法 - 方案5配置驱动的入口
     */
    async render(container, params = {}) {
        try {
            this.currentRole = params.role || this.getCurrentUserRole();
            this.activeTab = params.activeTab; // 保存激活的选项卡
            console.log(`🎯 API模块渲染 - 角色: ${this.currentRole}, 激活选项卡: ${this.activeTab || '默认'}`);
            
            // 显示加载状态
            container.innerHTML = UIComponents.generateLoadingState('正在加载API数据...');
            
            // 加载子模块系统
            await this.loadSubModules();
            
            // 获取角色配置
            const config = RoleDataAdapter.getApiConfig(this.currentRole);
            
            // 生成HTML内容
            container.innerHTML = this.generateHTML(config);
            
            // 确保DOM渲染完成后再初始化事件和加载数据
            setTimeout(() => {
                this.initializeEvents();
                this.loadData();
                
                // 如果指定了activeTab，激活对应的选项卡
                if (this.activeTab) {
                    this.activateTab(this.activeTab);
                }
            }, 50);
            
        } catch (error) {
            console.error('❌ API模块渲染失败:', error);
            container.innerHTML = UIComponents.generateEmptyState({
                icon: 'bi-exclamation-triangle',
                title: 'API模块加载失败',
                description: error.message
            });
        }
    }

    getCurrentUserRole() {
        if (this.tenantInfo?.tenant_type) {
            return this.tenantInfo.tenant_type;
        }
        const user = this.authManager?.getUser();
        return user?.user_type || 'merchant';
    }

    /**
     * 加载子模块系统
     */
    async loadSubModules() {
        if (this.subModulesLoaded) {
            return this.subModules;
        }

        try {
            console.log('🔄 加载API子模块系统...');
            
            // 确保模块加载器存在
            if (!window.apiModuleLoader) {
                console.warn('⚠️ API模块加载器未找到，使用传统模式');
                return;
            }

            // 加载所有子模块
            await window.apiModuleLoader.loadAllModules();
            
            // 初始化子模块实例
            this.subModules = window.apiModuleLoader.initializeModules(
                this.apiClient, 
                this.authManager, 
                this.utils
            );
            
            this.subModulesLoaded = true;
            console.log('✅ API子模块系统加载完成');
            
            return this.subModules;
            
        } catch (error) {
            console.error('❌ 加载API子模块失败:', error);
            // 不抛出错误，允许使用传统模式
            return {};
        }
    }

    generateHTML(config) {
        // 准备统计数据配置，添加ID映射
        const statsConfig = this.getStatsConfig(this.currentRole);
        const mockStats = this.getMockStatsData(this.currentRole);
        
        // 为UIComponents格式添加ID和值
        const formattedStats = statsConfig.map((stat, index) => ({
            ...stat,
            value: mockStats[index] || '0',
            id: stat.id // 确保ID传递给UIComponents
        }));
        
        return `
            <div class="api-module" data-role="${this.currentRole}">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-cloud me-2"></i>${config.title}</h2>
                            <p class="text-muted mb-0">API接口管理与开发工具</p>
                        </div>
                        <div>
                            ${this.generateHeaderButtons()}
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 - 使用自定义方法确保ID正确 -->
                ${this.generateCustomStatsCards(formattedStats)}

                <!-- 功能选项卡 -->
                ${this.generateCustomTabs(config.tabs)}
            </div>

            <style>${this.generateStyles()}</style>
        `;
    }

    getRoleConfig(role) {
        const configs = {
            'system_admin': {
                title: '系统API管理',
                description: '全系统API接口监控与管理',
                icon: 'bi bi-cloud-arrow-up',
                color: 'primary'
            },
            'platform_admin': {
                title: '平台API管理', 
                description: '平台API接口配置与监控',
                icon: 'bi bi-diagram-3',
                color: 'success'
            },
            'provider': {
                title: 'API接口配置',
                description: '设备API接口配置',
                icon: 'bi bi-router',
                color: 'info'
            },
            'merchant': {
                title: 'API开发工具',
                description: 'API密钥、接口测试、代码生成',
                icon: 'bi bi-code-slash',
                color: 'warning'
            }
        };
        return configs[role] || configs['merchant'];
    }

    generateHeaderButtons() {
        if (this.currentRole === 'merchant') {
            return `
                <button class="btn btn-primary" id="regenerateKeyBtn">
                    <i class="bi bi-arrow-clockwise me-2"></i>重新生成密钥
                </button>
                <button class="btn btn-outline-secondary ms-2" id="downloadSDKBtn">
                    <i class="bi bi-download me-2"></i>下载SDK
                </button>
            `;
        } else {
            return `
                <button class="btn btn-primary" id="addApiBtn">
                    <i class="bi bi-plus-circle me-2"></i>添加接口
                </button>
                <button class="btn btn-outline-secondary ms-2" id="refreshApiBtn">
                    <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                </button>
            `;
        }
    }

    generateCustomStatsCards(stats) {
        if (!stats || !Array.isArray(stats)) return '';
        
        return `
            <div class="row mb-4">
                ${stats.map(stat => `
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-${stat.color || 'primary'} shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-${stat.color || 'primary'} text-uppercase mb-1">
                                            ${stat.label}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="${stat.id}">
                                            ${stat.value || '0'}
                                        </div>
                                        ${stat.change ? `
                                            <div class="text-xs ${stat.change.startsWith('+') ? 'text-success' : 'text-danger'}">
                                                ${stat.change}
                                            </div>
                                        ` : ''}
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi ${stat.icon} fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    generateCustomTabs(tabs) {
        if (!tabs || !Array.isArray(tabs)) return '';
        
        return `
            <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
                ${tabs.map((tab, index) => `
                    <li class="nav-item" role="presentation">
                        <button class="nav-link ${index === 0 ? 'active' : ''}" 
                                id="${tab.id}-tab" 
                                data-bs-toggle="tab" 
                                data-bs-target="#${tab.id}" 
                                type="button" 
                                role="tab">
                            <i class="bi ${tab.icon} me-2"></i>${tab.label}
                        </button>
                    </li>
                `).join('')}
            </ul>
            <div class="tab-content" id="mainTabsContent">
                ${tabs.map((tab, index) => `
                    <div class="tab-pane fade ${index === 0 ? 'show active' : ''}" 
                         id="${tab.id}" 
                         role="tabpanel" 
                         aria-labelledby="${tab.id}-tab">
                        ${this.generateTabPaneContent(tab)}
                    </div>
                `).join('')}
            </div>
        `;
    }

    getStatsConfig(role) {
        const configs = {
            'system_admin': [
                { id: 'totalApis', label: '总接口数', icon: 'bi bi-diagram-3', color: 'primary' },
                { id: 'todayCalls', label: '今日调用', icon: 'bi bi-graph-up', color: 'success' },
                { id: 'activeKeys', label: '活跃密钥', icon: 'bi bi-key', color: 'info' },
                { id: 'errorRate', label: '错误率', icon: 'bi bi-exclamation-triangle', color: 'warning' }
            ],
            'platform_admin': [
                { id: 'platformApis', label: '平台接口', icon: 'bi bi-diagram-3', color: 'primary' },
                { id: 'todayCalls', label: '今日调用', icon: 'bi bi-graph-up', color: 'success' },
                { id: 'successRate', label: '成功率', icon: 'bi bi-check-circle', color: 'info' },
                { id: 'avgResponse', label: '平均响应', icon: 'bi bi-speedometer', color: 'warning' }
            ],
            'provider': [
                { id: 'deviceApis', label: '设备接口', icon: 'bi bi-router', color: 'primary' },
                { id: 'todayRequests', label: '今日请求', icon: 'bi bi-arrow-up-circle', color: 'success' },
                { id: 'onlineDevices', label: '在线设备', icon: 'bi bi-phone', color: 'info' },
                { id: 'failedRequests', label: '失败请求', icon: 'bi bi-x-circle', color: 'danger' }
            ],
            'merchant': [
                { id: 'myApiCalls', label: '我的调用', icon: 'bi bi-graph-up', color: 'primary' },
                { id: 'todayQuota', label: '今日配额', icon: 'bi bi-speedometer2', color: 'success' },
                { id: 'successRate', label: '成功率', icon: 'bi bi-check-circle', color: 'info' },
                { id: 'remainingQuota', label: '剩余配额', icon: 'bi bi-hourglass-split', color: 'warning' }
            ]
        };
        return configs[role] || configs['merchant'];
    }

    generateTabHeaders() {
        const tabs = this.getTabsConfig(this.currentRole);
        return tabs.map((tab, index) => `
            <li class="nav-item">
                <a class="nav-link ${index === 0 ? 'active' : ''}" 
                   id="${tab.id}-tab" 
                   data-bs-toggle="tab" 
                   href="#${tab.id}">
                    <i class="${tab.icon} me-2"></i>${tab.title}
                </a>
            </li>
        `).join('');
    }

    getTabsConfig(role) {
        const configs = {
            'system_admin': [
                { id: 'overview', title: '接口概览', icon: 'bi bi-graph-up', type: 'overview' },
                { id: 'apis', title: '接口管理', icon: 'bi bi-diagram-3', type: 'apis' },
                { id: 'logs', title: '调用日志', icon: 'bi bi-list-ul', type: 'logs' },
                { id: 'settings', title: '系统设置', icon: 'bi bi-gear', type: 'settings' }
            ],
            'platform_admin': [
                { id: 'overview', title: '平台概览', icon: 'bi bi-graph-up', type: 'overview' },
                { id: 'apis', title: '接口管理', icon: 'bi bi-diagram-3', type: 'apis' },
                { id: 'monitoring', title: '监控告警', icon: 'bi bi-bell', type: 'monitoring' },
                { id: 'docs', title: 'API文档', icon: 'bi bi-file-text', type: 'docs' }
            ],
            'provider': [
                { id: 'overview', title: '设备概览', icon: 'bi bi-graph-up', type: 'overview' },
                { id: 'config', title: '接口配置', icon: 'bi bi-gear', type: 'config' },
                { id: 'status', title: '设备状态', icon: 'bi bi-phone', type: 'status' },
                { id: 'logs', title: '请求日志', icon: 'bi bi-list-ul', type: 'logs' }
            ],
            'merchant': [
                { id: 'keys', title: 'API密钥', icon: 'bi bi-key', type: 'keys' },
                { id: 'testing', title: '接口测试', icon: 'bi bi-play-circle', type: 'testing' },
                { id: 'generator', title: '代码生成', icon: 'bi bi-code', type: 'generator' },
                { id: 'signature', title: '签名工具', icon: 'bi bi-shield-check', type: 'signature' },
                { id: 'docs', title: 'API文档', icon: 'bi bi-file-text', type: 'docs' }
            ]
        };
        return configs[role] || configs['merchant'];
    }

    generateTabContent() {
        const tabs = this.getTabsConfig(this.currentRole);
        return tabs.map((tab, index) => `
            <div class="tab-pane fade ${index === 0 ? 'show active' : ''}" id="${tab.id}">
                ${this.generateTabPaneContent(tab)}
            </div>
        `).join('');
    }

    generateTabPaneContent(tab) {
        // 根据tab.id或tab.type来确定内容类型
        const contentType = tab.type || tab.id;
        
        switch (contentType) {
            case 'overview':
                return this.generateOverviewContent();
            case 'keys':
                return this.generateApiKeysContent();
            case 'testing':
            case 'test':
                return this.generateTestingContent();
            case 'generator':
                return this.generateGeneratorContent();
            case 'signature':
                return this.generateSignatureContent();
            case 'docs':
                return this.generateDocsContent();
            case 'apis':
                return this.generateApisContent();
            default:
                return '<div class="text-center py-4">内容加载中...</div>';
        }
    }

    generateOverviewContent() {
        return `
            <div class="row g-4">
                <div class="col-md-8">
                    <div class="chart-container">
                        <h6 class="mb-3">API调用趋势</h6>
                        <canvas id="apiChart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-summary">
                        <h6 class="mb-3">实时统计</h6>
                        <div id="realtimeStats">
                            <div class="stat-item">
                                <span class="label">当前QPS:</span>
                                <span class="value text-primary" id="currentQPS">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">平均延迟:</span>
                                <span class="value text-success" id="avgLatency">-</span>
                            </div>
                            <div class="stat-item">
                                <span class="label">错误率:</span>
                                <span class="value text-warning" id="errorRate">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateApiKeysContent() {
        // 如果子模块已加载，使用子模块生成内容
        if (this.subModules.apiKeyManager && typeof this.subModules.apiKeyManager.generateApiKeysContent === 'function') {
            return this.subModules.apiKeyManager.generateApiKeysContent();
        }
        
        // 兼容模式：使用原有内容
        return `
            <div class="api-keys-section">
                <!-- API密钥管理 - 集成merchant/api-key-manager.js功能 -->
                <div class="row">
                    <!-- 左侧：密钥信息 -->
                    <div class="col-md-8">
                        <!-- 基本信息卡片 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>基本信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">开发者ID</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="developerIdInput" readonly>
                                                <button class="btn btn-outline-secondary" onclick="window.apiModule.copyToClipboard('developerIdInput')">
                                                    <i class="bi bi-clipboard"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">用于API调用时标识您的身份</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">创建时间</label>
                                            <input type="text" class="form-control" id="keyCreatedAtInput" readonly>
                                            <small class="text-muted">密钥首次生成时间</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">API密钥</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="apiKeyInput" readonly>
                                        <button class="btn btn-outline-secondary" onclick="window.apiModule.toggleKeyVisibility('apiKeyInput', 'toggleKeyBtn')" id="toggleKeyBtn">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="window.apiModule.copyToClipboard('apiKeyInput')">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">请妥善保管您的API密钥，不要在客户端代码中暴露</small>
                                </div>


                                
                                <div class="alert alert-warning">
                                    <h6><i class="bi bi-exclamation-triangle me-2"></i>安全提醒</h6>
                                    <ul class="mb-0 small">
                                        <li>API密钥用于签名验证，请勿泄露给第三方</li>
                                        <li>建议定期更换API密钥以确保安全</li>
                                        <li>如果密钥泄露，请立即重新生成</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- IP白名单卡片 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><i class="bi bi-shield-check me-2"></i>IP白名单</h6>
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.apiModule.addIpWhitelist()">
                                        <i class="bi bi-plus me-1"></i>添加IP
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="ipWhitelistContainer">
                                    <div class="text-center py-3">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                        <div class="mt-2 text-muted">正在加载IP白名单...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 回调配置卡片 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-arrow-left-right me-2"></i>回调配置</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">回调地址</label>
                                    <div class="input-group">
                                        <input type="url" class="form-control" id="callbackUrl" placeholder="https://your-domain.com/callback">
                                        <button class="btn btn-outline-primary" onclick="window.apiModule.updateCallbackUrl()">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">订单状态变化时将向此地址发送通知</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">回调验证</label>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-outline-info btn-sm" onclick="window.apiModule.testCallback()">
                                            <i class="bi bi-play me-1"></i>测试回调
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" onclick="window.apiModule.viewCallbackLogs()">
                                            <i class="bi bi-list me-1"></i>回调日志
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧：统计信息 -->
                    <div class="col-md-4">
                        <!-- 调用统计卡片 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-graph-up me-2"></i>调用统计</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value" id="todayCallsCount">-</div>
                                            <div class="stat-label">今日调用</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-value" id="monthCallsCount">-</div>
                                            <div class="stat-label">本月调用</div>
                                        </div>
                                    </div>
                                    <div class="col-6 mt-3">
                                        <div class="stat-item">
                                            <div class="stat-value" id="totalCallsCount">-</div>
                                            <div class="stat-label">总调用量</div>
                                        </div>
                                    </div>
                                    <div class="col-6 mt-3">
                                        <div class="stat-item">
                                            <div class="stat-value text-success" id="successRateCount">-</div>
                                            <div class="stat-label">成功率</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 快速操作卡片 -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-lightning me-2"></i>快速操作</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary btn-sm" onclick="window.apiModule.regenerateApiKey()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>重新生成密钥
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="window.apiModule.downloadPostman()">
                                        <i class="bi bi-download me-1"></i>下载Postman
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="window.apiModule.openSignatureTool()">
                                        <i class="bi bi-calculator me-1"></i>签名工具
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="window.apiModule.openApiTester()">
                                        <i class="bi bi-play-circle me-1"></i>接口测试
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 权限设置卡片 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-gear me-2"></i>权限设置</h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allowPayment" checked>
                                    <label class="form-check-label" for="allowPayment">
                                        支付权限
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allowQuery" checked>
                                    <label class="form-check-label" for="allowQuery">
                                        查询权限
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allowRefund">
                                    <label class="form-check-label" for="allowRefund">
                                        退款权限
                                    </label>
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.apiModule.updatePermissions()">
                                        <i class="bi bi-check me-1"></i>更新权限
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateTestingContent() {
        // 如果子模块已加载，使用子模块生成内容
        if (this.subModules.apiTester && typeof this.subModules.apiTester.generateTestingContent === 'function') {
            return this.subModules.apiTester.generateTestingContent();
        }
        
        // 兼容模式：使用原有内容
        return `
            <div class="api-testing-section">
                <!-- API接口测试 - 集成merchant功能 -->
                <div class="row">
                    <!-- 左侧：测试配置 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-play-circle me-2"></i>接口测试</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">选择接口</label>
                                    <select class="form-select" id="testApiSelect">
                                        <option value="">请选择要测试的接口</option>
                                        <option value="payment">支付接口 - /api/payment/create</option>
                                        <option value="query">查询接口 - /api/payment/query</option>
                                        <option value="refund">退款接口 - /api/payment/refund</option>
                                        <option value="callback">回调测试 - /api/callback/test</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">请求方式</label>
                                    <select class="form-select" id="testMethodSelect">
                                        <option value="POST">POST</option>
                                        <option value="GET">GET</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">环境选择</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="testEnv" id="testEnvSandbox" value="sandbox" checked>
                                        <label class="form-check-label" for="testEnvSandbox">
                                            沙箱环境 (测试)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="testEnv" id="testEnvProduction" value="production">
                                        <label class="form-check-label" for="testEnvProduction">
                                            生产环境 (正式)
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">请求头 (Headers)</label>
                                    <textarea class="form-control" id="testHeaders" rows="3" placeholder='{"Content-Type": "application/json", "Authorization": "Bearer your-token"}'></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">请求参数 (JSON)</label>
                                    <textarea class="form-control" id="testParams" rows="8" placeholder='{"amount": 100, "order_id": "test123", "notify_url": "https://your-domain.com/callback"}'></textarea>
                                    <small class="text-muted">请输入有效的JSON格式参数</small>
                                </div>
                                
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary" id="sendTestBtn" onclick="window.apiModule.sendApiTest()">
                                        <i class="bi bi-send me-2"></i>发送测试
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="window.apiModule.loadTestTemplate()">
                                        <i class="bi bi-file-text me-2"></i>加载模板
                                    </button>
                                    <button class="btn btn-outline-info" onclick="window.apiModule.saveTestCase()">
                                        <i class="bi bi-bookmark me-2"></i>保存用例
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧：测试结果 -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="bi bi-terminal me-2"></i>响应结果</h6>
                                <div>
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.apiModule.copyResponse()">
                                        <i class="bi bi-clipboard me-1"></i>复制
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="window.apiModule.clearResponse()">
                                        <i class="bi bi-trash me-1"></i>清空
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 响应状态 -->
                                <div class="mb-3" id="responseStatus" style="display: none;">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>状态码:</span>
                                        <span class="badge" id="statusCode">-</span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <span>响应时间:</span>
                                        <span id="responseTime">-</span>
                                    </div>
                                </div>
                                
                                <!-- 响应内容 -->
                                <div class="mb-3">
                                    <label class="form-label">响应内容</label>
                                    <pre id="testResponse" class="bg-light p-3 border rounded" style="height: 300px; overflow-y: auto; font-size: 12px;">等待测试结果...</pre>
                                </div>
                                
                                <!-- 响应头 -->
                                <div class="mb-3">
                                    <label class="form-label">响应头</label>
                                    <pre id="responseHeaders" class="bg-light p-3 border rounded" style="height: 120px; overflow-y: auto; font-size: 12px;">等待响应头信息...</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 测试历史记录 -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="bi bi-clock-history me-2"></i>测试历史</h6>
                                <button class="btn btn-outline-danger btn-sm" onclick="window.apiModule.clearTestHistory()">
                                    <i class="bi bi-trash me-1"></i>清空历史
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>接口</th>
                                                <th>方法</th>
                                                <th>状态码</th>
                                                <th>响应时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="testHistoryTable">
                                            <tr>
                                                <td colspan="6" class="text-center text-muted">暂无测试历史</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateGeneratorContent() {
        // 如果子模块已加载，使用子模块生成内容
        if (this.subModules.apiCodeGenerator && typeof this.subModules.apiCodeGenerator.generateGeneratorContent === 'function') {
            return this.subModules.apiCodeGenerator.generateGeneratorContent();
        }
        
        // 兼容模式：使用原有内容
        return `
            <div class="code-generator-section">
                <div class="mb-3">
                    <label class="form-label">选择编程语言</label>
                    <select class="form-select" id="languageSelect">
                        <option value="php">PHP</option>
                        <option value="java">Java</option>
                        <option value="python">Python</option>
                        <option value="nodejs">Node.js</option>
                        <option value="curl">cURL</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">选择接口</label>
                    <select class="form-select" id="codeApiSelect">
                        <option value="">请选择接口</option>
                    </select>
                </div>
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h6 class="mb-0">生成的代码</h6>
                        <button class="btn btn-sm btn-outline-primary" id="copyCodeBtn">
                            <i class="bi bi-clipboard me-1"></i>复制代码
                        </button>
                    </div>
                    <div class="card-body">
                        <pre id="generatedCode" class="bg-light p-3" style="height: 400px; overflow-y: auto;">请选择语言和接口...</pre>
                    </div>
                </div>
            </div>
        `;
    }

    generateDocsContent() {
        return `
            <div class="api-docs-section">
                <!-- API文档 - 集成merchant/api-documentation-manager.js功能 -->
                <div class="row">
                    <!-- 左侧：文档导航 -->
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-list me-2"></i>文档导航</h6>
                            </div>
                            <div class="card-body p-0">
                                <div class="list-group list-group-flush">
                                    <a href="#" class="list-group-item list-group-item-action active" onclick="window.apiModule.switchDocSection('overview')">
                                        <i class="bi bi-info-circle me-2"></i>概述
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.apiModule.switchDocSection('authentication')">
                                        <i class="bi bi-shield-lock me-2"></i>认证方式
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.apiModule.switchDocSection('apis')">
                                        <i class="bi bi-code-square me-2"></i>API接口
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.apiModule.switchDocSection('callback')">
                                        <i class="bi bi-arrow-left-right me-2"></i>回调通知
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.apiModule.switchDocSection('errors')">
                                        <i class="bi bi-exclamation-triangle me-2"></i>错误码
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action" onclick="window.apiModule.switchDocSection('examples')">
                                        <i class="bi bi-file-earmark-code me-2"></i>示例代码
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 快速链接 -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-lightning me-2"></i>快速链接</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary btn-sm" onclick="window.apiModule.switchDocSection('apis')">
                                        <i class="bi bi-code-square me-1"></i>查看API
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="window.apiModule.downloadApiDocs()">
                                        <i class="bi bi-download me-1"></i>下载文档
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="window.apiModule.openApiTester()">
                                        <i class="bi bi-play me-1"></i>在线测试
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="window.apiModule.openSignatureTool()">
                                        <i class="bi bi-calculator me-1"></i>签名工具
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧：文档内容 -->
                    <div class="col-md-9">
                        <div id="docContent" class="card">
                            <div class="card-body">
                                <!-- 概述内容 -->
                                <div id="docOverview">
                                    <h4><i class="bi bi-info-circle me-2"></i>API概述</h4>
                                    <hr>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h5>系统介绍</h5>
                                            <p class="text-muted">PayPal支付系统为商户提供完整的支付解决方案，支持多种支付方式和灵活的接口调用。</p>
                                            
                                            <h5 class="mt-4">主要特性</h5>
                                            <ul class="list-unstyled">
                                                <li><i class="bi bi-check-circle text-success me-2"></i>支持支付宝、微信等主流支付方式</li>
                                                <li><i class="bi bi-check-circle text-success me-2"></i>实时订单状态查询</li>
                                                <li><i class="bi bi-check-circle text-success me-2"></i>安全的签名验证机制</li>
                                                <li><i class="bi bi-check-circle text-success me-2"></i>灵活的回调通知</li>
                                                <li><i class="bi bi-check-circle text-success me-2"></i>完整的错误处理</li>
                                            </ul>
                                            
                                            <h5 class="mt-4">技术规范</h5>
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-sm">
                                                    <tr><td><strong>协议</strong></td><td>HTTPS</td></tr>
                                                    <tr><td><strong>请求方式</strong></td><td>POST</td></tr>
                                                    <tr><td><strong>字符编码</strong></td><td>UTF-8</td></tr>
                                                    <tr><td><strong>数据格式</strong></td><td>JSON</td></tr>
                                                    <tr><td><strong>签名算法</strong></td><td>MD5/SHA256</td></tr>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6><i class="bi bi-globe me-2"></i>API基础信息</h6>
                                                    <p class="small mb-2"><strong>测试环境:</strong><br>
                                                    <code>https://sandbox-api.paypal.com</code></p>
                                                    <p class="small mb-2"><strong>生产环境:</strong><br>
                                                    <code>https://api.paypal.com</code></p>
                                                    <p class="small mb-0"><strong>版本:</strong> v2.0</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 其他文档内容将通过JS动态加载 -->
                                <div id="docAuthentication" style="display: none;"></div>
                                <div id="docApis" style="display: none;"></div>
                                <div id="docCallback" style="display: none;"></div>
                                <div id="docErrors" style="display: none;"></div>
                                <div id="docExamples" style="display: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateApisContent() {
        return `
            <div class="apis-management-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6>接口列表</h6>
                    <button class="btn btn-primary btn-sm" id="addNewApiBtn">
                        <i class="bi bi-plus-circle me-2"></i>添加接口
                    </button>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>接口名称</th>
                                <th>路径</th>
                                <th>方法</th>
                                <th>版本</th>
                                <th>状态</th>
                                <th>今日调用</th>
                                <th>成功率</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="apisTableBody">
                            <tr><td colspan="8" class="text-center py-4">加载中...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    generateStyles() {
        return `
            .api-module { padding: 0; }
            .stat-card {
                background: white; border-radius: 10px; padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                display: flex; align-items: center; gap: 15px;
                transition: transform 0.2s;
            }
            .stat-card:hover { transform: translateY(-2px); }
            .stat-icon {
                width: 50px; height: 50px; border-radius: 50%;
                display: flex; align-items: center; justify-content: center;
                color: white; font-size: 20px;
            }
            .stat-content { flex: 1; }
            .stat-number { font-size: 24px; font-weight: bold; color: #333; }
            .stat-label { color: #666; font-size: 14px; }
            .chart-container, .stats-summary, .api-keys-section, .api-testing-section {
                background: white; border-radius: 10px; padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .stat-item, .info-item {
                display: flex; justify-content: space-between;
                margin-bottom: 10px; padding-bottom: 10px;
                border-bottom: 1px solid #eee;
            }
            .stat-item:last-child, .info-item:last-child {
                border-bottom: none; margin-bottom: 0;
            }
            .docs-content { padding: 20px; }
        `;
    }

    initializeEvents() {
        // 基础事件监听
        const refreshBtn = document.getElementById('refreshApiBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }

        // 商户专用事件
        if (this.currentRole === 'merchant') {
            this.initializeMerchantEvents();
        }
    }

    initializeMerchantEvents() {
        // API密钥相关事件
        const toggleKeyBtn = document.getElementById('toggleKeyBtn');
        if (toggleKeyBtn) {
            toggleKeyBtn.addEventListener('click', () => this.toggleKeyVisibility('apiKeyInput', 'toggleKeyBtn'));
        }

        const copyKeyBtn = document.getElementById('copyKeyBtn');
        if (copyKeyBtn) {
            copyKeyBtn.addEventListener('click', () => this.copyToClipboard('apiKeyInput'));
        }

        // 接口测试事件
        const sendTestBtn = document.getElementById('sendTestBtn');
        if (sendTestBtn) {
            sendTestBtn.addEventListener('click', () => this.sendApiTest());
        }

        // 代码生成事件
        const languageSelect = document.getElementById('languageSelect');
        if (languageSelect) {
            languageSelect.addEventListener('change', () => this.generateCode());
        }
    }

    async loadData() {
        try {
            console.log(`📊 加载${this.currentRole}的API数据...`);
            
            // 加载统计数据
            await this.loadStatsData();
            
            // 根据角色加载不同数据
            if (this.currentRole === 'merchant') {
                await this.loadMerchantData();
            } else {
                await this.loadAdminData();
            }
            
        } catch (error) {
            console.error('❌ 加载API数据失败:', error);
        }
    }

    async loadStatsData() {
        const mockStats = this.getMockStatsData(this.currentRole);
        this.updateStatsCards(mockStats);
    }

    getMockStatsData(role) {
        const mockData = {
            'system_admin': {
                'totalApis': '45',
                'todayCalls': '12,345',
                'activeKeys': '156',
                'errorRate': '0.5%'
            },
            'merchant': {
                'myApiCalls': '1,234',
                'todayQuota': '5,000',
                'successRate': '99.2%',
                'remainingQuota': '3,766'
            }
        };
        return mockData[role] || mockData['merchant'];
    }

    updateStatsCards(data) {
        Object.keys(data).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = data[key];
            } else {
                console.warn(`⚠️ 统计卡片元素未找到: ${key}`);
            }
        });
    }

    async loadMerchantData() {
        try {
            console.log('📋 加载商户API密钥数据...');
            
            // 调用完整的商户API数据加载功能
            await this.loadMerchantApiKeyData();
            this.loadApiOptions();
            
            console.log('✅ 商户API数据加载完成');
        } catch (error) {
            console.error('❌ 加载商户API数据失败:', error);
            // 使用模拟数据作为后备
            this.apiKeys = {
                api_key: 'pk_test_1234567890abcdef',
                created_at: '2024-01-15 10:30:00',
                last_used: '2024-01-20 14:25:30',
                call_count: 1234
            };
            this.updateApiKeysDisplay();
            this.loadApiOptions();
        }
    }

    updateApiKeysDisplay(retryCount = 0) {
        // 防止无限重试
        if (retryCount >= 5) {
            console.error('❌ API密钥显示更新失败：元素未找到，已达到最大重试次数');
            return;
        }

        // 确保apiKeys数据存在
        if (!this.apiKeys) {
            console.warn('⚠️ API密钥数据未初始化');
            return;
        }

        // 确保元素存在后再设置值
        const apiKeyInput = document.getElementById('apiKeyInput');
        const keyCreatedAt = document.getElementById('keyCreatedAt');
        const keyLastUsed = document.getElementById('keyLastUsed');
        const keyCallCount = document.getElementById('keyCallCount');

        if (!apiKeyInput) {
            console.warn(`⚠️ API密钥输入框元素未找到，稍后重试... (尝试 ${retryCount + 1}/5)`);
            // 延迟一小段时间后重试
            setTimeout(() => {
                this.updateApiKeysDisplay(retryCount + 1);
            }, 100);
            return;
        }

        try {
            apiKeyInput.value = this.apiKeys.api_key;
            
            if (keyCreatedAt) keyCreatedAt.textContent = this.apiKeys.created_at;
            if (keyLastUsed) keyLastUsed.textContent = this.apiKeys.last_used;
            if (keyCallCount) keyCallCount.textContent = this.apiKeys.call_count.toLocaleString();
            
            console.log('✅ API密钥显示更新成功');
        } catch (error) {
            console.error('❌ 更新API密钥显示时出错:', error);
        }
    }

    loadApiOptions() {
        const apis = [
            { id: 1, name: '创建支付订单', path: '/api/pay/create' },
            { id: 2, name: '查询订单状态', path: '/api/pay/query' },
            { id: 3, name: '获取收款码', path: '/api/pay/qrcode' }
        ];

        const testSelect = document.getElementById('testApiSelect');
        const codeSelect = document.getElementById('codeApiSelect');
        
        // 检查元素是否存在，因为可能在不同的标签页中
        if (testSelect) {
            testSelect.innerHTML = '<option value="">请选择要测试的接口</option>' +
                apis.map(api => `<option value="${api.id}">${api.name}</option>`).join('');
        }
        
        if (codeSelect) {
            codeSelect.innerHTML = '<option value="">请选择接口</option>' +
                apis.map(api => `<option value="${api.id}">${api.name}</option>`).join('');
        }
    }

    async loadAdminData() {
        // 管理员数据加载
        console.log('📋 加载管理员API数据...');
    }

    toggleKeyVisibility(inputId, buttonId) {
        const input = document.getElementById(inputId);
        const button = document.getElementById(buttonId);
        const icon = button.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'bi bi-eye';
        }
    }

    copyToClipboard(inputId) {
        const input = document.getElementById(inputId);
        input.select();
        document.execCommand('copy');
        this.utils?.showMessage('已复制到剪贴板', 'success');
    }

    sendApiTest() {
        const apiId = document.getElementById('testApiSelect').value;
        const params = document.getElementById('testParams').value;
        
        if (!apiId) {
            this.utils?.showMessage('请选择要测试的接口', 'warning');
            return;
        }

        // 模拟API测试
        const mockResponse = {
            code: 200,
            message: 'success',
            data: {
                order_id: 'ORD' + Date.now(),
                amount: 100,
                status: 'pending',
                qr_code: 'data:image/png;base64,iVBORw0KGgoAAAANS...'
            },
            timestamp: new Date().toISOString()
        };

        document.getElementById('testResponse').textContent = JSON.stringify(mockResponse, null, 2);
        this.utils?.showMessage('API测试完成', 'success');
    }

    generateCode() {
        const language = document.getElementById('languageSelect').value;
        const apiId = document.getElementById('codeApiSelect').value;
        
        if (!language || !apiId) return;

        const codeTemplates = {
            php: `<?php
$api_key = 'your_api_key';
$secret_key = 'your_secret_key';
$url = 'https://api.paypal.com/v1/pay/create';

$data = array(
    'amount' => 100,
    'order_id' => 'ORDER_' . time(),
    'notify_url' => 'https://your-domain.com/notify'
);

$signature = hash_hmac('sha256', json_encode($data), $secret_key);

$headers = array(
    'Content-Type: application/json',
    'Authorization: Bearer ' . $api_key,
    'Signature: ' . $signature
);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

echo $response;`,
            java: `import java.net.http.*;
import java.net.URI;

public class PayPalAPI {
    private static final String API_KEY = "your_api_key";
    private static final String SECRET_KEY = "your_secret_key";
    
    public static void main(String[] args) throws Exception {
        String url = "https://api.paypal.com/v1/pay/create";
        String data = "{\\"amount\\": 100, \\"order_id\\": \\"ORDER_123\\"}";
        
        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Content-Type", "application/json")
            .header("Authorization", "Bearer " + API_KEY)
            .POST(HttpRequest.BodyPublishers.ofString(data))
            .build();
            
        HttpResponse<String> response = client.send(request, 
            HttpResponse.BodyHandlers.ofString());
        System.out.println(response.body());
    }
}`
        };

        const code = codeTemplates[language] || '// 代码模板暂未提供';
        document.getElementById('generatedCode').textContent = code;
    }

    refreshData() {
        console.log('🔄 刷新API数据');
        this.loadData();
    }

    showError(container, message) {
        container.innerHTML = UIComponents.generateEmptyState({
            icon: 'bi-exclamation-triangle',
            title: '加载失败',
            description: message,
            color: 'danger'
        });
    }

    // ==================== 集成merchant目录下的完整API功能 ====================

    /**
     * 商户API密钥管理功能
     */
    async loadMerchantApiKeyData() {
        try {
            const response = await fetch('/api/merchant/tools.php?action=get_key_info', {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                const keyInfo = data.data || data;
                this.updateMerchantApiKeyDisplay(keyInfo);
                await this.loadCallStats();
                await this.loadIpWhitelist();
            } else {
                console.error('加载API密钥信息失败:', data.message);
            }
        } catch (error) {
            console.error('加载API密钥信息失败:', error);
        }
    }

    updateMerchantApiKeyDisplay(keyInfo) {
        // 更新开发者ID
        const developerIdInput = document.getElementById('developerIdInput');
        if (developerIdInput) developerIdInput.value = keyInfo?.developer_id || '';

        // 更新创建时间
        const keyCreatedAtInput = document.getElementById('keyCreatedAtInput');
        if (keyCreatedAtInput) keyCreatedAtInput.value = keyInfo?.created_at || '';

        // 更新API密钥
        const apiKeyInput = document.getElementById('apiKeyInput');
        if (apiKeyInput) apiKeyInput.value = keyInfo?.api_key || '';



        // 更新回调地址
        const callbackUrl = document.getElementById('callbackUrl');
        if (callbackUrl) callbackUrl.value = keyInfo?.callback_url || '';
    }

    async loadCallStats() {
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=get_call_stats', {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                const stats = data.data || data;
                this.updateCallStatsDisplay(stats);
            }
        } catch (error) {
            console.error('加载调用统计失败:', error);
        }
    }

    updateCallStatsDisplay(stats) {
        const elements = {
            todayCallsCount: stats.today_calls || 0,
            monthCallsCount: stats.month_calls || 0,
            totalCallsCount: stats.total_calls || 0,
            successRateCount: (stats.success_rate || 0) + '%'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        });
    }

    async loadIpWhitelist() {
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=get_ip_whitelist', {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                const ipList = data.data || [];
                this.renderIpWhitelist(ipList);
            }
        } catch (error) {
            console.error('加载IP白名单失败:', error);
        }
    }

    renderIpWhitelist(ipList) {
        const container = document.getElementById('ipWhitelistContainer');
        if (!container) return;

        if (!ipList || ipList.length === 0) {
            container.innerHTML = `
                <div class="text-center py-3 text-muted">
                    <i class="bi bi-shield-slash me-2"></i>暂未设置IP白名单
                    <div class="small mt-1">建议设置IP白名单以提高安全性</div>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="ip-whitelist">
                ${ipList.map((ip, index) => `
                    <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                        <div>
                            <code>${ip.ip_address}</code>
                            <small class="text-muted ms-2">${ip.description || ''}</small>
                        </div>
                        <button class="btn btn-outline-danger btn-sm" onclick="window.apiModule.removeIpWhitelist(${index})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                `).join('')}
            </div>
        `;
    }

    async addIpWhitelist() {
        const ip = prompt('请输入要添加的IP地址:');
        if (!ip) return;

        const description = prompt('请输入描述信息 (可选):');

        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=add_ip_whitelist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify({ ip_address: ip, description: description })
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('IP地址添加成功');
                this.loadIpWhitelist();
            } else {
                this.showError('添加失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('添加IP白名单失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    async removeIpWhitelist(index) {
        if (!confirm('确定要删除这个IP地址吗？')) return;

        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=remove_ip_whitelist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify({ index: index })
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('IP地址删除成功');
                this.loadIpWhitelist();
            } else {
                this.showError('删除失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('删除IP白名单失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    async updateCallbackUrl() {
        const callbackUrl = document.getElementById('callbackUrl')?.value;
        if (!callbackUrl) {
            this.showError('请输入回调地址');
            return;
        }

        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=update_callback_url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify({ callback_url: callbackUrl })
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('回调地址更新成功');
            } else {
                this.showError('更新失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('更新回调地址失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    async testCallback() {
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=test_callback', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('回调测试成功，请检查您的服务器日志');
            } else {
                this.showError('回调测试失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('回调测试失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    viewCallbackLogs() {
        // 打开回调日志查看页面
        window.open('/admin-panel/callback-logs.html', '_blank');
    }

    async regenerateApiKey() {
        if (!confirm('重新生成API密钥将导致现有密钥失效，确定要继续吗？')) return;

        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=regenerate_key', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('API密钥重新生成成功');
                this.loadMerchantApiKeyData();
            } else {
                this.showError('重新生成失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('重新生成API密钥失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    downloadPostman() {
        // 下载Postman集合
        window.location.href = '/api/merchant/index.php?module=tools&action=download_postman';
    }

    openSignatureTool() {
        // 打开签名工具页面
        window.open('/admin-panel/signature-tool.html', '_blank');
    }

    openApiTester() {
        // 切换到接口测试选项卡
        const testingTab = document.querySelector('[data-bs-target="#testing"]');
        if (testingTab) {
            testingTab.click();
        }
    }

    async updatePermissions() {
        const permissions = {
            allow_payment: document.getElementById('allowPayment')?.checked || false,
            allow_query: document.getElementById('allowQuery')?.checked || false,
            allow_refund: document.getElementById('allowRefund')?.checked || false
        };

        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=update_permissions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                },
                body: JSON.stringify(permissions)
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('权限设置更新成功');
            } else {
                this.showError('更新失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('更新权限失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    /**
     * API接口测试功能
     */
    async sendApiTest() {
        const apiType = document.getElementById('testApiSelect')?.value;
        const method = document.getElementById('testMethodSelect')?.value || 'POST';
        const env = document.querySelector('input[name="testEnv"]:checked')?.value || 'sandbox';
        const headers = document.getElementById('testHeaders')?.value;
        const params = document.getElementById('testParams')?.value;

        if (!apiType) {
            this.showError('请选择要测试的接口');
            return;
        }

        const startTime = Date.now();
        
        try {
            // 显示加载状态
            document.getElementById('testResponse').textContent = '正在发送请求...';
            document.getElementById('responseHeaders').textContent = '等待响应...';

            const requestHeaders = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.authManager?.getToken()}`
            };

            // 合并自定义请求头
            if (headers) {
                try {
                    const customHeaders = JSON.parse(headers);
                    Object.assign(requestHeaders, customHeaders);
                } catch (e) {
                    console.warn('请求头格式错误，使用默认请求头');
                }
            }

            const response = await fetch(`/api/merchant/index.php?module=test&action=${apiType}&env=${env}`, {
                method: method,
                headers: requestHeaders,
                body: method === 'POST' ? params : undefined
            });

            const responseTime = Date.now() - startTime;
            const responseText = await response.text();
            
            // 更新响应状态
            const responseStatus = document.getElementById('responseStatus');
            const statusCode = document.getElementById('statusCode');
            const responseTimeElement = document.getElementById('responseTime');
            
            if (responseStatus) responseStatus.style.display = 'block';
            if (statusCode) {
                statusCode.textContent = response.status;
                statusCode.className = `badge ${response.ok ? 'bg-success' : 'bg-danger'}`;
            }
            if (responseTimeElement) responseTimeElement.textContent = responseTime + 'ms';

            // 更新响应内容
            try {
                const jsonResponse = JSON.parse(responseText);
                document.getElementById('testResponse').textContent = JSON.stringify(jsonResponse, null, 2);
            } catch (e) {
                document.getElementById('testResponse').textContent = responseText;
            }

            // 更新响应头
            const responseHeadersText = Array.from(response.headers.entries())
                .map(([key, value]) => `${key}: ${value}`)
                .join('\n');
            document.getElementById('responseHeaders').textContent = responseHeadersText;

            // 记录测试历史
            this.addTestHistory(apiType, method, response.status, responseTime);

        } catch (error) {
            const responseTime = Date.now() - startTime;
            document.getElementById('testResponse').textContent = '请求失败: ' + error.message;
            document.getElementById('responseHeaders').textContent = '请求失败';
            
            // 更新状态为错误
            const responseStatus = document.getElementById('responseStatus');
            const statusCode = document.getElementById('statusCode');
            const responseTimeElement = document.getElementById('responseTime');
            
            if (responseStatus) responseStatus.style.display = 'block';
            if (statusCode) {
                statusCode.textContent = 'ERROR';
                statusCode.className = 'badge bg-danger';
            }
            if (responseTimeElement) responseTimeElement.textContent = responseTime + 'ms';

            // 记录测试历史
            this.addTestHistory(apiType, method, 'ERROR', responseTime);
        }
    }

    addTestHistory(apiType, method, statusCode, responseTime) {
        const historyTable = document.getElementById('testHistoryTable');
        if (!historyTable) return;

        // 清空"暂无测试历史"行
        if (historyTable.children.length === 1 && historyTable.children[0].children.length === 1) {
            historyTable.innerHTML = '';
        }

        const row = document.createElement('tr');
        const now = new Date();
        const timeStr = now.toLocaleTimeString();
        
        row.innerHTML = `
            <td>${timeStr}</td>
            <td>${apiType}</td>
            <td><span class="badge bg-secondary">${method}</span></td>
            <td><span class="badge ${statusCode === 200 || statusCode === '200' ? 'bg-success' : 'bg-danger'}">${statusCode}</span></td>
            <td>${responseTime}ms</td>
            <td>
                <button class="btn btn-outline-primary btn-sm" onclick="window.apiModule.replayTest('${apiType}', '${method}')">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </td>
        `;
        
        historyTable.insertBefore(row, historyTable.firstChild);
        
        // 限制历史记录数量
        if (historyTable.children.length > 10) {
            historyTable.removeChild(historyTable.lastChild);
        }
    }

    loadTestTemplate() {
        const apiType = document.getElementById('testApiSelect')?.value;
        if (!apiType) {
            this.showError('请先选择接口');
            return;
        }

        const templates = {
            payment: {
                headers: '{"Content-Type": "application/json"}',
                params: '{\n  "amount": 100,\n  "order_id": "test_' + Date.now() + '",\n  "subject": "测试订单",\n  "notify_url": "https://your-domain.com/callback",\n  "return_url": "https://your-domain.com/return"\n}'
            },
            query: {
                headers: '{"Content-Type": "application/json"}',
                params: '{\n  "order_id": "test_123456789"\n}'
            },
            refund: {
                headers: '{"Content-Type": "application/json"}',
                params: '{\n  "order_id": "test_123456789",\n  "refund_amount": 50,\n  "refund_reason": "用户申请退款"\n}'
            },
            callback: {
                headers: '{"Content-Type": "application/json"}',
                params: '{\n  "test_data": "callback_test"\n}'
            }
        };

        const template = templates[apiType];
        if (template) {
            document.getElementById('testHeaders').value = template.headers;
            document.getElementById('testParams').value = template.params;
            this.showSuccess('模板加载成功');
        }
    }

    saveTestCase() {
        const apiType = document.getElementById('testApiSelect')?.value;
        const params = document.getElementById('testParams')?.value;
        
        if (!apiType || !params) {
            this.showError('请先配置测试参数');
            return;
        }

        const caseName = prompt('请输入测试用例名称:');
        if (!caseName) return;

        // 保存到本地存储
        const testCases = JSON.parse(localStorage.getItem('apiTestCases') || '{}');
        testCases[caseName] = {
            apiType: apiType,
            params: params,
            createdAt: new Date().toISOString()
        };
        localStorage.setItem('apiTestCases', JSON.stringify(testCases));
        
        this.showSuccess('测试用例保存成功');
    }

    copyResponse() {
        const response = document.getElementById('testResponse')?.textContent;
        if (response) {
            navigator.clipboard.writeText(response).then(() => {
                this.showSuccess('响应内容已复制到剪贴板');
            });
        }
    }

    clearResponse() {
        document.getElementById('testResponse').textContent = '等待测试结果...';
        document.getElementById('responseHeaders').textContent = '等待响应头信息...';
        document.getElementById('responseStatus').style.display = 'none';
    }

    clearTestHistory() {
        if (!confirm('确定要清空测试历史吗？')) return;
        
        const historyTable = document.getElementById('testHistoryTable');
        if (historyTable) {
            historyTable.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无测试历史</td></tr>';
        }
    }

    replayTest(apiType, method) {
        document.getElementById('testApiSelect').value = apiType;
        document.getElementById('testMethodSelect').value = method;
        this.loadTestTemplate();
    }

    /**
     * API文档功能
     */
    switchDocSection(section) {
        // 更新导航状态
        document.querySelectorAll('.list-group-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[onclick*="'${section}'"]`).classList.add('active');

        // 隐藏所有文档内容
        document.querySelectorAll('[id^="doc"]').forEach(div => {
            div.style.display = 'none';
        });

        // 显示选中的文档内容
        const targetDiv = document.getElementById(`doc${section.charAt(0).toUpperCase() + section.slice(1)}`);
        if (targetDiv) {
            targetDiv.style.display = 'block';
            
            // 如果内容为空，则动态加载
            if (targetDiv.innerHTML === '') {
                this.loadDocSection(section, targetDiv);
            }
        }
    }

    loadDocSection(section, container) {
        const content = this.getDocContent(section);
        container.innerHTML = content;
    }

    getDocContent(section) {
        const contents = {
            authentication: `
                <h4><i class="bi bi-shield-lock me-2"></i>认证方式</h4>
                <hr>
                <p>API调用需要进行身份验证，我们提供两种认证方式：</p>
                
                <h5>1. API密钥认证</h5>
                <p>在请求头中添加API密钥：</p>
                <pre class="bg-light p-3"><code>Authorization: Bearer YOUR_API_KEY</code></pre>
                
                <h5>2. 签名认证</h5>
                <p>使用MD5签名验证，确保请求的完整性和安全性。</p>
                <div class="alert alert-info">
                    <strong>签名规则：</strong>
                    <ol>
                        <li>将所有请求参数按字母顺序排序</li>
                        <li>拼接成 key1=value1&key2=value2 格式</li>
                        <li>在末尾添加 &key=YOUR_SECRET_KEY</li>
                        <li>对整个字符串进行MD5加密</li>
                    </ol>
                </div>
            `,
            apis: `
                <h4><i class="bi bi-code-square me-2"></i>API接口</h4>
                <hr>
                
                <div class="accordion" id="apiAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#paymentApi">
                                <i class="bi bi-credit-card me-2"></i>支付接口
                            </button>
                        </h2>
                        <div id="paymentApi" class="accordion-collapse collapse show">
                            <div class="accordion-body">
                                <p><strong>接口地址:</strong> <code>POST /api/payment/create</code></p>
                                <p><strong>功能说明:</strong> 创建支付订单</p>
                                
                                <h6>请求参数:</h6>
                                <table class="table table-bordered table-sm">
                                    <thead>
                                        <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                                    </thead>
                                    <tbody>
                                        <tr><td>amount</td><td>decimal</td><td>是</td><td>支付金额</td></tr>
                                        <tr><td>order_id</td><td>string</td><td>是</td><td>商户订单号</td></tr>
                                        <tr><td>subject</td><td>string</td><td>是</td><td>订单标题</td></tr>
                                        <tr><td>notify_url</td><td>string</td><td>否</td><td>异步通知地址</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#queryApi">
                                <i class="bi bi-search me-2"></i>查询接口
                            </button>
                        </h2>
                        <div id="queryApi" class="accordion-collapse collapse">
                            <div class="accordion-body">
                                <p><strong>接口地址:</strong> <code>POST /api/payment/query</code></p>
                                <p><strong>功能说明:</strong> 查询订单状态</p>
                                
                                <h6>请求参数:</h6>
                                <table class="table table-bordered table-sm">
                                    <thead>
                                        <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                                    </thead>
                                    <tbody>
                                        <tr><td>order_id</td><td>string</td><td>是</td><td>商户订单号</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            callback: `
                <h4><i class="bi bi-arrow-left-right me-2"></i>回调通知</h4>
                <hr>
                <p>当订单状态发生变化时，系统会向您配置的回调地址发送POST请求。</p>
                
                <h5>回调参数</h5>
                <table class="table table-bordered">
                    <thead>
                        <tr><th>参数名</th><th>类型</th><th>说明</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>order_id</td><td>string</td><td>商户订单号</td></tr>
                        <tr><td>trade_no</td><td>string</td><td>平台交易号</td></tr>
                        <tr><td>status</td><td>string</td><td>订单状态</td></tr>
                        <tr><td>amount</td><td>decimal</td><td>实际支付金额</td></tr>
                        <tr><td>sign</td><td>string</td><td>签名</td></tr>
                    </tbody>
                </table>
                
                <h5>响应要求</h5>
                <p>收到回调通知后，请返回字符串 <code>success</code> 表示处理成功。</p>
                
                <div class="alert alert-warning">
                    <strong>注意事项：</strong>
                    <ul class="mb-0">
                        <li>请验证回调通知的签名，确保数据安全</li>
                        <li>同一订单可能收到多次回调，请做好幂等处理</li>
                        <li>回调超时时间为30秒</li>
                    </ul>
                </div>
            `,
            errors: `
                <h4><i class="bi bi-exclamation-triangle me-2"></i>错误码</h4>
                <hr>
                
                <h5>通用错误码</h5>
                <table class="table table-bordered">
                    <thead>
                        <tr><th>错误码</th><th>说明</th><th>解决方案</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>1001</td><td>参数错误</td><td>检查必填参数是否完整</td></tr>
                        <tr><td>1002</td><td>签名错误</td><td>检查签名算法和密钥</td></tr>
                        <tr><td>1003</td><td>权限不足</td><td>检查API权限设置</td></tr>
                        <tr><td>1004</td><td>订单不存在</td><td>确认订单号是否正确</td></tr>
                        <tr><td>1005</td><td>订单状态错误</td><td>检查订单当前状态</td></tr>
                    </tbody>
                </table>
                
                <h5>支付相关错误码</h5>
                <table class="table table-bordered">
                    <thead>
                        <tr><th>错误码</th><th>说明</th><th>解决方案</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>2001</td><td>余额不足</td><td>请充值后重试</td></tr>
                        <tr><td>2002</td><td>支付渠道维护</td><td>请稍后重试或使用其他渠道</td></tr>
                        <tr><td>2003</td><td>单笔限额超限</td><td>减少支付金额</td></tr>
                        <tr><td>2004</td><td>日限额超限</td><td>明日再试或提高限额</td></tr>
                    </tbody>
                </table>
            `,
            examples: `
                <h4><i class="bi bi-file-earmark-code me-2"></i>示例代码</h4>
                <hr>
                
                <ul class="nav nav-pills mb-3" id="codeExampleTabs">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="pill" href="#phpExample">PHP</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="pill" href="#javaExample">Java</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="pill" href="#pythonExample">Python</a>
                    </li>
                </ul>
                
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="phpExample">
                        <h6>PHP 示例代码</h6>
                        <pre class="bg-light p-3"><code><?php
// 支付接口调用示例
$apiKey = 'YOUR_API_KEY';
$secretKey = 'YOUR_SECRET_KEY';

$params = [
    'amount' => 100.00,
    'order_id' => 'ORDER_' . time(),
    'subject' => '测试订单',
    'notify_url' => 'https://your-domain.com/callback'
];

// 生成签名
ksort($params);
$signStr = http_build_query($params) . '&key=' . $secretKey;
$params['sign'] = md5($signStr);

// 发送请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.paypal.com/api/payment/create');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $apiKey
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);

echo $response;
?></code></pre>
                    </div>
                    
                    <div class="tab-pane fade" id="javaExample">
                        <h6>Java 示例代码</h6>
                        <pre class="bg-light p-3"><code>// Java 示例代码
import java.net.http.*;
import java.util.*;

public class PayPalAPI {
    private static final String API_KEY = "YOUR_API_KEY";
    private static final String SECRET_KEY = "YOUR_SECRET_KEY";
    
    public static void main(String[] args) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("amount", 100.00);
        params.put("order_id", "ORDER_123");
        
        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create("https://api.paypal.com/api/payment/create"))
            .header("Content-Type", "application/json")
            .header("Authorization", "Bearer " + API_KEY)
            .POST(HttpRequest.BodyPublishers.ofString(toJson(params)))
            .build();
            
        HttpResponse<String> response = client.send(request, 
            HttpResponse.BodyHandlers.ofString());
            
        System.out.println(response.body());
    }
}</code></pre>
                    </div>
                    
                    <div class="tab-pane fade" id="pythonExample">
                        <h6>Python 示例代码</h6>
                        <pre class="bg-light p-3"><code># Python 示例代码
import requests
import hashlib
import json
import time

API_KEY = 'YOUR_API_KEY'
SECRET_KEY = 'YOUR_SECRET_KEY'

def generate_sign(params, secret_key):
    # 参数排序
    sorted_params = sorted(params.items())
    sign_str = '&'.join([f'{k}={v}' for k, v in sorted_params])
    sign_str += f'&key={secret_key}'
    return hashlib.md5(sign_str.encode()).hexdigest()

# 构建请求参数
params = {
    'amount': 100.00,
    'order_id': f'ORDER_{int(time.time())}',
    'subject': '测试订单',
    'notify_url': 'https://your-domain.com/callback'
}

# 生成签名
params['sign'] = generate_sign(params, SECRET_KEY)

# 发送请求
headers = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {API_KEY}'
}

response = requests.post(
    'https://api.paypal.com/api/payment/create',
    headers=headers,
    data=json.dumps(params)
)

print(response.json())</code></pre>
                    </div>
                </div>
            `
        };
        
        return contents[section] || '<p>内容加载中...</p>';
    }

    downloadApiDocs() {
        // 下载API文档
        window.location.href = '/api/merchant/index.php?module=docs&action=download';
    }

    /**
     * 通用工具方法
     */
    showSuccess(message) {
        // 显示成功消息
        this.showToast(message, 'success');
    }

    showError(message) {
        // 显示错误消息
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info') {
        // 创建toast通知
        const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0`;
        toast.setAttribute('role', 'alert');
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        // 显示toast
        const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
        bsToast.show();
        
        // 自动移除
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    }
}

// 全局注册
window.ApiModule = ApiModule;
window.apiModule = new ApiModule();

console.log('✅ ApiModule 已注册到全局');
