<?php
/**
 * TOTP管理模块
 */

// 用户认证和权限检查已在common.php中处理

// 获取操作类型
$action_param = isset($_GET['action']) ? $_GET['action'] : '';

// 处理不同的操作
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        if ($action_param === 'list') {
            handleGetTOTPList();
        } elseif ($action_param === 'status') {
            handleGetTOTPStatus();
        } else {
            handleGetTOTPList();
        }
        break;
    case 'POST':
        handleCreateTOTP();
        break;
    case 'PUT':
        handleUpdateTOTP();
        break;
    case 'DELETE':
        handleDeleteTOTP();
        break;
    default:
        http_response_code(405);
        echo json_encode(array('code' => 405, 'message' => '不支持的请求方法'));
        break;
}

// 获取TOTP列表
function handleGetTOTPList() {
    global $db, $currentUser;
    
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    $offset = ($page - 1) * $limit;
    
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $status = isset($_GET['status']) ? trim($_GET['status']) : '';
    
    try {
        // 构建查询条件
        $whereConditions = array();
        $params = array();
        
        // 权限控制：merchant用户只能查看自己的TOTP
        if ($currentUser['user_type'] === 'merchant') {
            $whereConditions[] = 'ut.user_id = ?';
            $params[] = $currentUser['id'];
        }
        
        if (!empty($search)) {
            $whereConditions[] = '(u.username LIKE ? OR u.email LIKE ?)';
            $params[] = '%' . $search . '%';
            $params[] = '%' . $search . '%';
        }
        
        if (!empty($status)) {
            $whereConditions[] = 'ut.status = ?';
            $params[] = $status;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // 获取TOTP列表
        $totpList = $db->fetchAll(
            "SELECT ut.*, u.username, u.email, u.real_name
             FROM user_totp ut
             LEFT JOIN users u ON ut.user_id = u.id
             $whereClause
             ORDER BY ut.created_at DESC
             LIMIT $limit OFFSET $offset",
            array_merge($params, array($limit, $offset))
        );
        
        // 获取总数
        $totalResult = $db->fetch(
            "SELECT COUNT(*) as count FROM user_totp ut LEFT JOIN users u ON ut.user_id = u.id $whereClause",
            $params
        );
        $total = $totalResult ? intval($totalResult['count']) : 0;
        
        // 获取统计信息
        $stats = $db->fetch(
            "SELECT 
                COUNT(*) as total_totp,
                SUM(CASE WHEN ut.status = 'active' THEN 1 ELSE 0 END) as active_totp,
                SUM(CASE WHEN ut.status = 'inactive' THEN 1 ELSE 0 END) as inactive_totp,
                COUNT(DISTINCT ut.user_id) as users_with_totp
             FROM user_totp ut
             LEFT JOIN users u ON ut.user_id = u.id
             $whereClause",
            $params
        );
        
        if (!$stats) {
            $stats = array(
                'total_totp' => 0,
                'active_totp' => 0,
                'inactive_totp' => 0,
                'users_with_totp' => 0
            );
        }
        
        echo json_encode(array(
            'code' => 200,
            'message' => '获取成功',
            'data' => array(
                'totp_list' => $totpList ? $totpList : array(),
                'stats' => $stats,
                'pagination' => array(
                    'current_page' => $page,
                    'total_pages' => ceil($total / $limit),
                    'total_records' => $total,
                    'per_page' => $limit
                )
            )
        ));
        
    } catch (Exception $e) {
        error_log('获取TOTP列表失败: ' . $e->getMessage());
        echo json_encode(array(
            'code' => 200,
            'message' => '获取成功',
            'data' => array(
                'totp_list' => array(),
                'stats' => array(
                    'total_totp' => 0,
                    'active_totp' => 0,
                    'inactive_totp' => 0,
                    'users_with_totp' => 0
                ),
                'pagination' => array(
                    'current_page' => 1,
                    'total_pages' => 0,
                    'total_records' => 0,
                    'per_page' => $limit
                )
            )
        ));
    }
}

// 获取TOTP状态
function handleGetTOTPStatus() {
    global $db, $currentUser;
    
    try {
        $user_id = ($currentUser['user_type'] === 'merchant') ? $currentUser['id'] : (isset($_GET['user_id']) ? intval($_GET['user_id']) : 0);
        
        if (!$user_id) {
            echo json_encode(array(
                'code' => 200,
                'message' => '获取成功',
                'data' => array(
                    'has_totp' => false,
                    'status' => 'inactive',
                    'created_at' => null
                )
            ));
            return;
        }
        
        $totp = $db->fetch(
            "SELECT * FROM user_totp WHERE user_id = ? ORDER BY created_at DESC LIMIT 1",
            array($user_id)
        );
        
        echo json_encode(array(
            'code' => 200,
            'message' => '获取成功',
            'data' => array(
                'has_totp' => $totp ? true : false,
                'status' => $totp ? $totp['status'] : 'inactive',
                'created_at' => $totp ? $totp['created_at'] : null
            )
        ));
        
    } catch (Exception $e) {
        error_log('获取TOTP状态失败: ' . $e->getMessage());
        echo json_encode(array(
            'code' => 200,
            'message' => '获取成功',
            'data' => array(
                'has_totp' => false,
                'status' => 'inactive',
                'created_at' => null
            )
        ));
    }
}

// 创建TOTP
function handleCreateTOTP() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $user_id = ($currentUser['user_type'] === 'merchant') ? $currentUser['id'] : (isset($input['user_id']) ? intval($input['user_id']) : 0);
    
    if (!$user_id) {
        http_response_code(400);
        echo json_encode(array('code' => 400, 'message' => '缺少用户ID'));
        return;
    }
    
    try {
        // 生成TOTP密钥
        $secret = generateTOTPSecret();
        
        // 检查用户是否已有TOTP
        $existing = $db->fetch(
            "SELECT id FROM user_totp WHERE user_id = ?",
            array($user_id)
        );
        
        if ($existing) {
            // 更新现有TOTP
            $result = $db->execute(
                "UPDATE user_totp SET secret_key = ?, status = 'active', updated_at = NOW() WHERE user_id = ?",
                array($secret, $user_id)
            );
        } else {
            // 创建新TOTP
            $result = $db->execute(
                "INSERT INTO user_totp (user_id, secret_key, status, created_at, updated_at) VALUES (?, ?, 'active', NOW(), NOW())",
                array($user_id, $secret)
            );
        }
        
        if ($result) {
            echo json_encode(array(
                'code' => 200,
                'message' => 'TOTP设置成功',
                'data' => array('secret' => $secret)
            ));
        } else {
            http_response_code(500);
            echo json_encode(array('code' => 500, 'message' => '设置TOTP失败'));
        }
        
    } catch (Exception $e) {
        error_log('创建TOTP失败: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(array('code' => 500, 'message' => '创建TOTP失败: ' . $e->getMessage()));
    }
}

// 更新TOTP状态
function handleUpdateTOTP() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        parse_str(file_get_contents('php://input'), $input);
    }
    
    $user_id = ($currentUser['user_type'] === 'merchant') ? $currentUser['id'] : (isset($input['user_id']) ? intval($input['user_id']) : 0);
    $status = isset($input['status']) ? trim($input['status']) : '';
    
    if (!$user_id || !$status) {
        http_response_code(400);
        echo json_encode(array('code' => 400, 'message' => '缺少必要参数'));
        return;
    }
    
    try {
        $result = $db->execute(
            "UPDATE user_totp SET status = ?, updated_at = NOW() WHERE user_id = ?",
            array($status, $user_id)
        );
        
        if ($result) {
            echo json_encode(array('code' => 200, 'message' => 'TOTP状态更新成功'));
        } else {
            http_response_code(500);
            echo json_encode(array('code' => 500, 'message' => '更新TOTP状态失败'));
        }
        
    } catch (Exception $e) {
        error_log('更新TOTP失败: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(array('code' => 500, 'message' => '更新TOTP失败: ' . $e->getMessage()));
    }
}

// 删除TOTP
function handleDeleteTOTP() {
    global $db, $currentUser;
    
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
        $input = array('user_id' => $user_id);
    }
    
    $user_id = ($currentUser['user_type'] === 'merchant') ? $currentUser['id'] : (isset($input['user_id']) ? intval($input['user_id']) : 0);
    
    if (!$user_id) {
        http_response_code(400);
        echo json_encode(array('code' => 400, 'message' => '缺少用户ID'));
        return;
    }
    
    try {
        $result = $db->execute(
            "DELETE FROM user_totp WHERE user_id = ?",
            array($user_id)
        );
        
        if ($result) {
            echo json_encode(array('code' => 200, 'message' => 'TOTP删除成功'));
        } else {
            http_response_code(500);
            echo json_encode(array('code' => 500, 'message' => '删除TOTP失败'));
        }
        
    } catch (Exception $e) {
        error_log('删除TOTP失败: ' . $e->getMessage());
        http_response_code(500);
        echo json_encode(array('code' => 500, 'message' => '删除TOTP失败: ' . $e->getMessage()));
    }
}

// 生成TOTP密钥
function generateTOTPSecret($length = 32) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $secret = '';
    for ($i = 0; $i < $length; $i++) {
        $secret .= $chars[rand(0, strlen($chars) - 1)];
    }
    return $secret;
}
?> 