# 开发环境缓存控制
<IfModule mod_expires.c>
    ExpiresActive Off
</IfModule>

<IfModule mod_headers.c>
    # 禁用所有静态资源的缓存
    <FilesMatch "\.(js|css|html|php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
    
    # 为JavaScript文件添加版本号支持
    <FilesMatch "\.js$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
    </FilesMatch>
</IfModule>

# 强制重新加载
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # 为开发环境添加时间戳
    RewriteCond %{QUERY_STRING} !v=
    RewriteCond %{REQUEST_URI} \.(js|css)$
    RewriteRule ^(.*)$ $1?v=%{TIME} [L]
</IfModule> 