/**
 * 用户管理模块 - 四层架构版本
 * 负责用户CRUD操作、权限管理、员工管理等核心用户业务功能
 * 支持多租户环境下的数据隔离和权限控制
 * 
 * <AUTHOR> Team
 * @version 2.0.0 - 四层架构改造版本
 * @since 2024-12-17
 */

class UserManager {
    constructor() {
        this.currentUsers = [];
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalUsers = 0;
        this.searchQuery = '';
        this.filterStatus = 'all';
        this.filterUserType = 'all';
        
        // 依赖由 app-initializer.js 注入
        this.apiClient = null;
        this.utils = null;
        this.authManager = null;  // 新增：认证管理器
        this.tenantInfo = null;   // 新增：租户信息

        console.log('UserManager constructed. Dependencies will be injected.');
    }

    /**
     * 初始化函数，由 app-initializer 调用
     * @param {object} dependencies - 依赖项
     */
    init(dependencies) {
        this.apiClient = dependencies.apiClient;
        this.utils = dependencies.utils;
        this.authManager = dependencies.authManager;
        this.tenantInfo = dependencies.tenantInfo || window.TENANT_INFO;

        console.log('✅ 用户管理模块初始化完成', {
            tenantType: this.tenantInfo?.tenant_type,
            userType: this.authManager?.getUserType()
        });
    }

    /**
     * 方案5配置驱动 - 渲染方法
     * @param {HTMLElement} container 容器元素
     * @param {Object} params 参数对象
     */
    async render(container, params = {}) {
        try {
            console.log('🎯 UserManager.render 被调用:', params);
            
            // 调用现有的页面加载方法
            this.loadUserManagementPage(container);
            
            console.log('✅ 用户管理页面渲染完成');
        } catch (error) {
            console.error('❌ 用户管理页面渲染失败:', error);
            container.innerHTML = `
                <div class="alert alert-danger">
                    <h4>加载失败</h4>
                    <p>用户管理页面加载失败: ${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            `;
        }
    }

    /**
     * 加载用户管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadUserManagementPage(container) {
        // 检查权限
        if (!this.canManageUsers()) {
            container.innerHTML = this.generateNoPermissionHTML('用户管理');
            return;
        }

        container.innerHTML = this.generateUserManagementHTML();
        this.initializeUserManagementEvents();
        this.loadUserList();
    }

    /**
     * 加载员工管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadEmployeesPage(container) {
        // 检查权限
        if (!this.canManageEmployees()) {
            container.innerHTML = this.generateNoPermissionHTML('员工管理');
            return;
        }

        // 复用用户管理页面，但筛选出"员工"类型
        this.loadUserManagementPage(container);
        
        // 延迟执行以确保UI元素已渲染
        setTimeout(() => {
            const userTypeFilter = document.getElementById('userTypeFilter');
            if (userTypeFilter) {
                userTypeFilter.value = 'employee'; // 筛选员工
                this.handleSearch();
            }
        }, 100);
    }

    /**
     * 加载职位管理页面
     * @param {HTMLElement} container 容器元素
     */
    loadJobPositionsPage(container) {
        // 检查权限
        if (!this.canManageJobPositions()) {
            container.innerHTML = this.generateNoPermissionHTML('职位管理');
            return;
        }

        // 复用用户管理页面，可以根据需要进行筛选
        // 目前暂时显示所有用户，可以后续扩展
        this.loadUserManagementPage(container);

        setTimeout(() => {
            this.utils.showMessage('职位管理功能正在开发中，当前显示所有用户。', 'info');
        }, 100);
    }

    /**
     * 检查是否可以管理用户
     * @returns {boolean} 是否有权限
     */
    canManageUsers() {
        if (!this.tenantInfo || !this.authManager) return false;

        const tenantType = this.tenantInfo.tenant_type;
        const userType = this.authManager.getUserType();

        // 权限矩阵
        const permissions = {
            'system_admin': ['admin'],
            'platform_admin': ['admin'],
            'provider': ['provider', 'admin'],
            'merchant': ['merchant', 'admin']
        };

        return permissions[tenantType]?.includes(userType) || false;
    }

    /**
     * 检查是否可以管理员工
     * @returns {boolean} 是否有权限
     */
    canManageEmployees() {
        // 所有租户类型都可以管理自己的员工
        return this.canManageUsers();
    }

    /**
     * 检查是否可以管理职位
     * @returns {boolean} 是否有权限
     */
    canManageJobPositions() {
        // 所有租户类型都可以管理自己的职位
        return this.canManageUsers();
    }

    /**
     * 生成无权限访问页面
     * @param {string} moduleName 模块名称
     * @returns {string} HTML字符串
     */
    generateNoPermissionHTML(moduleName) {
        return `
            <div class="text-center py-5">
                <i class="bi bi-shield-exclamation text-warning" style="font-size: 4rem;"></i>
                <h3 class="mt-3">权限不足</h3>
                <p class="text-muted">您没有权限访问${moduleName}功能</p>
                <div class="mt-3">
                    <p class="text-muted"><strong>当前租户类型：</strong>${this.getTenantTypeText()}</p>
                    <p class="text-muted"><strong>当前用户类型：</strong>${this.getUserTypeText()}</p>
                </div>
                <div class="mt-4">
                    <button class="btn btn-primary" onclick="window.uiManager.navigateToPage('dashboard')">
                        <i class="bi bi-house me-2"></i>返回首页
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 获取租户类型文本
     * @returns {string} 租户类型文本
     */
    getTenantTypeText() {
        if (!this.tenantInfo) return '未知';
        
        const typeMap = {
            'system_admin': '系统管理员',
            'platform_admin': '平台管理员',
            'provider': '码商',
            'merchant': '商户'
        };
        return typeMap[this.tenantInfo.tenant_type] || '未知';
    }

    /**
     * 获取用户类型文本
     * @returns {string} 用户类型文本
     */
    getUserTypeText() {
        if (!this.authManager) return '未知';
        
        const userType = this.authManager.getUserType();
        const typeMap = {
            'admin': '管理员',
            'provider': '码商',
            'merchant': '商户',
            'employee': '员工'
        };
        return typeMap[userType] || '未知';
    }

    /**
     * 生成用户管理页面HTML
     * @returns {string} HTML字符串
     */
    generateUserManagementHTML() {
        return `
            <div class="user-management">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><i class="bi bi-people me-2"></i>用户管理</h2>
                            <p class="text-muted mb-0">管理系统用户、权限分配和账户状态</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" id="addUserBtn">
                                <i class="bi bi-person-plus me-2"></i>添加用户
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="exportUsersBtn">
                                <i class="bi bi-download me-2"></i>导出数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalUsersCount">-</div>
                                <div class="stat-label">总用户数</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-person-check"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="activeUsersCount">-</div>
                                <div class="stat-label">活跃用户</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-person-dash"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="inactiveUsersCount">-</div>
                                <div class="stat-label">停用用户</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="adminUsersCount">-</div>
                                <div class="stat-label">管理员</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">搜索用户</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="userSearchInput" 
                                           placeholder="用户名、邮箱、手机号...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">用户状态</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="all">全部状态</option>
                                    <option value="active">活跃</option>
                                    <option value="inactive">停用</option>
                                    <option value="pending">待审核</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">用户类型</label>
                                <select class="form-select" id="userTypeFilter">
                                    <option value="all">全部类型</option>
                                    <option value="admin">管理员</option>
                                    <option value="provider">码商</option>
                                    <option value="merchant">商户</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">注册时间</label>
                                <input type="date" class="form-control" id="dateFilter">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-outline-primary" id="searchBtn">
                                        <i class="bi bi-funnel me-1"></i>筛选
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">用户列表</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-secondary" id="refreshUsersBtn">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                        type="button" data-bs-toggle="dropdown">
                                    批量操作
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" id="batchActivateBtn">
                                        <i class="bi bi-check-circle me-2"></i>批量激活
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" id="batchDeactivateBtn">
                                        <i class="bi bi-x-circle me-2"></i>批量停用
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" id="batchDeleteBtn">
                                        <i class="bi bi-trash me-2"></i>批量删除
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" class="form-check-input" id="selectAllUsers">
                                        </th>
                                        <th>用户信息</th>
                                        <th>用户类型</th>
                                        <th>状态</th>
                                        <th>注册时间</th>
                                        <th>最后登录</th>
                                        <th width="120">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="userTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <div class="mt-2">正在加载用户数据...</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                显示第 <span id="currentPageInfo">1-20</span> 条，共 <span id="totalUsersInfo">0</span> 条记录
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="userPagination">
                                    <!-- 分页将动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 添加/编辑用户模态框 -->
            <div class="modal fade" id="userModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="userModalTitle">添加用户</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="userForm">
                                <input type="hidden" id="userId">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">用户名 *</label>
                                        <input type="text" class="form-control" id="username" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">邮箱 *</label>
                                        <input type="email" class="form-control" id="email" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">手机号</label>
                                        <input type="tel" class="form-control" id="phone">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">真实姓名</label>
                                        <input type="text" class="form-control" id="realName">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">用户类型 *</label>
                                        <select class="form-select" id="userType" required>
                                            <option value="">请选择用户类型</option>
                                            <option value="admin">管理员</option>
                                            <option value="provider">码商</option>
                                            <option value="merchant">商户</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">状态</label>
                                        <select class="form-select" id="status">
                                            <option value="active">活跃</option>
                                            <option value="inactive">停用</option>
                                            <option value="pending">待审核</option>
                                        </select>
                                    </div>
                                    <div class="col-12" id="passwordSection">
                                        <label class="form-label">密码 *</label>
                                        <input type="password" class="form-control" id="password">
                                        <div class="form-text">密码长度至少6位，包含字母和数字</div>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">备注</label>
                                        <textarea class="form-control" id="remarks" rows="3"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="saveUserBtn">保存</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户详情模态框 -->
            <div class="modal fade" id="userDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">用户详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="userDetailContent">
                            <!-- 用户详情内容将动态生成 -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>

            ${this.generateUserManagementStyles()}
        `;
    }

    /**
     * 生成用户管理样式
     * @returns {string} CSS样式
     */
    generateUserManagementStyles() {
        return `
            <style>
                .user-management .stat-card {
                    background: white;
                    border-radius: 12px;
                    padding: 20px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }

                .user-management .stat-card:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                }

                .user-management .stat-icon {
                    width: 50px;
                    height: 50px;
                    border-radius: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .user-management .stat-content {
                    flex: 1;
                }

                .user-management .stat-number {
                    font-size: 1.8rem;
                    font-weight: 700;
                    color: #1f2937;
                    margin-bottom: 4px;
                }

                .user-management .stat-label {
                    color: #6b7280;
                    font-size: 14px;
                }

                .user-management .user-avatar {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: 600;
                    margin-right: 12px;
                }

                .user-management .user-info h6 {
                    margin: 0;
                    font-weight: 600;
                    color: #1f2937;
                }

                .user-management .user-info small {
                    color: #6b7280;
                }

                .user-management .status-badge {
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .user-management .status-active {
                    background: #dcfce7;
                    color: #166534;
                }

                .user-management .status-inactive {
                    background: #fef3c7;
                    color: #92400e;
                }

                .user-management .status-pending {
                    background: #dbeafe;
                    color: #1e40af;
                }

                .user-management .type-badge {
                    padding: 4px 8px;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: 500;
                }

                .user-management .type-admin {
                    background: #fef2f2;
                    color: #dc2626;
                }

                .user-management .type-provider {
                    background: #f0f9ff;
                    color: #0284c7;
                }

                .user-management .type-merchant {
                    background: #f7fee7;
                    color: #65a30d;
                }

                .user-management .table th {
                    font-weight: 600;
                    color: #374151;
                    border-bottom: 2px solid #e5e7eb;
                }

                .user-management .table td {
                    vertical-align: middle;
                    border-bottom: 1px solid #f3f4f6;
                }

                .user-management .btn-action {
                    padding: 4px 8px;
                    font-size: 12px;
                    border-radius: 6px;
                    margin: 0 2px;
                }

                .user-management .pagination-sm .page-link {
                    padding: 6px 12px;
                    font-size: 14px;
                }

                .user-management .form-label {
                    font-weight: 600;
                    color: #374151;
                    margin-bottom: 6px;
                }

                .user-management .form-control:focus,
                .user-management .form-select:focus {
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
            </style>
        `;
    }

    /**
     * 初始化用户管理事件
     */
    initializeUserManagementEvents() {
        // 使用箭头函数确保 this 上下文正确
        document.getElementById('addUserBtn').addEventListener('click', () => this.showUserModal());
        document.getElementById('exportUsersBtn').addEventListener('click', () => this.exportUsers());
        document.getElementById('searchBtn').addEventListener('click', () => this.handleSearch());
        document.getElementById('refreshUsersBtn').addEventListener('click', () => this.loadUserList(this.currentPage));

        // 批量操作按钮
        document.getElementById('batchActivateBtn').addEventListener('click', () => this.handleBatchOperation('activate'));
        document.getElementById('batchDeactivateBtn').addEventListener('click', () => this.handleBatchOperation('deactivate'));
        document.getElementById('batchDeleteBtn').addEventListener('click', () => this.handleBatchOperation('delete'));

        // 全选复选框
        const selectAllCheckbox = document.getElementById('selectAllUsers');
        if(selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => this.handleSelectAll(e.target.checked));
        }
    }

    /**
     * 加载用户列表
     * @param {number} page 页码
     */
    async loadUserList(page = 1) {
        this.currentPage = page;
        this.showTableLoading();

        try {
            // 构建基础参数
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search: this.searchQuery,
                status: this.filterStatus,
                user_type: this.filterUserType
            });

            // 添加租户上下文参数
            if (this.tenantInfo) {
                params.append('tenant_type', this.tenantInfo.tenant_type);
                if (this.tenantInfo.platform_id) {
                    params.append('platform_id', this.tenantInfo.platform_id);
                }
                if (this.tenantInfo.provider_id) {
                    params.append('provider_id', this.tenantInfo.provider_id);
                }
                if (this.tenantInfo.merchant_id) {
                    params.append('merchant_id', this.tenantInfo.merchant_id);
                }
            }

            const response = await this.apiClient.get(`/admin.php?action=get_users&${params.toString()}`);
            
            if (response.data.code == 200) {
                this.currentUsers = response.data.data.users;
                this.totalUsers = response.data.data.pagination.total_records;
                
                this.renderUserTable();
                this.renderPagination();
                this.updateUserStats(response.data.data.stats);
            } else {
                throw new Error(response.data.message || '加载用户列表失败');
            }
        } catch (error) {
            console.error('加载用户列表失败:', error);
            this.showTableError(error.message);
            this.utils.showMessage('加载用户列表失败: ' + error.message, 'error');
        }
    }

    /**
     * 渲染用户表格
     */
    renderUserTable() {
        const tbody = document.getElementById('userTableBody');
        if (!tbody) return;

        if (this.currentUsers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="bi bi-inbox" style="font-size: 2rem; color: #6b7280;"></i>
                        <div class="mt-2 text-muted">暂无用户数据</div>
                    </td>
                </tr>
            `;
            return;
        }
        
        try {
            tbody.innerHTML = this.currentUsers.map(user => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input user-checkbox" value="${user.id}">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="user-avatar">
                            ${this.utils.getInitials(user.real_name || user.username)}
                        </div>
                        <div class="user-info">
                            <h6>${user.real_name || user.username}</h6>
                            <small>${user.email}</small>
                            ${user.phone ? `<br><small>${user.phone}</small>` : ''}
                        </div>
                    </div>
                </td>
                <td>
                    <span class="type-badge type-${user.user_type}">
                        ${this.getUserTypeText(user.user_type)}
                    </span>
                </td>
                <td>
                    <span class="status-badge status-${user.status}">
                        ${this.getStatusText(user.status)}
                    </span>
                </td>
                <td>
                    <small>${this.utils.formatDate(user.created_at)}</small>
                </td>
                <td>
                    <small>${user.last_login ? this.utils.formatDate(user.last_login) : '从未登录'}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-action" 
                                onclick="userManager.showUserDetail(${user.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-action" 
                                onclick="userManager.showUserModal(${user.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-action" 
                                onclick="userManager.deleteUser(${user.id})" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
        } catch (error) {
            console.error('渲染用户表格时出错:', error);
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-4">
                        <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                        <div class="mt-2 text-danger">渲染用户表格时出错: ${error.message}</div>
                    </td>
                </tr>
            `;
        }
    }

    /**
     * 显示表格加载状态
     */
    showTableLoading() {
        const tbody = document.getElementById('userTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2">正在加载用户数据...</div>
                </td>
            </tr>
        `;
    }

    /**
     * 显示表格错误状态
     * @param {string} message 错误信息
     */
    showTableError(message) {
        const tbody = document.getElementById('userTableBody');
        if (!tbody) return;

        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                    <div class="mt-2 text-danger">${message}</div>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="userManager.loadUserList()">
                        重新加载
                    </button>
                </td>
            </tr>
        `;
    }

    /**
     * 获取用户类型文本
     * @param {string} type 用户类型
     * @returns {string} 类型文本
     */
    getUserTypeText(type) {
        const typeMap = {
            'admin': '管理员',
            'provider': '码商',
            'merchant': '商户'
        };
        return typeMap[type] || type;
    }

    /**
     * 获取状态文本
     * @param {string} status 状态
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'active': '活跃',
            'inactive': '停用',
            'pending': '待审核'
        };
        return statusMap[status] || status;
    }

    /**
     * 更新用户统计数据
     * @param {Object} stats 统计数据（可选，如果不传则从API获取）
     */
    async updateUserStats(stats = null) {
        try {
            if (!stats) {
                const response = await this.apiClient.get('/admin.php?action=get_stats');
                
                if (response.data.code === 200) {
                    stats = response.data.data;
                } else {
                    console.error('获取统计数据失败:', response.data.message);
                    return;
                }
            }
            
            // 更新统计显示
            document.getElementById('totalUsersCount').textContent = stats.total_users || 0;
            document.getElementById('activeUsersCount').textContent = stats.active_users || 0;
            document.getElementById('inactiveUsersCount').textContent = (stats.total_users - stats.active_users) || 0;
            document.getElementById('adminUsersCount').textContent = stats.admin_users || 0;
        } catch (error) {
            console.error('加载用户统计失败:', error);
        }
    }

    /**
     * 显示用户模态框
     * @param {number} userId 用户ID（编辑时）
     */
    async showUserModal(userId = null) {
        const modal = new bootstrap.Modal(document.getElementById('userModal'));
        const title = document.getElementById('userModalTitle');
        const form = document.getElementById('userForm');
        const passwordSection = document.getElementById('passwordSection');

        // 重置表单
        form.reset();

        if (userId) {
            // 编辑模式
            title.textContent = '编辑用户';
            passwordSection.style.display = 'none';
            
            try {
                const response = await this.apiClient.get(`/admin.php?action=get_user&user_id=${userId}`);
                if (response.data.code === 200) {
                    const user = response.data.data;
                    document.getElementById('userId').value = user.id;
                    document.getElementById('username').value = user.username;
                    document.getElementById('email').value = user.email;
                    document.getElementById('phone').value = user.phone || '';
                    document.getElementById('realName').value = user.real_name || '';
                    document.getElementById('userType').value = user.user_type;
                    document.getElementById('status').value = user.status;
                    document.getElementById('remarks').value = user.remarks || '';
                }
            } catch (error) {
                console.error('加载用户信息失败:', error);
                this.utils.showMessage('加载用户信息失败', 'error');
                return;
            }
        } else {
            // 添加模式
            title.textContent = '添加用户';
            passwordSection.style.display = 'block';
            document.getElementById('password').required = true;
        }

        modal.show();
    }

    /**
     * 处理保存用户
     */
    async handleSaveUser() {
        const userId = document.getElementById('userId').value;
        const data = {
            username: document.getElementById('username').value,
            email: document.getElementById('email').value,
            phone: document.getElementById('phone').value,
            real_name: document.getElementById('realName').value,
            user_type: document.getElementById('userType').value,
            status: document.getElementById('status').value,
            remarks: document.getElementById('remarks').value,
            password: document.getElementById('password').value
        };

        // 验证...
        if (!data.username || !data.email) {
            this.utils.showMessage('用户名和邮箱不能为空', 'error');
            return;
        }
        
        try {
            let response;
            if (userId) {
                response = await this.apiClient.post(`/admin.php?action=edit_user`, { ...data, user_id: userId });
            } else {
                response = await this.apiClient.post('/admin.php?action=add_user', data);
            }
            
            if (response.data.code === 200) {
                this.utils.showMessage('用户保存成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
                this.loadUserList(userId ? this.currentPage : 1);
            } else {
                throw new Error(response.data.message || '保存失败');
            }
        } catch (error) {
            console.error('保存用户失败:', error);
            this.utils.showMessage(error.message || '保存用户失败', 'error');
        }
    }

    /**
     * 处理搜索
     */
    handleSearch() {
        this.searchQuery = document.getElementById('userSearchInput').value.trim();
        this.filterStatus = document.getElementById('statusFilter').value;
        this.filterUserType = document.getElementById('userTypeFilter').value;
        
        this.currentPage = 1;
        this.loadUserList(1);
    }

    /**
     * 处理全选
     * @param {boolean} checked 是否选中
     */
    handleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
    }

    /**
     * 获取选中的用户ID
     * @returns {Array} 用户ID数组
     */
    getSelectedUserIds() {
        const checkboxes = document.querySelectorAll('.user-checkbox:checked');
        return Array.from(checkboxes).map(cb => parseInt(cb.value));
    }

    /**
     * 处理批量操作
     * @param {string} action 操作类型
     */
    async handleBatchOperation(action) {
        const selectedIds = this.getSelectedUserIds();
        
        if (selectedIds.length === 0) {
            this.utils.showMessage('请选择要操作的用户', 'warning');
            return;
        }

        const actionText = {
            'activate': '激活',
            'deactivate': '停用',
            'delete': '删除'
        }[action];

        if (!confirm(`确定要批量${actionText}选中的 ${selectedIds.length} 个用户吗？`)) {
            return;
        }

        try {
            const response = await this.apiClient.post('/admin.php?action=batch_operation', {
                operation: action,
                user_ids: selectedIds
            });

            if (response.data.code === 200) {
                this.utils.showMessage(`批量${actionText}成功`, 'success');
                this.loadUserList(this.currentPage);
                document.getElementById('selectAllUsers').checked = false;
            } else {
                throw new Error(response.data.message || `批量${actionText}失败`);
            }
        } catch (error) {
            console.error(`批量${actionText}失败:`, error);
            this.utils.showMessage(error.message || `批量${actionText}失败`, 'error');
        }
    }

    /**
     * 删除用户
     * @param {number} userId 用户ID
     */
    async deleteUser(userId) {
        if (!confirm('确定要删除这个用户吗？此操作不可恢复！')) {
            return;
        }

        try {
            const response = await this.apiClient.post(`/admin.php?action=delete_user`, { user_id: userId });
            
            if (response.data.code === 200) {
                this.utils.showMessage('用户删除成功', 'success');
                this.loadUserList(this.currentPage);
            } else {
                throw new Error(response.data.message || '删除失败');
            }
        } catch (error) {
            console.error('删除用户失败:', error);
            this.utils.showMessage(error.message || '删除用户失败', 'error');
        }
    }

    /**
     * 显示用户详情
     * @param {number} userId 用户ID
     */
    async showUserDetail(userId) {
        try {
            const response = await this.apiClient.get(`/admin.php?action=get_user_detail&user_id=${userId}`);
            
            if (response.data.code === 200) {
                const user = response.data.data;
                const modal = new bootstrap.Modal(document.getElementById('userDetailModal'));
                const content = document.getElementById('userDetailContent');
                
                content.innerHTML = this.generateUserDetailHTML(user);
                modal.show();
            } else {
                throw new Error(response.data.message || '加载用户详情失败');
            }
        } catch (error) {
            console.error('加载用户详情失败:', error);
            this.utils.showMessage('加载用户详情失败', 'error');
        }
    }

    /**
     * 生成用户详情HTML
     * @param {Object} user 用户信息
     * @returns {string} HTML字符串
     */
    generateUserDetailHTML(user) {
        return `
            <div class="row g-4">
                <div class="col-md-4 text-center">
                    <div class="user-avatar mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                        ${this.utils.getInitials(user.real_name || user.username)}
                    </div>
                    <h5>${user.real_name || user.username}</h5>
                    <span class="type-badge type-${user.user_type}">${this.getUserTypeText(user.user_type)}</span>
                    <span class="status-badge status-${user.status} ms-2">${this.getStatusText(user.status)}</span>
                </div>
                <div class="col-md-8">
                    <table class="table table-borderless">
                        <tr>
                            <td width="120"><strong>用户名:</strong></td>
                            <td>${user.username}</td>
                        </tr>
                        <tr>
                            <td><strong>邮箱:</strong></td>
                            <td>${user.email}</td>
                        </tr>
                        <tr>
                            <td><strong>手机号:</strong></td>
                            <td>${user.phone || '未设置'}</td>
                        </tr>
                        <tr>
                            <td><strong>注册时间:</strong></td>
                            <td>${this.utils.formatDate(user.created_at)}</td>
                        </tr>
                        <tr>
                            <td><strong>最后登录:</strong></td>
                            <td>${user.last_login ? this.utils.formatDate(user.last_login) : '从未登录'}</td>
                        </tr>
                        <tr>
                            <td><strong>登录次数:</strong></td>
                            <td>${user.login_count || 0} 次</td>
                        </tr>
                        ${user.remarks ? `
                        <tr>
                            <td><strong>备注:</strong></td>
                            <td>${user.remarks}</td>
                        </tr>
                        ` : ''}
                    </table>
                </div>
            </div>
        `;
    }

    /**
     * 渲染分页
     */
    renderPagination() {
        const totalPages = Math.ceil(this.totalUsers / this.pageSize);
        const pagination = document.getElementById('userPagination');
        const currentPageInfo = document.getElementById('currentPageInfo');
        const totalUsersInfo = document.getElementById('totalUsersInfo');

        if (!pagination) return;

        // 更新信息显示
        const startItem = (this.currentPage - 1) * this.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.pageSize, this.totalUsers);
        currentPageInfo.textContent = `${startItem}-${endItem}`;
        totalUsersInfo.textContent = this.totalUsers;

        // 生成分页HTML
        let paginationHTML = '';

        // 上一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="userManager.loadUserList(${this.currentPage - 1})">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

        // 页码
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="userManager.loadUserList(1)">1</a>
                </li>
            `;
            if (startPage > 2) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="userManager.loadUserList(${i})">${i}</a>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="userManager.loadUserList(${totalPages})">${totalPages}</a>
                </li>
            `;
        }

        // 下一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="userManager.loadUserList(${this.currentPage + 1})">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UserManager };
} else {
    // 浏览器环境 - 创建全局实例
    const userManager = new UserManager();
    
    // 模块加载器期望的是实例，不是类
    window.UserManager = userManager; // 导出实例，让模块加载器可以直接调用render方法
    window.userManager = userManager; // 保持向后兼容
    
    // 为模块加载器导出整个模块
    window.UserManagementModule = {
        UserManager,
        userManager: userManager,
        version: '2.0.0',
        initialized: false
    };
    
    console.log('✅ 用户管理模块已导出到全局作用域');
}