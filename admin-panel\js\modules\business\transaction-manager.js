/**
 * 交易管理器
 * 从admin.js第5997-6705行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class TransactionManager {
    constructor() {
        this.auth = new AuthManager();
        this.currentPage = 1;
        this.filters = {
        };
        this.chartInstance = null;
    }
    // 初始化
    async initialize() {
        await this.loadFilterOptions();
        await this.loadTransactions();
        await this.loadStats();
        this.initializeEventListeners();
    }
    // 加载筛选选项
    async loadFilterOptions() {
        const user = this.auth.getUser();
        if (user.user_type !== 'admin') return;
        try {
            // 加载商户选项
            const merchantResponse = await fetch('/api/admin.php?action=merchants', {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            if (merchantResponse.ok) {
                const merchantData = await merchantResponse.json();
                if (merchantData.success) {
                    const merchantSelect = document.getElementById('merchantFilter');
                    if (merchantSelect) {
                        merchantData.data.forEach(merchant => {
                            merchantSelect.innerHTML += `<option value="${
                                merchant.id
                            }">${
                                merchant.username
                            }</option>`;
                        });
                    }
                }
            }
            // 加载码商选项
            const providerResponse = await fetch('/api/admin.php?action=payment_providers', {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            if (providerResponse.ok) {
                const providerData = await providerResponse.json();
                if (providerData.success) {
                    const providerSelect = document.getElementById('providerFilter');
                    if (providerSelect) {
                        providerData.data.forEach(provider => {
                            providerSelect.innerHTML += `<option value="${
                                provider.id
                            }">${
                                provider.provider_name
                            }</option>`;
                        });
                    }
                }
            }
        } catch (error) {
            console.error('加载筛选选项失败:',
            error);
        }
    }
    // 初始化事件监听器
    initializeEventListeners() {
        // 时间范围变化监听
        const dateRangeFilter = document.getElementById('dateRangeFilter');
        if (dateRangeFilter) {
            dateRangeFilter.addEventListener('change', (e) => {
                const customRanges = document.querySelectorAll('#customDateRange, #customDateRange2');
                if (e.target.value === 'custom') {
                    customRanges.forEach(el => el.style.display = 'block');
                } else {
                    customRanges.forEach(el => el.style.display = 'none');
                }
            });
        }
        // 搜索框回车事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchTransactions();
                }
            });
        }
    }
    // 加载交易列表
    async loadTransactions(page = 1) {
        this.currentPage = page;
        const container = document.getElementById('transactionsTableContainer');
        // 显示加载状态
        container.innerHTML = `
        <div class="text-center p-4">
        <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载交易数据...</p>
        </div>
        `;
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                limit: '20',
                ...this.filters
            });
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=get_transactions&${
                params.toString()
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP ${
                    response.status
                }`);
            }
            const data = await response.json();
            if (data.success) {
                this.renderTransactionList(data.data);
                this.renderPagination(data.pagination);
                this.updateTransactionCount(data.pagination.total_records);
            } else {
                throw new Error(data.message || '加载失败');
            }
        } catch (error) {
            console.error('加载交易列表失败:',
            error);
            container.innerHTML = `
            <div class="alert alert-danger m-3">
            <h6>加载失败</h6>
            <p class="mb-0">无法加载交易数据: ${
                error.message
            }</p>
            <button class="btn btn-sm btn-outline-danger mt-2" onclick="window.transactionManager.loadTransactions()">
            <i class="bi bi-arrow-clockwise me-1"></i>重试
            </button>
            </div>
            `;
        }
    }
    // 加载统计数据
    async loadStats(dateRange = 'today') {
        try {
            const params = new URLSearchParams({
                date_range: dateRange
            });
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=get_transaction_stats&${
                params.toString()
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.renderStats(data.data.overview);
                    if (data.data.trend_data) {
                        this.updateChart(data.data.trend_data);
                    }
                }
            }
        } catch (error) {
            console.error('加载统计数据失败:',
            error);
        }
    }
    // 渲染交易列表
    renderTransactionList(transactions) {
        const container = document.getElementById('transactionsTableContainer');
        if (transactions.length === 0) {
            container.innerHTML = `
            <div class="text-center p-5">
            <i class="bi bi-inbox" style="font-size: 3rem;
            opacity: 0.3;
            "></i>
            <h5 class="mt-3 text-muted">暂无交易数据</h5>
            <p class="text-muted">请尝试调整筛选条件或时间范围</p>
            </div>
            `;
            return;
        }
        const tableHtml = `
        <div class="table-responsive">
        <table class="table table-hover align-middle">
        <thead class="table-light">
        <tr>
        <th>订单号</th>
        <th>商户</th>
        <th>码商</th>
        <th>金额</th>
        <th>费率</th>
        <th>实际到账</th>
        <th>状态</th>
        <th>创建时间</th>
        <th>支付时间</th>
        <th>操作</th>
        </tr>
        </thead>
        <tbody>
        ${
            transactions.map(transaction => this.renderTransactionRow(transaction)).join('')
        }
        </tbody>
        </table>
        </div>
        `;
        container.innerHTML = tableHtml;
    }
    // 渲染单个交易行
    renderTransactionRow(transaction) {
        const user = this.auth.getUser();
        const canManage = ['admin', 'provider'].includes(user.user_type);
        return `
        <tr>
        <td>
        <code class="small">${
            transaction.order_id
        }</code>
        </td>
        <td>
        <div class="fw-medium">${
            transaction.merchant_name
        }</div>
        </td>
        <td>
        <div class="fw-medium">${
            transaction.provider_name
        }</div>
        </td>
        <td>
        <span class="fw-bold text-primary">¥${
            parseFloat(transaction.amount).toLocaleString()
        }</span>
        </td>
        <td>
        <span class="badge bg-secondary">${
            transaction.rate
        }%</span>
        </td>
        <td>
        <span class="fw-medium text-success">¥${
            parseFloat(transaction.actual_amount).toLocaleString()
        }</span>
        </td>
        <td>
        ${
            this.renderStatusBadge(transaction.status)
        }
        </td>
        <td>
        <small class="text-muted">${
            this.formatDateTime(transaction.created_at)
        }</small>
        </td>
        <td>
        <small class="text-muted">${
            transaction.paid_at ? this.formatDateTime(transaction.paid_at) : '-'
        }</small>
        </td>
        <td>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-info" onclick="window.transactionManager.viewTransactionDetail(${
            transaction.id
        })" title="查看详情" style="color: #3b82f6 !important;
        ">
        <i class="bi bi-eye"></i>
        </button>
        ${
            canManage ? `
            <button class="btn btn-outline-warning" onclick="window.transactionManager.showUpdateStatus(${
                transaction.id
            })" title="更新状态" style="color: #f59e0b !important;
            ">
            <i class="bi bi-pencil"></i>
            </button>
            ` : ''
        }
        </div>
        </td>
        </tr>
        `;
    }
    // 渲染状态徽章
    renderStatusBadge(status) {
        const statusConfig = {
            'pending': {
                class: 'bg-warning text-dark',
                text: '待处理'
            },
            'success': {
                class: 'bg-success',
                text: '成功'
            },
            'failed': {
                class: 'bg-danger',
                text: '失败'
            },
            'cancelled': {
                class: 'bg-secondary',
                text: '已取消'
            }
        };
        const config = statusConfig[status] || {
            class: 'bg-secondary',
            text: status
        };
        return `<span class="badge ${
            config.class
        }">${
            config.text
        }</span>`;
    }
    // 渲染统计数据
    renderStats(stats) {
        document.getElementById('totalTransactions').textContent = stats.total_transactions || 0;
        document.getElementById('successTransactions').textContent = stats.success_transactions || 0;
        document.getElementById('pendingTransactions').textContent = stats.pending_transactions || 0;
        document.getElementById('failedTransactions').textContent = stats.failed_transactions || 0;
        document.getElementById('totalAmount').textContent = '¥' + parseFloat(stats.total_amount || 0).toLocaleString();
        document.getElementById('successRate').textContent = (stats.success_rate || 0) + '%';
    }
    // 渲染分页
    renderPagination(pagination) {
        const container = document.getElementById('paginationContainer');
        if (!container || !pagination || pagination.total_pages <= 1) {
            if (container) container.innerHTML = '';
            return;
        }
        const currentPage = pagination.current_page;
        const totalPages = pagination.total_pages;
        let paginationHtml = '<nav aria-label="交易列表分页"><ul class="pagination justify-content-center">';
        // 上一页
        if (currentPage > 1) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.transactionManager.loadTransactions(${
                currentPage - 1
            })">
            <i class="bi bi-chevron-left"></i>
            </a>
            </li>
            `;
        }
        // 页码
        const startPage = Math.max(1,
        currentPage - 2);
        const endPage = Math.min(totalPages,
        currentPage + 2);
        if (startPage > 1) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.transactionManager.loadTransactions(1)">1</a>
            </li>
            `;
            if (startPage > 2) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        for (let i = startPage;
        i <= endPage;
        i++) {
            paginationHtml += `
            <li class="page-item ${
                i === currentPage ? 'active' : ''
            }">
            <a class="page-link" href="#" onclick="window.transactionManager.loadTransactions(${
                i
            })">${
                i
            }</a>
            </li>
            `;
        }
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.transactionManager.loadTransactions(${
                totalPages
            })">${
                totalPages
            }</a>
            </li>
            `;
        }
        // 下一页
        if (currentPage < totalPages) {
            paginationHtml += `
            <li class="page-item">
            <a class="page-link" href="#" onclick="window.transactionManager.loadTransactions(${
                currentPage + 1
            })">
            <i class="bi bi-chevron-right"></i>
            </a>
            </li>
            `;
        }
        paginationHtml += '</ul></nav>';
        container.innerHTML = paginationHtml;
    }
    // 更新交易计数
    updateTransactionCount(count) {
        const countElement = document.getElementById('transactionCount');
        if (countElement) {
            countElement.textContent = count || 0;
        }
    }
    // 应用筛选
    applyFilters() {
        this.filters = {
        };
        // 状态筛选
        const status = document.getElementById('statusFilter')?.value;
        if (status) this.filters.status = status;
        // 时间范围筛选
        const dateRange = document.getElementById('dateRangeFilter')?.value;
        if (dateRange === 'custom') {
            const startDate = document.getElementById('startDate')?.value;
            const endDate = document.getElementById('endDate')?.value;
            if (startDate) this.filters.start_date = startDate;
            if (endDate) this.filters.end_date = endDate;
        }
        // 商户和码商筛选（仅管理员）
        const user = this.auth.getUser();
        if (user.user_type === 'admin') {
            const merchantId = document.getElementById('merchantFilter')?.value;
            const providerId = document.getElementById('providerFilter')?.value;
            if (merchantId) this.filters.merchant_id = merchantId;
            if (providerId) this.filters.provider_id = providerId;
        }
        // 搜索关键词
        const searchKeyword = document.getElementById('searchInput')?.value;
        if (searchKeyword) this.filters.order_id = searchKeyword;
        this.loadTransactions(1);
        this.loadStats(dateRange || 'today');
    }
    // 搜索交易
    searchTransactions() {
        this.applyFilters();
    }
    // 刷新交易列表
    refreshTransactions() {
        this.loadTransactions(this.currentPage);
        this.loadStats();
    }
    // 查看交易详情
    async viewTransactionDetail(transactionId) {
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=get_transaction_detail&id=${
                transactionId
            }`, {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.showTransactionDetailModal(data.data);
                } else {
                    this.showError(data.message || '获取详情失败');
                }
            } else {
                throw new Error(`HTTP ${
                    response.status
                }`);
            }
        } catch (error) {
            console.error('获取交易详情失败:',
            error);
            this.showError('获取交易详情失败');
        }
    }
    // 显示交易详情模态框
    showTransactionDetailModal(transaction) {
        const user = this.auth.getUser();
        const canManage = ['admin', 'provider'].includes(user.user_type);
        const detailHtml = `
        <div class="row">
        <div class="col-md-6">
        <h6 class="text-muted">基本信息</h6>
        <table class="table table-sm">
        <tr><td><strong>订单号:</strong></td><td><code>${
            transaction.order_id
        }</code></td></tr>
        <tr><td><strong>交易状态:</strong></td><td>${
            this.renderStatusBadge(transaction.status)
        }</td></tr>
        <tr><td><strong>创建时间:</strong></td><td>${
            this.formatDateTime(transaction.created_at)
        }</td></tr>
        <tr><td><strong>支付时间:</strong></td><td>${
            transaction.paid_at ? this.formatDateTime(transaction.paid_at) : '-'
        }</td></tr>
        </table>
        </div>
        <div class="col-md-6">
        <h6 class="text-muted">金额信息</h6>
        <table class="table table-sm">
        <tr><td><strong>交易金额:</strong></td><td class="text-primary fw-bold">¥${
            parseFloat(transaction.amount).toLocaleString()
        }</td></tr>
        <tr><td><strong>费率:</strong></td><td>${
            transaction.rate
        }%</td></tr>
        <tr><td><strong>手续费:</strong></td><td class="text-warning">¥${
            parseFloat(transaction.fee).toLocaleString()
        }</td></tr>
        <tr><td><strong>实际到账:</strong></td><td class="text-success fw-bold">¥${
            parseFloat(transaction.actual_amount).toLocaleString()
        }</td></tr>
        </table>
        </div>
        </div>
        <div class="row mt-3">
        <div class="col-md-6">
        <h6 class="text-muted">商户信息</h6>
        <table class="table table-sm">
        <tr><td><strong>商户名称:</strong></td><td>${
            transaction.merchant_name
        }</td></tr>
        ${
            transaction.merchant_email ? `<tr><td><strong>商户邮箱:</strong></td><td>${
                transaction.merchant_email
            }</td></tr>` : ''
        }
        </table>
        </div>
        <div class="col-md-6">
        <h6 class="text-muted">码商信息</h6>
        <table class="table table-sm">
        <tr><td><strong>码商名称:</strong></td><td>${
            transaction.provider_name
        }</td></tr>
        ${
            transaction.provider_contact ? `<tr><td><strong>联系人:</strong></td><td>${
                transaction.provider_contact
            }</td></tr>` : ''
        }
        </table>
        </div>
        </div>
        ${
            transaction.device ? `
            <div class="row mt-3">
            <div class="col-12">
            <h6 class="text-muted">设备信息</h6>
            <table class="table table-sm">
            <tr><td><strong>设备名称:</strong></td><td>${
                transaction.device.name
            }</td></tr>
            <tr><td><strong>设备ID:</strong></td><td><code>${
                transaction.device.string_id
            }</code></td></tr>
            </table>
            </div>
            </div>
            ` : ''
        }
        ${
            transaction.alipay_account ? `
            <div class="row mt-3">
            <div class="col-12">
            <h6 class="text-muted">支付账户信息</h6>
            <table class="table table-sm">
            <tr><td><strong>账户名称:</strong></td><td>${
                transaction.alipay_account.name
            }</td></tr>
            <tr><td><strong>支付宝账号:</strong></td><td><code>${
                transaction.alipay_account.number
            }</code></td></tr>
            ${
                transaction.alipay_account.real_name ? `<tr><td><strong>真实姓名:</strong></td><td>${
                    transaction.alipay_account.real_name
                }</td></tr>` : ''
            }
            </table>
            </div>
            </div>
            ` : ''
        }
        ${
            transaction.remark ? `
            <div class="row mt-3">
            <div class="col-12">
            <h6 class="text-muted">备注信息</h6>
            <p class="text-muted">${
                transaction.remark
            }</p>
            </div>
            </div>
            ` : ''
        }
        `;
        document.getElementById('transactionDetailContent').innerHTML = detailHtml;
        // 显示更新状态按钮
        const updateBtn = document.getElementById('updateStatusBtn');
        if (updateBtn && canManage) {
            updateBtn.style.display = 'inline-block';
            updateBtn.onclick = () => this.showUpdateStatus(transaction.id);
        }
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('transactionDetailModal'));
        modal.show();
    }
    // 显示更新状态对话框
    showUpdateStatus(transactionId) {
        document.getElementById('updateTransactionId').value = transactionId;
        document.getElementById('newStatus').value = '';
        document.getElementById('updateRemark').value = '';
        const modal = new bootstrap.Modal(document.getElementById('updateStatusModal'));
        modal.show();
    }
    // 确认更新状态
    async confirmUpdateStatus() {
        const transactionId = document.getElementById('updateTransactionId').value;
        const newStatus = document.getElementById('newStatus').value;
        const remark = document.getElementById('updateRemark').value;
        if (!newStatus) {
            this.showError('请选择新状态');
            return;
        }
        const spinner = document.getElementById('updateStatusSpinner');
        spinner.classList.remove('d-none');
        try {
            const response = await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=update_transaction_status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    transaction_id: transactionId,
                    status: newStatus
                })
            });
            const data = await response.json();
            if (data.success) {
                this.showSuccess('交易状态更新成功');
                // 更新备注（如果有）
                if (remark) {
                    await this.updateTransactionRemark(transactionId,
                    remark);
                }
                // 关闭模态框并刷新列表
                bootstrap.Modal.getInstance(document.getElementById('updateStatusModal')).hide();
                this.refreshTransactions();
            } else {
                this.showError(data.message || '更新失败');
            }
        } catch (error) {
            console.error('更新状态失败:',
            error);
            this.showError('更新状态失败');
        } finally {
            spinner.classList.add('d-none');
        }
    }
    // 更新交易备注
    async updateTransactionRemark(transactionId,
    remark) {
        try {
            await fetch(`${
                CONFIG.API_BASE_URL
            }/admin.php?action=update_transaction_remark`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    transaction_id: transactionId,
                    remark: remark
                })
            });
        } catch (error) {
            console.error('更新备注失败:',
            error);
        }
    }
    // 切换图表显示
    toggleChart() {
        const chartSection = document.getElementById('transactionChart');
        if (chartSection.style.display === 'none') {
            chartSection.style.display = 'block';
            this.loadStats();
            // 重新加载数据以更新图表
        } else {
            chartSection.style.display = 'none';
        }
    }
    // 更新图表
    updateChart(trendData) {
        // 这里可以使用Chart.js或其他图表库
        // 为简化示例，暂时跳过图表实现
        console.log('图表数据:',
        trendData);
    }
    // 导出交易数据
    exportTransactions() {
        const params = new URLSearchParams(this.filters);
        params.append('export', 'true');
        const url = `${
            CONFIG.API_BASE_URL
        }/admin.php?action=get_transactions&${
            params.toString()
        }`;
        const link = document.createElement('a');
        link.href = url;
        link.target = '_blank';
        link.click();
        this.showInfo('正在准备导出数据...');
    }
    // 导出当前页数据
    exportCurrentData() {
        const params = new URLSearchParams(this.filters);
        params.append('export', 'true');
        params.append('page',
        this.currentPage.toString());
        const url = `${
            CONFIG.API_BASE_URL
        }/admin.php?action=get_transactions&${
            params.toString()
        }`;
        const link = document.createElement('a');
        link.href = url;
        link.target = '_blank';
        link.click();
        this.showInfo('正在准备导出当前页数据...');
    }
    // 格式化日期时间
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    // 显示成功消息
    showSuccess(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        ';
        alert.innerHTML = `
        <i class="bi bi-check-circle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
    // 显示错误消息
    showError(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        ';
        alert.innerHTML = `
        <i class="bi bi-exclamation-triangle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);
    }
    // 显示信息消息
    showInfo(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-info alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        ';
        alert.innerHTML = `
        <i class="bi bi-info-circle me-2"></i>${
            message
        }
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = TransactionManager;
} else if (typeof window !== "undefined") {
    window.TransactionManager = TransactionManager;
}

console.log('📦 TransactionManager 模块加载完成');
