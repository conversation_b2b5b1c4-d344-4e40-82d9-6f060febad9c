<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== 登录测试开始 ===\n";

try {
    // 测试基础PHP语法
    echo "1. PHP基础测试通过\n";
    
    // 测试包含文件
    echo "2. 测试包含TenantAuth.php...\n";
    require_once dirname(__FILE__) . '/../utils/TenantAuth.php';
    echo "   TenantAuth.php 包含成功\n";
     
    echo "3. 测试包含TenantManager.php...\n";
    require_once dirname(__FILE__) . '/../core/TenantManager.php';
    echo "   TenantManager.php 包含成功\n";
    
    // 测试对象创建
    echo "4. 测试创建TenantAuth对象...\n";
    $tenantAuth = new TenantAuth();
    echo "   TenantAuth 对象创建成功\n";
    
    echo "5. 测试创建TenantManager对象...\n";
    $tenantManager = new TenantManager();
    echo "   TenantManager 对象创建成功\n";
    
    // 测试获取当前租户
    echo "6. 测试获取当前租户...\n";
    $currentTenant = $tenantManager->getCurrentTenant();
    echo "   当前租户: " . print_r($currentTenant, true) . "\n";
    
    // 测试模拟登录请求
    echo "7. 测试模拟登录数据处理...\n";
    $testInput = array(
        'username' => 'test',
        'password' => 'test'
    );
    echo "   模拟登录数据: " . json_encode($testInput) . "\n";
    
    echo "=== 登录测试完成，没有发现明显错误 ===\n";
    
} catch (ParseError $e) {
    echo "PHP语法错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "PHP错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?> 