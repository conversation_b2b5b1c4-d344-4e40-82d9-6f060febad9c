/**
 * 设备管理模块统一管理器
 * 集成设备管理、小组管理、签到监控等功能模块
 */

class DeviceModuleManager {
    constructor() {
        // 延迟初始化模块，等待依赖模块加载完成
        this.modules = {};
        this.moduleClasses = {
            deviceManager: 'DeviceManager',
            groupManager: 'GroupManager', 
            checkinMonitor: 'CheckinMonitor'
        };
        
        this.currentModule = null;
        this.moduleContainer = null;
    }

    /**
     * 初始化设备管理模块管理器
     */
    async init(container) {
        try {
            // 设置容器
            if (container) {
                this.moduleContainer = container;
            } else {
                this.setupModuleContainer();
            }
            
            // 初始化模块实例
            this.initializeModules();
            
            this.setupNavigation();
            
            // 默认显示设备管理模块
            await this.showModule('deviceManager');
            
            console.log('✅ 设备管理模块管理器初始化成功');
        } catch (error) {
            console.error('❌ 设备管理模块初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化模块实例
     */
    initializeModules() {
        console.log('🔧 初始化设备管理子模块...');
        
        // 创建设备管理模块实例
        if (window.DeviceManager) {
            this.modules.deviceManager = new window.DeviceManager();
            console.log('✅ 设备管理模块实例创建成功');
        } else {
            console.warn('⚠️ DeviceManager类未找到，将在需要时创建');
        }
        
        // 创建小组管理模块实例（如果可用）
        if (window.GroupManager) {
            this.modules.groupManager = new window.GroupManager();
            console.log('✅ 小组管理模块实例创建成功');
        } else {
            console.warn('⚠️ GroupManager类未找到');
        }
        
        // 创建签到监控模块实例（如果可用）
        if (window.CheckinMonitor) {
            this.modules.checkinMonitor = new window.CheckinMonitor();
            console.log('✅ 签到监控模块实例创建成功');
        } else {
            console.warn('⚠️ CheckinMonitor类未找到');
        }
    }

    /**
     * 设置模块容器
     */
    setupModuleContainer() {
        // 检查是否存在设备管理容器
        let container = document.getElementById('deviceModuleContainer');
        if (!container) {
            // 创建模块容器
            container = document.createElement('div');
            container.id = 'deviceModuleContainer';
            container.className = 'device-module-container';
            
            // 插入到页面中适当位置
            const mainContent = document.querySelector('.main-content') || document.body;
            mainContent.appendChild(container);
        }
        
        this.moduleContainer = container;
    }

    /**
     * 设置导航菜单
     */
    setupNavigation() {
        // 创建导航标签页
        const navTabs = document.createElement('div');
        navTabs.innerHTML = `
            <div class="device-module-nav mb-4">
                <ul class="nav nav-tabs" id="deviceModuleTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="device-tab" data-bs-toggle="tab" 
                                data-module="deviceManager" type="button" role="tab">
                            <i class="bi bi-phone me-2"></i>设备管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="group-tab" data-bs-toggle="tab" 
                                data-module="groupManager" type="button" role="tab">
                            <i class="bi bi-people me-2"></i>小组管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="checkin-tab" data-bs-toggle="tab" 
                                data-module="checkinMonitor" type="button" role="tab">
                            <i class="bi bi-clock-history me-2"></i>签到监控
                        </button>
                    </li>
                </ul>
            </div>
        `;
        
        // 插入导航到容器顶部
        this.moduleContainer.insertBefore(navTabs, this.moduleContainer.firstChild);
        
        // 绑定标签页切换事件
        document.querySelectorAll('[data-module]').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const moduleName = e.target.getAttribute('data-module');
                this.showModule(moduleName);
            });
        });
    }

    /**
     * 显示指定模块
     */
    async showModule(moduleName) {
        try {
            console.log(`🔄 切换到模块: ${moduleName}`);
            
            // 如果当前模块存在销毁方法，先清理
            if (this.currentModule && this.modules[this.currentModule] && 
                typeof this.modules[this.currentModule].destroy === 'function') {
                this.modules[this.currentModule].destroy();
            }
            
            // 清空容器内容（保留导航）
            const content = this.moduleContainer.querySelector('.module-content');
            if (content) {
                content.remove();
            }
            
            // 创建新的内容容器
            const moduleContent = document.createElement('div');
            moduleContent.className = 'module-content';
            this.moduleContainer.appendChild(moduleContent);
            
            // 检查模块是否存在，如果不存在则尝试创建
            let module = this.modules[moduleName];
            if (!module) {
                module = this.createModuleInstance(moduleName);
                if (module) {
                    this.modules[moduleName] = module;
                }
            }
            
            // 初始化并显示模块
            if (module) {
                await module.init(moduleContent);
                this.currentModule = moduleName;
                
                // 更新导航状态
                this.updateNavigation(moduleName);
                
                console.log(`✅ 模块 ${moduleName} 加载成功`);
            } else {
                // 显示模块不可用的提示
                this.showModuleUnavailable(moduleContent, moduleName);
            }
        } catch (error) {
            console.error(`❌ 加载模块 ${moduleName} 失败:`, error);
            this.showError(`加载模块失败: ${error.message}`);
        }
    }

    /**
     * 创建模块实例
     */
    createModuleInstance(moduleName) {
        const className = this.moduleClasses[moduleName];
        if (className && window[className]) {
            console.log(`🔧 创建模块实例: ${className}`);
            return new window[className]();
        }
        console.warn(`⚠️ 无法创建模块实例: ${moduleName} (${className})`);
        return null;
    }

    /**
     * 显示模块不可用提示
     */
    showModuleUnavailable(container, moduleName) {
        const moduleNames = {
            deviceManager: '设备管理',
            groupManager: '小组管理',
            checkinMonitor: '签到监控'
        };
        
        const displayName = moduleNames[moduleName] || moduleName;
        
        container.innerHTML = `
            <div class="text-center p-5">
                <i class="bi bi-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                <h4 class="mt-3 text-muted">${displayName}模块暂不可用</h4>
                <p class="text-muted">该模块可能尚未加载或不支持当前环境</p>
                <div class="mt-4">
                    <button class="btn btn-outline-primary" onclick="window.location.reload()">
                        <i class="bi bi-arrow-clockwise me-2"></i>重新加载页面
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 更新导航状态
     */
    updateNavigation(activeModule) {
        document.querySelectorAll('[data-module]').forEach(tab => {
            const moduleName = tab.getAttribute('data-module');
            if (moduleName === activeModule) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });
    }

    /**
     * 获取当前模块实例
     */
    getCurrentModule() {
        return this.currentModule ? this.modules[this.currentModule] : null;
    }

    /**
     * 获取指定模块实例
     */
    getModule(moduleName) {
        return this.modules[moduleName] || null;
    }

    /**
     * 刷新当前模块
     */
    async refreshCurrentModule() {
        if (this.currentModule) {
            await this.showModule(this.currentModule);
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger alert-dismissible fade show';
        errorDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        this.moduleContainer.insertBefore(errorDiv, this.moduleContainer.firstChild);
        
        // 5秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    /**
     * 显示成功信息
     */
    showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'alert alert-success alert-dismissible fade show';
        successDiv.innerHTML = `
            <i class="bi bi-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        this.moduleContainer.insertBefore(successDiv, this.moduleContainer.firstChild);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 3000);
    }

    /**
     * 模块间通信
     * 允许模块之间传递消息和数据
     */
    sendMessage(fromModule, toModule, message, data = null) {
        try {
            const targetModule = this.modules[toModule];
            if (targetModule && typeof targetModule.receiveMessage === 'function') {
                targetModule.receiveMessage(fromModule, message, data);
            } else {
                console.warn(`模块 ${toModule} 不支持消息接收或不存在`);
            }
        } catch (error) {
            console.error('模块间通信失败:', error);
        }
    }

    /**
     * 销毁管理器
     */
    destroy() {
        // 销毁所有模块
        Object.keys(this.modules).forEach(moduleName => {
            const module = this.modules[moduleName];
            if (typeof module.destroy === 'function') {
                module.destroy();
            }
        });
        
        // 清理容器
        if (this.moduleContainer && this.moduleContainer.parentNode) {
            this.moduleContainer.remove();
        }
        
        this.modules = {};
        this.currentModule = null;
        this.moduleContainer = null;
    }
}

// 创建全局实例
window.deviceModuleManager = new DeviceModuleManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DeviceModuleManager };
} else {
    // 浏览器环境 - 导出类到全局作用域
    window.DeviceModuleManager = DeviceModuleManager;
    
    // 为模块加载器导出整个模块
    window.DeviceModuleManagerModule = {
        DeviceModuleManager,
        version: '1.0.0',
        initialized: true
    };
    
    console.log('✅ 设备模块管理器已导出到全局作用域');
}

// export default DeviceModuleManager; // 注释掉ES6导出，使用全局变量
window.DeviceModuleManager = DeviceModuleManager; 