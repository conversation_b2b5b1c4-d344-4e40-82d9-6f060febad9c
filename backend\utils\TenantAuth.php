<?php
require_once dirname(dirname(__FILE__)) . '/config/database.php';
require_once dirname(dirname(__FILE__)) . '/utils/Auth.php';
require_once dirname(dirname(__FILE__)) . '/core/TenantManager.php';

/**
 * 多租户认证系统 - 四层架构版本
 * 基于域名识别租户并验证访问权限
 * 集成四层架构域名解析逻辑
 */
class TenantAuth extends Auth {
    private $tenantManager;
    private $domainInfo;
    
    public function __construct() {
        parent::__construct();
        $this->tenantManager = new TenantManager();
        
        // 获取当前域名信息
        $currentDomain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
        $this->domainInfo = $this->resolveDomainInfo($currentDomain);
    }
    
    /**
     * 域名解析方法
     */
    public function resolveDomainInfo($domain) {
        try {
            // 确保数据库连接正常
            if (!$this->db) {
                $this->db = Database::getInstance();
            }
            
            // 1. 检查是否是保留域名
            $reservedDomains = array('admin.top005.com', 'localhost', '127.0.0.1', '**************', 'top005.com');
            if (in_array($domain, $reservedDomains)) {
                return array(
                    'tenant_type' => 'system_admin',
                    'tenant_id' => 0,
                    'platform_id' => null,
                    'platform_name' => null,
                    'platform_code' => null,
                    'status' => 'active',
                    'domain' => $domain,
                    'brand_name' => '系统管理后台',
                    'theme_config' => json_encode(array('primary_color' => '#1890ff'))
                );
            }
            
            // 2. 查询域名配置表
            error_log("TenantAuth::resolveDomainInfo - 查询域名: " . $domain);
            $config = $this->db->fetch("
                SELECT 
                    dc.tenant_type, 
                    dc.tenant_id, 
                    dc.custom_config,
                    dc.status,
                    dc.ssl_enabled,
                    dc.is_custom
                FROM domain_configs dc
                WHERE dc.domain = ? AND dc.status = 'active'
                LIMIT 1
            ", array($domain));
            
            error_log("TenantAuth::resolveDomainInfo - 查询结果: " . ($config ? json_encode($config) : 'null'));
            
            if ($config) {
                // 解析自定义配置
                $config['custom_config'] = json_decode($config['custom_config'], true) ?: array();
                $config['domain'] = $domain;
                
                // 根据租户类型获取关联的平台信息
                $platformInfo = $this->getPlatformInfoByTenant($config['tenant_type'], $config['tenant_id']);
                if ($platformInfo) {
                    $config['platform_id'] = $platformInfo['platform_id'];
                    $config['platform_name'] = $platformInfo['platform_name'];
                    $config['platform_code'] = $platformInfo['platform_code'];
                }
                
                return $config;
            }
            

            
            return null;
            
        } catch (Exception $e) {
            error_log("TenantAuth::resolveDomainInfo - 异常: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 根据租户类型和ID获取关联的平台信息
     */
    private function getPlatformInfoByTenant($tenantType, $tenantId) {
        try {
            $platformInfo = null;
            
            switch ($tenantType) {
                case 'platform_admin':
                    // 平台管理员直接从platforms表获取
                    $platformInfo = $this->db->fetch("
                        SELECT id as platform_id, platform_name, platform_code 
                        FROM platforms 
                        WHERE id = ?
                    ", array($tenantId));
                    break;
                    
                case 'provider':
                    // 码商从payment_providers表关联获取
                    $platformInfo = $this->db->fetch("
                        SELECT p.id as platform_id, p.platform_name, p.platform_code 
                        FROM payment_providers pp 
                        JOIN platforms p ON pp.platform_id = p.id 
                        WHERE pp.id = ?
                    ", array($tenantId));
                    break;
                    
                case 'merchant':
                    // 商户从merchants表关联获取
                    $platformInfo = $this->db->fetch("
                        SELECT p.id as platform_id, p.platform_name, p.platform_code 
                        FROM merchants m 
                        JOIN platforms p ON m.platform_id = p.id 
                        WHERE m.id = ?
                    ", array($tenantId));
                    break;
                    
                case 'system_admin':
                default:
                    // 系统管理员不关联具体平台
                    return null;
            }
            
            return $platformInfo;
            
        } catch (Exception $e) {
            error_log("TenantAuth::getPlatformInfoByTenant - 异常: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 获取当前域名信息
     */
    public function getDomainInfo() {
        return $this->domainInfo;
    }
    
    /**
     * 验证用户是否有权限访问当前租户域名
     */
    private function validateUserTenantAccess($user, $domainInfo) {
        if (!$domainInfo) {
            return array('valid' => false, 'message' => '域名未配置');
        }
        
        $tenantType = $domainInfo['tenant_type'];
        $tenantId = $domainInfo['tenant_id'];
        
        // 系统管理员特殊处理
        if ($tenantType === 'system_admin') {
            if ($user['user_type'] === 'system_admin') {
                return array('valid' => true, 'message' => '系统管理员权限验证通过');
            }
            return array('valid' => false, 'message' => '只有系统管理员可以访问此域名');
        }
        
        // 获取对应业务表名
        $tableName = $this->getTenantTable($tenantType);
        if (!$tableName) {
            return array('valid' => false, 'message' => '未知的租户类型');
        }
        
        // 检查是否是管理员
        $record = $this->db->fetch("SELECT user_id FROM {$tableName} WHERE id = ?", array($tenantId));
        if ($record && $record['user_id'] == $user['id']) {
            return array('valid' => true, 'message' => '管理员权限验证通过');
        }
        
        // 检查是否是员工
        if ($user['user_type'] === 'employee') {
            $employee = $this->db->fetch(
                "SELECT 1 FROM employees WHERE user_id = ? AND belongs_to_type = ? AND belongs_to_id = ?", 
                array($user['id'], $tenantType, $tenantId)
            );
            if ($employee) {
                return array('valid' => true, 'message' => '员工权限验证通过');
            }
        }
        
        return array('valid' => false, 'message' => '您无权访问此域名');
    }
    
    /**
     * 获取租户对应的业务表名
     */
    private function getTenantTable($tenantType) {
        $tables = array(
            'platform_admin' => 'platforms',
            'provider' => 'payment_providers', 
            'merchant' => 'merchants'
        );
        return isset($tables[$tenantType]) ? $tables[$tenantType] : null;
    }
    

    
    /**
     * 租户登录（四层架构版本）
     */
    public function tenantLogin($username, $password) {
        error_log("TenantAuth::tenantLogin() - 开始租户登录，用户名: " . $username);
        
        // 使用新的域名解析逻辑
        if (!$this->domainInfo) {
            error_log("TenantAuth::tenantLogin() - 无效的访问域名或域名未配置");
            return array('success' => false, 'message' => '无效的访问域名或域名未配置');
        }
        
        error_log("TenantAuth::tenantLogin() - 域名信息: " . json_encode($this->domainInfo));
        
        // 执行基础登录
        error_log("TenantAuth::tenantLogin() - 执行基础登录");
        $loginResult = $this->login($username, $password);
        
        error_log("TenantAuth::tenantLogin() - 基础登录结果: " . print_r($loginResult, true));
        
        if (!$loginResult['success']) {
            error_log("TenantAuth::tenantLogin() - 基础登录失败: " . $loginResult['message']);
            return $loginResult;
        }
        
        $user = $loginResult['user'];
        
        error_log("TenantAuth::tenantLogin() - 登录用户信息: " . print_r($user, true));
        
        // 验证用户是否有权访问当前租户域名
        error_log("TenantAuth::tenantLogin() - 验证租户访问权限");
        $accessCheck = $this->validateUserTenantAccess($user, $this->domainInfo);
        
        if (!$accessCheck['valid']) {
            error_log("TenantAuth::tenantLogin() - 权限验证失败: " . $accessCheck['message']);
            return array('success' => false, 'message' => $accessCheck['message']);
        }
        
        error_log("TenantAuth::tenantLogin() - 权限验证通过: " . $accessCheck['message']);
        
        // 生成租户专用token
        $tenantToken = $this->generateTenantToken($user['id'], $this->domainInfo);
        
        // 记录租户登录日志
        $this->logTenantAction($user['id'], 'tenant_login', 'user', $user['id'], 
                              '用户在租户域名登录: ' . $_SERVER['HTTP_HOST']);
        
        // 创建租户会话
        $this->createTenantSession($tenantToken, $user['id'], $this->domainInfo);
        
        return array(
            'success' => true,
            'token' => $tenantToken,
            'user' => $user,
            'tenant' => array(
                'type' => $this->domainInfo['tenant_type'],
                'id' => $this->domainInfo['tenant_id'],
                'platform_id' => $this->domainInfo['platform_id'],
                'platform_name' => $this->domainInfo['platform_name'],
                'platform_code' => $this->domainInfo['platform_code'],
                'domain' => $_SERVER['HTTP_HOST']
            )
        );
    }
    
    /**
     * 生成租户专用token（四层架构版本）
     */
    private function generateTenantToken($userId, $domainInfo) {
        $header = json_encode(array('typ' => 'JWT', 'alg' => 'HS256'));
        $payload = json_encode(array(
            'user_id' => $userId,
            'tenant_type' => $domainInfo['tenant_type'],
            'tenant_id' => $domainInfo['tenant_id'],
            'platform_id' => $domainInfo['platform_id'],
            'domain' => $_SERVER['HTTP_HOST'],
            'exp' => time() + (24 * 60 * 60) // 24小时过期
        ));
        
        $headerEncoded = str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($header));
        $payloadEncoded = str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($payload));
        
        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 
                              $this->getTenantSecretKey($domainInfo), true);
        $signatureEncoded = str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($signature));
        
        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }
    
    /**
     * 验证租户token（四层架构版本）
     */
    public function verifyTenantToken($token) {
        if (!$token) return false;
        
        $parts = explode('.', $token);
        if (count($parts) !== 3) return false;
        
        list($headerEncoded, $payloadEncoded, $signatureEncoded) = $parts;
        
        $payload = json_decode(base64_decode(str_replace(array('-', '_'), array('+', '/'), $payloadEncoded)), true);
        
        if (!$payload || $payload['exp'] < time()) return false;
        
        // 验证token中的租户信息与当前域名是否匹配
        if (!$this->domainInfo) {
            error_log("TenantAuth::verifyTenantToken - 当前域名未配置");
            return false;
        }
        
        if ($payload['tenant_type'] !== $this->domainInfo['tenant_type'] || 
            $payload['tenant_id'] != $this->domainInfo['tenant_id']) {
            error_log("TenantAuth::verifyTenantToken - token租户信息与当前域名不匹配");
            return false;
        }
        
        // 验证签名
        $expectedSignature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, 
                                     $this->getTenantSecretKey($this->domainInfo), true);
        $expectedSignatureEncoded = str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($expectedSignature));
        
        if ($signatureEncoded !== $expectedSignatureEncoded) {
            error_log("TenantAuth::verifyTenantToken - token签名验证失败");
            return false;
        }
        
        return $payload;
    }
    
    /**
     * 获取当前租户用户信息
     */
    public function getCurrentTenantUser($token) {
        try {
            error_log("TenantAuth::getCurrentTenantUser - 开始处理token: " . substr($token, 0, 50) . "...");
            
            $tokenData = $this->verifyTenantToken($token);
            error_log("TenantAuth::getCurrentTenantUser - verifyTenantToken结果: " . ($tokenData ? 'success' : 'failed'));
            
            if (!$tokenData) {
                error_log("TenantAuth::getCurrentTenantUser - token验证失败，返回false");
                return false;
            }
            
            error_log("TenantAuth::getCurrentTenantUser - token数据: " . json_encode($tokenData));
            
            $sql = "SELECT u.*, 
                        CASE 
                            WHEN u.user_type = 'provider' THEN p.id
                            WHEN u.user_type = 'merchant' THEN m.id
                            ELSE NULL
                        END as profile_id,
                        CASE 
                            WHEN u.user_type = 'provider' THEN CONCAT('码商-', COALESCE(p.provider_code, p.id))
                            WHEN u.user_type = 'merchant' THEN CONCAT('商户-', COALESCE(m.merchant_code, m.id))
                            WHEN u.user_type = 'employee' THEN u.username
                            ELSE u.username
                        END as display_name
                 FROM users u
                 LEFT JOIN payment_providers p ON u.id = p.user_id AND u.user_type = 'provider'
                 LEFT JOIN merchants m ON u.id = m.user_id AND u.user_type = 'merchant'
                 WHERE u.id = ?";
            
            error_log("TenantAuth::getCurrentTenantUser - 执行SQL: " . $sql);
            error_log("TenantAuth::getCurrentTenantUser - 参数: user_id=" . $tokenData['user_id']);
            
            // 检查数据库连接是否有效，如果为null则重新初始化
            if ($this->db === null) {
                error_log("TenantAuth::getCurrentTenantUser - 数据库连接为null，重新初始化");
                require_once dirname(dirname(__FILE__)) . '/config/database.php';
                $this->db = Database::getInstance();
                error_log("TenantAuth::getCurrentTenantUser - 数据库连接重新初始化完成");
            }
            
            // 用户认证查询不应该被租户过滤，因为需要获取用户的租户信息
            $user = $this->db->withoutTenantFilter(function() use ($sql, $tokenData) {
                return $this->db->fetch($sql, array($tokenData['user_id']));
            });
            
            error_log("TenantAuth::getCurrentTenantUser - 数据库查询结果: " . ($user ? 'found user' : 'no user found'));
            
            if ($user) {
                $user['tenant_info'] = array(
                    'type' => $tokenData['tenant_type'],
                    'id' => $tokenData['tenant_id'],
                    'domain' => $tokenData['domain']
                );
                error_log("TenantAuth::getCurrentTenantUser - 用户信息: ID=" . $user['id'] . ", 用户名=" . $user['username'] . ", 类型=" . $user['user_type']);
                
                // 设置数据库租户上下文
                $tenantContext = array(
                    'tenant_type' => $tokenData['tenant_type'],
                    'tenant_id' => $tokenData['tenant_id'],
                    'platform_id' => isset($tokenData['platform_id']) ? $tokenData['platform_id'] : null,
                    'user_id' => $user['id'],
                    'domain' => $tokenData['domain']
                );
                
                $this->db->setTenantContext($tenantContext);
                error_log("TenantAuth::getCurrentTenantUser - 已设置数据库租户上下文");
            }
            
            error_log("TenantAuth::getCurrentTenantUser - 处理完成，返回结果");
            return $user;
            
        } catch (Exception $e) {
            error_log("TenantAuth::getCurrentTenantUser - 异常: " . $e->getMessage());
            error_log("TenantAuth::getCurrentTenantUser - 异常堆栈: " . $e->getTraceAsString());
            return false;
        } catch (Error $e) {
            error_log("TenantAuth::getCurrentTenantUser - PHP错误: " . $e->getMessage());
            error_log("TenantAuth::getCurrentTenantUser - 错误堆栈: " . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * 检查租户权限
     */
    public function hasTenantPermission($user, $permission) {
        // 简化权限检查：只要是已认证用户就有权限
        return isset($user['id']) && !empty($user['id']);
    }
    
    /**
     * 创建租户会话
     */
    private function createTenantSession($token, $userId, $tenant) {
        // 暂时禁用租户会话创建，避免tenant_sessions表不存在的问题
        // 可以在后续创建表后重新启用
        return;
        
        // 清理过期会话
        $this->db->execute(
            "DELETE FROM tenant_sessions WHERE expires_at < NOW()"
        );
        
        // 创建新会话
        $sessionId = hash('sha256', $token . time());
        $this->db->execute(
            "INSERT INTO tenant_sessions (
                session_id, tenant_type, tenant_id, user_id, domain_name, 
                ip_address, user_agent, expires_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 24 HOUR))",
            array(
                $sessionId,
                $tenant['tenant_type'],
                $tenant['tenant_id'],
                $userId,
                $tenant['domain_name'],
                $this->getClientIP(),
                isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''
            )
        );
    }
    
    /**
     * 记录租户操作日志
     */
    public function logTenantAction($userId, $action, $targetType, $targetId, $description) {
        $tenant = $this->tenantManager->getCurrentTenant();
        
        // 扩展描述信息包含租户信息
        $fullDescription = $description;
        if ($tenant) {
            $fullDescription .= " [租户: " . $tenant['tenant_type'] . ":" . $tenant['tenant_id'] . 
                              " 域名: " . $tenant['domain_name'] . "]";
        }
        
        $this->logAction($userId, $action, $targetType, $targetId, $fullDescription);
    }
    
    /**
     * 获取租户专用密钥（四层架构版本）
     */
    private function getTenantSecretKey($domainInfo) {
        return 'tenant_' . $domainInfo['tenant_type'] . '_' . $domainInfo['tenant_id'] . '_' . ($domainInfo['platform_id'] ?: 'system') . '_secret_key';
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                return $ip;
            }
        }
        
        return '0.0.0.0';
    }
    
    /**
     * 验证租户管理员权限
     */
    public function verifyTenantAdminToken() {
        $token = $this->getAuthToken();
        $user = $this->getCurrentTenantUser($token);
        return $user !== false;
    }
    
    /**
     * 获取租户管理器实例
     */
    public function getTenantManager() {
        return $this->tenantManager;
    }
    
    /**
     * 租户注销
     */
    public function tenantLogout($token) {
        $tokenData = $this->verifyTenantToken($token);
        
        if ($tokenData) {
            // 暂时禁用租户会话删除，避免tenant_sessions表不存在的问题
            // $sessionId = hash('sha256', $token . $tokenData['exp']);
            // $this->db->execute(
            //     "DELETE FROM tenant_sessions WHERE session_id = ?",
            //     array($sessionId)
            // );
            
            // 记录注销日志
            $this->logTenantAction($tokenData['user_id'], 'tenant_logout', 'user', 
                                 $tokenData['user_id'], '用户从租户域名注销');
        }
        
        return array('code' => 200, 'message' => '注销成功');
    }
    
    // ============ 兼容性方法 ============
    // 为了简化现有文件，添加这些兼容性方法
    
    /**
     * 静态初始化方法
     */
    public static function init() {
        static $instance = null;
        if ($instance === null) {
            $instance = new self();
            
            // 尝试自动设置当前用户的租户上下文
            $token = $instance->getAuthToken();
            if ($token) {
                $user = $instance->getCurrentTenantUser($token);
                if ($user) {
                    error_log("TenantAuth::init - 自动设置租户上下文完成");
                }
            }
        }
        return $instance;
    }
    
    /**
     * 获取数据库连接实例
     * 方便其他API文件直接通过TenantAuth获取数据库连接
     */
    public function getDatabase() {
        if (!$this->db) {
            // 如果数据库连接不存在，尝试重新初始化
            require_once dirname(dirname(__FILE__)) . '/config/database.php';
            $this->db = Database::getInstance();
        }
        return $this->db;
    }
    
    /**
     * 兼容性方法：检查用户认证状态
     * 替代原来的 checkLogin() 方法
     */
    public function checkAuth() {
        $token = $this->getAuthToken();
        $user = $this->getCurrentTenantUser($token);
        return $user !== false;
    }
    
    /**
     * 兼容性方法：获取当前用户信息
     * 替代原来的 getCurrentUser() 方法
     */
    public function getCurrentUser() {
        $token = $this->getAuthToken();
        return $this->getCurrentTenantUser($token);
    }
    
    /**
     * 获取用户上下文信息 - 为中间件提供
     */
    public function getUserContext() {
        try {
            $user = $this->getCurrentUser();
            if (!$user) {
                return null;
            }
            
            $context = array(
                'user_id' => $user['id'],
                'username' => $user['username'],
                'user_type' => $user['user_type'],
                'domain_info' => $this->domainInfo
            );
            
            // 添加平台信息
            if ($this->domainInfo && isset($this->domainInfo['platform_id'])) {
                $context['platform_id'] = $this->domainInfo['platform_id'];
                $context['platform_name'] = isset($this->domainInfo['platform_name']) ? $this->domainInfo['platform_name'] : null;
                $context['platform_code'] = isset($this->domainInfo['platform_code']) ? $this->domainInfo['platform_code'] : null;
            }
            
            // 添加租户信息
            if ($this->domainInfo) {
                $context['tenant_type'] = $this->domainInfo['tenant_type'];
                $context['tenant_id'] = $this->domainInfo['tenant_id'];
            }
            
            return $context;
            
        } catch (Exception $e) {
            error_log("TenantAuth::getUserContext - 异常: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 兼容性方法：检查权限
     * 替代原来的 checkPermission() 方法
     */
    public function checkPermission($permission) {
        $user = $this->getCurrentUser();
        if (!$user) return false;
        
        return $this->hasTenantPermission($user, $permission);
    }
    
    /**
     * 兼容性方法：要求管理员权限
     * 替代原来的 requireAdmin() 方法
     */
    public function requireAdmin() {
        if (!$this->checkAuth()) {
            $this->sendUnauthorized('未登录');
            return false;
        }
        return true;
    }
    
    /**
     * 兼容性方法：发送未授权响应
     */
    public function sendUnauthorized($message = '未授权访问') {
        http_response_code(401);
        header('Content-Type: application/json');
        echo json_encode(['code' => 401, 'message' => $message]);
        exit;
    }
    
    /**
     * 兼容性方法：发送禁止访问响应
     */
    public function sendForbidden($message = '权限不足') {
        http_response_code(403);
        header('Content-Type: application/json');
        echo json_encode(['code' => 403, 'message' => $message]);
        exit;
    }
    
    /**
     * 兼容性方法：发送成功响应
     */
    public function sendSuccess($data = null, $message = '操作成功') {
        header('Content-Type: application/json');
        $response = ['code' => 200, 'message' => $message];
        if ($data !== null) {
            $response['data'] = $data;
        }
        echo json_encode($response);
        exit;
    }
    
    /**
     * 兼容性方法：发送错误响应
     */
    public function sendError($message = '操作失败', $code = 400) {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode(['code' => $code, 'message' => $message]);
        exit;
    }
    
    /**
     * 兼容性方法：记录操作日志
     * 替代原来的 logUserAction() 方法
     */
    public function logUserAction($action, $targetType, $targetId, $description) {
        $user = $this->getCurrentUser();
        if ($user) {
            $this->logTenantAction($user['id'], $action, $targetType, $targetId, $description);
        }
    }
}
?> 