<?php
/**
 * 多租户认证接口 - 四层架构版本
 * 支持基于域名的多租户登录和权限验证
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 引入多租户认证系统
require_once dirname(dirname(dirname(__FILE__))) . '/utils/TenantAuth.php';
require_once dirname(dirname(__FILE__)) . '/utils/ErrorHandler.php';

// 设置全局异常处理器
ErrorHandler::setGlobalExceptionHandler();

// 获取全局租户上下文（从admin.php传递）
$tenantAuth = isset($GLOBALS['tenant_auth']) ? $GLOBALS['tenant_auth'] : new TenantAuth();
$domainInfo = isset($GLOBALS['domain_info']) ? $GLOBALS['domain_info'] : $tenantAuth->getDomainInfo();
$tenantContext = isset($GLOBALS['tenant_context']) ? $GLOBALS['tenant_context'] : $domainInfo;

// 获取Authorization头（兼容FastCGI）
if (!function_exists('getAllHeaders')) {
    function getAllHeaders() {
        $headers = array();
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) == 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }
        return $headers;
    }
}

$headers = getAllHeaders();
$token = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = str_replace('Bearer ', '', $token);

// 获取请求参数
$input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
$action = isset($input['action']) ? $input['action'] : (isset($_GET['action']) ? $_GET['action'] : '');

try {
    switch ($action) {
        case 'login':
            handleTenantLogin();
            break;
            
        case 'logout':
            handleTenantLogout();
            break;
            
        case 'check_auth':
            handleCheckAuth();
            break;
            
        case 'get_tenant_info':
            handleGetTenantInfo();
            break;
            
        default:
            http_response_code(404);
            echo json_encode(array('code' => 404, 'message' => '接口不存在'));
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(array('code' => 500, 'message' => '服务器错误: ' . $e->getMessage()));
}

/**
 * 处理租户登录
 */
function handleTenantLogin() {
    global $tenantAuth, $domainInfo;
    
    $input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
    
    if (!$input || !isset($input['username']) || !isset($input['password'])) {
        http_response_code(400);
        echo json_encode(array('code' => 400, 'message' => '用户名和密码不能为空'));
        return;
    }
    
    $username = trim($input['username']);
    $password = $input['password'];
    
    if (empty($username) || empty($password)) {
        http_response_code(400);
        echo json_encode(array('code' => 400, 'message' => '用户名和密码不能为空'));
        return;
    }
    
    // 使用TenantAuth的租户登录方法
    $loginResult = $tenantAuth->tenantLogin($username, $password);
    
    if (!$loginResult['success']) {
        http_response_code(401);
        echo json_encode(array(
            'code' => 401, 
            'message' => $loginResult['message']
        ));
        return;
    }
    
    // 构建响应数据
    $responseData = array(
        'token' => $loginResult['token'],
        'user' => $loginResult['user'],
        'tenant' => $loginResult['tenant'],
        'permissions' => getTenantUserPermissions($loginResult['user'], $domainInfo),
        'domain_info' => $domainInfo
    );
    
    echo json_encode(array(
        'code' => 200,
        'message' => '登录成功',
        'data' => $responseData
    ));
}

/**
 * 处理租户登出
 */
function handleTenantLogout() {
    global $tenantAuth;
    
    $headers = getAllHeaders();
    $token = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = str_replace('Bearer ', '', $token);
    
    if ($token) {
        $logoutResult = $tenantAuth->tenantLogout($token);
        
        if ($logoutResult['success']) {
            echo json_encode(array(
                'code' => 200,
                'message' => '登出成功'
            ));
        } else {
            echo json_encode(array(
                'code' => 400,
                'message' => $logoutResult['message']
            ));
        }
    } else {
        echo json_encode(array(
            'code' => 200,
            'message' => '登出成功'
        ));
    }
}

/**
 * 检查认证状态
 */
function handleCheckAuth() {
    global $tenantAuth, $domainInfo;
    
    $headers = getAllHeaders();
    $token = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    $token = str_replace('Bearer ', '', $token);
    
    if (!$token) {
        http_response_code(401);
        echo json_encode(array(
            'code' => 401,
            'message' => '未提供认证token'
        ));
        return;
    }
    
    $currentUser = $tenantAuth->getCurrentTenantUser($token);
    
    if (!$currentUser) {
        http_response_code(401);
        echo json_encode(array(
            'code' => 401,
            'message' => '认证失败，请重新登录'
        ));
        return;
    }
    
    // 验证token
    $tokenData = $tenantAuth->verifyTenantToken($token);
    
    if (!$tokenData) {
        http_response_code(401);
        echo json_encode(array(
            'code' => 401,
            'message' => 'Token无效，请重新登录'
        ));
        return;
    }
    
    echo json_encode(array(
        'code' => 200,
        'message' => '认证有效',
        'data' => array(
            'user' => $currentUser,
            'tenant' => array(
                'type' => $domainInfo['tenant_type'],
                'id' => $domainInfo['tenant_id'],
                'platform_id' => $domainInfo['platform_id'],
                'domain' => $_SERVER['HTTP_HOST']
            ),
            'permissions' => getTenantUserPermissions($currentUser, $domainInfo),
            'token_data' => $tokenData
        )
    ));
}

/**
 * 获取租户信息
 */
function handleGetTenantInfo() {
    global $domainInfo;
    
    echo json_encode(array(
        'code' => 200,
        'message' => '获取成功',
        'data' => $domainInfo
    ));
}

// getTenantUserPermissions函数已在admin.php中定义，无需重复定义
?> 