<?php
/**
 * API日志记录器
 * 提供请求日志、性能监控、错误追踪、安全审计等功能
 * 
 * <AUTHOR> System Team
 * @version 1.0.0
 * @since Day 14
 */

class ApiLogger {
    private $config;
    private $loggers;
    private $requestId;
    private $startTime;
    private $contextData;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->config = $this->loadConfig();
        $this->loggers = array();
        $this->requestId = $this->generateRequestId();
        $this->startTime = microtime(true);
        $this->contextData = array();
        
        $this->initializeLoggers();
        $this->logRequestStart();
    }
    
    /**
     * 加载配置
     */
    private function loadConfig() {
        return array(
            'enabled' => true,
            'log_level' => 'INFO', // DEBUG, INFO, WARN, ERROR
            'log_dir' => dirname(dirname(__FILE__)) . '/logs',
            'max_file_size' => 10 * 1024 * 1024, // 10MB
            'max_files' => 30,
            'loggers' => array(
                'request' => array(
                    'enabled' => true,
                    'file' => 'api_requests.log',
                    'format' => 'json'
                ),
                'performance' => array(
                    'enabled' => true,
                    'file' => 'api_performance.log',
                    'format' => 'json'
                ),
                'error' => array(
                    'enabled' => true,
                    'file' => 'api_errors.log',
                    'format' => 'json'
                ),
                'security' => array(
                    'enabled' => true,
                    'file' => 'api_security.log',
                    'format' => 'json'
                ),
                'debug' => array(
                    'enabled' => true,
                    'file' => 'api_debug.log',
                    'format' => 'json'
                )
            ),
            'sensitive_fields' => array(
                'password', 'token', 'api_key', 'secret', 'authorization'
            ),
            'performance_thresholds' => array(
                'slow_query' => 1000, // 1秒
                'memory_limit' => 50 * 1024 * 1024, // 50MB
                'response_size_limit' => 5 * 1024 * 1024 // 5MB
            )
        );
    }
    
    /**
     * 初始化日志记录器
     */
    private function initializeLoggers() {
        // 确保日志目录存在
        if (!is_dir($this->config['log_dir'])) {
            mkdir($this->config['log_dir'], 0755, true);
        }
        
        // 初始化各种日志记录器
        foreach ($this->config['loggers'] as $type => $config) {
            if ($config['enabled']) {
                $this->loggers[$type] = new FileLogger(
                    $this->config['log_dir'] . '/' . $config['file'],
                    $config['format'],
                    $this->config['max_file_size'],
                    $this->config['max_files']
                );
            }
        }
    }
    
    /**
     * 记录请求开始
     */
    private function logRequestStart() {
        $this->contextData = array(
            'request_id' => $this->requestId,
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'UNKNOWN',
            'uri' => isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '',
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'ip' => $this->getClientIp(),
            'headers' => $this->sanitizeHeaders($this->getAllHeaders()),
            'query_params' => $_GET,
            'body_size' => isset($_SERVER['CONTENT_LENGTH']) ? $_SERVER['CONTENT_LENGTH'] : 0);
        
        $this->log('request', 'INFO', 'Request started', $this->contextData);
    }
    
    /**
     * 记录请求结束
     */
    public function logRequestEnd($responseCode, $responseSize = 0, $additionalData = array()) {
        $endTime = microtime(true);
        $duration = ($endTime - $this->startTime) * 1000; // 转换为毫秒
        
        $data = array_merge($this->contextData, array(
            'response_code' => $responseCode,
            'response_size' => $responseSize,
            'duration_ms' => round($duration, 2),
            'memory_usage' => memory_get_peak_usage(true),
            'memory_usage_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'end_timestamp' => date('Y-m-d H:i:s')
        ), $additionalData);
        
        $this->log('request', 'INFO', 'Request completed', $data);
        
        // 记录性能数据
        $this->logPerformance($data);
        
        // 检查性能阈值
        $this->checkPerformanceThresholds($data);
    }
    
    /**
     * 记录性能数据
     */
    private function logPerformance($data) {
        $performanceData = array(
            'request_id' => $this->requestId,
            'endpoint' => $this->getEndpoint(),
            'method' => $data['method'],
            'duration_ms' => $data['duration_ms'],
            'memory_usage_mb' => $data['memory_usage_mb'],
            'response_size' => $data['response_size'],
            'response_code' => $data['response_code'],
            'timestamp' => $data['timestamp'],
            'ip' => $data['ip']
        );
        
        $this->log('performance', 'INFO', 'Performance metrics', $performanceData);
    }
    
    /**
     * 检查性能阈值
     */
    private function checkPerformanceThresholds($data) {
        $thresholds = $this->config['performance_thresholds'];
        $warnings = array();
        
        // 检查响应时间
        if ($data['duration_ms'] > $thresholds['slow_query']) {
            $warnings[] = "Slow response: {$data['duration_ms']}ms";
        }
        
        // 检查内存使用
        if ($data['memory_usage'] > $thresholds['memory_limit']) {
            $warnings[] = "High memory usage: {$data['memory_usage_mb']}MB";
        }
        
        // 检查响应大小
        if ($data['response_size'] > $thresholds['response_size_limit']) {
            $warnings[] = "Large response: " . $this->formatBytes($data['response_size']);
        }
        
        if (!empty($warnings)) {
            $this->log('performance', 'WARN', 'Performance threshold exceeded', array(
                'request_id' => $this->requestId,
                'warnings' => $warnings,
                'data' => $data
            ));
        }
    }
    
    /**
     * 记录错误
     */
    public function logError($level, $message, $context = array(), $exception = null) {
        $errorData = array(
            'request_id' => $this->requestId,
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'timestamp' => date('Y-m-d H:i:s'),
            'endpoint' => $this->getEndpoint(),
            'method' => isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'UNKNOWN',
            'ip' => $this->getClientIp()
        );
        
        if ($exception) {
            $errorData['exception'] = array(
                'class' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            );
        }
        
        $this->log('error', $level, $message, $errorData);
        
        // 严重错误也记录到系统错误日志
        if (in_array($level, array('ERROR', 'CRITICAL'))) {
            error_log("API Error [{$this->requestId}]: {$message}");
        }
    }
    
    /**
     * 记录安全事件
     */
    public function logSecurity($event, $description, $severity = 'MEDIUM', $additionalData = array()) {
        $securityData = array(
            'request_id' => $this->requestId,
            'event' => $event,
            'description' => $description,
            'severity' => $severity,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getClientIp(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'endpoint' => $this->getEndpoint(),
            'method' => isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'UNKNOWN');
        
        $securityData = array_merge($securityData, $additionalData);
        
        $this->log('security', 'WARN', "Security event: {$event}", $securityData);
    }
    
    /**
     * 记录调试信息
     */
    public function logDebug($message, $data = array()) {
        if ($this->config['log_level'] === 'DEBUG') {
            $debugData = array(
                'request_id' => $this->requestId,
                'message' => $message,
                'data' => $data,
                'timestamp' => date('Y-m-d H:i:s'),
                'memory_usage' => memory_get_usage(true),
                'execution_time' => (microtime(true) - $this->startTime) * 1000
            );
            
            $this->log('debug', 'DEBUG', $message, $debugData);
        }
    }
    
    /**
     * 记录数据库查询
     */
    public function logDatabaseQuery($query, $params = array(), $duration = 0, $error = null) {
        $queryData = array(
            'request_id' => $this->requestId,
            'query' => $this->sanitizeQuery($query),
            'params' => $this->sanitizeParams($params),
            'duration_ms' => round($duration * 1000, 2),
            'timestamp' => date('Y-m-d H:i:s')
        );
        
        if ($error) {
            $queryData['error'] = $error;
            $this->log('error', 'ERROR', 'Database query failed', $queryData);
        } else {
            $this->log('debug', 'DEBUG', 'Database query executed', $queryData);
        }
        
        // 记录慢查询
        if ($duration > ($this->config['performance_thresholds']['slow_query'] / 1000)) {
            $this->log('performance', 'WARN', 'Slow database query', $queryData);
        }
    }
    
    /**
     * 记录缓存操作
     */
    public function logCache($operation, $key, $hit = null, $ttl = null) {
        $cacheData = array(
            'request_id' => $this->requestId,
            'operation' => $operation,
            'key' => $key,
            'timestamp' => date('Y-m-d H:i:s')
        );
        
        if ($hit !== null) {
            $cacheData['hit'] = $hit;
        }
        
        if ($ttl !== null) {
            $cacheData['ttl'] = $ttl;
        }
        
        $this->log('debug', 'DEBUG', "Cache {$operation}", $cacheData);
    }
    
    /**
     * 记录用户操作
     */
    public function logUserAction($userId, $action, $resource = null, $details = array()) {
        $actionData = array(
            'request_id' => $this->requestId,
            'user_id' => $userId,
            'action' => $action,
            'resource' => $resource,
            'details' => $details,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getClientIp(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '');
        
        $this->log('request', 'INFO', "User action: {$action}", $actionData);
    }
    
    /**
     * 通用日志记录方法
     */
    private function log($type, $level, $message, $data = array()) {
        if (!$this->config['enabled'] || !isset($this->loggers[$type])) {
            return;
        }
        
        $logEntry = array(
            'level' => $level,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s.u'),
            'request_id' => $this->requestId
        );
        
        $this->loggers[$type]->write($logEntry);
    }
    
    /**
     * 清理敏感数据
     */
    private function sanitizeData($data) {
        if (!is_array($data)) {
            return $data;
        }
        
        $sanitized = array();
        foreach ($data as $key => $value) {
            $lowerKey = strtolower($key);
            
            if (in_array($lowerKey, $this->config['sensitive_fields'])) {
                $sanitized[$key] = '[REDACTED]';
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitizeData($value);
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * 清理HTTP头
     */
    private function sanitizeHeaders($headers) {
        return $this->sanitizeData($headers);
    }
    
    /**
     * 清理查询参数
     */
    private function sanitizeParams($params) {
        return $this->sanitizeData($params);
    }
    
    /**
     * 清理SQL查询
     */
    private function sanitizeQuery($query) {
        // 移除多余的空白字符
        $query = preg_replace('/\s+/', ' ', trim($query));
        
        // 限制查询长度
        if (strlen($query) > 1000) {
            $query = substr($query, 0, 1000) . '... [TRUNCATED]';
        }
        
        return $query;
    }
    
    /**
     * 获取所有HTTP头
     */
    private function getAllHeaders() {
        $headers = array();
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) == 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
            }
        }
        return $headers;
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        $headers = array('HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP');
        
        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                return trim($ips[0]);
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
    }
    
    /**
     * 获取端点
     */
    private function getEndpoint() {
        $uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        $action = isset($_GET['action']) ? $_GET['action'] : 'unknown';
        return $action;
    }
    
    /**
     * 生成请求ID
     */
    private function generateRequestId() {
        return uniqid('req_', true);
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * 获取请求ID
     */
    public function getRequestId() {
        return $this->requestId;
    }
    
    /**
     * 添加上下文数据
     */
    public function addContext($key, $value) {
        $this->contextData[$key] = $value;
    }
    
    /**
     * 获取日志统计
     */
    public function getStats() {
        $stats = array();
        
        foreach ($this->loggers as $type => $logger) {
            $stats[$type] = $logger->getStats();
        }
        
        return $stats;
    }
    
    /**
     * 清理旧日志
     */
    public function cleanupOldLogs($days = 30) {
        $cutoff = time() - ($days * 24 * 60 * 60);
        
        foreach ($this->config['loggers'] as $type => $config) {
            $logFile = $this->config['log_dir'] . '/' . $config['file'];
            
            if (file_exists($logFile) && filemtime($logFile) < $cutoff) {
                unlink($logFile);
            }
        }
    }
}

/**
 * 文件日志记录器
 */
class FileLogger {
    private $file;
    private $format;
    private $maxSize;
    private $maxFiles;
    private $stats;
    
    public function __construct($file, $format = 'json', $maxSize = 10485760, $maxFiles = 30) {
        $this->file = $file;
        $this->format = $format;
        $this->maxSize = $maxSize;
        $this->maxFiles = $maxFiles;
        $this->stats = array(
            'entries' => 0,
            'size' => 0,
            'last_write' => null
        );
    }
    
    /**
     * 写入日志
     */
    public function write($entry) {
        // 检查文件大小并轮转
        $this->rotateIfNeeded();
        
        // 格式化日志条目
        $formatted = $this->formatEntry($entry);
        
        // 写入文件
        if (file_put_contents($this->file, $formatted . "\n", FILE_APPEND | LOCK_EX) !== false) {
            $this->stats['entries']++;
            $this->stats['size'] = filesize($this->file);
            $this->stats['last_write'] = time();
        }
    }
    
    /**
     * 格式化日志条目
     */
    private function formatEntry($entry) {
        switch ($this->format) {
            case 'json':
                return json_encode($entry, JSON_UNESCAPED_UNICODE);
                
            case 'text':
                return sprintf(
                    "[%s] %s: %s %s",
                    $entry['timestamp'],
                    $entry['level'],
                    $entry['message'],
                    json_encode($entry['data'], JSON_UNESCAPED_UNICODE)
                );
                
            default:
                return json_encode($entry, JSON_UNESCAPED_UNICODE);
        }
    }
    
    /**
     * 轮转日志文件
     */
    private function rotateIfNeeded() {
        if (!file_exists($this->file) || filesize($this->file) < $this->maxSize) {
            return;
        }
        
        // 轮转现有文件
        for ($i = $this->maxFiles - 1; $i > 0; $i--) {
            $oldFile = $this->file . '.' . $i;
            $newFile = $this->file . '.' . ($i + 1);
            
            if (file_exists($oldFile)) {
                if ($i == $this->maxFiles - 1) {
                    unlink($oldFile); // 删除最老的文件
                } else {
                    rename($oldFile, $newFile);
                }
            }
        }
        
        // 重命名当前文件
        rename($this->file, $this->file . '.1');
    }
    
    /**
     * 获取统计信息
     */
    public function getStats() {
        if (file_exists($this->file)) {
            $this->stats['size'] = filesize($this->file);
        }
        
        return $this->stats;
    }
}
?> 