<?php
/**
 * 手机端账单上报API
 * 
 * 功能：
 * 1. 接收手机端上报的账单数据
 * 2. 存储账单信息
 * 3. 自动匹配收款指令
 * 4. 更新指令状态
 * 
 * <AUTHOR> System
 * @version 1.0
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once dirname(dirname(dirname(__FILE__))) . '/config/database.php';
require_once dirname(dirname(__FILE__)) . '/core/PaymentInstructionManager.php';

/**
 * 统一响应格式
 */
function jsonResponse($code, $message, $data = null) {
    echo json_encode([
        'code' => $code,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 验证设备权限
 */
function validateDevice($deviceId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT d.*, aa.id as alipay_account_id
            FROM devices d
            LEFT JOIN alipay_accounts aa ON d.id = aa.device_id AND aa.status = 'approved'
            WHERE d.device_id = ? AND d.status = 'active'
        ");
        $stmt->execute([$deviceId]);
        $device = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $device ? $device : false;
        
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 保存账单数据
 */
function saveBillData($alipayAccountId, $instructionId, $billData, $deviceId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO alipay_bills (
                alipay_account_id,
                instruction_id,
                bill_id,
                transaction_id,
                amount,
                transaction_type,
                counterpart,
                transaction_time,
                description,
                raw_data,
                device_id,
                uploaded_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $alipayAccountId,
            $instructionId,
            isset($billData['bill_id']) ? $billData['bill_id'] : null,
            isset($billData['transaction_id']) ? $billData['transaction_id'] : null,
            $billData['amount'],
            isset($billData['transaction_type']) ? $billData['transaction_type'] : 'income',
            isset($billData['counterpart']) ? $billData['counterpart'] : '',
            $billData['transaction_time'],
            isset($billData['description']) ? $billData['description'] : '',
            json_encode($billData, JSON_UNESCAPED_UNICODE),
            $deviceId
        ]);
        
        return $pdo->lastInsertId();
        
    } catch (Exception $e) {
        error_log("Failed to save bill data: " . $e->getMessage());
        return false;
    }
}

/**
 * 匹配收款指令
 */
function matchPaymentInstruction($alipayAccountId, $amount, $transactionTime) {
    global $pdo;
    
    try {
        // 查找匹配的指令（优先匹配用户应付金额，其次匹配订单金额）
        $stmt = $pdo->prepare("
            SELECT instruction_id, target_amount, payable_amount, amount_tolerance
            FROM payment_instructions 
            WHERE alipay_account_id = ? 
            AND status IN ('pending', 'executing')
            AND expires_at > NOW()
            AND (
                ABS(payable_amount - ?) <= amount_tolerance + 0.01
                OR ABS(target_amount - ?) <= amount_tolerance + 0.01
            )
            ORDER BY 
                CASE 
                    WHEN ABS(payable_amount - ?) <= amount_tolerance THEN 1
                    WHEN ABS(target_amount - ?) <= amount_tolerance THEN 2
                    ELSE 3
                END,
                created_at ASC
            LIMIT 1
        ");
        
        $stmt->execute([$alipayAccountId, $amount, $amount, $amount, $amount]);
        $instruction = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $instruction ? $instruction : false;
        
    } catch (Exception $e) {
        error_log("Failed to match payment instruction: " . $e->getMessage());
        return false;
    }
}

/**
 * 更新账单匹配状态
 */
function updateBillMatchStatus($billId, $instructionId, $matchedOrderId = null) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            UPDATE alipay_bills 
            SET match_status = 'matched',
                matched_order_id = ?,
                matched_at = NOW(),
                matched_by = 'auto'
            WHERE id = ?
        ");
        
        $stmt->execute([$matchedOrderId, $billId]);
        
        return $stmt->rowCount() > 0;
        
    } catch (Exception $e) {
        error_log("Failed to update bill match status: " . $e->getMessage());
        return false;
    }
}

// 主程序开始
try {
    // 只接受POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        jsonResponse(405, '只支持POST请求');
    }
    
    $db = new Database();
    $pdo = $db->connect();
    
    // 获取输入数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(400, '无效的JSON数据');
    }
    
    // 验证必需参数
    $deviceId = isset($input['device_id']) ? trim($input['device_id']) : '';
    $instructionId = isset($input['instruction_id']) ? trim($input['instruction_id']) : '';
    $bills = isset($input['bills']) ? $input['bills'] : [];
    
    if (empty($deviceId)) {
        jsonResponse(400, '缺少设备ID参数');
    }
    
    if (empty($instructionId)) {
        jsonResponse(400, '缺少指令ID参数');
    }
    
    if (empty($bills) || !is_array($bills)) {
        jsonResponse(400, '缺少账单数据或格式错误');
    }
    
    // 验证设备
    $device = validateDevice($deviceId);
    if (!$device) {
        jsonResponse(401, '设备未授权或不存在');
    }
    
    $alipayAccountId = $device['alipay_account_id'];
    if (!$alipayAccountId) {
        jsonResponse(401, '设备未关联有效的支付宝账户');
    }
    
    // 获取指令管理器
    $instructionManager = new PaymentInstructionManager();
    
    // 验证指令是否存在且属于该设备
    $instructionResult = $instructionManager->getInstructionDetails($instructionId);
    if (!$instructionResult['success']) {
        jsonResponse(404, '指令不存在: ' . $instructionResult['error']);
    }
    
    $instruction = $instructionResult['instruction'];
    if ($instruction['alipay_account_id'] != $alipayAccountId) {
        jsonResponse(403, '指令不属于当前设备');
    }
    
    // 处理账单数据
    $savedBills = [];
    $matchedBills = [];
    $targetAmount = $instruction['target_amount'];
    $payableAmount = $instruction['payable_amount'];
    $foundMatch = false;
    $foundAmount = null;
    
    foreach ($bills as $billData) {
        // 验证账单数据格式
        if (!isset($billData['amount']) || !isset($billData['transaction_time'])) {
            continue; // 跳过格式不正确的账单
        }
        
        $amount = floatval($billData['amount']);
        $transactionTime = $billData['transaction_time'];
        
        // 保存账单数据
        $billId = saveBillData($alipayAccountId, $instructionId, $billData, $deviceId);
        if ($billId) {
            $savedBills[] = [
                'bill_id' => $billId,
                'amount' => $amount,
                'transaction_time' => $transactionTime
            ];
            
            // 检查是否匹配目标金额
            if (!$foundMatch) {
                $tolerance = 0.01; // 1分钱容错
                
                // 优先匹配用户应付金额
                if (abs($amount - $payableAmount) <= $tolerance) {
                    $foundMatch = true;
                    $foundAmount = $amount;
                    $matchedBills[] = [
                        'bill_id' => $billId,
                        'amount' => $amount,
                        'match_type' => 'payable_amount'
                    ];
                    
                    // 更新账单匹配状态
                    updateBillMatchStatus($billId, $instructionId, $instruction['payment_request_id']);
                }
                // 其次匹配订单金额
                elseif (abs($amount - $targetAmount) <= $tolerance) {
                    $foundMatch = true;
                    $foundAmount = $amount;
                    $matchedBills[] = [
                        'bill_id' => $billId,
                        'amount' => $amount,
                        'match_type' => 'target_amount'
                    ];
                    
                    // 更新账单匹配状态
                    updateBillMatchStatus($billId, $instructionId, $instruction['payment_request_id']);
                }
            }
        }
    }
    
    // 更新指令状态
    $executionResult = $foundMatch ? PaymentInstructionManager::RESULT_FOUND : PaymentInstructionManager::RESULT_NOT_FOUND;
    $newStatus = $foundMatch ? PaymentInstructionManager::STATUS_COMPLETED : PaymentInstructionManager::STATUS_EXECUTING;
    
    $updateResult = $instructionManager->updateInstructionStatus(
        $instructionId,
        $newStatus,
        $executionResult,
        [
            'found_amount' => $foundAmount,
            'bills_count' => count($savedBills),
            'bills_data' => $savedBills,
            'device_response' => [
                'device_id' => $deviceId,
                'reported_at' => date('Y-m-d H:i:s'),
                'total_bills' => count($bills),
                'saved_bills' => count($savedBills),
                'matched_bills' => count($matchedBills)
            ]
        ]
    );
    
    // 记录日志
    error_log("Bill report: Device {$deviceId}, Instruction {$instructionId}, Bills: " . count($savedBills) . ", Matched: " . ($foundMatch ? 'Yes' : 'No'));
    
    // 返回响应
    jsonResponse(200, '账单上报成功', [
        'instruction_id' => $instructionId,
        'bills_processed' => count($bills),
        'bills_saved' => count($savedBills),
        'match_found' => $foundMatch,
        'matched_amount' => $foundAmount,
        'matched_bills' => $matchedBills,
        'instruction_status' => $newStatus,
        'execution_result' => $executionResult,
        'target_amounts' => [
            'order_amount' => $targetAmount,
            'payable_amount' => $payableAmount
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Bill report error: " . $e->getMessage());
    jsonResponse(500, '服务器内部错误: ' . $e->getMessage());
}
?> 