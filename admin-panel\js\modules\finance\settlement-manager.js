/**
 * 结算管理器 - 四层架构财务系统核心模块
 * 负责平台→码商→商户的多层级结算管理和自动化结算
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class SettlementManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.tenantInfo = window.TENANT_INFO;
        
        this.currentPage = 1;
        this.pageSize = 20;
        this.settlements = [];
        this.settlementTypes = ['auto', 'manual', 'scheduled'];
        this.settlementStatus = ['pending', 'processing', 'completed', 'failed', 'cancelled'];
        
        console.log('💰 SettlementManager initialized for tenant:', this.tenantInfo?.tenant_type);
    }

    /**
     * 初始化结算管理器
     */
    async init(container) {
        try {
            this.container = container;
            await this.renderSettlementPage();
            await this.loadSettlements();
            await this.loadSettlementStats();
            this.bindEvents();
            this.startAutoRefresh();
            
            console.log('✅ 结算管理器初始化完成');
        } catch (error) {
            console.error('❌ 结算管理器初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    /**
     * 渲染结算管理页面
     */
    async renderSettlementPage() {
        this.container.innerHTML = `
            <div class="settlement-manager">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="bi bi-bank me-2"></i>结算管理</h2>
                            <p class="text-muted mb-0">管理平台、码商、商户的资金结算和自动化结算</p>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-success" onclick="settlementManager.showCreateSettlementModal()">
                                <i class="bi bi-plus me-2"></i>创建结算
                            </button>
                            <button class="btn btn-primary" onclick="settlementManager.showBatchSettlementModal()">
                                <i class="bi bi-collection me-2"></i>批量结算
                            </button>
                            <button class="btn btn-outline-info" onclick="settlementManager.exportSettlements()">
                                <i class="bi bi-download me-2"></i>导出记录
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 结算概览统计 -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-clock-history"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="pendingCount">0</div>
                                <div class="stat-label">待处理</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-arrow-repeat"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="processingCount">0</div>
                                <div class="stat-label">处理中</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="completedCount">0</div>
                                <div class="stat-label">已完成</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-danger">
                                <i class="bi bi-x-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="failedCount">0</div>
                                <div class="stat-label">失败</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="todayAmount">¥0</div>
                                <div class="stat-label">今日结算</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="stat-card">
                            <div class="stat-icon bg-secondary">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalAmount">¥0</div>
                                <div class="stat-label">总结算额</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时结算监控 -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-activity me-2"></i>实时结算监控
                                    <span class="badge bg-success ms-2" id="onlineStatus">在线</span>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-primary" id="currentProcessing">0</div>
                                            <div class="metric-label">正在处理</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-success" id="successRate">0%</div>
                                            <div class="metric-label">成功率</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-info" id="avgProcessTime">0s</div>
                                            <div class="metric-label">平均处理时间</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="metric">
                                            <div class="metric-value text-warning" id="queueLength">0</div>
                                            <div class="metric-label">队列长度</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-gear me-2"></i>自动结算设置
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="autoSettlementEnabled">
                                    <label class="form-check-label" for="autoSettlementEnabled">
                                        启用自动结算
                                    </label>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">结算周期</label>
                                    <select class="form-select form-select-sm" id="settlementCycle">
                                        <option value="daily">每日结算</option>
                                        <option value="weekly">每周结算</option>
                                        <option value="monthly">每月结算</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">结算时间</label>
                                    <input type="time" class="form-control form-control-sm" id="settlementTime" value="02:00">
                                </div>
                                <button class="btn btn-outline-primary btn-sm w-100" onclick="settlementManager.saveAutoSettings()">
                                    <i class="bi bi-check me-2"></i>保存设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 过滤和搜索 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label">结算类型</label>
                                <select class="form-select" id="filterSettlementType">
                                    <option value="">全部类型</option>
                                    <option value="auto">自动结算</option>
                                    <option value="manual">手动结算</option>
                                    <option value="scheduled">定时结算</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">结算状态</label>
                                <select class="form-select" id="filterStatus">
                                    <option value="">全部状态</option>
                                    <option value="pending">待处理</option>
                                    <option value="processing">处理中</option>
                                    <option value="completed">已完成</option>
                                    <option value="failed">失败</option>
                                    <option value="cancelled">已取消</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">对象类型</label>
                                <select class="form-select" id="filterTargetType">
                                    <option value="">全部对象</option>
                                    <option value="provider">码商</option>
                                    <option value="merchant">商户</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">时间范围</label>
                                <select class="form-select" id="filterTimeRange">
                                    <option value="today">今天</option>
                                    <option value="yesterday">昨天</option>
                                    <option value="week">本周</option>
                                    <option value="month">本月</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">金额范围</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="minAmount" placeholder="最小">
                                    <input type="number" class="form-control" id="maxAmount" placeholder="最大">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">操作</label>
                                <div class="d-flex gap-1">
                                    <button class="btn btn-outline-primary" onclick="settlementManager.applyFilters()">
                                        <i class="bi bi-search"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="settlementManager.resetFilters()">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3" id="customTimeRange" style="display: none;">
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="endDate">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结算记录列表 -->
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">结算记录列表</h6>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="settlementManager.refreshList()" title="刷新">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                                <button class="btn btn-outline-success" onclick="settlementManager.batchApprove()" title="批量审批">
                                    <i class="bi bi-check-all"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="settlementManager.batchCancel()" title="批量取消">
                                    <i class="bi bi-x-octagon"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAll" onchange="settlementManager.toggleSelectAll()">
                                        </th>
                                        <th>结算单号</th>
                                        <th>结算对象</th>
                                        <th>结算类型</th>
                                        <th>结算金额</th>
                                        <th>手续费</th>
                                        <th>实际到账</th>
                                        <th>创建时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="settlementTableBody">
                                    <tr>
                                        <td colspan="10" class="text-center py-4">
                                            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                            加载中...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="text-muted">
                                显示 <span id="pageInfo">0-0</span> 条，共 <span id="totalItems">0</span> 条
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="pagination">
                                    <!-- 分页将动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 创建结算模态框 -->
            <div class="modal fade" id="createSettlementModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-plus me-2"></i>创建结算
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createSettlementForm">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">结算对象类型 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="targetType" required>
                                            <option value="">请选择对象类型</option>
                                            <option value="provider">码商</option>
                                            <option value="merchant">商户</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">结算对象 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="targetId" required>
                                            <option value="">请先选择对象类型</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">结算类型 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="settlementType" required>
                                            <option value="manual">手动结算</option>
                                            <option value="scheduled">定时结算</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">结算周期</label>
                                        <select class="form-select" id="settlementPeriod">
                                            <option value="today">今日</option>
                                            <option value="yesterday">昨日</option>
                                            <option value="week">本周</option>
                                            <option value="month">本月</option>
                                            <option value="custom">自定义</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">开始时间</label>
                                        <input type="datetime-local" class="form-control" id="startTime">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">结束时间</label>
                                        <input type="datetime-local" class="form-control" id="endTime">
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">结算说明</label>
                                        <textarea class="form-control" id="settlementNote" rows="3" placeholder="结算备注信息..."></textarea>
                                    </div>
                                </div>
                                
                                <!-- 结算预览 -->
                                <div class="mt-4" id="settlementPreview" style="display: none;">
                                    <h6>结算预览</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <tbody id="previewTableBody">
                                                <!-- 预览数据将动态生成 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-info" onclick="settlementManager.previewSettlement()">
                                <i class="bi bi-eye me-2"></i>预览结算
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-success" onclick="settlementManager.createSettlement()">
                                <i class="bi bi-check me-2"></i>创建结算
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量结算模态框 -->
            <div class="modal fade" id="batchSettlementModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-collection me-2"></i>批量结算
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">批量设置</h6>
                                        </div>
                                        <div class="card-body">
                                            <form id="batchSettlementForm">
                                                <div class="mb-3">
                                                    <label class="form-label">对象类型</label>
                                                    <select class="form-select" id="batchTargetType">
                                                        <option value="provider">码商</option>
                                                        <option value="merchant">商户</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">结算周期</label>
                                                    <select class="form-select" id="batchPeriod">
                                                        <option value="today">今日</option>
                                                        <option value="yesterday">昨日</option>
                                                        <option value="week">本周</option>
                                                        <option value="month">本月</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">最小结算金额</label>
                                                    <input type="number" class="form-control" id="minSettlementAmount" value="100" step="0.01">
                                                </div>
                                                <button type="button" class="btn btn-primary w-100" onclick="settlementManager.loadBatchTargets()">
                                                    <i class="bi bi-search me-2"></i>查找对象
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">待结算对象</h6>
                                                <div>
                                                    <button class="btn btn-outline-primary btn-sm" onclick="settlementManager.selectAllTargets(true)">全选</button>
                                                    <button class="btn btn-outline-secondary btn-sm" onclick="settlementManager.selectAllTargets(false)">取消</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive" style="max-height: 400px;">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th width="50">选择</th>
                                                            <th>对象名称</th>
                                                            <th>待结算金额</th>
                                                            <th>手续费</th>
                                                            <th>实际到账</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="batchTargetsTableBody">
                                                        <tr>
                                                            <td colspan="5" class="text-center py-4 text-muted">
                                                                请先设置条件并查找对象
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <div class="me-auto">
                                <span class="text-muted">已选择 <span id="selectedCount">0</span> 个对象，总金额 <span id="totalSelectedAmount">¥0</span></span>
                            </div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-success" onclick="settlementManager.executeBatchSettlement()">
                                <i class="bi bi-check-all me-2"></i>执行批量结算
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 结算详情模态框 -->
            <div class="modal fade" id="settlementDetailModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-info-circle me-2"></i>结算详情
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="settlementDetailContent">
                            <!-- 详情内容将动态生成 -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 时间范围变化事件
        document.getElementById('filterTimeRange').addEventListener('change', (e) => {
            const customRange = document.getElementById('customTimeRange');
            customRange.style.display = e.target.value === 'custom' ? 'block' : 'none';
        });

        // 结算对象类型变化事件
        document.getElementById('targetType')?.addEventListener('change', (e) => {
            this.loadTargetOptions(e.target.value, 'targetId');
        });

        // 批量结算对象类型变化事件
        document.getElementById('batchTargetType')?.addEventListener('change', (e) => {
            this.clearBatchTargets();
        });

        // 结算周期变化事件
        document.getElementById('settlementPeriod')?.addEventListener('change', (e) => {
            this.updateTimeRange(e.target.value);
        });
    }

    /**
     * 加载结算记录列表
     */
    async loadSettlements() {
        try {
            this.showLoading(true);

            const params = {
                page: this.currentPage,
                pageSize: this.pageSize,
                settlementType: document.getElementById('filterSettlementType')?.value || '',
                status: document.getElementById('filterStatus')?.value || '',
                targetType: document.getElementById('filterTargetType')?.value || '',
                timeRange: document.getElementById('filterTimeRange')?.value || 'today',
                startDate: document.getElementById('startDate')?.value || '',
                endDate: document.getElementById('endDate')?.value || '',
                minAmount: document.getElementById('minAmount')?.value || '',
                maxAmount: document.getElementById('maxAmount')?.value || ''
            };

            const response = await this.apiClient.get('/admin/finance/settlements', { params });

            if (response.success) {
                this.settlements = response.data.settlements;
                this.renderSettlementTable();
                this.renderPagination(response.data.pagination);
            } else {
                throw new Error(response.message || '加载结算记录失败');
            }

        } catch (error) {
            console.error('加载结算记录失败:', error);
            this.showError('加载记录失败: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 加载结算统计数据
     */
    async loadSettlementStats() {
        try {
            const response = await this.apiClient.get('/admin/finance/settlement-stats');

            if (response.success) {
                const stats = response.data;
                this.updateStatsDisplay(stats);
                this.updateMonitoringDisplay(stats.monitoring);
            }

        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 渲染结算记录表格
     */
    renderSettlementTable() {
        const tbody = document.getElementById('settlementTableBody');
        
        if (this.settlements.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center py-4">
                        <i class="bi bi-inbox text-muted" style="font-size: 2rem;"></i>
                        <div class="mt-2 text-muted">暂无结算记录</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.settlements.map(settlement => `
            <tr>
                <td>
                    <input type="checkbox" class="settlement-checkbox" value="${settlement.id}">
                </td>
                <td>
                    <div class="fw-medium">${settlement.settlement_no}</div>
                    <small class="text-muted">#${settlement.id}</small>
                </td>
                <td>
                    <div class="fw-medium">${settlement.target_name}</div>
                    <small class="text-muted">${this.getTargetTypeText(settlement.target_type)}</small>
                </td>
                <td>
                    <span class="badge bg-${this.getSettlementTypeBadgeColor(settlement.settlement_type)}">
                        ${this.getSettlementTypeText(settlement.settlement_type)}
                    </span>
                </td>
                <td>
                    <div class="fw-medium text-primary">¥${this.formatAmount(settlement.settlement_amount)}</div>
                </td>
                <td>
                    <div class="text-warning">¥${this.formatAmount(settlement.fee_amount)}</div>
                </td>
                <td>
                    <div class="fw-medium text-success">¥${this.formatAmount(settlement.actual_amount)}</div>
                </td>
                <td>
                    <div>${this.formatDateTime(settlement.created_at)}</div>
                    ${settlement.completed_at ? `<small class="text-muted">完成: ${this.formatDateTime(settlement.completed_at)}</small>` : ''}
                </td>
                <td>
                    <span class="badge bg-${this.getStatusBadgeColor(settlement.status)}">
                        ${this.getStatusText(settlement.status)}
                    </span>
                    ${settlement.status === 'processing' ? `<div class="progress mt-1" style="height: 3px;"><div class="progress-bar" style="width: ${settlement.progress || 0}%"></div></div>` : ''}
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="settlementManager.viewSettlement(${settlement.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${settlement.status === 'pending' ? `
                            <button class="btn btn-outline-success" onclick="settlementManager.approveSettlement(${settlement.id})" title="审批">
                                <i class="bi bi-check"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="settlementManager.cancelSettlement(${settlement.id})" title="取消">
                                <i class="bi bi-x"></i>
                            </button>
                        ` : ''}
                        ${settlement.status === 'failed' ? `
                            <button class="btn btn-outline-warning" onclick="settlementManager.retrySettlement(${settlement.id})" title="重试">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 显示创建结算模态框
     */
    showCreateSettlementModal() {
        document.getElementById('createSettlementForm').reset();
        document.getElementById('settlementPreview').style.display = 'none';
        
        const modal = new bootstrap.Modal(document.getElementById('createSettlementModal'));
        modal.show();
    }

    /**
     * 创建结算
     */
    async createSettlement() {
        try {
            const form = document.getElementById('createSettlementForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const settlementData = {
                target_type: document.getElementById('targetType').value,
                target_id: document.getElementById('targetId').value,
                settlement_type: document.getElementById('settlementType').value,
                settlement_period: document.getElementById('settlementPeriod').value,
                start_time: document.getElementById('startTime').value,
                end_time: document.getElementById('endTime').value,
                note: document.getElementById('settlementNote').value
            };

            const response = await this.apiClient.post('/admin/finance/settlements', settlementData);

            if (response.success) {
                this.showSuccess('结算创建成功');
                bootstrap.Modal.getInstance(document.getElementById('createSettlementModal')).hide();
                await this.loadSettlements();
                await this.loadSettlementStats();
            } else {
                throw new Error(response.message || '创建失败');
            }

        } catch (error) {
            console.error('创建结算失败:', error);
            this.showError('创建失败: ' + error.message);
        }
    }

    /**
     * 预览结算
     */
    async previewSettlement() {
        try {
            const settlementData = {
                target_type: document.getElementById('targetType').value,
                target_id: document.getElementById('targetId').value,
                settlement_period: document.getElementById('settlementPeriod').value,
                start_time: document.getElementById('startTime').value,
                end_time: document.getElementById('endTime').value
            };

            if (!settlementData.target_type || !settlementData.target_id) {
                this.showError('请先选择结算对象');
                return;
            }

            const response = await this.apiClient.post('/admin/finance/settlements/preview', settlementData);

            if (response.success) {
                this.renderSettlementPreview(response.data);
                document.getElementById('settlementPreview').style.display = 'block';
            } else {
                throw new Error(response.message || '预览失败');
            }

        } catch (error) {
            console.error('预览结算失败:', error);
            this.showError('预览失败: ' + error.message);
        }
    }

    /**
     * 渲染结算预览
     */
    renderSettlementPreview(previewData) {
        const tbody = document.getElementById('previewTableBody');
        tbody.innerHTML = `
            <tr>
                <td>交易总额</td>
                <td class="text-end fw-medium">¥${this.formatAmount(previewData.total_transaction_amount)}</td>
            </tr>
            <tr>
                <td>平台手续费</td>
                <td class="text-end text-warning">-¥${this.formatAmount(previewData.platform_fee)}</td>
            </tr>
            <tr>
                <td>结算手续费</td>
                <td class="text-end text-warning">-¥${this.formatAmount(previewData.settlement_fee)}</td>
            </tr>
            <tr class="table-success">
                <td class="fw-medium">实际结算金额</td>
                <td class="text-end fw-medium">¥${this.formatAmount(previewData.actual_settlement_amount)}</td>
            </tr>
            <tr>
                <td>交易笔数</td>
                <td class="text-end">${previewData.transaction_count} 笔</td>
            </tr>
            <tr>
                <td>结算周期</td>
                <td class="text-end">${previewData.settlement_period_text}</td>
            </tr>
        `;
    }

    /**
     * 启动自动刷新
     */
    startAutoRefresh() {
        // 每30秒刷新一次统计数据
        setInterval(() => {
            this.loadSettlementStats();
        }, 30000);

        // 每60秒刷新一次列表（如果当前页有处理中的记录）
        setInterval(() => {
            const hasProcessing = this.settlements.some(s => s.status === 'processing');
            if (hasProcessing) {
                this.loadSettlements();
            }
        }, 60000);
    }

    /**
     * 更新统计显示
     */
    updateStatsDisplay(stats) {
        document.getElementById('pendingCount').textContent = stats.pending_count || 0;
        document.getElementById('processingCount').textContent = stats.processing_count || 0;
        document.getElementById('completedCount').textContent = stats.completed_count || 0;
        document.getElementById('failedCount').textContent = stats.failed_count || 0;
        document.getElementById('todayAmount').textContent = '¥' + this.formatAmount(stats.today_amount || 0);
        document.getElementById('totalAmount').textContent = '¥' + this.formatAmount(stats.total_amount || 0);
    }

    /**
     * 更新监控显示
     */
    updateMonitoringDisplay(monitoring) {
        document.getElementById('currentProcessing').textContent = monitoring.current_processing || 0;
        document.getElementById('successRate').textContent = (monitoring.success_rate || 0) + '%';
        document.getElementById('avgProcessTime').textContent = (monitoring.avg_process_time || 0) + 's';
        document.getElementById('queueLength').textContent = monitoring.queue_length || 0;

        // 更新在线状态
        const statusEl = document.getElementById('onlineStatus');
        if (monitoring.is_online) {
            statusEl.textContent = '在线';
            statusEl.className = 'badge bg-success ms-2';
        } else {
            statusEl.textContent = '离线';
            statusEl.className = 'badge bg-danger ms-2';
        }
    }

    /**
     * 加载目标选项
     */
    async loadTargetOptions(targetType, selectId) {
        const targetSelect = document.getElementById(selectId);
        targetSelect.innerHTML = '<option value="">加载中...</option>';

        if (!targetType) {
            targetSelect.innerHTML = '<option value="">请先选择对象类型</option>';
            return;
        }

        try {
            const response = await this.apiClient.get(`/admin/finance/settlement-targets/${targetType}`);
            if (response.success) {
                targetSelect.innerHTML = '<option value="">请选择对象</option>';
                response.data.forEach(target => {
                    const option = document.createElement('option');
                    option.value = target.id;
                    option.textContent = `${target.name} (余额: ¥${this.formatAmount(target.balance)})`;
                    targetSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('加载目标选项失败:', error);
            targetSelect.innerHTML = '<option value="">加载失败</option>';
        }
    }

    /**
     * 应用过滤器
     */
    applyFilters() {
        this.currentPage = 1;
        this.loadSettlements();
    }

    /**
     * 重置过滤器
     */
    resetFilters() {
        document.getElementById('filterSettlementType').value = '';
        document.getElementById('filterStatus').value = '';
        document.getElementById('filterTargetType').value = '';
        document.getElementById('filterTimeRange').value = 'today';
        document.getElementById('minAmount').value = '';
        document.getElementById('maxAmount').value = '';
        document.getElementById('customTimeRange').style.display = 'none';
        this.applyFilters();
    }

    /**
     * 格式化金额
     */
    formatAmount(amount) {
        return parseFloat(amount || 0).toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(datetime) {
        return new Date(datetime).toLocaleString('zh-CN');
    }

    /**
     * 获取结算类型文本
     */
    getSettlementTypeText(type) {
        const typeMap = {
            'auto': '自动结算',
            'manual': '手动结算',
            'scheduled': '定时结算'
        };
        return typeMap[type] || type;
    }

    /**
     * 获取结算类型徽章颜色
     */
    getSettlementTypeBadgeColor(type) {
        const colorMap = {
            'auto': 'success',
            'manual': 'primary',
            'scheduled': 'info'
        };
        return colorMap[type] || 'secondary';
    }

    /**
     * 获取目标类型文本
     */
    getTargetTypeText(type) {
        const typeMap = {
            'provider': '码商',
            'merchant': '商户'
        };
        return typeMap[type] || type;
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'pending': '待处理',
            'processing': '处理中',
            'completed': '已完成',
            'failed': '失败',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    }

    /**
     * 获取状态徽章颜色
     */
    getStatusBadgeColor(status) {
        const colorMap = {
            'pending': 'warning',
            'processing': 'info',
            'completed': 'success',
            'failed': 'danger',
            'cancelled': 'secondary'
        };
        return colorMap[status] || 'secondary';
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        console.log('✅', message);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        console.error('❌', message);
    }

    /**
     * 显示加载状态
     */
    showLoading(show) {
        // 实现加载状态显示
    }
}

// 导出模块
window.SettlementManager = SettlementManager;

// 创建全局实例
if (typeof window.settlementManager === 'undefined') {
    window.settlementManager = new SettlementManager();
}

console.log('💰 SettlementManager 模块加载完成'); 