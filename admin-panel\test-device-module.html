<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理模块测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .status-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .status-success {
            background-color: #d4edda;
            border-left-color: #28a745;
        }
        .status-error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        .status-warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }
        .module-test-area {
            min-height: 600px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin-top: 20px;
        }
        .debug-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-gear-fill me-2"></i>
                    设备管理模块测试 (重构版)
                </h1>
                
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>新文件结构:</strong> 所有设备相关模块已重新组织到 <code>js/modules/device/</code> 目录下
                </div>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">模块加载状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="moduleStatus">
                            <div class="status-item">
                                <i class="bi bi-hourglass-split me-2"></i>
                                正在初始化模块加载器...
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="testDeviceModule()">
                                <i class="bi bi-play-fill me-2"></i>
                                测试设备管理模块
                            </button>
                            <button class="btn btn-secondary ms-2" onclick="reloadModules()">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                重新加载模块
                            </button>
                            <button class="btn btn-info ms-2" onclick="showDebugInfo()">
                                <i class="bi bi-info-circle me-2"></i>
                                调试信息
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">设备管理模块测试区域</h5>
                    </div>
                    <div class="card-body">
                        <div id="deviceModuleArea" class="module-test-area">
                            <div class="text-center text-muted">
                                <i class="bi bi-arrow-up-circle" style="font-size: 3rem;"></i>
                                <p class="mt-3">点击上方按钮开始测试设备管理模块</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="debugPanel" class="debug-panel" style="display: none;">
                    <h6>调试信息</h6>
                    <div id="debugContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/modules/core/module-loader.js"></script>
    
    <script>
        let moduleLoader;
        let debugLogs = [];
        
        // 重写console.log来捕获调试信息
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            debugLogs.push({
                type: 'log',
                message: args.join(' '),
                timestamp: new Date().toLocaleTimeString()
            });
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            debugLogs.push({
                type: 'error',
                message: args.join(' '),
                timestamp: new Date().toLocaleTimeString()
            });
            originalConsoleError.apply(console, args);
        };
        
        console.warn = function(...args) {
            debugLogs.push({
                type: 'warn',
                message: args.join(' '),
                timestamp: new Date().toLocaleTimeString()
            });
            originalConsoleWarn.apply(console, args);
        };
        
        // 初始化
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                addStatusMessage('正在初始化模块加载器...', 'info');
                
                // 初始化模块加载器
                moduleLoader = new ModuleLoader();
                addStatusMessage('✅ 模块加载器初始化成功', 'success');
                
                // 设置测试用的认证信息
                localStorage.setItem('admin_token', 'test_token');
                localStorage.setItem('admin_user', JSON.stringify({
                    id: 1,
                    username: 'test_admin',
                    role: 'admin'
                }));
                
                addStatusMessage('✅ 测试认证信息已设置', 'success');
                
            } catch (error) {
                console.error('初始化失败:', error);
                addStatusMessage('❌ 初始化失败: ' + error.message, 'error');
            }
        });
        
        // 测试设备管理模块
        async function testDeviceModule() {
            try {
                addStatusMessage('开始加载设备管理模块...', 'info');
                
                // 加载设备管理模块
                const module = await moduleLoader.loadModule('device-management');
                
                if (module) {
                    addStatusMessage('✅ 设备管理模块加载成功', 'success');
                    
                    // 初始化模块
                    const container = document.getElementById('deviceModuleArea');
                    await module.init(container);
                    
                    addStatusMessage('✅ 设备管理模块初始化成功', 'success');
                } else {
                    addStatusMessage('❌ 设备管理模块加载失败', 'error');
                }
                
            } catch (error) {
                console.error('测试设备模块失败:', error);
                addStatusMessage('❌ 测试失败: ' + error.message, 'error');
            }
        }
        
        // 重新加载模块
        async function reloadModules() {
            try {
                addStatusMessage('正在重新加载模块...', 'info');
                
                // 清理容器
                document.getElementById('deviceModuleArea').innerHTML = `
                    <div class="text-center text-muted">
                        <i class="bi bi-arrow-clockwise" style="font-size: 3rem;"></i>
                        <p class="mt-3">模块已重置，点击测试按钮重新加载</p>
                    </div>
                `;
                
                // 重新加载模块
                if (moduleLoader) {
                    moduleLoader.clearCache(true);
                    addStatusMessage('✅ 模块缓存已清理', 'success');
                }
                
            } catch (error) {
                console.error('重新加载失败:', error);
                addStatusMessage('❌ 重新加载失败: ' + error.message, 'error');
            }
        }
        
        // 显示调试信息
        function showDebugInfo() {
            const debugPanel = document.getElementById('debugPanel');
            const debugContent = document.getElementById('debugContent');
            
            if (debugPanel.style.display === 'none') {
                debugPanel.style.display = 'block';
                updateDebugContent();
            } else {
                debugPanel.style.display = 'none';
            }
        }
        
        // 更新调试内容
        function updateDebugContent() {
            const debugContent = document.getElementById('debugContent');
            
            let content = '<h6>系统状态</h6>';
            
            if (moduleLoader) {
                const metrics = moduleLoader.getPerformanceMetrics();
                const debugInfo = moduleLoader.getDebugInfo();
                
                content += `
                    <p><strong>已加载模块:</strong> ${debugInfo.loadedModules.join(', ') || '无'}</p>
                    <p><strong>缓存模块数:</strong> ${metrics.cachedModulesCount}</p>
                    <p><strong>平均加载时间:</strong> ${metrics.averageLoadTime.toFixed(2)}ms</p>
                    <p><strong>缓存命中:</strong> ${metrics.cacheHits}</p>
                    <p><strong>缓存未命中:</strong> ${metrics.cacheMisses}</p>
                `;
            }
            
            content += '<h6>调试日志</h6>';
            
            if (debugLogs.length > 0) {
                content += '<div style="max-height: 200px; overflow-y: auto;">';
                debugLogs.slice(-20).forEach(log => {
                    const color = log.type === 'error' ? 'red' : log.type === 'warn' ? 'orange' : 'black';
                    content += `<div style="color: ${color}; margin: 2px 0;">[${log.timestamp}] ${log.message}</div>`;
                });
                content += '</div>';
            } else {
                content += '<p>暂无调试日志</p>';
            }
            
            debugContent.innerHTML = content;
        }
        
        // 添加状态消息
        function addStatusMessage(message, type = 'info') {
            const statusContainer = document.getElementById('moduleStatus');
            const statusDiv = document.createElement('div');
            
            let iconClass = 'bi-info-circle';
            let statusClass = 'status-item';
            
            switch (type) {
                case 'success':
                    iconClass = 'bi-check-circle';
                    statusClass += ' status-success';
                    break;
                case 'error':
                    iconClass = 'bi-exclamation-triangle';
                    statusClass += ' status-error';
                    break;
                case 'warning':
                    iconClass = 'bi-exclamation-triangle';
                    statusClass += ' status-warning';
                    break;
            }
            
            statusDiv.className = statusClass;
            statusDiv.innerHTML = `
                <i class="bi ${iconClass} me-2"></i>
                ${message}
                <small class="float-end text-muted">${new Date().toLocaleTimeString()}</small>
            `;
            
            statusContainer.appendChild(statusDiv);
            
            // 自动滚动到底部
            statusContainer.scrollTop = statusContainer.scrollHeight;
            
            // 更新调试信息
            if (document.getElementById('debugPanel').style.display !== 'none') {
                updateDebugContent();
            }
        }
        
        // 定期更新调试信息
        setInterval(() => {
            if (document.getElementById('debugPanel').style.display !== 'none') {
                updateDebugContent();
            }
        }, 2000);
    </script>
</body>
</html> 