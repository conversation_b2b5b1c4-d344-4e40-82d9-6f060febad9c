<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多租户用户管理测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            background-color: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .test-result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 5px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            max-height: 400px;
            overflow-y: auto;
        }
        .success { 
            background-color: #d4edda; 
            border: 1px solid #c3e6cb; 
            color: #155724; 
        }
        .error { 
            background-color: #f8d7da; 
            border: 1px solid #f5c6cb; 
            color: #721c24; 
        }
        .info { 
            background-color: #d1ecf1; 
            border: 1px solid #bee5eb; 
            color: #0c5460; 
        }
        button { 
            background-color: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px; 
        }
        button:hover { 
            background-color: #0056b3; 
        }
        button:disabled { 
            background-color: #6c757d; 
            cursor: not-allowed; 
        }
        input[type="text"], input[type="password"], input[type="email"], select { 
            width: 200px; 
            padding: 8px; 
            margin: 5px; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .user-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .user-table th, .user-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .user-table th {
            background-color: #f2f2f2;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-success { background-color: #28a745; color: white; }
        .status-error { background-color: #dc3545; color: white; }
        .status-pending { background-color: #ffc107; color: black; }
    </style>
</head>
<body>
    <div class="container">
        <h1>👥 多租户用户管理测试 - Day 6 Step 3</h1>
        <div class="info test-result">
            <strong>测试目标:</strong> 验证users.php多租户用户管理系统改造结果<br>
            <strong>测试域名:</strong> <span id="currentDomain"></span><br>
            <strong>API地址:</strong> <span id="apiUrl"></span>
        </div>
        
        <!-- 登录状态 -->
        <div class="test-section">
            <h2>🔐 登录状态</h2>
            <p>Token: <span id="loginToken">未登录</span></p>
            <p>当前用户: <span id="currentUser">未登录</span></p>
            <button onclick="quickLogin()">快速登录</button>
            <div id="loginResult" class="test-result"></div>
        </div>
        
        <!-- 测试1: 获取用户列表 -->
        <div class="test-section">
            <h2>测试1: 获取租户用户列表</h2>
            <div>
                <input type="text" id="searchKeyword" placeholder="搜索关键词" value="">
                <select id="filterUserType">
                    <option value="">所有用户类型</option>
                    <option value="system">系统管理员</option>
                    <option value="admin">平台管理员</option>
                    <option value="provider">码商</option>
                    <option value="merchant">商户</option>
                    <option value="employee">员工</option>
                </select>
                <select id="filterStatus">
                    <option value="">所有状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">停用</option>
                </select>
                <button onclick="testGetUsers()" id="getUsersBtn" disabled>获取用户列表</button>
                <button onclick="testGetUserTypes()" id="getUserTypesBtn" disabled>获取可用用户类型</button>
            </div>
            <div id="usersResult" class="test-result"></div>
            <div id="usersTable"></div>
        </div>
        
        <!-- 测试2: 创建用户 -->
        <div class="test-section">
            <h2>测试2: 创建租户用户</h2>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="newUsername" placeholder="用户名" value="test_user_001">
            </div>
            <div class="form-group">
                <label>邮箱:</label>
                <input type="email" id="newEmail" placeholder="邮箱" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="newPassword" placeholder="密码" value="password123">
            </div>
            <div class="form-group">
                <label>真实姓名:</label>
                <input type="text" id="newRealName" placeholder="真实姓名" value="测试用户001">
            </div>
            <div class="form-group">
                <label>用户类型:</label>
                <select id="newUserType">
                    <option value="">请选择用户类型</option>
                    <option value="employee">员工</option>
                    <option value="admin">平台管理员</option>
                    <option value="provider">码商</option>
                    <option value="merchant">商户</option>
                    <option value="system">系统管理员</option>
                </select>
            </div>
            <button onclick="testCreateUser()" id="createUserBtn" disabled>创建用户</button>
            <div id="createUserResult" class="test-result"></div>
        </div>
        
        <!-- 测试3: 权限验证 -->
        <div class="test-section">
            <h2>测试3: 权限验证测试</h2>
            <p>测试不同租户类型的权限限制</p>
            <button onclick="testPermissions()" id="testPermissionsBtn" disabled>测试权限限制</button>
            <div id="permissionsResult" class="test-result"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentToken = '';
        let currentUserInfo = null;
        const API_BASE_AUTH = window.location.protocol + '//' + window.location.hostname + '/api/admin/auth.php';
        const API_BASE_USERS = window.location.protocol + '//' + window.location.hostname + '/api/admin/users.php';
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('currentDomain').textContent = window.location.hostname;
            document.getElementById('apiUrl').textContent = API_BASE_USERS;
        });
        
        // 通用API请求函数
        async function apiRequest(apiBase, action, data = {}, useToken = false) {
            const url = apiBase + '?action=' + action;
            
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (useToken && currentToken) {
                headers['Authorization'] = 'Bearer ' + currentToken;
            }
            
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(data)
                });
                
                let result;
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    result = await response.json();
                } else {
                    const text = await response.text();
                    result = { error: 'Non-JSON response', response_text: text };
                }
                
                return { 
                    success: response.ok, 
                    data: result, 
                    status: response.status,
                    statusText: response.statusText
                };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        // 显示结果
        function displayResult(elementId, result, isSuccess = null) {
            const element = document.getElementById(elementId);
            
            if (isSuccess === null) {
                isSuccess = result.success;
            }
            
            element.className = 'test-result ' + (isSuccess ? 'success' : 'error');
            element.textContent = JSON.stringify(result, null, 2);
        }
        
        // 更新按钮状态
        function updateButtonStates() {
            const hasToken = !!currentToken;
            document.getElementById('getUsersBtn').disabled = !hasToken;
            document.getElementById('getUserTypesBtn').disabled = !hasToken;
            document.getElementById('createUserBtn').disabled = !hasToken;
            document.getElementById('testPermissionsBtn').disabled = !hasToken;
        }
        
        // 快速登录
        async function quickLogin() {
            displayResult('loginResult', { message: '正在登录...' }, true);
            
            const loginData = {
                username: 'system_admin',
                password: 'password'
            };
            
            const result = await apiRequest(API_BASE_AUTH, 'login', loginData);
            displayResult('loginResult', result);
            
            if (result.success && result.data && result.data.data) {
                currentToken = result.data.data.token;
                currentUserInfo = result.data.data.user;
                
                document.getElementById('loginToken').textContent = currentToken.substring(0, 50) + '...';
                document.getElementById('currentUser').textContent = currentUserInfo.username + ' (' + currentUserInfo.user_type + ')';
                
                updateButtonStates();
            }
        }
        
        // 测试1: 获取用户列表
        async function testGetUsers() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            const search = document.getElementById('searchKeyword').value;
            const userType = document.getElementById('filterUserType').value;
            const status = document.getElementById('filterStatus').value;
            
            let queryParams = '';
            if (search) queryParams += '&search=' + encodeURIComponent(search);
            if (userType) queryParams += '&user_type=' + encodeURIComponent(userType);
            if (status) queryParams += '&status=' + encodeURIComponent(status);
            
            displayResult('usersResult', { message: '正在获取用户列表...' }, true);
            
            try {
                const url = API_BASE_USERS + '?action=get_users' + queryParams;
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + currentToken
                    }
                });
                
                const result = await response.json();
                displayResult('usersResult', { success: response.ok, data: result, status: response.status });
                
                if (response.ok && result.data && result.data.users) {
                    displayUsersTable(result.data.users, result.data.stats);
                }
            } catch (error) {
                displayResult('usersResult', { success: false, error: error.message });
            }
        }
        
        // 显示用户表格
        function displayUsersTable(users, stats) {
            const tableContainer = document.getElementById('usersTable');
            
            let html = '<h3>用户列表 (共 ' + users.length + ' 个用户)</h3>';
            
            if (stats) {
                html += '<p><strong>统计信息:</strong></p>';
                html += '<p>按类型: ' + JSON.stringify(stats.by_type) + '</p>';
                html += '<p>按状态: ' + JSON.stringify(stats.by_status) + '</p>';
            }
            
            html += '<table class="user-table">';
            html += '<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>真实姓名</th><th>用户类型</th><th>状态</th><th>平台</th><th>归属</th><th>创建时间</th></tr>';
            
            users.forEach(user => {
                html += '<tr>';
                html += '<td>' + user.id + '</td>';
                html += '<td>' + user.username + '</td>';
                html += '<td>' + user.email + '</td>';
                html += '<td>' + (user.real_name || '-') + '</td>';
                html += '<td>' + user.user_type_name + '</td>';
                html += '<td>' + user.status + '</td>';
                html += '<td>' + (user.platform_name || '-') + '</td>';
                html += '<td>' + user.belongs_to_display + '</td>';
                html += '<td>' + user.created_at + '</td>';
                html += '</tr>';
            });
            
            html += '</table>';
            tableContainer.innerHTML = html;
        }
        
        // 获取可用用户类型
        async function testGetUserTypes() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            displayResult('usersResult', { message: '正在获取可用用户类型...' }, true);
            
            const result = await apiRequest(API_BASE_USERS, 'get_user_types', {}, true);
            displayResult('usersResult', result);
            
            if (result.success && result.data && result.data.data) {
                // 更新用户类型选择框
                const select = document.getElementById('newUserType');
                select.innerHTML = '<option value="">请选择用户类型</option>';
                
                result.data.data.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.value;
                    option.textContent = type.label;
                    select.appendChild(option);
                });
            }
        }
        
        // 测试2: 创建用户
        async function testCreateUser() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            const userData = {
                username: document.getElementById('newUsername').value,
                email: document.getElementById('newEmail').value,
                password: document.getElementById('newPassword').value,
                real_name: document.getElementById('newRealName').value,
                user_type: document.getElementById('newUserType').value
            };
            
            // 验证必填字段
            if (!userData.username || !userData.email || !userData.password || !userData.user_type) {
                alert('请填写所有必填字段');
                return;
            }
            
            displayResult('createUserResult', { message: '正在创建用户...' }, true);
            
            const result = await apiRequest(API_BASE_USERS, 'add_user', userData, true);
            displayResult('createUserResult', result);
            
            if (result.success) {
                // 清空表单
                document.getElementById('newUsername').value = 'test_user_' + Date.now();
                document.getElementById('newEmail').value = 'test' + Date.now() + '@example.com';
                document.getElementById('newRealName').value = '测试用户' + Date.now();
                
                // 刷新用户列表
                setTimeout(() => {
                    testGetUsers();
                }, 1000);
            }
        }
        
        // 测试3: 权限验证
        async function testPermissions() {
            if (!currentToken) {
                alert('请先登录');
                return;
            }
            
            displayResult('permissionsResult', { message: '正在测试权限验证...' }, true);
            
            const permissionTests = [
                { action: 'get_users', description: '查看用户列表' },
                { action: 'add_user', description: '创建用户', data: { username: 'test', email: '<EMAIL>', password: 'test', user_type: 'employee' } },
                { action: 'get_user_types', description: '获取用户类型' }
            ];
            
            let results = [];
            
            for (let test of permissionTests) {
                try {
                    const result = await apiRequest(API_BASE_USERS, test.action, test.data || {}, true);
                    results.push({
                        action: test.action,
                        description: test.description,
                        success: result.success,
                        code: result.data ? result.data.code : result.status,
                        message: result.data ? result.data.message : result.statusText
                    });
                } catch (error) {
                    results.push({
                        action: test.action,
                        description: test.description,
                        success: false,
                        error: error.message
                    });
                }
            }
            
            displayResult('permissionsResult', { 
                success: true, 
                message: '权限测试完成',
                results: results 
            });
        }
    </script>
</body>
</html> 