/**
 * API统计管理器
 * 从ApiModule.js提取的统计数据相关功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class ApiStatsManager {
    constructor() {
        this.authManager = window.authManager;
        this.utils = window.utils;
        this.statsData = {};
        
        console.log('✅ ApiStatsManager initialized');
    }

    /**
     * 统一渲染方法 - 路由系统调用
     */
    async render(container, params = {}) {
        try {
            console.log('🎯 ApiStatsManager渲染 - 参数:', params);
            
            // 显示加载状态
            container.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><div class="mt-2">正在加载统计数据...</div></div>';
            
            // 生成HTML内容
            container.innerHTML = this.generateStatsContent();
            
            // 设置全局引用以便HTML中的onclick调用
            window.apiModule = this;
            
            // 加载统计数据
            setTimeout(() => {
                this.loadStatsData();
            }, 100);
            
        } catch (error) {
            console.error('❌ ApiStatsManager渲染失败:', error);
            container.innerHTML = `<div class="alert alert-danger">统计数据管理器加载失败: ${error.message}</div>`;
        }
    }

    /**
     * 生成统计数据HTML内容
     */
    generateStatsContent() {
        return `
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        API调用次数
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="apiCalls">
                                        0
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        成功率
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="successRate">
                                        0%
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        平均响应时间
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="avgResponseTime">
                                        0ms
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-speedometer fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        月度配额
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800" id="monthlyQuota">
                                        0%
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-pie-chart fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表和报告 -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-bar-chart me-2"></i>API调用趋势
                            </h6>
                        </div>
                        <div class="card-body">
                            <div id="apiTrendChart" style="height: 300px;">
                                <div class="text-center py-5 text-muted">
                                    <i class="bi bi-bar-chart" style="font-size: 3rem;"></i>
                                    <h5 class="mt-3">图表加载中...</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="bi bi-tools me-2"></i>统计工具
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="apiModule.refreshStats()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                                </button>
                                <button class="btn btn-outline-success" onclick="apiModule.exportStatsReport('csv')">
                                    <i class="bi bi-download me-2"></i>导出CSV
                                </button>
                                <button class="btn btn-outline-info" onclick="apiModule.exportStatsReport('json')">
                                    <i class="bi bi-file-earmark-code me-2"></i>导出JSON
                                </button>
                                <button class="btn btn-outline-warning" onclick="apiModule.generatePerformanceReport()">
                                    <i class="bi bi-file-earmark-text me-2"></i>性能报告
                                </button>
                            </div>
                            
                            <hr class="my-3">
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="autoRefresh" onchange="apiModule.toggleAutoRefresh()">
                                <label class="form-check-label" for="autoRefresh">
                                    自动刷新 (5分钟)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 切换自动刷新
     */
    toggleAutoRefresh() {
        const checkbox = document.getElementById('autoRefresh');
        if (checkbox.checked) {
            this.setupAutoRefresh(5);
        } else {
            this.stopAutoRefresh();
        }
    }

    /**
     * 获取统计配置（根据角色）
     */
    getStatsConfig(role) {
        const configs = {
            'system_admin': [
                { id: 'totalApis', label: '系统API总数', icon: 'bi-cloud', color: 'primary' },
                { id: 'activeTenants', label: '活跃租户', icon: 'bi-building', color: 'success' },
                { id: 'todayRequests', label: '今日请求', icon: 'bi-arrow-up-right', color: 'info' },
                { id: 'errorRate', label: '错误率', icon: 'bi-exclamation-triangle', color: 'warning' }
            ],
            'platform_admin': [
                { id: 'platformApis', label: '平台API数', icon: 'bi-diagram-3', color: 'primary' },
                { id: 'merchants', label: '商户数量', icon: 'bi-shop', color: 'success' },
                { id: 'dailyVolume', label: '日交易量', icon: 'bi-graph-up', color: 'info' },
                { id: 'uptime', label: '系统可用性', icon: 'bi-check-circle', color: 'success' }
            ],
            'provider': [
                { id: 'deviceApis', label: '设备API数', icon: 'bi-router', color: 'primary' },
                { id: 'connectedDevices', label: '连接设备', icon: 'bi-cpu', color: 'success' },
                { id: 'dataTransfer', label: '数据传输', icon: 'bi-arrow-left-right', color: 'info' },
                { id: 'alerts', label: '告警数量', icon: 'bi-bell', color: 'warning' }
            ],
            'merchant': [
                { id: 'apiCalls', label: 'API调用次数', icon: 'bi-graph-up', color: 'primary' },
                { id: 'successRate', label: '成功率', icon: 'bi-check-circle', color: 'success' },
                { id: 'avgResponseTime', label: '平均响应时间', icon: 'bi-speedometer', color: 'info' },
                { id: 'monthlyQuota', label: '月度配额', icon: 'bi-pie-chart', color: 'warning' }
            ]
        };
        return configs[role] || configs['merchant'];
    }

    /**
     * 获取模拟统计数据
     */
    getMockStatsData(role) {
        const mockData = {
            'system_admin': ['1,247', '156', '45,832', '2.3%'],
            'platform_admin': ['89', '1,234', '$234,567', '99.9%'],
            'provider': ['23', '45', '2.3TB', '3'],
            'merchant': ['12,456', '98.5%', '245ms', '75%']
        };
        return mockData[role] || mockData['merchant'];
    }

    /**
     * 生成统计卡片HTML
     */
    generateCustomStatsCards(stats) {
        if (!stats || !Array.isArray(stats)) return '';
        
        return `
            <div class="row mb-4">
                ${stats.map(stat => `
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-${stat.color || 'primary'} shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-${stat.color || 'primary'} text-uppercase mb-1">
                                            ${stat.label}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="${stat.id}">
                                            ${stat.value || '0'}
                                        </div>
                                        ${stat.change ? `
                                            <div class="text-xs ${stat.change.startsWith('+') ? 'text-success' : 'text-danger'}">
                                                ${stat.change}
                                            </div>
                                        ` : ''}
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi ${stat.icon} fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * 加载统计数据
     */
    async loadStatsData() {
        try {
            console.log('🔄 开始加载统计数据...');
            
            const response = await fetch('/api/merchant/index.php?module=tools&action=get_call_stats', {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                const stats = data.data || data;
                console.log('✅ 统计数据加载成功:', stats);
                this.statsData = stats;
                this.updateStatsCards(stats);
            } else {
                console.error('❌ 统计数据加载失败:', data.message);
                // 使用模拟数据
                this.loadMockStats();
            }
        } catch (error) {
            console.error('❌ 加载统计数据异常:', error);
            // 使用模拟数据
            this.loadMockStats();
        }
    }

    /**
     * 加载模拟统计数据
     */
    loadMockStats() {
        const mockStats = {
            apiCalls: '12,456',
            successRate: '98.5%',
            avgResponseTime: '245ms',
            monthlyQuota: '75%'
        };
        
        this.statsData = mockStats;
        this.updateStatsCards(mockStats);
    }

    /**
     * 更新统计卡片显示
     */
    updateStatsCards(data) {
        try {
            // 更新各个统计卡片
            Object.keys(data).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = data[key];
                    
                    // 添加动画效果
                    element.classList.add('animate__animated', 'animate__pulse');
                    setTimeout(() => {
                        element.classList.remove('animate__animated', 'animate__pulse');
                    }, 1000);
                }
            });
            
            console.log('✅ 统计卡片更新完成');
        } catch (error) {
            console.error('❌ 更新统计卡片失败:', error);
        }
    }

    /**
     * 加载调用统计数据
     */
    async loadCallStats() {
        try {
            console.log('🔄 开始加载API调用统计...');
            
            const response = await fetch('/api/merchant/index.php?module=tools&action=get_call_stats', {
                headers: {
                    'Authorization': `Bearer ${this.authManager?.getToken()}`
                }
            });

            const data = await response.json();
            if (data.code === 200 || data.success) {
                const stats = data.data || data;
                console.log('✅ API调用统计加载成功:', stats);
                this.updateCallStatsDisplay(stats);
            } else {
                console.error('❌ API调用统计加载失败:', data.message);
                this.showError('API调用统计加载失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('❌ 加载API调用统计异常:', error);
            this.showError('加载API调用统计时发生错误: ' + error.message);
        }
    }

    /**
     * 更新调用统计显示
     */
    updateCallStatsDisplay(stats) {
        try {
            // 更新今日调用次数
            const todayCallsElement = document.getElementById('todayCalls');
            if (todayCallsElement && stats.today_calls !== undefined) {
                todayCallsElement.textContent = stats.today_calls.toLocaleString();
            }
            
            // 更新本月调用次数
            const monthCallsElement = document.getElementById('monthCalls');
            if (monthCallsElement && stats.month_calls !== undefined) {
                monthCallsElement.textContent = stats.month_calls.toLocaleString();
            }
            
            // 更新成功率
            const successRateElement = document.getElementById('successRate');
            if (successRateElement && stats.success_rate !== undefined) {
                successRateElement.textContent = stats.success_rate + '%';
            }
            
            // 更新平均响应时间
            const avgResponseTimeElement = document.getElementById('avgResponseTime');
            if (avgResponseTimeElement && stats.avg_response_time !== undefined) {
                avgResponseTimeElement.textContent = stats.avg_response_time + 'ms';
            }
            
            console.log('✅ API调用统计显示更新完成');
        } catch (error) {
            console.error('❌ 更新API调用统计显示失败:', error);
        }
    }

    /**
     * 生成调用统计图表
     */
    generateCallStatsChart(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container || !data) return;

        // 这里可以使用Chart.js或其他图表库
        // 简单示例：生成一个基本的统计图表
        const chartHtml = `
            <div class="chart-container">
                <canvas id="${containerId}Chart" width="400" height="200"></canvas>
            </div>
            <script>
                // 这里可以添加Chart.js代码来生成实际图表
                console.log('图表数据:', ${JSON.stringify(data)});
            </script>
        `;
        
        container.innerHTML = chartHtml;
    }

    /**
     * 生成API使用趋势图
     */
    generateUsageTrendChart(data) {
        // 生成7天使用趋势
        const last7Days = [];
        const today = new Date();
        
        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            last7Days.push({
                date: date.toISOString().split('T')[0],
                calls: Math.floor(Math.random() * 1000) + 500 // 模拟数据
            });
        }
        
        return last7Days;
    }

    /**
     * 生成API性能报告
     */
    generatePerformanceReport() {
        const report = {
            summary: {
                totalCalls: this.statsData.apiCalls || '0',
                successRate: this.statsData.successRate || '0%',
                avgResponseTime: this.statsData.avgResponseTime || '0ms',
                errorCount: Math.floor(Math.random() * 100)
            },
            topEndpoints: [
                { endpoint: '/api/payment', calls: 5432, avgTime: '120ms' },
                { endpoint: '/api/query', calls: 3210, avgTime: '85ms' },
                { endpoint: '/api/refund', calls: 1876, avgTime: '200ms' },
                { endpoint: '/api/callback', calls: 987, avgTime: '50ms' }
            ],
            errorTypes: [
                { type: '400 Bad Request', count: 45 },
                { type: '401 Unauthorized', count: 23 },
                { type: '500 Internal Error', count: 12 },
                { type: '503 Service Unavailable', count: 8 }
            ]
        };
        
        return report;
    }

    /**
     * 导出统计报告
     */
    exportStatsReport(format = 'json') {
        const report = this.generatePerformanceReport();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        
        let content, mimeType, filename;
        
        switch (format) {
            case 'csv':
                content = this.convertToCSV(report);
                mimeType = 'text/csv';
                filename = `api-stats-${timestamp}.csv`;
                break;
            case 'json':
            default:
                content = JSON.stringify(report, null, 2);
                mimeType = 'application/json';
                filename = `api-stats-${timestamp}.json`;
                break;
        }
        
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess(`统计报告已导出: ${filename}`);
    }

    /**
     * 将数据转换为CSV格式
     */
    convertToCSV(data) {
        let csv = 'API统计报告\n\n';
        
        // 概要信息
        csv += '概要信息\n';
        csv += 'API调用总数,成功率,平均响应时间,错误数量\n';
        csv += `${data.summary.totalCalls},${data.summary.successRate},${data.summary.avgResponseTime},${data.summary.errorCount}\n\n`;
        
        // 热门接口
        csv += '热门接口\n';
        csv += '接口地址,调用次数,平均响应时间\n';
        data.topEndpoints.forEach(endpoint => {
            csv += `${endpoint.endpoint},${endpoint.calls},${endpoint.avgTime}\n`;
        });
        
        csv += '\n错误类型\n';
        csv += '错误类型,数量\n';
        data.errorTypes.forEach(error => {
            csv += `${error.type},${error.count}\n`;
        });
        
        return csv;
    }

    /**
     * 刷新统计数据
     */
    async refreshStats() {
        try {
            console.log('🔄 刷新统计数据...');
            
            // 显示刷新动画
            const refreshBtn = document.getElementById('refreshStatsBtn');
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>刷新中...';
                refreshBtn.disabled = true;
            }
            
            // 重新加载数据
            await this.loadStatsData();
            await this.loadCallStats();
            
            // 恢复按钮状态
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>刷新数据';
                refreshBtn.disabled = false;
            }
            
            this.showSuccess('统计数据已刷新');
            
        } catch (error) {
            console.error('❌ 刷新统计数据失败:', error);
            this.showError('刷新统计数据失败: ' + error.message);
            
            // 恢复按钮状态
            const refreshBtn = document.getElementById('refreshStatsBtn');
            if (refreshBtn) {
                refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>刷新数据';
                refreshBtn.disabled = false;
            }
        }
    }

    /**
     * 设置自动刷新
     */
    setupAutoRefresh(intervalMinutes = 5) {
        // 清除之前的定时器
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        // 设置新的定时器
        this.refreshTimer = setInterval(() => {
            console.log('🔄 自动刷新统计数据...');
            this.loadStatsData();
        }, intervalMinutes * 60 * 1000);
        
        console.log(`✅ 已设置自动刷新，间隔: ${intervalMinutes}分钟`);
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
            console.log('✅ 已停止自动刷新');
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        if (window.apiModule && window.apiModule.showSuccess && window.apiModule.showSuccess !== this.showSuccess) {
            window.apiModule.showSuccess(message);
        } else {
            console.log('✅ 成功:', message);
            // 简单的成功提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-check-circle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        if (window.apiModule && window.apiModule.showError && window.apiModule.showError !== this.showError) {
            window.apiModule.showError(message);
        } else {
            console.error('❌ 错误:', message);
            // 简单的错误提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>${message}`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.stopAutoRefresh();
        console.log('✅ ApiStatsManager已销毁');
    }
}

// 导出类
window.ApiStatsManager = ApiStatsManager; 