/**
 * 全局状态管理系统
 * 统一管理应用状态，提供响应式状态更新和持久化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class StateManager {
    constructor() {
        this.state = this.getInitialState();
        this.subscribers = new Map();
        this.history = [];
        this.maxHistorySize = 50;
        this.persistentKeys = ['user', 'settings', 'preferences'];
        
        // 状态变更统计
        this.stats = {
            totalChanges: 0,
            changesByKey: new Map()
        };
        
        console.log('🗃️ StateManager initialized');
        this.loadPersistedState();
    }

    /**
     * 获取初始状态
     */
    getInitialState() {
        return {
            user: null,
            permissions: [],
            isAuthenticated: false,
            currentModule: null,
            loading: false,
            error: null,
            modules: {
                device: { devices: [], selectedDevices: [], filters: {} },
                finance: { transactions: [], balance: 0, reports: [] },
                merchant: { merchants: [], selectedMerchant: null, orders: [] },
                system: { performance: {}, logs: [], alerts: [] }
            },
            ui: {
                sidebarCollapsed: false,
                theme: 'light',
                language: 'zh-CN',
                notifications: [],
                modals: {}
            },
            settings: {
                autoRefresh: true,
                refreshInterval: 30000,
                pageSize: 20,
                dateFormat: 'YYYY-MM-DD HH:mm:ss'
            },
            preferences: {
                dashboard: {
                    widgets: ['overview', 'recent-transactions', 'device-status'],
                    layout: 'grid'
                },
                table: {
                    density: 'medium',
                    showActions: true
                }
            }
        };
    }

    /**
     * 获取状态值
     * @param {string} path - 状态路径，支持点号分隔
     */
    getState(path) {
        if (!path) return this.state;
        
        const keys = path.split('.');
        let current = this.state;
        
        for (const key of keys) {
            if (current === null || current === undefined) return undefined;
            current = current[key];
        }
        
        return current;
    }

    /**
     * 设置状态值
     * @param {string} path - 状态路径
     * @param {any} value - 新值
     * @param {object} options - 选项
     */
    setState(path, value, options = {}) {
        const oldValue = this.getState(path);
        if (!options.force && this.isEqual(oldValue, value)) return false;
        
        this.setStateInternal(path, value);
        
        if (this.shouldPersist(path)) {
            this.persistState(path);
        }
        
        this.notifySubscribers(path, value, oldValue);
        
        if (window.eventBus) {
            window.eventBus.emit('state:changed', { path, value, oldValue });
        }
        
        return true;
    }

    /**
     * 内部状态设置
     */
    setStateInternal(path, value) {
        const keys = path.split('.');
        let current = this.state;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (current[key] === undefined || current[key] === null) {
                current[key] = {};
            }
            current = current[key];
        }
        
        current[keys[keys.length - 1]] = value;
    }

    /**
     * 批量更新状态
     * @param {object} updates - 更新对象 { path: value }
     */
    batchSetState(updates, options = {}) {
        const changes = [];
        
        for (const [path, value] of Object.entries(updates)) {
            const oldValue = this.getState(path);
            if (!options.force && this.isEqual(oldValue, value)) {
                continue;
            }
            
            this.recordHistory(path, oldValue, value);
            this.setStateInternal(path, value);
            this.updateStats(path);
            
            changes.push({ path, value, oldValue });
        }
        
        if (changes.length === 0) return false;
        
        // 批量持久化
        this.batchPersistState(changes.map(c => c.path));
        
        // 批量通知
        for (const change of changes) {
            this.notifySubscribers(change.path, change.value, change.oldValue);
        }
        
        // 触发批量变更事件
        if (window.eventBus) {
            window.eventBus.emit('state:batch-changed', {
                changes,
                timestamp: Date.now()
            });
        }
        
        return true;
    }

    /**
     * 订阅状态变更
     * @param {string} path - 状态路径
     * @param {function} callback - 回调函数
     * @param {object} options - 选项
     */
    subscribe(path, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('回调函数必须是function类型');
        }
        
        if (!this.subscribers.has(path)) {
            this.subscribers.set(path, []);
        }
        
        const subscription = {
            callback,
            id: this.generateId(),
            context: options.context || null
        };
        
        this.subscribers.get(path).push(subscription);
        
        if (options.immediate !== false) {
            const currentValue = this.getState(path);
            this.executeCallback(subscription, currentValue, undefined);
        }
        
        return () => this.unsubscribe(path, subscription.id);
    }

    /**
     * 取消订阅
     * @param {string} path - 状态路径
     * @param {string} subscriptionId - 订阅ID
     */
    unsubscribe(path, subscriptionId) {
        if (!this.subscribers.has(path)) return false;
        
        const subscriptions = this.subscribers.get(path);
        const index = subscriptions.findIndex(s => s.id === subscriptionId);
        
        if (index !== -1) {
            subscriptions.splice(index, 1);
            if (subscriptions.length === 0) {
                this.subscribers.delete(path);
            }
            return true;
        }
        
        return false;
    }

    /**
     * 通知订阅者
     */
    notifySubscribers(path, newValue, oldValue) {
        if (this.subscribers.has(path)) {
            const subscriptions = this.subscribers.get(path) || [];
            
            for (const subscription of subscriptions.slice()) {
                this.executeCallback(subscription, newValue, oldValue);
            }
        }
    }

    executeCallback(subscription, newValue, oldValue) {
        try {
            if (subscription.context) {
                subscription.callback.call(subscription.context, newValue, oldValue);
            } else {
                subscription.callback(newValue, oldValue);
            }
        } catch (error) {
            console.error('状态订阅回调执行错误:', error);
        }
    }

    /**
     * 计算状态
     * @param {function} selector - 选择器函数
     * @param {array} dependencies - 依赖路径数组
     */
    computed(selector, dependencies = []) {
        if (typeof selector !== 'function') {
            throw new Error('选择器必须是function类型');
        }
        
        let cachedValue;
        let isInitialized = false;
        
        const compute = () => {
            try {
                const depValues = dependencies.map(dep => this.getState(dep));
                return selector(this.state, ...depValues);
            } catch (error) {
                console.error('计算状态错误:', error);
                return undefined;
            }
        };
        
        // 订阅依赖变更
        const unsubscribers = dependencies.map(dep => 
            this.subscribe(dep, () => {
                const newValue = compute();
                if (!this.isEqual(cachedValue, newValue)) {
                    cachedValue = newValue;
                }
            }, { immediate: false })
        );
        
        return {
            get value() {
                if (!isInitialized) {
                    cachedValue = compute();
                    isInitialized = true;
                }
                return cachedValue;
            },
            destroy() {
                unsubscribers.forEach(unsub => unsub());
            }
        };
    }

    /**
     * 重置状态
     * @param {string} path - 状态路径，为空则重置全部
     */
    resetState(path) {
        if (!path) {
            this.state = this.getInitialState();
        } else {
            const initialState = this.getInitialState();
            const initialValue = this.getState.call({ state: initialState }, path);
            this.setState(path, initialValue, { force: true });
        }
    }

    /**
     * 记录状态变更历史
     */
    recordHistory(path, oldValue, newValue) {
        const record = {
            path,
            oldValue: this.deepClone(oldValue),
            newValue: this.deepClone(newValue),
            timestamp: Date.now(),
            id: this.generateHistoryId()
        };
        
        this.history.push(record);
        
        // 限制历史记录大小
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
        }
    }

    /**
     * 获取状态变更历史
     */
    getHistory(limit = 20) {
        return this.history.slice(-limit);
    }

    /**
     * 更新统计信息
     */
    updateStats(path) {
        this.stats.totalChanges++;
        
        const currentCount = this.stats.changesByKey.get(path) || 0;
        this.stats.changesByKey.set(path, currentCount + 1);
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            topChangedKeys: Array.from(this.stats.changesByKey.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10)
        };
    }

    /**
     * 状态持久化
     */
    shouldPersist(path) {
        return this.persistentKeys.some(key => path.startsWith(key));
    }

    persistState(path) {
        if (!this.shouldPersist(path)) return;
        
        const rootKey = path.split('.')[0];
        const value = this.getState(rootKey);
        
        try {
            localStorage.setItem(`state_${rootKey}`, JSON.stringify(value));
        } catch (error) {
            console.warn('状态持久化失败:', error);
        }
    }

    batchPersistState(paths) {
        const rootKeys = new Set();
        
        for (const path of paths) {
            if (this.shouldPersist(path)) {
                rootKeys.add(path.split('.')[0]);
            }
        }
        
        for (const rootKey of rootKeys) {
            const value = this.getState(rootKey);
            try {
                localStorage.setItem(`state_${rootKey}`, JSON.stringify(value));
            } catch (error) {
                console.warn(`状态持久化失败 (${rootKey}):`, error);
            }
        }
    }

    loadPersistedState() {
        for (const key of this.persistentKeys) {
            try {
                const stored = localStorage.getItem(`state_${key}`);
                if (stored) {
                    const value = JSON.parse(stored);
                    this.setStateInternal(key, value);
                }
            } catch (error) {
                console.warn(`加载持久化状态失败 (${key}):`, error);
            }
        }
    }

    /**
     * 工具方法
     */
    isEqual(a, b) {
        if (a === b) return true;
        if (a === null || b === null) return false;
        if (typeof a !== typeof b) return false;
        
        if (typeof a === 'object') {
            return JSON.stringify(a) === JSON.stringify(b);
        }
        
        return false;
    }

    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        
        try {
            return JSON.parse(JSON.stringify(obj));
        } catch (error) {
            return obj;
        }
    }

    generateId() {
        return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    generateHistoryId() {
        return `hist_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 清理状态管理器
     */
    destroy() {
        this.subscribers.clear();
        this.history.length = 0;
        this.stats = {
            totalChanges: 0,
            changesByKey: new Map()
        };
        
        console.log('🗑️ StateManager destroyed');
    }

    /**
     * 获取调试信息
     */
    getDebugInfo() {
        return {
            state: this.state,
            subscribers: Object.fromEntries(
                Array.from(this.subscribers.entries()).map(([path, subs]) => [
                    path, 
                    subs.length
                ])
            ),
            stats: this.getStats(),
            historySize: this.history.length
        };
    }
}

// 创建全局状态管理器实例
if (typeof window !== 'undefined') {
    window.StateManager = StateManager;
    window.GlobalState = new StateManager();
    
    console.log('🎯 全局状态管理器已就绪');
} 