/**
 * 商户配置管理器
 * 从admin.js第19780-20787行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class MerchantConfigManager {
    constructor() {
        this.auth = new AuthManager();
        this.configData = null;
        this.currentTab = 'callback';
    }
    async initialize() {
        try {
            await this.loadConfigData();
            this.renderConfigManagement();
        } catch (error) {
            console.error('初始化系统配置失败:',
            error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }
    async loadConfigData() {
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=get_config', {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.configData = data.data || data;
                return true;
            } else {
                this.showError('加载配置数据失败: ' + (data.message || '未知错误'));
                return false;
            }
        } catch (error) {
            console.error('加载配置数据失败:',
            error);
            this.showError('网络错误，请重试');
            return false;
        }
    }
    renderConfigManagement() {
        const container = document.getElementById('merchantConfigContainer');
        if (!container) return;
        container.innerHTML = `
        <!-- 配置管理标签页 -->
        <div class="card">
        <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs">
        <li class="nav-item">
        <a class="nav-link ${
            this.currentTab === 'callback' ? 'active' : ''
        }"
        href="#" onclick="window.merchantConfigManager.switchTab('callback')">
        <i class="bi bi-arrow-repeat me-2"></i>回调配置
        </a>
        </li>
        <li class="nav-item">
        <a class="nav-link ${
            this.currentTab === 'whitelist' ? 'active' : ''
        }"
        href="#" onclick="window.merchantConfigManager.switchTab('whitelist')">
        <i class="bi bi-shield-check me-2"></i>IP白名单
        </a>
        </li>
        <li class="nav-item">
        <a class="nav-link ${
            this.currentTab === 'profile' ? 'active' : ''
        }"
        href="#" onclick="window.merchantConfigManager.switchTab('profile')">
        <i class="bi bi-person me-2"></i>个人资料
        </a>
        </li>
        <li class="nav-item">
        <a class="nav-link ${
            this.currentTab === 'security' ? 'active' : ''
        }"
        href="#" onclick="window.merchantConfigManager.switchTab('security')">
        <i class="bi bi-lock me-2"></i>安全设置
        </a>
        </li>
        </ul>
        </div>
        <div class="card-body">
        <div id="configTabContent">
        ${
            this.renderCurrentTab()
        }
        </div>
        </div>
        </div>
        `;
    }
    renderCurrentTab() {
        switch (this.currentTab) {
            case 'callback':
            return this.renderCallbackConfig();
            case 'whitelist':
            return this.renderWhitelistConfig();
            case 'profile':
            return this.renderProfileConfig();
            case 'security':
            return this.renderSecurityConfig();
            default:
            return this.renderCallbackConfig();
        }
    }
    renderCallbackConfig() {
        const config = this.configData || {
        };
        const callback = config.callback || {
        };
        return `
        <div class="callback-config">
        <div class="row">
        <div class="col-md-8">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-arrow-repeat me-2"></i>回调地址配置
        </h6>
        </div>
        <div class="card-body">
        <form id="callbackConfigForm">
        <div class="mb-3">
        <label class="form-label">支付成功回调地址</label>
        <div class="input-group">
        <input type="url" class="form-control" id="successCallbackUrl"
        value="${
            callback.success_url || ''
        }"
        placeholder="https://your-domain.com/callback/success">
        <button class="btn btn-outline-secondary" type="button"
        onclick="window.merchantConfigManager.testCallback('success')">
        <i class="bi bi-play-circle"></i> 测试
        </button>
        </div>
        <small class="form-text text-muted">
        支付成功后系统将向此地址发送POST请求通知
        </small>
        </div>
        <div class="mb-3">
        <label class="form-label">支付失败回调地址</label>
        <div class="input-group">
        <input type="url" class="form-control" id="failCallbackUrl"
        value="${
            callback.fail_url || ''
        }"
        placeholder="https://your-domain.com/callback/fail">
        <button class="btn btn-outline-secondary" type="button"
        onclick="window.merchantConfigManager.testCallback('fail')">
        <i class="bi bi-play-circle"></i> 测试
        </button>
        </div>
        <small class="form-text text-muted">
        支付失败后系统将向此地址发送POST请求通知
        </small>
        </div>
        <div class="mb-3">
        <label class="form-label">异步通知地址</label>
        <div class="input-group">
        <input type="url" class="form-control" id="notifyUrl"
        value="${
            callback.notify_url || ''
        }"
        placeholder="https://your-domain.com/callback/notify">
        <button class="btn btn-outline-secondary" type="button"
        onclick="window.merchantConfigManager.testCallback('notify')">
        <i class="bi bi-play-circle"></i> 测试
        </button>
        </div>
        <small class="form-text text-muted">
        系统将向此地址发送异步通知，请确保能正常接收POST请求
        </small>
        </div>
        <div class="mb-3">
        <div class="form-check">
        <input class="form-check-input" type="checkbox" id="enableCallback"
        ${
            callback.enabled ? 'checked' : ''
        }>
        <label class="form-check-label" for="enableCallback">
        启用回调通知
        </label>
        </div>
        </div>
        <div class="d-flex gap-2">
        <button type="button" class="btn btn-primary"
        onclick="window.merchantConfigManager.saveCallbackConfig()">
        <i class="bi bi-check me-2"></i>保存配置
        </button>
        <button type="button" class="btn btn-outline-secondary"
        onclick="window.merchantConfigManager.resetCallbackConfig()">
        <i class="bi bi-arrow-clockwise me-2"></i>重置
        </button>
        </div>
        </form>
        </div>
        </div>
        </div>
        <div class="col-md-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-info-circle me-2"></i>回调说明
        </h6>
        </div>
        <div class="card-body">
        <div class="callback-info">
        <h6>回调参数</h6>
        <div class="mb-3">
        <code class="d-block p-2 bg-light rounded">
        order_id: 订单号<br>
        amount: 支付金额<br>
        status: 支付状态<br>
        timestamp: 时间戳<br>
        sign: 签名
        </code>
        </div>
        <h6>签名验证</h6>
        <p class="small text-muted">
        请使用您的API密钥验证回调请求的签名，确保请求来源的真实性。
        </p>
        <h6>响应要求</h6>
        <p class="small text-muted">
        请在接收到回调通知后返回 "SUCCESS" 字符串，否则系统将重复发送通知。
        </p>
        <div class="mt-3">
        <a href="#" onclick="window.merchantConfigManager.viewCallbackLogs()"
        class="btn btn-outline-primary btn-sm">
        <i class="bi bi-list-ul me-1"></i>查看回调日志
        </a>
        </div>
        </div>
        </div>
        </div>
        <!-- 回调状态 -->
        <div class="card mt-3">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-activity me-2"></i>回调状态
        </h6>
        </div>
        <div class="card-body">
        <div class="callback-status">
        <div class="d-flex justify-content-between align-items-center mb-2">
        <span>今日回调</span>
        <strong class="text-primary">${
            callback.today_count || 0
        }</strong>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
        <span>成功率</span>
        <strong class="text-success">${
            callback.success_rate || 0
        }%</strong>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
        <span>最后回调</span>
        <small class="text-muted">${
            callback.last_callback || '暂无'
        }</small>
        </div>
        <div class="d-flex justify-content-between align-items-center">
        <span>状态</span>
        <span class="badge ${
            callback.enabled ? 'bg-success' : 'bg-secondary'
        }">
        ${
            callback.enabled ? '已启用' : '已禁用'
        }
        </span>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    renderWhitelistConfig() {
        const config = this.configData || {
        };
        const whitelist = config.whitelist || {
        };
        const ipList = whitelist.ips || [];
        return `
        <div class="whitelist-config">
        <div class="row">
        <div class="col-md-8">
        <div class="card">
        <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
        <h6 class="mb-0">
        <i class="bi bi-shield-check me-2"></i>IP白名单管理
        </h6>
        <button class="btn btn-primary btn-sm"
        onclick="window.merchantConfigManager.showAddIpModal()">
        <i class="bi bi-plus-circle me-1"></i>添加IP
        </button>
        </div>
        </div>
        <div class="card-body">
        ${
            ipList.length === 0 ? `
            <div class="text-center py-4">
            <i class="bi bi-shield-x" style="font-size: 2rem;
            color: #6c757d;
            "></i>
            <h6 class="text-muted mt-2">暂无IP白名单</h6>
            <p class="text-muted">添加IP地址以限制API访问来源</p>
            <button class="btn btn-primary"
            onclick="window.merchantConfigManager.showAddIpModal()">
            <i class="bi bi-plus-circle me-2"></i>添加第一个IP
            </button>
            </div>
            ` : `
            <div class="table-responsive">
            <table class="table table-hover">
            <thead>
            <tr>
            <th>IP地址</th>
            <th>描述</th>
            <th>状态</th>
            <th>添加时间</th>
            <th>操作</th>
            </tr>
            </thead>
            <tbody>
            ${
                ipList.map(ip => this.renderIpRow(ip)).join('')
            }
            </tbody>
            </table>
            </div>
            `
        }
        </div>
        </div>
        </div>
        <div class="col-md-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-info-circle me-2"></i>白名单说明
        </h6>
        </div>
        <div class="card-body">
        <div class="whitelist-info">
        <h6>功能说明</h6>
        <p class="small text-muted">
        IP白名单用于限制API访问来源，只有在白名单中的IP地址才能调用您的API接口。
        </p>
        <h6>IP格式</h6>
        <div class="mb-3">
        <code class="d-block p-2 bg-light rounded small">
        ***********00 (单个IP)<br>
        ***********/24 (IP段)<br>
        ***********-***********00 (IP范围)
        </code>
        </div>
        <h6>当前IP地址</h6>
        <div class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i>
        您的IP: <strong>${
            whitelist.current_ip || '获取中...'
        }</strong>
        </div>
        <div class="d-grid">
        <button class="btn btn-outline-primary btn-sm"
        onclick="window.merchantConfigManager.addCurrentIp()">
        <i class="bi bi-plus-circle me-1"></i>添加当前IP
        </button>
        </div>
        </div>
        </div>
        </div>
        <!-- 白名单统计 -->
        <div class="card mt-3">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-bar-chart me-2"></i>访问统计
        </h6>
        </div>
        <div class="card-body">
        <div class="whitelist-stats">
        <div class="d-flex justify-content-between align-items-center mb-2">
        <span>白名单IP数量</span>
        <strong class="text-primary">${
            ipList.length
        }</strong>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
        <span>今日允许访问</span>
        <strong class="text-success">${
            whitelist.today_allowed || 0
        }</strong>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-2">
        <span>今日拒绝访问</span>
        <strong class="text-danger">${
            whitelist.today_blocked || 0
        }</strong>
        </div>
        <div class="d-flex justify-content-between align-items-center">
        <span>状态</span>
        <span class="badge ${
            whitelist.enabled ? 'bg-success' : 'bg-secondary'
        }">
        ${
            whitelist.enabled ? '已启用' : '已禁用'
        }
        </span>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    renderIpRow(ip) {
        return `
        <tr>
        <td>
        <div class="d-flex align-items-center">
        <i class="bi bi-geo me-2 text-muted"></i>
        <strong>${
            ip.address
        }</strong>
        </div>
        </td>
        <td>
        <span class="text-muted">${
            ip.description || '-'
        }</span>
        </td>
        <td>
        <span class="badge ${
            ip.enabled ? 'bg-success' : 'bg-secondary'
        }">
        ${
            ip.enabled ? '启用' : '禁用'
        }
        </span>
        </td>
        <td>
        <div>${
            this.formatDateTime(ip.created_at)
        }</div>
        <small class="text-muted">${
            this.getRelativeTime(ip.created_at)
        }</small>
        </td>
        <td>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-secondary"
        onclick="window.merchantConfigManager.editIp('${
            ip.id
        }')" title="编辑">
        <i class="bi bi-pencil"></i>
        </button>
        <button class="btn btn-outline-${
            ip.enabled ? 'warning' : 'success'
        }"
        onclick="window.merchantConfigManager.toggleIp('${
            ip.id
        }')"
        title="${
            ip.enabled ? '禁用' : '启用'
        }">
        <i class="bi bi-${
            ip.enabled ? 'pause' : 'play'
        }"></i>
        </button>
        <button class="btn btn-outline-danger"
        onclick="window.merchantConfigManager.deleteIp('${
            ip.id
        }')" title="删除">
        <i class="bi bi-trash"></i>
        </button>
        </div>
        </td>
        </tr>
        `;
    }
    renderProfileConfig() {
        const config = this.configData || {
        };
        const profile = config.profile || {
        };
        return `
        <div class="profile-config">
        <div class="row">
        <div class="col-md-8">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-person me-2"></i>个人资料
        </h6>
        </div>
        <div class="card-body">
        <form id="profileForm">
        <div class="row">
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">商户名称</label>
        <input type="text" class="form-control" id="merchantName"
        value="${
            profile.merchant_name || ''
        }" required>
        </div>
        </div>
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">联系人</label>
        <input type="text" class="form-control" id="contactName"
        value="${
            profile.contact_name || ''
        }">
        </div>
        </div>
        </div>
        <div class="row">
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">联系电话</label>
        <input type="tel" class="form-control" id="contactPhone"
        value="${
            profile.contact_phone || ''
        }">
        </div>
        </div>
        <div class="col-md-6">
        <div class="mb-3">
        <label class="form-label">邮箱地址</label>
        <input type="email" class="form-control" id="contactEmail"
        value="${
            profile.contact_email || ''
        }">
        </div>
        </div>
        </div>
        <div class="mb-3">
        <label class="form-label">公司地址</label>
        <textarea class="form-control" id="companyAddress" rows="3">${
            profile.company_address || ''
        }</textarea>
        </div>
        <div class="mb-3">
        <label class="form-label">业务描述</label>
        <textarea class="form-control" id="businessDescription" rows="3"
        placeholder="请简要描述您的业务类型...">${
            profile.business_description || ''
        }</textarea>
        </div>
        <div class="d-flex gap-2">
        <button type="button" class="btn btn-primary"
        onclick="window.merchantConfigManager.saveProfile()">
        <i class="bi bi-check me-2"></i>保存资料
        </button>
        <button type="button" class="btn btn-outline-secondary"
        onclick="window.merchantConfigManager.resetProfile()">
        <i class="bi bi-arrow-clockwise me-2"></i>重置
        </button>
        </div>
        </form>
        </div>
        </div>
        </div>
        <div class="col-md-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-shield-check me-2"></i>账户信息
        </h6>
        </div>
        <div class="card-body">
        <div class="account-info">
        <div class="d-flex justify-content-between align-items-center mb-3">
        <span>开发者ID</span>
        <strong class="text-primary">${
            profile.developer_id || 'DEV_001'
        }</strong>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-3">
        <span>账户状态</span>
        <span class="badge ${
            profile.status === 'active' ? 'bg-success' : 'bg-warning'
        }">
        ${
            profile.status === 'active' ? '正常' : '待审核'
        }
        </span>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-3">
        <span>注册时间</span>
        <small class="text-muted">${
            this.formatDate(profile.created_at)
        }</small>
        </div>
        <div class="d-flex justify-content-between align-items-center">
        <span>最后登录</span>
        <small class="text-muted">${
            this.formatDateTime(profile.last_login)
        }</small>
        </div>
        </div>
        </div>
        </div>
        <!-- 资料完整度 -->
        <div class="card mt-3">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-percent me-2"></i>资料完整度
        </h6>
        </div>
        <div class="card-body">
        <div class="profile-completeness">
        <div class="progress mb-3" style="height: 10px;
        ">
        <div class="progress-bar" style="width: ${
            this.calculateCompleteness(profile)
        }%"></div>
        </div>
        <div class="completeness-items">
        ${
            this.renderCompletenessItems(profile)
        }
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    renderSecurityConfig() {
        const config = this.configData || {
        };
        const security = config.security || {
        };
        return `
        <div class="security-config">
        <div class="row">
        <div class="col-md-8">
        <!-- 密码修改 -->
        <div class="card mb-4">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-key me-2"></i>密码修改
        </h6>
        </div>
        <div class="card-body">
        <form id="passwordForm">
        <div class="mb-3">
        <label class="form-label">当前密码</label>
        <input type="password" class="form-control" id="currentPassword" required>
        </div>
        <div class="mb-3">
        <label class="form-label">新密码</label>
        <input type="password" class="form-control" id="newPassword" required>
        <small class="form-text text-muted">
        密码长度至少8位，包含字母、数字和特殊字符
        </small>
        </div>
        <div class="mb-3">
        <label class="form-label">确认新密码</label>
        <input type="password" class="form-control" id="confirmPassword" required>
        </div>
        <button type="button" class="btn btn-primary"
        onclick="window.merchantConfigManager.changePassword()">
        <i class="bi bi-check me-2"></i>修改密码
        </button>
        </form>
        </div>
        </div>
        <!-- API密钥重新生成 -->
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-shield-lock me-2"></i>API密钥管理
        </h6>
        </div>
        <div class="card-body">
        <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <strong>注意：</strong>重新生成API密钥后，需要更新您的应用程序中的密钥配置。
        </div>
        <div class="d-flex gap-2">
        <button class="btn btn-warning"
        onclick="window.merchantConfigManager.regenerateApiKey()">
        <i class="bi bi-arrow-clockwise me-2"></i>重新生成API密钥
        </button>
        <button class="btn btn-outline-primary"
        onclick="window.merchantConfigManager.downloadApiKeyBackup()">
        <i class="bi bi-download me-2"></i>下载密钥备份
        </button>
        </div>
        </div>
        </div>
        </div>
        <div class="col-md-4">
        <!-- 安全状态 -->
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-shield-check me-2"></i>安全状态
        </h6>
        </div>
        <div class="card-body">
        <div class="security-status">
        <div class="security-item mb-3">
        <div class="d-flex justify-content-between align-items-center">
        <span>密码强度</span>
        <span class="badge ${
            security.password_strength === 'strong' ? 'bg-success' : security.password_strength === 'medium' ? 'bg-warning' : 'bg-danger'
        }">
        ${
            security.password_strength === 'strong' ? '强' : security.password_strength === 'medium' ? '中' : '弱'
        }
        </span>
        </div>
        </div>
        <div class="security-item mb-3">
        <div class="d-flex justify-content-between align-items-center">
        <span>IP白名单</span>
        <span class="badge ${
            security.whitelist_enabled ? 'bg-success' : 'bg-secondary'
        }">
        ${
            security.whitelist_enabled ? '已启用' : '未启用'
        }
        </span>
        </div>
        </div>
        <div class="security-item mb-3">
        <div class="d-flex justify-content-between align-items-center">
        <span>回调验证</span>
        <span class="badge ${
            security.callback_verification ? 'bg-success' : 'bg-secondary'
        }">
        ${
            security.callback_verification ? '已启用' : '未启用'
        }
        </span>
        </div>
        </div>
        <div class="security-item">
        <div class="d-flex justify-content-between align-items-center">
        <span>最后修改</span>
        <small class="text-muted">${
            this.formatDateTime(security.last_updated)
        }</small>
        </div>
        </div>
        </div>
        </div>
        </div>
        <!-- 登录记录 -->
        <div class="card mt-3">
        <div class="card-header">
        <h6 class="mb-0">
        <i class="bi bi-clock-history me-2"></i>最近登录
        </h6>
        </div>
        <div class="card-body">
        <div class="login-history">
        ${
            (security.login_history || []).map(login => `
            <div class="login-item mb-2">
            <div class="d-flex justify-content-between">
            <small class="text-muted">${
                login.ip
            }</small>
            <small class="text-muted">${
                this.formatDateTime(login.time)
            }</small>
            </div>
            <div class="d-flex justify-content-between">
            <small>${
                login.location || '未知位置'
            }</small>
            <span class="badge ${
                login.status === 'success' ? 'bg-success' : 'bg-danger'
            } badge-sm">
            ${
                login.status === 'success' ? '成功' : '失败'
            }
            </span>
            </div>
            </div>
            `).join('')
        }
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
    }
    // 工具函数
    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }
    getRelativeTime(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffDays === 0) return '今天';
        if (diffDays === 1) return '昨天';
        if (diffDays < 30) return `${
            diffDays
        }天前`;
        return '';
    }
    calculateCompleteness(profile) {
        const fields = ['merchant_name', 'contact_name', 'contact_phone', 'contact_email', 'company_address', 'business_description'];
        const completed = fields.filter(field => profile[field] && profile[field].trim()).length;
        return Math.round((completed / fields.length) * 100);
    }
    renderCompletenessItems(profile) {
        const items = [
        {
            field: 'merchant_name',
            label: '商户名称',
            completed: !!profile.merchant_name
        },
        {
            field: 'contact_name',
            label: '联系人',
            completed: !!profile.contact_name
        },
        {
            field: 'contact_phone',
            label: '联系电话',
            completed: !!profile.contact_phone
        },
        {
            field: 'contact_email',
            label: '邮箱地址',
            completed: !!profile.contact_email
        },
        {
            field: 'company_address',
            label: '公司地址',
            completed: !!profile.company_address
        },
        {
            field: 'business_description',
            label: '业务描述',
            completed: !!profile.business_description
        }
        ];
        return items.map(item => `
        <div class="d-flex justify-content-between align-items-center mb-1">
        <small class="text-muted">${
            item.label
        }</small>
        <i class="bi bi-${
            item.completed ? 'check-circle-fill text-success' : 'circle text-muted'
        }"></i>
        </div>
        `).join('');
    }
    // 交互功能
    switchTab(tab) {
        this.currentTab = tab;
        const tabContent = document.getElementById('configTabContent');
        if (tabContent) {
            tabContent.innerHTML = this.renderCurrentTab();
        }
        // 更新标签状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[onclick*="'${
            tab
        }'"]`).classList.add('active');
    }
    // 回调配置相关功能
    async saveCallbackConfig() {
        const configData = {
            success_url: document.getElementById('successCallbackUrl').value.trim(),
            fail_url: document.getElementById('failCallbackUrl').value.trim(),
            notify_url: document.getElementById('notifyUrl').value.trim(),
            enabled: document.getElementById('enableCallback').checked
        };
        // 表单验证
        if (configData.enabled) {
            if (!configData.success_url || !configData.fail_url || !configData.notify_url) {
                this.showError('启用回调通知时，所有回调地址都必须填写');
                return;
            }
        }
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=save_callback_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify(configData)
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('回调配置保存成功');
                await this.loadConfigData();
                this.renderConfigManagement();
            } else {
                this.showError('保存回调配置失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('保存回调配置失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async testCallback(type) {
        const urlMap = {
            'success': document.getElementById('successCallbackUrl').value.trim(),
            'fail': document.getElementById('failCallbackUrl').value.trim(),
            'notify': document.getElementById('notifyUrl').value.trim()
        };
        const url = urlMap[type];
        if (!url) {
            this.showError('请先填写回调地址');
            return;
        }
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=test_callback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    type,
                    url
                })
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess(`${
                    type
                }回调测试成功`);
            } else {
                this.showError(`回调测试失败: ${
                    data.message || '未知错误'
                }`);
            }
        } catch (error) {
            console.error('回调测试失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    resetCallbackConfig() {
        if (confirm('确定要重置回调配置吗？')) {
            document.getElementById('successCallbackUrl').value = '';
            document.getElementById('failCallbackUrl').value = '';
            document.getElementById('notifyUrl').value = '';
            document.getElementById('enableCallback').checked = false;
        }
    }
    viewCallbackLogs() {
        this.showInfo('回调日志功能开发中...');
    }
    // IP白名单相关功能
    showAddIpModal() {
        this.showInfo('添加IP地址功能开发中...');
    }
    addCurrentIp() {
        this.showInfo('添加当前IP功能开发中...');
    }
    editIp(ipId) {
        this.showInfo('编辑IP功能开发中...');
    }
    async toggleIp(ipId) {
        if (!confirm('确定要切换IP状态吗？')) return;
        this.showInfo('切换IP状态功能开发中...');
    }
    async deleteIp(ipId) {
        if (!confirm('确定要删除这个IP地址吗？')) return;
        this.showInfo('删除IP功能开发中...');
    }
    // 个人资料相关功能
    async saveProfile() {
        const profileData = {
            merchant_name: document.getElementById('merchantName').value.trim(),
            contact_name: document.getElementById('contactName').value.trim(),
            contact_phone: document.getElementById('contactPhone').value.trim(),
            contact_email: document.getElementById('contactEmail').value.trim(),
            company_address: document.getElementById('companyAddress').value.trim(),
            business_description: document.getElementById('businessDescription').value.trim()
        };
        if (!profileData.merchant_name) {
            this.showError('请输入商户名称');
            return;
        }
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=save_profile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify(profileData)
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('个人资料保存成功');
                await this.loadConfigData();
                this.renderConfigManagement();
            } else {
                this.showError('保存个人资料失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('保存个人资料失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    resetProfile() {
        if (confirm('确定要重置个人资料吗？')) {
            this.renderConfigManagement();
        }
    }
    // 安全设置相关功能
    async changePassword() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        if (!currentPassword || !newPassword || !confirmPassword) {
            this.showError('请填写所有密码字段');
            return;
        }
        if (newPassword !== confirmPassword) {
            this.showError('新密码和确认密码不一致');
            return;
        }
        if (newPassword.length < 8) {
            this.showError('新密码长度至少8位');
            return;
        }
        try {
            const response = await fetch('/api/merchant/index.php?module=tools&action=change_password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    current_password: currentPassword,
                    new_password: newPassword
                })
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.showSuccess('密码修改成功');
                document.getElementById('passwordForm').reset();
            } else {
                this.showError('密码修改失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('密码修改失败:',
            error);
            this.showError('网络错误，请重试');
        }
    }
    async regenerateApiKey() {
        if (!confirm('确定要重新生成API密钥吗？此操作不可恢复！')) return;
        this.showInfo('重新生成API密钥功能开发中...');
    }
    downloadApiKeyBackup() {
        this.showInfo('下载密钥备份功能开发中...');
    }
    showSuccess(message) {
        console.log('Success:',
        message);
        // 这里可以集成toast通知组件
    }
    showError(message) {
        console.error('Error:',
        message);
        // 这里可以集成toast通知组件
    }
    showInfo(message) {
        console.log('Info:',
        message);
        // 这里可以集成toast通知组件
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = MerchantConfigManager;
} else if (typeof window !== "undefined") {
    window.MerchantConfigManager = MerchantConfigManager;
}

console.log('📦 MerchantConfigManager 模块加载完成');
