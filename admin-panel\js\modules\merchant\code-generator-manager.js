/**
 * 商户代码生成工具管理器
 * 从admin.js第16206-16559行提取并格式化
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024年12月25日
 */

class MerchantCodeGeneratorManager {
    constructor() {
        this.auth = new AuthManager();
        this.currentLanguage = 'php';
        this.currentApiType = 'pay_get_qrcode';
        this.languages = [];
        this.generatedCode = '';
    }
    async initialize() {
        try {
            await this.loadLanguages();
            this.renderCodeGenerator();
        } catch (error) {
            console.error('初始化代码生成器失败:',
            error);
            this.showError('初始化失败，请刷新页面重试');
        }
    }
    async loadLanguages() {
        try {
            const response = await fetch('/api/merchant/index.php?module=code_generator&action=get_languages', {
                headers: {
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                }
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.languages = data.data || data;
                return true;
            } else {
                this.showError('加载语言列表失败: ' + (data.message || '未知错误'));
                return false;
            }
        } catch (error) {
            console.error('加载语言列表失败:',
            error);
            this.showError('网络错误，请重试');
            return false;
        }
    }
    renderCodeGenerator() {
        const container = document.getElementById('codeGeneratorContainer');
        if (!container) return;
        const languageOptions = this.languages.map(lang => `
        <option value="${
            lang.key
        }" ${
            lang.key === this.currentLanguage ? 'selected' : ''
        }>
        ${
            lang.name
        } - ${
            lang.description
        }
        </option>
        `).join('');
        container.innerHTML = `
        <div class="row">
        <!-- 左侧：配置面板 -->
        <div class="col-md-4">
        <div class="card">
        <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-gear me-2"></i>生成配置</h6>
        </div>
        <div class="card-body">
        <div class="mb-3">
        <label class="form-label">编程语言</label>
        <select class="form-select" id="languageSelect" onchange="window.codeGeneratorManager.changeLanguage(this.value)">
        ${
            languageOptions
        }
        </select>
        </div>
        <div class="mb-3">
        <label class="form-label">API接口类型</label>
        <select class="form-select" id="apiTypeSelect" onchange="window.codeGeneratorManager.changeApiType(this.value)">
        <option value="pay_get_qrcode" ${
            this.currentApiType === 'pay_get_qrcode' ? 'selected' : ''
        }>获取收款页面</option>
        <option value="pay_get_orders_inquiry" ${
            this.currentApiType === 'pay_get_orders_inquiry' ? 'selected' : ''
        }>查询订单信息</option>
        <option value="pay_get_payment_quota" ${
            this.currentApiType === 'pay_get_payment_quota' ? 'selected' : ''
        }>查询产品可用额度</option>
        <option value="callback_verify" ${
            this.currentApiType === 'callback_verify' ? 'selected' : ''
        }>回调验证</option>
        </select>
        </div>
        <div class="d-grid gap-2">
        <button class="btn btn-primary" onclick="window.codeGeneratorManager.generateCode()">
        <i class="bi bi-code-square me-2"></i>生成代码
        </button>
        <button class="btn btn-outline-secondary" onclick="window.codeGeneratorManager.resetForm()">
        <i class="bi bi-arrow-clockwise me-2"></i>重置
        </button>
        </div>
        </div>
        </div>
        <!-- 语言说明卡片 -->
        <div class="card mt-3">
        <div class="card-header">
        <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>当前语言说明</h6>
        </div>
        <div class="card-body">
        <div id="languageInfo">
        <p class="text-muted">选择语言后显示相关说明</p>
        </div>
        </div>
        </div>
        </div>
        <!-- 右侧：代码展示区 -->
        <div class="col-md-8">
        <div class="card">
        <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
        <h6 class="mb-0"><i class="bi bi-file-earmark-code me-2"></i>生成的代码</h6>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-primary" onclick="window.codeGeneratorManager.copyCode()" id="copyBtn">
        <i class="bi bi-clipboard me-1"></i>复制
        </button>
        <button class="btn btn-outline-success" onclick="window.codeGeneratorManager.downloadCode()" id="downloadBtn">
        <i class="bi bi-download me-1"></i>下载
        </button>
        </div>
        </div>
        </div>
        <div class="card-body p-0">
        <div id="codeDisplay" style="min-height: 400px;
        background: #f8f9fa;
        ">
        <div class="text-center py-5 text-muted">
        <i class="bi bi-code-slash" style="font-size: 3rem;
        opacity: 0.3;
        "></i>
        <p class="mt-3">点击"生成代码"按钮开始生成示例代码</p>
        </div>
        </div>
        </div>
        </div>
        </div>
        </div>
        `;
        this.updateLanguageInfo();
    }
    changeLanguage(language) {
        this.currentLanguage = language;
        this.updateLanguageInfo();
    }
    changeApiType(apiType) {
        this.currentApiType = apiType;
    }
    updateLanguageInfo() {
        const infoContainer = document.getElementById('languageInfo');
        if (!infoContainer) return;
        const currentLang = this.languages.find(lang => lang.key === this.currentLanguage);
        if (currentLang) {
            infoContainer.innerHTML = `
            <h6>${
                currentLang.name
            }</h6>
            <p class="text-muted small mb-2">${
                currentLang.description
            }</p>
            <div class="badge bg-primary">${
                currentLang.icon || currentLang.key
            }</div>
            `;
        }
    }
    async generateCode() {
        try {
            const generateBtn = document.querySelector('button[onclick*="generateCode"]');
            if (generateBtn) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>生成中...';
            }
            const response = await fetch('/api/merchant/index.php?module=code_generator&action=generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${
                        this.auth.getToken()
                    }`
                },
                body: JSON.stringify({
                    language: this.currentLanguage,
                    api_type: this.currentApiType
                })
            });
            const data = await response.json();
            if (data.code === 200 || data.success) {
                this.generatedCode = data.data.code || data.code;
                this.displayCode(this.generatedCode,
                data.data.filename || data.filename);
                this.showSuccess('代码生成成功！');
            } else {
                this.showError('代码生成失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('生成代码失败:',
            error);
            this.showError('网络错误，请重试');
        } finally {
            const generateBtn = document.querySelector('button[onclick*="generateCode"]');
            if (generateBtn) {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="bi bi-code-square me-2"></i>生成代码';
            }
        }
    }
    displayCode(code,
    filename) {
        const codeDisplay = document.getElementById('codeDisplay');
        if (!codeDisplay) return;
        // 转义HTML字符
        const escapedCode = code.replace(/</g, '&lt;
        ').replace(/>/g, '&gt;
        ');
        codeDisplay.innerHTML = `
        <div class="code-header" style="background: #e9ecef;
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
        ">
        <small class="text-muted">
        <i class="bi bi-file-earmark-code me-1"></i>
        ${
            filename || 'generated_code.' + this.currentLanguage
        }
        </small>
        </div>
        <pre style="margin: 0;
        padding: 20px;
        background: #f8f9fa;
        max-height: 500px;
        overflow-y: auto;
        "><code>${
            escapedCode
        }</code></pre>
        `;
        // 启用复制和下载按钮
        document.getElementById('copyBtn').disabled = false;
        document.getElementById('downloadBtn').disabled = false;
    }
    copyCode() {
        if (!this.generatedCode) {
            this.showError('没有可复制的代码');
            return;
        }
        navigator.clipboard.writeText(this.generatedCode).then(() => {
            this.showSuccess('代码已复制到剪贴板');
            // 临时改变按钮文字
            const copyBtn = document.getElementById('copyBtn');
            const originalText = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="bi bi-check me-1"></i>已复制';
            setTimeout(() => {
                copyBtn.innerHTML = originalText;
            }, 2000);
        }).catch(err => {
            console.error('复制失败:',
            err);
            this.showError('复制失败，请手动复制');
        });
    }
    downloadCode() {
        if (!this.generatedCode) {
            this.showError('没有可下载的代码');
            return;
        }
        const filename = this.getFilename();
        const blob = new Blob([this.generatedCode], {
            type: 'text/plain'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        this.showSuccess('代码文件已下载');
    }
    getFilename() {
        const extensions = {
            'php': 'php',
            'java': 'java',
            'python': 'py',
            'nodejs': 'js',
            'csharp': 'cs',
            'go': 'go'
        };
        const ext = extensions[this.currentLanguage] || 'txt';
        return `paypal_${
            this.currentApiType
        }.${
            ext
        }`;
    }
    resetForm() {
        this.currentLanguage = 'php';
        this.currentApiType = 'pay_get_qrcode';
        this.generatedCode = '';
        document.getElementById('languageSelect').value = this.currentLanguage;
        document.getElementById('apiTypeSelect').value = this.currentApiType;
        const codeDisplay = document.getElementById('codeDisplay');
        if (codeDisplay) {
            codeDisplay.innerHTML = `
            <div class="text-center py-5 text-muted">
            <i class="bi bi-code-slash" style="font-size: 3rem;
            opacity: 0.3;
            "></i>
            <p class="mt-3">点击"生成代码"按钮开始生成示例代码</p>
            </div>
            `;
        }
        document.getElementById('copyBtn').disabled = true;
        document.getElementById('downloadBtn').disabled = true;
        this.updateLanguageInfo();
        this.showSuccess('表单已重置');
    }
    showLanguageInfo() {
        // 显示语言详细信息的模态框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
        <h5 class="modal-title">支持的编程语言</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
        <div class="row">
        ${
            this.languages.map(lang => `
            <div class="col-md-6 mb-3">
            <div class="card">
            <div class="card-body">
            <h6>${
                lang.name
            }</h6>
            <p class="text-muted small">${
                lang.description
            }</p>
            <div class="badge bg-secondary">${
                lang.icon || lang.key
            }</div>
            </div>
            </div>
            </div>
            `).join('')
        }
        </div>
        </div>
        <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
        </div>
        </div>
        `;
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }
    showSuccess(message) {
        console.log('Success:',
        message);
        // 这里可以集成toast通知组件
    }
    showError(message) {
        console.error('Error:',
        message);
        // 这里可以集成toast通知组件
    }
}

// 模块导出
if (typeof module !== "undefined" && module.exports) {
    module.exports = MerchantCodeGeneratorManager;
} else if (typeof window !== "undefined") {
    window.MerchantCodeGeneratorManager = MerchantCodeGeneratorManager;
}

console.log('📦 MerchantCodeGeneratorManager 模块加载完成');
