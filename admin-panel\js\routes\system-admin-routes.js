/**
 * 系统管理员路由配置
 * 优化版本 - 只加载系统管理员需要的功能
 * 
 * <AUTHOR>
 * @version 2.0.0 - 架构优化版本
 */

const SystemAdminRoutes = {
    // 租户类型标识
    tenantType: 'system_admin',
    
    // 系统管理员菜单配置（11个核心功能）
    menuConfig: [
        {
            id: 'dashboard',
            name: '系统概览',
            icon: 'bi-speedometer2',
            path: '/dashboard',
            module: 'SystemDashboardModule',
            permission: 'system_dashboard',
            description: '系统整体运行状态监控'
        },
        {
            id: 'platforms',
            name: '平台管理',
            icon: 'bi-building',
            path: '/platforms',
            module: 'PlatformManagementModule',
            permission: 'platform_management',
            description: '管理所有平台配置'
        },
        {
            id: 'domains',
            name: '域名配置',
            icon: 'bi-globe',
            path: '/domains',
            module: 'DomainConfigModule',
            permission: 'domain_config',
            description: '管理域名解析配置'
        },
        {
            id: 'system_logs',
            name: '系统日志',
            icon: 'bi-journal-text',
            path: '/system-logs',
            module: 'SystemLogsModule',
            permission: 'system_logs',
            description: '查看系统操作日志'
        },
        {
            id: 'api_router',
            name: 'API路由管理',
            icon: 'bi-diagram-3',
            path: '/api-router',
            module: 'ApiRouterModule',
            permission: 'api_router_management',
            description: '管理API路由配置'
        },
        {
            id: 'sql_router',
            name: 'SQL路由管理',
            icon: 'bi-database',
            path: '/sql-router',
            module: 'SqlRouterModule',
            permission: 'sql_router_management',
            description: '管理SQL查询路由'
        },
        {
            id: 'system_config',
            name: '系统配置',
            icon: 'bi-gear',
            path: '/system-config',
            module: 'SystemConfigModule',
            permission: 'system_config',
            description: '系统参数配置'
        },
        {
            id: 'backup_restore',
            name: '备份恢复',
            icon: 'bi-archive',
            path: '/backup-restore',
            module: 'BackupRestoreModule',
            permission: 'backup_restore',
            description: '数据备份与恢复'
        },
        {
            id: 'system_monitor',
            name: '系统监控',
            icon: 'bi-activity',
            path: '/system-monitor',
            module: 'SystemMonitorModule',
            permission: 'system_monitor',
            description: '系统性能监控'
        },
        {
            id: 'security_audit',
            name: '安全审计',
            icon: 'bi-shield-check',
            path: '/security-audit',
            module: 'SecurityAuditModule',
            permission: 'security_audit',
            description: '安全事件审计'
        },
        {
            id: 'admin_profile',
            name: '个人资料',
            icon: 'bi-person-circle',
            path: '/profile',
            module: 'AdminProfileModule',
            permission: 'profile_management',
            description: '管理个人账户信息'
        }
    ],

    // 路由配置
    routes: {
        '/dashboard': {
            component: 'SystemDashboardModule',
            title: '系统概览',
            requireAuth: true,
            permission: 'system_dashboard'
        },
        '/platforms': {
            component: 'PlatformManagementModule',
            title: '平台管理',
            requireAuth: true,
            permission: 'platform_management'
        },
        '/domains': {
            component: 'DomainConfigModule',
            title: '域名配置',
            requireAuth: true,
            permission: 'domain_config'
        },
        '/system-logs': {
            component: 'SystemLogsModule',
            title: '系统日志',
            requireAuth: true,
            permission: 'system_logs'
        },
        '/api-router': {
            component: 'ApiRouterModule',
            title: 'API路由管理',
            requireAuth: true,
            permission: 'api_router_management'
        },
        '/sql-router': {
            component: 'SqlRouterModule',
            title: 'SQL路由管理',
            requireAuth: true,
            permission: 'sql_router_management'
        },
        '/system-config': {
            component: 'SystemConfigModule',
            title: '系统配置',
            requireAuth: true,
            permission: 'system_config'
        },
        '/backup-restore': {
            component: 'BackupRestoreModule',
            title: '备份恢复',
            requireAuth: true,
            permission: 'backup_restore'
        },
        '/system-monitor': {
            component: 'SystemMonitorModule',
            title: '系统监控',
            requireAuth: true,
            permission: 'system_monitor'
        },
        '/security-audit': {
            component: 'SecurityAuditModule',
            title: '安全审计',
            requireAuth: true,
            permission: 'security_audit'
        },
        '/profile': {
            component: 'AdminProfileModule',
            title: '个人资料',
            requireAuth: true,
            permission: 'profile_management'
        }
    },

    // 权限配置
    permissions: [
        'system_dashboard',
        'platform_management',
        'domain_config',
        'system_logs',
        'api_router_management',
        'sql_router_management',
        'system_config',
        'backup_restore',
        'system_monitor',
        'security_audit',
        'profile_management'
    ],

    // 默认路由
    defaultRoute: '/dashboard',

    // 主题配置
    theme: {
        primaryColor: '#1890ff',
        brandName: '系统管理后台',
        sidebarStyle: 'dark',
        headerStyle: 'light'
    },

    // API端点配置
    apiEndpoints: {
        base: '/api/admin.php',
        auth: '/api/admin.php?action=login',
        logout: '/api/admin.php?action=logout',
        profile: '/api/admin.php?action=get_profile'
    },

    // 快速操作配置
    quickActions: [
        {
            name: '查看系统状态',
            icon: 'bi-speedometer2',
            action: 'dashboard',
            color: 'primary'
        },
        {
            name: '添加平台',
            icon: 'bi-plus-circle',
            action: 'add_platform',
            color: 'success'
        },
        {
            name: '系统监控',
            icon: 'bi-activity',
            action: 'system_monitor',
            color: 'info'
        },
        {
            name: '安全审计',
            icon: 'bi-shield-check',
            action: 'security_audit',
            color: 'warning'
        }
    ],

    // 统计信息配置
    dashboardWidgets: [
        'platform_count',
        'total_users',
        'system_health',
        'api_requests',
        'error_rate',
        'storage_usage'
    ]
};

// 导出配置
window.SystemAdminRoutes = SystemAdminRoutes;
console.log('✅ 系统管理员路由配置已加载 (11个功能)'); 